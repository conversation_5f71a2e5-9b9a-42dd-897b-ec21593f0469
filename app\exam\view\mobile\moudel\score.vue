<template>
  <el-row>
    <div class="successBox">
      <img src="../../../public/static/img/icon.png" alt="" class="successImg">
      <div class="successTitle">恭喜你，完成答题！</div>
      <div class="successNum">
        <span>{{ my_score }}</span>
        <span>分</span>
      </div>
    </div>
  </el-row>
</template>

<script>
module.exports = {
  name: "score",
  props: {
    score: {
      type: String,
      default: '0',
    },
  },
  data: function () {
    return {
      my_score: this.score,
    };
  },
  watch: {
    my_score(val) {
      this.score = val;
    }
  },
}
</script>
