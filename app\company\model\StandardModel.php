<?php

namespace app\company\model;

use app\model\SettingModel;
use think\facade\Db;
use think\Model;
use Medoo\Mdb;
use app\model\FileModel;

class StandardModel extends Model
{
    public static function standardSave($param,$id=0)
    {
        $company = Db::table('top_company_info')->where([['id','=',$_SESSION['company']['id']],['status','=','1']])->find();
        if(empty($company)||empty($_SESSION['company']['id'])){
            result('',1003,'请先认证企业信息');
        }
        $standard = Db::table('top_standard_name')->where(['id'=>$param['standard_id'],'is_del'=>0])->field('id,name')->find();
        if(empty($standard)){
            result('',1003,'申请标准不存在');
        }
        $data = [
            'standard_id' => $standard['id'],
            'standard_name' => $standard['name'],
            'area_id' => $company['area_id'],
            'dept_id' => $company['dept_id'],
            'level' => $param['level'],
            'is_advisory' => $param['is_advisory'],
            'advisory' => $param['advisory'],
            'status' => 1,
            'apply_user_id' => $_SESSION['company']['user_id'],
            'apply_user_name' => $_SESSION['company']['user_name'],
            'apply_time' => date('Y-m-d H:i:s'),
        ];
        $re = Db::table('top_standard_apply')->where([['company_id','=',$_SESSION['company']['id']],['status','=','1']])->find();
        if($re){
            result('',1003,'已有待审核的申请');
        }
        if($id){
            $re = Db::table('top_standard_apply')->where(['id'=>$id])->find();
            if(empty($re)){
                result('',1003,'数据不存在或已删除');
            }
//            dd($data);
            $re = Db::table('top_standard_apply')->where(['id'=>$id])->update($data);
        }else{
            $data['company_id'] = $company['id'];
            $data['company_name'] = $company['name'];
            $data['industry'] = $company['industry'];
            $data['specialty'] = $company['specialty'];
            $id = Db::table('top_standard_apply')->insertGetId($data);
        }
        return $id;
    }


    //参数格式化
    public static function codeToText($info){
        if(empty($info)){
            return $info;
        }
        foreach ($info as $k=>$v){
            $info[$k] = $v==='0000-00-00'?'':$v;
            if(is_int($v)){
                $info[$k] = (string)$v;
            }
            if($v===null){
                $info[$k] = '';
            }
        }
        return $info;
    }


}