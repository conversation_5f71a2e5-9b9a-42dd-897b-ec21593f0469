<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单搜索测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
</head>
<body>
    <div id="app" style="padding: 20px;">
        <h2>Element UI 搜索功能测试</h2>
        
        <h3>方法1: 使用 filterable 属性（推荐）</h3>
        <el-select 
            v-model="value1" 
            placeholder="请选择（支持搜索）" 
            filterable
            style="width: 300px;">
            <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.searchLabel"
                :value="item.value">
                <span style="float: left">{{ item.label }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.comment }}</span>
            </el-option>
        </el-select>
        
        <h3>方法2: 使用 filter-method 属性</h3>
        <el-select 
            v-model="value2" 
            placeholder="请选择（自定义过滤）" 
            filterable
            :filter-method="filterMethod"
            style="width: 300px;">
            <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value">
                <span style="float: left">{{ item.label }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.comment }}</span>
            </el-option>
        </el-select>
        
        <h3>方法3: 手动输入框 + 过滤列表</h3>
        <el-input 
            v-model="searchKeyword" 
            placeholder="输入搜索关键词" 
            @input="filterOptions"
            style="width: 300px; margin-bottom: 10px;">
        </el-input>
        <el-select v-model="value3" placeholder="请选择" style="width: 300px;">
            <el-option
                v-for="item in filteredOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
                <span style="float: left">{{ item.label }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.comment }}</span>
            </el-option>
        </el-select>
        
        <div style="margin-top: 20px; padding: 15px; background: #f5f5f5; border-radius: 4px;">
            <h4>调试信息</h4>
            <p>选中值1: {{ value1 }}</p>
            <p>选中值2: {{ value2 }}</p>
            <p>选中值3: {{ value3 }}</p>
            <p>搜索关键词: {{ searchKeyword }}</p>
            <p>过滤后选项数量: {{ filteredOptions.length }}</p>
            <p>过滤方法调用次数: {{ filterCallCount }}</p>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    value1: '',
                    value2: '',
                    value3: '',
                    searchKeyword: '',
                    filterCallCount: 0,
                    options: [
                        { 
                            value: 'user_info', 
                            label: 'user_info', 
                            comment: '用户信息表',
                            searchLabel: 'user_info 用户信息表'
                        },
                        { 
                            value: 'user_role', 
                            label: 'user_role', 
                            comment: '用户角色表',
                            searchLabel: 'user_role 用户角色表'
                        },
                        { 
                            value: 'product_info', 
                            label: 'product_info', 
                            comment: '产品信息表',
                            searchLabel: 'product_info 产品信息表'
                        },
                        { 
                            value: 'order_detail', 
                            label: 'order_detail', 
                            comment: '订单详情表',
                            searchLabel: 'order_detail 订单详情表'
                        },
                        { 
                            value: 'system_config', 
                            label: 'system_config', 
                            comment: '系统配置表',
                            searchLabel: 'system_config 系统配置表'
                        }
                    ],
                    filteredOptions: []
                }
            },
            mounted() {
                this.filteredOptions = this.options;
            },
            methods: {
                filterMethod(value) {
                    this.filterCallCount++;
                    console.log('自定义过滤方法被调用:', value);
                    
                    if (!value) {
                        return true;
                    }
                    
                    const keyword = value.toLowerCase();
                    
                    // 这里需要返回一个函数，该函数接收option参数
                    return (option) => {
                        const item = this.options.find(opt => opt.value === option.value);
                        if (!item) return false;
                        
                        const label = item.label.toLowerCase();
                        const comment = item.comment.toLowerCase();
                        
                        return label.includes(keyword) || comment.includes(keyword);
                    };
                },
                
                filterOptions() {
                    const keyword = this.searchKeyword.toLowerCase().trim();
                    
                    if (!keyword) {
                        this.filteredOptions = this.options;
                        return;
                    }
                    
                    this.filteredOptions = this.options.filter(item => {
                        const label = item.label.toLowerCase();
                        const comment = item.comment.toLowerCase();
                        return label.includes(keyword) || comment.includes(keyword);
                    });
                }
            }
        });
    </script>
</body>
</html>
