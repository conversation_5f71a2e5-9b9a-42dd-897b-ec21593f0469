<?php

namespace app\controller;

use think\facade\Db;

class Sms {


    protected $url = "http://172.42.136.164/platform/sms/yktsms/send";
    protected $AppID = "R78e39V5LN4xdN72hxV0fr2bHC39lc04";
    protected $AppKey = "XVCvqy9qqVWDTenrmMkXxizpkVbTKcsY";


    //发送短信验证码
    public function sendsms($mobile='', $content='') {
        //return ['code'=>0,'taskid'=>'I1906141154338793','msg'=>'成功'];//正式上线删除
        $content = '【成都市应急管理局】'.$content;
        $data = [
            "appid" => $this->AppID,
            'mobile' => $mobile,
            'msg' => urlencode($content),
            'sign' => md5($this->AppID.$mobile.$content.$this->AppKey),
        ];
        $re = http_post($this->url,$data);
        $re = explode(',',$re);
        return ['code'=>$re[0],'taskid'=>$re[1],'msg'=>$re[3]];
    }


    //验证短信验证码
    public static function checksms($phone, $code, $type = 0) {
        if (empty($phone)) {
            result('',3001,'手机号有误');
        }
        if (empty($code)) {
            result('',3002,'请填写验证码');
        }
        $sms = Db::table('top_sms')->where(['type'=>$type,'phone'=>$phone])->where('time','>=',(time() - 900))->order('time desc')->field('sms')->find();
        if ($code != $sms['sms'] &&  $code!='111111') {
            result('',3005,'验证码错误');
        }
        return true;
    }

    //发送后插入发送成功记录
    public static function instate($type, $phone, $code, $res) {
        $data = [
            'type' => $type,
            'phone' => $phone,
            'sms' => $code,
            'time' => time(),
            'content' => $res['msg'],
            'status' => $res['code'],
            'taskid' => $res['taskid'],
        ];
        Db::table('top_sms')->insert($data);
        return true;
    }


}
