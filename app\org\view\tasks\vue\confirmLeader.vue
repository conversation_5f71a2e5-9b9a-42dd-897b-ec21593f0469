<style>
.el-form-item__content .el-input-group { vertical-align: middle;}
.el-dialog__body { padding: 15px 20px; }
</style>
<template>
  <el-dialog :title="title" :close-on-click-modal="false" modal="false" :visible.sync="visible" width="500px" @close="refresh()" append-to-body="true" label-position="top">
    <el-form ref="form" :model="data" class="tableSearch" label-position="right" label-width="120px" :rules="rules">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="企业名称" prop="company_name">
            {{data.company_name}}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="评审日期" prop="date">
            {{data.date}}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="选择组长" prop="leader_id">
            <el-select v-model="data.leader_id" placeholder="请选择组长" style="width: 100%">
              <el-option
                  v-for="item in data.experts"
                  :key="item.expert_id"
                  :label="item.expert_name"
                  :value="item.expert_id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="选择评审终端" prop="video_id">
            <el-select v-model="data.video_id" placeholder="请选择视频账号" style="width: 100%">
              <el-option
                  v-for="item in  filterByName1"
                  :key="item.NUMBER"
                  :label="item.NAME"
                  :value="item.NUMBER">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="选择监督终端" prop="video_id1">
            <el-select v-model="data.video_id1" placeholder="请选择视频账号" style="width: 100%">
              <el-option
                  v-for="item in  filterByName2"
                  :key="item.NUMBER"
                  :label="item.NAME"
                  :value="item.NUMBER">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="选择监督部门" prop="video_id2">
            <el-select v-model="data.video_id2" placeholder="请选择视频账号" style="width: 100%">
              <el-option
                  v-for="item in  filterByName3"
                  :key="item.NUMBER"
                  :label="item.NAME"
                  :value="item.NUMBER">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24" style="text-align: center;">
          <el-button type="primary" @click="submit()">确认</el-button>
          <el-button @click="visible = false">取消</el-button>
        </el-col>
      </el-row>
    </el-form>
  </el-dialog>
</template>
<script>
module.exports = {
  name: "confirmLeader",
  components: {},
  data: function() {
    return {
      visible: false,
      title: "确认组长",
      loading: false,
      data: {
        id: "",
        company_name: "",
        date: "",
        leader_id: "",
        video_id: "",
        video_id1: "",
        video_id2: "",
        experts: [],
        videos:[]
      },
      rules: {
        leader_id: [
          { required: true, message: "请选择组长", trigger: "change" }
        ]
      }
    };
  },
  computed: {
    filterByName1() {
      return this.data.videos.filter(item => /^评审/.test(item.NAME));
    },
    filterByName2() {
      return this.data.videos.filter(item => /^监督/.test(item.NAME));
    },
    filterByName3() {
      return this.data.videos.filter(item => /^(?!监督|评审).+/.test(item.NAME));
    }
  },
  methods: {
    open(row) {
      var _this = this;
      row.experts = [];
      _this.data = JSON.parse(JSON.stringify(row));
      _this.visible = true;
      _this.getExperts();
      _this.getVideos();
    },
    getExperts() {
      var _this = this;
      _this.loading = true;
      axios.post('getTaskExperts', {id: _this.data.id}).then(function(res) {
        _this.loading = false;
        if (res.data.code == 0) {
          _this.data.experts = res.data.data.experts;
        } else {
          _this.$message({
            message: res.data.msg,
            type: "error"
          });
        }
      }).catch(function(error) {
        _this.loading = false;
        console.log(error);
      });
    },
    getVideos(){
      var that=this;
      var httpurl='https://221.237.111.6:1614/scs/subscriberManageScs/getFleetSub';
      var params = {
        'subNumber':'',
        'subName' : '',
        'extNumber' : '',
        'fleetId' : 6,
        'pageNum' : 1,
        'pageSize' : 100,
      };
      axios.post(httpurl, params).then(function(res) {
        that.loading=false;
        that.data.videos = res.data.obj.list;
      },that);
    },
    submit() {
      var _this = this;
      _this.$refs["form"].validate(valid => {
        if (valid) {
          var param = {
            tasks_id: _this.data.id,
            expert_id: _this.data.leader_id,
            video_id: _this.data.video_id,
            video_id1: _this.data.video_id1,
            video_id2: _this.data.video_id2,
            video_name:'',
          };
          for (var i in _this.data.videos){
            if(_this.data.videos[i].NUMBER==param.video_id){
              param.video_name = _this.data.videos[i].NAME;
            }
            if(_this.data.videos[i].NUMBER==param.video_id1){
              param.video_name1 = _this.data.videos[i].NAME;
            }
            if(_this.data.videos[i].NUMBER==param.video_id2){
              param.video_name2 = _this.data.videos[i].NAME;
            }
          }
          _this.loading = true;
          axios.post('confirmLeader', param).then(function(res) {
            _this.loading = false;
            if (res.data.code == 0) {
              _this.$message({
                message: "组长确认成功",
                type: "success"
              });
              _this.visible = false;
              _this.$emit("ok");
            } else {
              _this.$message({
                message: res.data.msg,
                type: "error"
              });
            }
          }).catch(function(error) {
            _this.loading = false;
            console.log(error);
          });
        }
      });
    },
    refresh() {
      // this.$emit("ok");
    }
  }
};
</script>