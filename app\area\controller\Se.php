<?php
declare (strict_types = 1);

namespace app\area\controller;

use app\model\FileModel;
use Endroid\QrCode\QrCode;
use think\facade\Db;
use think\facade\View;
use Html2image\Assets\Html2img;
use think\facade\config;
use think\facade\request;

class Se extends Base
{


    public function htmlToImg($url,$filepath){
        $html=file_get_contents($url);
        $path= public_path().'storage/tmp/';
        $file_name=time();
        $data['filepath']=$filepath;
        $data['file_name']=$file_name;
        $back_url='/general/toppingsoft/index.php/file/base64_image_content';
        $html2img=new Html2img($back_url);
        $html2img->getImage($html,$data);
        exit('sssssss');
        result();
    }

    /**
     * @Apidoc\Title("评审任务")
     * @Apidoc\Desc("评审任务")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("日程")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function index($limit=20) {
        if (request()->isAjax()) {
            $title = $this->request->param('title','','trim');
            $confirm_status = $this->request->param('confirm_status','','trim');
            // 构建查询条件
            $where = [
                ['submit_status','>',0],
            ];
            // 权限相关条件
            $areaWhere = [
                ['area_id','=',$_SESSION['area']['id']],
                ['dept_id','in',$_SESSION['area']['manager']],
            ];
            if(!empty($title)){
                $where[] = ['company_name','like',"%{$title}%"];
            }
            if($confirm_status !== ''){
                $where[] = ['area_confirm_status','=',$confirm_status];
            }
            $res = Db::table('top_company_evaluate')
                ->where($where)
                ->where($areaWhere)
                ->order('date desc')
                ->paginate($limit)->each(function ($item, $key) {
                    $item['score'] = json_decode($item['score'],true);
                    $f = FileModel::getFile(0,$item['files'],'');
                    $item['files'] = [
                        'id' => $f['id'],
                        'name' => $f['name'],
                        'url' => $f['url'],
                    ];
                    return $item;
                });
            result($res);
        } else {
            return view();
        }
    }

    public function info($id=0){
        if (request()->isAjax()) {
            $task = Db::table('top_org_tasks')->where(['id'=>$id])->find();
            $details = Db::table('top_org_tasks_works')->where(['tasks_id'=>$task['id']])->order('sort')->select()->toArray();
            $detail = [];
            foreach ($details as $v){
                $v['files'] = json_decode($v['files'],true);
                $detail[$v['stage']][] = $v;
            }
            $review_flow = config('global.review_flow');
            foreach ($review_flow as $k=>$v){
                $review_work[] = [
                    'stage'=>$v['stage'],
                    'work' => $detail[$k],
                ];
            }
            $data['review_work'] = $review_work;
            $res = Db::table('top_org_tasks_experts')->where(['tasks_id'=>$id])->select()->toArray();
            $ids = [];
            foreach ($res as $v){
                $eids = explode(',',$v['element_id']);
                foreach ($eids as $v1){
                    if($v1>0){
                        $idarr[$v1] = [
                            'user_name' => $v['expert_name'],
                            'status' => $v['status'],
                        ];
                        $ids[] = $v1;
                    }
                }
            }
            $review_list = Db::table('top_org_tasks_element')->where('tasks_id','=',$id)->select()->toArray();
            $data['review_list'] = $review_list;
            result($data);
        } else {
            $task = Db::table('top_org_tasks')->where(['id'=>$id])->find();
            $re = Db::table('top_org_tasks_experts')->where(['tasks_id'=>$id,'expert_id'=>$_SESSION['expert']['id']])->find();
            $grading = Db::table('top_grading')->where(['id'=>$task['grading_id']])->find();
            $details = Db::table('top_org_tasks_works')->where(['tasks_id'=>$task['id']])->order('sort')->select()->toArray();
            $detail = [];
            foreach ($details as $v){
                $v['files'] = json_decode($v['files'],true);
                $detail[$v['stage']][] = $v;
            }
            $review_flow = config('flow.review_flow');
            foreach ($review_flow as $k=>$v){
                $review_work[] = [
                    'stage'=>$v['stage'],
                    'work' => $detail[$k],
                ];
            }
            $url = $_SERVER['REQUEST_SCHEME'].'://'.$_SERVER['HTTP_HOST'].'/general/toppingsoft/index.php/exam/mobile/myd?id='.$id;
            // 实例化QrCode对象
            $qrCode = new QrCode($url);
            // 设置二维码的尺寸
            $qrCode->setSize(500);
            // 设置二维码的边距
            $qrCode->setMargin(10);
            // 设置二维码的颜色和背景颜色
            $qrCode->setForegroundColor(['r' => 0, 'g' => 0, 'b' => 0, 'a' => 0]);
            View::assign('mydcodeimg', $qrCode->writeDataUri());
            View::assign('review_flow', $review_work);
            View::assign('task', $task);
            View::assign('re', $re);
            View::assign('grading', $grading);
            return view();
        }
    }

    /**
     * @Apidoc\Title("区县确认企业自评")
     * @Apidoc\Desc("区县确认企业自评")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("评审任务")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function confirm($id=0) {
        if (request()->isAjax()) {
            $remark = $this->request->param('remark','','trim');
            // 检查记录是否存在且属于当前区县管理
            $companyIds = Db::table('top_company_info')->where([
                ['area_id','=',$_SESSION['area']['id']],
                ['dept_id','in',$_SESSION['area']['manager']],
            ])->column("id");
            $evaluate = Db::table('top_company_evaluate')->where([
                ['id','=',$id],
                ['submit_status','=',1],
                ['company_id','in',$companyIds],
            ])->find();
            if(empty($evaluate)){
                result('',1002,'评审记录不存在或无权限操作');
            }
            if($evaluate['area_confirm_status'] == 1){
                result('',1003,'该记录已经确认过了');
            }
            $data = [
                'submit_status' => 2,  // 区县已确认
                'area_confirm_status' => 1,  // 区县确认状态：已确认
                'status' => 7,  // 区县确认状态：已确认
                'area_confirm_time' => date('Y-m-d H:i:s'),  // 区县确认时间
                'area_confirm_user' => $_SESSION['area']['user_name'],  // 区县确认人
                'area_confirm_remark' => $remark,  // 区县确认备注
            ];
            $result = Db::table('top_company_evaluate')->where(['id'=>$id])->update($data);
            if($result){
                result('',0,'确认成功');
            } else {
                result('',1001,'确认失败');
            }
        } else {
            result('',1000,'请求方式错误');
        }
    }

}
