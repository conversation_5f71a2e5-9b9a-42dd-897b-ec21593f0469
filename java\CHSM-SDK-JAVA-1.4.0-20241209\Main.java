package org.example;

import kl.hsm.client.model.SessionKey;
import kl.hsm.client.sdk.HsmClient;
import kl.hsm.common.KeyType;
import kl.hsm.server.svc.base.*;
import org.bouncycastle.util.encoders.Hex;
import kl.hsm.client.model.FileRead;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.Properties;

/**
 * 格尔密码机 HSM 客户端命令行工具
 * 提供密码机的各种操作功能，包括加解密、签名验签、密钥生成、摘要计算等
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class Main {
    /** HSM 客户端实例，支持多线程 */
    private static HsmClient hsmClient = new HsmClient();
    
    /** 配置文件路径 */
    private static final String CONFIG_FILE = "hsm-config.properties";
    
    /**
     * 程序入口点
     * @param args 命令行参数，第一个参数为操作命令
     */
    public static void main(String[] args) {
        //设置程序日志级别
        System.setProperty("org.slf4j.simpleLogger.defaultLogLevel", "off");
        System.setProperty("org.slf4j.simpleLogger.showDateTime", "false");
        System.setProperty("org.slf4j.simpleLogger.showThreadName", "false");
        System.setProperty("org.slf4j.simpleLogger.showLogName", "false");
        if (args.length == 0) {
            printUsage();
            return;
        }
        
        try {
            // 初始化HSM客户端连接
            initializeClient();
            // 执行用户指定的命令
            executeCommand(args);
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 初始化HSM客户端连接
     * 从配置文件读取连接参数，包括IP、端口、SSL配置等
     * 
     * @throws Exception 初始化失败时抛出异常
     */
    private static void initializeClient() throws Exception {
        Properties props = new Properties();
        try (InputStream input = new FileInputStream(CONFIG_FILE)) {
            props.load(input);
        }
        
        // 读取配置参数，提供默认值
        String ip = props.getProperty("hsm.ip", "127.0.0.1");
        int port = Integer.parseInt(props.getProperty("hsm.port", "10000"));
        int poolMax = Integer.parseInt(props.getProperty("hsm.pool.max", "4"));
        boolean ssl = Boolean.parseBoolean(props.getProperty("hsm.ssl", "false"));
        
        // 根据是否启用SSL选择不同的初始化方式
        if (ssl) {
            String keyStore = props.getProperty("hsm.keystore");
            String trustStore = props.getProperty("hsm.truststore");
            hsmClient.init(ip, port, ssl, keyStore, trustStore, poolMax);
        } else {
            hsmClient.init(ip, port, poolMax);
        }
        
        //System.out.println("HSM Client initialized successfully");
    }
    
    /**
     * 执行用户指定的命令
     * 根据第一个参数分发到对应的处理方法
     * 
     * @param args 命令行参数数组
     * @throws Exception 命令执行失败时抛出异常
     */
    private static void executeCommand(String[] args) throws Exception {
        String command = args[0].toLowerCase();
        
        switch (command) {
            // 新增简化的PHP调用方法
            case "enc":
                handleSimpleEncrypt(args);
                break;
            case "dec":
                handleSimpleDecrypt(args);
                break;
            case "hmac":
                handleSimpleHmac(args);
                break;
            case "verify":
                handleSimpleVerify(args);
                break;
            // 原有命令保持不变
            case "random":
                handleRandom(args);
                break;
            case "digest":
                handleDigest(args);
                break;
            case "encrypt":
                handleEncrypt(args);
                break;
            case "decrypt":
                handleDecrypt(args);
                break;
            case "sign":
                handleSign(args);
                break;
            case "genkey":
                handleGenerateKey(args);
                break;
            case "session":
                handleSession(args);
                break;
            case "mac":
                handleMac(args);
                break;
            case "file":
                handleFile(args);
                break;
            case "zuc":
                handleZuc(args);
                break;
            default:
                System.err.println("Unknown command: " + command);
                printUsage();
        }
    }
    
    /**
     * 处理随机数生成命令
     * 使用密码机生成指定长度的随机数
     * 
     * @param args 命令参数：random <length>
     * @throws Exception 生成随机数失败时抛出异常
     */
    private static void handleRandom(String[] args) throws Exception {
        if (args.length < 2) {
            System.err.println("Usage: random <length>");
            return;
        }
        
        int length = Integer.parseInt(args[1]);
        byte[] random = hsmClient.generateRandom(length);
        System.out.println("Random: " + Hex.toHexString(random));
    }
    
    /**
     * 处理摘要计算命令
     * 支持对字符串或文件计算摘要，支持多种摘要算法
     * 
     * @param args 命令参数：digest <algorithm> <data|file:path>
     * @throws Exception 计算摘要失败时抛出异常
     */
    private static void handleDigest(String[] args) throws Exception {
        if (args.length < 3) {
            System.err.println("Usage: digest <algorithm> <data|file:path>");
            return;
        }
        
        String algoStr = args[1].toUpperCase();
        Algo algo = Algo.valueOf(algoStr);
        String input = args[2];
        
        byte[] digest;
        // 判断是文件路径还是直接数据
        if (input.startsWith("file:")) {
            String filePath = input.substring(5);
            byte[] fileData = Files.readAllBytes(Paths.get(filePath));
            digest = hsmClient.digest(fileData, algo);
        } else {
            byte[] data = input.getBytes(StandardCharsets.UTF_8);
            digest = hsmClient.digest(data, algo);
        }
        
        System.out.println("Digest: " + Hex.toHexString(digest));
    }
    
    /**
     * 处理加密命令
     * 支持内部密钥加密和外部密钥加密，可指定加密模式、IV、填充方式
     * 
     * @param args 命令参数：encrypt <internal|external> <keyIndex|key> <data> [mode] [iv] [padding]
     * @throws Exception 加密失败时抛出异常
     */
    private static void handleEncrypt(String[] args) throws Exception {
        if (args.length < 4) {
            System.err.println("Usage: encrypt <internal|external> <keyIndex|key> <data> [mode] [iv] [padding]");
            return;
        }
        
        String type = args[1];
        String keyParam = args[2];
        String data = args[3];
        
        // 构建对称加密参数
        SymParam symParam = new SymParam();
        if (args.length > 4) symParam.setMode(EncMode.valueOf(args[4]));
        if (args.length > 5) symParam.setIv(Hex.decode(args[5]));
        if (args.length > 6) symParam.setPadding(Padding.valueOf(args[6]));
        
        byte[] dataBytes = data.getBytes(StandardCharsets.UTF_8);
        byte[] encrypted;
        
        switch (type) {
            case "internal":
                // 使用密码机内部密钥加密
                int keyIndex = Integer.parseInt(keyParam);
                encrypted = hsmClient.encrypt(keyIndex, symParam, dataBytes);
                break;
            case "external":
                // 使用外部提供的密钥加密
                byte[] key = Hex.decode(keyParam);
                encrypted = hsmClient.encrypt(Algo.SM4, key, symParam, dataBytes);
                break;
            default:
                throw new IllegalArgumentException("Invalid encrypt type: " + type);
        }
        
        System.out.println("Encrypted: " + Hex.toHexString(encrypted));
    }
    
    /**
     * 处理解密命令
     * 支持内部密钥解密和外部密钥解密，可指定解密模式、IV、填充方式
     * 
     * @param args 命令参数：decrypt <internal|external> <keyIndex|key> <encryptedData> [mode] [iv] [padding]
     * @throws Exception 解密失败时抛出异常
     */
    private static void handleDecrypt(String[] args) throws Exception {
        if (args.length < 4) {
            System.err.println("Usage: decrypt <internal|external> <keyIndex|key> <encryptedData> [mode] [iv] [padding]");
            return;
        }
        
        String type = args[1];
        String keyParam = args[2];
        String encryptedData = args[3];
        
        // 构建对称解密参数
        SymParam symParam = new SymParam();
        if (args.length > 4) symParam.setMode(EncMode.valueOf(args[4]));
        if (args.length > 5) symParam.setIv(Hex.decode(args[5]));
        if (args.length > 6) symParam.setPadding(Padding.valueOf(args[6]));
        
        byte[] encryptedBytes = Hex.decode(encryptedData);
        byte[] decrypted;
        
        switch (type) {
            case "internal":
                // 使用密码机内部密钥解密
                int keyIndex = Integer.parseInt(keyParam);
                decrypted = hsmClient.decrypt(keyIndex, symParam, encryptedBytes);
                break;
            case "external":
                // 使用外部提供的密钥解密
                byte[] key = Hex.decode(keyParam);
                decrypted = hsmClient.decrypt(Algo.SM4, key, symParam, encryptedBytes);
                break;
            default:
                throw new IllegalArgumentException("Invalid decrypt type: " + type);
        }
        
        System.out.println("Decrypted: " + new String(decrypted, StandardCharsets.UTF_8));
    }
    
    /**
     * 处理签名命令
     * 支持内部密钥签名和外部密钥签名，内部密钥需要提供密码获取使用权限
     * 
     * @param args 命令参数：sign <internal|external> <algorithm> <keyIndex|privateKey> <data> <digestAlgo> [password]
     * @throws Exception 签名失败时抛出异常
     */
    private static void handleSign(String[] args) throws Exception {
        if (args.length < 5) {
            System.err.println("Usage: sign <internal|external> <algorithm> <keyIndex|privateKey> <data> <digestAlgo> [password]");
            return;
        }
        
        String type = args[1];
        Algo algo = Algo.valueOf(args[2]);
        String keyParam = args[3];
        String data = args[4];
        Algo digestAlgo = args.length > 5 ? Algo.valueOf(args[5]) : Algo.SM3;
        String password = args.length > 6 ? args[6] : null;
        
        // 先计算数据摘要
        byte[] dataBytes = data.getBytes(StandardCharsets.UTF_8);
        byte[] digest = hsmClient.digest(dataBytes, digestAlgo);
        byte[] signature;
        
        switch (type) {
            case "internal":
                // 使用密码机内部私钥签名，需要密码授权
                int keyIndex = Integer.parseInt(keyParam);
                if (password != null) {
                    long session = hsmClient.openSession();
                    try {
                        // 获取私钥使用权限
                        hsmClient.getPrivateKeyAccessRight(session, keyIndex, password);
                        signature = hsmClient.sign(session, keyIndex, digest, digestAlgo);
                    } finally {
                        hsmClient.closeSession(session);
                    }
                } else {
                    throw new IllegalArgumentException("Password required for internal key signing");
                }
                break;
            case "external":
                // 使用外部提供的私钥签名
                byte[] privateKey = Hex.decode(keyParam);
                signature = hsmClient.sign(algo, privateKey, digest, digestAlgo);
                break;
            default:
                throw new IllegalArgumentException("Invalid sign type: " + type);
        }
        
        System.out.println("Signature: " + Hex.toHexString(signature));
    }
    
    /**
     * 处理验签命令
     * 支持内部公钥验签和外部公钥验签
     * 
     * @param args 命令参数：verify <internal|external> <algorithm> <keyIndex|publicKey> <data> <digestAlgo> <signature>
     * @throws Exception 验签失败时抛出异常
     */
    private static void handleVerify(String[] args) throws Exception {
        if (args.length < 6) {
            System.err.println("Usage: verify <internal|external> <algorithm> <keyIndex|publicKey> <data> <digestAlgo> <signature>");
            return;
        }
        
        String type = args[1];
        Algo algo = Algo.valueOf(args[2]);
        String keyParam = args[3];
        String data = args[4];
        Algo digestAlgo = Algo.valueOf(args[5]);
        String signatureStr = args[6];
        
        // 计算数据摘要
        byte[] dataBytes = data.getBytes(StandardCharsets.UTF_8);
        byte[] digest = hsmClient.digest(dataBytes, digestAlgo);
        byte[] signature = Hex.decode(signatureStr);
        boolean result;
        
        switch (type) {
            case "internal":
                // 使用密码机内部公钥验签
                int keyIndex = Integer.parseInt(keyParam);
                result = hsmClient.verify(keyIndex, digest, digestAlgo, signature);
                break;
            case "external":
                // 使用外部提供的公钥验签
                byte[] publicKey = Hex.decode(keyParam);
                result = hsmClient.verify(algo, publicKey, digest, digestAlgo, signature);
                break;
            default:
                throw new IllegalArgumentException("Invalid verify type: " + type);
        }
        
        System.out.println("Verification result: " + result);
    }
    
    /**
     * 处理密钥生成命令
     * 支持对称密钥和非对称密钥对生成
     * 
     * @param args 命令参数：genkey <symmetric|asymmetric> [algorithm] [length]
     * @throws Exception 密钥生成失败时抛出异常
     */
    private static void handleGenerateKey(String[] args) throws Exception {
        if (args.length < 2) {
            System.err.println("Usage: genkey <symmetric|asymmetric> [algorithm] [length]");
            return;
        }
        
        String type = args[1];
        
        switch (type) {
            case "symmetric":
                // 生成对称密钥（实际是生成随机数作为密钥）
                int length = args.length > 2 ? Integer.parseInt(args[2]) : 16;
                byte[] symKey = hsmClient.generateRandom(length);
                System.out.println("Symmetric Key: " + Hex.toHexString(symKey));
                break;
            case "asymmetric":
                // 生成非对称密钥对
                Algo algo = args.length > 2 ? Algo.valueOf(args[2]) : Algo.SM2;
                AsymKeypair keyPair = hsmClient.generateAsymKeyPair(algo);
                System.out.println("Public Key: " + Hex.toHexString(keyPair.getPubKey()));
                System.out.println("Private Key: " + Hex.toHexString(keyPair.getPrivKey()));
                break;
            default:
                throw new IllegalArgumentException("Invalid key type: " + type);
        }
    }
    
    /**
     * 处理会话管理命令
     * 支持会话的打开、关闭和会话密钥生成
     * 
     * @param args 命令参数：session <open|close|genkey> [sessionId] [params...]
     * @throws Exception 会话操作失败时抛出异常
     */
    private static void handleSession(String[] args) throws Exception {
        if (args.length < 2) {
            System.err.println("Usage: session <open|close|genkey> [sessionId] [params...]");
            return;
        }
        
        String operation = args[1];
        
        switch (operation) {
            case "open":
                // 打开新会话
                long sessionId = hsmClient.openSession();
                System.out.println("Session ID: " + sessionId);
                break;
            case "close":
                // 关闭指定会话
                if (args.length < 3) {
                    System.err.println("Usage: session close <sessionId>");
                    return;
                }
                long closeSessionId = Long.parseLong(args[2]);
                hsmClient.closeSession(closeSessionId);
                System.out.println("Session closed: " + closeSessionId);
                break;
            case "genkey":
                // 在会话中生成密钥
                if (args.length < 6) {
                    System.err.println("Usage: session genkey <sessionId> <keyIndex> <keyType> <length>");
                    return;
                }
                long genSessionId = Long.parseLong(args[2]);
                int keyIndex = Integer.parseInt(args[3]);
                KeyType keyType = KeyType.valueOf(args[4]);
                int length = Integer.parseInt(args[5]);
                
                // 生成会话密钥
                SessionKey sessionKey = hsmClient.generateSessionKey(genSessionId, keyIndex, keyType, length);
                System.out.println("Session Key Handle: " + sessionKey.getSessionKeyHandle());
                System.out.println("Session Key Cipher: " + Hex.toHexString(sessionKey.getSessionKeyCipher()));
                break;
            default:
                throw new IllegalArgumentException("Invalid session operation: " + operation);
        }
    }
    
    /**
     * 处理MAC计算命令
     * 支持HMAC、CMAC、GMAC等多种MAC算法
     * 
     * @param args 命令参数：mac <hmac|cmac|gmac> <keyIndex> <algorithm> <data> [iv] [aad]
     * @throws Exception MAC计算失败时抛出异常
     */
    private static void handleMac(String[] args) throws Exception {
        if (args.length < 5) {
            System.err.println("Usage: mac <hmac|cmac|gmac> <keyIndex> <algorithm> <data> [iv] [aad]");
            return;
        }
        
        String macType = args[1];
        int keyIndex = Integer.parseInt(args[2]);
        Algo algo = Algo.valueOf(args[3]);
        byte[] data = args[4].getBytes(StandardCharsets.UTF_8);
        
        byte[] mac;
        switch (macType) {
            case "hmac":
                // 计算HMAC
                mac = hsmClient.hmac(keyIndex, algo, data);
                break;
            case "cmac":
                // 计算CMAC，需要IV
                byte[] iv = args.length > 5 ? Hex.decode(args[5]) : new byte[16];
                mac = hsmClient.cmac(keyIndex, algo, data, iv);
                break;
            case "gmac":
                // 计算GMAC，需要IV和AAD
                byte[] gcmIv = args.length > 5 ? Hex.decode(args[5]) : "123456781234".getBytes();
                byte[] aad = args.length > 6 ? args[6].getBytes() : "1234".getBytes();
                mac = hsmClient.gmac(keyIndex, algo, data, gcmIv, aad);
                break;
            default:
                throw new IllegalArgumentException("Invalid MAC type: " + macType);
        }
        
        System.out.println("MAC: " + Hex.toHexString(mac));
    }
    
    /**
     * 处理文件操作命令
     * 支持文件的创建、读取、写入、删除操作
     * 
     * @param args 命令参数：file <create|read|write|delete> [params...]
     * @throws Exception 文件操作失败时抛出异常
     */
    private static void handleFile(String[] args) throws Exception {
        if (args.length < 2) {
            System.err.println("Usage: file <create|read|write|delete> [params...]");
            return;
        }
        
        String operation = args[1];
        
        switch (operation) {
            case "create":
                // 创建文件
                if (args.length < 4) {
                    System.err.println("Usage: file create <fileName> <sizeKB>");
                    return;
                }
                String fileName = args[2];
                int fileSize = Integer.parseInt(args[3]);
                hsmClient.createFile(fileName, fileSize);
                System.out.println("File created: " + fileName);
                break;
            case "read":
                // 读取文件内容
                if (args.length < 5) {
                    System.err.println("Usage: file read <fileName> <offset> <length>");
                    return;
                }
                String readFileName = args[2];
                int offset = Integer.parseInt(args[3]);
                int length = Integer.parseInt(args[4]);
                
                FileRead fileRead = hsmClient.readFile(readFileName, offset, length);
                System.out.println("File data: " + Hex.toHexString(fileRead.getData()));
                break;
            case "write":
                // 写入文件内容
                if (args.length < 5) {
                    System.err.println("Usage: file write <fileName> <offset> <data>");
                    return;
                }
                String writeFileName = args[2];
                int writeOffset = Integer.parseInt(args[3]);
                byte[] writeData = Hex.decode(args[4]);
                hsmClient.writeFile(writeFileName, writeOffset, writeData, writeData.length);
                System.out.println("File written: " + writeFileName);
                break;
            case "delete":
                // 删除文件
                if (args.length < 3) {
                    System.err.println("Usage: file delete <fileName>");
                    return;
                }
                String deleteFileName = args[2];
                hsmClient.deleteFile(deleteFileName);
                System.out.println("File deleted: " + deleteFileName);
                break;
            default:
                throw new IllegalArgumentException("Invalid file operation: " + operation);
        }
    }
    
    /**
     * 处理祖冲之算法命令
     * 支持ZUC-EIA（完整性算法）和ZUC-EEA（加密算法）
     * 
     * @param args 命令参数：zuc <eia|eea> <data> <key> <count> <bearer> <direction>
     * @throws Exception 祖冲之算法操作失败时抛出异常
     */
    private static void handleZuc(String[] args) throws Exception {
        if (args.length < 7) {
            System.err.println("Usage: zuc <eia|eea> <data> <key> <count> <bearer> <direction>");
            return;
        }
        
        String type = args[1];
        byte[] data = args[2].getBytes(StandardCharsets.UTF_8);
        byte[] key = Hex.decode(args[3]);
        int count = Integer.parseInt(args[4]);
        int bearer = Integer.parseInt(args[5]);
        int direction = Integer.parseInt(args[6]);
        
        if ("eia".equals(type)) {
            // ZUC完整性算法，计算MAC
            byte[] mac = hsmClient.zucEia(data, key, count, bearer, direction);
            System.out.println("ZUC EIA MAC: " + Hex.toHexString(mac));
        } else if ("eea".equals(type)) {
            // ZUC加密算法
            byte[] result = hsmClient.zucEea(data, key, count, bearer, direction);
            System.out.println("ZUC EEA Result: " + Hex.toHexString(result));
        } else {
            throw new IllegalArgumentException("Invalid ZUC operation: " + type);
        }
    }
    
    /**
     * 打印使用说明
     * 显示所有支持的命令和参数格式
     */
    private static void printUsage() {
        System.out.println("=== 格尔密码机 HSM 客户端命令行工具 ===");
        System.out.println("Usage: java -jar hsm-client.jar <command> [options]");
        System.out.println();
        System.out.println("PHP简化命令:");
        System.out.println("  enc <data>                                          - 加密数据");
        System.out.println("  dec <encryptedData>                                 - 解密数据");
        System.out.println("  hmac <data>                                         - 计算HMAC");
        System.out.println("  verify <data> <hmac>                                - 验证HMAC");
        System.out.println();
        System.out.println("完整命令:");
        System.out.println("  random <length>                                    - 生成指定长度的随机数");
        System.out.println("  digest <algorithm> <data|file:path>                - 计算摘要值");
        System.out.println("    算法: SM3, MD5, SHA1, SHA2_256, SHA2_384, SHA2_512");
        System.out.println();
        System.out.println("  encrypt <internal|external> <key> <data> [options] - 加密数据");
        System.out.println("    internal: 使用密码机内部密钥，key为密钥索引");
        System.out.println("    external: 使用外部密钥，key为十六进制密钥");
        System.out.println("    options: [mode] [iv] [padding]");
        System.out.println();
        System.out.println("  decrypt <internal|external> <key> <data> [options] - 解密数据");
        System.out.println("    参数格式同encrypt命令");
        System.out.println();
        System.out.println("  sign <internal|external> <algo> <key> <data> <digestAlgo> [password]");
        System.out.println("    - 数字签名，internal类型需要提供密码");
        System.out.println();
        System.out.println("  verify <internal|external> <algo> <key> <data> <digestAlgo> <signature>");
        System.out.println("    - 验证数字签名");
        System.out.println();
        System.out.println("  genkey <symmetric|asymmetric> [algorithm] [length] - 生成密钥");
        System.out.println("    symmetric: 生成对称密钥");
        System.out.println("    asymmetric: 生成非对称密钥对，支持SM2、RSA等");
        System.out.println();
        System.out.println("  session <open|close|genkey> [params]               - 会话管理");
        System.out.println("    open: 打开新会话");
        System.out.println("    close <sessionId>: 关闭会话");
        System.out.println("    genkey <sessionId> <keyIndex> <keyType> <length>: 生成会话密钥");
        System.out.println();
        System.out.println("  mac <hmac|cmac|gmac> <keyIndex> <algo> <data> [iv] [aad]");
        System.out.println("    - 计算消息认证码");
        System.out.println();
        System.out.println("  file <create|read|write|delete> [params]           - 文件操作");
        System.out.println("    create <fileName> <sizeKB>: 创建文件");
        System.out.println("    read <fileName> <offset> <length>: 读取文件");
        System.out.println("    write <fileName> <offset> <data>: 写入文件");
        System.out.println("    delete <fileName>: 删除文件");
        System.out.println();
        System.out.println("  zuc <eia|eea> <data> <key> <count> <bearer> <direction>");
        System.out.println("    - 祖冲之算法，eia为完整性算法，eea为加密算法");
        System.out.println();
        System.out.println("配置文件: " + CONFIG_FILE);
        System.out.println("请确保配置文件包含正确的HSM服务器连接信息");
    }

    /**
     * 简化加密 - 专为PHP调用设计
     * @param args enc <data>
     */
    private static void handleSimpleEncrypt(String[] args) throws Exception {
        if (args.length < 2) return;
        
        String data = args[1];
        SymParam param = new SymParam();
        param.setMode(EncMode.CBC);
        param.setPadding(Padding.PKCS7Padding);
        param.setIv(new byte[16]); // 全零IV
        
        byte[] encrypted = hsmClient.encrypt(1, param, data.getBytes(StandardCharsets.UTF_8));
        System.out.print(Base64.getEncoder().encodeToString(encrypted));
    }

    /**
     * 简化解密 - 专为PHP调用设计
     * @param args dec <encryptedData>
     */
    private static void handleSimpleDecrypt(String[] args) throws Exception {
        if (args.length < 2) return;
        
        String encryptedData = args[1];
        SymParam param = new SymParam();
        param.setMode(EncMode.CBC);
        param.setPadding(Padding.PKCS7Padding);
        param.setIv(new byte[16]); // 全零IV
        
        byte[] encrypted = Base64.getDecoder().decode(encryptedData);
        byte[] decrypted = hsmClient.decrypt(1, param, encrypted);
        System.out.print(new String(decrypted, StandardCharsets.UTF_8));
    }

    /**
     * 简化HMAC计算 - 专为PHP调用设计
     * @param args hmac <data>
     */
    private static void handleSimpleHmac(String[] args) throws Exception {
        if (args.length < 2) return;
        
        String data = args[1];
        byte[] hmac = hsmClient.hmac(2, Algo.SM3, data.getBytes(StandardCharsets.UTF_8));
        System.out.print(Hex.toHexString(hmac));
    }

    /**
     * 简化HMAC验证 - 专为PHP调用设计
     * @param args verify <data> <hmac>
     */
    private static void handleSimpleVerify(String[] args) throws Exception {
        if (args.length < 3) return;
        
        String data = args[1];
        String expectedHmac = args[2];
        
        byte[] hmac = hsmClient.hmac(2, Algo.SM3, data.getBytes(StandardCharsets.UTF_8));
        String actualHmac = Hex.toHexString(hmac);
        
        System.out.print(expectedHmac.equalsIgnoreCase(actualHmac) ? "true" : "false");
    }
}

