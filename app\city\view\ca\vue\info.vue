<template>
  <el-dialog :title="title" :close-on-click-modal="false" modal="false" :visible.sync="visible" width="900px"
             @close="refresh()" append-to-body="true" label-position="top">
    <h2 style="text-align: center;font-weight: 700;">{{data.title}}</h2>
    <div style="line-height: 40px;"><span style="float: right;">日期：{{data.date}}</span></div>
    <div>
      <el-image
          style="width: 100%;"
          :src="data.image"
          fit="contain"></el-image>
    </div>
  </el-dialog>
</template>
<script>
module.exports = {
  name: "info",
  data() {
    return {
      id:0,
      pdfUrl:'',
      visible: false,
      fileVisible: false,
      title: '公示详情',
      loading: false,
      user_id: 0,
      data: {
        title:'',
        date:'',
        content:'',
        files:[],
      },
      height: document.documentElement.clientHeight - 310,
    }
  },
  mounted(){
    var that = this;
    window.addEventListener('resize',function(){
      clearTimeout(that.resizeFlag);
      that.resizeFlag =setTimeout(function(){
        that.height = document.documentElement.clientHeight -50
      },300)
    })
  },
  methods: {
    /**
     * 打开弹窗调用方法
     * */
    open: function (row) {
      var _this =this;
      _this.id = row.id;
      _this.visible = true;
      _this.data=row;
      return true;
      //this.clear1();
      axios.post('publictyInfo', {
        id:row.id,
      }).then(function (res) {
        if (res.data.code == 0) {
          if(res.data.data.files.length>0){
            _this.fileVisible = true;
          }else{
            _this.fileVisible = false;
          }
          _this.data=res.data.data;
        }else {
          _this.$message({
            message: res.data.msg,
            type: res.data.type
          });
        }
      }).catch(function (error) {
        console.log("出现错误:",error);
      });
    },
    refresh: function () {
      this.visible = false;
    }
  }
}
</script>


