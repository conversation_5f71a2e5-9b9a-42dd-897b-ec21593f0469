<?php

namespace app\validate;

use think\Validate;

class GradingVerify extends Validate
{
    protected $rule = [
        'id' => 'require|number',
        'level' => 'require',
        'type' => 'require',
        'nature' =>'require',
    ];

    protected $message = [
        'id' => '参数错误',
        'level' => '请选择申请等级',
        'type' => '请选择申请类型',
        'nature' =>'请选择创建性质',
    ];

    protected $scene = [
        'save' => ['level','type','nature'],
    ];

    public function userCheck($scene,$data){
        $validate = new self();
        $re = $validate->scene($scene)->check($data);
        if(!$re){
            result('',1001,$validate->getError());
//            if(request()->isAjax()){
//                result('',1001,$validate->getError());
//            }else{
//                return $validate->getError();
//            }
        }
    }

}