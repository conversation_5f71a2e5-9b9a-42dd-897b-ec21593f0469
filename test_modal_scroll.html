<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模态框滚动测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .btn {
            padding: 10px 20px;
            margin: 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        .btn-danger { background-color: #dc3545; color: white; }

        /* 模态框样式 - 从DmQuery.php复制的改进样式 */
        .modal { 
            display: none; 
            position: fixed; 
            z-index: 1000; 
            left: 0; 
            top: 0; 
            width: 100%; 
            height: 100%; 
            background-color: rgba(0,0,0,0.5); 
        }
        
        .modal-content { 
            background-color: white; 
            margin: 2% auto; 
            padding: 20px; 
            border-radius: 8px; 
            width: 600px; 
            max-width: 90%; 
            max-height: 90vh; 
            position: relative; 
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 15px;
            border-bottom: 1px solid #e9ecef;
            margin-bottom: 20px;
        }

        .modal-header h3 {
            margin: 0;
            color: #2c3e50;
            font-size: 18px;
        }

        .modal-header .close {
            font-size: 24px;
            font-weight: bold;
            color: #aaa;
            cursor: pointer;
            border: none;
            background: none;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s;
        }

        .modal-header .close:hover {
            color: #000;
            background-color: #f8f9fa;
        }

        .form-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); 
            gap: 15px; 
            max-height: 60vh; 
            overflow-y: auto; 
            padding-right: 10px;
        }

        .form-row { margin-bottom: 15px; }
        .form-row label { 
            display: block; 
            margin-bottom: 5px; 
            font-weight: bold; 
            color: #2c3e50; 
        }
        .form-row input, .form-row select { 
            width: 100%; 
            padding: 8px; 
            border: 1px solid #ddd; 
            border-radius: 4px; 
            font-size: 14px;
        }
        .form-row input:focus, .form-row select:focus { 
            outline: none; 
            border-color: #3498db; 
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .modal-footer {
            position: sticky;
            bottom: 0;
            background: white;
            padding: 15px 0 0 0;
            margin-top: 20px;
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .form-grid::-webkit-scrollbar {
            width: 8px;
        }
        .form-grid::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }
        .form-grid::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }
        .form-grid::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        @media (max-width: 768px) {
            .modal-content { 
                width: 95%; 
                margin: 2% auto; 
                max-height: 95vh;
                padding: 15px;
            }
            .form-grid { 
                grid-template-columns: 1fr; 
                max-height: 60vh;
            }
            .modal-footer {
                flex-direction: column-reverse;
                gap: 8px;
            }
            .modal-footer button {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>模态框滚动条测试</h1>
        <p>这个页面用于测试修复后的模态框滚动效果。点击下面的按钮测试不同场景：</p>
        
        <button class="btn btn-primary" onclick="showTestModal(10)">测试 10 个字段</button>
        <button class="btn btn-success" onclick="showTestModal(20)">测试 20 个字段</button>
        <button class="btn btn-warning" onclick="showTestModal(30)">测试 30 个字段</button>
        <button class="btn btn-danger" onclick="showTestModal(50)">测试 50 个字段</button>

        <h2>修复内容</h2>
        <ul>
            <li>✅ 模态框添加了最大高度限制 (90vh)</li>
            <li>✅ 表单区域添加了垂直滚动条</li>
            <li>✅ 保存按钮固定在底部，始终可见</li>
            <li>✅ 优化了滚动条样式</li>
            <li>✅ 响应式设计，适配移动设备</li>
            <li>✅ 改善了视觉效果和用户体验</li>
        </ul>
    </div>

    <script>
        function showTestModal(fieldCount) {
            // 创建测试模态框
            let modalHtml = '<div class="modal" id="testModal" style="display: block;">';
            modalHtml += '<div class="modal-content">';
            modalHtml += '<div class="modal-header">';
            modalHtml += `<h3>测试编辑 - ${fieldCount} 个字段</h3>`;
            modalHtml += '<span class="close" onclick="closeTestModal()">&times;</span>';
            modalHtml += '</div>';
            
            modalHtml += '<form>';
            modalHtml += '<div class="form-grid">';
            
            // 生成指定数量的字段
            for (let i = 1; i <= fieldCount; i++) {
                const fieldTypes = ['VARCHAR(50)', 'INTEGER', 'DATE', 'TEXT', 'DECIMAL(10,2)'];
                const fieldType = fieldTypes[i % fieldTypes.length];
                const isRequired = i % 5 === 0;
                const isReadonly = i % 10 === 0;
                
                modalHtml += `<div class="form-row">
                    <label>字段_${i} (${fieldType})${isRequired ? ' <span style="color: red;">*</span>' : ''}</label>
                    <input type="text" name="field_${i}" value="测试数据 ${i}" ${isReadonly ? 'readonly' : ''} ${isRequired ? 'required' : ''}>
                </div>`;
            }
            
            modalHtml += '</div>';
            
            // 固定底部工具栏
            modalHtml += '<div class="modal-footer">';
            modalHtml += '<button type="button" class="btn btn-danger" onclick="closeTestModal()">取消</button>';
            modalHtml += '<button type="submit" class="btn btn-primary">保存</button>';
            modalHtml += '</div>';
            
            modalHtml += '</form></div></div>';
            
            // 移除现有模态框
            const existingModal = document.getElementById('testModal');
            if (existingModal) {
                existingModal.remove();
            }
            
            // 添加新模态框
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            
            // 绑定表单提交事件
            document.querySelector('#testModal form').addEventListener('submit', function(e) {
                e.preventDefault();
                alert('表单提交成功！（这只是测试）');
                closeTestModal();
            });
        }

        function closeTestModal() {
            const modal = document.getElementById('testModal');
            if (modal) {
                modal.remove();
            }
        }

        // 点击模态框外部关闭
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal')) {
                closeTestModal();
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeTestModal();
            }
        });
    </script>
</body>
</html>
