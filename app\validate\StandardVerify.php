<?php

namespace app\validate;

use think\Validate;

class StandardVerify extends Validate
{
    protected $rule = [
        'id' => 'require|number',
        'standard_id' => 'require',
        'level' =>'require',
    ];

    protected $message = [
        'id' => '参数错误',
        'standard_id' => '请选择评定标准',
        'level' =>'请选择标准等级',
    ];

    protected $scene = [
        'save' => ['standard_id','level'],
    ];

    public function userCheck($scene,$data){
        $validate = new self();
        $re = $validate->scene($scene)->check($data);
        if(!$re){
            result('',1001,$validate->getError());
//            if(request()->isAjax()){
//                result('',1001,$validate->getError());
//            }else{
//                return $validate->getError();
//            }
        }
    }

}