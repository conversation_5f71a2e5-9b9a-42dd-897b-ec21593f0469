<?php

// 应用公共文件
use think\facade\Db;
use think\Response;
use think\facade\Request;
use think\exception\HttpResponseException;
use think\facade\Cache;

if (!function_exists('sso_curl_post')) {

    function sso_curl_post($url, $data = array()) {
        $http = $_SERVER['REQUEST_SCHEME'];
        $host = $_SERVER['HTTP_HOST'];
        $port = $_SERVER['SERVER_PORT'];
        $url = $http."://" . $host . ":" . $port . str_replace($_SERVER['PATH_INFO'],'',$_SERVER['REQUEST_URI']) . $url;
        $cookie = "PHPSESSID=" . session_id();
        session_write_close();
        $header = array(
            'Accept: application/json',
        );
        //初始化
        $curl = curl_init();
        //设置抓取的url
        curl_setopt($curl, CURLOPT_URL, $url);
        //设置头文件的信息作为数据流输出
        curl_setopt($curl, CURLOPT_HEADER, 0);
        //设置获取的信息以文件流的形式返回，而不是直接输出。
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        // 超时设置
        curl_setopt($curl, CURLOPT_TIMEOUT, 2);
        // 超时设置，以毫秒为单位
        // curl_setopt($curl, CURLOPT_TIMEOUT_MS, 500);
        // 设置请求头
        curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        //设置post方式提交
        curl_setopt($curl, CURLOPT_POST, 0);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        curl_setopt($curl, CURLOPT_COOKIE, $cookie);
        //执行命令
        $data = curl_exec($curl);
        //dd($url);
        // print_r($data);die;
        // 显示错误信息
        curl_close($curl);
        if (curl_error($curl)) {
            return "ERROR";
        } else {
            // 打印返回的内容
            return $data;
        }
    }

}

