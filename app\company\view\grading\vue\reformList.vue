<style scoped>
.reformBox{
  width: 600px;
  margin: auto;
  margin-top: 30px;
}
.avatar-uploader{
  margin-top: 10px;
}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 80px;
  height: 80px;
  line-height: 80px;
  text-align: center;
}
.avatar {
  width: 80px;
  height: 80px;
  display: block;
}
.el-upload-dragger {
  background-color: #fff;
  border: 0px dashed #d9d9d9;
  border-radius: 6px;
  box-sizing: border-box;
  width: 80px;
  height: 80px;
  line-height: 88px;
  text-align: center;
  position: relative;
  overflow: hidden;
}
.el-upload--picture-card {
  background-color: #fbfdff;
  border: 1px dashed #c0ccda;
  border-radius: 6px;
  box-sizing: border-box;
  width: 80px;
  height: 80px;
  line-height: 80px;
  vertical-align: top;
}
.el-upload-list--picture-card .el-upload-list__item {
  overflow: hidden;
  background-color: #fff;
  border: 1px solid #c0ccda;
  border-radius: 6px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 80px;
  height: 80px;
  margin: 0 8px 8px 0;
  display: inline-block;
}
.el-upload-list--picture-card .el-upload-list__item-thumbnail {
  width: 80px;
  height: 80px;
}
/* 隐藏加载动画图标 */
.el-upload-list__item-status {
  display: none !important;
}

/* 或更精确地针对 loading 状态 */
.el-upload-list__item .el-icon-loading {
  display: none !important;
}
/* 通过 CSS 隐藏加载图标 */
/deep/ .el-upload-list__item-status {
  display: none !important;
}
/* Vue 3 */
::v-deep(.el-upload-list__item-status) {
  display: none !important;
}
.is-success {
  position: relative; /* 关键定位基础 */
}

/* 角标容器 */
.is-success::after {
  content: '';
  position: absolute;
  top: -6px;    /* 向上偏移 */
  right: -6px;  /* 向右偏移 */
  width: 20px;
  height: 20px;
  background: #67c23a; /* ElementUI 成功绿 */
  border-radius: 50%;  /* 圆形背景 */
  box-shadow: 0 2px 4px rgba(0,0,0,0.1); /* 立体感 */
}

/* 对勾图标 */
.is-success::before {
  content: "";
  position: absolute;
  top: 1px;
  right: -1px;
  width: 10px;
  height: 3px;
  border-left: 2px solid rgb(255, 255, 255);
  border-bottom: 2px solid rgb(255, 255, 255);
  transform: rotate(-45deg);
  z-index: 1;
}
</style>

<template>
  <el-dialog :title="title" :close-on-click-modal="false"
             modal="false" top="2vh"
             :visible.sync="visible" width="98%"
             append-to-body="true" label-position="top">

    <el-table
        :data="tableData"
        style="width: 100%">
      <el-table-column
          type="index"
          label="序号"
          align="center"
          width="50">
      </el-table-column>
      <el-table-column
          prop="reform"
          label="整改内容"
          min-width="180">
      </el-table-column>
      <el-table-column
          prop="reform_date"
          label="整改时间"
          width="150">
        <template slot-scope="scope">
          <el-date-picker
              v-model="scope.row.reform_date"
              style="width: 130px"
              size="mini"
              type="date"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd">
          </el-date-picker>
        </template>
      </el-table-column>
      <el-table-column
          prop="name"
          label="整改前情况"
          min-width="380">
        <template slot-scope="scope">
          <el-input v-model="scope.row.reform_before_info" type="textarea" :autosize="{minRows: 2}" placeholder="请输入整改前情况"></el-input>
          <el-upload
              :ref="'reform_before_files'+scope.$index"
              class="avatar-uploader"
              accept=".jpg,.jpeg,.png"
              action="upload"
              drag
              multiple
              :file-list="tableData[scope.$index].reform_before_files"
              list-type="picture-card"
              :auto-upload="true"
              :on-success="(response,file,fileList)=>handleAvatarSuccess(response,file,fileList,scope.$index,'reform_before_files')"
              :before-upload="beforeAvatarUpload"
              :on-remove="handleRemove">

            <i slot="default" class="el-icon-plus"></i>

              <div slot="file" slot-scope="{file}">
                <img
                    class="el-upload-list__item-thumbnail"
                    :src="file.url" alt=""
                >
                <span class="el-upload-list__item-actions">
                  <span
                      class="el-upload-list__item-preview"
                      @click="handlePictureCardPreview(file)"
                  >
                    <i class="el-icon-zoom-in"></i>
                  </span>
                  <span
                      class="el-upload-list__item-delete"
                      @click="handleCustomRemove(file,scope.$index,'reform_before_files')"
                  >
                    <i class="el-icon-delete"></i>
                  </span>
                </span>
              </div>
          </el-upload>
        </template>
      </el-table-column>
      <el-table-column
          prop="address"
          label="整改后情况"
          min-width="380">
        <template slot-scope="scope">
          <el-input v-model="scope.row.reform_affter_info" type="textarea" :autosize="{minRows: 2}" placeholder="请输入整改后情况"></el-input>
          <el-upload
              :ref="'reform_affter_files'+scope.$index"
              class="avatar-uploader"
              accept=".jpg,.jpeg,.png"
              action="upload"
              drag
              multiple
              :file-list="tableData[scope.$index].reform_affter_files"
              list-type="picture-card"
              :auto-upload="true"
              :on-success="(response,file,fileList)=>handleAvatarSuccess(response,file,fileList,scope.$index,'reform_affter_files')"
              :before-upload="beforeAvatarUpload"
              :on-remove="handleRemove">

            <i slot="default" class="el-icon-plus"></i>

            <div slot="file" slot-scope="{file}">
              <img
                  class="el-upload-list__item-thumbnail"
                  :src="file.url" alt=""
              >
              <span class="el-upload-list__item-actions">
                  <span
                      class="el-upload-list__item-preview"
                      @click="handlePictureCardPreview(file)"
                  >
                    <i class="el-icon-zoom-in"></i>
                  </span>
                  <span
                      class="el-upload-list__item-delete"
                      @click="handleCustomRemove(file,scope.$index,'reform_affter_files')"
                  >
                    <i class="el-icon-delete"></i>
                  </span>
                </span>
            </div>
          </el-upload>
        </template>
      </el-table-column>
      <el-table-column
          prop="name"
          label="备注"
          min-width="200">
        <template slot-scope="scope">
          <el-input v-model="scope.row.remark" type="textarea" :autosize="{minRows: 2}" placeholder="请输入备注"></el-input>
          <div style="width: 80px;height: 80px;margin-top: 20px;"></div>
        </template>
      </el-table-column>
    </el-table>

    <div slot="footer" class="dialog-footer" style="text-align: center">
      <el-button type="primary" @click="saveFun()">保 存</el-button>
      <el-button @click="visible = false">取 消</el-button>
      <!--      <el-button type="primary" @click="submit()">提交申请</el-button>-->
    </div>

    <reform ref="reform" @ok="okFun()"></reform>

    <el-dialog :visible.sync="dialogVisible" :modal=false>
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>

  </el-dialog>
</template>
<script>
module.exports = {
  name: "add",
  components: {
    'reform': 'url:/general/toppingsoft/app/company/view/grading/vue/reform.vue?v=1',
  },
  data:function() {
    return {
      dialogImageUrl: '',
      dialogVisible: false,
      grading_id:0,
      grading_data: [],
      visible: false,
      title: '企业整改详情',
      loading: false,
      noHidden: true,
      wrapHeight: 0,
      delDialogitem: 0,
      imgLoad: false,
      centerDialogVisible: false,
      imageUrl: '',
      tableData: []
    }
  },
  mounted: function(){
    // this.getInfo();
  },
  created:function(){
  },
  methods: {
    handleRemove(file) {
      // 调用接口删除服务器文件‌
      var _this = this;
      var param = {
        code: file.code,
        // code: file.response.data.code,
      };
      axios.post("deleteFile", param).then(function (res) {
        if (res.code == 0) {
          _this.$message({
            message: res.msg,
            type: "success"
          });
        }
      }).catch(function (error) {
        console.log("出现错误:",error);
      });
    },
    handleCustomRemove(file,index,field) {
      console.log(file,index,field)
      this.$refs[field+index].handleRemove(file);
      this.tableData[index][field].splice(this.tableData[index][field].indexOf(file),1);
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    handleAvatarSuccess(res, file,fileList,index,field) {
      // this.imageUrl = URL.createObjectURL(file.raw);
      //
      // var files = [];
      // console.log(res)
      // for(var i in fileList){
      //   files.push(fileList[i].response??fileList[i]);
      // }
      //
      console.log(this.tableData[index][field])
      // console.log(field)
      this.tableData[index][field].push(res.data);
      console.log(this.tableData[index][field])

      // this.tableData[field] = res.data.code;
      // this.tableData[field+'Url'] = res.data.url;
    },
    beforeAvatarUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 10;

      const isImage = ['image/jpeg', 'image/png'].includes(file.type);
      if (!isImage) {
        this.$message.error('仅支持上传JPG/PNG格式图片！');
      }

      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 10MB!');
      }
      return isImage && isLt2M;
    },
    //数据加载
    getData() {
      var _this = this;
      var param = {};
      param._ajax = 1;
      _this.loading = true;
      axios.post('index',param).then(function (res) {
        if (res.data.code == 0) {
          _this.data = res.data.data.data;
          _this.$nextTick(() => {
            // 这里的代码会在DOM更新完成后执行
            _this.loading = false;
          });
          _this.handleRefreshTable();
        } else {
          _this.$message({
            message: res.data.msg,
            type: "error"
          });
          return false;
        }
      }).catch(function (error) {
        console.log(error);
      });
    },
    /**
     * 打开弹窗调用方法
     * */
    open: function (row) {
      var _this =this;
      _this.visible = true;
      _this.grading_id = row.id;
      _this.grading_data = row;

      //获取列表
      this.getInfo();
    },
    saveFun: function () {
      var _this = this;
      var param = {
        grading_id: _this.grading_id,
        data: _this.tableData
      };
      axios.post("reformItemSave", param).then(function (res) {
        _this.$message({
          message: res.data.msg,
          type: res.data.type
        });
        if (res.data.code == 0) {
          _this.visible = false;
          _this.$emit("ok");
        }
      }).catch(function (error) {
        console.log("出现错误:",error);
      });
    },
    submit: function () {
      var _this = this;
      var param = {
        grading_id: _this.grading_id,
        data: _this.tableData
      };
      axios.post("reformItemSave", param).then(function (res) {
        _this.$message({
          message: res.data.msg,
          type: res.data.type
        });
        if (res.data.code == 0) {
          //保存成功后调用上传整改报告
          _this.$refs.reform.open(_this.grading_data);
        }
      }).catch(function (error) {
        console.log("出现错误:",error);
      });
    },
    okFun:function(){
      this.visible = false;
      this.$emit("ok");
    },
    cancelFile(file) {
      console.log(file);
    },
    getInfo:function(){
      var _this = this;
      _this.loading = true;
      axios.post('getReformInfo', {grading_id:_this.grading_id}).then(function (res) {
        _this.loading = false;
        if (res.data.code == 0) {
          _this.tableData = res.data.data;
        }
      }).catch(function (error) {
        _this.loading = false;
        console.log("出现错误:",error);
      });
    },
    changeweekBegin(data){
      console.log(data);
    },
  }
}
</script>