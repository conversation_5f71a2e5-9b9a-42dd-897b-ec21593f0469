<?php
declare (strict_types=1);

namespace app\area\model;

use think\Model;
use think\facade\Db;
use app\admin\model\CompanyModel;
use app\admin\model\StandardNameModel;
use app\area\model\TopStandardApplyApproval;
use app\model\SettingModel;


/**
 * @mixin \think\Model
 */
class TopStandardApply extends Model
{
	//


	public function company()
	{
		return $this->belongsTo(CompanyModel::class, 'company_id', 'id');
	}

	public function standard()
	{
		return $this->belongsTo(StandardNameModel::class, 'standard_id', 'id');
	}

	public function getList($where, $page = 1, $limit = 10)
	{
//		$whereArr[] = ['status', '=', 1];
		if (isset($where['name']) && $where['name'] != '') $whereArr[] = ['name', 'like', '%' . $where['name'] . '%'];
		$data = $this->where($whereArr)->paginate($limit)->each(function ($item, $index) {
			$item = $this->getInfo($item);
			return $item;
		});
		return $data;
	}

	public function getStatus($status)
	{
		$str = '';
		switch ($status) {
			case 0:
				$str = '未提交';
				break;
			case 1:
				$str = '审核中';
				break;
			case 5:
				$str = '驳回';
				break;
			case 7:
				$str = '通过';
				break;
			default:
				break;
		}
		return $str;
	}

	public function getInfo($item, &$model = null)
	{
		$item->companyName = $item->company ? $item->company->toArray()['name'] : '';;
		$item->standardName = $item->standard ? $item->standard->toArray()['name'] : '';;
		$item->apply_time = date('Y-m-d H:i:s', strtotime($item->apply_time));
		$item->statusStr = $this->getStatus($item->status);
		return $item;
	}


	public function doCheck($model, $param)
	{
		$model->status = $param['status'];
		$aModel = new TopStandardApplyApproval;
		$cModel = new CompanyModel;
		Db::startTrans();
		try {
			$model->save();
			$files = [];
			if (!empty($param['files'])) {
				foreach ($param['files'] as $file) {
					$files[] = $file['code'];
				}
			}
			$c_data = array(
				'apply_id' => $model->id,
				'remark' => $param['reason'],
				'status' => $param['status'],
				'check_user_id' => $_SESSION['user_id'],
				'check_user_name' => $_SESSION['user_name'],
				'check_time' => date('Y-m-d H:i:s'),
				'files' => implode(',', $files),
			);
			$aModel->save($c_data);
			if($model->status == 7){
				$cModelData = $cModel::find(intval($model->company_id));
				$standard = $aModel->standard ? $aModel->standard->toArray() : [];
				if ($cModelData) {
					$cModelData->standard_id = $standard['id'];
					$cModelData->standard_name = $standard['name'];
					$cModelData->standard_level = $model->level;
					$cModelData->stand_status = $model->status;
					$cModelData->stand_status = 1;
					$cModelData->stand_date = date('Y-m-d H:i:s');
					$cModelData->st_date = date('Y-m-d H:i:s', strtotime('+60 day'));
					$cModelData->save();
					SettingModel::setReview($model->company_id);
				}
			}
			Db::commit();
			return '数据处理成功';
		}catch (\Exception $e){
			Db::rollback();
		}
		return '数据处理失败';
	}
}
