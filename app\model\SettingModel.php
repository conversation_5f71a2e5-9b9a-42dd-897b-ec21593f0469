<?php
namespace app\model;

use think\facade\Db;
use think\Model;
use think\facade\Cache;

class SettingModel extends Model
{

    /**
     * 获取省市区列表
     * @param int $pid 操作数据表
     * @param int $type 表id
     * @return object
     */
    public function getPcas(int $pid = 0) {
        $pcas = Cache::get('pcas_'.$pid);
        if(empty($pcas)){
            $pcas = Db::table('top_pca')->where(['enabled'=>1,'pid'=>$pid])->select()->toArray();
            Cache::tag('pca')->set('pcas_'.$pid,$pcas);
        }
        return $pcas;
    }

    /**
     * 获取所有省市区数据
     * @param int $type 返回数据结构{默认0：简单结构，1：树形结构}
     * @return object
     */
    public function getPcasAll(int $type = 0,$level=0) {
//        Cache::tag('pca')->clear();
        $results = Cache::get('pcasAll_'.$type.$level);
        if(empty($results)){
            $res = Cache::get('pcasAll'.$level);
            if(empty($res)){
                $where[] = ['enabled','=',1];
                if($level>0){
                    $where[] = ['level','<=',$level];
                }
                $res = Db::table('top_pca')->where($where)->order('sort,code')->select()->toArray();
                Cache::tag('pca')->set('pcasAll'.$level,$res);
            }
            if($type==1){
                $results = get_tree_children($res);
            }else{
                foreach($res as $v){
                    $results[$v['code']] = $v['name'];
                }
            }
            Cache::tag('pca')->set('pcasAll_'.$type.$level,$results);
        }
        return $results;
    }

    /**
     * 获取所有经济类型数据
     * @param int $type 返回数据结构{默认0：简单结构，1：树形结构}
     * @return object
     */
    public function getEconomyType(int $type = 0) {
//        Cache::tag('economyType')->clear();
        $results = Cache::get('economyTypeAll_'.$type);
        if(empty($results)){
            $res = Cache::get('economyTypeAll');
            if(empty($res)){
                $res = Db::table('top_economy_type')->where(['enabled'=>1])->order('sort,code')->select()->toArray();
                Cache::tag('economyType')->set('economyTypeAll',$res);
            }
            if($type==1){
                $results = get_tree_children($res);
            }else{
                foreach($res as $v){
                    $results[$v['code']] = $v['name'];
                }
            }
            Cache::tag('economyType')->set('economyTypeAll_'.$type,$results);
        }
        return $results;
    }

    /**
     * 获取所有产业园区数据
     * @param int $type 返回数据结构{默认0：简单结构，1：树形结构}
     * @return object
     */
    public function getIndustrialPark(int $type = 0) {
//        Cache::tag('industrialPark')->clear();
        $results = Cache::get('industrialParkAll_'.$type);
        if(empty($results)){
            $res = Cache::get('industrialParkAll');
            if(empty($res)){
                $res = Db::table('top_industrial_park')->where(['enabled'=>1])->order('sort,code')->select()->toArray();
                Cache::tag('industrialPark')->set('industrialParkAll',$res);
            }
            if($type==1){
                $results = get_tree_children($res);
            }else{
                foreach($res as $v){
                    $results[$v['code']] = $v['name'];
                }
            }
            Cache::tag('industrialPark')->set('industrialParkAll_'.$type,$results);
        }
        return $results;
    }

    /**
     * 获取所有经济行业数据
     * @param int $type 返回数据结构{默认0：简单结构，1：树形结构}
     * @return object
     */
    public function getEconomySector(int $type = 0) {
//        Cache::tag('economySector')->clear();
        $results = Cache::get('economySectorAll_'.$type);
        if(empty($results)){
            $res = Cache::get('economySectorAll');
            if(empty($res)){
                $res = Db::table('top_economy_sector')->where(['enabled'=>1])->order('sort,code')->select()->toArray();
                Cache::tag('economySector')->set('economySectorAll',$res);
            }
            if($type==1){
                $results = get_tree_children($res);
            }else{
                foreach($res as $v){
                    $results[$v['code']] = $v['name'];
                }
            }
            Cache::tag('economySector')->set('economySectorAll_'.$type,$results);
        }
        return $results;
    }

    /**
     * 获取所有行业数据
     * @param int $type 返回数据结构{默认0：简单结构，1：树形结构}
     * @return object
     */
    public function getIndustry(int $type = 0) {
//        Cache::tag('industry')->clear();
        $results = Cache::get('industryAll_'.$type);
        if(empty($results)){
            $res = Cache::get('industryAll');
            if(empty($res)){
                $res = Db::table('top_industry')->where(['enabled'=>1])->order('sort,code')->select()->toArray();
                Cache::tag('industry')->set('industryAll',$res);
            }
            if($type==1){
                $results = get_tree_children($res);
            }else{
                foreach($res as $v){
                    $results[$v['code']] = $v['name'];
                }
            }
            Cache::tag('industry')->set('industryAll_'.$type,$results);
        }
        return $results;
    }


    //企业评审标准设置
    public static function setReview($companyid, $date = '')
    {
        $company = Db::table('top_company_info')->where(['id' => $companyid])->find();
        $re = Db::table('top_standard_name')->where(['id' => $company['standard_id'], 'is_del' => 0])->find();
        $element = Db::table('top_standard_element')->where(['main_id' => $re['id'], 'is_del' => 0])->order('sort,id')->select()->toArray();
        $element = get_tree_children($element);
        $content = Db::table('top_standard_content')->where(['main_id' => $re['id'], 'is_del' => 0])->order('sort,id')->select()->toArray();
        $cont = [];
        foreach ($content as $v) {
            $cont[$v['element_id']][] = $v;
        }
        Db::startTrans();
        try {
            $data = [
                'company_id' => $company['id'],
                'accessment_id' => $re['id'],
                'accessment_name' => $re['name'],
                'element' => $re['element'],
                'create_time' => date('Y-m-d H:i:s'),
                'date' => empty($date) ? date('Y-m-d') : $date,
            ];
            Db::table('top_company_review')->insert($data);
            $id = Db::table('top_company_review')->where($data)->field('id')->order('id desc')->find()['id'];
//            $id = Db::table('cay_company_review')->getLastInsID('id');
//            $id->getLastid();
//            dd($id);
            self::companyElementInsert($element, $cont, $id);
            Db::table('top_company_info')->where(['id' => $companyid])->update(['review_id' => $id]);//企业绑定评审表更新
            Db::table('top_company_review_content_list')->where(['company_id' => $companyid])->update(['is_expire' => 1]);//历史上报数据过期
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            return $e->getMessage();
        }
    }

    public static function companyElementInsert($element, $content, $mid = 0, $pid = 0, $pids = ',')
    {
        foreach ($element as $v) {
            $data = [
                'main_id' => $mid,
                'name' => $v['name'],
                'sum_score' => $v['score'],
                'mark' => $v['mark'],
                'weight' => $v['weight'],
                'pid' => $pid,
                'pids' => $pids,
                'sort' => $v['sort'],
            ];
            Db::table('top_company_review_element')->insertGetId($data);
            $id = Db::table('top_company_review_element')->where($data)->field('id')->order('id desc')->find()['id'];
            if (!empty($content[$v['id']])) {
                $data_content = [];
                foreach ($content[$v['id']] as $v1) {
                    $data_content[] = [
                        'main_id' => $mid,
                        'element_id' => $id,
                        'element_ids' => $pids . $id . ',',
                        'content' => $v1['content'],
                        'score' => $v1['score'],
                        'cycle' => $v1['cycle'],
                        'ask' => $v1['ask'],
                        'standards' => $v1['standards'],
                        'method' => $v1['method'],
                        'sort' => $v1['sort'],
                        'files' => $v1['files'],
                        'file_remark'=> $v1['file_remark'],
                    ];
                }
                Db::table('top_company_review_content')->insertAll($data_content);
            }
            if (!empty($v['children'])) {
                self::companyElementInsert($v['children'], $content, $mid, $id, $pids . $id . ',');
            }
        }
        return true;
    }

    //设置企业需要上报的信息
    public static function setReport($companyid, $type = '', $setdate = '')
    {
        $company = Db::table('top_company_info')->where(['id' => $companyid])->find();
        $where[] = ['a.id', '=', $company['review_id']];
        if (!empty($type)) {
            $where[] = ['b.cycle', '=', $type];
        }
        $list = Db::table('top_company_review')->alias('a')
            ->leftJoin('top_company_review_content b', 'a.id = b.main_id')
            ->where($where)->field('b.*,a.date')->select()->toArray();
        foreach ($list as $v) {
            if ($v['cycle'] == '月') {
                if (!empty($setdate)) {
                    if (strtotime($setdate) >= strtotime($v['date']) && strtotime($setdate) <= time()) {
                        self::insertList($v, $company, strtotime($setdate), '月');
                    }
                } else {
                    for ($i = 0; $i < 360; $i++) {
                        $date = strtotime('+' . $i . 'month', strtotime($v['date']));
                        //判断月份大于当前月份则跳出循环
                        if (date('Ym', $date) > date('Ym')) {
                            break;
                        }
                        self::insertList($v, $company, $date, '月');
                    }
                }
            }
            if ($v['cycle'] == '季') {
                if (!empty($setdate)) {
                    if (strtotime($setdate) >= strtotime($v['date']) && strtotime($setdate) <= time()) {
                        self::insertList($v, $company, strtotime($setdate), '季');
                    }
                } else {
                    for ($i = 0; $i < 120; $i++) {
                        $date = strtotime('+' . ($i * 3) . 'month', strtotime($v['date']));
                        if (date('n', $date) >= 1 && date('n', $date) <= 3) {
                            $quarter = '1';
                        } else if (date('n', $date) >= 4 && date('n', $date) <= 6) {
                            $quarter = '2';
                        } else if (date('n', $date) >= 7 && date('n', $date) <= 9) {
                            $quarter = '3';
                        } else if (date('n', $date) >= 10 && date('n', $date) <= 12) {
                            $quarter = '4';
                        }
                        if (date('n') >= 1 && date('n') <= 3) {
                            $tquarter = '1';
                        } else if (date('n') >= 4 && date('n') <= 6) {
                            $tquarter = '2';
                        } else if (date('n') >= 7 && date('n') <= 9) {
                            $tquarter = '3';
                        } else if (date('n') >= 10 && date('n') <= 12) {
                            $tquarter = '4';
                        }
                        //判断月份大于当前月份则跳出循环
                        if (date('Y', $date) . $quarter > date('Y') . $tquarter) {
                            break;
                        }
                        self::insertList($v, $company, $date, '季');
                    }
                }
            }
            if ($v['cycle'] == '年') {
                if (!empty($setdate)) {
                    if (strtotime($setdate) >= strtotime($v['date']) && strtotime($setdate) <= time()) {
                        self::insertList($v, $company, strtotime($setdate), '年');
                    }
                } else {
                    for ($i = 0; $i < 30; $i++) {
                        $date = strtotime('+' . $i . 'year', strtotime($v['date']));
                        //判断月份大于当前月份则跳出循环
                        if (date('Y', $date) > date('Y')) {
                            break;
                        }
                        self::insertList($v, $company, $date, '年');
                    }
                }
            }
        }
        return true;
    }

    public static function insertList($data, $company, $date, $type)
    {
        $data_list = [
            'company_id' => $company['id'],
            'review_id' => $data['main_id'],
            'element_id' => $data['element_id'],
            'element_ids' => $data['element_ids'],
            'content_id' => $data['id'],
            'time' => date('Y年m月', $date),
        ];
        if ($type == '月') {
            $data_list['time'] = date('Y年m月', $date);
        } else if ($type == '季') {
            if (date('n', $date) >= 1 && date('n', $date) <= 3) {
                $quarter = '第一季度';
            } else if (date('n', $date) >= 4 && date('n', $date) <= 6) {
                $quarter = '第二季度';
            } else if (date('n', $date) >= 7 && date('n', $date) <= 9) {
                $quarter = '第三季度';
            } else if (date('n', $date) >= 10 && date('n', $date) <= 12) {
                $quarter = '第四季度';
            }
            $data_list['time'] = date('Y年', $date) . $quarter;
        } else if ($type == '年') {
            $data_list['time'] = date('Y年', $date);
        }
        $re = Db::table('top_company_review_content_list')->where($data_list)->find();
        if (empty($re)) {
            Db::table('top_company_review_content_list')->insert($data_list);
        }
    }
}
