<?php
/**
 * This file is part of workerman.
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the MIT-LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link http://www.workerman.net/
 * @license http://www.opensource.org/licenses/mit-license.php MIT License
 */

namespace app\gateway\controller;

/**
 * 用于检测业务代码死循环或者长时间阻塞等问题
 * 如果发现业务卡死，可以将下面declare打开（去掉//注释），并执行php start.php reload
 * 然后观察一段时间workerman.log看是否有process_timeout异常
 */
//declare(ticks=1);

use app\BaseController;
// 添加这句，注释写法为 @Apidoc\参数名(...)
use hg\apidoc\annotation as Apidoc;

/**
 * 主逻辑
 * 主要是处理 onConnect onMessage onClose 三个方法
 * onConnect 和 onClose 如果不需要可以不用实现并删除
 */
class Demo
{
    /**
     * @Apidoc\Title("上传文件到讯飞星火")
     * @Apidoc\Param("url", type="string",default="", desc="文件")
     * @Apidoc\Param("filename", type="string",default="", desc="文件名称")
     * @Apidoc\Returned("id", type="object", desc="文件id")
     * @Apidoc\Returned("filepath", type="int", desc="文件路径")
     */
    public function uploadAI($url='',$filename='') {
        $time = time();
        $header = [
            'appId' => '51a232b1',
            'timestamp' => $time,
            'signature' => base64_encode(hash_hmac('sha1', md5('51a232b1'.$time), 'M2I4OWYxNGUzYzYxMzBhMWVlYTgwZmIw', true)),
        ];
        $data = [
            'url' => $url,
            'fileName' => $filename,
            'fileType' => 'wiki',
            'callbackUrl' => 'http://oa.toppingsoft.com:48088/general/topcrm/index.php/gatway/demo/notfayAI',
        ];
        $result = $this->http_request('https://chatdoc.xfyun.cn/openapi/fileUpload',$data,$header);
        result($result);
    }

    /**
     * @Apidoc\Title("文件到讯飞星火回调")
     * @Apidoc\Param("url", type="string",default="", desc="文件")
     * @Apidoc\Param("filename", type="string",default="", desc="文件名称")
     * @Apidoc\Returned("id", type="object", desc="文件id")
     * @Apidoc\Returned("filepath", type="int", desc="文件路径")
     */
    public function notfayAI() {
        $param = request()->param();
        $file = $_SERVER['DOCUMENT_ROOT'].'/general/topcrm/notfayAI.txt';
        file_put_contents($file,date('【Y-m-d H:i:s】').'start'."\n",FILE_APPEND);
        file_put_contents($file,json_encode($param)."\n",FILE_APPEND);
    }
    /**
     * 发送post请求
     * @param string $url 请求地址
     * @param array $post_data post键值对数据
     * @return string
     */
    function http_request($url, $post_data, $headers) {
        $postdata = http_build_query($post_data);
        $options = array(
            'http' => array(
                'method' => 'POST',
                'header' => $headers,
                'content' => $postdata,
                'timeout' => 15 * 60 // 超时时间（单位:s）
            )
        );
        $context = stream_context_create($options);
        $result = file_get_contents($url, false, $context);

        return $result;

        return "success";
    }
}
