<style>
.my-autocomplete li{line-height: normal;padding: 7px;}
.my-autocomplete li .name{text-overflow: ellipsis;overflow: hidden;}
.my-autocomplete li .addr{font-size: 12px;color: #b4b4b4;}
.my-autocomplete li .highlighted{color: #ddd;}
.mytable {border-collapse:collapse;width: 100%;}
.mytable thead tr td {border: 1px solid #333;font-weight: 700; padding: 10px;background: #f5f7fa;text-align: center;}
.mytable tbody tr th {border: 1px solid #333;font-weight: 700; padding: 10px;text-align: center;font-size: 16px;height: 20px;}
.mytable tbody tr td {border: 1px solid #333; padding: 10px;text-align: center;font-size: 16px;}
.mytable .active td{ background: #f2f2f2;}
.mytable tbody tr td {font-family: 宋体;text-align: left;height: 20px;}
.mytable tbody tr td p{line-height: 30px;}
</style>
<template>
  <el-dialog :title="title" :close-on-click-modal="false" :visible.sync="visible" width="1000px" top="10px" @close="refresh()" :append-to-body="true" label-position="top">
    <el-form ref="form" :model="form" label-width="80px">
      <el-form-item label="终止原因">
        <el-radio-group v-model="form.reason">
          <el-radio style="display: block;margin: 5px 0;" label="企业申报标准与实际不符"></el-radio>
          <el-radio style="display: block;margin: 5px 0;" label="本次自评前1年内本企业发生过死亡事件"></el-radio>
          <el-radio style="display: block;margin: 5px 0;" label="本次自评前1年内本企业发生过3人及以上重伤或直接经济损失总计 100 万元及以上的生产安全事故"></el-radio>
          <el-radio style="display: block;margin: 5px 0;" label="本企业发生过造成重大社会不良影响的事件"></el-radio>
          <el-radio style="display: block;margin: 5px 0;" label="本企业被列入安全生产失信惩戒名单"></el-radio>
          <el-radio style="display: block;margin: 5px 0;" label="其他"></el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备注">
        <el-input type="textarea" v-model="form.remark"></el-input>
      </el-form-item>
      <el-form-item label="佐证照片">
        <el-upload
            action="upload"
            list-type="picture-card"
            :file-list="form.files"
            :limit="10"
            :on-success="(response,file,fileList)=>uploadSuccess(response,file,fileList)"
            :before-upload="uploadBefore"
            :on-preview="handlePictureCardPreview"
            :on-remove="(file,fileList)=>handleRemove(file,fileList)">
          <i class="el-icon-plus"></i>
        </el-upload>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">提交</el-button>
        <el-button @click="closeDialog">取消</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>
<script>
module.exports = {
  name: "add",
  // 模板导入区
  components: {
  },
  data:function() {
    return {
      id:0,
      activeName:'a',
      type:1,
      visible: false,
      dialogVisible: false,
      dialogImageUrl: '',
      title: '终止评审',
      loading: false,
      user_id: 0,
      data:{

      },
      form:{
        id:'',
        reason:'',
        remark:'',
        files:[],
      },
      is_see:0,
      userData:[],
      details:[],
      cards:{},
      pcas: [],
      thisuser:'',
      rules: {
      },
    }
  },
  methods: {
    /**
     * 打开弹窗调用方法
     * */
    open: function (id) {
      var _this =this;
      _this.visible = true;
      _this.form.id = id;
    },
    handleRemove(file, fileList) {
      this.form.files = [];
    },
    uploadBefore(file) {
      const isJPG = file.type === 'image/jpeg';
      if(!isJPG){
        this.$message.error('请上传jpg图片');
      }
      return isJPG;
    },
    uploadSuccess(res, file) {
      let data = {
        id:res.id,
        url:res.url,
        name:res.name,
      };
      if(res.state==='SUCCESS'){
        this.form.files.push(data);
      }
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    handleClick(tab, event) {
      console.log(tab, event);
    },
    onSubmit() {
      var _this = this;
      _this.$confirm('确定提交?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _this.loading = true;
        var param = _this.form;
        param.status = 2;
        axios.post('end',param).then(res => {
          _this.loading = false
          if(res.data.code === 0){
            _this.visible = false;
          }else{
            _this.$message.error(res.data.msg);
          }
        })
      }).catch(() => {
      });
    },
    closeDialog: function () {
      this.visible = false;
    },
    refresh: function () {
      this.$emit("refresh");
    },

  }
}
</script>


