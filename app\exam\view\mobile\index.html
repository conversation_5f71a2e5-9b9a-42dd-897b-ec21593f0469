<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>手机考试</title>
    <!-- 引入样式 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">

    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <link href="__PUBLIC__/plugs/layui/css/layui.css" rel="stylesheet">
    <script src="__PUBLIC__/plugs/layui/layui.js"></script>

</head>

<body  style="background: white">
<div id="app">

    <template v-if="step==1">
        <company-info :company-name="company_name"
                      :sign-name="sign"
                      :task-id="task_id"
                      :standard-id="standard_id"
                      @ok="createSuccess"
                      ref="companyInfo"></company-info>
    </template>
    <template v-else-if="step==2">
        <paper-list
                @ok="submitSuccess"
                :task-id="task_id"
                :sign="sign"
                :paper-id="paper_id"
                ref="paperList">

        </paper-list>
    </template>
    <template v-else-if="step==3">
        <score-info :score="score"></score-info>
    </template>
    <template v-else>错误</template>
</div>
</body>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script>

    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        // 模板导入区
        components: {
            'company-info': 'url:../../../app/exam/view/mobile/moudel/companyInfo.vue?v=1',
            'paper-list': 'url:../../../app/exam/view/mobile/moudel/paperList.vue?v=1',
            'score-info': 'url:../../../app/exam/view/mobile/moudel/score.vue?v=1',
        },
        data: function () {
            return {
                sign: '',
                score: '0',
                paper_id: '12',
                task_id: '',
                company_name: '',
                standard_id: '',
                loading: false,
                step: 1,
            }
        },
        created: function () {
            this.task_id = '{$task_id}'
            this.sign = '{$sign}'
            this.company_name = '{$company_name}'
            this.standard_id = '{$standard_id}'

        },

        methods: {
            createSuccess: function (paper_id) {
                this.paper_id = paper_id;
                this.step = 2;
            },

            submitSuccess: function (score) {
                this.score = score;
                this.step = 3;
            },


        },


    })
</script>

<style scoped>
    * {
        padding: 0;
        margin: 0;
    }

    .bgImage {
        width: 100%;
    }

    .headerBox {
        color: #fff;
        position: relative;
    }

    .headerTitle {
        font-size: 22px;
        position: absolute;
        width: 100%;
        text-align: center;
        top: 25%;
    }

    .headerTitle div:last-child {
        margin-top: 8%;
        font-size: 18px;
    }

    .infoBox {
        background: #fff;
        border-radius: 10px 10px 0 0;
        position: relative;
        top: -20px;
        padding: 10px 0;
    }

    .formItem {
        border-bottom: 1px solid #DDDDDD;
        padding: 15px 20px 0;
        color: #7B7B7B;
        margin-bottom: 20px;
    }

    .formItemLabel {
        margin-bottom: 10px;
        font-size: 14px;
        font-weight: 600;
    }

    .el-form-item {
        margin-bottom: 0;
    }

    .el-input__inner {
        border: none;
    }

    .formBtn {
        padding: 20px;
    }

    .formBtn button {
        width: 100%;
        border-radius: 100px;
        background: linear-gradient(270deg, #3E8CFF 0%, #1E44FF 100%);
        margin-top: 20px;
    }

    .el-select {
        display: block;
    }

    .examinationHeader {
        text-align: center;
        color: #363636;
        font-size: 20px;
        font-weight: 600;
        height: 80px;
        line-height: 60px;
        position: fixed;
        width: 100%;
        left: 0;
        top: 0;
        background: #fff;
        z-index: 999;
    }

    .countdown {
        color: #DD2D2D;
        font-size: 12px;
        line-height: 20px;
        width: 100%;
        position: absolute;
        bottom: 10px;
    }

    .examinationSection {
        margin-top: 80px;
        padding: 15px;
        margin-bottom: 70px;
    }

    .examinationItem {
        margin-bottom: 20px;
    }

    .examinationItemLabel {
        font-size: 16px;
        color: #333;
        margin-bottom: 15px;
    }

    .examinationRadioBox {
        border-radius: 14px;
        border: 1px solid rgba(112, 112, 112, 0.2);
        padding: 10px 15px;
    }

    .el-radio {
        display: block;
        line-height: 2.5;
        color: rgba(0, 0, 0, 0.6);
    }

    .el-radio-group {
        display: block;
    }

    .el-radio__inner {
        width: 18px;
        height: 18px;
    }

    .el-radio__input.is-checked .el-radio__inner {
        border-color: #0052D9;
        background-color: #0052D9;
    }

    .submitBtn {
        padding: 15px;
        background: #fff;
        position: fixed;
        bottom: 0;
        width: calc(100% - 30px);
        border-top: 1px solid #eee;
    }

    .submitBtn button {
        width: 100%;
        border-radius: 100px;
        background: linear-gradient(270deg, #3E8CFF 0%, #1E44FF 100%);
    }

    .successBox {
        background: #fff;
        border-radius: 6px;
        box-shadow: 0px 6px 10px 1px #EDEDED;
        position: relative;
        top: 20vh;
        text-align: center;
    }

    .successImg {
        position: relative;
        margin-top: -90px;
    }

    .successTitle {
        font-size: 14px;
        color: #181818;
    }

    .successNum {
        font-size: 24px;
        color: #000000;
        padding: 40px 0;
    }

    .successNum span {
        display: inline-block;
        vertical-align: bottom;
        margin-bottom: 15px;
        font-weight: 600;
    }

    .successNum span:first-child {
        color: #FF3300;
        font-size: 72px;
        margin-bottom: 0;
    }

    .el-message-box {
        width: 80%;
        margin-top: 20%;
        margin-left: 10px;
        margin-right: 10px;
    }
</style>
</html>