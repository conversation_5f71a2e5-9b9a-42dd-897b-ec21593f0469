<?php
namespace app\model;

use think\facade\Db;
use think\Model;

class FlowModel extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'top_flow';
    protected $pk = 'id';

    //设置审核步骤
    public static function setApprove($type,$id,$user,$result = [],$ischeck = false){
        $flow = config('user_flow')[$type];
        $setting = $type=='welfare_subsidy_stop'?config('user_vacation')['welfare_subsidy']:config('user_vacation')[$type];
        foreach ($flow as $k=>$v){
            if($v['children']){
                foreach ($v['children'] as $v1){
                    $data[] = [
                        'model' => $type,
                        'model_id' => $id,
                        'flow_id' => $k,
                        'title' => $v['title'],
                        'check_status' => 0,
                        'check_priv' => $v1['title'],
                        'check_remark' => '',
                        //'check_users' => $ischeck?'':self::getCheckUser($v1['check_user'],$user,$result,$v['notOur']),
                        'check_users' => self::getCheckUser($v1['check_user'],$user,$result,$v['notOur']),
                        'is_check' => $ischeck?1:self::getIsCheck($v1['is_check'],$user,$result),
                    ];
                }
            }else{
                $data[] = [
                    'model' => $type,
                    'model_id' => $id,
                    'flow_id' => $k,
                    'title' => $v['title'],
                    'check_status' => 0,
                    'check_priv' => '',
                    'check_remark' => '',
                    //'check_users' => $ischeck?'':self::getCheckUser($v['check_user'],$user,$result,$v['notOur']),
                    'check_users' => self::getCheckUser($v['check_user'],$user,$result,$v['notOur']),
                    'is_check' => $ischeck?1:self::getIsCheck($v['is_check'],$user,$result),
                ];
            }
        }
        foreach ($data as $k=>$v){
            $us = [];
            $users = Db::table('td_user')->where('user_id','in',trim($v['check_users']))->field('user_name')->select()->toArray();
            foreach ($users as $v1){
                $us[] = $v1['user_name'];
            }
            $data[$k]['check_users_name'] = implode('，',$us);
            if(empty(trim($v['check_users'],','))){
                $data[$k]['is_check'] = 1;
            }
        }
        Db::table($setting['table_approval'])->where(['model'=>$type,'model_id'=>$id])->delete();
        if(!empty($data)){
            Db::table($setting['table_approval'])->insertAll($data);
        }
    }

    //获取审核人
    public static function getCheckUser($checkRule,$user,$result = [],$notOur=1){
        if(is_array($checkRule)){
            foreach ($checkRule as $v){
                $rule = explode('&&',$v['rule']);
                $a = 0;
                foreach($rule as $v1){
                    $rule1 = explode('||',$v1);
                    foreach($rule1 as $v2){
                        $rules = explode(':',$v2);
                        switch ($rules[0]){
                            case 'ALL': //全部
                                $a = 1;
                                break;
                            case 'USER_TYPE': //人员类型
                                $re = explode('|',$rules[1]);
                                $us = Db::table('top_user_info')->where('user_id','=',$user['user_id'])->field('user_type')->find();
                                $a = in_array($us['user_type'],$re)?1:0;
                                break;
                            case 'TYPE': //类型
                                $re = explode('|',$rules[1]);
                                $a = in_array($result['type'],$re)?1:0;
                                break;
                            case 'PRIV': //类型
                                $re = explode('|',$rules[1]);
                                $a = in_array($user['user_priv'],$re)?1:0;
                                break;
                            default:
                                $a = 0;
                                break;
                        }
                        if($a==1){
                            break;
                        }
                    }
                    if($a==0){
                        break;
                    }
                }
                if($a==1){
                    $re = self::CheckUserRule($v['check'],$user,$notOur);
                    foreach ($re as $v){
                        $u[] = $v;
                    }
                }
            }
        }else{
            $u = self::CheckUserRule($checkRule,$user,$notOur);
        }
        foreach ($u as $k=>$v){
            if($user['user_id']==$v&&$notOur==1){
                unset($u[$k]);
            }
            if($user['user_id']==$v&&$notOur==2){
                $u = [];
                break;
            }
        }
        return empty($u)?'':','.implode(',',$u).',';
    }

    public static function CheckUserRule($checkRule,$user,$notOur=1){
        $rule = explode(':',$checkRule);
        switch ($rule[0]){
            case 'DEPT': //部门
                $re = explode('|',$rule[1]);
                $users = Db::table('top_user_info')->where('dept_id','in',$re)->field('user_id')->select()->toArray();
                foreach($users as $v){
                    $u[] = $v['user_id'];
                }
                break;
            case 'DEPT1': //本部门字段
                $re = explode('|',$rule[1]);
                $dept = Db::table('department')->where(['DEPT_ID'=>$user['dept_id']])->find();
                foreach ($re as $v){
                    $tmp = explode(',',$dept[$v]);
                    foreach ($tmp as $v1){
                        if(!empty($v1)){
                            $u[] = $v1;
                        }
                    }
                }
                break;
            case 'MANAGER': //直接领导
                $dept = Db::table('department')->where(['DEPT_ID'=>$user['dept_id']])->find();
                $re = Db::table('top_config')->where(['key'=>'dep_admin_'.$user['dept_id'],'model'=>'user_info'])->find();
                if(empty(trim($re['value'],','))||trim($re['value'],',')==$user['user_id']){
                    $re = Db::table('top_config')->where(['key'=>'dep_admin_'.$dept['DEPT_PARENT'],'model'=>'user_info'])->find();
                }
                $tmp = explode(',',$re['value']);
                foreach ($tmp as $v1){
                    if(!empty($v1)){
                        $u[] = $v1;
                    }
                }
                break;
            case 'MANAGER2': //直接领导
                $dept = Db::table('department')->where(['DEPT_ID'=>$user['dept_id']])->find();
                $re = Db::table('top_config')->where(['key'=>'dep_admin_'.$user['dept_id'],'model'=>'user_info'])->find();
                if(empty(trim($re['value'],','))){
                    $re = Db::table('top_config')->where(['key'=>'dep_admin_'.$dept['DEPT_PARENT'],'model'=>'user_info'])->find();
                }
                $tmp = explode(',',$re['value']);
                foreach ($tmp as $v1){
                    if(!empty($v1)){
                        $u[] = $v1;
                    }
                }
                break;
            case 'LEADER': //上级领导
                $dept = Db::table('department')->where(['DEPT_ID'=>$user['dept_id']])->find();
                $dept_id = empty($dept['DEPT_PARENT'])?$user['dept_id']:$dept['DEPT_PARENT'];
                $re = Db::table('top_config')->where(['key'=>'dep_admin_'.$dept_id,'model'=>'user_info'])->find();
                $tmp = explode(',',$re['value']);
                foreach ($tmp as $v1){
                    if(!empty($v1)){
                        $u[] = $v1;
                    }
                }
                break;
            case 'PRIV':
                $re = explode('|',$rule[1]);
                $users = Db::table('td_user')->where('USER_PRIV','in',$re)->select()->toArray();
                foreach($users as $v){
                    $u[] = $v['USER_ID'];
                }
                break;
            case 'USER':
                $re = empty($rule[1])?'0':implode(',',explode('|',$rule[1]));
                $tmp = explode(',',$re);
                foreach ($tmp as $v1){
                    if(!empty($v1)){
                        $u[] = $v1;
                    }
                }
                break;
            case 'DEPTPRIV':
                $re = empty($rule[1])?[]:explode(',',$rule[1]);
                $dept = empty($re[0])?[]:explode('|',$re[0]);
                $priv = empty($re[1])?[]:explode('|',$re[1]);
                $where = [
                    ['u.dept_id','in',$dept],
                    ['a.user_priv','in',$priv],
                ];
                $u = Db::table('td_user')->alias('a')
                    ->leftJoin('top_user_info u','a.user_id = u.user_id')
                    ->where($where)->column('a.user_id');
                break;
            case 'FLOW':
                $re = explode('|',$rule[1]);
                $keys = [];
                foreach ($re as $v){
                    $keys[] = 'flow_users_'.$v;
                }
                $users = Db::table('top_config')->where('key','in',$keys)->field('value')->select()->toArray();
                foreach($users as $v){
                    $us = explode(',',$v['value']);
                    foreach ($us as $v1){
                        $u[] = $v1;
                    }
                }
                break;
            case 'POSITION':
                $position = Db::table('top_user_info_position')->where(['user_id'=>$user['user_id']])->find();
                $re = Db::table('top_config')->where(['key'=>'dep_admin_'.$position['dept_id'],'model'=>'user_info'])->find();
                $tmp = explode(',',$re['value']);
                foreach ($tmp as $v1){
                    if(!empty($v1)){
                        $u[] = $v1;
                    }
                }
                break;
            default:
                break;
        }
        foreach ($u as $k=>$v){
            if($user['user_id']==$v&&$notOur==1){
                unset($u[$k]);
            }
            if($user['user_id']==$v&&$notOur==2){
                $u = [];
                break;
            }
        }
        return $u;
    }

    //获取是否需要审核
    public static function getIsCheck($checkRule,$user,$result = []){
        if(is_array($checkRule)){
            $isCheck = 0;
            foreach($checkRule as $v){
                $rule = explode('&&',$v);
                foreach ($rule as $v1){
                    $rule1 = explode('||',$v1);
                    foreach ($rule1 as $v2){
                        $isCheck = self::IsCheckRule($v2,$user,$result);
                        if($isCheck == 0){
                            break;
                        }
                    }
                    if($isCheck==1){
                        break;
                    }
                }
                if($isCheck == 0){
                    break;
                }
            }
        }else{
            $isCheck = 0;
            $rule = explode('&&',$checkRule);
            foreach ($rule as $v1){
                $rule1 = explode('||',$v1);
                foreach ($rule1 as $v2){
                    $isCheck = self::IsCheckRule($v2,$user,$result);
                    if($isCheck == 0){
                        break;
                    }
                }
                if($isCheck==1){
                    break;
                }
            }
        }
        return $isCheck;
    }

    public static function IsCheckRule($rule,$user,$res=[]){
        $rule = explode(':',$rule);
        switch ($rule[0]){
            case 'LEADER': //有上级领导
                $dept = Db::table('department')->where(['DEPT_ID'=>$user['dept_id']])->find();
                $isCheck = empty($dept['DEPT_PARENT'])?0:1;
                break;
            case 'DEPT': //部门
                $re = explode('|',$rule[1]);
                $isCheck = in_array($user['dept_id'],$re)?0:1;
                break;
            case 'EDEPT': //部门
                $re = explode('|',$rule[1]);
                $isCheck = in_array($user['dept_id'],$re)?1:0;
                break;
            case 'PRIV':
                $re = explode('|',$rule[1]);
                $isCheck = in_array($user['user_priv'],$re)?0:1;
                break;
            case 'EPRIV':
                $re = explode('|',$rule[1]);
                $isCheck = in_array($user['user_priv'],$re)?1:0;
                break;
            case 'USER':
                $re = explode('|',$rule[1]);
                $isCheck = in_array($user['user_id'],$re)?0:1;
                break;
            case 'EUSER':
                $re = explode('|',$rule[1]);
                $isCheck = in_array($user['user_id'],$re)?1:0;
                break;
            case 'TYPE':
                $re = explode('|',$rule[1]);
                $isCheck = in_array($res['type'],$re)?0:1;
                break;
            case 'USER_TYPE':
                $re = explode('|',$rule[1]);
                $isCheck = in_array($res['user_type'],$re)?0:1;
                break;
            case 'POSITION':
                $re = Db::table('top_user_info_position')->where(['user_id'=>$user['user_id']])->find();
                $isCheck = $re?0:1;
                break;
            case 'NOT':
                $isCheck = 1;
                break;
            default:
                $isCheck = 0;
                break;
        }
        return $isCheck;
    }


    public static function dataList($type,$where,$limit,$order = 'a.id desc'){
        $setting = config('user_vacation')[$type];
        if($type=='family'){
            $condition = "b.model in ('family','family_extend') and a.id = b.model_id and a.flow_id = b.flow_id and b.is_check = 0";
        }else if($type=='welfare_subsidy'){
            $condition = "b.model in ('welfare_subsidy','welfare_subsidy_stop') and a.id = b.model_id and a.flow_id = b.flow_id and b.is_check = 0";
        }else{
            $condition = "b.model = '$type' and a.id = b.model_id and a.flow_id = b.flow_id and b.is_check = 0";
        }
        $result = Db::table($setting['table'])->alias('a')
            ->leftJoin("top_user_info u",'a.user_id = u.user_id')
            ->leftJoin("department d",'a.dept_id = d.DEPT_ID')
            ->leftJoin("{$setting['table_approval']} b",$condition)
            ->where($where)
            ->order($order)
            ->group('a.id')
            ->field('a.*,b.check_users');
        if($limit>0){
            $result = $result->paginate($limit);
        }else{
            $result = $result->select();
        }
        $result = $result->each(function ($item,$key) use ($setting,$type){
            $status = [
                '1' => '同意',
                '2' => '不同意',
            ];
            $item = dataHand($item);
            $item = self::codeToText($item);//参数整理
            $item['mb_type'] = $setting['type'][$item['type']];
            $item['mb_status'] = empty($setting['status'])?config('global.UserInfo.vacation_status')[$item['status']]:$setting['status'][$item['status']];
            if($type=='welfare_subsidy'){
                $item['mb_subsidy_type'] = $setting['subsidy_type'][$item['subsidy_type']];
                $item['stop_time'] = $item['is_stop']==1?$item['stop_time']:'';
                $item['stop_name'] = $item['is_stop']==1?'是':'否';
            }
            if($type=='welfare_level'){
                $item['level_money'] = $item['level'].$item['money'].'元/月';
                $item['mb_month'] = empty($item['month'])?'':date('Y年m月',strtotime($item['month'].'-01'));
            }
            $item['apply_time'] = date('Y-m-d H:i',$item['apply_time']);
            $approval = Db::table($setting['table_approval'])->where(['model_id'=>$item['id'],'model'=>$type])->order('flow_id')->select()->toArray();
            $item['approval'] = [];
            foreach($approval as $v){
                $v['mb_status'] = $status[$v['check_status']];
                $v['check_users'] = trim($v['check_users'],',');
                $v['check_time'] = empty($v['check_time'])?'':date('Y-m-d H:i',$v['check_time']);
                $item['approval'][$v['flow_id']][] = $v;
                $item['approval'.$v['flow_id']] .= empty($item['approval'.$v['flow_id']])?$v['mb_status']."\r\n".$v['check_time']:"\r\n".$v['mb_status']."\r\n".$v['check_time']."\r\n";
            }
            $check_users = explode(',',$item['check_users']);
            $item['check'] = in_array($_SESSION['LOGIN_USER_ID'],$check_users)?true:false;
            return $item;
        })->toArray();
        return $result;
    }

    //流程审批通用方法
    public static function flowCheck($id,$param,$result,$approval){
        $user_name = UserModel::getUserName($_SESSION['LOGIN_USER_ID']);
        $user_sign = UserModel::getUserSign($_SESSION['LOGIN_USER_ID']);
        $flow_type = $approval['model'];
        $setting = config('user_vacation')[$flow_type];
        if($flow_type=='welfare_subsidy_stop'){
            $setting = config('user_vacation')['welfare_subsidy'];
        }
        if($flow_type=='family_extend'){
            $setting = config('user_vacation')['family'];
        }
        Db::startTrans();
        try {
            $data = [];
            if($flow_type=='welfare_subsidy'){
                switch ($result['flow_id']){
                    case 1:
                        $data['start'] = $data['start1'] = !isset($param['start1'])?$result['start']:$param['start1'];
                        break;
                    case 2:
                        $data['start'] = $data['start2'] = !isset($param['start2'])?$result['start']:$param['start2'];
                        break;
                    case 4:
                        $data['start'] = $data['start4'] = !isset($param['start4'])?$result['start']:$param['start4'];
                        break;
                    case 5:
                        $data['start'] = $data['start5'] = !isset($param['start5'])?$result['start']:$param['start5'];
                        break;
                    default:
                        $data = [];
                        break;
                }
            }
            if($flow_type=='welfare_subsidy_stop'){
                $data['stop_time'] = $param['stop_time'];
            }
            if($flow_type=='welfare_express'){
                switch ($result['flow_id']){
                    case 1 or 2:
                        $data['money2'] = empty($param['money2'])?$result['money2']:$param['money2'];
                        if(empty($data['money2'])){
                            return '请填写建议标准';
                        }
                        break;
                    case 4 or 5:
                        $data['money'] = empty($param['money'])?$result['money']:$param['money'];
                        if(empty($data['money'])){
                            return '请填写建议标准';
                        }
                        break;
                    default:
                        $data = [];
                        break;
                }
            }
            if($flow_type=='welfare_relief'){
                switch ($result['flow_id']){
                    case 4 or 5:
                        $data['money'] = empty($param['money'])?$result['money']:$param['money'];
                        if(empty($data['money'])){
                            return '请填写审批金额';
                        }
                        break;
                    default:
                        $data = [];
                        break;
                }
            }
            if($flow_type=='welfare_level'){
                $data['des'] = $param['check']['remark'];
            }
            if($flow_type=='welfare_qixiang'){
                switch ($result['flow_id']){
                    case 1 or 4 or 5:
                        $data['level'] = empty($param['level'])?$result['level']:$param['level'];
                        $data['money'] = empty($param['money'])?$result['money']:$param['money'];
                        $data['start'] = empty($param['start'])?$result['start']:$param['start'];
                        if(empty($data['level'])){
                            return '请填写享受等级';
                        }
                        if(empty($data['money'])){
                            return '请填写发放标准';
                        }
                        if(empty($data['start'])){
                            return '请选择始发时间';
                        }
                        break;
                    default:
                        $data = [];
                        break;
                }
            }
            $data_approval = [
                'check_user' => $_SESSION['LOGIN_USER_ID'],
                'check_user_name' => $user_name,
                'sign_img' => $user_sign,
                'check_time' => time(),
                'check_status' => $param['check']['status'],
                'check_remark' => $param['check']['remark'],
                'is_check' => 1,
            ];
            Db::table($setting['table_approval'])->where(['id'=>$approval['id']])->update($data_approval);
            $thischeck = Db::table($setting['table_approval'])->where(['model'=>$flow_type,'model_id'=>$approval['model_id'],'flow_id'=>$approval['flow_id'],'is_check'=>0])->find();
            if($data_approval['check_status']==1){
                if($thischeck){
                    Db::commit();
                    return true;
                }
                $nextapproval = FlowModel::nextApproval($flow_type,$result['id'],$result['flow_id']);
                $data['flow_id'] = $nextapproval['flow_id'];
                if(empty($nextapproval)){
                    $data['status'] = 7;
                    if($flow_type=='welfare_subsidy_stop'){
                        $data['is_stop'] = 1;
                    }
                    if($flow_type=='duty_outs'){
                        $data_list = [
                            'main_id' => $result['duty_id'],
                            'list_id' => 4,
                            'dept_id' => $result['dept_id'],
                            'field_1' => $result['dept_name'],
                            'field_2' => $result['user_name'],
                            'field_3' => $result['address'],
                            'field_4' => $result['car_code'],
                            'field_5' => $result['driver'],
                            'field_6' => $result['out_time'],
                            'field_7' => $result['come_time'],
                            'field_8' => $result['remark'],
                            'is_show' => 1,
                        ];
                        Db::table('top_duty_disperse_list')->insert($data_list);
                    }
                }
                sendMessage($_SESSION['LOGIN_USER_ID'],$result['user_id'],'您的'.$setting['title'].'申请已通过',$setting['url']);
            }else{
                $data['flow_id'] = 0;
                $data['status'] = 5;
                if($flow_type=='welfare_subsidy_stop'){
                    $data['status'] = 7;
                }
                sendMessage($_SESSION['LOGIN_USER_ID'],$result['user_id'],'您的'.$setting['title'].'申请已驳回',$setting['url']);
                OaModel::setTodos($setting['mid']);
            }
            Db::table($setting['table'])->where(['id'=>$result['id']])->update($data);
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            return $e->getMessage();
        }
    }


    public static function nextApproval($type,$id,$flow_id=0,$is_send = true){
        $setting = config('user_vacation')[$type];
        if(empty($setting)){
            return false;
        }
        $nextapproval = Db::table($setting['table_approval'])->where([['model','=',$type],['model_id','=',$id],['is_check','=','0'],['flow_id','>',$flow_id]])->order('flow_id')->find();
        if($nextapproval&&$nextapproval['flow_id']!=7&&$is_send){
            $nextapprovalAll = Db::table($setting['table_approval'])->where([['model','=',$type],['model_id','=',$id],['is_check','=','0'],['flow_id','=',$nextapproval['flow_id']]])->select()->toArray();
            $apply = Db::table($setting['table'])->where(['id'=>$id])->field('user_id')->find();
            foreach ($nextapprovalAll as $v){
                $url = $v['title']=='人力资源复审'?$setting['mangeUrl'].'?status=3':$setting['mangeUrl'];
                sendMessage($apply['user_id'],trim($v['check_users'],','),'您有待审批的'.$setting['title'].'申请',$url);
            }
        }
        OaModel::setTodos($setting['mid']);
        return $nextapproval;
    }

    //设置流程步骤
    public function setFlow($id,$param){
        $setting = config('user_vacation')[$param['model']];
        $approval = Db::table($setting['table_approval'])->where(['id'=>$id,'model'=>$param['model']])->find();
        if(empty($setting)){
            result('',1001,'参数错误');
        }
        $isadmin = Config::isAdmin($setting['manageset'],true);
        if (!$isadmin) {
            result('',3001,'无权限');
        }
        $user = trim($this->request->param('check_users'),',');
        $data['check_users_name'] = trim($this->request->param('check_users_name'),',');
        $data['is_check'] = $this->request->param('is_check');
        if(empty($user)){
            result('',1001,'请选择审批人');
        }
        if(empty($approval)){
            result('',3001,'数据不存在');
        }
        $data['check_users'] = ','.$user.',';
        $res = Db::table($setting['table_approval'])->where(['id'=>$approval['id']])->update($data);
        if($res){
            result('',0,'修改成功');
        }else{
            result('',4001,'网络错误');
        }
    }

    //参数格式化
    public static function codeToText($info){
        if(empty($info)){
            return $info;
        }
        $field = config('global.UserInfo');
        $field['status'] = $field['plan_status'];
        foreach ($field as $k=>$v){
            if(isset($v[$info[$k]])){
                $info['mb_'.$k] = $v[$info[$k]];
            }
        }
        $pcas = get_pcas_all(2);
        $info['update_time'] = empty($info['update_time'])?'':date('Y-m-d',$info['update_time']);
        if(isset($info['domicile_place'])){
            $info['mb_domicile_place'] = $info['domicile_place']?$pcas[$info['domicile_place']]:'';
        }
        if($info['relation_nickname']){
            $info['mb_relation_nickname'] = $field['relation'][$info['relation_nickname']];
        }
        switch ($info['marriage']){
            case '0':
                $info['mb_marriage'] = '未婚';
                break;
            case '1':
                $info['mb_marriage'] = '已婚';
                break;
            default:
                $info['mb_marriage'] = $field['marriage'][$info['marriage']];
                break;
        }
        foreach ($info as $k=>$v){
            $info[$k] = $v==='0000-00-00'?'':$v;
            if(is_int($v)){
                $info[$k] = (string)$v;
            }
            if($v===null){
                $info[$k] = '';
            }
        }
        return $info;
    }


}