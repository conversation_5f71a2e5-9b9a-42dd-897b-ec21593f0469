<?php

namespace app\model;

use think\facade\Db;
use think\Model;

class TimerModel extends Model
{
    /**
     * 自评报告提醒任务
     * 10月1日开始提醒企业上报当年自评报告
     * 年底再次提醒未提交的企业
     */
    public static function selfEvaluationReminder()
    {
        $currentYear = date('Y');
        $currentMonth = date('m');
        $currentDay = date('d');

        // 只在10月1日和12月31日执行
        if (!($currentMonth == '10' && $currentDay == '01') && !($currentMonth == '12' && $currentDay == '31')) {
            return;
        }

        // 获取所有企业信息
        $companies = Db::table('top_company_info')
            ->where(['status' => 1]) // 只获取正常状态的企业
            ->field('id,user_id,name,industry,specialty')
            ->select();

        foreach ($companies as $company) {
            // 检查企业是否已提交当年的自评报告
            $hasSubmitted = Db::table('top_company_evaluate')
                ->where([
                    ['company_id', '=', $company['id']],
                    ['year', '=', $currentYear],
                    ['status', '=', 7] // 已提交状态
                ])
                ->find();

            if (!$hasSubmitted) {
                // 企业未提交，发送提醒
                self::sendSelfEvaluationReminder($company, $currentMonth);
            }
        }
    }

    /**
     * 发送自评报告提醒消息
     * @param array $company 企业信息
     * @param string $month 当前月份
     */
    private static function sendSelfEvaluationReminder($company, $month)
    {
        $currentYear = date('Y');

        if ($month == '10') {
            // 10月1日的提醒
            $smsContent = "尊敬的{$company['name']}，您好！根据安全生产标准化管理要求，请及时提交{$currentYear}年度企业自评报告。提交截止时间为{$currentYear}年12月31日。请登录系统完成自评报告的填报和提交。";
            $smsType = '企业自评报告开始提醒';
        } else {
            // 12月31日的催促提醒
            $smsContent = "尊敬的{$company['name']}，您好！今日为{$currentYear}年度企业自评报告提交的最后一天，您尚未提交本年度的自评报告。请您尽快登录系统完成自评报告的填报和提交，避免影响您的安全生产标准化评定。";
            $smsType = '企业自评报告截止提醒';
        }

        $smsUrl = '/general/toppingsoft/index.php/company/grading/index'; // 跳转到自评报告页面

        // 调用MessageModel发送站内消息
        //MessageModel::sendSms('company', $company['user_id'], $smsType, $smsContent, $smsUrl);
        self::sendMessageToObject($company['user_id'], 'company', $smsType, $smsContent, $smsUrl);
    }

    /**
     * 企业上传评审资料提醒任务
     * 每月月初、月末、季度初、季度末、年初、年末执行
     */
    public static function uploadReviewMaterialsReminder()
    {
        $today = date('Y-m-d');
        $year = date('Y');
        $month = date('m');
        $day = date('d');

        // 判断是否为月初（1号）、月末（当月最后一天）
        $isFirstDayOfMonth = $day === '01';
        $isLastDayOfMonth = $day === date('t', strtotime($today));

        // 判断是否为季度初或季度末
        $quarter = ceil($month / 3);
        $firstDayOfQuarter = date('Y-m-01', strtotime("{$year}-" . (($quarter - 1) * 3 + 1) . "-01"));
        $lastDayOfQuarter = date('Y-m-t', strtotime("{$year}-" . ($quarter * 3) . "-01"));

        $isFirstDayOfQuarter = $today === $firstDayOfQuarter;
        $isLastDayOfQuarter = $today === $lastDayOfQuarter;

        // 判断是否为年初或年末
        $isFirstDayOfYear = $month === '01' && $day === '01';
        $isLastDayOfYear = $month === '12' && $day === '31';

        // 判断是否命中提醒时间点
        if (!(
            $isFirstDayOfMonth || $isLastDayOfMonth || $isFirstDayOfQuarter || $isLastDayOfQuarter || $isFirstDayOfYear || $isLastDayOfYear
        )) {
            return;
        }

        // 获取所有正常状态的企业
        $companies = Db::table('top_company_info')
            ->where(['status' => 1])
            ->field('id, user_id, name')
            ->select();

        foreach ($companies as $company) {
            // 检查企业是否已上传当年评审资料（根据你的业务逻辑判断）
            $unsubmitted = Db::table('top_company_review_content_list')
                ->where([
                    ['company_id', '=', $company['id']],
                    ['time', '=', $year . '年'],
                    ['is_sub', '=', 0],
                ])
                ->count();

            if ($unsubmitted > 0) {
                self::sendReviewMaterialReminder($company, $isFirstDayOfMonth, $isLastDayOfMonth, $isFirstDayOfQuarter, $isLastDayOfQuarter, $isFirstDayOfYear, $isLastDayOfYear);
            }
        }
    }

    /**
     * @param array $company 根据时间节点生成提醒内容并发送消息
     * @param bool $isFirstDayOfMonth 判断是否为月初（1号）
     * @param bool $isLastDayOfMonth 判断是否为月末（当月最后一天）a
     * @param bool $isFirstDayOfQuarter 判断是否为季度初
     * @param bool $isLastDayOfQuarter 判断是否为季度末
     * @param bool $isFirstDayOfYear 判断是否为年初
     * @param bool $isLastDayOfYear 判断是否为年末
     */
    private static function sendReviewMaterialReminder($company, $isFirstDayOfMonth, $isLastDayOfMonth, $isFirstDayOfQuarter, $isLastDayOfQuarter, $isFirstDayOfYear, $isLastDayOfYear)
    {
        $year = date('Y');
        $smsContent = '';
        $smsType = '';
        $smsUrl = '/general/toppingsoft/index.php/company/index/review';

        if ($isFirstDayOfYear) {
            $smsContent = "尊敬的{$company['name']}，您好！{$year}年已开始，请及时上传本年度评审资料，确保符合安全生产管理要求。";
            $smsType = '年初资料提醒';
        } elseif ($isLastDayOfYear) {
            $smsContent = "尊敬的{$company['name']}，您好！今天是{$year}年最后一天，您尚未上传本年度评审资料，请尽快上传，以免影响评定。";
            $smsType = '年末截止提醒';
        } elseif ($isFirstDayOfQuarter) {
            $smsContent = "尊敬的{$company['name']}，您好！第" . ceil(date('m') / 3) . "季度开始，请注意及时上传最新评审资料。";
            $smsType = '季度初资料提醒';
        } elseif ($isLastDayOfQuarter) {
            $smsContent = "尊敬的{$company['name']}，您好！今天是第" . ceil(date('m') / 3) . "季度最后一天，请尽快上传评审资料，避免影响评定进度。";
            $smsType = '季度末截止提醒';
        } elseif ($isFirstDayOfMonth) {
            $smsContent = "尊敬的{$company['name']}，您好！本月开始，请注意及时上传相关评审资料。";
            $smsType = '月初资料提醒';
        } elseif ($isLastDayOfMonth) {
            $smsContent = "尊敬的{$company['name']}，您好！今天是本月最后一天，请尽快上传评审资料，确保评定顺利进行。";
            $smsType = '月末截止提醒';
        }

        // 发送多渠道提醒
        self::sendMessageToObject($company['user_id'], 'company', $smsType, $smsContent, $smsUrl);
    }

    /**
     * 公示7天后发送站内信提醒市局发布公告
     */
    public static function remindPublicityToAnnounce()
    {
        $today = date('Y-m-d');

        // 获取 status = 1 且未提醒过的公示记录
        $publicityList = Db::table('top_publicity')
            ->where([
                ['status', '=', 1],
                //['remind_status', '<>', 1], // 未提醒过
            ])
            ->select();

        foreach ($publicityList as $item) {
            // 判断是否已过7天（公示开始时间 + 7天 <= 当前时间）
            $remindDate = date('Y-m-d', strtotime($item['date'] . ' +7 days'));
            if (strtotime($remindDate) > strtotime($today)) {
                continue; // 未到提醒时间
            }

            // 构造提醒内容
            $smsContent = "尊敬的市局用户，您好！公示标题《{$item['title']}》已公示满7天，请及时发布公告，避免影响后续流程。";
            $smsType = '公示公告提醒';
            $smsUrl = '/general/toppingsoft/index.php/city/ca/publicty'; // 站内信跳转链接

            // 发送多渠道提醒
            self::sendMessageToObject($item['user_id'], 'city', $smsType, $smsContent, $smsUrl);
        }
    }

    /**
     * 后续再异步
     * 多渠道发送消息给企业（站内消息、短信、邮件、微信等）
     */
    private static function sendMessageToObject($object_id, $object_type, $sms_type, $content, $url, $channel = ['site_message'])
    {
        /*// 获取企业联系人信息（手机号、邮箱、微信等）,仅仅多渠道考虑
        $contact = Db::table('top_company_contact')
        ->where(['company_id' => $company['id']])
            ->field('phone, email, wechat_openid')
            ->find();

        if (!$contact) return;*/
        foreach ($channel as $item) {
            if ($item == 'site_message') {
                // 站内消息
                MessageModel::sendSms($object_type, $object_id, $sms_type, $content, $url);
            }
            /*if($item == 'sms'){
                // 短信通知
                if ($contact['phone']) {
                    \app\service\SmsService::send($contact['phone'], $content);
                }
            }*/
            /*if($item == 'email'){
                // 邮件通知
                if ($contact['email']) {
                    \app\service\MailService::send($contact['email'], '评审资料上传提醒', $content);
                }
            }*/
            /*if($item == 'wechat'){
                // 微信通知
                if ($contact['wechat_openid']) {
                    \app\service\WechatService::sendMessage($contact['wechat_openid'], $content, $url);
                }
            }*/
        }
    }


}