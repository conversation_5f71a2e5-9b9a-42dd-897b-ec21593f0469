<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>初次申请</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/company.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .my-content p{ line-height:20px; margin: 10px 0;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">
        <el-form :inline="true" :model="searchFrom" class="form-inline" @submit.native.prevent>
            <el-form-item style="float: right">
                <el-button :loading="loading" type="success" size="mini" @click="add" v-loading.fullscreen.lock="loading">初次申请</el-button>
            </el-form-item>
        </el-form>
        <el-table border
                  v-loading="loading"
                  :data="data"
                  style="width: 100%;margin-bottom: 20px;"
                  ref="qtable"
                  :height="height"
                  size="small">
            <el-table-column
                    type="index"
                    label="序号"
                    align="center"
                    width="50">
            </el-table-column>
            <el-table-column
                    prop="company_name"
                    label="企业名称"
                    align="center"
                    show-overflow-tooltip
                    min-width="200">
            </el-table-column>
            <el-table-column
                    prop="level"
                    label="申请等级"
                    align="center"
                    width="100">
            </el-table-column>
            <el-table-column
                    prop="type"
                    label="申请类型"
                    align="center"
                    width="100">
            </el-table-column>
            <el-table-column
                    prop="nature"
                    label="创建性质"
                    align="center"
                    min-width="100">
                <template slot-scope="scope">
                    <div v-if="scope.row.nature==0" type="info">自主创建</div>
                    <div v-if="scope.row.nature==1" type="primary">“{{scope.row.advisory}}”指导</div>
                </template>
            </el-table-column>
            <!--<el-table-column
                    prop="reform_status"
                    label="整改内容"
                    align="center"
                    width="100">
                <template slot-scope="scope">
&lt;!&ndash;                    <el-link v-if="scope.row.reform_status>0" @click="reforminfo(scope.row)" type="primary">详情</el-link>&ndash;&gt;
                </template>
            </el-table-column>-->
            <el-table-column
                    width="150px"
                    :show-overflow-tooltip="true"
                    prop="processing_stage_name"
                    label="当前办理阶段">
                <template slot-scope="scope">
                    {{scope.row.prcs_name}}
                </template>
            </el-table-column>
            <!--<el-table-column
                    prop="FLOW_PRCS"
                    label="已停留"
                    show-overflow-tooltip
                    width="150">
                <template slot-scope="scope">
                    <p>{{scope.row.reach_time}}</p>
                    <p>{{scope.row.receive_time}}</p>
                </template>
            </el-table-column>-->
            <el-table-column
                    prop="mb_status"
                    label="申请状态"
                    align="center"
                    show-overflow-tooltip
                    width="120">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.status==0" type="">未提交</el-tag>
                    <el-tag v-if="scope.row.status==1" type="info">审批中</el-tag>
                    <el-tag v-if="scope.row.status==2" type="info">整改中</el-tag>
                    <el-tag v-if="scope.row.status==3" type="info">整改审批中</el-tag>
                    <el-tag v-if="scope.row.status==7" type="primary">已通过</el-tag>
                    <el-tag v-if="scope.row.status==5" type="danger">未通过</el-tag>
                    <el-tag v-if="scope.row.status==9" type="danger">放弃评审</el-tag>
                </template>
            </el-table-column>
            <el-table-column
                    prop="apply_time"
                    label="申请时间"
                    align="center"
                    show-overflow-tooltip
                    width="120">
            </el-table-column>
            <el-table-column
                    label="操作"
                    align="center"
                    width="300">
                <template slot-scope="scope">
                    <el-button type="" @click="info(scope.row)" size="small">详情</el-button>
                    <el-button v-if="scope.row.status==0" type="warning" @click="edit(scope.row)" size="small">编辑</el-button>
                    <el-button v-if="scope.row.status==1" type="danger" @click="retract(scope.row)" size="small">放弃评审</el-button>
                    <el-button v-if="scope.row.status==2" type="primary" @click="reformList(scope.row)" size="small"></el-button>
                    <el-button v-if="scope.row.status==2" type="success" @click="reform(scope.row)" size="small">整改申请</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-dialog title="温馨提示" width="500px" :visible.sync="visible" :close-on-click-modal="false">
            <div class="my-content">
                <p>首次申请定级需持续运行半年以上，请确认当前日期可申请定级。<br/><span  style="color:red;">可申请复评日期：{$company.st_date}</span></p>
                <p>填报前，您需要提前准备以下材料</p>
                <p>1.企业定级申请表；<el-link type="primary" download="企业定级申请表" href="/general/toppingsoft/public/word/template1.docx">模板下载</el-link></p>
                <p>2.营业执照复印件；高危行业企业安全生产（经营）许可证书复印件；</p>
                <p>3.企业主要负责人承诺书（初次申请定级承诺内容应当符合第七条规定；复评申请定级承诺内容应当符合第七条、第九条规定）；<el-link type="primary" download="企业主要负责人承诺书" href="/general/toppingsoft/public/word/template3.docx">模板下载</el-link></p>
                <p>4.工艺流程图、总平面布置图、周边关系示意图、设备设施台账等；</p>
                <p>5.原安全生产标准化证书复印件（复评申请提供）；</p>
                <p>6.自评报告（初次申请提供近一年自评报告；复评申请提供近三年自评报告）。<el-link type="primary" href="/general/toppingsoft/index.php/company/se/index">自评管理</el-link></p>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="visible = false" size="small">取 消</el-button>
                <el-button type="primary" @click="next" size="small">准备完毕，上传资料</el-button>
            </div>
        </el-dialog>
    </div>
    <reform-list ref="reformList" @ok="getData()"></reform-list>
    <reform ref="reform" @ok="getData()"></reform>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.all.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                title: '新增',
                searchFrom: {},
                data: [],
                visible: false,
                loading: false,
                height: document.documentElement.clientHeight - 155,
            };
        },
        components: {
            'reform': 'url:/general/toppingsoft/app/company/view/grading/vue/reform.vue?v=1',
            'reform-list': 'url:/general/toppingsoft/app/company/view/grading/vue/reformList.vue?v=1',
            'uploadfileitem': 'url:/general/toppingsoft/public/vue/preview.vue?v=1',
        },
        watch: {
            $route() {
                //切换标签时
                this.handleRefreshTable();
            }
        },
        created() {
            let _this = this;
            //窗口改变时
            window.onresize = function () {
                _this.handleRefreshTable();
            };
        },
        updated() {
            //数据改变时
            this.handleRefreshTable();
        },
        methods: {
            //数据加载
            getData() {
                var _this = this;
                var param = _this.searchFrom;
                param._ajax = 1;
                _this.loading = true;
                axios.post('', param).then(function (res) {
                    if (res.data.code == 0) {
                        _this.data = res.data.data.data;
                        _this.$nextTick(() => {
                            // 这里的代码会在DOM更新完成后执行
                            _this.loading = false;
                        });
                        _this.handleRefreshTable();
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            add(){
                this.visible = true;
            },
            reformList(row){
                this.$refs.reformList.open(row);
            },
            reform(row){
                this.$refs.reform.open(row);
            },
            next(){
                location.href = 'applyfirst';
            },
            edit(row){
                location.href = 'applyfirst?id='+row.id;
            },
            info(row){
                location.href = 'info?id='+row.id;
            },
            retract(row){
                var _this = this;
                this.$prompt('放弃评审半年内不可申请评审，确认请在下方输入框输入“放弃评审”', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    inputPattern: /^放弃评审$/,
                    inputErrorMessage: '输入有误'
                }).then(({value}) => {
                    _this.loading = true;
                    var param = {};
                    param.id = row.id;
                    axios.post('retract', param).then(function (res) {
                        _this.loading = false;
                        if (res.data.code == 0) {
                            _this.$message({
                                message: res.data.msg,
                                type: "success"
                            });
                            _this.getData();
                        } else {
                            _this.$message({
                                message: res.data.msg,
                                type: "error"
                            });
                        }
                    }).catch(function (error) {
                        console.log(error);
                    });
                }).catch(function (error){
                    console.log(error);
                });
            },
            handleRefreshTable: function () {
                this.$nextTick(function () {
                    var refList = this.$refs;
                    if (refList) {
                        for (var i of Object.keys(refList)) {
                            if (refList[i] && refList[i].doLayout) {
                                refList[i].doLayout();
                            }
                        }
                    }
                });
            },
        },
        mounted() {
            //获取列表
            this.getData();
        }
    })
</script>


</body>
</html>