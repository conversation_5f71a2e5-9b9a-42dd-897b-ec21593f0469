<?php

namespace app\expert\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use app\company\model\CompanyModel;
use app\expert\model\ExpertModel;
use app\validate\ExpertVerify as Verify;
use hg\apidoc\annotation as Apidoc;
use think\facade\View;
use think\Request;
use think\facade\Db;

/**
 * @Apidoc\Title("日程管理")
 * @Apidoc\Group("Index")
 * @Apidoc\Sort(1)
 */
class Schedule extends Base {

    /**
     * @Apidoc\Title("我的日程")
     * @Apidoc\Desc("我的日程")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("日程")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function index() {
        if (request()->isAjax()) {
            $where = [];
            $where[] = ['expert_id','=',$_SESSION['expert']['id']];
            $where[] = ['status','=',1];
            $res['idle'] = Db::table('top_expert_idle')->where($where)->order('date')
                ->select()->each(function ($item, $key) {
                $item['classname'] = 'selected';
                $item['content'] = '已报名';
                return $item;
            });
            $res['tasks'] = Db::table('top_org_tasks')->alias('a')
                ->leftJoin('top_org_tasks_experts b','a.id = b.tasks_id')
                ->where(['b.expert_id'=>$_SESSION['expert']['id']])->order('a.date')
                ->field('a.id,a.date,a.company_name,b.status')
                ->select()->each(function ($item, $key) {
                $item['classname'] = 'selected';
                $item['content'] = $item['company_name'].'现场评审';
                return $item;
            });
            result($res);
        } else {
            return view();
        }
    }

    public function addDate($date=''){
        if(empty($date)){
            result('',1002,'请选择日期');
        }
        $data = [
            'expert_id' => $_SESSION['expert']['id'],
            'date' => $date,
        ];
        $re = Db::table('top_expert_idle')->where($data)->find();
        if($re){
            Db::table('top_expert_idle')->where(['id'=>$re['id']])->update(['status'=>1]);
        }else{
            Db::table('top_expert_idle')->insertGetId($data);
        }
        result('',0,'报名成功');
    }

    public function delDate($date=''){
        if(empty($date)){
            result('',1002,'请选择日期');
        }
        $data = [
            'expert_id' => $_SESSION['expert']['id'],
            'date' => $date,
        ];
        $re = Db::table('top_expert_idle')->where($data)->find();
        if($re){
            Db::table('top_expert_idle')->where(['id'=>$re['id']])->update(['status'=>0]);
        }else{
            result('',1002,'数据不存在，取消报名失败');
        }
        result('',0,'取消报名成功');
    }


}
