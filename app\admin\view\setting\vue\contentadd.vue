<style>
.my-autocomplete li{line-height: normal;padding: 7px;}
.my-autocomplete li .name{text-overflow: ellipsis;overflow: hidden;}
.my-autocomplete li .addr{font-size: 12px;color: #b4b4b4;}
.my-autocomplete li .highlighted{color: #ddd;}
</style>
<template>
  <el-dialog title="添加" width="90%" class="icon-dialog" :visible.sync="visible" append-to-body>
    <el-form size="small" ref="form" :model="form" :rules="rules" label-width="140px">
      <el-row v-if="options">
        <el-col :span="24">
          <el-form-item label="上级要素" prop="element_id">
            <el-cascader style="width: 400px;" v-model="form.element_id" :options="options" :props="{value:'id',label:'name'}">
            </el-cascader>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="指标名称" prop="content">
            <el-input type="textarea" v-model="form.content" autoComplete="off" clearable  placeholder="请输入评审标准名称"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row >
        <el-col :span="6">
          <el-form-item label="指标分值" prop="score">
            <el-input  v-model="form.score" autoComplete="off" clearable  placeholder=""></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="指标填报周期" prop="cycle">
            <el-select v-model="form.cycle" placeholder="请选择">
              <el-option key="线下核实" label="线下核实" value=""></el-option>
              <el-option key="月" label="月" value="月"></el-option>
              <el-option key="季" label="季" value="季"></el-option>
              <el-option key="年" label="年" value="年"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="排序号" prop="sort">
            <el-input  v-model="form.sort" autoComplete="off" clearable  placeholder="请输入排序号"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row >
        <el-col :span="24">
          <el-form-item label="基本规范要求" prop="ask">
            <el-input type="textarea" v-model="form.ask" autoComplete="off" clearable  placeholder=""></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row >
        <el-col :span="24">
          <el-form-item label="企业达标标准" prop="standards">
            <el-input type="textarea" v-model="form.standards" autoComplete="off" clearable  placeholder=""></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row >
        <el-col :span="24">
          <el-form-item label="评分方式" prop="method">
            <el-input type="textarea" v-model="form.method" autoComplete="off" clearable  placeholder=""></el-input>
            <!--							<key-data v-if="show" :item.sync="jzd"></key-data>-->
          </el-form-item>
        </el-col>
      </el-row>
      <el-row >
        <el-col :span="24">
          <el-form-item label="自评/评审描述" prop="mark">
            <el-input type="textarea"  v-model="form.file_remark" autoComplete="off" clearable  placeholder="" ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row >
        <el-col :span="24">
          <el-form-item label="模板材料" prop="mark">
            <el-upload
                class="upload-demo"
                action="upload"
                multiple
                :file-list="form.files"
                limit="20"
                :on-success="(response,file,fileList)=>uploadSuccess(response,file,fileList)"
                :before-upload="uploadBefore"
                :on-preview="preview"
                :on-remove="(file,fileList)=>handleRemove(file,fileList)">
              <el-button size="small" type="primary">点击上传</el-button>
              <!--                                <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>-->
            </el-upload>
<!--            <Upload size="small" file_type="files" :files.sync="form.files"></Upload>-->
          </el-form-item>
        </el-col>
      </el-row>
      <el-row >
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" :loading="loading" type="primary" @click="submit" v-loading.fullscreen.lock="loading">保 存</el-button>
      <el-button size="small" @click="visible = false">取 消</el-button>
    </div>
    <preview ref="preview"></preview>
  </el-dialog>
</template>
<script>
module.exports = {
  name: "add",
  // 模板导入区
  components: {
    'preview': 'url:/general/toppingsoft/public/vue/preview.vue',
  },
  data:function() {
    return {
      id:0,
      isAdmin: false,
      visible: false,
      title: '新增',
      options: [],
      form: {
        pid:'',
        main_id:'',
        pname:'',
        name:'',
        mark:'',
        weight:'',
        sort:'',
      },
      loading:false,
      rules: {
        pid:[
          {required: true, message: '上级要素不能为空', trigger: 'blur'},
        ],
        name:[
          {required: true, message: '要素名称名称不能为空', trigger: 'blur'},
        ],
      }
    }
  },
  mounted: function(){
  },
  created:function(){
  },
  methods: {
    /**
     * 打开弹窗调用方法
     * */
    open: function (row,options) {
      console.log(row)
      row = row?row:{id:0};
      var _this =this;
      _this.options = options;
      _this.visible = true;
      _this.form = row;
    },
    uploadBefore(file) {
      // const isJPG = file.type === 'image/jpeg';
      // const isPNG = file.type === 'image/png';
      // const isPDF = file.type === 'application/pdf';
      // if(!isJPG&&!isPNG&&!isPDF){
      //     this.$message.error('请上传jpg/png/pdf文件');
      // }
      // return isJPG||isPNG||isPDF;
    },
    uploadSuccess(res, file,fileList) {
      var files = [];
      console.log(fileList);
      for(var i in fileList){
        files.push(fileList[i].response?fileList[i].response.data.code:fileList[i].code);
      }
      console.log(files);
      this.form.files = files;
    },
    handleRemove(file, fileList) {
      var files = [];
      for(var i in fileList){
        files.push(fileList[i].response?fileList[i].response.data.code:fileList[i].code);
      }
      this.form.files = files;
    },
    preview: function (file) {
      file = file.response?file.response.data:file;
      this.$refs.preview.open(file.url,file.name);
    },
    submit: function () {
      var _this = this;
      var param = _this.form;
      this.$refs.form.validate(function (valid) {
        if(valid){
          axios.post("standardContentSave", param).then(function (res) {
            _this.$message({
              message: res.data.msg,
              type: res.data.type
            });
            if (res.data.code == 0) {
              _this.visible = false;
              _this.$emit("ok");
            }
          }).catch(function (error) {
            console.log("出现错误:",error);
          });
        }
      });
    },
    refresh: function () {
      this.$emit("refresh");
    },
  }
}
</script>