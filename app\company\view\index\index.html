<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>企业管理</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/company.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .el-badge__content.is-fixed { right: 20px;z-index: 9;}
        .is-active { background-color:#3556B8 !important;}
        .el-menu-item i { color:#ffffff;}
        .el-submenu__title i { color:#ffffff;}
        .el-form-item{margin-bottom: 0;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <el-container>
        <el-header style="text-align:center;font-size: 12px;height:50px;">
            <b style="font-size:16px;float:left">成都市企业安全生产标准化信息管理系统</b>
            <span>{$user.company_name}</span>
            <el-dropdown style="float:right">
                    <span><el-image
                            style="width: 30px; height: 30px;border-radius:30px;float:left;margin:10px;"
                            :src="head"
                            fit="cover"></el-image>
                        {$user.user_name}
                        <i class="el-icon-more" style="margin:0 15px;transform: rotate(90deg);color:#999;"></i></span>
                <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item @click.native="openUserInfo">个人信息</el-dropdown-item>
                    <el-dropdown-item @click.native="editpassword">修改密码</el-dropdown-item>
                    <el-dropdown-item @click.native="logout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
            </el-dropdown>
        </el-header>
        <el-container>
            <el-aside width="200px">
                <el-menu
                        default-active="{$url}"
                        :style="{height:(height-50)+'px'}"
                        :height="height"
                        @select="open"
                        background-color="#436CE6"
                        text-color="#ffffff"
                        active-text-color="#ffffff">
                    <el-menu-item index="index/main">
                        <i class="el-icon-house"></i>
                        <span slot="title">首页</span>
                    </el-menu-item>
                    <el-menu-item index="index/info">
                        <i class="el-icon-office-building"></i>
                        <span slot="title">企业信息</span>
                    </el-menu-item>
                    <el-submenu>
                        <template slot="title">
                            <i class="el-icon-price-tag"></i>
                            <span>三级标准化建设</span>
                        </template>
                        <el-menu-item index="standard/index">
<!--                            <i class="el-icon-price-tag"></i>-->
                            <span slot="title">创标申请</span>
                        </el-menu-item>
                        <el-menu-item index="info/review">
<!--                            <i class="el-icon-notebook-2"></i>-->
                            <span slot="title">运行资料</span>
                        </el-menu-item>
                        <el-menu-item index="se/index">
<!--                            <i class="el-icon-price-tag"></i>-->
                            <span slot="title">自评管理</span>
                        </el-menu-item>
                        <el-menu-item index="grading/index">
<!--                            <i class="el-icon-discount"></i>-->
                            <span slot="title">初次申请</span>
                        </el-menu-item>
                        <el-menu-item index="info/review?v=1">
                            <span slot="title">持续运行</span>
                        </el-menu-item>
                        <el-menu-item index="grading/repeat">
<!--                            <i class="el-icon-discount"></i>-->
                            <span slot="title">复评申请</span>
                        </el-menu-item>
                        <el-menu-item index="task/index">
                            <!--                            <i class="el-icon-discount"></i>-->
                            <span slot="title">评审任务查看</span>
                        </el-menu-item>
                        <el-menu-item index="discuss/index">
<!--                            <i class="el-icon-discount"></i>-->
                            <span slot="title">评审群聊</span>
                        </el-menu-item>
                    </el-submenu>
                    <el-menu-item index="grading/record">
                        <i class="el-icon-discount"></i>
                        <span slot="title">一/二级备案</span>
                    </el-menu-item>
                    <el-menu-item index="ca/index">
                        <i class="el-icon-tickets"></i>
                        <span slot="title">我的证书</span>
                    </el-menu-item>
                </el-menu>
            </el-aside>
            <el-main :style="{height:(height-50)+'px'}">
                <iframe id="iframe" :src="iframeSrc" width="100%" height="100%" border="0" frameborder="0" framespacing="0" marginheight="0" marginwidth="0"></iframe>
            </el-main>
        </el-container>
    </el-container>
    <el-dialog title="修改密码" width="500px" :visible.sync="visible">
        <el-form :model="form">
            <el-descriptions class="margin-top" title="" :column="1" size="small" border>
                <el-descriptions-item label="原密码">
                    <el-form-item label="">
                        <el-input type="password" v-model="form.old_password" ></el-input>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label="新密码">
                    <el-form-item label="">
                        <el-input type="password" v-model="form.new_password" ></el-input>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label="确认新密码">
                    <el-form-item label="">
                        <el-input type="password" v-model="form.confirm_password" ></el-input>
                    </el-form-item>
                </el-descriptions-item>
            </el-descriptions>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="visible = false">取消</el-button>
            <el-button type="primary" @click="onSubmit">保存</el-button>
        </div>
    </el-dialog>

    <!-- 个人信息弹框 -->
    <el-dialog title="个人信息" width="600px" :visible.sync="showUserInfoDialog" @close="closeUserInfoDialog">
                 <!-- 显示个人信息 -->
         <div v-if="!isEditingUserInfo">
             <el-descriptions :column="2" border>
                 <el-descriptions-item label="姓名">
                     {{userInfo.name || '未设置'}}
                 </el-descriptions-item>
                 <el-descriptions-item label="手机号">
                     <span style="margin-right: 10px;">{{userInfo.mobile || '未设置'}}</span>
                     <el-button type="text" size="mini" @click="startEditMobile" style="color: #409EFF;">
                         <i class="el-icon-edit"></i> 修改
                     </el-button>
                 </el-descriptions-item>
                 <el-descriptions-item label="邮箱">
                     {{userInfo.email || '未设置'}}
                 </el-descriptions-item>
                 <el-descriptions-item label="注册时间" :span="2">
                     {{userInfo.reg_time || '未知'}}
                 </el-descriptions-item>
             </el-descriptions>
         </div>

         <!-- 手机号修改表单 -->
         <div v-if="isEditingMobile">
             <el-alert 
                 title="修改手机号" 
                 type="info" 
                 :closable="false"
                 show-icon
                 style="margin-bottom: 20px;">
                 <span>当前手机号：{{userInfo.mobile || '未设置'}}</span>
             </el-alert>
             
             <el-form :model="mobileForm" :rules="mobileRules" ref="mobileForm" label-width="100px">
                 <el-form-item label="新手机号" prop="mobile">
                     <el-input
                         v-model="mobileForm.mobile"
                         placeholder="请输入新的11位手机号"
                         maxlength="11"
                         @input="formatMobileInput"
                         @blur="checkNewMobile">
                         <template slot="prepend">+86</template>
                     </el-input>
                     <div style="font-size:12px; color:#999; margin-top:5px;">
                         <i class="el-icon-warning" style="color:#E6A23C;"></i> 
                         手机号用于登录系统，修改后请使用新手机号登录
                     </div>
                 </el-form-item>
                 
                 <!-- 短信验证码输入框 -->
                 <el-form-item v-if="showMobileSmsCode" label="短信验证码" prop="sms_code">
                     <el-row :gutter="10">
                         <el-col :span="16">
                             <el-input 
                                 v-model="mobileForm.sms_code" 
                                 placeholder="请输入6位验证码"
                                 maxlength="6">
                             </el-input>
                         </el-col>
                         <el-col :span="8" style="margin-top:12px;">
                             <el-button 
                                 type="primary" 
                                 @click="sendMobileSmsCode" 
                                 :disabled="mobileSmsButtonDisabled || !isValidNewMobile"
                                 :loading="sendingMobileSms"
                                 style="width:100%;">
                                 {{mobileSmsButtonText}}
                             </el-button>
                         </el-col>
                     </el-row>
                     <div style="font-size:12px; color:#999; margin-top:5px;">
                         <i class="el-icon-info" style="color:#409EFF;"></i> 
                         验证码将发送到新手机号码
                     </div>
                 </el-form-item>
             </el-form>
         </div>

         <!-- 编辑其他个人信息表单 -->
         <div v-if="isEditingUserInfo">
             <el-form :model="editUserForm" :rules="userInfoRules" ref="editUserForm" label-width="100px">
                 <el-form-item label="姓名" prop="name">
                     <el-input v-model="editUserForm.name" placeholder="请输入姓名（选填）"></el-input>
                 </el-form-item>
                 <el-form-item label="邮箱" prop="email" style="margin-top: 12px">
                     <el-input v-model="editUserForm.email" placeholder="请输入邮箱（选填）"></el-input>
                 </el-form-item>
             </el-form>
         </div>

                 <div slot="footer" class="dialog-footer">
             <!-- 查看模式按钮 -->
             <div v-if="!isEditingUserInfo && !isEditingMobile">
                 <el-button @click="closeUserInfoDialog">关闭</el-button>
                 <el-button type="primary" @click="editUserInfo">修改其他信息</el-button>
             </div>
             <!-- 手机号修改模式按钮 -->
             <div v-if="isEditingMobile">
                 <el-button @click="cancelEditMobile">取消</el-button>
                 <el-button type="primary" @click="saveMobile" :loading="mobileLoading">保存手机号</el-button>
             </div>
             <!-- 其他信息编辑模式按钮 -->
             <div v-if="isEditingUserInfo">
                 <el-button @click="cancelEditUserInfo">取消</el-button>
                 <el-button type="primary" @click="saveUserInfo" :loading="userInfoLoading">保存</el-button>
             </div>
         </div>
    </el-dialog>
</div>

<!--<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>-->
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<!--<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>-->
<!--<script src="__PUBLIC__/plugs/layer/layer.js"></script>-->
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<!--<script src="__PUBLIC__/static/js/request.js"></script>-->
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data:function() {
                         return {
                 visible:false,
                 showUserInfoDialog: false,
                 isEditingUserInfo: false,
                 userInfoLoading: false,
                 isEditingMobile: false,
                 mobileLoading: false,
                title: '',
                head: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
                iframeSrc: '',
                loading: false,
                form:{
                    old_password:'',
                    new_password:'',
                    confirm_password:'',
                },
                                                                   userInfo: {},
                                   editUserForm: {
                       name: '',
                       email: ''
                   },
                   mobileForm: {
                       mobile: '',
                       sms_code: ''
                   },
                 
                  // 手机号修改专用状态
                  showMobileSmsCode: false, // 是否显示手机号修改的验证码输入框
                  sendingMobileSms: false, // 是否正在发送手机号修改的短信
                  mobileSmsButtonDisabled: false, // 手机号修改短信按钮是否禁用
                  mobileSmsButtonText: '发送验证码', // 手机号修改短信按钮文字
                  mobileSmsCountdown: 0, // 手机号修改短信倒计时
                                                                   userInfoRules: {
                      name: [
                          { required: false, message: '请输入姓名', trigger: 'blur' },
                          { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' },
                          { pattern: /^[\u4e00-\u9fa5a-zA-Z\s]+$/, message: '姓名只能包含中文、英文和空格', trigger: 'blur' }
                      ],
                      email: [
                          { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
                      ]
                  },
                  mobileRules: {
                      mobile: [
                          { required: true, message: '请输入新手机号', trigger: 'blur' },
                          { pattern: /1[0-9]{10}$/, message: '请输入正确的11位手机号', trigger: 'blur' }
                      ],
                      sms_code: [
                          { required: true, message: '请输入短信验证码', trigger: 'blur' },
                          { pattern: /^\d{6}$/, message: '请输入6位数字验证码', trigger: 'blur' }
                      ]
                  },
                                 height: document.documentElement.clientHeight,
             };
         },
                   computed: {
              // 检查新手机号是否有效
              isValidNewMobile: function() {
                  return /1[0-9]{10}$/.test(this.mobileForm.mobile);
              }
          },
         components: {
         },
        methods: {
            open:function(key, keyPath) {
                if(key){
                    this.iframeSrc = '/general/toppingsoft/index.php/company/'+key;
                }
            },
            getMessage:function(){
                var _this = this;
                axios.post('getMessage', {}).then(function (res) {
                    if (res.data.code == 0) {
                        for (i in res.data.data.message){
                            _this.$notify({
                                title: '消息提醒',
                                message: res.data.data.message[i].sms_content,
                                type: 'info',
                                duration: 0,
                                onClick:function(e){
                                    axios.post('getMessageInfo', {id:res.data.data.message[i].id}).then(function (re) {
                                        if (re.data.code == 0) {
                                            if(re.data.data.sms_url){
                                                _this.open(re.data.data.sms_url);
                                            }
                                        }
                                    }).catch(function (error) {
                                        console.log("出现错误:",error);
                                    });
                                    this.close();
                                }
                                // position: 'bottom-right'
                            });
                        }
                    }
                }).catch(function (error) {
                    console.log("出现错误:",error);
                });
            },
            logout:function() {
                location.href = 'loginout';
            },
                         openUserInfo:function() {
                 this.showUserInfoDialog = true;
                 this.getUserInfo();
             },
             // 开始修改手机号
             startEditMobile: function() {
                 this.isEditingMobile = true;
                 this.mobileForm = {
                     mobile: '',
                     sms_code: ''
                 };
                 this.showMobileSmsCode = false;
                 this.resetMobileSmsButton();
             },
             // 取消修改手机号
             cancelEditMobile: function() {
                 this.isEditingMobile = false;
                 this.showMobileSmsCode = false;
                 this.resetMobileSmsButton();
                 if(this.$refs.mobileForm) {
                     this.$refs.mobileForm.resetFields();
                 }
             },
            editpassword:function (){
                this.form = {
                    old_password:'',
                    new_password:'',
                    confirm_password:'',
                };
                this.visible = true;
            },
            onSubmit:function (){
                var _this = this;
                var params = _this.form;
                _this.loading = true;
                axios.post('editPassword', params).then(function (res) {
                    if (res.data.code == 0) {
                        _this.visible = false;
                        _this.$alert('密码修改成功，请重新登陆', '提示', {
                            confirmButtonText: '确定',
                            callback: function action() {
                                _this.logout();
                            }
                        });
                    }else{
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                    }
                }).catch(function (error) {
                    console.log("出现错误:",error);
                });
            },
            // 获取用户信息
            getUserInfo: function() {
                var _this = this;
                axios.post('getUserInfo', {}).then(function (res) {
                    if (res.data.code == 0) {
                        _this.userInfo = res.data.data;
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                    }
                }).catch(function (error) {
                    console.log("出现错误:", error);
                    _this.$message({
                        message: '获取用户信息失败',
                        type: "error"
                    });
                });
            },
                                                                             // 切换编辑其他信息模式
               editUserInfo: function() {
                   this.isEditingUserInfo = true;
                   this.editUserForm = {
                       name: this.userInfo.name || '',
                       email: this.userInfo.email || ''
                   };
               },
                                                   // 取消编辑其他个人信息
              cancelEditUserInfo: function() {
                  this.isEditingUserInfo = false;
                  if(this.$refs.editUserForm) {
                      this.$refs.editUserForm.resetFields();
                  }
              },
                                                   // 关闭个人信息弹框
              closeUserInfoDialog: function() {
                  this.showUserInfoDialog = false;
                  this.isEditingUserInfo = false;
                  this.isEditingMobile = false;
                  this.showMobileSmsCode = false;
                  this.resetMobileSmsButton();
              },
              // 格式化手机号输入（手机号修改专用）
              formatMobileInput: function(value) {
                  this.mobileForm.mobile = value.replace(/[^\d]/g, '');
              },
              // 检查新手机号
              checkNewMobile: function() {
                  if (this.isValidNewMobile && this.mobileForm.mobile !== this.userInfo.mobile) {
                      this.showMobileSmsCode = true;
                      this.mobileForm.sms_code = '';
                  } else {
                      this.showMobileSmsCode = false;
                  }
              },
                         
              // 发送手机号修改验证码
              sendMobileSmsCode: function() {
                  var _this = this;
                  if (!this.isValidNewMobile) {
                      this.$message({
                          message: '请输入正确的手机号格式',
                          type: "error"
                      });
                      return;
                  }
                  
                  this.sendingMobileSms = true;
                  var params = {
                      mobile: this.mobileForm.mobile
                  };
                  
                  axios.post('sendUserInfoSms', params).then(function (res) {
                      _this.sendingMobileSms = false;
                      if (res.data.code == 0) {
                          _this.$message({
                              message: '验证码发送成功',
                              type: "success"
                          });
                          _this.startMobileCountdown(); // 开始倒计时
                      } else {
                          _this.$message({
                              message: res.data.msg,
                              type: "error"
                          });
                      }
                  }).catch(function (error) {
                      _this.sendingMobileSms = false;
                      console.log("出现错误:", error);
                      _this.$message({
                          message: '验证码发送失败，请重试',
                          type: "error"
                      });
                  });
              },
              // 开始手机号修改倒计时
              startMobileCountdown: function() {
                  var _this = this;
                  this.mobileSmsCountdown = 60;
                  this.mobileSmsButtonDisabled = true;
                  this.mobileSmsButtonText = this.mobileSmsCountdown + 's后重发';
                  
                  var timer = setInterval(function() {
                      _this.mobileSmsCountdown--;
                      _this.mobileSmsButtonText = _this.mobileSmsCountdown + 's后重发';
                      
                      if (_this.mobileSmsCountdown <= 0) {
                          clearInterval(timer);
                          _this.resetMobileSmsButton();
                      }
                  }, 1000);
              },
              // 重置手机号修改短信按钮状态
              resetMobileSmsButton: function() {
                  this.mobileSmsButtonDisabled = false;
                  this.mobileSmsButtonText = '发送验证码';
                  this.mobileSmsCountdown = 0;
              },
              // 保存手机号
              saveMobile: function() {
                  var _this = this;
                  this.$refs.mobileForm.validate(function(valid) {
                      if (valid) {
                          _this.mobileLoading = true;
                          var params = {
                              name: _this.userInfo.name, // 保持原有姓名
                              mobile: _this.mobileForm.mobile,
                              email: _this.userInfo.email, // 保持原有邮箱
                              sms_code: _this.mobileForm.sms_code
                          };
                          
                          axios.post('updateUserInfo', params).then(function (res) {
                              _this.mobileLoading = false;
                              if (res.data.code == 0) {
                                  _this.$message({
                                      message: '手机号修改成功',
                                      type: "success"
                                  });
                                  _this.isEditingMobile = false;
                                  _this.getUserInfo(); // 重新获取用户信息
                                  _this.showMobileSmsCode = false;
                                  _this.resetMobileSmsButton();
                              } else {
                                  _this.$message({
                                      message: res.data.msg,
                                      type: "error"
                                  });
                              }
                          }).catch(function (error) {
                              _this.mobileLoading = false;
                              console.log("出现错误:", error);
                              _this.$message({
                                  message: '修改失败，请重试',
                                  type: "error"
                              });
                          });
                      } else {
                          return false;
                      }
                  });
              },
            // 保存其他用户信息（姓名、邮箱）
             saveUserInfo: function() {
                 var _this = this;
                 this.$refs.editUserForm.validate(function(valid) {
                     if (valid) {
                         _this.userInfoLoading = true;
                         var params = {
                             name: _this.editUserForm.name,
                             mobile: _this.userInfo.mobile, // 保持原有手机号
                             email: _this.editUserForm.email,
                             sms_code: '' // 不修改手机号时不需要验证码
                         };
                         
                         axios.post('updateUserInfo', params).then(function (res) {
                             _this.userInfoLoading = false;
                             if (res.data.code == 0) {
                                 _this.$message({
                                     message: '个人信息修改成功',
                                     type: "success"
                                 });
                                 _this.isEditingUserInfo = false;
                                 _this.getUserInfo(); // 重新获取用户信息
                                 // 如果修改了姓名，刷新页面以更新右上角显示的用户名
                                 if(_this.editUserForm.name !== _this.userInfo.name) {
                                     setTimeout(function() {
                                         window.location.reload();
                                     }, 1000);
                                 }
                             } else {
                                 _this.$message({
                                     message: res.data.msg,
                                     type: "error"
                                 });
                             }
                         }).catch(function (error) {
                             _this.userInfoLoading = false;
                             console.log("出现错误:", error);
                             _this.$message({
                                 message: '修改失败，请重试',
                                 type: "error"
                             });
                         });
                     } else {
                         return false;
                     }
                 });
             },
        },
        mounted:function() {
            this.open('{$url}');
            // this.getMessage();
        }
    })

    window.addEventListener("message", function (e) {
        if (e.data && e.data.redirectUrl) {
            window.location.href = e.data.redirectUrl;
        }
    });
</script>


</body>
</html>