<?php
declare (strict_types = 1);

namespace app\area\controller;

use think\App;
use think\facade\Cache;
use think\facade\Db;
use \liliuwei\think\Jump;

session_start();

/**
 * 控制器基础类
 */
abstract class Base
{
    use Jump;


    protected $baseUrl = '/general/toppingsoft/index.php/';

    /**
     * Request实例
     * @var \think\Request
     */
    protected $request;

    /**
     * 应用实例
     * @var \think\App
     */
    protected $app;


    /**
     * 是否批量验证
     * @var bool
     */
    protected $batchValidate = false;

    /**
     * 控制器中间件
     * @var array
     */
    protected $middleware = [];


    protected $userInfo;

    //空方法
    public function __call($method, $args)
    {
        $data = request()->param();
        return view($method, ['data' => $data]);
    }

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        error_reporting(E_ERROR | E_PARSE);
        $this->app     = $app;
        $this->request = $this->app->request;

        //初始化
        $this->initialize();
        // 关闭未定义对象报错
        error_reporting(E_ERROR | E_PARSE);
    }

    // 初始化
    protected function initialize()
    {
        $vars = $this->request;
        /*$_SESSION['company'] = [
            'id' => '1',
            'user_id' => '1',
        ];*/
        $token = $this->request->param('token');
        $this->user = empty($_SESSION['area'])?Cache::get('area_'.$token):$_SESSION['area'];
        if (empty($this->user)) {
            //后面改成实际ip
            $controller = $vars->controller();
            if ($controller != 'Login') {
                if (!empty($_SERVER['REQUEST_METHOD']) && strtolower($_SERVER['REQUEST_METHOD']) == 'post') {
                    // 接口请求处理
                    result('',9001,'请先登陆');
                } else {
                    header("Location: ".config('app.http_host'). "/general/toppingsoft/index.php/area/login/login");
                    exit();
                    // 非接口请求处理
                }
//                exit("http://" . $host . ":" . $port . "/general/toppingsoft/index.php/company/login/login");
            }
        }
    }

}
