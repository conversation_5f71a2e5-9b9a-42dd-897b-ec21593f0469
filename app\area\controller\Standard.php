<?php
declare (strict_types = 1);

namespace app\area\controller;

use hg\apidoc\annotation as Apidoc;
use app\model\SettingModel;
use think\facade\Db;
use think\facade\View;
use think\Request;
use app\model\FileModel;

class Standard extends Base
{
    /**
     * @Apidoc\Title("创标申请列表")
     * @Apidoc\Desc("创标申请列表")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function index($limit = 20, $excel = 0)
    {
        if (request()->isAjax()) {
            $title = $this->request->param('title','','trim');
            $status = $this->request->param('status','','trim');
            $where = [
                ['area_id','=',$_SESSION['area']['id']],
                ['dept_id','in',$_SESSION['area']['manager']],
            ];
            if(!empty($title)){
                $where[] = ['company_name','like',"%{$title}%"];
            }
            if(!empty($status)){
                $where[] = ['status','=',$status];
            }
            $res = Db::table('top_standard_apply')->where($where)->order('id desc');
            $res = $res->paginate($limit)->each(function ($item, $key) {
                return $item;
            });
            result($res);
        } else {
            View::assign('title', '首页');
            return view();
        }
    }



    public function stadnardInfo($id = 0)
    {
        $re = Db::table('top_standard_apply')->alias("a1")
                ->field("a1.*,a2.remark")
                ->leftJoin("top_standard_apply_approval a2","a1.id=a2.apply_id")
                ->where(['a1.id' => $id,'a1.area_id'=>$_SESSION['area']['id'],['a1.dept_id','in',$_SESSION['area']['manager']]])
                ->find();
        if (empty($re)) {
            result('', 1002, '申请信息不存在');
        }
        result($re);
    }

    public function stadnardCheck($id = 0,$status=0,$reason='')
    {
        $re = Db::table('top_standard_apply')->where(['id' => $id,'area_id'=>$_SESSION['area']['id'],['dept_id','in',$_SESSION['area']['manager']]])->find();
        if(empty($re)){
            result('', 1002, '申请信息不存在');
        }
        Db::table('top_standard_apply')->where(['id'=>$re['id']])->update(['status'=>$status]);
        $data = [
            'apply_id' => $re['id'],
            'remark' => $reason,
            'status' => $status,
            'check_user_id' => $_SESSION['area']['user_id'],
            'check_user_name' => $_SESSION['area']['user_name'],
            'check_time' => date('Y-m-d H:i:s'),
        ];
        Db::table('top_standard_apply_approval')->insert($data);
        if($status==7){
            $data = [
                'standard_id' => $re['standard_id'],
                'standard_name' => $re['standard_name'],
                'standard_level' => $re['level'],
                'stand_status' => 1,
                'stand_date' => date('Y-m-d'),
                'st_date' => date('Y-m-d',strtotime('+6month')),
            ];
            Db::table('top_company_info')->where(['id'=>$re['company_id']])->update($data);
            SettingModel::setReview($re['company_id']);
            SettingModel::setReport($re['company_id']);
        }
        result($re);
    }

    public function companyInfo($id = 0)
    {
        \app\model\CompanyModel::getCompanyInfo($id);
        exit();
    }

    public function getReview($element_id = 0)
    {
        if (request()->isAjax()) {
            $request = $this->request->post();
            \app\model\CompanyModel::getAjaxReview($element_id, $request);
            exit();

        } else {
            View::assign('element_id', $element_id);
            View::assign('title', '首页');
            return view();
        }
    }
}
