﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>注册</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/login.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <script src="__PUBLIC__/plugs/element/axios.min.js"></script>
    <script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
    <script src="__PUBLIC__/static/js/request.js"></script>
</head>
<body>
<div class="y_noSelect" id="app" v-cloak>
    <div class="login_bg">
        <div class="login_box">
            <div class="login_title">
                <p class="login_tit">成都市企业安全生产标准化信息管理系统</p>
                <span style="line-height: 50px;font-size:22px;color:#999999">- 企业端 -</span>
            </div>
            <div class="login_content">
                <ul class="login_nav">
                    <li class="'login_nav_item active'">注册账号</li>
                </ul>
                <div class="login_item_box">
                    <div class="login_item">
                        <input class="login_inp name" type="text" v-model="form.mobile" placeholder="请输入手机号" @keyup.enter="doSub" />
                    </div>
                    <div class="login_item">
                        <input class="login_inp psd" type="password" v-model="form.password" placeholder="请输入密码" @keyup.enter="doSub" />
                    </div>
                    <div class="login_item">
                        <input class="login_inp code" type="tel" v-model="form.imgcode" placeholder="请输入图片验证码" @keyup.enter="doSub" />
                        <img class="login_cod" :src="codeurl" @click="getCode"/>
                    </div>
                    <div class="login_item">
                        <input class="login_inp code" type="tel" v-model="form.code" placeholder="请输入短信验证码" @keyup.enter="doSub" />
                        <span :class="'login_inp_btn '+(yzm_boo?'active':'')" @click="getCod">{{yzmtex}}</span>
                    </div>
                </div>
                <button class="login_btn" @click="doSub">注 册</button>
                <p style="text-align: center;font-size:14px;color:#999;">已有账号？<a style="color:#409eff;cursor: pointer;" href="login">去登录</a></p>
            </div>
        </div>
        <!-- <div class="login_box">
            <img class="login_box_bg" src="__PUBLIC__/static/images/img/login_bg_in.png"/>
            <img class="login_logo" src="__PUBLIC__/static/images/icon/login_logo.png"/>
            <div class="login_cont">
                <img class="login_title" src="__PUBLIC__/static/images/icon/login_title.png"/>
                <input type="text" class="login_input user" v-model="form." placeholder="" />
                <input type="password" class="login_input pasd" v-model="form.password" placeholder="请输入密码" @keyup.enter="doSub"/>
                <div class="y_textRight">
                    <span :class="'login_check '+(form.remb?'active':'')" @click="rember">记住密码</span>
                </div>
                <button class="login_btn" @click="doSub">登 录</button>
            </div>
        </div> -->
    </div>
</div>
</body>
</html>
<script type="text/javascript">
    const app = new Vue({
        el: '#app',
        data: {
            nav_on: 0,
            nav: ['密码登录', '验证码登录'],
            form: {type: 0},
            codeurl: '',
            yzmtex: '获取验证码',
            yzm_boo: false,
        },
        methods: {
            navChang: function(k){
                this.nav_on = k
                this.form.type = k
            },
            getCod: function(){
                var _this = this;
                if(this.yzm_boo){return}
                if(!this.form.mobile){
                    _this.$message({
                        message: '请填写手机号码',
                        type: "error"
                    });
                    return;
                }
                const loading = this.$loading();
                axios.post('registerSms', {mobile: _this.form.mobile}).then(function (res) {
                    loading.close();
                    if (res.data.code == 0) {
                        _this.yzm_boo = true;
                        var time = 60;
                        _this.yzmtex = '重新获取（'+time+'）';
                        var timer = setInterval(function(){
                            --time;
                            if(time < 0){
                                clearInterval(timer);
                                _this.yzm_boo = false;
                                _this.yzmtex = '获取验证码';
                            }else{
                                _this.yzmtex = '重新获取（'+time+'）';
                            }
                        }, 1000)
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            getCode: function(){
                this.codeurl = 'verify?t='+new Date().getMilliseconds();
            },
            doSub: function () {
                let _this = this;
                if (!_this.form.mobile) {
                    _this.$message({
                        message: '请输入手机号',
                        type: "error"
                    });
                    return;
                }
                if (!_this.form.password&&_this.nav_on==0) {
                    _this.$message({
                        message: '请输入密码',
                        type: "error"
                    });
                    return;
                }
                if (!_this.form.imgcode) {
                    _this.$message({
                        message: '请输入图片验证码',
                        type: "error"
                    });
                    return;
                }
                if (!_this.form.code&&_this.nav_on==1) {
                    _this.$message({
                        message: '请输入验短信证码',
                        type: "error"
                    });
                    return;
                }
                const loading = this.$loading();
                axios.post('register', _this.form).then(function (res) {
                    loading.close();
                    if (res.data.code == 0) {
                        _this.$alert('注册成功，去登录？', '提示', {
                            confirmButtonText: '确定',
                            callback: action => {
                                location.href = 'login';
                            }
                        });
                        /*_this.$message({
                            message: res.data.msg,
                            type: "success",
							duration: 1500 
                        });
                        location.href = 'login';*/
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            }
        },
        mounted: function () {
            let _this = this;
            _this.getCode();
        }
    })
</script>