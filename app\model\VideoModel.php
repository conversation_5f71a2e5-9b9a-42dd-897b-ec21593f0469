<?php


namespace app\model;


use think\Model;
use think\App;
use think\facade\Db;

//权限配置模块
class VideoModel extends Model
{

    protected $appid = '1050737590748';
    protected $key = '28ee28a1206cb1af73575d3b7f2f5c3c';
    protected $domain = 'http://172.42.136.164/platform/video';

    function getFleetSub(){
        $data = [
            'subNumber' => '',
            'subName' => '',
            'extNumber' => '',
            'fleetId' => 6,
            'pageNum' => 1,
            'pageSize' => 100,
        ];
        $res = http_post('http://172.42.136.164/platform/video2/scs/subscriberManageScs/getFleetSub',$data);
        $res = json_decode($res,true);
        return $res['obj']['list'];
    }

    function pushTask($params){
        $data = [
            'timestamp' => time()*1000,
            'taskDetail' => $params,
        ];
        $header = ["Safe-City-Sign: ".$this->sign($data)];
        $re = self::postUrl($this->domain.'/safe-city/task/pushTask',$data,$header);
        return $re;
    }

    /**
     * 判断是否是管理员
     * @return bool
     */
    function sign($data){
        $temp = $data['timestamp'].$this->key;
        return md5($temp);
    }

    //curlPost请求
    function postUrl($url, $data = [],$header=[]) {
        $ch = curl_init($url);
        $header[] = 'Content-Type: application/json';
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 1000);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $return_data = curl_exec($ch);
        curl_errno($ch);
        return $return_data;
    }


}