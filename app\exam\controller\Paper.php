<?php

namespace app\exam\controller;


use app\model\TasksModel;
use think\Exception;
use think\facade\Db;
use think\Request;

class Paper extends Base
{

    protected $type = 'paper';//

    public function createOnePaper(Request $request)
    {
        try {

            $task_id = $request->param('task_id', "");
            $standard_id = $request->param('standard_id', "");
            $sign = $request->param('sign', "");
            $position_name = $request->param('position_name', "");
            $user_name = $request->param('user_name', "");
            if (empty($task_id)) {
                throw new Exception("任务id为空");
            }
            if (empty($sign)) {
                throw new Exception("签名有误");
            }
            if (empty($position_name)) {
                throw new Exception("岗位未填写");
            }
            if (empty($user_name)) {
                throw new Exception("参考人未填写");
            }
            if (empty($standard_id)) {
                throw new Exception("评审标准为空");
            }


            $count = Db::table('top_cay_exam_paper')
                ->field(['user_name', 'position_name'])
                ->where('task_id', $task_id)
                ->where('user_name', $user_name)
//                ->where('position_name',  $position_name)
                ->whereNotNull('end_date')
                ->count();
            if ($count > 0) {
                throw new Exception($user_name . '已经参加了考试不能再次参加');
            }


            //检查人数
            $verify = new TasksModel();
            $verify = $verify->examVerify($task_id, $sign);

            if ($verify['code'] !== 0) {
                throw new Exception($verify['msg']);
            }
            $exam_num = $verify['data']['exam_num'];

            $count = Db::table('top_cay_exam_paper')
                ->field(['user_name', 'position_name'])
                ->where('task_id', $task_id)
                ->where('user_name', '<>', $user_name)
//                ->where('position_name', '<>', $position_name)
                ->count();

            if ($count >= $exam_num) {
                throw new Exception('大于参考人数不能报名参加');
            }
            $model = Db::table('top_cay_exam_config')->where('id', '>', 0)->find();
            if (empty($model)) {
                throw new Exception("试题配置为空，请联系管理员配置");
            }


            Db::startTrans();
            Db::table('top_cay_exam_paper')
                ->where('task_id', $task_id)
                ->where('user_name', $user_name)
//                ->where('position_name', $position_name)
                ->delete();
            //插入试卷基本信息
            $unique_id = uniqid(microtime(true), true);
            $count = Db::table('top_cay_exam_paper')
                ->insertGetId([
                    'task_id' => $task_id,
                    'sign' => $sign,
                    'user_name' => $user_name,
                    'position_name' => $position_name,
                    'total_score' => 0,
                    'uid' => $unique_id,
                ]);
            if ($count != 1) {
                throw new Exception("参加考试失败");
            }
            $paper_id = Db::table('top_cay_exam_paper')->where('uid', $unique_id)->value('id');

            $base_radio_count = intval(($model['base'] * 0.01) * $model['radio']);
            $speciality_radio_count = $model['radio'] - $base_radio_count;
            $base_judgment_count = intval(($model['speciality'] * 0.01) * $model['judgment']);
            $speciality_judgment_count = $model['judgment'] - $base_judgment_count;


            $test_list = [];
            $data_speciality = Db::table('top_cay_exam_test_info')
                ->where('standard_name_id', 'like', '%,' . $standard_id . ',%')
                ->whereNotNull('standard_name_id')
                ->where('type_id', 1)
                ->orderRand()
                ->limit($speciality_radio_count)
                ->select()
                ->toArray();
            if (!empty($data_speciality)) {
                $test_list = array_merge($test_list, $data_speciality);
            }

            $base_radio_count = $base_radio_count + ($speciality_radio_count - count($data_speciality));
            if ($base_radio_count > 0) {
                $data_base_radio = Db::table('top_cay_exam_test_info')
                    ->whereNull('standard_name_id')
                    ->where('type_id', 1)
                    ->orderRand()
                    ->limit($base_radio_count)
                    ->select()
                    ->toArray();

                if (count($data_base_radio) < $base_radio_count) {
                    throw new Exception("公共单选题量不足，请联系管理员！！");
                }
                $test_list = array_merge($test_list, $data_base_radio);
            }


            $data_speciality_judgment = Db::table('top_cay_exam_test_info')
                ->where('standard_name_id', 'like', '%,' . $standard_id . ',%')
                ->whereNotNull('standard_name_id')
                ->where('type_id', 2)
                ->orderRand()
                ->limit($speciality_judgment_count)
                ->select()
                ->toArray();
            if (!empty($data_speciality_judgment)) {
                $test_list = array_merge($test_list, $data_speciality_judgment);
            }

            $base_judgment_count = $base_judgment_count + ($speciality_judgment_count - count($data_speciality_judgment));
            if ($base_judgment_count > 0) {
                $data_base_judgment = Db::table('top_cay_exam_test_info')
                    ->whereNull('standard_name_id')
                    ->where('type_id', 2)
                    ->orderRand()
                    ->limit($base_judgment_count)
                    ->select()->toArray();
                if (count($data_base_judgment) < $base_judgment_count) {
                    throw new Exception("公共判断题量不足，请联系管理员！！");
                }
                $test_list = array_merge($test_list, $data_base_judgment);
            }

            //插入试卷试题信息
            foreach ($test_list as $item) {
                Db::table('top_cay_exam_paper_list')
                    ->insert([
                        'test_info_id' => $item['id'],
                        'answer' => $item['test_answer'],
                        'score' => $model['one_judgment_score'],//one_judgment_score
                        'paper_id' => $paper_id,
                        'my_score' => 0,
                    ]);
            }


            Db::commit();
            return json(['code' => 1000, 'type' => 'success', 'data' => $paper_id, 'msg' => "创建成功"]);
        } catch (\Exception $exception) {
            Db::rollback();
            return json(['code' => 201, 'type' => 'error', 'msg' => $exception->getMessage()]);
        }
    }

    /**
     * @param Request $request
     * @return \think\response\Json
     *
     * 获取试卷详情
     */
    public function getOnePaperInfo(Request $request)
    {
        try {
            $alphabet = range('A', 'Z');
            $paper_id = $request->param('paper_id', "");
            Db::table('top_cay_exam_paper')
                ->where('id', $paper_id)
                ->update([
                    'start_date' => date('Y-m-d H:i:s'),
                ]);
            $paperModel = Db::table('top_cay_exam_paper')->where('id', $paper_id)->find();
            $testList = Db::table('top_cay_exam_paper_list')
                ->alias('l')
                ->field(['l.*', 'i.test_title', 'i.test_option'])
                ->leftJoin('top_cay_exam_test_info i', 'l.test_info_id = i.id')
                ->where('l.paper_id', $paper_id)
                ->select()
                ->each(function ($model) use ($alphabet) {
                    $model['test_option'] = json_decode($model['test_option'], true);
                    $option = [];
                    foreach ($model['test_option'] as $key => $item) {
                        $option[$alphabet[$key]] = $item;
                    }

                    $model['test_option'] = $option;

                    return $model;
                });
            $paperModel['test_list'] = $testList;
            return json(['code' => 1000, 'type' => 'success', 'data' => $paperModel, 'msg' => "试卷获取成功"]);
        } catch (\Exception $exception) {
            Db::rollback();
            return json(['code' => 201, 'type' => 'error', 'msg' => $exception->getMessage()]);
        }
    }

    /**
     * @param Request $request
     * @return \think\response\Json
     *
     * 提交试卷
     */
    public function submitOnePaperInfo(Request $request)
    {
        try {
            $paper_id = $request->param('paper_id', "");
            $paper_list = $request->param('paper_list', "");
            Db::startTrans();
            foreach ($paper_list as $key => $item) {
                $answer = $item['answer'];
                $my_answer = $item['my_answer'];
                $score = $item['score'];
                $my_score = 0;
                $all_my_score = [];
                $my_answer = str_replace(' ', '', $my_answer);
                $answer = str_replace(' ', '', $answer);
                $type_id = Db::table('top_cay_exam_test_info')->where('id', $item['test_info_id'])->value('type_id');
                if ($type_id == 2) {
                    $my_answer = $item['test_option'][$my_answer];
                }
                if (!empty($my_answer) && ($answer == $my_answer)) {
                    $my_score = $score;
                    $all_my_score[] = $my_score;
                }

                Db::table('top_cay_exam_paper_list')
                    ->where('id', $item['id'])
                    ->where('paper_id', $paper_id)
                    ->update([
                        'my_score' => $my_score,
                        'my_answer' => $my_answer,
                    ]);
            }
            $total_score = Db::table('top_cay_exam_paper_list')
                ->where('paper_id', $paper_id)
                ->sum('my_score');
            Db::table('top_cay_exam_paper')
                ->where('id', $paper_id)
                ->update([
                    'end_date' => date('Y-m-d H:i:s'),
                    'total_score' => $total_score,
                ]);
            Db::commit();
            return json(['code' => 1000, 'type' => 'success', 'data' => $total_score, 'msg' => "交卷成功"]);
        } catch (\Exception $exception) {
            Db::rollback();
            return json(['code' => 201, 'type' => 'error', 'msg' => $exception->getMessage()]);
        }
    }


}