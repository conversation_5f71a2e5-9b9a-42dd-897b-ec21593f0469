<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>一、二级备案</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/company.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .my-content p{ line-height:20px; margin: 10px 0;}
        .el-form-item--mini.el-form-item, .el-form-item--small.el-form-item {margin-bottom: 0;}
        .el-dialog__body{
            overflow: hidden;
        }
        .el-descriptions .is-bordered {
             table-layout: initial;
        }
        .el-date-editor.el-input, .el-date-editor.el-input__inner {
            width: 100%;
        }
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">
        <el-form :inline="true" :model="searchFrom" class="form-inline" @submit.native.prevent>
            <el-form-item style="float: right">
                <el-button :loading="loading" type="success" size="mini" @click="add" v-loading.fullscreen.lock="loading">备案申请</el-button>
            </el-form-item>
        </el-form>
        <el-table border
                  v-loading="loading"
                  :data="data"
                  style="width: 100%;margin-bottom: 20px;"
                  ref="qtable"
                  :height="height"
                  size="small">
            <el-table-column
                    type="index"
                    label="序号"
                    align="center"
                    width="50">
            </el-table-column>
            <el-table-column
                    prop="company_name"
                    label="企业名称"
                    align="center"
                    show-overflow-tooltip
                    min-width="150">
            </el-table-column>
            <el-table-column
                    prop="address"
                    label="地址"
                    align="center"
                    show-overflow-tooltip
                    min-width="120">
            </el-table-column>
            <el-table-column
                    prop="area_name"
                    label="区县"
                    align="center"
                    width="100">
            </el-table-column>
               <el-table-column
                    prop="manager"
                    label="联系人"
                    align="center"
                    width="100">
            </el-table-column>
            <el-table-column
                    prop="manager"
                    label="联系人"
                    align="center"
                    width="100">
            </el-table-column>
            <el-table-column
                prop="content"
                label="备注"
                align="center"
                min-width="100">
            </el-table-column>
            <el-table-column
                    prop="manager_mobile"
                    label="电话"
                    align="center"
                    width="120">
            </el-table-column>
            <el-table-column
                    prop="industry"
                    label="行业类别"
                    align="center"
                    show-overflow-tooltip
                    width="120">
            </el-table-column>
            <el-table-column
                    prop="certificate_code"
                    label="证书编号"
                    align="center"
                    show-overflow-tooltip
                    width="120">
            </el-table-column>
            <el-table-column
                    prop="certificate_start_date"
                    label="生效日期"
                    align="center"
                    width="110">
            </el-table-column>
            <el-table-column
                    prop="certificate_end_date"
                    label="证书有效期"
                    align="center"
                    width="110">
            </el-table-column>
            <el-table-column
                    prop="level"
                    label="申请等级"
                    align="center"
                    width="100">
            </el-table-column>
            <el-table-column
                    prop="mb_status"
                    label="申请状态"
                    align="center"
                    show-overflow-tooltip
                    width="120">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.status==0" type="">未提交</el-tag>
                    <el-tag v-if="scope.row.status==1" type="info">审批中</el-tag>
                    <el-tag v-if="scope.row.status==7" type="primary">已通过</el-tag>
                    <el-tag v-if="scope.row.status==5" type="danger">未通过</el-tag>
                </template>
            </el-table-column>
            <el-table-column
                    prop="apply_time"
                    label="申请时间"
                    align="center"
                    show-overflow-tooltip
                    width="120">
            </el-table-column>
            <el-table-column
                    label="操作"
                    align="center"
                    width="200">
                <template slot-scope="scope">
                    <el-button type="" @click="info(scope.row)" size="small">详情</el-button>
                    <el-button v-if="scope.row.status==0" type="warning" @click="edit(scope.row)" size="small">编辑</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-dialog title="备案申请" width="1000px" :visible.sync="visible" :close-on-click-modal="false">
            <el-form size="small" :model="form"  ref="form" class="oa-form">
                <el-descriptions class="margin-top" :column="2" border label-style="width:100px" content-style="width:300px">
                    <el-descriptions-item label="企业名称">
                        <el-form-item prop="company_name">
                            {{form.company_name}}
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="地址">
                        <el-form-item prop="address">
                            {{form.address}}
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="区县">
                        <el-form-item prop="area_name">
                            {{form.area_name}}
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="联系人">
                        <el-form-item prop="manager">
                            {{form.manager}}
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="电话">
                        <el-form-item prop="manager_mobile">
                            {{form.manager_mobile}}
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="行业类别">
                        <el-form-item prop="industry">
                            {{form.industry}}
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="证书编号">
                        <el-form-item prop="certificate_code">
                            <el-input v-model="form.certificate_code" placeholder="请输入证书编号"></el-input>
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="申请等级">
                        <el-form-item prop="level">
                            <el-radio-group v-model="form.level">
                                <el-radio label="一级">一级</el-radio>
                                <el-radio label="二级">二级</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="证书生效日期" >
                        <el-form-item prop="certificate_start_date">
                            <el-date-picker v-model="form.certificate_start_date"  type="date" placeholder="选择证书生效日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd" @change="calculateEndDate"></el-date-picker>
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="证书有效期">
                        <el-form-item prop="certificate_end_date">
                            <el-date-picker v-model="form.certificate_end_date" type="date" placeholder="选择证书有效期" format="yyyy-MM-dd" value-format="yyyy-MM-dd"></el-date-picker>
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="备注说明" :span="2">
                        <el-form-item prop="content">
                            <el-input type="textarea" autosize v-model="form.content"  placeholder="请输入备注说明"></el-input>
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="证书附件" :span="2">
                        <el-form-item prop="file">
                            <el-upload
                                    class="upload-demo"
                                    action="upload"
                                    :on-success="(response,file,fileList)=>uploadSuccess(response,file,fileList,'file')"
                                    :before-upload="uploadBefore"
                                    :on-preview="preview"
                                    :on-remove="(file,fileList)=>uploadSuccess(file,fileList,'file')"
                                    multiple
                                    :limit="3"
                                    :file-list="data.file">
                                <el-button size="small" type="primary">点击上传证书</el-button>
                                <div slot="tip" class="el-upload__tip">只能上传jpg/png/pdf文件，且不超过2M</div>
                            </el-upload>
                        </el-form-item>
                    </el-descriptions-item>
                </el-descriptions>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="visible = false" size="small">取 消</el-button>
                <el-button type="primary" @click="submit" size="small">提 交</el-button>
            </div>
        </el-dialog>
        <el-dialog title="详情" width="800px" :visible.sync="infoVisible" :close-on-click-modal="false">
            <el-form size="small" :model="form"  ref="form" class="oa-form">
                <el-descriptions class="margin-top" :column="2" border label-style="width:100px" content-style="width:300px">
                    <el-descriptions-item label="企业名称">
                        <el-form-item prop="company_name">
                            {{form.company_name}}
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="地址">
                        <el-form-item prop="address">
                            {{form.address}}
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="区县">
                        <el-form-item prop="area_name">
                            {{form.area_name}}
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="联系人">
                        <el-form-item prop="manager">
                            {{form.manager}}
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="电话">
                        <el-form-item prop="manager_mobile">
                            {{form.manager_mobile}}
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="行业类别">
                        <el-form-item prop="industry">
                            {{form.industry}}
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="证书编号">
                        <el-form-item prop="certificate_code">
                            {{form.certificate_code}}
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="生效日期" label-style="width:130px">
                        <el-form-item prop="certificate_start_date">
                            {{form.certificate_start_date}}
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="证书有效期" label-style="width:120px">
                        <el-form-item prop="certificate_end_date">
                            {{form.certificate_end_date}}
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="申请等级">
                        <el-form-item prop="level">
                            {{form.level}}
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="申请时间">
                        <el-form-item>
                            {{form.apply_time}}
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="状态">
                        <el-form-item prop="status">
                            <el-tag v-if="form.status==0" type="">未提交</el-tag>
                            <el-tag v-if="form.status==1" type="info">审批中</el-tag>
                            <el-tag v-if="form.status==7" type="primary">已通过</el-tag>
                            <el-tag v-if="form.status==5" type="danger">未通过</el-tag>
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="审核人">
                        <el-form-item prop="check_user_name">
                            {{form.check_user_name}}
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="审核时间">
                        <el-form-item prop="check_time">
                            {{form.check_time}}
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="备注说明" :span="2">
                        <el-form-item prop="content">
                            {{form.content}}
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="证书附件" :span="2">
                        <el-form-item prop="file">
                            <p v-for="file in form.file">
                                <el-link @click="preview(file)" type="primary">{{file.name}}</el-link>
                            </p>
                        </el-form-item>
                    </el-descriptions-item>
                </el-descriptions>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="infoVisible = false" size="small">关闭</el-button>
            </div>
        </el-dialog>
        <preview ref="preview"></preview>
    </div>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.all.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                title: '新增',
                searchFrom: {},
                form: {
                    company_id:'{$company.id}',
                    company_name:'{$company.name}',
                    address:'{$company.mb_operate_address}',
                    area_name:'{$company.area_name}',
                    manager:'{$company.manager}',
                    manager_mobile:'{$company.manager_mobile}',
                    industry:'{$company.industry}',
                    certificate_code:'',
                    certificate_start_date:'',
                    certificate_end_date:'',
                    level:'',
                    content:'',
                    file:[],
                    certificate_file:[],
                },
                data: [],
                visible: false,
                infoVisible: false,
                loading: false,
                height: document.documentElement.clientHeight - 155,
            };
        },
        components: {
            'preview': 'url:/general/toppingsoft/public/vue/preview.vue',
        },
        watch: {
            $route() {
                //切换标签时
                this.handleRefreshTable();
            },
            // 监听生效日期变化，自动计算有效期
            'form.certificate_start_date'(newVal) {
                if (newVal) {
                    this.calculateEndDate();
                }
            }
        },
        created() {
            let _this = this;
            //窗口改变时
            window.onresize = function () {
                _this.handleRefreshTable();
            };
        },
        updated() {
            //数据改变时
            this.handleRefreshTable();
        },
        methods: {
            //计算有效期（默认3年）
            calculateEndDate() {
                if (this.form.certificate_start_date) {
                    const startDate = new Date(this.form.certificate_start_date);
                    const endDate = new Date(startDate);
                    endDate.setFullYear(startDate.getFullYear() + 3); // 默认3年有效期
                    
                    // 格式化日期为 yyyy-MM-dd
                    const year = endDate.getFullYear();
                    const month = String(endDate.getMonth() + 1).padStart(2, '0');
                    const day = String(endDate.getDate()).padStart(2, '0');
                    
                    this.form.certificate_end_date = `${year}-${month}-${day}`;
                }
            },
            //数据加载
            getData() {
                var _this = this;
                var param = _this.searchFrom;
                console.log(_this.form)
                param._ajax = 1;
                _this.loading = true;
                axios.post('', param).then(function (res) {
                    if (res.data.code == 0) {
                        _this.data = res.data.data.data;
                        _this.$nextTick(() => {
                            // 这里的代码会在DOM更新完成后执行
                            _this.loading = false;
                        });
                        _this.handleRefreshTable();
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            add(){
                this.visible = true;
            },
            edit(row){
                this.form = Object.assign({}, row);
                console.log( row)
                this.visible = true;
            },
            info(row){
                this.form = row;
                this.infoVisible = true;
            },
            uploadBefore(file) {
                console.log(file)
                const isJPG = file.type === 'image/jpeg';
                const isPNG = file.type === 'image/png';
                const isPDF = file.type === 'application/pdf';
                if(!isJPG&&!isPNG&&!isPDF){
                    this.$message.error('请上传jpg/png/pdf文件');
                }
                return isJPG||isPNG||isPDF;
            },
            uploadSuccess(res, file,fileList,field) {
                var files = [];
                for(var i in fileList){
                    files.push(fileList[i].response.data??fileList[i]);
                }
                this.form[field] = files;
            },
            preview: function (file) {
                file = file.response?file.response.data:file;
                this.$refs.preview.open(file.url,file.name);
            },
            submit: function (status) {
                var _this = this;
                var param = _this.form;
                this.$refs.form.validate(function (valid) {
                    if(valid){
                        _this.loading = true;
                        axios.post("recordSave", param).then(function (res) {
                            _this.loading = false;
                            _this.$message({
                                message: res.data.msg,
                                type: res.data.type
                            });
                            if (res.data.code == 0) {
                                _this.getData();
                            }
                            _this.visible = false;
                        }).catch(function (error) {
                            _this.visible = false;
                            console.log("出现错误:",error);
                        });
                    }
                });
            },
            handleRefreshTable: function () {
                this.$nextTick(function () {
                    var refList = this.$refs;
                    if (refList) {
                        for (var i of Object.keys(refList)) {
                            if (refList[i] && refList[i].doLayout) {
                                refList[i].doLayout();
                            }
                        }
                    }
                });
            },
        },
        mounted() {
            //获取列表
            this.getData();
        }
    })
</script>


</body>
</html>