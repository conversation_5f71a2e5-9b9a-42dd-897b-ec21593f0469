<style>
.my-label { width: 200px;}
.my-content { width: 450px;}
.margin-bottom { margin-bottom: 15px;}
.form-header { background-color: #E9F2F3; line-height: 25px; margin-bottom: 15px; padding: 5px 10px;}
.el-dialog__body { padding: 15px 20px;}
.el-tabs__content { overflow: auto;}
</style>
<template>
  <el-dialog :title="title" :close-on-click-modal="false"
             modal="false" :visible.sync="visible" width="90%" append-to-body="true" label-position="top">
    <el-descriptions class="margin-top" title="基本信息" :column="2" border label-class-name="my-label" content-class-name="my-content">
      <el-descriptions-item label="企业名称">
        {{data.company_name}}
      </el-descriptions-item>
      <el-descriptions-item label="行业/专业">
        {{data.industry}}/{{data.specialty}}
      </el-descriptions-item>
      <el-descriptions-item label="运行标准">
        {{data.standard_name}}
      </el-descriptions-item>
      <el-descriptions-item label="标准等级">
        {{data.level}}
      </el-descriptions-item>
      <el-descriptions-item label="是否机构参与">
        {{data.advisory}}
      </el-descriptions-item>
      <el-descriptions-item label="驳回原因" v-if="data.status==5">
        {{data.remark}}
      </el-descriptions-item>
      <el-descriptions-item label="状态">
        <el-tag v-if="data.status==5" type="danger">已驳回</el-tag>
        <el-tag v-if="data.status==1" type="">待审核</el-tag>
        <el-tag v-if="data.status==7" type="success">已通过</el-tag>
      </el-descriptions-item>
    </el-descriptions>
    <div style="padding:20px;text-align:center;">
      <template v-if="data.status==1">
        <el-button type="primary" @click="submit(7)" size="small">审核通过</el-button>
        <el-button type="danger" @click="submit(5)" size="small">驳回</el-button>
      </template>
      <el-button @click="visible = false" size="small">关闭</el-button>
    </div>
    <!--编辑联系人-->
  </el-dialog>
</template>
<script>
module.exports = {
  name: "add",
  // 模板导入区
  components: {
  },
  data:function() {
    return {
      id:0,
      isAdmin:false,
      visible: false,
      dialogFormVisible: false,
      title: '详情',
      loading: false,
      noMore: false,
      user_id: 0,
      data: {
      },
      is_see:0,
      ca:[],
      details:[],
      cards:{},
      type:{},
      pcas: [],
      restaurants:[],
      restaurants2:[],
      height: document.documentElement.clientHeight - 250,
    }
  },
  computed: {
    disabled () {
      return this.loading || this.noMore
    }
  },
  mounted: function(){
    //this.getConfig();
  },
  created:function(){
  },
  methods: {
    /**
     * 打开弹窗调用方法
     * */
    open: function (row) {
      var _this =this;
      _this.id = row.id;
      _this.visible = true;
      _this.noMore = false;
      _this.getInfo(row.id);
    },
    closeDialog: function () {
      this.visible = false;
    },
    submit:function(status){
      var _this = this;
      if(status==5){
        _this.$prompt('请填写驳回原因', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /^\S+$/,
          inputErrorMessage: '请填写驳回原因'
        }).then(({ value }) => {
          const loading = this.$loading();
          axios.post("stadnardCheck", {id:_this.id,status:status,reason:value}).then(function (res) {
            _this.$message({
              message: res.data.msg,
              type: res.data.type
            });
            if (res.data.code == 0) {
                _this.visible = false;
                window.parent.getMessage();
                _this.$emit("ok");
            }
            loading.close();
          }).catch(function (error) {
            console.log("出现错误:",error);
          });
        }).catch(() => {
        });
      }else if(status==7){
        _this.$confirm('确认审核通过？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        }).then(() => {
          const loading = this.$loading();
          axios.post("stadnardCheck", {id:_this.id,status:status}).then(function (res) {
            _this.$message({
              message: res.data.msg,
              type: res.data.type
            });
            if (res.data.code == 0) {
              _this.visible = false;
              _this.$emit("ok");
            }
            loading.close();
          }).catch(function (error) {
            console.log("出现错误:",error);
          });
        }).catch(() => {
        });
      }
    },
    getInfo:function(id){
      var _this = this;
      if(id){
        axios.post("stadnardInfo", {
          id:id
        }).then(function (res) {
          if (res.data.code == 0) {
            _this.data=res.data.data;
          }else {
            _this.$message({
              message: res.data.msg,
              type: res.data.type
            });
          }
        }).catch(function (error) {
          console.log("出现错误:",error);
        });
      }
    },
    addContacts: function(){
      this.form = {};
      this.dialogFormVisible = true;
    },
    editContacts: function(row){
      this.form = row;
      this.dialogFormVisible = true;
    },
  }
}
</script>


