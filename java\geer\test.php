<?php
function hsmEncrypt($data) {
    $result = shell_exec("java -jar hsm-client.jar enc \"$data\" 2>nul");
    return trim($result);
}

function hsmDecrypt($encryptedData) {
    $result = shell_exec("java -jar hsm-client.jar dec \"$encryptedData\" 2>nul");
    return trim($result);
}

function hsmHmac($data) {
    $result = shell_exec("java -jar hsm-client.jar hmac \"$data\" 2>nul");
    return trim($result);
}

function hsmVerify($data, $hmac) {
    $result = shell_exec("java -jar hsm-client.jar verify \"$data\" \"$hmac\" 2>nul");
    return trim($result) === 'true';
}

// 使用示例
$data = "使用示例";

// 机密性：加密解密
$encrypted = hsmEncrypt($data);
echo "加密: $encrypted\n";1
$decrypted = hsmDecrypt($encrypted);
echo "解密: $decrypted\n";

// 完整性：HMAC计算和验证
$hmac = hsmHmac($data);
echo "HMAC: $hmac\n";
$isValid = hsmVerify($data, $hmac);
echo "验证: " . ($isValid ? "通过" : "失败") . "\n";
?>