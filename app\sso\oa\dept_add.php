<?php
$DEPT_NO = '';
$DEPT_NAME = '';
$DEPT_PARENT = '';
$DEPT_TYPE = '0';
$TO_ID = '';
$TO_NAME = '';
$ASSISTANT_ID = '';
$ASSISTANT_NAME = '';
$TO_ID3 = '';
$TO_NAME3 = '';
$TO_ID4 = '';
$TO_NAME4 = '';
$TEL_NO = '';
$FAX_NO = '';
$DEPT_ADDRESS = '';
$DEPT_FUNC = '';
$DN_STR = '';
$button = '保存';
include_once "auth.php";
include_once "inc/session.php";
$PHPSESSID = (isset($_GET["PHPSESSID"]) ? $_GET["PHPSESSID"] : (isset($_POST["PHPSESSID"]) ? $_POST["PHPSESSID"] : ""));

if (preg_match("/^[a-z0-9]{20,32}$/i", $PHPSESSID)) {
    session_id($PHPSESSID);
}

if (stristr($PHP_SELF, "export") || stristr($PHP_SELF, "excel") || stristr($PHP_SELF, "word") || stristr($PHP_SELF, "attach.php") || stristr($PHP_SELF, "download.php") || stristr($PHP_SELF, "down.php")) {
    session_cache_limiter("private, must-revalidate");
}
session_start();
ob_start();
include_once "inc/utility.php";
$SCRIPT_NAME = $_SERVER["SCRIPT_NAME"];
if (0 < MYOA_OFFLINE_TIME_MIN) {
    $LAST_OPERATION_TIME = $_COOKIE["LAST_OPERATION_TIME"];
    if (!stristr($SCRIPT_NAME, "/general/ipanel/") && !stristr($SCRIPT_NAME, "/general/task_center/") && !stristr($SCRIPT_NAME, "/general/mytable/") && !stristr($SCRIPT_NAME, "/general/status_bar/") && !stristr($SCRIPT_NAME, "/general/topbar.php") && !stristr($SCRIPT_NAME, "/inc/")) {
        setcookie("LAST_OPERATION_TIME", time(), 0, "/");
    }
    if (($LAST_OPERATION_TIME != "") && ((MYOA_OFFLINE_TIME_MIN * 60) < (time() - $LAST_OPERATION_TIME))) {
        $sessionid = session_id();
        del_my_online_status($sessionid);
        clear_online_status();
        setcookie("LAST_OPERATION_TIME", "", 1, "/");
        session_unset();
        session_destroy();
    }
}

if (!isset($SESSION_WRITE_CLOSE) || ($SESSION_WRITE_CLOSE != 0)) {
    session_write_close();
}

$GZIP_POSTFIX = (MYOA_SUPPORT_GZIP ? ".gz" : "");

if (isset($HTML_PAGE_TITLE)) {
    unset($HTML_PAGE_TITLE);
}
include_once "inc/utility_all.php";
include_once "inc/utility_org.php";
include_once "inc/td_core.php";
include_once "inc/utility_email_audit.php";
$SYS_PARA_ARRAY = get_sys_para("LOGIN_USE_DOMAIN,WEIXINQY_CORPID,WEIXINQY_SECRET,QYWEIXIN_CORPID");
$LOGIN_USE_DOMAIN = $SYS_PARA_ARRAY["LOGIN_USE_DOMAIN"];
$WEIXINQY_CORPID = $SYS_PARA_ARRAY["WEIXINQY_CORPID"];
$WEIXINQY_SECRET = $SYS_PARA_ARRAY["WEIXINQY_SECRET"];
$QYWEIXIN_CORPID = $SYS_PARA_ARRAY["QYWEIXIN_CORPID"];
$sql = "SELECT secret FROM qyweixin_app where app_name = 'department'";
$cursor = exequery(TD::conn(), $sql);

if ($ROW = mysqli_fetch_array($cursor)) {
	$app_secret = $ROW["secret"];
}

$sql = "SELECT * FROM dingding_app where app_name = 'department'";
$cursor = exequery(TD::conn(), $sql);

if ($ROW = mysqli_fetch_array($cursor)) {
	$dd_app_key = $ROW["app_key"];
	$dd_app_secret = $ROW["app_secret"];
}
//include_once "inc/header.inc.php";

if (trim($DEPT_NAME) == "") {
	exit(json_encode(array('code' => 1001, 'msg' => '部门名称不能为空')));
}

if (strstr($DEPT_NAME, "\\")) {
	exit(json_encode(array('code' => 1002, 'msg' => '部门名称中含有\\')));
}

$DEPT_NAME = str_replace(array("\\\"", "\'"), array("", ""), $DEPT_NAME);

if (!preg_match("/[0-9]{3}$/", $DEPT_NO)) {
	exit(json_encode(array('code' => 1003, 'msg' => '排序号长度必须为3位，且都是数字')));
}

$IS_ORG = 0;
$G_DEPT = 0;

if ($DEPT_TYPE == "1") {
	$IS_ORG = 1;
}
else if ($DEPT_TYPE == "2") {
	$G_DEPT = 1;
}

$query = "SELECT DEPT_ID from DEPARTMENT where DEPT_PARENT='$DEPT_PARENT' and right(DEPT_NO,3)='$DEPT_NO' and DEPT_ID!='$DEPT_ID'";
$cursor = exequery(TD::conn(), $query);

if (0 < mysqli_num_rows($cursor)) {
	exit(json_encode(array('code' => 1004, 'msg' => "排序号 {$DEPT_NO} 已被使用")));
}
if ($G_DEPT == 1) {
}

if ($IS_ORG == 1) {
	$department_array = TD::get_cache("SYS_DEPARTMENT");
	$global_dept_str = "";

	foreach ($department_array as $k5 => $v5 ) {
		if (($v5["G_DEPT"] == "1") && !find_id($dept_is_org_str, $k5)) {
			$global_dept_str .= $k5 . ",";
		}
	}

	$global_dept_str = td_trim($global_dept_str);
	$tmp_str = get_dept_parent_all($DEPT_PARENT);
	$tmp_str = $tmp_str . $DEPT_PARENT;
	$tmp_str_arr = explode(",", $tmp_str);
	$global_dept_arr = explode(",", $global_dept_str);
	$return_str = array_intersect($global_dept_arr, $tmp_str_arr);

}

$query = "SELECT DEPT_ID,DEPT_NO,DINGDING_DEPT_ID,WEIXIN_DEPT_ID,QYWEIXIN_DEPT_ID from DEPARTMENT where DEPT_ID ='$DEPT_PARENT'";
$cursor = exequery(TD::conn(), $query);

if ($ROW = mysqli_fetch_array($cursor)) {
	$PARENT_DEPT_NO = $ROW["DEPT_NO"];
	$PARENT_DINGDING_DEPT_ID = $ROW["DINGDING_DEPT_ID"];
	$PARENT_WEIXIN_DEPT_ID = $ROW["WEIXIN_DEPT_ID"];
	$PARENT_QYWEIXIN_DEPT_ID = $ROW["QYWEIXIN_DEPT_ID"];
    $DEPT_PARENT = $ROW["DEPT_ID"];
}

$DEPT_NO = $PARENT_DEPT_NO . $DEPT_NO;
$email_audit_flag = check_email_audit(3);
$query = "insert into DEPARTMENT(DEPT_NAME,TEL_NO,FAX_NO,DEPT_ADDRESS,DEPT_NO,DEPT_PARENT,DEPT_FUNC,IS_ORG,G_DEPT";

if ($email_audit_flag) {
	$query .= ",DEPT_EMAIL_AUDITS_IDS";
}

$query .= ") values ('$DEPT_NAME','$TEL_NO','$FAX_NO','$DEPT_ADDRESS','$DEPT_NO','$DEPT_PARENT','$DEPT_FUNC','$IS_ORG','$G_DEPT'";

if ($email_audit_flag) {
	$query .= ",'$DEPT_EMAIL_AUDITS_IDS'";
}

$query .= ")";
exequery(TD::conn(), $query);
$DEPT_ID = mysqli_insert_id(TD::conn());
if ($DEPT_ID && ($dd_app_key != "") && ($dd_app_secret != "")) {
	$PARENT_DINGDING_DEPT_ID = ($PARENT_DINGDING_DEPT_ID ? $PARENT_DINGDING_DEPT_ID : 1);
	$sync_info = array("qy_type" => "dd,", "type" => "create_dept", "id" => $DEPT_ID, "name" => $DEPT_NAME, "parentid" => $PARENT_DINGDING_DEPT_ID, "order" => ltrim(substr($DEPT_NO, -3), 0));
	sync_oa2qy($sync_info);
}

if ($DEPT_ID && ($WEIXINQY_CORPID != "") && ($WEIXINQY_SECRET != "")) {
	$PARENT_WEIXIN_DEPT_ID = ($PARENT_WEIXIN_DEPT_ID ? $PARENT_WEIXIN_DEPT_ID : 1);
	$sync_info = array("qy_type" => "wx,", "type" => "create_dept", "id" => $DEPT_ID, "name" => $DEPT_NAME, "parentid" => $PARENT_WEIXIN_DEPT_ID, "order" => ltrim(substr($DEPT_NO, -3), 0));
	sync_oa2qy($sync_info);
}

if ($DEPT_ID && ($QYWEIXIN_CORPID != "") && ($app_secret != "")) {
	$PARENT_QYWEIXIN_DEPT_ID = ($PARENT_QYWEIXIN_DEPT_ID ? $PARENT_QYWEIXIN_DEPT_ID : 1);
	$sync_info = array("qy_type" => "qywx,", "type" => "create_dept", "id" => $DEPT_ID, "name" => $DEPT_NAME, "parentid" => $PARENT_QYWEIXIN_DEPT_ID, "order" => ltrim(substr($DEPT_NO, -3), 0));
	sync_oa2qy($sync_info);
}

set_sys_para(array("ORG_UPDATE" => date("Y-m-d H:i:s")));

if ($LOGIN_USE_DOMAIN == "1") {
	$value_str = "";
	$dn_array = explode("\n", str_replace("\r\n", "\n", $DN_STR));

	for ($i = 0; $i < count($dn_array); $i++) {
		if ($dn_array[$i] == "") {
			continue;
		}

		$guid = substr($dn_array[$i], 0, strpos($dn_array[$i], ","));
		$dn = substr($dn_array[$i], strpos($dn_array[$i], ",") + 1);
		$query = "SELECT DEPT_ID from DEPT_MAP where DEPT_GUID='" . $guid . "';";
		$cursor = exequery(TD::conn(), $query);

		if (0 < mysqli_num_rows($cursor)) {
			$query = "update DEPT_MAP set DEPT_ID='$DEPT_ID',DN='" . addslashes($dn) . "' where DEPT_GUID='" . $guid . "';";
			exequery(TD::conn(), $query);
		}
		else {
			$value_str .= "('" . $DEPT_ID . "', '" . $guid . "', '" . addslashes($dn) . "'),";
		}
	}
	$value_str = td_trim($value_str);
	if ($value_str != "") {
		$sql = "insert into DEPT_MAP (DEPT_ID, DEPT_GUID, DN) values " . $value_str;
		$cursor = exequery(TD::conn(), $sql);
	}
}

cache_department();

exit(json_encode(array('code' => 0, 'msg' => '成功', 'data' => array('dept_id' => $DEPT_ID))));

