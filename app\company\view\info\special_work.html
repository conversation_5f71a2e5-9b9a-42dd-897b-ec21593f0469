<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>特殊作业许可管理</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/company.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .el-form-item {margin-bottom:0;}
        .el-form--label-top .el-form-item__label { padding:0;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">
        <el-page-header @back="goBack" content="特殊作业许可管理"></el-page-header>
        <el-divider style="margin:10px 0;"></el-divider>
        <el-form :inline="true" class="form-inline" @submit.native.prevent>
<!--            <el-row>-->
<!--                <el-form-item style="float: left">-->
<!--                    <h2>作业许可管理</h2>-->
<!--                </el-form-item>-->
<!--            </el-row>-->
            <el-row>
                <el-form-item style="float: right">
                    <el-button :loading="loading" type="success" size="small" @click="add">新建</el-button>
                </el-form-item>
            </el-row>
        </el-form>
        <el-table border
                  v-loading="loading"
                  :data="data"
                  style="width: 100%;margin-bottom: 20px;"
                  ref="qtable"
                  :height="height"
                  size="small">
            <el-table-column
                    type="index"
                    label="序号"
                    align="center"
                    width="50">
            </el-table-column>
            <el-table-column
                    prop="work_item"
                    label="作业项目"
                    align="center"
                    min-width="240">
            </el-table-column>
            <el-table-column
                    prop="work_time"
                    label="作业时间段"
                    align="center"
                    show-overflow-tooltip
                    min-width="200">
                <template slot-scope="scope">
                    {{scope.row.work_time_start}} 至 {{scope.row.work_time_end}}
                </template>
            </el-table-column>
            <el-table-column
                    prop="work_place"
                    label="作业地点"
                    align="center"
                    min-width="150">
            </el-table-column>
            <el-table-column
                    prop="work_risk"
                    label="作业风险"
                    align="center"
                    min-width="150">
            </el-table-column>
            <el-table-column
                    prop="examin_user"
                    label="审批人"
                    align="center"
                    show-overflow-tooltip
                    min-width="150">
            </el-table-column>
            <el-table-column
                    prop="work_permit"
                    label="证书扫描件"
                    align="left"
                    min-width="200">
                <template slot-scope="scope">
                    <div v-for="item in scope.row.files">
                        <el-link type="primary" :href="item.url" target="_blank">{{item.name}}</el-link>
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                    label="操作"
                    align="center"
                    width="150">
                <template slot-scope="scope">
                    <el-button type="primary" @click="edit(scope.row)" size="small">编辑</el-button>
                    <el-button type="danger" @click="delFun(scope.row)" size="small">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <add ref="add" @ok="okFun()"></add>

    </div>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                data: [],
                loading: true,
                height: document.documentElement.clientHeight - 155,
            };
        },
        components: {
            'add': 'url:/general/toppingsoft/app/company/view/info/vue/addSpecialWork.vue?v=1.1',
        },
        methods: {
            goBack() {
                location.href = 'review';
            },
            okFun:function(){
                this.getData();
            },
            add: function () {
                this.$refs.add.title = '作业许可新增';
                this.$refs.add.open();
            },
            edit: function (row) {
                this.$refs.add.title = '作业许可编辑';
                var rowData = JSON.parse(JSON.stringify(row));
                this.$refs.add.open(rowData);
            },
            delFun: function (row) {
                var _this = this;

                this.$confirm('此操作将删除该条数据, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    var param = {id: row.id};
                    axios.post('specialWorkDelete', param).then(function (res) {
                        if (res.data.code == 0) {
                            _this.$message({
                                type: 'success',
                                message: '删除成功!'
                            });
                            _this.getData();
                        } else {
                            _this.$message({
                                message: res.data.msg,
                                type: "error"
                            });
                            return false;
                        }
                    }).catch(function (error) {
                        console.log(error);
                    });

                }).catch(() => {
                    console.log(已取消删除)
                });
            },
            //数据加载
            getData() {
                var _this = this;
                var param = {};
                param._ajax = 1;
                axios.post('', param).then(function (res) {
                    if (res.data.code == 0) {
                        _this.data = res.data.data;
                        _this.$nextTick(() => {
                            // 这里的代码会在DOM更新完成后执行
                            _this.loading = false;
                        });
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
        },
        mounted() {
            //获取列表
            this.getData();
        }
    })
</script>


</body>
</html>