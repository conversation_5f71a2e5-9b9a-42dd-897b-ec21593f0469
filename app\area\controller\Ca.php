<?php
declare (strict_types = 1);

namespace app\area\controller;

use app\admin\model\CompanyModel;
use app\model\MessageModel;
use hg\apidoc\annotation as Apidoc;
use think\facade\Db;
use think\facade\View;
use think\Request;
use app\model\ExcelModel;

class Ca extends Base
{

    /**
     * @Apidoc\Title("证书列表")
     * @Apidoc\Desc("证书列表")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function index($limit = 20, $excel = 0)
    {
        if (request()->isAjax()) {
            $title = $this->request->param('title','','trim');
            $status = $this->request->param('status','','trim');
            $where = [
                ['b.area_id','=',$_SESSION['area']['id']],
                ['b.dept_id','in',$_SESSION['area']['manager']],
            ];
            if(!empty($status)){
                $where[] = ['a.status','=',$status];
            }
            if(!empty($title)){
                $where[] = ['a.company_name|a.code','like',"%{$title}%"];
            }
            $fields = "a.*";
            $res = Db::table('top_certificate')->alias('a')
                ->leftJoin('top_company_info b','a.company_id = b.id')
                ->where($where)->field($fields)->order('a.start desc');
             if ($excel == 1) {
                 set_time_limit(0);
                 $mbStatus = [
                     1 => '未生效',
                     7 => '生效中',
                     8 => '已过期',
                     9 => '已撤销',
                 ];
                 $data = $res->select()->each(function ($item, $key) use ($mbStatus) {
                         $item = CompanyModel::codeToText($item);
                         $item['mb_status'] =  $mbStatus[$item['status']];
                         return $item;
                     })->toArray();
                 $header = [
                     ['title' => '企业名称', 'field' => 'company_name', 'width' => '30'],
                     ['title' => '证书编号', 'field' => 'code', 'width' => '30'],
                     ['title' => '状态', 'field' => 'mb_status', 'width' => '30'],
                     ['title' => '生效日期', 'field' => 'start', 'width' => '30'],
                     ['title' => '有效期', 'field' => 'ends', 'width' => '30'],
                 ];
                 $filename = '证书列表_' . date('YmdHis');
                 ExcelModel::exportExcel( $header,$data, $filename,true);
             }else{
               $res = $res->paginate($limit)->each(function ($item, $key) {
                    if($item['status']==7){
                        $item['status'] = strtotime($item['ends'])>strtotime(date('Y-m-d'))?7:8;
                    }
                    return $item;
                });
                result($res);
            }
        } else {
            View::assign('title', '首页');
            return view();
        }
    }

    public function revoke($id=0,$remark=''){
        $re = Db::table('top_certificate')->where('id','=',$id)->find();
        if(empty($re)||$re['status']!=7){
            result('',1001,'证书不存在或未生效');
        }
        $data = [
            'status' => 9,
        ];
        Db::table('top_certificate')->where(['id'=>$id])->update($data);
        $company = Db::table('top_company_info')->where(['id'=>$re['company_id']])->field('user_id')->find();
        MessageModel::sendSms("company",$company['user_id'],'证书撤销',"您的编号为“{$re['code']}”的证书已被撤销，撤销原因：{$remark}！");
        result('',0,'撤销完成');
    }


}
