<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>统计大屏</title>
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/screen/style.css">
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/plugs/element/axios.min.js"></script>
    <script src="__PUBLIC__/static/js/request.js"></script>
    <script src="__PUBLIC__/plugs/echarts/echarts.min.js"></script>
    <script src="https://webapi.amap.com/loader.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .el-table .danger-row {
            background: #fbc4c4;
        }
        .el-table .warning-row {
            background: oldlace;
        }
        .el-table .success-row {
            background: #f0f9eb;
        }
        .el-badge__content.is-fixed { right: 20px;z-index: 9;}
    </style>
</head>
<body><div id="app">
    <div id="map" style="height: 100%;width: 100%;position: absolute;left: 0;right: 0;"></div>
    <img src="__PUBLIC__/screen/image/shangMask.png" alt="" class="shangMaskImg">
    <img src="__PUBLIC__/screen/image/zuoMask.png" alt="" class="zuoMaskImg">
    <img src="__PUBLIC__/screen/image/youMask.png" alt="" class="youMaskImg">
    <nav>
        <img src="__PUBLIC__/screen/image/topHeaderPage.png" alt="" class="topHeaderPage">
        <div>
            企业安全生产标准化信息管理系统
        </div>
        <img src="__PUBLIC__/screen/image/logo.png" alt="" class="logoImg">
        <div class="navRightBox">
            <div>{{currentTime}}</div>
            <div class="loginOutBtnBox">
                <img src="__PUBLIC__/screen/image/loginOutIcon.png" alt="">
                <span>退出</span>
            </div>
        </div>
    </nav>
    <div class="datePickerBox">
        <el-date-picker
                v-model="year"
                type="year"
                size="mini"
                placeholder="选择年">
        </el-date-picker>
    </div>
    <section class="section2">

        <div class="section2LeftBox">
            <!--总体概览-->
            <div class="sectionBox">
                <div class="sectionBoxTitle">
                    <img src="__PUBLIC__/screen/image/biaotikuang.png" alt="" class="biaotikuang">
                    <div>总体概览</div>
                </div>
                <div class="sectionBoxBody" style="margin-bottom: 20px;">
                    <div class="sectionBoxBody1">
                        <el-row>
                            <el-col :span="8">
                                <div class="section2Item">
                                    <img src="__PUBLIC__/screen/image/zkqy.png" alt="">
                                    <div>
                                        <div class="section2ItemContent section2ItemContent2">
                                            <span v-text="overviewData.total_company_count"></span>
                                            <span class="section2ItemContentUnit section2ItemContent2">家</span>
                                        </div>
                                        <div class="section2ItemTitle">在库企业数</div>
                                    </div>
                                </div>
                            </el-col>
                            <el-col :span="8">
                                <div class="section2Item">
                                    <img src="__PUBLIC__/screen/image/dbqy.png" alt="">
                                    <div>
                                        <div class="section2ItemContent section2ItemContent2">
                                            <span v-text="overviewData.standard_company_count"></span>
                                            <span class="section2ItemContentUnit">家</span>
                                        </div>
                                        <div class="section2ItemTitle">达标企业数</div>
                                    </div>
                                </div>
                            </el-col>
                            <el-col :span="8">
                                <div class="section2Item">
                                    <img src="__PUBLIC__/screen/image/dbyxqy.png" alt="">
                                    <div>
                                        <div class="section2ItemContent section2ItemContent2">
                                            <span v-text="overviewData.valid_standard_company_count"></span>
                                            <span class="section2ItemContentUnit">家</span>
                                        </div>
                                        <div class="section2ItemTitle">达标有效企业数</div>
                                    </div>
                                </div>
                            </el-col>
                        </el-row>
                        <el-row style="margin-top: 10px;">
                            <el-col :span="8">
                                <div class="section2Item">
                                    <img src="__PUBLIC__/screen/image/yi.png" alt="">
                                    <div>
                                        <div class="section2ItemContent">
                                            <span v-text="overviewData.pass_rate"></span>
                                            <span class="section2ItemContentUnit">%</span>
                                        </div>
                                        <div class="section2ItemTitle">总体通过率</div>
                                    </div>
                                </div>
                            </el-col>
                            <el-col :span="8">
                                <div class="section2Item">
                                    <img src="__PUBLIC__/screen/image/wei.png" alt="">
                                    <div>
                                        <div class="section2ItemContent section2ItemContent3">
                                            <span v-text="overviewData.not_pass_count"></span>
                                            <span class="section2ItemContentUnit">家</span>
                                        </div>
                                        <div class="section2ItemTitle">未通过数</div>
                                    </div>
                                </div>
                            </el-col>
                            <el-col :span="8">
                                <div class="section2Item">
                                    <img src="__PUBLIC__/screen/image/zttgl.png" alt="">
                                    <div>
                                        <div class="section2ItemContent section2ItemContent2">
                                            <span v-text="overviewData.pass_count"></span>
                                            <span class="section2ItemContentUnit">家</span>
                                        </div>
                                        <div class="section2ItemTitle">通过数</div>
                                    </div>
                                </div>
                            </el-col>
                        </el-row>
                    </div>
                </div>
            </div>
            <!--分行业概览-->
            <div class="sectionBox">
                <div class="sectionBoxTitle">
                    <img src="__PUBLIC__/screen/image/biaotikuang.png" alt="" class="biaotikuang">
                    <div>分行业概览</div>
                </div>
                <div class="sectionBoxBody">
                    <div style="width: 100%;height: 18vh;" id="chart1"></div>
                </div>
            </div>
            <!--各区(市)县概览-->
            <div class="sectionBox">
                <div class="sectionBoxTitle">
                    <img src="__PUBLIC__/screen/image/biaotikuang.png" alt="" class="biaotikuang">
                    <div>各区(市)县概览</div>
                    <div class="selectBox">
                        <el-select v-model="value" placeholder="请选择" size="mini" @change="getAreaData">
                            <el-option
                                    v-for="item in areaOptions"
                                    :key="item.code"
                                    :label="item.name"
                                    :value="item.code">
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <div class="sectionBoxBody">
                    <div class="areaBox">
                        <!--<img src="__PUBLIC__/screen/image/areaTitleBg.png" alt="">-->
                        <img src="__PUBLIC__/screen/image/areaBodyBg.png" alt="" class="areaBoxBg">
                        <div class="areaTitleBox">
                            <img src="__PUBLIC__/screen/image/areaTitleBg.png" alt="" class="areaTitleBg">
                            <div v-text="areaData.area_name"></div>
                        </div>
                        <div class="areaBodyBox">
                            <div class="areaBodyListBox">
                                <div class="areaBodyItem">
                                    <div class="section2ItemContent section2ItemContent2">
                                        <span v-text="areaData.total_company_count"></span>
                                        <span class="section2ItemContentUnit">家</span>
                                    </div>
                                    <div class="section2ItemTitle">企业总数</div>
                                </div>
                                <div class="areaBodyItem">
                                    <div class="section2ItemContent section2ItemContent2">
                                        <span v-text="areaData.standard_company_count"></span>
                                        <span class="section2ItemContentUnit">家</span>
                                    </div>
                                    <div class="section2ItemTitle">达标企业</div>
                                </div>
                                <div class="areaBodyItem">
                                    <div class="section2ItemContent section2ItemContent2">
                                        <span v-text="areaData.review_task_count"></span>
                                        <span class="section2ItemContentUnit">次</span>
                                    </div>
                                    <div class="section2ItemTitle">评审任务数</div>
                                </div>
                                <div class="areaBodyItem">
                                    <div class="section2ItemContent section2ItemContent2">
                                        <span v-text="areaData.review_timeliness_rate"></span>
                                        <span class="section2ItemContentUnit">%</span>
                                    </div>
                                    <div class="section2ItemTitle">评审及时率</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--高分评审企业TOP三-->
            <div class="sectionBox">
                <div class="sectionBoxTitle">
                    <img src="__PUBLIC__/screen/image/biaotikuang.png" alt="" class="biaotikuang">
                    <div>高分评审企业TOP三</div>
                </div>
                <div class="sectionBoxBody">
                    <div class="topThreeRadioBox">
                        <el-radio-group v-model="radio1" size="mini">
                            <el-radio-button :label="1">工贸</el-radio-button>
                            <el-radio-button :label="2">危化</el-radio-button>
                            <el-radio-button :label="3">商业商务楼宇</el-radio-button>
                        </el-radio-group>
                    </div>
                    <div class="topThreeTableBox">
                        <div class="topThreeTableHeader">
                            <div class="tableTh1 tableTr1"></div>
                            <div class="tableTh2 tableTr2">公司名称</div>
                            <div class="tableTh3 tableTr3">评审得分</div>
                            <div class="tableTh4 tableTr4">行业</div>
                        </div>
                        <div class="topThreeTableBody" v-for="(item,index) in topThree" :key="index">
                            <div class="tableTr1">
                                <div :class="'no'+(index+1)+'Fonts'">NO.{{index+1}}</div>
                            </div>
                            <div class="tableTr2">
                                <div class="companyFontTd">
                                    {{item.company_name}}
                                </div>
                            </div>
                            <div class="tableTr3">{{item.score}}</div>
                            <div class="tableTr4">{{item.industry}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="section2CenterBox">
            <div class="companySearchBox">
                <div class="companySearchTitle">企业筛选</div>
                <div>
                    <el-form ref="form" :model="form" label-width="80px">
                        <el-form-item label="行业类别">
                            <el-select v-model="form.region" placeholder="请选择" size="mini" style="width:160px;" >
                                <el-option
                                        v-for="item in industryOptions"
                                        :key="item.code"
                                        :label="item.name"
                                        :value="item.name">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <!--                        <el-form-item label="重点领域">-->
                        <!--                            <el-checkbox v-model="form.checked">包含</el-checkbox>-->
                        <!--                        </el-form-item>-->
                        <el-form-item style="text-align: right;padding-bottom: 5px;">
                            <el-button type="primary" @click="onSubmit" size="mini">查询</el-button>
                            <el-button size="mini" style="margin-right: 5px;">重置</el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </div>
        </div>

        <div class="section2RightBox" >
            <div class="sectionBox">
                <div class="sectionBoxTitle">
                    <img src="__PUBLIC__/screen/image/biaotikuang.png" alt="" class="biaotikuang">
                    <div>
                        重点领域监管
                    </div>
                </div>
                <div class="sectionBoxBody">
                    <div class="sectionBoxBody1">
                        <div class="importantHeaderBox">
                            <img src="__PUBLIC__/screen/image/totalIcon.png" alt="">
                            <span>有限空间</span>
                        </div>
                        <div class="importantBodyBox">
                            <div id="chart2" style="height: 11vh;width: 100%;"></div>
                        </div>
                    </div>
                    <div class="sectionBoxBody1">
                        <div class="importantHeaderBox">
                            <img src="__PUBLIC__/screen/image/totalIcon.png" alt="">
                            <span>粉尘涉爆</span>
                        </div>
                        <div class="importantBodyBox">
                            <div id="chart3" style="height: 10vh;width: 100%;"></div>
                            <div id="chart4" style="height: 10vh;width: 100%;"></div>
                        </div>
                    </div>
                    <div class="sectionBoxBody1">
                        <div class="importantHeaderBox">
                            <img src="__PUBLIC__/screen/image/totalIcon.png" alt="">
                            <span>液氨制冷</span>
                        </div>
                        <div class="importantBodyBox">
                            <div class="importantItemBox">
                                <div class="importantItem1Box">
                                    <div>[液氨各用途使用量]</div>
                                    <div class="importantItem1BodyBox">
                                        <div class="importantItem1BodyLabel">制气：</div>
                                        <div class="importantItem1BodyNum">999 t/a</div>
                                    </div>
                                    <div class="importantItem1BodyBox">
                                        <div class="importantItem1BodyLabel">调节PH值：</div>
                                        <div class="importantItem1BodyNum">399 t/a</div>
                                    </div>
                                </div>
                                <div class="importantItem2Box">
                                    <div>[液氨存储量]</div>
                                    <div id="chart5" style="height: 10vh;width: 100%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="sectionBoxBody1">
                    <div class="importantHeaderBox">
                        <img src="__PUBLIC__/screen/image/totalIcon.png" alt="">
                        <span>涉高温熔融金属</span>
                    </div>
                    <div class="importantBodyBox">
                        <div class="metalBox">
                            <div class="metalHeaderBox">冶炼炉</div>
                            <div class="metalBodyBox">
                                <div class="metalItemBox">
                                    <div class="metalItemNumber">500</div>
                                    <div>高炉数量</div>
                                </div>
                                <div class="metalItemBox">
                                    <div class="metalItemNumber">800</div>
                                    <div>有色金属冶炼</div>
                                </div>
                                <div class="metalItemBox">
                                    <div class="metalItemNumber">400</div>
                                    <div>铁合金矿热炉</div>
                                </div>
                                <div class="metalItemBox">
                                    <div class="metalItemNumber">300</div>
                                    <div>冲天炉数量</div>
                                </div>
                                <div class="metalItemBox">
                                    <div class="metalItemNumber">0</div>
                                    <div>其他数量</div>
                                </div>
                            </div>
                        </div>
                        <div class="metalBox">
                            <div class="metalHeaderBox">熔炼炉</div>
                            <div id="chart6" style="height: 11vh;width: 100%;margin-top: 5px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </section>
    <section class="section3">
        <div class="sectionBox">
            <div class="sectionBoxTitle">
                <img src="__PUBLIC__/screen/image/biaotikuang2.png" alt="" class="biaotikuang">
                <div class="sectionBoxTitleImg">
                    <img src="__PUBLIC__/screen/image/icon1.png" alt="">
                    评审实时情况
                </div>
            </div>
            <div class="sectionBoxBody">
                <el-table
                        :data="tableData"
                        stripe
                        @row-click="tableRowClick"
                        height="220"
                        style="width: 100%;margin-top: 10px;">
                    <el-table-column
                            prop="name"
                            align="center"
                            show-overflow-tooltip
                            label="评审企业">
                    </el-table-column>
                    <el-table-column
                            prop="pca_name"
                            align="center"
                            show-overflow-tooltip
                            label="所属区域">
                    </el-table-column>
                    <el-table-column-->
                        prop="org_name"
                        align="center"
                        show-overflow-tooltip
                        label="评审机构">
                        </el-table-column>
                        <el-table-column
                                prop="thedate"
                                align="center"
                                show-overflow-tooltip
                                label="评审日期">
                        </el-table-column>
                        <el-table-column
                                prop="experts"
                                align="center"
                                show-overflow-tooltip
                                label="评审专家组">
                        </el-table-column>
                        <el-table-column
                                prop="status_name"
                                align="center"
                                show-overflow-tooltip
                                width="150"
                                label="评审进度">
                        </el-table-column>
                </el-table>
            </div>
        </div>
    </section>
    <card ref="cardBox"></card>
    <!--<tablelist></tablelist>-->
</div>
<script>
    window._AMapSecurityConfig = {
        securityJsCode: "a6d0efbc78ad832c76c46bfaf224f06c",
    };
    Vue.use(httpVueLoader);
    new Vue({
        el: '#app',
        data: function () {
            return {
                value: '510104',
                tab1:1,
                tab2:1,
                tableData: [],
                map:'',
                ip:"https://bzh.cdsafety.org.cn/general/toppingsoft/index.php",
                url:{
                    getData:'/index/getData'
                },
                pointsData :{$pointData|raw},
                industryOptions: {$industry|raw},
            year: new Date(),
                currentTime: '',
                timer: null,
                radio1:'工贸',
                form: {
                region: {$industry|raw},
                checked: false,
            },
            overviewData: {
                total_company_count: 0,
                    standard_company_count: 0,
                    valid_standard_company_count: 0,
                    pass_rate: 0,
                    not_pass_count: 0,
                    pass_count: 0,
            },
            topThree: [],
                areaData: [],
                areaOptions: [],
                keyAreaData: [],
                reviewStatus: [],
        }
        },
        components:{
            // 'card':'url:__PUBLIC__/screen/card.vue?v=1.0',
            // 'tablelist':'url:__PUBLIC__/screen/table.vue?v=1.0',
        },
        mounted(){
            var that=this,marker;
            // 初始化立即显示
            this.updateTime();
            // 设置定时器每秒更新
            this.timer = setInterval(() => {
                this.updateTime();
            }, 1000);

            this.getOverviewData(); //获取总体概括
            this.getIndustryData();
            this.getTopThreeCompanies();
            this.getReviewStatus();
            //this.getAreaData();
            this.reloadMapData({$pointData|raw});
            this.chart2();
            this.chart3();
            this.chart4();
            this.chart5();
            this.chart6();
            this.year = new Date();
        },
        watch: {
            year(val) {
                this.getOverviewData();
                this.getIndustryData();
                this.getAreaData();
                this.getTopThreeCompanies();
                //this.getKeyAreaData();
                this.getReviewStatus();
            },
            radio1(val) {
                this.getTopThreeCompanies();
            }
        },
        beforeDestroy() {
            // 组件销毁时清除定时器
            clearInterval(this.timer);
        },
        methods:{
            reloadMapData(pointsData){
                var that = this;
                // 地图初始化
                AMapLoader.load({
                    key: "605605cbe2e0ac070398f77a1f356783", //申请好的Web端开发者 Key，调用 load 时必填
                    version: "2.0", //指定要加载的 JS API 的版本，缺省时默认为 1.4.15
                })
                    .then((AMap) => {
                        that.map = new AMap.Map("map",{
                            mapStyle: "amap://styles/darkblue", //设置地图的显示样式
                            zoom: 10,
                            center: [104.066301,30.572961],
                        });
                        pointsData.forEach(item=>{
                            /*添加标记点*/
                            marker = new AMap.Marker({
                                icon: "__PUBLIC__/screen/image/mapicon.png",
                                position: [item.lng,item.lat],
                                offset: new AMap.Pixel(-13, -30),
                                title:item.title,

                            });
                            marker.data={...item}
                            marker.on('click',function (res) {
                                console.log(res.target.data);
                                that.$refs.cardBox.windowsShow=true;
                            })
                            marker.setMap(that.map);
                        })

                    })
                    .catch((e) => {
                        console.error(e); //加载错误提示
                    });
            },
            getOverviewData() {
                let year = this.year.getFullYear();
                Person1(`getOverviewData?year=${year}`).then(res => {
                    this.overviewData = res.data;
                })
            },
            getIndustryData() {
                let year = this.year.getFullYear();
                Person1(`getIndustryData?year=${year}`).then(res => {
                    this.initChart1(res.data);
                })
            },
            getAreaData() {
                let year = this.year.getFullYear();
                Person1(`getAreaData?year=${year}&area=${this.value}`).then(res => {
                    this.areaData = res.data.area_data;
                    this.areaOptions = res.data.area_options;
                })
            },
            getTopThreeCompanies() {
                let year = this.year.getFullYear();
                Person1(`getTopThreeCompanies?year=${year}&industry=${this.radio1}`).then(res => {
                    this.topThree = res.data;
                })
            },
            getKeyAreaData() {
                let year = this.year.getFullYear();
                Person1(`getKeyAreaData?year=${year}`).then(res => {
                    this.keyAreaData = res.data;
                })
            },
            getReviewStatus() {
                let year = this.year.getFullYear();
                Person1(`getReviewStatus?year=${year}`).then(res => {
                    this.tableData = res.data;
                })
            },

            initChart1(data){
                let that=this;
                that.$nextTick(() => {
                    let myChart = echarts.init(document.getElementById('chart1'));
                    myChart.setOption({

                        tooltip: {
                            trigger: "axis",
                            textStyle: {
                                fontSize: 12,
                                fontFamily:'AlRegular',
                                color:'#fff',
                            },
                            backgroundColor: 'rgba(50,50,50,0.9)',
                            borderWidth: 0,
                        },
                        grid: {
                            left: '10%',
                            top: '20%',
                            bottom: '20%',
                            right: '10%',
                        },
                        legend: {
                            data: data.legend,
                            top: "2%",
                            textStyle: {
                                fontSize: 12,
                                color:'#fff',
                                fontFamily:'AlRegular'
                            },
                            // itemHeight: 14,
                            // itemWidth: 14,
                        },
                        xAxis: {
                            data: data.xAxis,
                            axisLine: {
                                show: true, //隐藏X轴轴线
                                lineStyle: {
                                    color: 'rgba(48, 98, 105, 1)',
                                    width: 1
                                }
                            },
                            axisTick: {
                                show: true //隐藏X轴刻度
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 12,
                                    fontFamily:'AlRegular'
                                },
                                rotate: 0,//倾斜度
                            },
                        },
                        yAxis: [{
                            type: "value",
                            /*name: "亿元",*/
                            nameTextStyle: {
                                fontSize: 12,
                                color:'#fff',
                                fontFamily:'AlRegular'
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: "rgba(48, 98, 105, 0.2)",
                                    width:1,
                                    type: 'dashed'//虚线
                                }
                            },
                            axisTick: {
                                show: false
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: 'rgba(48, 98, 105, 1)',
                                    width: 1
                                }
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    fontSize: 12,
                                    color:'#fff',
                                    fontFamily:'AlRegular'
                                }
                            },

                        },
                            {
                                type: "value",
                                /*name: "同比",*/
                                nameTextStyle: {
                                    fontSize: 12,
                                    color:'#fff',
                                    fontFamily:'AlRegular'
                                },
                                position: "right",
                                splitLine: {
                                    show: false
                                },
                                axisTick: {
                                    show: false
                                },
                                axisLine: {
                                    show: false,
                                },
                                axisLabel: {
                                    show: true,
                                    formatter: "{value} %", //右侧Y轴文字显示
                                    textStyle: {
                                        fontSize: 12,
                                        color:'#fff',
                                        fontFamily:'AlRegular'
                                    }
                                }
                            }
                        ],
                        series: data.series
                    })
                })
            },
            tableRowClick(val,row){
                console.log(row)
                console.log(val)
                // this.$refs.fourlevelBox.initialization();
            },
            //右上角日期
            formatTime() {
                const date = new Date();
                const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];

                // 日期部分
                const year = date.getFullYear();
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const day = date.getDate().toString().padStart(2, '0');

                // 时间部分
                const hours = date.getHours().toString().padStart(2, '0');
                const minutes = date.getMinutes().toString().padStart(2, '0');
                const seconds = date.getSeconds().toString().padStart(2, '0');

                // 星期获取
                const weekDay = weekDays[date.getDay()];

                return `${year}.${month}.${day}  /  ${weekDay}  /  ${hours}:${minutes}:${seconds}`;
            },

            // 更新时间
            updateTime() {
                this.currentTime = this.formatTime();
            },
            //根据行业类型来加载企业的描点
            onSubmit() {
                let that = this;
                let year = this.year.getFullYear();
                let industry = this.form.region;
                let keyArea = this.form.checked ? 1 : 0; // 假设后端通过 1/0 表示是否包含重点领域

                // 请求后端接口，根据筛选条件获取数据
                Person1(`filterCompanies?year=${year}&industry=${industry}&keyArea=${keyArea}`)
                    .then(res => {
                        // 更新相关数据
                        if (res.code === 0) {
                            // 假设 res.data 包含需要更新的图表数据和表格数据
                            that.reloadMapData(res.data);
                            // 提示用户筛选成功
                            // this.$message({
                            //     message: '筛选成功',
                            //     type: 'success'
                            // });
                        } else {
                            // 提示错误信息
                            this.$message.error('筛选失败，请重试');
                        }
                    })
                    .catch(err => {
                        console.error('请求失败:', err);
                        this.$message.error('网络异常，请检查连接');
                    });
            },
            chart2(){
                let that=this;
                that.$nextTick(() => {
                    let myChart = echarts.init(document.getElementById('chart2'));
                    myChart.setOption({

                        tooltip: {
                            trigger: "axis",
                            textStyle: {
                                fontSize: 12,
                                fontFamily:'AlRegular',
                                color:'#fff',
                            },
                            backgroundColor: 'rgba(50,50,50,0.9)',
                            borderWidth: 0,
                        },
                        grid: {
                            left: '10%',
                            top: '10%',
                            bottom: '20%',
                            right: '10%',
                        },
                        // legend: {
                        //     data: ["达标企业", "评审数","通过数","总体通过率"],
                        //     top: "2%",
                        //     textStyle: {
                        //         fontSize: 12,
                        //         color:'#fff',
                        //         fontFamily:'AlRegular'
                        //     },
                        //     // itemHeight: 14,
                        //     // itemWidth: 14,
                        // },
                        xAxis: {
                            data:{$limitSpaceData_name|raw},
                            axisLine: {
                                show: true, //隐藏X轴轴线
                                lineStyle: {
                                    color: 'rgba(48, 98, 105, 1)',
                                    width: 1
                                }
                            },
                            axisTick: {
                                show: false //隐藏X轴刻度
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 12,
                                    fontFamily:'AlRegular'
                                },
                                rotate: 0,//倾斜度
                            },
                        },
                        yAxis: [{
                            type: "value",
                            /*name: "亿元",*/
                            nameTextStyle: {
                                fontSize: 12,
                                color:'#fff',
                                fontFamily:'AlRegular'
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: "rgba(48, 98, 105, 0.2)",
                                    width:1,
                                    type: 'dashed'//虚线
                                }
                            },
                            axisTick: {
                                show: false
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: '#768293',
                                    width: 0
                                }
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    fontSize: 12,
                                    color:'#fff',
                                    fontFamily:'AlRegular'
                                }
                            },

                        },
                            {
                                type: "value",
                                /*name: "同比",*/
                                nameTextStyle: {
                                    fontSize: 12,
                                    color:'#fff',
                                    fontFamily:'AlRegular'
                                },
                                position: "right",
                                splitLine: {
                                    show: false
                                },
                                axisTick: {
                                    show: false
                                },
                                axisLine: {
                                    show: false,
                                },
                                axisLabel: {
                                    show: true,
                                    formatter: "{value} %", //右侧Y轴文字显示
                                    textStyle: {
                                        fontSize: 12,
                                        color:'#fff',
                                        fontFamily:'AlRegular'
                                    }
                                }
                            }
                        ],
                        series: [
                            {
                                name: "有限空间",
                                type: "bar",
                                barWidth: 14,
                                itemStyle: {
                                    normal: {
                                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                            offset: 0,
                                            color: '#0895E0'
                                        }, {
                                            offset: 1,
                                            color: '#2CDEDD'
                                        }]),
                                        borderColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                            offset: 0,
                                            color: '#0895E0'
                                        }, {
                                            offset: 1,
                                            color: '#2CDEDD'
                                        }]),
                                        borderWidth: 1
                                    }
                                },

                                data: {$limitSpaceData_count|raw}
                            }
                        ]
                    })
                })
            },
            chart3(){
                let that=this;
                that.$nextTick(() => {
                    let myChart = echarts.init(document.getElementById('chart3'));
                    var legends = {$allDustIndestryType|raw};
                    var colors = ['#C9130E', '#2CAFF9', '#FF9C5D', '#068888', '#FFD15C', '#8A00E1','#31FFFF','#0027A7'].reverse();
                    var data = {$allDustIndestryTypeData|raw};
                    myChart.setOption({
                        color: colors,
                        legend: {
                            orient: 'vertical',
                            top: "center",
                            right: "5%",
                            itemGap: 10,
                            itemWidth: 5,
                            itemHeight: 5,
                            textStyle: {
                                color: "#fff",
                                fontSize: 12
                            },
                            data: legends,
                        },
                        tooltip: {
                            trigger: 'item',
                            formatter: '{a} <br/>{b} : {c} ({d}%)',
                            textStyle: {
                                fontSize: 12,
                                fontFamily:'AlRegular',
                                color:'#fff',
                            },
                            backgroundColor: 'rgba(50,50,50,0.9)',
                            borderWidth: 0,
                        },
                        series: [{
                            name: '粉尘涉爆',
                            type: 'pie',
                            radius: ['30%', '90%'],
                            center: ['25%', '50%'],
                            roseType: 'radius',
                            minShowLabelAngle: 60,
                            label: {
                                show: false,
                            },
                            data: data
                        }]
                    })
                })
            },
            chart4(){
                let that=this;
                that.$nextTick(() => {
                    let myChart = echarts.init(document.getElementById('chart4'));
                    myChart.setOption({

                        tooltip: {
                            trigger: "axis",
                            textStyle: {
                                fontSize: 12,
                                fontFamily:'AlRegular',
                                color:'#fff',
                            },
                            backgroundColor: 'rgba(50,50,50,0.9)',
                            borderWidth: 0,
                        },
                        grid: {
                            left: '10%',
                            top: '5%',
                            bottom: '30%',
                            right: '10%',
                        },
                        // legend: {
                        //     data: ["达标企业", "评审数","通过数","总体通过率"],
                        //     top: "2%",
                        //     textStyle: {
                        //         fontSize: 12,
                        //         color:'#fff',
                        //         fontFamily:'AlRegular'
                        //     },
                        //     // itemHeight: 14,
                        //     // itemWidth: 14,
                        // },
                        xAxis: {
                            data: {$dustTypeData_name|raw},
                            axisLine: {
                                show: true, //隐藏X轴轴线
                                lineStyle: {
                                    color: 'rgba(48, 98, 105, 1)',
                                    width: 1
                                }
                            },
                            axisTick: {
                                show: false //隐藏X轴刻度
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 12,
                                    fontFamily:'AlRegular'
                                },
                                rotate: 0,//倾斜度
                            },
                        },
                        yAxis: [{
                            type: "value",
                            /*name: "亿元",*/
                            nameTextStyle: {
                                fontSize: 12,
                                color:'#fff',
                                fontFamily:'AlRegular'
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: "rgba(48, 98, 105, 0.2)",
                                    width:1,
                                    type: 'dashed'//虚线
                                }
                            },
                            axisTick: {
                                show: false
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: '#768293',
                                    width: 0
                                }
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    fontSize: 12,
                                    color:'#fff',
                                    fontFamily:'AlRegular'
                                }
                            },

                        },
                            {
                                type: "value",
                                /*name: "同比",*/
                                nameTextStyle: {
                                    fontSize: 12,
                                    color:'#fff',
                                    fontFamily:'AlRegular'
                                },
                                position: "right",
                                splitLine: {
                                    show: false
                                },
                                axisTick: {
                                    show: false
                                },
                                axisLine: {
                                    show: false,
                                },
                                axisLabel: {
                                    show: true,
                                    formatter: "{value} %", //右侧Y轴文字显示
                                    textStyle: {
                                        fontSize: 12,
                                        color:'#fff',
                                        fontFamily:'AlRegular'
                                    }
                                }
                            }
                        ],
                        series: [
                            {
                                name: "粉尘涉爆",
                                type: "bar",
                                barWidth: 14,
                                itemStyle: {
                                    normal: {
                                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                            offset: 0,
                                            color: '#0245FD'
                                        }, {
                                            offset: 1,
                                            color: '#1E5AE2'
                                        }]),
                                        borderColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                            offset: 0,
                                            color: '#0245FD'
                                        }, {
                                            offset: 1,
                                            color: '#1E5AE2'
                                        }]),
                                        borderWidth: 1
                                    }
                                },
                                data: {$dustTypeData_count|raw}
                            }
                        ]
                    })
                })
            },
            chart5(){
                let that=this;
                that.$nextTick(() => {
                    let myChart = echarts.init(document.getElementById('chart5'));
                    const optionsData = [
                        {
                            name: '罐装',
                            value: 13,
                            itemStyle: {
                                color: '#00C1F8',
                                // opacity: 1,
                            },
                        },
                        {
                            name: '瓶装',
                            value: 21,
                            itemStyle: {
                                color: '#7AF6B0',
                                // opacity: 1,
                            },
                        }
                    ];

                    const series = that.getPie3D(optionsData, 0.8, 240, 28, 26, 0.5);

                    series.push({
                        name: 'pie2d',
                        type: 'pie',
                        label: {
                            opacity: 1,
                            fontSize: 13,
                            lineHeight: 20,
                            color:'#fff',
                        },
                        labelLine: {
                            length: 15,
                            length2: 30,
                        },
                        startAngle: -30, //起始角度，支持范围[0, 360]。
                        clockwise: false, //饼图的扇区是否是顺时针排布。上述这两项配置主要是为了对齐3d的样式
                        radius: ['20%', '50%'],
                        center: ['50%', '50%'],
                        data: optionsData,
                        itemStyle: {
                            opacity: 0,
                        },
                    });
                    myChart.setOption({
                        tooltip: {
                            formatter: (params) => {
                                if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {
                                    let bfb = (
                                        (option.series[params.seriesIndex].pieData.endRatio -
                                            option.series[params.seriesIndex].pieData.startRatio) *
                                        100
                                    ).toFixed(2);
                                    return (
                                        `${params.seriesName}<br/>` +
                                        `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${params.color};"></span>` +
                                        `${bfb}%`
                                    );
                                }
                            },
                        },
                        labelLine: {
                            show: true,
                            lineStyle: {
                                color: '#7BC0CB',
                            },
                        },
                        label: {
                            show: true,
                            position: 'outside',
                            formatter: '{b} \n{c} {d}%',
                        },
                        xAxis3D: {
                            min: -1,
                            max: 1,
                        },
                        yAxis3D: {
                            min: -1,
                            max: 1,
                        },
                        zAxis3D: {
                            min: -1,
                            max: 1,
                        },
                        grid3D: {
                            show: false,
                            boxHeight: 30, // 三维笛卡尔坐标系在三维场景中的高度
                            viewControl: {
                                alpha: 40,
                                beta: 40,
                                distance: 500, //调整视角到主体的距离，类似调整zoom
                                rotateSensitivity: 0, // 设置为0无法旋转
                                zoomSensitivity: 0, // 设置为0无法缩放
                                panSensitivity: 0, // 设置为0无法平移
                                autoRotate: false, // 自动旋转
                            },
                        },
                        series: series,
                    })
                })
            },
            // 生成扇形的曲面参数方程，用于 series-surface.parametricEquation
            getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, h) {
                // 计算
                const midRatio = (startRatio + endRatio) / 2;
                const startRadian = startRatio * Math.PI * 2;
                const endRadian = endRatio * Math.PI * 2;
                const midRadian = midRatio * Math.PI * 2;
                // 如果只有一个扇形，则不实现选中效果。
                if (startRatio === 0 && endRatio === 1) {
                    isSelected = false;
                }
                // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
                k = 1;
                // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
                const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
                const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;
                // 计算高亮效果的放大比例（未高亮，则比例为 1）
                const hoverRate = isHovered ? 1.05 : 1;
                // 返回曲面参数方程
                return {
                    u: {
                        min: -Math.PI,
                        max: Math.PI * 3,
                        step: Math.PI / 32,
                    },
                    v: {
                        min: 0,
                        max: Math.PI * 2,
                        step: Math.PI / 20,
                    },
                    x: function (u, v) {
                        if (u < startRadian) {
                            return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
                        }
                        if (u > endRadian) {
                            return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
                        }
                        return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
                    },
                    y: function (u, v) {
                        if (u < startRadian) {
                            return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
                        }
                        if (u > endRadian) {
                            return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
                        }
                        return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
                    },
                    z: function (u, v) {
                        if (u < -Math.PI * 0.5) {
                            return Math.sin(u);
                        }
                        if (u > Math.PI * 2.5) {
                            return Math.sin(u) * h * 0.1;
                        }
                        return Math.sin(v) > 0 ? 1 * h * 0.1 : -1;
                    },
                };
            },
            /**
             * 绘制3d图
             * @param pieData 总数据
             * @param internalDiameterRatio:透明的空心占比
             * @param distance 视角到主体的距离
             * @param alpha 旋转角度
             * @param pieHeight 立体的高度
             * @param opacity 饼或者环的透明度
             */
            getPie3D(pieData, internalDiameterRatio, distance, alpha, pieHeight, opacity = 1) {
                let that=this;
                const series = [];
                let sumValue = 0;
                let startValue = 0;
                let endValue = 0;
                const legendData = [];
                const k =
                    typeof internalDiameterRatio !== 'undefined'
                        ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
                        : 1 / 3;
                // 为每一个饼图数据，生成一个 series-surface 配置
                for (let i = 0; i < pieData.length; i += 1) {
                    sumValue += pieData[i].value;
                    const seriesItem = {
                        name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
                        type: 'surface',
                        parametric: true,
                        wireframe: {
                            show: false,
                        },
                        pieData: pieData[i],
                        pieStatus: {
                            selected: false,
                            hovered: false,
                            k: k,
                        },
                    };
                    if (typeof pieData[i].itemStyle !== 'undefined') {
                        const itemStyle = {};
                        if (typeof pieData[i].itemStyle.color !== 'undefined') {
                            itemStyle.color = pieData[i].itemStyle.color;
                        }
                        if (typeof pieData[i].itemStyle.opacity !== 'undefined') {
                            itemStyle.opacity = pieData[i].itemStyle.opacity;
                        }
                        seriesItem.itemStyle = itemStyle;
                    }
                    series.push(seriesItem);
                }
                // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
                // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
                for (let i = 0; i < series.length; i += 1) {
                    endValue = startValue + series[i].pieData.value;
                    series[i].pieData.startRatio = startValue / sumValue;
                    series[i].pieData.endRatio = endValue / sumValue;
                    console.log(series[i].pieData.startRatio,
                        series[i].pieData.endRatio,
                        false,
                        false,
                        k,
                        series[i].pieData.value)
                    series[i].parametricEquation = that.getParametricEquation(
                        series[i].pieData.startRatio,
                        series[i].pieData.endRatio,
                        false,
                        false,
                        k,
                        series[i].pieData.value
                    );
                    startValue = endValue;
                    legendData.push(series[i].name);
                }
                return series;
            },
            chart6(){
                let that=this;
                that.$nextTick(() => {
                    let myChart = echarts.init(document.getElementById('chart6'));
                    myChart.setOption({

                        tooltip: {
                            trigger: "axis",
                            textStyle: {
                                fontSize: 12,
                                fontFamily:'AlRegular',
                                color:'#fff',
                            },
                            backgroundColor: 'rgba(50,50,50,0.9)',
                            borderWidth: 0,
                        },
                        grid: {
                            left: '10%',
                            top: '5%',
                            bottom: '30%',
                            right: '10%',
                        },
                        xAxis: {
                            data: ['<0.5t', '0.5t≤X≤1t', '1t≤X≤3t','3t≤X≤5t','5t≤X≤10t','10t≤X≤30t','≥30t'],
                            axisLine: {
                                show: true, //隐藏X轴轴线
                                lineStyle: {
                                    color: 'rgba(48, 98, 105, 1)',
                                    width: 1
                                }
                            },
                            axisTick: {
                                show: false //隐藏X轴刻度
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 12,
                                    fontFamily:'AlRegular'
                                },
                                rotate: 0,//倾斜度
                            },
                        },
                        yAxis: [{
                            type: "value",
                            /*name: "亿元",*/
                            nameTextStyle: {
                                fontSize: 12,
                                color:'#fff',
                                fontFamily:'AlRegular'
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: "rgba(48, 98, 105, 0.2)",
                                    width:1,
                                    type: 'dashed'//虚线
                                }
                            },
                            axisTick: {
                                show: false
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: '#768293',
                                    width: 0
                                }
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    fontSize: 12,
                                    color:'#fff',
                                    fontFamily:'AlRegular'
                                }
                            },

                        },
                            {
                                type: "value",
                                /*name: "同比",*/
                                nameTextStyle: {
                                    fontSize: 12,
                                    color:'#fff',
                                    fontFamily:'AlRegular'
                                },
                                position: "right",
                                splitLine: {
                                    show: false
                                },
                                axisTick: {
                                    show: false
                                },
                                axisLine: {
                                    show: false,
                                },
                                axisLabel: {
                                    show: true,
                                    formatter: "{value} %", //右侧Y轴文字显示
                                    textStyle: {
                                        fontSize: 12,
                                        color:'#fff',
                                        fontFamily:'AlRegular'
                                    }
                                }
                            }
                        ],
                        series: [
                            {
                                name: "熔炼炉",
                                type: "bar",
                                barWidth: 14,
                                itemStyle: {
                                    normal: {
                                        color: '#00CBFF',
                                        borderColor:'#00CBFF',
                                        borderWidth: 1
                                    }
                                },
                                data: ['500', '800', '400','300','340','380','290']
                            }
                        ]
                    })
                })
            },
        },
    })
</script>

</body>
</html>