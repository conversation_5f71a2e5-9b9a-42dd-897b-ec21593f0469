<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>评审标准管理</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .el-tree-node__content { height: 40px;}
        .custom-tree-node { display: inline-block;width: 100%;}
        .mytable {border-collapse:collapse;width: 100%;}
        .mytable thead tr td {border: 1px solid #333;font-weight: 700; padding: 10px;background: #f5f7fa;text-align: center;}
        .mytable tbody tr td {border: 1px solid #333; padding: 10px;text-align: center;}
        .mytable .active td{ background: #bcbec2;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">

        <el-form :inline="true" :model="searchFrom" class="form-inline" @submit.native.prevent>
            <el-form-item style="float: right">
                <el-button :loading="loading" type="success" size="mini" @click="nameAdd" v-loading.fullscreen.lock="loading">添加</el-button>
            </el-form-item>
        </el-form>

        <el-table border
                  v-loading="loading"
                  :data="data"
                  style="width: 100%;margin-bottom: 20px;"
                  :height="height"
                  size="small">
            <el-table-column
                    type="index"
                    label="序号"
                    align="center"
                    width="50">
            </el-table-column>
            <el-table-column
                    prop="name"
                    label="评审标准名称"
                    align="center"
                    show-overflow-tooltip
                    min-width="200">
            </el-table-column>
            <el-table-column
                    prop="status"
                    label="状态"
                    align="center"
                    show-overflow-tooltip
                    min-width="120">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.status==0" type="success">启用</el-tag>
                    <el-tag v-else type="info">停用</el-tag>
                </template>
            </el-table-column>
<!--            <el-table-column-->
<!--                    prop="apply_time"-->
<!--                    label="运用任务"-->
<!--                    align="center"-->
<!--                    show-overflow-tooltip-->
<!--                    min-width="120">-->
<!--            </el-table-column>-->
            <!--<el-table-column
                    prop="create_time"
                    label="创建时间"
                    align="center"
                    min-width="150">
            </el-table-column>
            <el-table-column
                    prop="update_time"
                    label="修改时间"
                    align="center"
                    min-width="150">
            </el-table-column>-->
            <el-table-column
                    label="操作"
                    align="center"
                    min-width="200">
                <template slot-scope="scope">
                    <el-button type="" @click="nameView(scope.row)" size="small">标准详情</el-button>
                    <el-button type="warning" @click="nameUpdate(scope.row)" size="small">编辑</el-button>
                    <el-button type="danger" @click="nameDelete(scope.row)" size="small">删除</el-button>
                    <el-button v-if="scope.row.status==0" type="danger" @click="updateStatus(scope.row,1)" size="small">停用</el-button>
                    <el-button v-if="scope.row.status==1" type="success" @click="updateStatus(scope.row,0)" size="small">启用</el-button>
                </template>
            </el-table-column>
        </el-table>

        <nameadd ref="nameadd" @ok="getResult()"></nameadd>
        <standard-des ref="standardDes" @ok="getResult()"></standard-des>
    </div>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                title:'新增',
                nameList:[],
                currentKey:0,
                searchFrom: {
                    search: '',
                },
                filterText:'',
                data:{
                    elementTitle:[],
                },
                dialogFormVisible: false,
                visible: false,
                loading: true,
                // height:document.documentElement.clientHeight,
                width:document.documentElement.clientWidth,

                searchFrom: {},
                data: [],
                height: document.documentElement.clientHeight - 155,
            };
        },
        components: {
            'nameadd': 'url:/general/toppingsoft/app/admin/view/setting/vue/nameadd.vue?v=1',
            'standard-des': 'url:/general/toppingsoft/app/admin/view/setting/vue/standardDes.vue?v=1',
        },
        created() {
            var _this = this;
            //窗口改变时
            window.onresize = function () {
                _this.height = document.documentElement.clientHeight;
                _this.width = document.documentElement.clientWidth;
            };
        },
        watch: {
            filterText(val) {
                this.$refs.tree.filter(val);
            }
        },
        methods: {
            nameView(row){
                this.$refs.standardDes.title="评审标准管理";
                this.$refs.standardDes.open(row);
            },
            getResult() {
                var _this = this;
                var param = _this.searchFrom;
                axios.post('getStandardList',param).then(function (res) {
                    if(res.data.code == 0){
                        _this.data = res.data.data;
                        _this.loading = false
                    }else{
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                    }
                    _this.loading = false;
                }).catch(function (error) {
                    console.log(error);
                    _this.loading = false;
                });
            },

            nameAdd(){
                this.$refs.nameadd.title="新增标准";
                this.$refs.nameadd.open();
            },
            nameUpdate(data){
                this.$refs.nameadd.title="编辑标准";
                this.$refs.nameadd.open(data);
            },
            nameDelete(row){
                var _this = this;
                _this.$confirm('确定删除该标准及其下所有信息吗?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    axios.post('standardDel',{id:row.id}).then(res => {
                        if(res.data.code == 0){
                            _this.$message({message: res.data.msg, type: 'success'})
                            _this.getResult()
                            _this.activeName = {};
                            _this.data = {};
                        }else{
                            _this.$message.error(res.data.msg)
                        }
                    })
                }).catch(() => {})
            },
            updateStatus(row,status){
                var _this = this;
                var msg = status == 1 ? '确定停用该标准吗?' : '确定启用该标准吗?';
                _this.$confirm(msg, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    axios.post('updateStatus',{id:row.id,status:status}).then(res => {
                        if(res.data.code == 0){
                            _this.$message({message: res.data.msg, type: 'success'})
                            _this.getResult()
                        }else{
                            _this.$message.error(res.data.msg)
                        }
                    })
                }).catch(() => {})
            },
        },
        mounted() {
            this.getResult();
        }
    })
</script>


</body>
</html>