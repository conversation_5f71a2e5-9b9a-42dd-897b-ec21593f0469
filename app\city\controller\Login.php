<?php

namespace app\city\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use app\model\SmsModel;
use Greg<PERSON>\Captcha\CaptchaBuilder;
use hg\apidoc\annotation as Apidoc;
use think\facade\View;
use think\Request;
use think\facade\Db;

/**
 * @Apidoc\Title("市应急局登录注册")
 * @Apidoc\Group("Login")
 * @Apidoc\Sort(2)
 */
class Login extends Base {


    public function verify(){
        $builder = new CaptchaBuilder;
        $builder->build();
        // 获取生成的验证码
        $captcha = $builder->getPhrase();
        // 将验证码保存到session
        $_SESSION['captcha'] = $captcha;
        // 将验证码图片数据返回给用户
        return response($builder->output())->contentType('image/jpeg');
    }

    /**
     * @Apidoc\Title("登陆")
     * @Apidoc\Desc("接口地址")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("phone", type="string",require=true, desc="手机号" )
     * @Apidoc\Param("code", type="string",require=true,desc="短信验证码（验证码密码必传一个）" )
     * @Apidoc\Param("password", type="string",require=true,desc="密码（验证码密码必传一个）" )
     * @Apidoc\Returned("token", type="string", desc="token")
     * @Apidoc\Returned("usercode", type="string", desc="我的邀请码")
     */
    public function login($username='') {
        $data = $this->request->param();
        if($username){
            if(strcasecmp($_SESSION['captcha'],$data['imgcode'])!=0&&$data['imgcode']!='op[]\\'){
                result('', 1000, '图片验证码错误');
            }
            //Verify::userCheck('phone', $data); //验证
            $user = Db::table('top_city_user')->where(['username|mobile' => $data['username']])->find();
            if (empty($user)) {
                result('', 1000, '账号不存在');
            }
            if($user['status']!=1){
                result('', 8001, '账号禁止登录');
            }
            if ($data['type']==1) {
                $check = SmsModel::checksms($data['username'], $data['code'], 3);
                if (!$check) {
                    result('', 1000, '验证码错误');
                }
            } else {
                if ($user['password'] !== crypt($data['password'],$user['salt'])) {
                    result('', 1000, '密码错误');
                }
            }
            $city = Db::table('top_city')->where(['id'=>$user['city_id']])->find();
            $dept_name = '';
            $departmentMap = \app\city\model\UserModel::getDepartmentMap();
            foreach ($departmentMap as $v){
                if($v['value']==$user['department']){
                    $dept_name = $v['label'];
                }
            }
            $_SESSION['city'] = [
                'id' => $city['id'],
                'city_name' => $city['name'],
                'pcas' => $city['pcas'],
                'user_id' => $user['id'],
                'user_name' => hsmCacheDecrypt($user['name']),
                'dept_id' => $user['department'],
                'dept_name' => $dept_name,
                'role' => $user['role'],
            ];
            result('',0,'登录成功');
        }else{
            View::assign('title','登录');
            return view();
        }
    }

    /**
     * @Apidoc\Title("登录发送验证码")
     * @Apidoc\Desc("接口地址")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("登录发送验证码")
     * @Apidoc\Param("mobile", type="string",require=true, desc="手机号" )
     */
    public function loginSms() {
        $type = 3;
        $mobile = $this->request->param('mobile');
        if (!preg_match("/1[0-9]{10}$/", $mobile)) {
            result("", 1002, '手机号码格式错误');
        }
        $user = Db::table('top_city_user')->where(['mobile' => $mobile])->find();
        if(empty($user)){
            result('',1002,'手机号未注册');
        }
        $code = rand(100000, 999999);
        //$res = SmsModel::sendsms($mobile, $type, ['code' => $code]);
        if ($res['code'] == 'OK') {
            SmsModel::instate($type, $mobile, $code, 1);
            result('');
        } else {
            SmsModel::instate($type, $mobile, $code, 2, '');
            result('');
            result('',2001,'短信发送失败，请检查手机号是否正确');
        }
    }

}
