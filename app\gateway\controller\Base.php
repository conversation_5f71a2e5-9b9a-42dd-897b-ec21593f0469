<?php
declare (strict_types = 1);

namespace app\gateway\controller;

use think\App;
use think\facade\Db;
use \liliuwei\think\Jump;

/**
 * 控制器基础类
 */
abstract class Base
{
    use Jump;

    /**
     * Request实例
     * @var \think\Request
     */
    protected $request;

    /**
     * 应用实例
     * @var \think\App
     */
    protected $app;

    /**
     * 是否批量验证
     * @var bool
     */
    protected $batchValidate = false;

    /**
     * 控制器中间件
     * @var array
     */
    protected $middleware = [];

    //空方法
    public function __call($method, $args)
    {
        $data = request()->param();
        return view($method, ['data' => $data]);
    }

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        error_reporting(E_ERROR | E_PARSE);
        $this->app     = $app;
        $this->request = $this->app->request;
        session_start();
        //初始化
        $this->initialize();
        // 关闭未定义对象报错
    }

}
