<template>
  <el-dialog :title="title"
             :visible.sync="visible"
             width="800px"
             append-to-body="true" top="30px"
             :show-close="false"
             :close-on-press-escape="false"
             :height="height"
             :close-on-click-modal="false">
    <template>
      <el-row style="text-align: center;margin-top: 10px">
        <el-descriptions class="margin-top" :column="2" size="small" border>
          <el-descriptions-item>
            <template slot="label">
              <i class="el-icon-notebook-1"></i>
              试题标题
            </template>
            <el-input v-model="testInfo.test_title"></el-input>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              <i class="el-icon-wallet"></i>
              试题类型
            </template>

            <template>
              <el-select @change="typeChange" v-model="testInfo.type_id" placeholder="请选择">
                <el-option
                    v-for="item in typeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </template>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              <i class="el-icon-time"></i>
              试题解析
            </template>

            <template>
              <el-input v-model="testInfo.test_analysis" type="textarea" :row="3" placeholder="试题解析">
              </el-input>
            </template>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              <i class="el-icon-wallet"></i>
              试题答案
            </template>

            <template>
              <el-select v-model="testInfo.test_answer" placeholder="请选择">
                <el-option
                    v-for="item in testInfo.test_option"
                    :key="item.option"
                    :label="item.option"
                    :value="item.option">
                </el-option>
              </el-select>
            </template>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              <i class="el-icon-wallet"></i>
              试题选项
            </template>

            <template>
              <el-row style="text-align: right">
                <el-button v-if="testInfo.type_id==1" style="margin-bottom: 10px" size="small" type="primary"
                           @click="addRow">添加选项
                </el-button>
                <el-table height="350"
                          :data="testInfo.test_option"
                          border
                          style="width: 100%">
                  <el-table-column
                      type="index"
                      label="序号"
                      width="80">
                  </el-table-column>
                  <el-table-column
                      prop="option" width="450"
                      label="选项内容">
                    <template slot-scope="scope">
                      <template v-if="testInfo.type_id==1">
                        <el-input type="textarea"
                                  v-model="scope.row.option"></el-input>
                      </template>
                      <template v-else>
                        {{ scope.row.option }}
                      </template>

                    </template>
                  </el-table-column>
                  <el-table-column
                      label="操作"
                      width="120">
                    <template slot-scope="scope">
                      <el-button v-if="testInfo.type_id==1"
                                 @click="deleteRow(scope.$index)"
                                 type="text"
                                 size="small">
                        移除选项
                      </el-button>
                    </template>
                  </el-table-column>

                </el-table>
              </el-row>
            </template>
          </el-descriptions-item>
        </el-descriptions>
      </el-row>
      <el-row style="margin-top: 10px;text-align: right;margin-right: 20px">
        <el-button size="small" type="primary" @click="sure">提交</el-button>
        <el-button size="small" type="info" @click="visible=false">关闭</el-button>
      </el-row>
    </template>
  </el-dialog>

</template>

<script>


module.exports = {
  name: "template",
  components: {
    'choice-manager-user': 'url:../../../../../public/vue/personnelChoose.vue?v=014',
    'choice-look-user': 'url:../../../../../public/vue/personnelChoose.vue?v=014',
  },
  data() {
    return {
      type: 'info',
      type_id: '',
      visible: false,
      isDisabled: false,
      title: '详情',
      template_id: '',
      loading: false,
      specialFiledTypeOption: [],
      typeOptions: [{
        value: 1,
        label: '单选'
      }, {
        value: 2,
        label: '判断'
      }],

      testInfo: {
        test_title: "",
        test_option: [],
        test_answer: [],
        test_analysis: "",
        type_id: "",
        industry_ids: "",
        standard_name_id: "",
        id: "",
      },
      currentTab: 'first', // 当前tab来源
      height: document.documentElement.clientHeight - 550,
    }
  },

  methods: {

    addOne: function (value, tab) {
      this.type = 'add';
      this.title = '新增';
      this.isDisabled = false;
      this.visible = true;
      this.currentTab = tab || 'first';
      
      this.testInfo = {
        test_title: "",
        test_option: [],
        test_answer: "",
        test_analysis: "",
        type_id: "",
        industry_ids: "",
        standard_name_id: "",
        id: "",
      };
      
      // 根据tab来源设置相应字段
      if (this.currentTab === 'second') {
        // 专业库：使用standard_name_id
        this.testInfo.standard_name_id = value;
        this.testInfo.industry_ids = "";
        this.title = '新增专业库试题';
      } else {
        // 基础库：使用industry_ids
        this.testInfo.industry_ids = value;
        this.testInfo.standard_name_id = "";
        this.title = '新增基础库试题';
      }
    },
    changeOne: function (test_id) {
      this.type = 'change';
      this.title = '编辑';
      this.isDisabled = false;
      this.visible = true;
      this.testInfo = {
        test_title: "",
        test_option: [],
        test_answer: "",
        test_analysis: "",
        type_id: "",
        industry_ids: "",
        standard_name_id: "",
        id: "",
      };
      this.testInfo.id = test_id;
      this.getOneTestInfo();
    },

    deleteRow(index) {
      this.testInfo.test_option.splice(index, 1);
    },
    typeChange() {
      if (this.testInfo.type_id == 2) {
        this.testInfo.test_answer = [];
        this.testInfo.test_option = [
          {option: '对'},
          {option: '错'},
        ];
      } else {
        this.testInfo.test_answer = [];
        this.testInfo.test_option = [];
      }
    },

    addRow() {
      this.testInfo.test_option.push({
        option: ''
      })
    },
    sure: function () {
      switch (this.type) {
        case "add":
          this.addOneInfo();
          break;
        case "change":
          this.changeOneInfo();
          break;
      }
    },

    changeOneInfo: function () {
      var vm = this;
      axios.post("changeOne", this.testInfo)
          .then(function (res) {
            if (res.data.code == 1000) {
              vm.$emit('ok')
            }
            vm.$message({
              message: res.data.msg,
              type: res.data.type
            });

          }).catch(function (error) {
        console.log(error);
      });
    },
    addOneInfo: function () {
      var vm = this;
      axios.post("addOne", this.testInfo)
          .then(function (res) {
            if (res.data.code == 1000) {
              vm.$emit('ok')
              vm.visible = false;
            }
            vm.$message({
              message: res.data.msg,
              type: res.data.type
            });

          }).catch(function (error) {
        console.log(error);
      });
    },
    getOneTestInfo: function () {
      var vm = this;
      var url = "getOneTestInfo";
      axios.post(url, {test_id: this.testInfo.id})
          .then(function (res) {
            if (res.data.code == 1000) {
              vm.testInfo.id = res.data.data.id
              vm.testInfo.test_option = res.data.data.test_option
              vm.testInfo.test_analysis = res.data.data.test_analysis
              vm.testInfo.test_answer = res.data.data.test_answer
              vm.testInfo.test_title = res.data.data.test_title
              vm.testInfo.type_id = res.data.data.type_id
              vm.testInfo.industry_ids = res.data.data.industry_ids
              vm.testInfo.standard_name_id = res.data.data.standard_name_id
            }
          }).catch(function (error) {
        console.log(error);
      });
    },

  },
  created: function () {

  }
}
</script>
