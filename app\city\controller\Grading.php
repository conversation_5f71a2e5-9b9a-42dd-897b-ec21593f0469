<?php
declare (strict_types=1);

namespace app\city\controller;

use app\city\model\CompanyModel;
use app\city\model\GradingModel;
use app\model\MessageModel;
use app\model\SettingModel;
use think\facade\Db;
use think\facade\View;
use app\model\FileModel;

class Grading extends Base
{
    /**
     * @Apidoc\Title("定级申请列表")
     * @Apidoc\Desc("定级申请列表")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function index($limit = 20, $excel = 0)
    {
        if (request()->isAjax()) {
            $title = $this->request->param('title', '', 'trim');
            $status = $this->request->param('status', '', 'trim');
            $area = $this->request->param('area', '', 'trim');
            $industry = $this->request->param('industry', '', 'trim');
            $where = [
                ['city_id', '=', $_SESSION['city']['id']],
            ];
            if ($_SESSION['city']['dept_id'] != 1) {
                $where[] = ['dept_id', '=', $_SESSION['city']['dept_id']];
            }
            if (!empty($title)) {
                $where[] = ['company_name', 'like', "%{$title}%"];
            }
            if (!empty($area)) {
                $where[] = ['area_id', '=', "$area"];
            }
            if (!empty($industry)) {
                $where[] = ['industry', '=', "$industry"];
            }
            if ($status == 1) {
                $where[] = ['prcs_id', 'in', [3, 7]];
            } else if ($status == 2) {
                $where[] = ['prcs_id', '>', 0];
            } else if ($status == 7) {
                $where[] = ['status', '=', 7];
            } else if ($status == 5) {
                $where[] = ['status', 'in', [5, 9]];
            }
            $res = Db::table('top_grading')->where($where)->order('id desc');
            $res = $res->paginate($limit)->each(function ($item, $key) {
                $item['apply_time'] = date('Y-m-d', strtotime($item['apply_time']));
                return $item;
            });
            result($res);
        } else {
            $area = Db::table('top_area')->alias('a')->leftJoin('top_pca b', 'a.pcas = b.code')->field('a.id,b.name')->select()->toArray();
            $industry = Db::table('top_industry')->where(['pid' => 0])->field('id,name')->select()->toArray();
            View::assign('area', $area);
            View::assign('industry', $industry);
            View::assign('title', '首页');
            return view();
        }
    }

    public function record($limit = 20, $excel = 0)
    {
        if (request()->isAjax()) {
            $title = $this->request->param('title', '', 'trim');
            $status = $this->request->param('status', '', 'trim');
            $area = $this->request->param('area', '', 'trim');
            $industry = $this->request->param('industry', '', 'trim');
            $where = [
                ['city_id', '=', $_SESSION['city']['id']],
            ];
            if ($_SESSION['city']['dept_id'] != 1) {
                $where[] = ['dept_id', '=', $_SESSION['city']['dept_id']];
            }
            if (!empty($title)) {
                $where[] = ['company_name', 'like', "%{$title}%"];
            }
            if (!empty($area)) {
                $where[] = ['area_id', '=', "$area"];
            }
            if (!empty($industry)) {
                $where[] = ['industry', '=', "$industry"];
            }
            if ($status == 1) {
                $where[] = ['status', '=', 1];
            } else if ($status == 7) {
                $where[] = ['status', '=', 7];
            } else if ($status == 5) {
                $where[] = ['status', 'in', [5, 9]];
            }
            $res = Db::table('top_grading_record')->where($where)->order('id desc');
            $res = $res->paginate($limit)->each(function ($item, $key) {
                $codes = explode(',', $item['file']);
                $item['file'] = [];
                foreach ($codes as $v1) {
                    if (!empty($v1)) {
                        $f = FileModel::getFile(0, $v1, '');
                        $item['file'][] = [
                            'id' => $f['id'],
                            'code' => $f['code'],
                            'name' => $f['name'],
                            'url' => $f['url'],
                            'ext' => $f['ext'],
                        ];
                    }
                }
                // 获取区县名称
                if ($item['area_id']) {
                    $area = Db::table('top_area')->where(['id' => $item['area_id']])->find();
                    $item['area_name'] = $area['name'] ?? '';
                }
                $item['apply_time'] = date('Y-m-d', strtotime($item['apply_time']));
                $item['check_time'] = empty($item['check_time']) ? '' : date('Y-m-d', strtotime($item['check_time']));
                return $item;
            });

            result($res);
        } else {
            $area = Db::table('top_area')->alias('a')->leftJoin('top_pca b', 'a.pcas = b.code')->field('a.id,b.name')->select()->toArray();
            $industry = Db::table('top_industry')->where(['pid' => 0])->field('id,name')->select()->toArray();
            View::assign('area', $area);
            View::assign('industry', $industry);
            View::assign('title', '首页');
            return view();
        }
    }

    public function appeal($limit = 20, $excel = 0)
    {
        if (request()->isAjax()) {
            $title = $this->request->param('title', '', 'trim');
            $status = $this->request->param('status', '', 'trim');
            $where = [];
            if (!empty($title)) {
                $where[] = ['b.company_name', 'like', "%{$title}%"];
            }
            if ($status == 1) {
                $where[] = ['a.status', '=', 1];
            } else if ($status == 7) {
                $where[] = ['a.status', '=', 7];
            } else if ($status == 5) {
                $where[] = ['a.status', 'in', [5, 9]];
            }
            $res = Db::table('top_org_tasks_appeal')->alias('a')
                ->leftJoin('top_grading b', 'a.grading_id = b.id')
                ->leftJoin('top_org_tasks c', 'a.task_id = b.id')
                ->where($where)
                ->field('a.id,a.task_id,a.remark,a.create_time,b.level,b.company_id,b.company_name,c.review_name,c.date')->order('a.id desc');
            $res = $res->paginate($limit)->each(function ($item, $key) {
                $item['create_time'] = date('Y-m-d', strtotime($item['create_time']));
                return $item;
            });
            result($res);
        } else {
            View::assign('title', '首页');
            return view();
        }
    }

    public function gradingInfo($id = 0)
    {
        $where = [
            'id' => $id,
            ['city_id', '=', $_SESSION['city']['id']],
        ];
        if ($_SESSION['city']['dept_id'] != 1) {
            $where[] = ['dept_id', '=', $_SESSION['city']['dept_id']];
        }
        $re = Db::table('top_grading')->where($where)->find();
        $re = GradingModel::codeToText($re);
        $re['prcs'] = Db::table('top_grading_approval')->where(['grading_id' => $re['id']])->order('id')
            ->select()->each(function ($item, $key) {
                $files = empty($item['check_files']) ? [] : explode(',', $item['check_files']);
                foreach ($files as $v) {
                    $f = FileModel::getFile(0, $v, '');
                    $item['files'][] = [
                        'id' => $f['id'],
                        'name' => $f['name'],
                        'url' => $f['url'],
                    ];
                }
                $item['end_time'] = empty($item['end_time']) ? '' : date('Y-m-d H:i:s', strtotime($item['end_time']));
                $item['remark'] = empty($item['remark']) ? $item['check_content'] : $item['remark'];
                return $item;
            })->toArray();
        $re['reform'] = [];
        foreach ($re['prcs'] as $v) {
            if ($v['prcs_id'] == 5) {
                $re['reform'][] = $v;
            }
        }
        $re['steps'] = [];
        $re['stepsActive'] = 1;
        if (in_array($re['status'], [5, 7, 9])) {
            foreach ($re['prcs'] as $v) {
                $re['steps'][] = [
                    'label' => $v['prcs_name'],
                    'status' => 'success',
                ];
            }
            $re['steps'][count($re['steps']) - 1]['status'] = in_array($re['status'], [5, 9]) ? 'error' : 'success';
            $re['stepsActive'] = count($re['steps']);
        } else {
            $i = 0;
            foreach (config('global.grading_prcs') as $k => $v) {
                if ((!in_array($k, [5, 6, 7]) || in_array($re['reform_status'], [1, 2])) && !in_array($k, [10])) {
                    $re['steps'][] = [
                        'label' => $v['title'],
                        'status' => '',
                    ];
                }
                if ($re['prcs_id'] == $k) {
                    $re['stepsActive'] = $i;
                }
                $i++;
            }
        }
        $re['orgs'] = Db::table('top_org')->where(['is_del' => 0, 'status' => 1])->field('id,name')->select()->toArray();
        $re['files'] = config('global.grading_files');
        result($re);
    }


    public function recordInfo($id = 0)
    {
        $re = Db::table('top_grading_record')->where(['id' => $id])->find();
        $codes = empty($re['file']) ? [] : explode(',', $re['file']);
        $re['file'] = [];
        foreach ($codes as $v1) {
            if (!empty($v1)) {
                $f = FileModel::getFile(0, $v1, '');
                $re['file'][] = [
                    'id' => $f['id'],
                    'code' => $f['code'],
                    'name' => $f['name'],
                    'url' => $f['url'],
                    'ext' => $f['ext'],
                ];
            }
        }
        // 获取区县名称
        if ($re['area_id']) {
            $area = Db::table('top_area')->where(['id' => $re['area_id']])->find();
            $re['area_name'] = $area['name'] ?? '';
        }
        $re['apply_time'] = date('Y-m-d', strtotime($re['apply_time']));
        $re['check_time'] = empty($re['check_time']) ? '' : date('Y-m-d', strtotime($re['check_time']));
        result($re);
    }

    public function upload($model = 'city')
    {
        $file = request()->file('file');
        $result = FileModel::upload($file, $model);
        result($result);
    }

    public function gradingCheck($id = 0, $status = 0, $content = '', $org_id = 0, $remark = '')
    {
        $re = Db::table('top_grading')->where(['id' => $id, 'city_id' => $_SESSION['city']['id'], ['dept_id', '=', $_SESSION['city']['dept_id']]])->find();
        if (empty($re)) {
            result('', 1002, '申请信息不存在');
        }
        if (!in_array($re['prcs_id'], [3, 7])) {
            result('', 1002, '状态有误');
        }
        $prcs = Db::table('top_grading_approval')->where([['grading_id', '=', $re['id']], ['prcs_id', '=', $re['prcs_id']], ['status', 'in', [1, 2]]])->find();
        if (empty($prcs)) {
            result('', 1002, '审核信息不存在');
        }
        if ($status == 5 && empty(trim($content))) {
            result('', 1002, '请填写驳回原因');
        }
        $company = Db::table('top_company_info')->where(['id' => $re['company_id']])->find();
        $file = empty($file) ? '' : implode(',', $file);
        Db::startTrans();
        try {
            if ($re['prcs_id'] == 3) {
                $data = [
                    'end_user_id' => $_SESSION['city']['user_id'],
                    'end_user_name' => $_SESSION['city']['user_name'],
                    'end_time' => date('Y-m-d H:i:s'),
                    'check_content' => $content,
                    'check_files' => $file,
                    'status' => $status,
                    'status_name' => $status == 7 ? '已通过' : '未通过',
                ];
                Db::table('top_grading_approval')->where(['id' => $prcs['id']])->update($data);
                if ($status == 7) {
                    if (empty($org_id) && $re['type'] == '初次申请') {
                        Db::rollback();
                        result('', 1002, '请选择派发评审单位');
                    }
                    Db::table('top_grading')->where(['id' => $re['id']])->update(['prcs_id' => 4, 'prcs_name' => config('global.grading_prcs')[4]['title']]);
                    $apply_data = [
                        'grading_id' => $id,
                        'prcs_id' => 4,
                        'prcs_name' => config('global.grading_prcs')[4]['title'],
                        'status' => 1,
                        'params' => 'c:' . $re['city_id'],
                        'status_name' => '评审中',
                        'create_user_id' => $_SESSION['city']['user_id'],
                        'create_user_name' => $_SESSION['city']['user_name'],
                        'create_time' => date('Y-m-d H:i:s'),
                    ];
                    Db::table('top_grading_approval')->insertGetId($apply_data);
                    if ($company['personnel'] <= 100) {
                        $bishi_sum = 10;
                    } else if ($company['personnel'] <= 500) {
                        $bishi_sum = round($company['personnel'] / 10);
                    } else if ($company['personnel'] <= 1000) {
                        $bishi_sum = 50;
                    } else {
                        $bishi_sum = round($company['personnel'] / 20);
                    }
                    $pcas = SettingModel::getPcasAll();
                    $company['operate_address'] = empty($company['operate_address']) ? [] : explode(',', $company['operate_address']);
                    $company['mb_operate_address'] = '';
                    foreach ($company['operate_address'] as $k => $v) {
                        $company['mb_operate_address'] .= $pcas[$v];
                    }
                    $company['mb_operate_address'] .= $company['operate_address_info'];
                    $tasks_data = [
                        'grading_id' => $re['id'],
                        'company_id' => $re['company_id'],
                        'company_name' => $re['company_name'],
                        'org_id' => $org_id,
                        'remark' => $remark,
                        'review_id' => $company['review_id'],
                        'review_name' => $company['standard_name'],
                        'personnel' => $company['personnel'],
                        'address' => $company['mb_operate_address'],
                        'exam_num' => $bishi_sum,
                        'code' => 'T' . date("YmdHis"),
                        'city_id' => $company['city_id'],
                        'dept_id' => $company['dept_id'],
                    ];
                    Db::table('top_org_tasks')->insert($tasks_data);
                    $url = "/general/toppingsoft/index.php/company/grading/index";
                    $companyUser = Db::table('top_company_info')->field("user_id")->where(['id' => $re['company_id']])->find();
                    MessageModel::sendSms("company", $companyUser['user_id'], 'message', '您的定级申请已通过市应急局审核，等待评审单位评审！', $url);
                } else if ($status == 5) {
                    $apply_data = [
                        'grading_id' => $id,
                        'prcs_id' => 2,
                        'prcs_name' => config('global.grading_prcs')[2]['title'],
                        'status' => 1,
                        'params' => 'a:' . $company['area_id'],
                        'status_name' => '审核中',
                        'create_user_id' => $_SESSION['city']['user_id'],
                        'create_user_name' => $_SESSION['city']['user_name'],
                        'create_time' => date('Y-m-d H:i:s'),
                    ];
                    Db::table('top_grading_approval')->insertGetId($apply_data);
                    $url = "/general/toppingsoft/index.php/company/grading/index";
                    $companyUser = Db::table('top_company_info')->field("user_id")->where(['id' => $re['company_id']])->find();
                    MessageModel::sendSms("company", $companyUser['user_id'], 'message', '您的定级申请被驳回，请重新提交申请！', $url);
                    Db::table('top_grading')->where(['id' => $re['id']])->update(['prcs_id' => 2, 'prcs_name' => $apply_data['prcs_name']]);
                }
            } else if ($re['prcs_id'] == 7) {
                $data = [
                    'end_user_id' => $_SESSION['city']['user_id'],
                    'end_user_name' => $_SESSION['city']['user_name'],
                    'end_time' => date('Y-m-d H:i:s'),
                    'check_content' => $content,
                    'check_files' => $file,
                    'status' => $status,
                    'status_name' => $status == 7 ? '已通过' : '未通过',
                ];
                Db::table('top_grading_approval')->where(['id' => $prcs['id']])->update($data);
                if ($status == 7) {
                    Db::table('top_grading')->where(['id' => $re['id']])->update(['prcs_id' => 0, 'prcs_name' => '', 'status' => 7]);
                    $area = Db::table('top_area')->where(['id' => $re['area_id']])->find();
                    $ca_data = [
                        'grading_id' => $re['id'],
                        'company_id' => $re['company_id'],
                        'company_name' => $re['company_name'],
                        'industry' => $company['industry'],
                        'specialty' => $company['specialty'],
                        'level' => $re['level'],
                        'city_id' => $re['city_id'],
                        'dept_id' => $re['dept_id'],
                        'area_id' => $area['id'],
                        'area' => $area['pcas'],
                        'status' => 1,
                    ];
                    Db::table('top_certificate')->insert($ca_data);
                    $url = "/general/toppingsoft/index.php/company/grading/index";
                    $companyUser = Db::table('top_company_info')->field("user_id")->where(['id' => $re['company_id']])->find();
                    MessageModel::sendSms("company", $companyUser['user_id'], 'message', '您的定级申请已通过！', $url);
                } else if ($status == 5) {
                    $url = "/general/toppingsoft/index.php/company/grading/index";
                    $companyUser = Db::table('top_company_info')->field("user_id")->where(['id' => $re['company_id']])->find();
                    MessageModel::sendSms("company", $companyUser['user_id'], 'message', '您的定级申请被驳回，请重新提交申请！', $url);
                    Db::table('top_grading')->where(['id' => $re['id']])->update(['prcs_id' => 0, 'prcs_name' => '', 'status' => 5]);
                }
            }
            Db::commit();
            result($re);
        } catch (\Exception $e) {
            Db::rollback();
            result('', 7002, $e->getMessage());
        }
    }


    public function recordCheck($id = 0, $status = 0, $content = '')
    {
        $re = Db::table('top_grading_record')->where(['id' => $id, 'city_id' => $_SESSION['city']['id'], ['dept_id', '=', $_SESSION['city']['dept_id']]])->find();
        if (empty($re)) {
            result('', 1002, '申请信息不存在');
        }
        if (!in_array($re['status'], [1])) {
            result('', 1002, '状态有误');
        }
        Db::startTrans();
        try {
            $data = [
                'status' => $status,
                'check_user_id' => $_SESSION['city']['user_id'],
                'check_user_name' => $_SESSION['city']['user_name'],
                'check_time' => date('Y-m-d H:i:s'),
                'check_remark' => $content,
            ];
            $up = Db::table('top_grading_record')->where(['id' => $id])->update($data);

            //审核通过后自动添加证书到证书记录表
            if ($status == 7) {
                // 检查是否已经有证书记录
                $existing = Db::table('top_certificate')->where([
                    'company_id' => $re['company_id'],
                    'level' => $re['level']
                ])->find();

                if (!$existing) {
                    // 自动生成证书编号（如果为空）
                    $certificate_code = $re['certificate_code'];
                    if (empty($certificate_code)) {
                        $certificate_code = 'CERT' . date('Ymd') . str_pad($re['company_id'], 4, '0', STR_PAD_LEFT);
                    }

                    // 添加证书到证书记录表
                    $certificate_data = [
                        'company_id' => $re['company_id'],
                        'company_name' => $re['company_name'],
                        'code' => $certificate_code,
                        'level' => $re['level'],
                        'industry' => $re['industry'],
                        'specialty' => $re['specialty'],
                        'area_id' => $re['area_id'],
                        'start' => $re['certificate_start_date'] ?? date('Y-m-d'),
                        'ends' => $re['certificate_end_date'] ?? date('Y-m-d', strtotime('+3 year', strtotime($re['certificate_start_date']))),
                        'check_date' => date('Y-m-d'),
                        'status' => 1,
                        'grading_id' => $id,
                        'image' => $re['file']
                    ];

                    Db::table('top_certificate')->insert($certificate_data);
                }
            }
            Db::commit();
            result();
        } catch (\Exception $e) {
            Db::rollback();
            result('', 1001, '操作失败：' . $e->getMessage());
        }

        if ($up) {
            $this->recordApprove($id);
            result();
        } else {
            result('', 4001, '网络错误');
        }
    }

    //获取企业整改详情数据
    public function getReformInfo()
    {
        $request = $this->request->post();
        if (isset($request['grading_id']) && !empty($request['grading_id'])) {
            $data = Db::table('top_company_reform')->where(["grading_id" => $request['grading_id']])->select()->toArray();

            if (!empty($data) && count($data) > 0) {
                foreach ($data as $k => $datum) {
                    $reform_before_files = !empty($datum['reform_before_files']) ? $datum['reform_before_files'] = explode(',', $datum['reform_before_files']) : [];
                    $data[$k]['reform_before_files'] = [];
                    foreach ($reform_before_files as $v) {
                        $f = FileModel::getFile('', $v, '');
                        $data[$k]['reform_before_files'][] = $f;
                        $data[$k]['reform_before_files_preview'][] = $f['url'];
                    }

                    $reform_affter_files = !empty($datum['reform_affter_files']) ? $datum['reform_affter_files'] = explode(',', $datum['reform_affter_files']) : [];
                    $data[$k]['reform_affter_files'] = [];
                    foreach ($reform_affter_files as $v) {
                        $f = FileModel::getFile('', $v, '');
                        $data[$k]['reform_affter_files'][] = $f;
                        $data[$k]['reform_affter_files_preview'][] = $f['url'];
                    }
                }
            }

            result($data, 0, '提交成功');
        } else {
            result('', 7001, '缺少关键参数');
        }
    }

    public function companyInfo($id = 0)
    {
        \app\model\CompanyModel::getCompanyInfo($id);
        exit();
    }

    public function getReview($element_id = 0)
    {
        if (request()->isAjax()) {
            $request = $this->request->post();
            \app\model\CompanyModel::getAjaxReview($element_id, $request);
            exit();

        } else {
            View::assign('element_id', $element_id);
            View::assign('title', '首页');
            return view();
        }
    }
}
