<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库表和字段快速过滤演示</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; background: #f5f5f5; padding: 20px; }
        
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { color: #2c3e50; margin-bottom: 10px; }
        .header p { color: #7f8c8d; }
        
        .search-section { margin-bottom: 30px; padding: 20px; background: #f8f9fa; border-radius: 6px; }
        .search-row { display: flex; gap: 15px; margin-bottom: 15px; align-items: center; flex-wrap: wrap; }
        .search-input { flex: 1; min-width: 200px; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; }
        .search-input:focus { outline: none; border-color: #3498db; }
        
        .search-options { display: flex; gap: 15px; flex-wrap: wrap; }
        .option-group { display: flex; align-items: center; gap: 5px; }
        .option-group label { font-size: 14px; color: #2c3e50; cursor: pointer; }
        .option-group input[type="checkbox"] { margin-right: 5px; }
        
        .btn { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; }
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn:hover { opacity: 0.9; }
        
        .results-section { margin-bottom: 30px; }
        .results-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
        .results-header h3 { color: #2c3e50; }
        .results-count { color: #7f8c8d; font-size: 14px; }
        
        .results-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .result-panel { background: #f8f9fa; border-radius: 6px; padding: 15px; }
        .result-panel h4 { color: #2c3e50; margin-bottom: 10px; border-bottom: 2px solid #3498db; padding-bottom: 5px; }
        
        .table-list, .field-list { max-height: 400px; overflow-y: auto; }
        .table-item, .field-item { padding: 10px; margin-bottom: 8px; background: white; border-radius: 4px; border-left: 4px solid #3498db; }
        .table-item:hover, .field-item:hover { background: #e3f2fd; }
        
        .table-name, .field-name { font-weight: bold; color: #2c3e50; margin-bottom: 5px; }
        .table-comment, .field-info { color: #7f8c8d; font-size: 12px; }
        .field-type { color: #e67e22; font-weight: bold; margin-right: 10px; }
        .field-table { color: #9b59b6; font-size: 11px; }
        
        .no-results { text-align: center; color: #7f8c8d; padding: 40px; font-style: italic; }
        
        .demo-section { margin-top: 30px; padding: 20px; background: #e8f5e8; border-radius: 6px; }
        .demo-section h3 { color: #27ae60; margin-bottom: 15px; }
        .demo-buttons { display: flex; gap: 10px; flex-wrap: wrap; }
        .demo-buttons .btn { font-size: 12px; padding: 6px 12px; }
        
        @media (max-width: 768px) {
            .results-grid { grid-template-columns: 1fr; }
            .search-row { flex-direction: column; align-items: stretch; }
            .search-options { justify-content: center; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>数据库表和字段快速过滤演示</h1>
            <p>演示 DatabaseCompatibilityService 的过滤功能</p>
        </div>

        <!-- 搜索区域 -->
        <div class="search-section">
            <div class="search-row">
                <input type="text" id="searchKeyword" class="search-input" placeholder="输入搜索关键词..." />
                <button class="btn btn-primary" onclick="performSearch()">搜索</button>
                <button class="btn btn-success" onclick="quickSearchDemo()">快速搜索</button>
            </div>
            
            <div class="search-options">
                <div class="option-group">
                    <input type="checkbox" id="searchTables" checked />
                    <label for="searchTables">搜索表</label>
                </div>
                <div class="option-group">
                    <input type="checkbox" id="searchFields" checked />
                    <label for="searchFields">搜索字段</label>
                </div>
                <div class="option-group">
                    <input type="checkbox" id="searchComments" checked />
                    <label for="searchComments">搜索注释</label>
                </div>
                <div class="option-group">
                    <input type="checkbox" id="caseSensitive" />
                    <label for="caseSensitive">大小写敏感</label>
                </div>
                <div class="option-group">
                    <input type="checkbox" id="exactMatch" />
                    <label for="exactMatch">精确匹配</label>
                </div>
            </div>
        </div>

        <!-- 搜索结果 -->
        <div class="results-section">
            <div class="results-header">
                <h3>搜索结果</h3>
                <div class="results-count" id="resultsCount">请输入关键词进行搜索</div>
            </div>
            
            <div class="results-grid">
                <!-- 表搜索结果 -->
                <div class="result-panel">
                    <h4>匹配的表</h4>
                    <div class="table-list" id="tableResults">
                        <div class="no-results">暂无搜索结果</div>
                    </div>
                </div>
                
                <!-- 字段搜索结果 -->
                <div class="result-panel">
                    <h4>匹配的字段</h4>
                    <div class="field-list" id="fieldResults">
                        <div class="no-results">暂无搜索结果</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 演示功能 -->
        <div class="demo-section">
            <h3>演示功能</h3>
            <p style="margin-bottom: 15px; color: #666;">点击下面的按钮体验不同的搜索场景：</p>
            <div class="demo-buttons">
                <button class="btn btn-primary" onclick="demoSearch('user', false, false)">搜索 "user"</button>
                <button class="btn btn-primary" onclick="demoSearch('varchar', false, false)">搜索 "varchar" 类型</button>
                <button class="btn btn-primary" onclick="demoSearch('id', false, false)">搜索 "id" 字段</button>
                <button class="btn btn-primary" onclick="demoSearch('用户', false, false)">搜索 "用户" 注释</button>
                <button class="btn btn-primary" onclick="demoSearch('USER', true, false)">大小写敏感搜索</button>
                <button class="btn btn-primary" onclick="demoSearch('user_info', false, true)">精确匹配</button>
            </div>
        </div>
    </div>

    <script>
        // 模拟数据 - 实际使用时应该通过AJAX从后端获取
        const mockTables = [
            { table_name: 'user_info', display_name: 'user_info (用户信息表)', table_comment: '用户信息表' },
            { table_name: 'user_role', display_name: 'user_role (用户角色表)', table_comment: '用户角色表' },
            { table_name: 'product_info', display_name: 'product_info (产品信息表)', table_comment: '产品信息表' },
            { table_name: 'order_detail', display_name: 'order_detail (订单详情表)', table_comment: '订单详情表' },
            { table_name: 'system_config', display_name: 'system_config (系统配置表)', table_comment: '系统配置表' }
        ];

        const mockFields = [
            { table_name: 'user_info', field_name: 'user_id', field_type: 'VARCHAR(32)', field_comment: '用户ID', is_nullable: false },
            { table_name: 'user_info', field_name: 'user_name', field_type: 'VARCHAR(50)', field_comment: '用户名', is_nullable: false },
            { table_name: 'user_info', field_name: 'email', field_type: 'VARCHAR(100)', field_comment: '邮箱地址', is_nullable: true },
            { table_name: 'user_role', field_name: 'role_id', field_type: 'INT(11)', field_comment: '角色ID', is_nullable: false },
            { table_name: 'user_role', field_name: 'role_name', field_type: 'VARCHAR(30)', field_comment: '角色名称', is_nullable: false },
            { table_name: 'product_info', field_name: 'product_id', field_type: 'VARCHAR(32)', field_comment: '产品ID', is_nullable: false },
            { table_name: 'product_info', field_name: 'product_name', field_type: 'VARCHAR(100)', field_comment: '产品名称', is_nullable: false },
            { table_name: 'order_detail', field_name: 'order_id', field_type: 'VARCHAR(32)', field_comment: '订单ID', is_nullable: false },
            { table_name: 'system_config', field_name: 'config_key', field_type: 'VARCHAR(50)', field_comment: '配置键', is_nullable: false }
        ];

        function performSearch() {
            const keyword = document.getElementById('searchKeyword').value.trim();
            if (!keyword) {
                alert('请输入搜索关键词');
                return;
            }

            const searchTables = document.getElementById('searchTables').checked;
            const searchFields = document.getElementById('searchFields').checked;
            const searchComments = document.getElementById('searchComments').checked;
            const caseSensitive = document.getElementById('caseSensitive').checked;
            const exactMatch = document.getElementById('exactMatch').checked;

            // 模拟过滤逻辑
            const filteredTables = searchTables ? filterTables(keyword, { searchComments, caseSensitive, exactMatch }) : [];
            const filteredFields = searchFields ? filterFields(keyword, { searchComments, caseSensitive, exactMatch }) : [];

            displayResults(filteredTables, filteredFields, keyword);
        }

        function filterTables(keyword, options) {
            const searchKeyword = options.caseSensitive ? keyword : keyword.toLowerCase();
            return mockTables.filter(table => {
                const tableName = options.caseSensitive ? table.table_name : table.table_name.toLowerCase();
                const tableComment = options.caseSensitive ? table.table_comment : table.table_comment.toLowerCase();
                
                let matched = false;
                if (options.exactMatch) {
                    matched = tableName === searchKeyword || (options.searchComments && tableComment === searchKeyword);
                } else {
                    matched = tableName.includes(searchKeyword) || (options.searchComments && tableComment.includes(searchKeyword));
                }
                return matched;
            });
        }

        function filterFields(keyword, options) {
            const searchKeyword = options.caseSensitive ? keyword : keyword.toLowerCase();
            return mockFields.filter(field => {
                const fieldName = options.caseSensitive ? field.field_name : field.field_name.toLowerCase();
                const fieldType = options.caseSensitive ? field.field_type : field.field_type.toLowerCase();
                const fieldComment = options.caseSensitive ? field.field_comment : field.field_comment.toLowerCase();
                
                let matched = false;
                if (options.exactMatch) {
                    matched = fieldName === searchKeyword || fieldType === searchKeyword || 
                             (options.searchComments && fieldComment === searchKeyword);
                } else {
                    matched = fieldName.includes(searchKeyword) || fieldType.includes(searchKeyword) || 
                             (options.searchComments && fieldComment.includes(searchKeyword));
                }
                return matched;
            });
        }

        function displayResults(tables, fields, keyword) {
            // 显示表结果
            const tableResults = document.getElementById('tableResults');
            if (tables.length > 0) {
                tableResults.innerHTML = tables.map(table => `
                    <div class="table-item">
                        <div class="table-name">${table.table_name}</div>
                        <div class="table-comment">${table.table_comment}</div>
                    </div>
                `).join('');
            } else {
                tableResults.innerHTML = '<div class="no-results">未找到匹配的表</div>';
            }

            // 显示字段结果
            const fieldResults = document.getElementById('fieldResults');
            if (fields.length > 0) {
                fieldResults.innerHTML = fields.map(field => `
                    <div class="field-item">
                        <div class="field-name">${field.field_name}</div>
                        <div class="field-info">
                            <span class="field-type">${field.field_type}</span>
                            <span class="field-comment">${field.field_comment}</span>
                        </div>
                        <div class="field-table">所属表: ${field.table_name}</div>
                    </div>
                `).join('');
            } else {
                fieldResults.innerHTML = '<div class="no-results">未找到匹配的字段</div>';
            }

            // 更新结果统计
            document.getElementById('resultsCount').textContent = 
                `找到 ${tables.length} 个表，${fields.length} 个字段 (关键词: "${keyword}")`;
        }

        function demoSearch(keyword, caseSensitive, exactMatch) {
            document.getElementById('searchKeyword').value = keyword;
            document.getElementById('caseSensitive').checked = caseSensitive;
            document.getElementById('exactMatch').checked = exactMatch;
            performSearch();
        }

        function quickSearchDemo() {
            const keyword = document.getElementById('searchKeyword').value.trim();
            if (!keyword) {
                alert('请输入搜索关键词');
                return;
            }

            // 模拟快速搜索
            const tables = filterTables(keyword, { searchComments: true, caseSensitive: false, exactMatch: false });
            const fields = filterFields(keyword, { searchComments: true, caseSensitive: false, exactMatch: false });
            
            displayResults(tables.slice(0, 10), fields.slice(0, 20), keyword + ' (快速搜索)');
        }

        // 实际的API调用函数
        function callSearchAPI(endpoint, params) {
            const baseUrl = '/admin/FieldEncryption/';
            const url = baseUrl + endpoint;

            return fetch(url + '?' + new URLSearchParams(params))
                .then(response => response.json())
                .catch(error => {
                    console.error('API调用失败:', error);
                    return { code: 500, msg: '网络错误', data: [] };
                });
        }

        // 真实的搜索函数（替换模拟数据）
        function performRealSearch() {
            const keyword = document.getElementById('searchKeyword').value.trim();
            if (!keyword) {
                alert('请输入搜索关键词');
                return;
            }

            const searchTables = document.getElementById('searchTables').checked;
            const searchFields = document.getElementById('searchFields').checked;
            const searchComments = document.getElementById('searchComments').checked;
            const caseSensitive = document.getElementById('caseSensitive').checked;
            const exactMatch = document.getElementById('exactMatch').checked;

            // 搜索表
            if (searchTables) {
                callSearchAPI('searchTables', {
                    keyword: keyword,
                    search_name: true,
                    search_comment: searchComments,
                    case_sensitive: caseSensitive,
                    exact_match: exactMatch
                }).then(result => {
                    if (result.code === 200) {
                        displayTables(result.data);
                    } else {
                        console.error('搜索表失败:', result.msg);
                        displayTables([]);
                    }
                });
            }

            // 这里可以添加字段搜索的逻辑
            // 由于字段搜索需要指定表名，可以遍历所有表进行搜索
        }

        function displayTables(tables) {
            const tableResults = document.getElementById('tableResults');
            if (tables.length > 0) {
                tableResults.innerHTML = tables.map(table => `
                    <div class="table-item">
                        <div class="table-name">${table.table_name}</div>
                        <div class="table-comment">${table.table_comment || ''}</div>
                    </div>
                `).join('');
            } else {
                tableResults.innerHTML = '<div class="no-results">未找到匹配的表</div>';
            }
        }

        // 页面加载时的提示
        window.onload = function() {
            console.log('数据库过滤演示页面已加载');
            console.log('现在可以使用真实的API进行搜索');

            // 添加真实搜索按钮
            const searchButton = document.querySelector('.btn-primary');
            if (searchButton) {
                searchButton.onclick = performRealSearch;
            }
        };
    </script>
</body>
</html>
