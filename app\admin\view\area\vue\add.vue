<style>
    .my-autocomplete li {
        line-height: normal;
        padding: 7px;
    }

    .my-autocomplete li .name {
        text-overflow: ellipsis;
        overflow: hidden;
    }

    .my-autocomplete li .addr {
        font-size: 12px;
        color: #b4b4b4;
    }

    .my-autocomplete li .highlighted {
        color: #ddd;
    }

    .el-cascader {
        width: 100%;
    }

    .el-form-item__content .el-input-group {
        vertical-align: middle;
    }

    .avatar-uploader .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .avatar-uploader .el-upload:hover {
        border-color: #409EFF;
    }

    .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 150px;
        height: 150px;
        line-height: 150px;
        text-align: center;
    }

    .avatar {
        width: 150px;
        height: 150px;
        display: block;
    }
</style>
<template>
    <el-dialog :title="title" :close-on-click-modal="false" modal="false" :visible.sync="visible" width="70%" top="100px"
               @close="refresh()" append-to-body="true" label-position="top">
        <el-form ref="form" :model="data" class="tableSearch" label-position="right" label-width="150px" :rules="rules">
            <el-row :gutter="20">
                <el-col :span="10">
                    <el-form-item label="名称" prop="name">
                        <el-input v-model="data.name" size="mini"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="10">
                    <el-form-item label="联系人" prop="contacts">
                        <el-input v-model="data.contacts" size="mini"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="10">
                    <el-form-item label="联系电话" prop="tel">
                        <el-input v-model="data.tel" size="mini"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="10">
                    <el-form-item label="地址" prop="address">
                        <el-input v-model="data.address" size="mini"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="10">
                    <el-form-item label="状态" prop="status">
                        <el-radio-group v-model="data.status">
                            <el-radio :label="1">正常</el-radio>
                            <el-radio :label="0">未使用</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="所属行政区" prop="pcas">
                        <el-tree
                                :data="pca"
                                show-checkbox
                                :default-checked-keys="pcas"
                                node-key="id"
                                :props="defaultProps"
                                @check-change="handleCheckChange"
                                ref="tree"
                        >
                        </el-tree>
                    </el-form-item>
                </el-col>
                <el-col :span="24" style="text-align: center;">
                    <el-button type="primary" @click="submit()">保存</el-button>
                    <el-button @click="visible = false">关闭</el-button>
                </el-col>
            </el-row>
        </el-form>
    </el-dialog>
</template>
<script>
    module.exports = {
        name: "add",
        // 模板导入区
        components: {},
        data: function () {
            return {
                id: 0,
                isAdmin: false,
                visible: false,
                title: '新增',
                model: '',
                loading: false,
                user_id: 0,
                date_object: new Date(),
                user_name: '',
                data: {},
                pca: {},
                rules: {
                    name: [
                        {required: true, message: '请输入名称', trigger: 'blur'}
                    ]
                },
                pcas: {},
                config: [],
                contractData: [],
                salesData: [],
                userData: [],
                dels: [],
                dels2: [],
                imageUrl: '',
                url: {
                    "newSave": 'save_data',
                },
                defaultProps: {
                    children: 'children',
                    label: 'label',
                    id: 'id'
                }
            }
        },
        mounted: function () {
            // this.getConfig();
        },
        created: function () {
        },
        methods: {
            handleChange(value) {
                var data = [];
                for (var i = 0; i < 3; i++) {
                    data.push(value[i]);
                }
                this.data.region = data;
            },
            handleCheckChange(data, checked, indeterminate) {
                checkedNodes = this.$refs.tree.getCheckedNodes();
                console.log(checkedNodes)
                var _this = this;
                _this.pcas = [];
                if (checkedNodes.length > 0) {
                    for (var i = 0; i < checkedNodes.length; i++) {
                        _this.pcas.push(checkedNodes[i].id);
                    }
                }
            },
            /**
             * 打开弹窗调用方法
             * */
            open: function (row, pca) {
                row = row ? row : {id: 0};
                this.data = row;

                var _this = this;
                _this.pca = pca;
                _this.pcas = row.pcas_ids;
                _this.visible = true;

                // _this.getConfig();
                // _this.getInfo(row.id);
            },
            submit: function () {
                var _this = this;
                var param = _this.data;
                param.pcas = _this.pcas.length > 0 ? _this.pcas.join(',') : '';
                var url = this.url.newSave;
                this.$refs.form.validate(function (valid) {
                    if (valid) {
                        axios.post(url, param).then(function (res) {
                            _this.$message({
                                message: res.data.msg,
                                type: res.data.type
                            });
                            if (res.data.code == 0) {
                                _this.visible = false;
                                _this.$emit("ok");
                            }
                        }).catch(function (error) {
                            console.log("出现错误:", error);
                        });
                    }
                });
            },

            refresh: function () {
                this.$emit("refresh");
            },
            changeweekBegin(data) {
                console.log(data);
            },
        }
    }
</script>