<style>
.my-label { width: 200px;}
.my-content { width: 450px;}
.margin-bottom { margin-bottom: 15px;}
.form-header { background-color: #E9F2F3; line-height: 25px; margin-bottom: 15px; padding: 5px 10px;}
.el-dialog__body { padding: 15px 20px;}
.el-tabs__content { overflow: auto;}
</style>
<template>
  <el-dialog :title="title" :close-on-click-modal="false"  modal="false" :visible.sync="visible" width="95%" top="10px" @close="refresh()" append-to-body="true" label-position="top">
    <el-tabs type="border-card">
      <el-tab-pane label="基本信息" :style="{height:height+'px'}">
        <el-descriptions class="margin-top" title="基本信息" :column="3" border label-class-name="my-label" content-class-name="my-content">
          <el-descriptions-item label="企业名称">
            {{data.name}}
          </el-descriptions-item>
          <el-descriptions-item label="注册地址">
            {{data.mb_reg_address}}
          </el-descriptions-item>
          <el-descriptions-item label="生产经营地点">
            {{data.mb_operate_address}}
          </el-descriptions-item>
          <el-descriptions-item label="所属行政区">
            {{data.mb_region}}
          </el-descriptions-item>
          <el-descriptions-item label="营业执照">
            <el-image
                style="width: 100px; height: 100px"
                :src="data.licenseUrl"
                :preview-src-list="[data.licenseUrl]">
            </el-image>
          </el-descriptions-item>
          <el-descriptions-item label="安全行政许可资料">
            <el-image
                style="width: 100px; height: 100px"
                :src="data.aocUrl"
                :preview-src-list="[data.aocUrl]">
            </el-image>
          </el-descriptions-item>
          <el-descriptions-item label="法定代表人">
            {{data.legal}}
          </el-descriptions-item>
          <el-descriptions-item label="法人联系电话">
            {{data.legal_mobile}}
          </el-descriptions-item>
          <el-descriptions-item label="法人邮箱">
            {{data.legal_email}}
          </el-descriptions-item>
          <el-descriptions-item label="座机电话号码">
            {{data.phone}}
          </el-descriptions-item>
          <el-descriptions-item label="企业传真">
            {{data.fax}}
          </el-descriptions-item>
          <el-descriptions-item label="产业园区">
            {{data.industrial_park}}
          </el-descriptions-item>
          <el-descriptions-item label="邮政编码">
            {{data.postal_code}}
          </el-descriptions-item>
          <el-descriptions-item label="邮政编码">
            {{data.postal_code}}
          </el-descriptions-item>
          <el-descriptions-item label="国民经济行业">
            {{data.mb_economy_sector}}
          </el-descriptions-item>
          <el-descriptions-item label="行业/专业">
            {{data.industry}}/{{data.specialty}}
          </el-descriptions-item>
          <el-descriptions-item label="统一社会信用代码">
            {{data.license_number}}
          </el-descriptions-item>
          <el-descriptions-item label="信用代码有效期">
            {{data.license_date}}
          </el-descriptions-item>
          <el-descriptions-item label="经济类型">
            {{data.mb_economy_type}}
          </el-descriptions-item>
          <el-descriptions-item label="企业规模">
            {{data.enterprise_size}}
          </el-descriptions-item>
          <el-descriptions-item label="注册资本">
            {{data.reg_money}}万元
          </el-descriptions-item>
          <el-descriptions-item label="安全管理联系人">
            {{data.manager}}
          </el-descriptions-item>
          <el-descriptions-item label="联系电话">
            {{data.manager_mobile}}
          </el-descriptions-item>
          <el-descriptions-item label="邮箱">
            {{data.manager_email}}
          </el-descriptions-item>
          <el-descriptions-item label="成立日期">
            {{data.date}}
          </el-descriptions-item>
          <el-descriptions-item label="固定资产">
            {{data.fixed_asset}}
          </el-descriptions-item>
          <el-descriptions-item label="年营业收入">
            {{data.revenue}}万元
          </el-descriptions-item>
          <el-descriptions-item label="员工总数">
            {{data.personnel}}
          </el-descriptions-item>
          <el-descriptions-item label="营业场所面积">
            {{data.area}}m³
          </el-descriptions-item>
          <el-descriptions-item label="专职安全管理人数">
            {{data.personnel_full}}
          </el-descriptions-item>
          <el-descriptions-item label="兼职安全管理人数">
            {{data.personnel_part}}
          </el-descriptions-item>
          <el-descriptions-item label="特种作业人数">
            {{data.personnel_special}}
          </el-descriptions-item>
          <el-descriptions-item label="所属集团名称">
            {{data.group_name}}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag v-if="data.status==0" type="danger">禁用</el-tag>
            <el-tag v-if="data.status==1" type="success">正常</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="经营范围" :span="3">
            {{data.business}}
          </el-descriptions-item>


          <el-descriptions-item :span="3">
            <template slot="label">
              <el-tag type="info" effect="dark" style="font-size: 15px;display: block;">粉尘涉爆</el-tag>
            </template>
          </el-descriptions-item>
          <el-descriptions-item label="是否粉尘涉爆" :span="3">
            {{data.is_dust_explosion}}
          </el-descriptions-item>
          <el-descriptions-item label="所属行业" v-if="data.is_dust_explosion=='是'" :span="3">
            {{data.sector}}
          </el-descriptions-item>
          <template v-for="(dust,key) in data.dust_list">
            <el-descriptions-item label="粉尘种类" v-if="data.is_dust_explosion=='是'" :span="1">
              {{dust.name}}
            </el-descriptions-item>
            <el-descriptions-item label="涉粉作业人数" v-if="data.is_dust_explosion=='是'" :span="2">
              {{dust.param_value}}
            </el-descriptions-item>
          </template>


          <el-descriptions-item :span="3">
            <template slot="label">
              <el-tag type="info" effect="dark" style="font-size: 15px;display: block;">涉氨制冷</el-tag>
            </template>
          </el-descriptions-item>
          <el-descriptions-item label="是否涉氨制冷" :span="3">
            {{data.is_ammonia_cold}}
          </el-descriptions-item>
          <el-descriptions-item label="液氨的用途" v-if="data.is_ammonia_cold=='是'">
            {{data.ammonia_use}}
          </el-descriptions-item>
          <el-descriptions-item label="液氨使用量" v-if="data.is_ammonia_cold=='是'">
            {{data.ammonia_usage}}t/a
          </el-descriptions-item>
          <el-descriptions-item label="液氨储存方式" v-if="data.is_ammonia_cold=='是'">
            {{data.ammonia_storage_method}}
          </el-descriptions-item>
          <el-descriptions-item label="液氨储存量" v-if="data.is_ammonia_cold=='是'">
            {{data.ammonia_storage_capacity}}t
          </el-descriptions-item>
          <el-descriptions-item label="气体泄露报警装置数" :span="2" v-if="data.is_ammonia_cold=='是'">
            {{data.gas_alarm_number}}
          </el-descriptions-item>


          <el-descriptions-item label="高炉数量" :span="1" v-if="data.is_hot_melting=='是'">
            {{data.blast_furnace_number}}
          </el-descriptions-item>
          <el-descriptions-item label="熔炼炉" :span="2" v-if="data.is_hot_melting=='是'">
            <span style="width: 120px;display: inline-block">{{data.hot_list[0].name}}</span> 数量: {{data.hot_list[0].param_value}}
          </el-descriptions-item>
          <el-descriptions-item label="有色金属冶炼炉数量" :span="1" v-if="data.is_hot_melting=='是'">
            {{data.nonferrous_furnace_number}}
          </el-descriptions-item>
          <el-descriptions-item label="熔炼炉" :span="2" v-if="data.is_hot_melting=='是'">
            <span style="width: 120px;display: inline-block">{{data.hot_list[1].name}}</span> 数量: {{data.hot_list[1].param_value}}
          </el-descriptions-item>
          <el-descriptions-item label="铁合金矿热炉数量" :span="1" v-if="data.is_hot_melting=='是'">
            {{data.ferroalloy_furnace_number}}
          </el-descriptions-item>
          <el-descriptions-item label="熔炼炉" :span="2" v-if="data.is_hot_melting=='是'">
            <span style="width: 120px;display: inline-block">{{data.hot_list[2].name}}</span> 数量: {{data.hot_list[2].param_value}}
          </el-descriptions-item>
          <el-descriptions-item label="冲天炉数量" :span="1" v-if="data.is_hot_melting=='是'">
            {{data.soaring_furnace_number}}
          </el-descriptions-item>
          <el-descriptions-item label="熔炼炉" :span="2" v-if="data.is_hot_melting=='是'">
            <span style="width: 120px;display: inline-block">{{data.hot_list[3].name}}</span> 数量: {{data.hot_list[3].param_value}}
          </el-descriptions-item>
          <el-descriptions-item label="" :span="1" v-if="data.is_hot_melting=='是'">
          </el-descriptions-item>
          <el-descriptions-item label="熔炼炉" :span="2" v-if="data.is_hot_melting=='是'">
            <span style="width: 120px;display: inline-block">{{data.hot_list[4].name}}</span> 数量: {{data.hot_list[4].param_value}}
          </el-descriptions-item>
          <el-descriptions-item label="" :span="1" v-if="data.is_hot_melting=='是'">
          </el-descriptions-item>
          <el-descriptions-item label="熔炼炉" :span="2" v-if="data.is_hot_melting=='是'">
            <span style="width: 120px;display: inline-block">{{data.hot_list[5].name}}</span> 数量: {{data.hot_list[5].param_value}}
          </el-descriptions-item>
          <el-descriptions-item label="" :span="1" v-if="data.is_hot_melting=='是'">
          </el-descriptions-item>
          <el-descriptions-item label="熔炼炉" :span="2" v-if="data.is_hot_melting=='是'">
            <span style="width: 120px;display: inline-block">{{data.hot_list[6].name}}</span> 数量: {{data.hot_list[6].param_value}}
          </el-descriptions-item>



          <el-descriptions-item :span="3">
            <template slot="label">
              <el-tag type="info" effect="dark" style="font-size: 15px;display: block;">涉轻工行业有限空间</el-tag>
            </template>
          </el-descriptions-item>
          <el-descriptions-item label="涉轻工行业有限空间" :span="1">
            {{data.is_light_industry}}
          </el-descriptions-item>
          <el-descriptions-item label="有限空间类型" :span="2" v-if="data.is_light_industry=='是'">
            {{data.limited_space_type}}
          </el-descriptions-item>

        </el-descriptions>
      </el-tab-pane>
      <el-tab-pane label="证书添加记录" :style="{height:height+'px'}">
        <el-table border
                  v-loading="loading"
                  :data="ca"
                  style="width: 100%;margin-bottom: 20px;"
                  ref="qtable"
                  :height="height"
                  size="small">
          <el-table-column
              prop="standard_name"
              label="企业安全生产标准化评定标准"
              align="center"
              show-overflow-tooltip
              min-width="200">
          </el-table-column>
          <el-table-column
              prop="level"
              label="企业安全生产标准化级别"
              align="center"
              min-width="100">
          </el-table-column>
          <el-table-column
              prop="standard"
              label="现场评审报告"
              align="center"
              min-width="100">
            <template slot-scope="scope">
              <el-image
                  style="width: 100px; height: 100px"
                  :src="scope.row.standardUrl"
                  :preview-src-list="[scope.row.standardUrl]">
              </el-image>
            </template>
          </el-table-column>
          <el-table-column
              prop="image"
              label="企业安全生产标准化证书"
              align="center"
              min-width="100">
            <template slot-scope="scope">
              <el-image
                  style="width: 100px; height: 100px"
                  :src="scope.row.imageUrl"
                  :preview-src-list="[scope.row.imageUrl]">
              </el-image>
            </template>
          </el-table-column>
          <el-table-column
              prop="code"
              label="企业安全生产标准化证书编号"
              align="center"
              min-width="100">
          </el-table-column>
          <el-table-column
              prop="date"
              label="证书有效期"
              align="center"
              min-width="100">
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="创标申请记录" :style="{height:height+'px'}">
        <el-table border
                  v-loading="loadingCreate"
                  :data="createApply"
                  style="width: 100%;margin-bottom: 20px;"
                  ref="createTable"
                  :height="height"
                  size="small">
          <el-table-column
              type="index"
              label="序号"
              align="center"
              width="50">
          </el-table-column>
          <el-table-column
              prop="company_name"
              label="企业名称"
              align="center"
              show-overflow-tooltip
              min-width="200">
          </el-table-column>
          <el-table-column
              prop="industry"
              label="行业/专业"
              align="center"
              show-overflow-tooltip
              min-width="100">
            <template slot-scope="scope">
              {{scope.row.industry}}/{{scope.row.specialty}}
            </template>
          </el-table-column>
          <el-table-column
              prop="standard_name"
              label="申请标准"
              align="center"
              show-overflow-tooltip
              min-width="100">
          </el-table-column>
          <el-table-column
              prop="level"
              label="评定级别"
              align="center"
              show-overflow-tooltip
              min-width="100">
          </el-table-column>
          <el-table-column
              prop="status"
              label="申请状态"
              align="center"
              show-overflow-tooltip
              width="120">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.status==1" type="info">待审核</el-tag>
              <el-tag v-if="scope.row.status==7" type="primary">审核通过</el-tag>
              <el-tag v-if="scope.row.status==5" type="success">已驳回</el-tag>
            </template>
          </el-table-column>
          <el-table-column
              prop="apply_date"
              label="申请时间"
              align="center"
              show-overflow-tooltip
              width="120">
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="定级申请记录" :style="{height:height+'px'}">
        <el-table border
                  v-loading="loadingLevel"
                  :data="levelApply"
                  style="width: 100%;margin-bottom: 20px;"
                  ref="levelTable"
                  :height="height"
                  size="small">
          <el-table-column
              type="index"
              label="序号"
              align="center"
              width="50">
          </el-table-column>
          <el-table-column
              prop="company_name"
              label="企业名称"
              align="center"
              show-overflow-tooltip
              min-width="200">
          </el-table-column>
          <el-table-column
              prop="level"
              label="申请等级"
              align="center"
              width="100">
          </el-table-column>
          <el-table-column
              prop="type"
              label="申请类型"
              align="center"
              width="100">
          </el-table-column>
          <el-table-column
              prop="nature"
              label="创建性质"
              align="center"
              min-width="100">
            <template slot-scope="scope">
              <div v-if="scope.row.nature==0" type="info">自主创建</div>
              <div v-if="scope.row.nature==1" type="primary">"{{scope.row.advisory}}"指导</div>
            </template>
          </el-table-column>
          <el-table-column
              width="150px"
              :show-overflow-tooltip="true"
              prop="prcs_name"
              label="当前办理阶段">
          </el-table-column>
          <el-table-column
              prop="status"
              label="申请状态"
              align="center"
              show-overflow-tooltip
              width="120">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.status==0" type="">未提交</el-tag>
              <el-tag v-if="scope.row.status==1" type="info">审批中</el-tag>
              <el-tag v-if="scope.row.status==2" type="info">整改中</el-tag>
              <el-tag v-if="scope.row.status==3" type="info">整改审批中</el-tag>
              <el-tag v-if="scope.row.status==7" type="primary">已通过</el-tag>
              <el-tag v-if="scope.row.status==5" type="danger">未通过</el-tag>
              <el-tag v-if="scope.row.status==9" type="danger">放弃评审</el-tag>
            </template>
          </el-table-column>
          <el-table-column
              prop="apply_time"
              label="申请时间"
              align="center"
              show-overflow-tooltip
              width="120">
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="自评报告">

                <el-table border
                          :data="selfEvaluationData"
                          style="width: 100%;margin-bottom: 20px;"
                          :height="height"
                          size="small">
                    <el-table-column
                            type="index"
                            label="序号"
                            align="center"
                            width="60">
                    </el-table-column>
                    <el-table-column
                            prop="year"
                            label="年度"
                            align="center"
                            width="80">
                    </el-table-column>
                    <el-table-column
                            prop="company_name"
                            label="企业名称"
                            align="center"
                            show-overflow-tooltip
                            min-width="200">
                    </el-table-column>
                    <el-table-column
                            prop="industry"
                            label="行业/专业"
                            align="center"
                            width="200">
                        <template slot-scope="scope">
                            {{scope.row.industry}}/{{scope.row.specialty}}
                        </template>
                    </el-table-column>
                    <el-table-column
                            prop="date"
                            label="自评日期"
                            align="center"
                            width="120">
                    </el-table-column>
                    <el-table-column
                            prop="score.total.score"
                            label="自评得分"
                            align="center"
                            width="100">
                    </el-table-column>
                    <el-table-column
                            label="操作"
                            align="center"
                            width="150">
                        <template slot-scope="scope">
                            <el-button v-if="scope.row.status>=2" type="primary" @click="preview(scope.row.files)" size="small">自评报告</el-button>
                        </template>
                    </el-table-column>
                </el-table>

            </el-tab-pane>
      <el-tab-pane label="登录用户" :style="{height:height+'px'}">
        <el-descriptions class="margin-top" title="登录用户信息" :column="2" border label-class-name="my-label" content-class-name="my-content">
          <el-descriptions-item label="用户名">
            {{loginUser.username}}
          </el-descriptions-item>
          <el-descriptions-item label="姓名">
            {{loginUser.name}}
          </el-descriptions-item>
          <el-descriptions-item label="联系电话">
            {{loginUser.mobile}}
          </el-descriptions-item>
          <el-descriptions-item label="邮箱">
            {{loginUser.email}}
          </el-descriptions-item>
          <el-descriptions-item label="注册时间">
            {{loginUser.reg_time}}
          </el-descriptions-item>
          <el-descriptions-item label="最后登录时间">
            {{loginUser.last_login_time}}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag v-if="loginUser.status==0" type="danger">禁用</el-tag>
            <el-tag v-if="loginUser.status==1" type="success">正常</el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </el-tab-pane>

    </el-tabs>
    <!--编辑联系人-->
  </el-dialog>
</template>
<script>
module.exports = {
  name: "add",
  // 模板导入区
  components: {
  },
  data:function() {
    return {
      id:0,
      isAdmin:false,
      visible: false,
      dialogFormVisible: false,
      title: '详情',
      loading: false,
      loadingCreate: false,
      loadingLevel: false,
      loadingCert: false,
      noMore: false,
      user_id: 0,
      data: {
      },
      loginUser: {
        username: '',
        name: '',
        mobile: '',
        email: '',
        reg_time: '',
        last_login_time: '',
        status: 1
      },
      is_see:0,
      ca:[],
      createApply: [],
      levelApply: [],
      certRecord: [],
      details:[],
      cards:{},
      type:{},
      pcas: [],
      restaurants:[],
      restaurants2:[],
      selfEvaluationData: [],
      height: document.documentElement.clientHeight - 250,
    }
  },
  computed: {
    disabled () {
      return this.loading || this.noMore
    }
  },
  mounted: function(){
    //this.getConfig();
  },
  created:function(){
  },
  methods: {
    /**
     * 打开弹窗调用方法
     * */
    open: function (row) {
      var _this =this;
      console.log(row)
      _this.id = row.id;
      _this.visible = true;
      _this.noMore = false;
      _this.getInfo(row.id);
      _this.getCa(row.id);
      _this.getCreateApply(row.id);
      _this.getLevelApply(row.id);
      _this.getLoginUser(row.id);
    },
    closeDialog: function () {
      this.visible = false;
    },
    getCa:function(id){
      var _this = this;
      if(!_this.noMore){
        _this.loading = true;
        axios.post('caList', {id:id}).then(function (res) {
          if (res.data.code == 0) {
            _this.ca = res.data.data;
          }
          _this.loading = false;
        }).catch(function (error) {
          _this.loading = false;
          console.log("出现错误:",error);
        });
      }
    },
    getInfo:function(id){
      var _this = this;
      if(id){
        axios.post("companyInfo", {
          id:id
        }).then(function (res) {
          if (res.data.code == 0) {
            _this.data=res.data.data;
            _this.selfEvaluationData = _this.data.selfEvaluationData;
          }else {
            _this.$message({
              message: res.data.msg,
              type: res.data.type
            });
          }
        }).catch(function (error) {
          console.log("出现错误:",error);
        });
      }
    },
    addContacts: function(){
      this.form = {};
      this.dialogFormVisible = true;
    },
    editContacts: function(row){
      this.form = row;
      this.dialogFormVisible = true;
    },
    getCreateApply: function(id){
      var _this = this;
      if(!_this.noMore){
        _this.loadingCreate = true;
        axios.post('createApplyList', {id:id}).then(function (res) {
          if (res.data.code == 0) {
            _this.createApply = res.data.data;
          }
          _this.loadingCreate = false;
        }).catch(function (error) {
          _this.loadingCreate = false;
          console.log("出现错误:",error);
        });
      }
    },
    getLevelApply: function(id){
      var _this = this;
      if(!_this.noMore){
        _this.loadingLevel = true;
        axios.post('levelApplyList', {id:id}).then(function (res) {
          if (res.data.code == 0) {
            _this.levelApply = res.data.data;
          }
          _this.loadingLevel = false;
        }).catch(function (error) {
          _this.loadingLevel = false;
          console.log("出现错误:",error);
        });
      }
    },
    getLoginUser: function(id){
      var _this = this;
      if(!_this.noMore){
        axios.post('getLoginUser', {id:id}).then(function (res) {
          if (res.data.code == 0) {
            _this.loginUser = res.data.data;
          }
        }).catch(function (error) {
          console.log("出现错误:",error);
        });
      }
    },
  }
}
</script>


