<?php

namespace app\api\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use app\company\model\GradingModel;
use app\model\FileModel;
use app\model\SettingModel;
use hg\apidoc\annotation as Apidoc;
use think\App;
use think\facade\Cache;
use think\facade\Db;
use app\api\model\SecsModel;

/**
 * @Apidoc\Title("专家端接口")
 * @Apidoc\Group("Index")
 * @Apidoc\Sort(9)
 */
class Expert extends Base {


    public function __construct(App $app)
    {
        parent::__construct($app);
        $token = $this->request->param('token');
        $user = Cache::get('token_'.$token);
        if($user['user_type']==='expert'){
            $this->user = $user['userinfo'];
        }else{
            //result('',8001,'暂无权限');
        }
    }

    /**
    * 
     * @Apidoc\Title("获取专家系统数据接口")
     * @Apidoc\Desc("获取专家系统数据接口")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("apiName", type="string",require=true, default="secsExpert/getAllCategory",desc="接口名称")
     * @Apidoc\Param("param", type="Object",require=false, default="{}",desc="参数")
     * @Apidoc\Param("method", type="string",require=false, default="post",desc="是否是post请求")
     * @Apidoc\Returned("code", type="int", desc="状态码")
     * @Apidoc\Returned("msg", type="string", desc="状态信息")
     * @Apidoc\Returned("data", type="string", desc="数据")
     **/
    public function getSecsData($apiName,$param=[],$method='post') {
        $res = SecsModel::getSecsInterfaceData($apiName,$param,$method);
        exit(json_encode($res));
    }


    /**
     * NotHeaders
     * @Apidoc\Title("获取评审的任务")
     */

    /**
     * NotHeaders
     * @Apidoc\Title("企业信息详情")
     * @Apidoc\Desc("企业信息详情")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Param("token", type="string",require=true, default="5f1052124fdb69cc11384c8619fb0e51",desc="登陆接口获取")
     * @Apidoc\Param("id", type="int",require=true, default="1",desc="企业id")
     * @Apidoc\Returned("id", type="int", desc="消息id")
     * @Apidoc\Returned("name", type="string", desc="企业名称")
     * @Apidoc\Returned("legal", type="string", desc="法定代表人")
     * @Apidoc\Returned("industry", type="string", desc="行业/专业")
     * @Apidoc\Returned("is_ca", type="string", desc="是否获得证书")
     * @Apidoc\Returned("mb_operate_address", type="string", desc="生产经营地点")
     * @Apidoc\Returned("postal_code", type="string", desc="统一社会信用代码")
     * @Apidoc\Returned("license_date", type="string", desc="信用代码有效期")
     * @Apidoc\Returned("reg_money", type="string", desc="注册资本")
     * @Apidoc\Returned("manager", type="string", desc="安全管理联系人")
     * @Apidoc\Returned("personnel", type="string", desc="员工总数")
     * @Apidoc\Returned("area", type="string", desc="营业场所面积")
     * @Apidoc\Returned("personnel_full", type="string", desc="专职安全管理人数")
     * @Apidoc\Returned("personnel_part", type="string", desc="兼职安全管理人数")
     * @Apidoc\Returned("personnel_special", type="string", desc="特种作业人数")
     * @Apidoc\Returned("mb_economy_sector", type="string", desc="经济类型")
     * @Apidoc\Returned("enterprise_size", type="string", desc="企业规模")
     * @Apidoc\Returned("date", type="string", desc="成立日期")
     */
    public function companyInfo($id=0) {
        $user = $this->user;
        $re = Db::table('top_company_info')->where(['id'=>$id])->find();
        $re = self::codeToText($re);
        $data = [
            'name' => $re['name'],
            'legal' => $re['legal'],
            'industry' => $re['industry'].'/'.$re['specialty'],
            'is_ca' => $re['ca_status']==1?'是':'否',
            'mb_operate_address' => $re['mb_operate_address'],
            'postal_code' => $re['postal_code'],
            'license_date' => $re['license_date'],
            'reg_money' => $re['reg_money'],
            'manager' => $re['manager'],
            'personnel' => $re['personnel'],
            'area' => $re['area'],
            'personnel_full' => $re['personnel_full'],
            'personnel_part' => $re['personnel_part'],
            'personnel_special' => $re['personnel_special'],
            'mb_economy_sector' => $re['mb_economy_sector'],
            'enterprise_size' => $re['enterprise_size'],
            'date' => $re['date'],
        ];
        result($data);
    }

    /**
     * NotHeaders
     * @Apidoc\Title("消息通知列表")
     * @Apidoc\Desc("消息通知列表")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Param("token", type="string",require=false, default="",desc="登陆接口获取")
     * @Apidoc\Param("limit", type="int",require=false, default="10",desc="每页展示数量")
     * @Apidoc\Param("page", type="int",require=false, default="1",desc="页码")
     * @Apidoc\Returned("data", type="list", desc="消息通知",children={
         * @Apidoc\Returned("id", type="int", desc="消息id"),
         * @Apidoc\Returned("sms_type", type="string", desc="消息类型"),
         * @Apidoc\Returned("sms_content", type="string", desc="消息内容"),
         * @Apidoc\Returned("sms_url", type="string", desc="消息链接"),
         * @Apidoc\Returned("sms_time", type="string", desc="消息时间"),
     *     })
     * @Apidoc\Returned("total", type="int", desc="数据总数")
     * @Apidoc\Returned("per_page", type="int", desc="每页数量")
     * @Apidoc\Returned("current_page", type="int", desc="当前页码")
     * @Apidoc\Returned("last_page", type="int", desc="最后一页页码")
     */
    public function message($limit=10) {
        $user = $this->user;
        if(in_array($user['user_type'],['company','city','area','org','expert'])) {
            $data = Db::table('top_message')
                ->where(['user_type' => $user['user_type'], 'user_id' => $user['user_id'], 'is_read' => 0])
                ->field('id,sms_type,sms_content,sms_url,sms_time')->order('sms_time desc')
                ->paginate($limit)->each(function ($item, $key) {
                    $item['sms_time'] = date('Y-m-d H:i',strtotime($item['sms_time']));
                    return $item;
                });
        }
        result($data);
    }

    /**
     * NotHeaders
     * @Apidoc\Title("消息通知详情")
     * @Apidoc\Desc("消息通知详情")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Param("token", type="string",require=true, default="5f1052124fdb69cc11384c8619fb0e51",desc="登陆接口获取")
     * @Apidoc\Param("id", type="int",require=true, default="",desc="消息id")
         * @Apidoc\Returned("id", type="int", desc="消息id")
         * @Apidoc\Returned("sms_type", type="string", desc="消息类型")
         * @Apidoc\Returned("sms_content", type="string", desc="消息内容")
         * @Apidoc\Returned("sms_url", type="string", desc="消息链接")
         * @Apidoc\Returned("sms_time", type="string", desc="消息时间")
     */
    public function messageInfo($id=0) {
        $user = $this->user;
        if(in_array($user['user_type'],['company','city','area','org','expert'])) {
            $data = Db::table('top_message')
                ->where(['user_type' => $user['user_type'], 'user_id' => $user['user_id'], 'id' => $id])
                ->field('id,sms_type,sms_content,sms_url,sms_time')->order('sms_time desc')
                ->find();
            $data['sms_time'] = date('Y-m-d H:i',strtotime($data['sms_time']));
        }
        result($data);
    }

    /**
     * NotHeaders
     * @Apidoc\Title("评审任务列表")
     * @Apidoc\Desc("评审任务列表")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("token", type="string",require=true, default="5f1052124fdb69cc11384c8619fb0e51",desc="登陆接口获取")
     * @Apidoc\Param("page", type="int",require=true, default="",desc="页码，默认-10")
     * @Apidoc\Param("status", type="int",require=true, default="",desc="状态")
     * @Apidoc\Returned("data", type="List", desc="任务列表",children={
     * @Apidoc\Returned("id", type="int", desc="id"),
     * @Apidoc\Returned("company_id", type="string", desc="企业id"),
     * @Apidoc\Returned("comapny_name", type="string", desc="企业名称"),
     * @Apidoc\Returned("level", type="string", desc="申请等级"),
     * @Apidoc\Returned("date", type="string", desc="评审日期"),
     * @Apidoc\Returned("status", type="string", desc="状态码"),
     * @Apidoc\Returned("mb_status", type="string", desc="状态"),
     * @Apidoc\Returned("status", type="string", desc="状态"),
     *     })
     */
    public function taskList($limit=20,$status=-10) {
        $user = $this->user;
        $where = [];
        if($status!=-10){
            $where[] = ['b.status','=',$status];
        }
        $where[] = ['c.expert_id','=',$user['user_id']];
        $field = "b.id,a.company_id,a.company_name,a.level,b.date,c.position_id,b.status,c.status estatus,c.element_name";
        $res = Db::table('top_grading')->alias('a')
            ->leftJoin('top_org_tasks b','a.id = b.grading_id')
            ->leftJoin('top_org_tasks_experts c','b.id = c.tasks_id')
            ->where($where)->order('b.date desc')
            ->field($field)
            ->paginate($limit)->each(function ($item, $key) {
                $e = Db::table('top_org_tasks_element')->where(['tasks_id'=>$item['id'],'expert_id'=>$_SESSION['expert']['id']])->column('element_name');
                $item['element_name'] = empty($e)?'未分配':implode('，',$e);
                $item['discuss'] = Db::table('top_discuss_group')->alias('a')
                    ->leftJoin('top_discuss_group_user b',"a.id = b.group_id and user_type='expert' and user_id = '{$_SESSION['expert']['id']}'")
                    ->where(['a.tasks_id'=>$item['id']])
                    ->field('a.*,b.readsum')->find();
                $item['discuss']['id'] = $item['discuss']['id']*1;
                $item['discuss']['readsum'] = $item['discuss']['readsum']<=0?'':$item['discuss']['readsum'];
                return $item;
            });
        result($res);
    }


    /**
     * NotHeaders
     * @Apidoc\Title("评审任务详情")
     * @Apidoc\Desc("评审任务详情")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("token", type="string",require=true, default="5f1052124fdb69cc11384c8619fb0e51",desc="登陆接口获取")
     * @Apidoc\Param("taskCode", type="int",require=true, default="",desc="评审任务code")
     * @Apidoc\Returned("id", type="int", desc="id")
     * @Apidoc\Returned("company_id", type="string", desc="企业id")
     * @Apidoc\Returned("comapny_name", type="string", desc="企业名称")
     * @Apidoc\Returned("address", type="string", desc="地址")
     * @Apidoc\Returned("legal", type="string", desc="法人")
     * @Apidoc\Returned("level", type="string", desc="申请等级")
     * @Apidoc\Returned("date", type="string", desc="评审日期")
     * @Apidoc\Returned("status", type="string", desc="状态码")
     * @Apidoc\Returned("mb_status", type="string", desc="状态")
     * @Apidoc\Returned("status", type="string", desc="状态")
     * @Apidoc\Returned("experts", type="List", desc="评审专家列表",children={
     * @Apidoc\Returned("expert_name", type="string", desc="专家姓名"),
     * @Apidoc\Returned("mobile", type="string", desc="手机号"),
     * @Apidoc\Returned("position", type="string", desc="角色"),
     *     })
     */
    public function taskInfo($taskCode=0){
        $user = $this->user;
        $where = [
            ['b.code','=',$taskCode],
            ['c.expert_id','=',$user['user_id']],
        ];
        $field = "b.id,a.company_id,a.company_name,a.level,a.address,a.legal,b.date,b.status";
        $res = Db::table('top_org_tasks')->alias('b')
            ->leftJoin('top_grading a','a.id = b.grading_id')
            ->leftJoin('top_org_tasks_experts c','b.id = c.tasks_id')
            ->where($where)->order('b.date desc')
            ->field($field)->find();
        $res['experts'] = Db::table('top_org_tasks_experts')->where(['tasks_id'=>$res['id']])->order('position desc')->field('expert_name,mobile,position')->select();
        $res['date'] = empty($res['date'])?'':$res['date'];
        $res['mb_status'] = config('global.task_status')[$res['status']];
        //如果评审任务还没有开始则不能显示名称
        if(!empty($res['date']) && $res['date'] > date('Y-m-d')){
            $res['company_name'] = '****';
        }
        result($res);
    }


    /**
     * NotHeaders
     * @Apidoc\Title("任务现场取证列表")
     * @Apidoc\Desc("任务现场取证列表")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("token", type="string",require=true, default="5f1052124fdb69cc11384c8619fb0e51",desc="登陆接口获取")
	 * @Apidoc\Param("task_id", type="string",require=true, default="",desc="任务ID")
	 * @Apidoc\Param("limit", type="int",require=false, default="10",desc="每页展示数量")
     * @Apidoc\Param("page", type="int",require=true, default="",desc="页码，默认1")
     * @Apidoc\Returned("id", type="int", desc="id"),
     * @Apidoc\Returned("task_id", type="string", desc="任务id"),
     * @Apidoc\Returned("hidden_cont", type="string", desc="隐患内容")
     * @Apidoc\Returned("hidden_pos", type="string", desc="隐患点位"),
     * @Apidoc\Returned("hidden_type", type="string", desc="隐患类型"),
     * @Apidoc\Returned("create_time", type="string", desc="创建时间"),
     * @Apidoc\Returned("status", type="string", desc="状态"),
     */
	   public function evidenceList($task_id=1,$page=1,$limit=20) {
		$user = $this->user;
		$where = [];
		$where[] = ['c.expert_id','=',$user['user_id']];
		$res = Db::table('top_evidence_main')
			->where('task_id',$task_id)
			->order('id desc')
			->paginate(['list_rows'=>$limit, 'page'=>$page])
			->each(function ($item, $key) {
				$e = Db::table('top_evidence_photo')
					->where(['main_id'=>$item['id']])
					->value('photo_path');
				if($e){
					$item['images'] = ['url'=>config("app.http_host").'general/toppingsoft/index.php/file/info?code='.$e,'code'=>$e];
				}else{
					$item['images']= [];
				}
				return $item;
			});
		result($res);
	  }


     /**
     * NotHeaders
     * @Apidoc\Title("添加任务现场取证")
     * @Apidoc\Desc("添加任务现场取证")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("task_id", type="string",require=true, default="",desc="任务ID")
     * @Apidoc\Param("hidden_type", type="string",require=true, default="",desc="隐患类型")
	 * @Apidoc\Param("hidden_pos", type="string",require=true, default="",desc="隐患点位")
	 * @Apidoc\Param("hidden_cont", type="string",require=true, default="",desc="隐患内容")
     * @Apidoc\Param("images", type="List", desc="附件列表",children={
     * @Apidoc\Param("code", type="string", desc="图片标识"),
     * @Apidoc\Param("id", type="string", desc="图片ID"),
     * @Apidoc\Param("name", type="string", desc="图片名称"),
     * @Apidoc\Param("url", type="string", desc="图片URL"),
     * })
     */
    public function addEvidence() {
        $user = $this->user;
        Db::startTrans();
        try{
        $param = request()->param();
		$insertData = [
			'task_id'=>$param['task_id'],
			'hidden_type'=>$param['hidden_type'],
			'hidden_pos'=>$param['hidden_pos'],
			'hidden_cont'=>$param['hidden_cont'],
			'create_time'=>time(),
		];
		$mainId = Db::table('top_evidence_main')->insertGetId($insertData);
        unset($insertData['hidden_cont']);
        $mainId = Db::table('top_evidence_main')->where($insertData)->field('id')->find()['id'];//兼容达梦
        $images = $param['images'];
        if(count($images)>0){
            foreach ($images as $k=>$v){
                $photoData[] = [
                    'main_id'=>$mainId,
                    'photo_path'=>$v['code'],
                ];
            } 
           $res = Db::table('top_evidence_photo')->insertAll($photoData);
        }
        Db::commit();
        }catch(\Exception $e){
            Db::rollback();
            result('',8001,$e->getMessage());
        }
        result($param);
    }

     /**
     * NotHeaders
     * @Apidoc\Title("删除任务现场取证")
     * @Apidoc\Desc("删除任务现场取证")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("id", type="string",require=true, default="",desc="取证主表ID")
     */
    public function delEvidence() {
        $user = $this->user;
        Db::startTrans();
        try{
        $param = request()->param();
		$where = [
			'id'=>$param['id'],
		];
		$re = Db::table('top_evidence_main')->where($where)->find();
		if(!$re){
			result('',8001,"记录不存在！");
		}
		Db::table('top_evidence_main')->where($where)->delete();
        Db::table('top_evidence_photo')->where(['main_id'=>$param['id']])->delete();
        Db::commit();
        }catch(\Exception $e){
            Db::rollback();
            result('',8001,$e->getMessage());
        }
        result($param);
    }

    //参数格式化
    public static function codeToText($info){
        if(empty($info)){
            return $info;
        }
        $pcas = SettingModel::getPcasAll();
        $info['reg_address'] = empty($info['reg_address'])?[]:explode(',',$info['reg_address']);
        $info['mb_reg_address'] = '';
        foreach ($info['reg_address'] as $k=>$v){
            $info['mb_reg_address'] .= $pcas[$v];
        }
        $info['mb_reg_address'] .= $info['reg_address_info'];
        $info['operate_address'] = empty($info['operate_address'])?[]:explode(',',$info['operate_address']);
        $info['mb_operate_address'] = '';
        foreach ($info['operate_address'] as $k=>$v){
            $info['mb_operate_address'] .= $pcas[$v];
        }
        $info['mb_operate_address'] .= $info['operate_address_info'];
        $info['region'] = empty($info['region'])?[]:explode(',',$info['region']);
        $info['mb_region'] = '';
        foreach ($info['region'] as $k=>$v){
            $info['mb_region'] .= $pcas[$v];
        }
        $info['economy_sector'] = explode(',',$info['economy_sector']);
        $info['mb_economy_sector'] = implode('/',$info['economy_sector']);
        $info['economy_type'] = explode(',',$info['economy_type']);
        $info['mb_economy_type'] = implode('/',$info['economy_type']);
        $info['license_date'] = $info['license_start'].'至'.$info['license_end'];
        $info['licenseUrl'] = empty($info['license'])?'':FileModel::getFile(0,$info['license']);
        $info['aocUrl'] = empty($info['aoc'])?'':FileModel::getFile(0,$info['aoc']);
        foreach ($info as $k=>$v){
            $info[$k] = $v==='0000-00-00'?'':$v;
            if(is_int($v)){
                $info[$k] = (string)$v;
            }
            if($v===null){
                $info[$k] = '';
            }
        }
        return $info;
    }

    public function wemobile($code='',$token='') {
        $wx = new WxModel();
        $res = $wx->getuserphone($code);
        $user = Cache::get('token_'.$token);
        if(empty($user)){
            result('',9001,'请先登陆');
        }
        $file = '/opt/tdoa/webroot/general/toppingsoft/public/api.txt';
        file_put_contents($file,json_encode($user)."\n",FILE_APPEND);
        $user = self::findUser('',$res['phone_info']['purePhoneNumber'],$user['openid'],$user['unionid']);
        $data = [
            'token' => $token,
            'user' => [
                'user_type' => $user['user_type'],
                'name' => $user['userinfo']['name'],
                'head' => $user['userinfo']['head'],
                'position' => $user['userinfo']['position'],
            ],
        ];
        Cache::set('token_'.$token,$user);
        result($data);
    }

}
