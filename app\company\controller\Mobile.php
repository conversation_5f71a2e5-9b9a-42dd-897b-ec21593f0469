<?php

namespace app\company\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use app\company\model\CompanyModel;
use app\company\model\GradingModel;
use app\validate\GradingVerify as Verify;
use app\model\SettingModel;
use hg\apidoc\annotation as Apidoc;
use think\facade\View;
use think\Request;
use think\facade\Db;
use app\model\FileModel;
use DateTime;

/**
 * @Apidoc\Title("移动端接口")
 * @Apidoc\Group("Index")
 * @Apidoc\Sort(1)
 */
class Mobile extends Base {

    /**
     * @Apidoc\Title("企业登陆")
     * @Apidoc\Desc("企业登陆")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function login() {
        $user = $this->user;
    }
    /**
     * @Apidoc\Title("企业首页")
     * @Apidoc\Desc("企业首页")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function index() {
        $user = $this->user;
        $data['message'] = Db::table('top_message')->where(['user_type'=>'company','user_id'=>$user['user_id'],'is_read'=>0])->order('sms_time desc')->field('id,sms_type,sms_content,sms_url,sms_time')->find();
        result($data);
    }


}
