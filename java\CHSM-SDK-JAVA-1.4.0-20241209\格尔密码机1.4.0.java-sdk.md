
# 格尔密码机 Java SDK 文档

## 概述

格尔密码机Java SDK 是一个用于访问 密码机服务的 Java 库，提供了方便的 API 和高性能的客户端。

密码机服务Java SDK 适用于 JDK 8-11 版本，支持gmssl网络协议。

## 安装
您可以通过以下方式安装 密码机服务 Java SDK：
- 使用 Maven：在 pom.xml 文件中添加以下依赖(sdk需要依赖bc库，版本1.6.4以上，建议使用1.7.0)：

```xml
<dependency>
    <groupId>kl.hsm.client</groupId>
    <artifactId>hsm-sdk-java</artifactId>
    <version>1.4.0</version>
    <scope>system</scope>
    <systemPath>/path/sdk.jar</systemPath>
</dependency>
<dependency>
    <groupId>org.bouncycastle</groupId>
    <artifactId>bcprov-jdk15on</artifactId>
    <version>1.7.0</version>
</dependency>
<dependency>
    <groupId>org.bouncycastle</groupId>
    <artifactId>bcpkix-jdk15on</artifactId>
    <version>1.7.0</version>
</dependency>
<dependency>
    <groupId>org.bouncycastle</groupId>
    <artifactId>bcprov-ext-jdk15on</artifactId>
    <version>1.7.0</version>
</dependency>
```
## 快速入门

要使用 格尔密码机 Java SDK，您需要先创建一个 HsmClient对象，然后调用其相应的方法。以下是一个示例：：

```java
// 导入 SDK 包
import kl.hsm.client.model.SessionKey;
import kl.hsm.client.sdk.HsmClient;
import kl.hsm.common.KeyType;
import kl.hsm.server.svc.base.*;
import org.bouncycastle.util.encoders.Hex;

public class Demo {

    //支持多线程
    private static HsmClient hsmClient = new HsmClient();
    // 私钥授权码-管理页面创建非对称密钥时填入
    private static String password = "pass";

    public static void main(String[] args) {
        String ip = "**********";
        //ip="**********,**********"; 多服务配置方式，
        int port = 10000;
        //连接池大小（每个IP），建议按实际情况配置，
        int poolMax = 4;
        //String keys = "/path/client.p12"; 证书在管理页面下载
        //String trusts = "/path/ca.cer";
        try {
            hsmClient.init(ip, port, poolMax);
            //hsmClient.init(ip, port, true, keys, trusts, poolMax); 开启gmssl
            //hsmSdk.init("/path/HsmConfig.properties"); 配置文件方式初始化
        } catch (SvcException e) {
            System.out.println("初始化连接池失败:" + e.getErrCode() + ":" + e.getErr());
        }

        try {
            System.out.println("-------------对称加解密 SM4 CBC例子-------------");
            byte[] data = "123456781234567812".getBytes();
            byte[] iv = "1234567812345678".getBytes();
            //组装对称加密参数
            SymParam symParam = new SymParam();
            //工作模式
            symParam.setMode(EncMode.CBC);
            //初始向量
            symParam.setIv(iv);

            //使用内部密钥加解密
            int sm4KeyIndex = 1;
            byte[] encrypt = hsmClient.encrypt(sm4KeyIndex, symParam, data);
            System.out.println("加密数据:" + Hex.toHexString(encrypt));
            byte[] decrypt = hsmClient.decrypt(sm4KeyIndex, symParam, encrypt);
            System.out.println("解密数据：" + new String(decrypt));

            //使用外部密钥对称加密
            byte[] key = hsmClient.getRandom(16);
            encrypt = hsmClient.encrypt(Algo.SM4, key, symParam, data);
            System.out.println("加密数据:" + Hex.toHexString(encrypt));
            decrypt = hsmClient.decrypt(Algo.SM4, key, symParam, encrypt);
            System.out.println("解密数据：" + new String(decrypt));
				
			//使用会话密钥对称加解密，由于获取session比较耗时，可以考虑重用session。参考文档 (常见问题-处理session问题)
            //建立会话获得会话句柄
            long session = hsmClient.openSession();
            //生成16字节的会话密钥,使用内部加密密钥加密会话密钥。(需要保存sessionKeyCipher)
            SessionKey sessionKey = hsmClient.generateSessionKey(session, sm4KeyIndex, KeyType.SYMMETRIC, 16);
            
            // 加密
            encrypt = hsmClient.encrypt(session, sessionKey.getSessionKeyHandle(), Algo.SM4, symParam, data);
            System.out.println("加密数据:" + Hex.toHexString(encrypt));
            // 销毁会话密钥
            hsmClient.destorySessionKey(session,sessionKey.getSessionKeyHandle());
            hsmClient.closeSession(session);
            
            // 解密 通常加解密是分开操作，所以使用前需要先将加密时生成的会话密钥导入当前会话
            long session1 = hsmClient.openSession();
            int sessionKeyHandle = hsmClient.importSessionKeyCipher(session1, sm4KeyIndex, KeyType.SYMMETRIC, sessionKey.getSessionKeyCipher());
            decrypt = hsmClient.decrypt(session1, sessionKeyHandle, Algo.SM4, symParam, encrypt);
            System.out.println("解密数据：" + new String(decrypt));
            hsmClient.destorySessionKey(session,sessionKey.getSessionKeyHandle());
            hsmClient.closeSession(session1);

            System.out.println("-------------非对称加解密 SM2例子-------------");
            AsymKeypair asymKeypair = hsmClient.generateAsymKeyPair(Algo.SM2);
            byte[] pubKey = asymKeypair.getPubKey();
            byte[] privKey = asymKeypair.getPrivKey();
            int sm2KeyIndex = 110;
            //外部密钥加解密
            encrypt = hsmClient.asymEncrypt(Algo.SM2, pubKey, data);
            System.out.println("解密数据:" + Hex.toHexString(encrypt));
            decrypt = hsmClient.asymDecrypt(Algo.SM2, privKey, encrypt);
            System.out.println("解密数据：" + new String(decrypt));
            
            //内部密钥加解密
            encrypt = hsmClient.asymEncrypt(sm2KeyIndex, data);
            System.out.println("解密数据:" + Hex.toHexString(encrypt));
            //使用内部私钥需要先授权
            session = hsmClient.openSession();
            hsmClient.getPrivateKeyAccessRight(session, sm2KeyIndex, password);
            decrypt = hsmClient.asymDecrypt(session, sm2KeyIndex, encrypt);
            System.out.println("解密数据：" + new String(decrypt));
            hsmClient.closeSession(session);

            System.out.println("-------------非对称签名验签 SM2例子-------------");
            //外部密钥签名验签
            byte[] digest = hsmClient.digest(data, Algo.SM3);
            byte[] sign = hsmClient.sign(Algo.SM2, privKey, digest, Algo.SM3);
            System.out.println("签名数据:" + Hex.toHexString(sign));
            boolean verify = hsmClient.verify(Algo.SM2, pubKey, digest, Algo.SM3, sign);
            System.out.println("验签结果:" + verify);
            
            // 内部密钥签名验签
            // 获取私钥使用权限
            session = hsmClient.openSession();
            hsmClient.getPrivateKeyAccessRight(session, sm2KeyIndex, password);
            sign = hsmClient.sign(session, sm2KeyIndex, digest, Algo.SM3);
            System.out.println("签名数据:" + Hex.toHexString(sign));
            verify = hsmClient.verify(sm2KeyIndex, digest, Algo.SM3, sign);
            System.out.println("验签结果:" + verify);

            System.out.println("-------------摘要例子-------------");
            Algo[] algos = new Algo[]{Algo.MD5, Algo.SHA1, Algo.SHA2_256, Algo.SHA2_384, Algo.SHA2_512, Algo.SM3};
            for (Algo algo : algos) {
                digest = hsmClient.digest(data, algo);
                System.out.println(algo.name() + ":" + Hex.toHexString(digest));
            }
            System.out.println("-------------MAC例子-------------");
            //使用内部密钥计算mac
            byte[] cmac = hsmClient.cmac(sm4KeyIndex, Algo.SM4, data, iv);
            System.out.println("cmac:" + Hex.toHexString(cmac));
            byte[] hmac = hsmClient.hmac(sm4KeyIndex, Algo.SM3, data);
            System.out.println("hmac:" + Hex.toHexString(hmac));
            byte[] sm4gcmiv = "123456781234".getBytes();
            byte[] aad = "1234".getBytes();
            byte[] gmac = hsmClient.gmac(sm4KeyIndex, Algo.SM4, data, sm4gcmiv, aad);
            System.out.println("gmac:" + Hex.toHexString(gmac));

        } catch (SvcException e) {
            System.out.println("调用加密机异常:" + e.getErrCode() + ":" + e.getErr());
        }
    }
}
```

## API 参考

格尔密码机 Java SDK 提供了以下 API 类和方法，您可以根据您的需求选择合适的 API 进行操作。

### HsmSdk 类

HsmSdk 类是 格尔密码机 Java SDK 的核心类，提供了与密码机服务进行交互的方法。您需要创建一个 HsmSdk 对象，并传入您使用的密码机服务ip和端口参数，然后调用其相应的方法。

### Algo 类
Algo 类是密码机支持算法的枚举类，使用时可以对照<密码机支持的算法>项来使用。

### SymParam 类
SymParam 类是用于对称算法、cmac、gmac等接口，在调用时指定对应的iv、mode、padding、aad等参数。
使用时可以对照<密码机支持的算法>项来使用。

### EncMode 类
EncMode 类是用于对称算法、cmac、gmac等接口，指定使用对称算法使用的模式。使用时可以对照<密码机支持的算法>项来使用。

### SvcException 类
SvcException 类是在调用接口时统一抛出的异常类，sdk的调用逻辑为调用密码机服务失败时会抛出对应错误码的异常信息。使用时可以对照<错误码表>项来使用。



# HsmSdk接口列表
## 核心接口
### 1.初始化连接池
```java
// 连接池支持多线程，使用时全局生成一个就可以
void init(String ip, int port, boolean ssl, String keyStore, String trustStore, int poolMax) throws SvcException
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| ip | String | 密码服务ip(多服务ip之间使用逗号分割) |
| port | int | 端口号（10000） |
| ssl | boolean | 是否开启gmssl通道 true开启 false关闭 |
| keyStore | String | 开启gmssl 使用的证书路径 |
| trustStore | String | 开启gmssl使用的证书路径 |
| poolMax | int | 连接池最大连接数 |
#### 返回
无
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 初始化失败 |

### 2.关闭连接池
```java
void closePool()
```
#### 参数
无
#### 返回
无
#### 异常
无

### 3.获取会话
```java
// 不做任何操作情况下，默认3小时失效，开启session会占用服务端资源，不用需关闭
long openSession()throws SvcException
```
#### 参数
无
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| session | long | 会话 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 获取会话失败 |

### 4.关闭指定的会话
```java
void closeSession(long session)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| session| Long | 会话 |
#### 返回
无
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 关闭会话失败 |

### 5.获取设备的基本信息，如型号、序列号、版本号等
```java
String getDeviceInfo()
```
#### 参数
无
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| deviceInfo | String | 设备信息 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 获取设备信息失败 |

### 6.获取指定长度的随机数 
```java
byte[] generateRandom(int len)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| len | int | 随机数字节长度 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| random | byte[] | 随机数 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 获取随机数失败 |

### 7.获取指定索引的私钥使用权限
```java
void getPrivateKeyAccessRight(long session,int keyIndex,String password)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| session | long | 会话 |
| keyIndex | int | 密钥索引 |
| password | String | 密钥口令 |
#### 返回
无
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 获取私钥使用权限失败 |

### 8.释放指定索引的私钥使用权限
```java
void releasePrivateKeyAccessRight(long session,int keyIndex)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| session | long | 会话 |
| keyIndex | int | 密钥索引 |
#### 返回
无
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 释放私钥使用权限失败 |

### 9.导出指定索引的非对称密钥公钥
```java
byte[] exportPublicKey(int keyIndex,AsymType type)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| keyIndex | int | 密钥索引 |
| type | AsymType | 非对称密钥类型，加解密、签名验签 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| pubKey | byte[] | 公钥 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 导出加密密钥失败 |

### 10.生成指定算法的非对称密钥对
```java
AsymKeypair generateAsymKeyPair(Algo algo)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| algo | Algo | 算法 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| keypair | AsymKeypair | 密钥对 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 生成密钥对失败 |

### 11.信封转换
```java
//将使用指定的索引密钥公钥加密的数据，转为使用外部输入的公钥加密输出
byte[] exchangeEnvelope(long sessionHandle, int keyIndex, Algo pubKeyAlgo, byte[] pubKey, byte[] data)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| sessionHandle | long | 会话 |
| keyIndex | int | 密钥索引 |
| pubKeyAlgo | Algo | 算法 |
| pubKey | byte[] | 外部公钥 |
| data | byte[] | 用内部密钥加密加密的数据 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| encData | byte[] | 外部公钥加密输出 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 转加密失败 |

## 会话密钥相关操作

### 1.生成指定长度的会话密钥，并以指定索引的非对称密钥公钥或对称密钥加密输出
```java
SessionKey generateSessionKey(long sessionHandle,int keyIndex, KeyType type, int keyLen)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| sessionHandle | long | 会话 |
| keyIndex | int | 内部密钥索引 （不支持SM9）|
| type | KeyType | 密钥类型，对称密钥、非对称密钥 |
| keyLen | int | 会话密钥长度 （使用RSA密钥长度最大为公钥的bits/8-11）|
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| sessionKeyHandle | int | 会话密钥索引 |
| sessionKeyCipher | byte[] | 会话密钥密文 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 生成或加密会话密钥失败 |

### 2.生成指定长度的会话密钥，并以外部输入的公钥加密输出
```java
SessionKey generateSessionKey(long sessionHandle, Algo pubKeyAlgo, byte[] pubKey, int keyLen)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| sessionHandle | long | 会话 |
| pubKeyAlgo | Algo | 算法（不支持SM9） |
| pubKey | byte[] | 外部公钥 |
| keyLen | int | 会话密钥长度 （使用RSA密钥长度最大为公钥的bits/8-11）|
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| sessionKeyHandle | int | 会话密钥索引 |
| sessionKeyCipher | byte[] | 会话密钥密文 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 生成或加密会话密钥失败 |

### 3.导入指定索引的非对称密钥加密的会话密钥
```java
int importSessionKeyCipher(long sessionHandle, int keyIndex, KeyType type, byte[] data)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| sessionHandle | long | 会话 |
| keyIndex | int | 密钥索引 |
| type | AsymType | 非对称密钥类型，加解密、签名验签 |
| data |byte[] | 会话密钥密文 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| sKeyIndex | int | 会话密钥索引 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 导入会话密钥失败 |

###  4.导入明文会话密钥
```java
int importSessionKey(long sessionHandle, byte[] data) 
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| sessionHandle | long | 会话 |
| data | byte[] | 会话密钥明文 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| sKeyHandle | int | 会话密钥索引 |
#### 异常

| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 导入会话密钥失败 |

###  5 销毁会话密钥
```java
void destorySessionKey(long sessionHandle, int sessionKeyHandle)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| sessionHandle | long | 会话 |
| sessionKeyHandle | int | 会话密钥索引 |
#### 返回
无
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 销毁会话密钥失败 |



## SM2密钥协商

### 1.生成密钥协商参数并输出 
```java
// 生成指定算法和长度的密钥协商参数，并输出协商密钥公钥和临时公钥
keyAgreementResp initiateAgreementKey(long sessionHandle, int keyIndex, int keyLen, byte[] spId, Algo algo)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| sessionHandle | long | 会话 |
| keyIndex | int | 私钥索引 |
| keyLen | int | 数据bit长度 |
| spID | byte[] | 默认“1234567812345678” |
| algo | Algo | 算法 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| resp | keyAgreementResp | 协商密钥公钥和临时公钥 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 生成或输出密钥协商参数失败 |

### 2.计算会话密钥
```java
// 根据协商密钥公钥、临时公钥和句柄，计算会话密钥
int generateAgreementKey(long sessionHandle, byte[] respId, byte[] spPubKey, byte[] spTmpPubKey, int handle, Algo algo) 
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| sessionHandle | long | 会话 |
| respID | byte[] | 默认“1234567812345678” |
| spPubKey | byte[] | 协商密钥公钥 |
| spTmpPubKey | byte[] | 协商密钥临时公钥 |
| handle | int | 协商密钥句柄 |
| algo | Algo | 算法 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| sKeyHandle | int | 会话密钥句柄 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 计算会话密钥失败 |

### 3.产生协商数据并计算会话密钥
```java
// 产生协商密钥公钥和临时公钥，并根据对方的协商密钥公钥和临时公钥，计算会话密钥
keyAgreementResp keyAgreementResp respondAgreementKey(long sessionHandle, int keyIndex, int keyLen, byte[] respId, byte[] spId, byte[] spPubKey, byte[] spTmpPubKey, Algo algo)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| sessionHandle | long | 会话 |
| privKeyIndex | int | 私钥索引 |
| keyBits | int | 数据bit长度 |
| respID | byte[] | 默认“1234567812345678” |
| spID | byte[] | 默认“1234567812345678” |
| spPubKey | byte[] | 协商密钥公钥 |
| spTmpPubKey | byte[] | 临时公钥 |
| algo | Algo | 算法 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| resp | keyAgreementResp | 协商密钥公钥、临时公钥和句柄 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 产生或计算会话密钥失败 |


## 非对称算法加解密

###  1.外部非对称密钥加密
```java
byte[] asymEncrypt(Algo algo, byte[] pubKey, byte[] data)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| algo | Algo | 算法 |
| pubKey | byte[] | 公钥 |
| data | byte[] | 加密数据 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| dataEncrypted | byte[] | 数据密文 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| Exception | 非对称加密失败 |

###  2.外部非对称密钥解密
```java
byte[] asymDecrypt(Algo algo, byte[] priKey, byte[] data)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| algo | Algo | 算法 |
| priKey | byte[] | 私钥 |
| data | byte[] | 密文 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| dataDecrypted | byte[] | 数据明文 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| Exception | 非对称解密失败 |


###  3.内部索引密钥非对称加密
```java
byte[] asymEncrypt(int keyIndex, byte[] data)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| keyIndex | int | 密钥索引 |
| data |  byte[] | 加密数据 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| dataEncrypted |  byte[] | 数据密文 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 内部非对称加密失败 |

###  4.内部索引密钥非对称解密
```java
byte[] asymDecrypt(long sessionHandle, int keyIndex, byte[] data)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| sessionHandle | long | 会话|
| keyIndex | int | 密钥索引 |
| data | byte[]  | 解密数据 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| dataDecrypted | byte[]  | 数据明文 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 内部非对称解密失败 |

## 非对称算法签名验签
###  1.外部非对称密钥数据签名
```java
byte[] sign(Algo algo, byte[] privKey, byte[] digest, Algo digestAlgo) 
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| algo | Algo | 非对称加密算法 |
| privKey | byte[] | 外部私钥，注意与算法匹配 |
| digest | byte[] | 待签名数据的摘要值 |
| digestAlgo | Algo | 计算摘要时使用的参数 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| dataSigned | byte[] | 签名数据 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 外部签名失败 |

###  2.外部非对称密钥数据验签
```java
boolean verify(Algo algo, byte[] pubKey, byte[] digest, Algo digestAlgo, byte[] signData)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| algo | Algo | 非对称加密算法 |
| pubKey | byte[] | 外部公钥，注意与算法匹配 |
| digest | byte[] | 待签名数据的摘要值 |
| digestAlgo | Algo | 计算摘要时使用的参数 |
| signData | byte[] | 签名结果 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| verifyResult | boolean | 验签结果 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 外部验签失败 |

###  3.内部索引密钥数据签名
```java
byte[] sign(long sessionHandle, int keyIndex, byte[] digest, Algo digestAlgo)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| sessionHandle | long | 会话 |
| keyIndex | int | 密钥索引 |
| digest | byte[] | 摘要数据 |
| digestAlgo | Algo | 计算摘要时使用的参数 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| dataSigned | byte[] | 签名数据 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 内部签名失败 |

###  4.内部索引密钥数据验签
```java
boolean verify(int keyIndex, byte[] digest, Algo digestAlgo, byte[] signData) 
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| keyID | int | 密钥索引 |
| digest | byte[] | 摘要数据 |
| digestAlgo | Algo | 计算摘要时使用的参数 |
| signData | byte[] | 签名数据 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| verifyResult | Boolean | 验签成功或失败 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 内部验签失败 |

## 对称算法解密解密

###  1.会话密钥对称加密
```java
byte[] encrypt(long sessionHandle, int sessionKeyHandle, Algo algo, SymParam param, byte[] data)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| sessionHandle | long | 会话 |
| sessionKeyHandle | int | 会话密钥索引 |
| algo | Algo | 算法 (不支持Des和TriDES)|
| param | SymParam | 对称加密参数（包含工作模式，初始向量，padding模式，aad） |
| data | byte[] | 待加密数据 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| dataEncrypted | byte[] | 加密数据 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 使用会话密钥进行对称加密失败 |

###  2.会话密钥对称解密
```java
byte[] decrypt(long sessionHandle, int sessionKeyHandle, Algo algo, SymParam param, byte[] data)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| sessionHandle | long | 会话 |
| sessionKeyHandle | int | 会话密钥索引 |
| algo | Algo | 算法 |
| param | SymParam | 对称加密参数（包含工作模式，初始向量，padding模式，aad） |
| data | byte[] | 解密数据 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| dataDecrypted | byte[] | 数据明文 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 使用会话密钥进行对称解密失败 |

###  3.使用外部密钥做对称加密
```java
byte[] encrypt(Algo algo, byte[] key, SymParam param, byte[] data)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| algo | Algo | 对称加密算法 |
| key | byte[] | 对称加密密钥，注意长度需与算法匹配 |
| param | SymParam | 对称加密参数 |
| data | byte[] | 待加密数据 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| dataEncrypted | byte[] | 返回密文 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 外部密钥对称加密失败 |


###  4.使用外部密钥做对称解密
```java
byte[] decrypt(Algo algo, byte[] key, SymParam param, byte[] data)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| algo | Algo | 对称加密算法 |
| key | byte[] | 对称加密密钥，注意长度需与算法匹配 |
| param | SymParam | 对称加密参数 |
| data | byte[] | 密文数据 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| dataDecrypted | byte[] | 返回明文 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 外部密钥对称解密失败 |


###  5.使用内部索引密钥做对称加密
```java
byte[] encrypt(int keyIndex, SymParam param, byte[] data)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| keyIndex | int | 加密机内部对称密钥索引。使用时注意其对应算法 |
| param | SymParam | 对称加密参数 |
| data | byte[] | 待加密数据 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| dataEncrypted | byte[] | 返回密文 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 内部密钥对称加密失败 |


###  6.使用内部索引密钥做对称解密
```java
byte[] decrypt(int keyIndex, SymParam param, byte[] data)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| keyIndex | int | 加密机内部对称密钥索引。使用时注意其对应算法 |
| param | SymParam | 对称加密参数 |
| data | byte[] | 密文 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| dataDecrypted | byte[] | 返回明文 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 内部密钥对称解密失败 |


## MAC计算

###  1.会话密钥计算cmac
```java
byte[] cmac(long sessionHandle, int sessionKeyHandle, Algo algo, byte[] data, byte[] iv)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| sessionHandle | long | 会话 |
| sessionKeyHandle | int | 会话密钥句柄 |
| algo | Algo | 算法（参照cmac算法，des和TriDES不支持） |
| data | byte[] | 明文数据 |
| iv | byte[] | 初始向量 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| dataMac | byte[] | 计算mac后得到的数据 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 计算mac失败 |

###  2.会话密钥计算hmac
```java
byte[] hmac(long sessionHandle, int sessionKeyHandle, Algo digestAlgo, byte[] data)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| sessionHandle | long | 会话 |
| sessionKeyHandle | int | 会话密钥句柄 |
| digestAlgo | Algo | 算法 （参照hmac算法）|
| data | byte[] | 明文数据 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| dataMac | byte[] | 计算mac后得到的数据 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 计算mac失败 |

###  3.会话密钥计算gmac
```java
byte[] gmac(long sessionHandle, int sessionKeyHandle, Algo algo, byte[] data, byte[] iv, byte[] aad)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| sessionHandle | long | 会话 |
| sessionKeyHandle | int | 会话密钥句柄 |
| algo | Algo | 算法（参照gmac算法） |
| data | byte[] | 明文数据 |
| iv | byte[] | 初始向量 |
| aad | byte[] |认证数据 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| dataMac | byte[] | 计算mac后得到的数据 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 计算mac失败 |


###  4.外部密钥计算cmac
```java
byte[] cmac(byte[] key, Algo algo, byte[] data, byte[] iv)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| key | byte[] | 对称密钥 |
| algo | Algo | 算法 （参照cmac算法）|
| data | byte[] | 数据 |
| iv | byte[] | 初始向量 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| dataMac | byte[] | 计算mac后得到的数据 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 计算mac失败 |

###  5.外部密钥计算hmac
```java
byte[] hmac(byte[] key, Algo digestAlgo, byte[] data)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| key | byte[] | 对称密钥 |
| digestAlgo | Algo | 算法（参照hmac算法） |
| data | byte[] | 数据 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| dataMac | byte[] | 计算mac后得到的数据 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 计算mac失败 |

###  6.外部密钥计算gmac
```java
byte[] gmac(byte[] key, Algo algo, byte[] data, byte[] iv, byte[] aad)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| key | byte[] | 对称密钥 |
| algo | Algo | 算法（参照gmac算法）|
| data | byte[] | 数据 |
| iv | byte[] | 初始向量 |
| aad | byte[] |认证数据 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| dataMac | byte[] | 计算mac后得到的数据 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 计算mac失败 |


###  7.内部索引密钥计算cmac
```java
byte[] cmac(int keyIndex, Algo algo, byte[] data, byte[] iv)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| keyIndex | int | 密钥索引 |
| algo | Algo | 算法（参照cmac算法） |
| data | byte[] | 数据 |
| iv | byte[] | 初始向量 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| dataMac | byte[] | 计算mac后得到的数据 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 计算mac失败 |

###  8.内部索引密钥计算hmac
```java
byte[] hmac(int keyIndex, Algo digestAlgo, byte[] data)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| keyIndex | int | 密钥索引 |
| digestAlgo | Algo | 算法（参照hmac算法） |
| data | byte[] | 数据 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| dataMac | byte[] | 计算mac后得到的数据 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 计算mac失败 |

###  9.内部索引密钥计算gmac
```java
byte[] gmac(int keyIndex, Algo algo, byte[] data, byte[] iv, byte[] aad)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| keyIndex | int | 密钥索引 |
| algo | Algo | 算法 （参照gmac算法）|
| data | byte[] | 数据 |
| iv | byte[] | 初始向量 |
| aad | byte[] |认证数据 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| dataMac | byte[] | 计算mac后得到的数据 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 计算mac失败 |


## 摘要计算
###  1.计算摘要
```java
byte[] digest(byte[] data, Algo algo)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| data | byte[] | 明文数据 |
| algo | Algo | 算法 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| dataHashed | byte[] | 摘要数据 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| Exception | 计算摘要失败 |

###  2.文件计算摘要
```java
byte[] digest(String filePath, Algo algo)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| filePath | String |文件路径 |
| algo | Algo | 算法 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| dataHashed | byte[] | 摘要数据 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| Exception | 计算摘要失败 |

###  3.获取计算sm2预签名Z值
```java
byte[] getZSM3Digest(byte[] data, byte[] pubKey, String userId)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| data | byte[] | 摘要数据原文 |
| pubKey | byte[] | 签名公钥 |
| userId | String | 用户id |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| dataZHashed | byte[] | 返回计算Z值的SM3hash |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 获取计算Z值失败 |


## 文件操作

###  1.创建文件
```java
void createFile(String fileName, int fileSize)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| fileName | String | 文件名称 |
| fileSize | int | 文件大小，KB |
#### 返回
无
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 创建文件失败 |


###  2.读取文件
```java
FileRead readFile(String fileName,int offSet,int readLength)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| fileName | String | 文件名称 |
| offSet | int | 读取起始位置 |
| readLength | int | 读取长度 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| data | byte[] | 返回读取数据 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 读取文件失败 |


###  3.写文件
```java
void writeFile(String fileName, int offSet, byte[] data, int writeLength)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| fileName | String | 文件名称 |
| offSet | int | 写入起始位置 |
| data | byte[] | 写入数据 |
| writeLength | int | 写入长度 |
#### 返回
无
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 写文件失败 |

###  4.删除文件
```java
void deleteFile(String fileName)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| fileName | String | 文件名称 |
#### 返回
无
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 删除文件失败 |

## SM9算法
###  1.使用SM9算法签名
```java
byte[] sm9Sign(byte[] data, byte[] privateKey, byte[] masterPublicKey)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| data | byte[] | 签名数据 |
| privatekey | byte[] | 私钥 |
| masterPublicKey | byte[] | 签名主公钥 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| dataSigned | byte[] | 返回签名数据 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | SM9签名失败 |

###  2.使用SM9算法验签
```java
// SM9验签
boolean sm9Verify(byte[] data, byte[] userId, byte[] signature, byte[] masterPublicKey)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| data | byte[] | 签名数据 |
| userId | byte[] | 用户id |
| signature | byte[] | 签名结果 |
| masterPublicKey | byte[] | 签名主公钥 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| verifyResult | boolean | 返回验签结果 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | SM9验签失败 |


###  3.使用SM9算法加密
```java
byte[] sm9Encrypt(byte[] data, byte[] userId, byte[] masterPublicKey, int type)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| data | byte[] | 加密数据 |
| id | byte[] | 用户id |
| masterPublicKey | byte[] | 加密主公钥 |
| type | int | 加密明文的方法：0 基于KDF的序列 ，1 结合KDF的分组（使用分组算法data需要是16的倍数） |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| dataEncrypted | byte[] | 返回加密结果数据 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | SM9加密失败 |


###  4.使用SM9算法解密
```java
byte[] sm9Decrypt(byte[] data, byte[] userId, byte[] privatekey, int type)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| data | byte[] | 解密数据 |
| id | byte[] | 用户id |
| privatekey | byte[] | 私钥 |
| type | int | 加密明文的方法：0 基于KDF的序列 ，1 结合KDF的分组 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| dataDecrypted | byte[] | 返回解密数据 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | SM9解密失败 |

###  5.导出内部索引sm9密钥主公钥
```java
byte[] sm9ExportMasterPublicKey(int masterKeyIndex, AsymType type)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| uiSM9Index | int | 在密码机中SM9索引号 |
| type | AsymType | 密钥类型 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| masterPublicKey | byte[] | 返回SM9主公钥 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | SM9算法导出主公钥失败 |

###  6.生成sm9算法用户私钥
```java
byte[] sm9GenerateUserPrivKey(int masterKeyIndex, byte[] userId, AsymType type)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| sm9Index | int | 密码机中SM9索引号 |
| userID | byte[] | 用户ID |
| type | AsymType | 密钥类型 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| userPrivKey | byte[] | 返回SM9用户私钥 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | SM9算法生成用户私钥失败 |

###  7.SM9 生成密钥协商参数
```java
SM9KeyExchange sm9GenerateAgreementData(long sessionHandle, byte[] responseId, byte[] masterPublicKey)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| sessionHandle | long | 会话 |
| responseId | byte[] | 响应方id |
| masterPublicKey | byte[] | 加密主公钥 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| spTmpPubKey | byte[] | 临时公钥 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 调用失败 |

###  8.SM9 密钥协商响应方计算会话密钥
```java
SM9KeyExchange sm9GenerateAgreemetDataAndKey(long sessionHandle, int keyLen, byte[] responseId, byte[] sponseId, byte[] privateKey, byte[] masterPublicKey, byte[] sponsorTmpPublicKey)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| sessionHandle | long | 会话 |
| keyLen | int | 密钥长度 |
| responseId | byte[] | 响应方id |
| sponseId | byte[] | 发起方id |
| privateKey | byte[] | 响应方的用户加密私钥 |
| masterPublicKey | byte[] | 加密主公钥 |
| sponsorTmpPublicKey | byte[] | 发起方临时加密主公钥 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| sKeyHandle | int | 协商会话密钥句柄 |
| spTmpPubKey | byte[] | 临时公钥 |
| hashSA | byte[] | 校验摘要值 |
| hashSB | byte[] | 校验摘要值 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 调用失败 |

###  9.SM9 密钥协商发起方计算协商密钥并验证
```java
SM9KeyExchange sm9GenerateKey(long sessionHandle, int keyLen, byte[] sponseId, byte[] responseId, byte[] privateKey, byte[] masterPublicKey, byte[] responseTmpPublicKey, int agreementHandle, byte[] hashSA, byte[] hashSB)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| sessionHandle | long | 会话 |
| keyLen | int | 密钥长度 |
| sponseId | byte[] | 发起方id |
| responseId | byte[] | 响应方id |
| privateKey | byte[] | 响应方的用户加密私钥 |
| masterPublicKey | byte[] | 加密主公钥 |
| responseTmpPublicKey | byte[] | 响应方临时加密主公钥 |
| agreementHandle | int | 协商参数句柄 |
| hashSA | byte[] | 校验摘要值 |
| hashSB | byte[] | 校验摘要值 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| sKeyHandle | int | 协商会话密钥句柄 |
| hashSA | byte[] | 校验摘要值 |
| hashSB | byte[] | 校验摘要值 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 调用失败 |

###  10.SM9 密钥协商响应方验证协商密钥参数
```java
boolean sm9GenerateKeyVerify(long sessionHandle, byte[] hashSA, byte[] hashSB, int sessionKeyHandle)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| sessionHandle | long | 会话 |
| hashSA | byte[] | 校验摘要值 |
| hashSB | byte[] | 校验摘要值 |
| sessionKeyHandle | int | 会话密钥句柄 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| | boolean | 协商成功 false协商失败 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 调用失败 |


###  11.使用SM9密钥封装
```java
SM9Encapsulate sm9Encap(byte[] userId, byte[] masterPublicKey, int keyLen)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| userId | byte[] | 用户 ID |
| masterPublicKey | byte[] | 加密主公钥 |
| keyLen | int |封装密钥长度|
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| key | byte[] | 密钥 |
| cipher | byte[] | 封装密文数据 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 调用失败 |

###  12.使用SM9密钥解封装
```java
byte[] sm9Decap(byte[] userId, byte[] privateKey, byte[] cipher, int keyLen)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| userId | byte[] | 用户 ID |
| privateKey | byte[] | 用户私钥 |
| cipher | byte[] | 封装密文数据 |
| keyLen | int |封装密钥长度|
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| key | byte[] | 密钥 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 调用失败 |


## 祖冲之算法
###  1.祖冲之算法(EIA)接口，计算MAC
```java
byte[] zucEia(byte[] data, byte[] key, int count, int bearer, int direction)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| inbuf | byte[] | 输入原文 |
| key | byte[] | 密钥 |
| count | int | 计数器 |
| bearer | int | 承载位，小于32 |
| direction | int | 方向位，1位 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| dataMac | byte[] | 返回EIA计算后的结果 |

#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 祖冲之算法(EIA)接口，计算MAC zuc_eia失败 |

###  2.祖冲之算法(EEA)接口，加密解密(调用一次加密，调用两次解密)
```java
byte[] zucEea(byte[] data, byte[] key, int count, int bearer, int direction)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| inbuf | byte[] | 输入数据 |
| key | byte[] | 密钥 |
| count | int | 计数器 |
| bearer | int | 承载位，小于32 |
| direction | int | 方向位，1位 |
#### 返回
| 返回值 | 类型 | 描述 |
| :----: | :--: | :--: |
| dataEEA | byte[] | 返回EEA计算后的结果 |
#### 异常
| 异常名 | 描述 |
| :----: | :--: |
| SvcException | 祖冲之算法(EEA)接口，加密解密失败 |


## RSA运算（不做PADDING）
###  1.使用内部索引密钥做RSA公钥运算
```java
byte[] rsaPublicOperation(int keyIndex, byte[] data)
```
#### 参数
| 参数名 | 类型 | 描述 |
| :----: | :--: | :--: |
| keyIndex  |int | 加密机内部非对称密钥索引，使用时注意其对应算法|
| data  |byte[]| 待运算数据 |
#### 返回
| 返回值 |  类型 | 描述|
| :----: | :--: | :--:|
| dataPubOp | byte[]| 公钥运算结果|
#### 异常
|异常名 |  描述 |
| :----: | :--:|
|SvcException | 使用内部RSA公钥运算失败 |

###  2.使用内部索引密钥做RSA私钥运算
```java
byte[] rsaPrivateOperation(long sessionHandle, int keyIndex, byte[] data)
```
#### 参数
|参数名 |  类型 |  描述 |
| :----: | :--:| :--:|
| sessionHandle | long | 会话|
| keyIndex  | int | 加密机内部非对称密钥索引。使用时注意其对应算法 |
|data | byte[] | 待加密数据。|
#### 返回
|返回值 | 类型 | 描述 |
| :----: | :--:| :--:|
|dataPrivOp | byte[] | 私钥运算结果|
#### 异常
|异常名 | 描述 |
| :----: | :--:|
|SvcException|  使用内部RSA私钥运算失败|

## KMS扩展接口

### 1.生成EDDSA密钥对
```java
EdDsaKeyPair generateKeyPairEdDsa(long sessionHandle)
```
#### 参数
|参数名 |  类型 |  描述 |
| :----: | :--:| :--:|
| sessionHandle | long | 会话|
#### 返回
|返回值 | 类型 | 描述 |
| :----: | :--:| :--:|
|pubKey | EdDsaRefPublicKey | 公钥|
|priKey | EdDsaRefPrivateKey| 私钥|
#### 异常
|异常名 | 描述 |
| :----: | :--:|
|SvcException|  调用失败|

### 2.生成ECDSA密钥对
```java
EcDsaKeyPair generateKeyPairEcDsa(long sessionHandle, CurveType curveType)
```
#### 参数
|参数名 |  类型 |  描述 |
| :----: | :--:| :--:|
| sessionHandle | long | 会话|
| curveType | CurveType | 曲线|
#### 返回
|返回值 | 类型 | 描述 |
| :----: | :--:| :--:|
|pubKey | EcDsaRefPublicKey | 公钥|
|priKey | EcDsaRefPrivateKey| 私钥|
#### 异常
|异常名 | 描述 |
| :----: | :--:|
|SvcException|  调用失败|

### 3.生成会话密钥对，并用指定索引密钥加密输出
```java
KeyPairWithKek generateKeypairWithKek(long sessionHandle, SymAlgoMode algo, int keyIndex, byte[] iv, byte[] aad, AsymAlgo asymAlgo,int bits, CurveType curveType)
```
#### 参数
|参数名 |  类型 |  描述 |
| :----: | :--:| :--:|
| sessionHandle | long | 会话|
| algo | SymAlgoMode | 使用的加密算法|
| keyIndex | int | 索引密钥|
| iv | byte[] | iv|
| aad | byte[] | aad|
| asymAlgo | AsymAlgo | 生成密钥对算法|
| bits | int | RSA算法需要指定|
| curveType | CurveType | EC算法需要指定算法曲线|
#### 返回
|返回值 | 类型 | 描述 |
| :----: | :--:| :--:|
| handler | long | 会话密钥索引|
|priKey | byte[]| 私钥|
|pubKey | byte[] | 公钥|
| pucTagData | byte[]| GCM和CCM模式校验码|
#### 异常
|异常名 | 描述 |
| :----: | :--:|
|SvcException|  调用失败|


### 4.ECDSA、SM2 会话密钥签名
```java
byte[] signSessionKey(long sessionHandle, long sessionKeyHandle, byte[] digestData)
```
#### 参数
|参数名 |  类型 |  描述 |
| :----: | :--:| :--:|
| sessionHandle | long | 会话|
| sessionKeyHandle | long | 会话密钥|
| digestData | byte[] |摘要数据|
#### 返回
|返回值 | 类型 | 描述 |
| :----: | :--:| :--:|
| sign | byte[]| 签名数据|
#### 异常
|异常名 | 描述 |
| :----: | :--:|
|SvcException|  调用失败|


### 5.EdDSA 会话密钥签名
```java
byte[] signEdSessionKey(long sessionHandle, long sessionKeyHandle, EdDsaFlag flag, byte[] context, byte[] digestData)
```
#### 参数
|参数名 |  类型 |  描述 |
| :----: | :--:| :--:|
| sessionHandle | long | 会话|
|sessionKeyHandle | long | 会话密钥|
| flag | EdDsaFlag |EdDSA 签名模式|
| context | byte[] |盐：用于增加算法的安全性|
| digestData | byte[] |摘要数据|
#### 返回
|返回值 | 类型 | 描述 |
| :----: | :--:| :--:|
| sign | byte[]| 签名数据|
#### 异常
|异常名 | 描述 |
| :----: | :--:|
|SvcException|  调用失败|


### 6.RSA 会话密钥签名
```java
byte[] signRsaSessionKey(long sessionHandle, long sessionKeyHandle, Algo digestAlgo, RSASignAndVerifyPadMode padMode, byte[] digestData)
```
#### 参数
|参数名 |  类型 |  描述 |
| :----: | :--:| :--:|
| sessionHandle | long | 会话|
|sessionKeyHandle | long | 会话密钥|
| digestAlgo | Algo |摘要算法|
| padMode | RSASignAndVerifyPadMode |补码方式|
| digestData | byte[] |摘要数据|
#### 返回
|返回值 | 类型 | 描述 |
| :----: | :--:| :--:|
| sign | byte[]| 签名数据|
#### 异常
|异常名 | 描述 |
| :----: | :--:|
|SvcException|  调用失败|


### 7.ECDSA、SM2 会话密钥验签
```java
boolean verifySessionKey(long sessionHandle, long sessionKeyHandle, byte[] digestData, byte[] signData)
```
#### 参数
|参数名 |  类型 |  描述 |
| :----: | :--:| :--:|
| sessionHandle | long | 会话|
|sessionKeyHandle | long | 会话密钥|
| digestData | byte[] |摘要数据|
| signData | byte[] |签名数据|
#### 返回
|返回值 | 类型 | 描述 |
| :----: | :--:| :--:|
| verify | boolean| 验签结果|
#### 异常
|异常名 | 描述 |
| :----: | :--:|
|SvcException|  调用失败|


### 8.EdDSA 会话密钥验签
```java
boolean verifyEdSessionKey(long sessionHandle, long sessionKeyHandle, EdDsaFlag flag, byte[] context, byte[] digestData, byte[] signData)
```
#### 参数
|参数名 |  类型 |  描述 |
| :----: | :--:| :--:|
| sessionHandle | long | 会话|
|sessionKeyHandle | long | 会话密钥|
| flag | EdDsaFlag | 签名模式|
| context | byte[] |盐：用于增加算法的安全性|
| digestData | byte[] |摘要数据|
| signData | byte[] |签名数据|
#### 返回
|返回值 | 类型 | 描述 |
| :----: | :--:| :--:|
| verify | boolean| 验签结果|
#### 异常
|异常名 | 描述 |
| :----: | :--:|
|SvcException|  调用失败|

### 9.RSA 会话密钥验签
```java
boolean verifyRsaSessionKey(long sessionHandle, long sessionKeyHandle, Algo digestAlgo, RSASignAndVerifyPadMode padMode, byte[] digestData, byte[] signData)
```
#### 参数
|参数名 |  类型 |  描述 |
| :----: | :--:| :--:|
| sessionHandle | long | 会话|
|sessionKeyHandle | long | 会话密钥|
| digestAlgo | Algo |摘要算法|
| padMode | RSASignAndVerifyPadMode |补码方式|
| digestData | byte[] |摘要数据|
| signData | byte[] |签名数据|
#### 返回
|返回值 | 类型 | 描述 |
| :----: | :--:| :--:|
| verify | boolean| 验签结果|
#### 异常
|异常名 | 描述 |
| :----: | :--:|
|SvcException|  调用失败|


### 10.ECDSA 、SM2会话密钥加密
```java
byte[] encryptSessionKeypair(long sessionHandle, long sessionKeyHandle, byte[] data)
```
#### 参数
|参数名 |  类型 |  描述 |
| :----: | :--:| :--:|
| sessionHandle | long | 会话|
|sessionKeyHandle | long | 会话密钥|
| data | byte[] |数据|
#### 返回
|返回值 | 类型 | 描述 |
| :----: | :--:| :--:|
| encData | byte[]| 加密结果|
#### 异常
|异常名 | 描述 |
| :----: | :--:|
|SvcException|  调用失败|


### 11.ECDSA 、SM2会话密钥解密
```java
byte[] decryptSessionKeypair(long sessionHandle, long sessionKeyHandle, byte[] encData)
```
#### 参数
|参数名 |  类型 |  描述 |
| :----: | :--:| :--:|
| sessionHandle | long | 会话|
|sessionKeyHandle | long | 会话密钥|
| encData | byte[] |数据|
#### 返回
|返回值 | 类型 | 描述 |
| :----: | :--:| :--:|
| data | byte[]| 解密结果|
#### 异常
|异常名 | 描述 |
| :----: | :--:|
|SvcException|  调用失败|


### 12.RSA 会话密钥加密
```java
byte[] encryptRsaSessionKeypair(long sessionHandle, long sessionKeyHandle, RSASignAndVerifyPadMode padMode, byte[] data) 
```
#### 参数
|参数名 |  类型 |  描述 |
| :----: | :--:| :--:|
| sessionHandle | long | 会话|
|sessionKeyHandle | long | 会话密钥|
| encData | byte[] |数据|
| padMode | RSASignAndVerifyPadMode |补码方式|
#### 返回
|返回值 | 类型 | 描述 |
| :----: | :--:| :--:|
| data | byte[]| 加密结果|
#### 异常
|异常名 | 描述 |
| :----: | :--:|
|SvcException|  调用失败|


### 13.RSA 会话密钥解密
```java
byte[] decryptRsaSessionKeypair(long sessionHandle, long sessionKeyHandle, RSASignAndVerifyPadMode padMode, byte[] encData) 
```
#### 参数
|参数名 |  类型 |  描述 |
| :----: | :--:| :--:|
| sessionHandle | long | 会话|
|sessionKeyHandle | long | 会话密钥|
| encData | byte[] |数据|
| padMode | RSASignAndVerifyPadMode |补码方式|
#### 返回
|返回值 | 类型 | 描述 |
| :----: | :--:| :--:|
| data | byte[]| 加密结果|
#### 异常
|异常名 | 描述 |
| :----: | :--:|
|SvcException|  调用失败|



### 14.密钥派生运算
```java
byte[] kdf(long sessionHandle, long sessionKeyHandle, KdfMac algo, byte[] fixedInputData, int keyLength)
```
#### 参数
|参数名 |  类型 |  描述 |
| :----: | :--:| :--:|
| sessionHandle | long | 会话|
|sessionKeyHandle | long | 会话密钥|
| algo | KdfMac |MAC方式|
| fixedInputData | byte[] |盐：用于增加密钥的安全性|
| keyLength | byte[] |密钥长度|
#### 返回
|返回值 | 类型 | 描述 |
| :----: | :--:| :--:|
| data | byte[]|密钥|
#### 异常
|异常名 | 描述 |
| :----: | :--:|
|SvcException|  调用失败|


### 15.导出会话密钥公钥
```java
byte[] exportSessionKeypairPublicKey(long sessionHandle, long sessionKeyHandle)
```
#### 参数
|参数名 |  类型 |  描述 |
| :----: | :--:| :--:|
| sessionHandle | long | 会话|
|sessionKeyHandle | long | 会话密钥|
#### 返回
|返回值 | 类型 | 描述 |
| :----: | :--:| :--:|
| data | byte[]|公钥|
#### 异常
|异常名 | 描述 |
| :----: | :--:|
|SvcException|  调用失败|


### 16.导入加密会话密钥对
```java
long importCipherKeypairWithKek(long sessionHandle, SymAlgoMode symAlgo, int keyIndex, byte[] iv, byte[] aad, byte[] tagData, AsymAlgo asymAlgo, byte[] publicKey, byte[] privateKeyCipher)
```
#### 参数
|参数名 |  类型 |  描述 |
| :----: | :--:| :--:|
| sessionHandle | long | 会话|
| symAlgo | SymAlgoMode | 对称算法|
| keyIndex | int | 对称密钥索引 |
|iv | byte[] | iv |
| aad | byte[] | aad |
| tagData | byte[] | 认证标签数据（GCM、CCM 时不能为空值）|
| asymAlgo | AsymAlgo | 导入的密钥算法|
| publicKey | byte[] | 公钥|
| privateKeyCipher | byte[] | 内部索引对称密钥加密的密文数据|
#### 返回
|返回值 | 类型 | 描述 |
| :----: | :--:| :--:|
| sessionKeyH | long|会话密钥|
#### 异常
|异常名 | 描述 |
| :----: | :--:|
|SvcException|  调用失败|

### 17.导入明文会话密钥对
```java
KeyPairWithKek importPlainKeypairWithKek(long sessionHandle, SymAlgoMode symAlgo, int keyIndex, byte[] iv, byte[] aad, AsymAlgo asymAlgo, byte[] publicKey, byte[] privateKey)
```
#### 参数
|参数名 |  类型 |  描述 |
| :----: | :--:| :--:|
| sessionHandle | long | 会话|
| symAlgo | SymAlgoMode | 对称算法|
| keyIndex | int | 对称密钥索引|
| iv | byte[] | iv |
| aad | byte[] | 认证标签数据（GCM、CCM 时不能为空值）| 
| asymAlgo | AsymAlgo | 导入的密钥算法|
| publicKey | byte[] | 公钥|
| privateKeyCipher | byte[] | 私钥|
#### 返回
|返回值 | 类型 | 描述 |
| :----: | :--:| :--:|
| handler | long | 会话密钥索引|
| priKey | byte[]| 私钥|
| pubKey | byte[] | 公钥|
| pucTagData | byte[]| GCM和CCM校验码|
#### 异常
|异常名 | 描述 |
| :----: | :--:|
|SvcException|  调用失败|

## 扩展接口

### 1.更新session(会话时间)，session在没有操作的情况下3小时自动销毁
```java
void updateSession(long sessionHandle)throws SvcException
```
#### 参数
|参数名 |  类型 |  描述 |
| :----: | :--:| :--:|
| sessionHandle | long | 会话|
#### 返回


#### 异常
|异常名 | 描述 |
| :----: | :--:|
|SvcException|  调用失败|

### 2.导入密钥对
```java
void importKeyPair(long sessionHandle, int keyIndex, String envelopedBlob)
```
#### 参数
|参数名 |  类型 |  描述 |
| :----: | :--:| :--:|
| sessionHandle | long | 会话|
| keyIndex | int | 索引号|
| envelopedBlob | string | 信封密文|
#### 返回


#### 异常
|异常名 | 描述 |
| :----: | :--:|
|SvcException|  调用失败|

### 3.生成一个CSR文件并保存在工作目录
```java
void generateCsrToFile(long sessionHandle,int keyIndex, 
                                  String cn, String o, String ou,
                                  String l, String s, String c) 
```
#### 参数
|参数名 |  类型 |  描述 |
| :----: | :--:| :--:|
| sessionHandle | long | 会话|
| keyIndex | int | 索引号|
| cn | string |Common Name (CN)|
| o | string | Organization (O)|
| ou | string |Organizational Unit (OU)|
| l | string | Locality (L)|
| s | string | State (S)|
| c | string |  Country (C)|
#### 返回


#### 异常
|异常名 | 描述 |
| :----: | :--:|
|SvcException|  调用密码机失败|
|IOException|  生文件失败|

## 错误码表
### 返回方式
调用中异常码通过异常抛出异常类SvcException，返回错误errCode。返回的错误码是10进制。
### 错误码定义
| 错误码 | 16进制 | 10进制 | 含义 |
| ------ | ------ | ------ | ---- |
| SDR_UNKNOWERR | 0x01000000 | 16777216 | 配置错误(配置多ip情况) |
| SDR_UNKNOWERR | 0x01000001 | 16777217 | 未知错误 |
| SDR_NOTSUPPORT | 0x01000002 | 16777218 | 不支持 |
| SDR_COMMFAIL | 0x01000003 | 16777219 | 通信错误 |
| SDR_HARDFAIL | 0x01000004 | 16777220 | 硬件错误 |
| SDR_OPENDEVICE | 0x01000005 | 16777221 | 打开设备错误 |
| SDR_OPENSESSION | 0x01000006 | 16777222 | 打开会话句柄错误 |
| SDR_PARDENY | 0x01000007 | 16777223 | 权限不满足 |
| SDR_KEYNOTEXIST | 0x01000008 | 16777224 | 密钥不存在 |
| SDR_ALGNOTSUPPORT | 0x01000009 | 16777225 | 不支持的算法 |
| SDR_ALGMODNOTSUPPORT | 0x0100000A | 16777226 | 不支持的算法模式 |
| SDR_PKOPERR | 0x0100000B | 16777227 | 公钥运算错误 |
| SDR_SKOPERR | 0x0100000C | 16777228 | 私钥运算错误 |
| SDR_SIGNERR | 0x0100000D | 16777229 | 签名错误 |
| SDR_VERIFYERR | 0x0100000E | 16777230 | 验证错误 |
| SDR_SYMOPERR | 0x0100000F | 16777231 | 对称运算错误 |
| SDR_STEPERR | 0x01000010 | 16777232 | 步骤错误 |
| SDR_FILESIZEERR | 0x01000011 | 16777233 | 文件大小错误或输入数据长度非法 |
| SDR_FILENOEXIST | 0x01000012 | 16777234 | 文件不存在 |
| SDR_FILEOFSERR | 0x01000013 | 16777235 | 文件操作偏移量错误 |
| SDR_KEYTYPEERR | 0x01000014 | 16777236 | 密钥类型错误 |
| SDR_KEYERR | 0x01000015 | 16777237 | 密钥错误 |
| SDR_ENCDATAERR | 0x01000016 | 16777238 | 加密数据错误 |
| SDR_RANDERR | 0x01000017 | 16777239 | 随机数产生失败 |
| SDR_PRKRERR | 0x01000018 | 16777240 | 私钥使用权限获取失败 |
| SDR_MACERR | 0x01000019 | 16777241 | MAC 运算失败 |
| SDR_FILEEXISTS | 0x0100001A | 16777242 | 指定文件已存在 |
| SDR_FILEWERR | 0x0100001B | 16777243 | 文件写入失败 |
| SDR_NOBUFFER | 0x0100001C | 16777244 | 存储空间不足 |
| SDR_INARGERR | 0x0100001D | 16777245 | 输入参数错误 |
| SDR_SESSIONNOTEXIST | 0x0100002F | 16777263 | session不存在 |
| SDR_OUTARGERR | 0x0100001E | 16777246 | 输出参数错误 |
| SDR_MALLOCER | 0x01000121 | 16777409 | 内存分配失败 |
| SDR_PRIVATEER | 0x01000126 | 16777414 | 私有错误 |
| SDR_SERVEREER | 其他错误码 | | 系统错误|

## 密码机支持的算法
描述了支持的对称算法、非对称算法、摘要算法和MAC算法。
### 对称算法
| 算法 | 模式 | Key length | IV length |
| --------- | ---- | ---------- | --------- |
| SM1       | CBC  | 16         | 16        |
| SM1       | ECB  | 16         | 0         |
| SM4       | CBC  | 16         | 16        |
| SM4       | ECB  | 16         | 0         |
| SM4       | OFB  | 16         | 16        |
| SM4       | CFB  | 16         | 16        |
| SM4       | GCM  | 16         | 12        |
| SM4       | CCM  | 16         | 12        |
| SM4       | CTR  | 16         | 16        |
| DES       | CBC  | 8          | 8         |
| DES       | ECB  | 8          | 0         |
| DES       | OFB  | 8          | 8         |
| DES       | CFB  | 8          | 8         |
| AES_128   | CBC  | 16         | 16        |
| AES_128   | ECB  | 16         | 0         |
| AES_128   | OFB  | 16         | 16        |
| AES_128   | CFB  | 16         | 16        |
| AES_128   | GCM  | 16         | 12        |
| AES_128   | CCM  | 16         | 12        |
| AES_128   | CTR  | 16         | 16        |
| AES_256   | CBC  | 32         | 16        |
| AES_256   | ECB  | 32         | 0         |
| AES_256   | OFB  | 32         | 16        |
| AES_256   | CFB  | 32         | 16        |
| AES_256   | GCM  | 32         | 12        |
| AES_256   | CCM  | 32         | 12        |
| AES_256   | CTR  | 32         | 16        |
| TriDES    | CBC  | 24         | 8         |
| TriDES    | ECB  | 24         | 0         |
| TriDES    | OFB  | 24         | 8         |
| TriDES    | CFB  | 24         | 8         |
|zuc        | | ||

### 非对称算法
- SM2
- SM9
- RSA_1024
- RSA_2048
- RSA_4096

### 摘要算法
- SM3
- MD5
- SHA1
- SHA2_256
- SHA2_384
- SHA2_512

### mac算法
#### hmac:
- SM3
- MD5
- SHA1
- SHA2_256
- SHA2_384
- SHA2_512

#### cmac:
- SM1
- SM4
- DES
- AES_128
- AES_256
- TriDES

#### gmac:
- SM4
- AES_128
- AES_256

#### zucmac

## 非对称密钥结构
RSA和SM2的公私钥结构为 ASN.1编码

SM2密钥对（base64）：
公钥：MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEJ98APhrzYR0B51Rhuvd2DCJ6a6+G7+MZ0t4Hf11jm5ml3gFdae+bquHUjLsxH5xUYX/L1jJAYIxmJjsgKfZJGw==
私钥：MHcCAQEEIGb0fLpyRUskw3bNKF46U4IDnLDnQhdyMwcTK8S7SY2BoAoGCCqBHM9VAYItoUQDQgAEJ98APhrzYR0B51Rhuvd2DCJ6a6+G7+MZ0t4Hf11jm5ml3gFdae+bquHUjLsxH5xUYX/L1jJAYIxmJjsgKfZJGw==

RSA密钥对（base64）：
公钥：MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwiZmQwRy3O3e6FxGo/e4dL8b30rlUNMl29lyVNx0EHqQ5NG5OWHatGVkaL+ZVoaMifSQseVIzKVRWB5yMsMf4QdWTEFe8PLmlnMnQHY50sqh1jzf7byMK54KYR2vvELvdwhTYn30URbWSPIGokOWI1hODx/M8YhUwR2N9LOzOIWta3GYLpy+XfZZoTqhghmRl9HsVYU7lQiXxou3udoMFjgwGfjQmuy3VA7ozxAOSfSRLOB68JzLF0GNhKr0YKhOHw2oXYP2nEpKQTu37d9C0HmmKU/PbU2aj6FC6880M8K+klia28g/GlBuuXV7ie288v7zqMjd3mt84kUR4YdNkwIDAQAB
私钥：
MIIEpQIBAAKCAQEAwiZmQwRy3O3e6FxGo/e4dL8b30rlUNMl29lyVNx0EHqQ5NG5OWHatGVkaL+ZVoaMifSQseVIzKVRWB5yMsMf4QdWTEFe8PLmlnMnQHY50sqh1jzf7byMK54KYR2vvELvdwhTYn30URbWSPIGokOWI1hODx/M8YhUwR2N9LOzOIWta3GYLpy+XfZZoTqhghmRl9HsVYU7lQiXxou3udoMFjgwGfjQmuy3VA7ozxAOSfSRLOB68JzLF0GNhKr0YKhOHw2oXYP2nEpKQTu37d9C0HmmKU/PbU2aj6FC6880M8K+klia28g/GlBuuXV7ie288v7zqMjd3mt84kUR4YdNkwIDAQABAoIBAEc87cLja/4SrfrWePkLGTyFLwX0pkXM5BRSKjhnF5NAVdAQAAXFGQmeNoSU4P+1qPWprjoIJnkoo7tQmJh1rknUdl7OkbaZefB0Wk+10au9vDou5f+GcmxCtg5CZUwPx2wGuwnlq3LhjUkAvVxmrj1T1niIM5vgaTDe1kqWcUloZm1HY3n2LYRc2dh+mgb3h4OXL/82Q643YDeeeD+SMq/lKRc9KVm7iorEBZzLB40RbNCev6rx7R8+dHY3Sp6ixzigtXB0j6kq1y5UjVW8kCZWLziu7JDW9Xm8gnEjy4XXrwHTUjR4YfezSYmyUF7LKrW8g2KIRMf2scsqb9OMFlkCgYEA/ZhiMfpCRRxIiPOhrXMapvdTIW9tTtOeuFyfNC6yXzodUnJuj13jxYMLQmPRtbg/kA9THuIVcpADgu11w8dOYuHvIxYJG0eIqtYN/0Oh5jxVQb3f5noPUovzci5sUdS399PYn8jLQLGVBTENSkeUHhmtDl96HvVJCsxJBekSFf8CgYEAw/21koplh5HGMR1qmI09FT2ppXl4rNfzPeKDLMTh68v/24gcZPTN4PCH0FrSI2Gx1Uk/TSfm6aEFN5VY7fpv1xT2xLdFipBruoKtQEzgf9a095l6kFOEu2Ln7dPxNji5wKustBnI4LpJd65qBdVwCQVLn7ybAlFJxh1kP4SMEG0CgYEA2HSwJB6LYDgTBaqGrukXr7pdn6VfdLAWwyBz13OgVr3g91WrXv3yA82l16YiAuzgfFNITI0HZ4DbLm8HXPOKycY1ewo8/abiVYdd7StKTp3i27l5Gl3HJFtGkYhkdriNyqBtP32BV5f8LbE/uc489DBQUXtXXoNZNDlIfx4AjPsCgYEAjwys7GprNIonmE2aMgJUpR4+pnKEq9NqzK55g8GoDAz3kGpGH0G2ywmEd4Tyf7Scscomk9Ad8EQApNMy92+lFd1NR8Nk1IAYwScEATybV3OE3nt9Q75R0Nud4K6jDipVGdWz1jhDsBMH8HyzEdL5VPwyeEKW+RgPurRVKS87c/kCgYEA+1AkgmpIcjaY0QTvRzmZXaYmogp7IH8gq+BDUPmLW5J/zo+WwVtgzNEFEu8w4Y0/8kbDBIR6rG/JH4szVD/CBXPBzvdNch6gqPKDLIIiZIZSVljryr5G86XDKlmQJOTtROTt1giFAHg80nT5cXWgNf7Cqg629bbfgT0vo2/IdX4=


## 常见问题

以下是一些使用 格尔密码机 Java SDK 过程中可能遇到的问题和解决方案：

- Q: 如何获取索引密钥和确认要使用的索引号？
- A: 您可以密码机操作员登录控制台，在密钥管理页面进行查看和创建。

- Q: 如何确定密码服务是否可用？
- A: 可用在管理页面查看密码机是否是运行状态、在相同的网段的机器上使用ping命令进行测试，如果可用ping通在使用telnet ip 10000测试服务端口监听状态。如果以上操作结果都是正确的还是无法连接，可以检查服务端是否开启了gmssl、有没有配置白名单。

- Q: 如何处理异常？
- A: 您可以使用 try-catch 语句捕获并处理可能抛出的异常，对照错误码表进行特定的处理。

- Q: 处理session问题？
- A: 如果想要缓存session来重复调用，需要注意session过期问题（无操作3小时）以及特殊情况下session丢失问题。这种可以通过判断异常码（16777263）来做后续的处理。
         会话密钥、私钥使用权限、SM2密钥协商临时密钥和session绑定。
     
- Q: 配置多服务(ip)需要注意的问题？ 
- A: 配置多服务获取的session会和某一台服务进行绑定(客户端保存)，后续涉及到这个session的操作都会发往绑定的服务，调用closeSession后解绑(删除保存信息)。在调用期间如果绑定的服务发送故障，按照上面session过期处理后续逻辑。
- A: 配置多服务在操作文件接口时，会默认使用第一个服务进行所有操作，如果第一个服务故障，后续接口将无法调用。
- A: 配置多服务需要保证所有服务器的内部密钥完全相同，如果不相同初始化客户端会报配置错误(16777216)。
- A：配置多服务的情况下，如果调用接口需要使用session，并且客户端是集群部署，需要保证session一直在相同的客户端上进行使用。









