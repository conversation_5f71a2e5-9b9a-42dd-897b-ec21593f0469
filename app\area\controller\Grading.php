<?php
declare (strict_types=1);

namespace app\area\controller;

use app\model\MessageModel;
use hg\apidoc\annotation as Apidoc;
use app\area\model\GradingModel;
use think\facade\Db;
use think\facade\View;
use think\Request;
use app\model\TopGradingApproval;
use app\model\TopGrading;
use app\admin\model\CompanyModel;
use app\model\FileModel;

class Grading extends Base
{
    /**
     * @Apidoc\Title("定级申请列表")
     * @Apidoc\Desc("定级申请列表")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function index($limit = 20, $excel = 0)
    {
        if (request()->isAjax()) {
            $title = $this->request->param('title','','trim');
            $status = $this->request->param('status','','trim');
            $where = [
                ['area_id','=',$_SESSION['area']['id']],
                ['dept_id','in',$_SESSION['area']['manager']],
            ];
            if(!empty($title)){
                $where[] = ['company_name','like',"%{$title}%"];
            }
            if($status==1){
                $where[] = ['prcs_id','in',[2,6]];
            }else if($status==2){
                $where[] = ['prcs_id','>',0];
                $where[] = ['status','not in',[0,9]];
            }else if($status==7){
                $where[] = ['status','=',7];
            }else if($status==5){
                $where[] = ['status','in',[5,9]];
            }
            $res = Db::table('top_grading')->where($where)->order('id desc');
            $res = $res->paginate($limit)->each(function ($item, $key) {
                $item['apply_time'] = date('Y-m-d',strtotime($item['apply_time']));
                return $item;
            });
            result($res);
        } else {
            View::assign('title', '首页');
            return view();
        }
    }

    public function gradingInfo($id=0) {
        $where = [
            'id'=>$id,
            ['area_id','=',$_SESSION['area']['id']],
            ['dept_id','in',$_SESSION['area']['manager']],
        ];
        $re = Db::table('top_grading')->where($where)->find();
        $re = GradingModel::codeToText($re);
        $re['prcs'] = Db::table('top_grading_approval')->where(['grading_id'=>$re['id']])->order('id')
            ->select()->each(function ($item, $key) {
                $files = empty($item['check_files'])?[]:explode(',',$item['check_files']);
                foreach ($files as $v){
                    $f = FileModel::getFile(0,$v,'');
                    $item['files'][] = [
                        'id' => $f['id'],
                        'name' => $f['name'],
                        'url' => $f['url'],
                    ];
                }
                $item['end_time'] = empty($item['end_time'])?'':date('Y-m-d',strtotime($item['end_time']));
                $item['remark'] = empty($item['remark'])?$item['check_content']:$item['remark'];
                return $item;
            })->toArray();
        $re['reform'] = [];
        foreach ($re['prcs'] as $v){
            if($v['prcs_id']==5){
                $re['reform'][] = $v;
            }
        }
        $re['steps'] = [];
        $re['stepsActive'] = 1;
        if(in_array($re['status'],[5,7,9])){
            foreach ($re['prcs'] as $v){
                $re['steps'][] = [
                    'label' => $v['prcs_name'],
                    'status' => 'success',
                ];
            }
            $re['steps'][count($re['steps'])-1]['status'] = in_array($re['status'],[5,9])?'error':'success';
            $re['stepsActive'] = count($re['steps']);
        }else{
            $i = 0;
            foreach (config('global.grading_prcs') as $k=>$v){
                if((!in_array($k,[5,6,7])||in_array($re['reform_status'],[1,2]))&&!in_array($k,[10])){
                    $re['steps'][] = [
                        'label' => $v['title'],
                        'status' => '',
                    ];
                }
                if($re['prcs_id']==$k){
                    $re['stepsActive'] = $i;
                }
                $i++;
            }
        }
        $re['files'] = config('global.grading_files');
        result($re);
    }

    public function upload($model='area') {
        $file = request()->file('file');
        $result = FileModel::upload($file,$model);
        result($result);
    }

    public function gradingCheck($id = 0,$status=0,$content='',$files=[])
    {
        $re = Db::table('top_grading')->where(['id' => $id,'area_id'=>$_SESSION['area']['id'],['dept_id','in',$_SESSION['area']['manager']]])->find();
        if(empty($re)){
            result('', 1002, '申请信息不存在');
        }
        if(!in_array($re['prcs_id'],[2,6])){
            result('', 1002, '状态有误');
        }
        $prcs = Db::table('top_grading_approval')->where([['grading_id','=',$re['id']],['prcs_id','=',$re['prcs_id']],['status','in',[1,2]]])->find();
        if(empty($prcs)){
            result('', 1002, '审核信息不存在');
        }
        foreach ($files as $v){
            FileModel::saveFile($v['code'],'area/grading/'.date('Ym'));
            $file[] = $v['code'];
        }
        if($status==5&&empty(trim($content))){
            result('', 1002, '请填写驳回原因');
        }
        $file = empty($file)?'':implode(',',$file);
        Db::startTrans();
        try {
            if($re['prcs_id']==2){
                $data = [
                    'end_user_id' => $_SESSION['area']['user_id'],
                    'end_user_name' => $_SESSION['area']['user_name'],
                    'end_time' => date('Y-m-d H:i:s'),
                    'check_content' => $content,
                    'check_files' => $file,
                    'status' => $status,
                    'status_name' => $status==7?'已通过':'未通过',
                ];
                Db::table('top_grading_approval')->where(['id'=>$prcs['id']])->update($data);
                if($status==7){
                    if(in_array($re['standard_level'],['小型','微型'])){
                        Db::table('top_grading')->where(['id'=>$re['id']])->update(['prcs_id'=>0,'prcs_name'=>'','status'=>7]);
                        $area = Db::table('top_area')->where(['id'=>$re['area_id']])->find();
                        $ca_data = [
                            'grading_id' => $re['id'],
                            'company_id' => $re['company_id'],
                            'company_name' => $re['company_name'],
                            'industry' => $re['industry'],
                            'specialty' => $re['specialty'],
                            'level' => $re['level'],
                            'city_id' => $re['city_id'],
                            'dept_id' => $re['dept_id'],
                            'area_id' => $area['id'],
                            'area' => $area['pcas'],
                            'status' => 1,
                        ];
                        Db::table('top_certificate')->insert($ca_data);
                    }else{
                        Db::table('top_grading')->where(['id'=>$re['id']])->update(['prcs_id'=>3,'prcs_name'=>config('global.grading_prcs')[3]['title']]);
                        $apply_data = [
                            'grading_id' => $id,
                            'prcs_id' => 3,
                            'prcs_name' => config('global.grading_prcs')[3]['title'],
                            'status' => 1,
                            'params' => 'c:'.$re['area_id'],
                            'status_name' => '审核中',
                            'create_user_id' => $_SESSION['area']['user_id'],
                            'create_user_name' => $_SESSION['area']['user_name'],
                            'create_time' => date('Y-m-d H:i:s'),
                        ];
                        Db::table('top_grading_approval')->insertGetId($apply_data);
                    }
                    $url = "/general/toppingsoft/index.php/company/grading/index";
                    $companyUser = Db::table('top_company_info')->field("user_id")->where(['id' => $re['company_id']])->find();
                    MessageModel::sendSms("company",$companyUser['user_id'],'定级申请审核','您的定级申请已通过！',$url);
                }else if($status==5){
                    $url = "/general/toppingsoft/index.php/company/grading/index";
                    $companyUser = Db::table('top_company_info')->field("user_id")->where(['id' => $re['company_id']])->find();
                    MessageModel::sendSms("company",$companyUser['user_id'],'定级申请审核','您的定级申请被驳回，请重新提交申请！',$url);
                    Db::table('top_grading')->where(['id'=>$re['id']])->update(['prcs_id'=>0,'prcs_name'=>'','status'=>5]);
                }
            }else if($re['prcs_id']==6){
                $data = [
                    'end_user_id' => $_SESSION['area']['user_id'],
                    'end_user_name' => $_SESSION['area']['user_name'],
                    'end_time' => date('Y-m-d H:i:s'),
                    'check_content' => $content,
                    'check_files' => $file,
                    'status' => $status,
                    'status_name' => $status==7?'已通过':'未通过',
                ];
                Db::table('top_grading_approval')->where(['id'=>$prcs['id']])->update($data);
                if($status==7){
                    Db::table('top_grading')->where(['id'=>$re['id']])->update(['prcs_id'=>7,'prcs_name'=>config('global.grading_prcs')[7]['title']]);
                    $apply_data = [
                        'grading_id' => $id,
                        'prcs_id' => 7,
                        'prcs_name' => config('global.grading_prcs')[7]['title'],
                        'status' => 1,
                        'params' => 'c:'.$re['area_id'],
                        'status_name' => '审核中',
                        'create_user_id' => $_SESSION['area']['user_id'],
                        'create_user_name' => $_SESSION['area']['user_name'],
                        'create_time' => date('Y-m-d H:i:s'),
                    ];
                    Db::table('top_grading_approval')->insertGetId($apply_data);
                }else if($status==5){
                    $url = "/general/toppingsoft/index.php/company/grading/index";
                    $companyUser = Db::table('top_company_info')->field("user_id")->where(['id' => $re['company_id']])->find();
                    MessageModel::sendSms("company",$companyUser['user_id'],'定级申请审核','您的定级申请被驳回，请重新提交申请！',$url);
                    Db::table('top_grading')->where(['id'=>$re['id']])->update(['prcs_id'=>0,'prcs_name'=>'','status'=>5]);
                }
            }
            Db::commit();
            result($re);
        } catch (\Exception $e) {
            Db::rollback();
            result('', 7002, $e->getMessage());
        }
    }

    //获取企业整改详情数据
    public function getReformInfo()
    {
        $request = $this->request->post();
        if( isset($request['grading_id']) && !empty($request['grading_id']) ){
            $data = Db::table('top_company_reform')->where(["grading_id" => $request['grading_id']])->select()->toArray();

            if( !empty($data) && count($data) > 0 )
            {
                foreach ($data as $k => $datum)
                {
                    $reform_before_files = !empty($datum['reform_before_files'])?$datum['reform_before_files'] = explode(',',$datum['reform_before_files']):[];
                    $data[$k]['reform_before_files'] = [];
                    foreach ($reform_before_files as $v){
                        $f = FileModel::getFile('',$v,'');
                        $data[$k]['reform_before_files'][] = $f;
                        $data[$k]['reform_before_files_preview'][] = $f['url'];
                    }

                    $reform_affter_files = !empty($datum['reform_affter_files'])?$datum['reform_affter_files'] = explode(',',$datum['reform_affter_files']):[];
                    $data[$k]['reform_affter_files'] = [];
                    foreach ($reform_affter_files as $v){
                        $f = FileModel::getFile('',$v,'');
                        $data[$k]['reform_affter_files'][] = $f;
                        $data[$k]['reform_affter_files_preview'][] = $f['url'];
                    }
                }
            }

            result($data,0,'提交成功');
        }else{
            result('',7001,'缺少关键参数');
        }
    }

    public function companyInfo($id = 0)
    {
        $where = ['id' => $id,'area_id'=>$_SESSION['area']['id'], ['dept_id','in',$_SESSION['area']['manager']]];
        \app\model\CompanyModel::getCompanyInfo($id, $where);
        exit();
    }

    public function getReview($element_id = 0)
    {
        if (request()->isAjax()) {
            $request = $this->request->post();
            \app\model\CompanyModel::getAjaxReview($element_id, $request);
            exit();

        } else {
            View::assign('element_id', $element_id);
            View::assign('title', '首页');
            return view();
        }
    }

}
