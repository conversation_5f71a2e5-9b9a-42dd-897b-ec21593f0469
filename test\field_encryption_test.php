<?php
/**
 * 字段加密功能测试脚本
 * 
 * 使用方法：
 * php test/field_encryption_test.php
 */

// 引入ThinkPHP框架
require_once __DIR__ . '/../vendor/autoload.php';

use think\facade\Db;

class FieldEncryptionTest {
    
    private $testTableName = 'test_encryption_table';
    
    public function __construct() {
        echo "=== 字段加密功能测试 ===\n";
    }
    
    /**
     * 运行所有测试
     */
    public function runAllTests() {
        try {
            $this->createTestTable();
            $this->insertTestData();
            $this->testGetTables();
            $this->testGetTableFields();
            $this->testEncryptFields();
            $this->testDecryptFields();
            $this->cleanupTestTable();
            
            echo "\n=== 所有测试完成 ===\n";
        } catch (Exception $e) {
            echo "测试失败: " . $e->getMessage() . "\n";
            $this->cleanupTestTable();
        }
    }
    
    /**
     * 创建测试表
     */
    private function createTestTable() {
        echo "\n1. 创建测试表...\n";
        
        $sql = "CREATE TABLE IF NOT EXISTS `{$this->testTableName}` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(100) DEFAULT NULL COMMENT '姓名',
            `phone` varchar(20) DEFAULT NULL COMMENT '电话',
            `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
            `address` text DEFAULT NULL COMMENT '地址',
            `created_time` datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='加密测试表'";
        
        Db::execute($sql);
        echo "测试表创建成功\n";
    }
    
    /**
     * 插入测试数据
     */
    private function insertTestData() {
        echo "\n2. 插入测试数据...\n";
        
        $testData = [
            [
                'name' => '张三',
                'phone' => '13800138001',
                'email' => '<EMAIL>',
                'address' => '北京市朝阳区某某街道123号'
            ],
            [
                'name' => '李四',
                'phone' => '13800138002',
                'email' => '<EMAIL>',
                'address' => '上海市浦东新区某某路456号'
            ],
            [
                'name' => '王五',
                'phone' => '13800138003',
                'email' => '<EMAIL>',
                'address' => '广州市天河区某某大道789号'
            ]
        ];
        
        foreach ($testData as $data) {
            Db::table($this->testTableName)->insert($data);
        }
        
        echo "测试数据插入成功，共插入 " . count($testData) . " 条记录\n";
    }
    
    /**
     * 测试获取表列表
     */
    private function testGetTables() {
        echo "\n3. 测试获取表列表...\n";
        
        $tables = Db::query("SHOW TABLES");
        $found = false;
        
        foreach ($tables as $table) {
            $tableName = array_values($table)[0];
            if ($tableName === $this->testTableName) {
                $found = true;
                break;
            }
        }
        
        if ($found) {
            echo "✓ 测试表在表列表中找到\n";
        } else {
            throw new Exception("测试表未在表列表中找到");
        }
    }
    
    /**
     * 测试获取表字段
     */
    private function testGetTableFields() {
        echo "\n4. 测试获取表字段...\n";
        
        $fields = Db::query("SHOW FULL COLUMNS FROM `{$this->testTableName}`");
        $expectedFields = ['id', 'name', 'phone', 'email', 'address', 'created_time'];
        $foundFields = [];
        
        foreach ($fields as $field) {
            $foundFields[] = $field['Field'];
        }
        
        foreach ($expectedFields as $expectedField) {
            if (in_array($expectedField, $foundFields)) {
                echo "✓ 字段 {$expectedField} 找到\n";
            } else {
                throw new Exception("字段 {$expectedField} 未找到");
            }
        }
    }
    
    /**
     * 测试字段加密
     */
    private function testEncryptFields() {
        echo "\n5. 测试字段加密...\n";
        
        // 获取原始数据
        $originalData = Db::table($this->testTableName)->select();
        echo "原始数据记录数: " . count($originalData) . "\n";
        
        // 模拟加密操作（这里使用简单的base64编码代替HSM加密）
        $fieldsToEncrypt = ['name', 'phone', 'email'];
        $successCount = 0;
        
        foreach ($originalData as $record) {
            $updateData = [];
            foreach ($fieldsToEncrypt as $field) {
                if (isset($record[$field]) && !empty($record[$field])) {
                    // 使用base64编码模拟加密
                    $updateData[$field] = base64_encode($record[$field]);
                }
            }
            
            if (!empty($updateData)) {
                Db::table($this->testTableName)->where('id', $record['id'])->update($updateData);
                $successCount++;
            }
        }
        
        echo "✓ 成功加密 {$successCount} 条记录\n";
        
        // 验证加密结果
        $encryptedData = Db::table($this->testTableName)->select();
        foreach ($encryptedData as $record) {
            foreach ($fieldsToEncrypt as $field) {
                if (isset($record[$field]) && !empty($record[$field])) {
                    // 检查是否为base64编码
                    if (base64_encode(base64_decode($record[$field])) === $record[$field]) {
                        echo "✓ 字段 {$field} 加密成功\n";
                    }
                }
            }
            break; // 只检查第一条记录
        }
    }
    
    /**
     * 测试字段解密
     */
    private function testDecryptFields() {
        echo "\n6. 测试字段解密...\n";
        
        // 获取加密后的数据
        $encryptedData = Db::table($this->testTableName)->select();
        $fieldsToDecrypt = ['name', 'phone', 'email'];
        $successCount = 0;
        
        foreach ($encryptedData as $record) {
            $updateData = [];
            foreach ($fieldsToDecrypt as $field) {
                if (isset($record[$field]) && !empty($record[$field])) {
                    // 使用base64解码模拟解密
                    $decrypted = base64_decode($record[$field]);
                    if ($decrypted !== false) {
                        $updateData[$field] = $decrypted;
                    }
                }
            }
            
            if (!empty($updateData)) {
                Db::table($this->testTableName)->where('id', $record['id'])->update($updateData);
                $successCount++;
            }
        }
        
        echo "✓ 成功解密 {$successCount} 条记录\n";
        
        // 验证解密结果
        $decryptedData = Db::table($this->testTableName)->select();
        foreach ($decryptedData as $record) {
            if (isset($record['name']) && !empty($record['name'])) {
                // 检查是否为中文字符（解密成功的标志）
                if (preg_match('/[\x{4e00}-\x{9fa5}]/u', $record['name'])) {
                    echo "✓ 字段解密成功，数据已恢复\n";
                }
                break;
            }
        }
    }
    
    /**
     * 清理测试表
     */
    private function cleanupTestTable() {
        echo "\n7. 清理测试数据...\n";
        
        try {
            Db::execute("DROP TABLE IF EXISTS `{$this->testTableName}`");
            echo "✓ 测试表清理完成\n";
        } catch (Exception $e) {
            echo "清理测试表时出错: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * 测试HSM加密函数（如果可用）
     */
    private function testHsmFunctions() {
        echo "\n8. 测试HSM加密函数...\n";
        
        if (function_exists('hsmEncrypt') && function_exists('hsmDecrypt')) {
            $testString = "测试字符串123";
            
            $encrypted = hsmEncrypt($testString);
            if (!empty($encrypted)) {
                echo "✓ HSM加密成功\n";
                
                $decrypted = hsmDecrypt($encrypted);
                if ($decrypted === $testString) {
                    echo "✓ HSM解密成功\n";
                } else {
                    echo "✗ HSM解密失败\n";
                }
            } else {
                echo "✗ HSM加密失败\n";
            }
        } else {
            echo "! HSM函数不可用，跳过测试\n";
        }
    }
}

// 运行测试
if (php_sapi_name() === 'cli') {
    $test = new FieldEncryptionTest();
    $test->runAllTests();
} else {
    echo "请在命令行环境下运行此测试脚本\n";
}
