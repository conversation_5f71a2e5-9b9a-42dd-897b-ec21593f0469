<?php

namespace app\admin\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use app\model\DbModel;
use app\model\SettingModel;
use app\BaseController;
use hg\apidoc\annotation as Apidoc;
use think\facade\View;
use app\model\ExcelModel;
use think\Request;
use think\facade\Db;
use app\model\FileModel;
use app\admin\model\CompanyModel;

/**
 * @Apidoc\Title("企业管理")
 * @Apidoc\Group("Index")
 * @Apidoc\Sort(1)
 */
class Company extends BaseController
{

    public function testDb()
    {
        $tableList = Db::connect("dm_onlie2")->table("ALL_ALL_TABLES")->alias("t")
            ->field("t.TABLE_NAME,c.COMMENT$ as COMMENTS")
            ->leftJoin('SYSTABLECOMMENTS c', 't.TABLE_NAME = c.TVNAME')
            ->where("t.TABLE_NAME like 'top_%' and t.OWNER='SYSDBA' and c.SCH<PERSON>ME='SYSDBA'")
            ->select()
            ->toArray();

        $allData = [];
        foreach ($tableList as $table) {
            $tableName = $table['TABLE_NAME'];
            unset($tempData);
            $tempData[] = [
                'OWNER' => '',
                'TABLE_NAME' => $tableName,
                'COLUMN_NAME' => '',
                'DATA_TYPE' => '',
                'DATA_LENGTH' => '',
                'COMMENTS' => $table['COMMENTS'],
            ];
            $tempData[] = [
                'OWNER' => '',
                'TABLE_NAME' => '表名',
                'COLUMN_NAME' => '字段名',
                'DATA_TYPE' => '类型',
                'DATA_LENGTH' => '长度',
                'COMMENTS' => '注释',
            ];
            $filedList = Db::connect("dm_onlie2")->table("ALL_TAB_COLUMNS")->alias('col')
                ->field("col.OWNER,col.TABLE_NAME,col.COLUMN_NAME,col.DATA_TYPE,col.DATA_LENGTH,cs.COMMENTS")
                ->leftJoin('ALL_COL_COMMENTS cs', 'col.COLUMN_NAME = cs.COLUMN_NAME')
                ->where("col.TABLE_NAME = '{$tableName}' and col.OWNER='SYSDBA' and cs.TABLE_NAME = '{$tableName}' and cs.OWNER='SYSDBA'")
                ->select()->toArray();

            if (!empty($filedList) && count($filedList) > 0) {
                $tempData = array_merge($tempData, $filedList);
            }
            $allData = array_merge($allData, $tempData);
//            $allData[] = $tempData;
        }

        //表头列名
        $header = array(
            '序号',
            '表名',
            '字段名',
            '类型',
            '长度',
            '注释',
        );
        $exportData = array();
        //查询附件 - 项目基本信息
        foreach ($allData as $k => $rows) {
            $index = count($exportData);
            $exportData[$index][] = $k + 1;
            $exportData[$index][] = $rows['TABLE_NAME'];
            $exportData[$index][] = $rows['COLUMN_NAME'];
            $exportData[$index][] = $rows['DATA_TYPE'];
            $exportData[$index][] = $rows['DATA_LENGTH'];
            $exportData[$index][] = $rows['COMMENTS'];
        }
        ToExcel::export($header, $exportData, date('Y-m-d') . ' 城安院数据库表结构');
    }

    /**
     * @Apidoc\Title("企业管理列表")
     * @Apidoc\Desc("企业管理列表")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function index($limit = 20, $excel = 0)
    {
        if (request()->isAjax()) {
            $param = $this->request->param();
            $where = [];

            if (!empty($param['title'])) {
                $where[] = ['a.name|b.username|b.mobile', 'like', "%{$param['title']}%"];
            }

            $res = Db::table('top_company_info')
                ->alias('a')  // 设置别名 a
                ->leftJoin('top_company_user b', 'a.user_id = b.id')
                ->where($where)
                ->field('a.*')
                ->order('a.id');

            if ($excel == 1) {
                set_time_limit(0);
                $res = $res->select()->each(function ($item, $key) {
                    $item = CompanyModel::codeToText($item);
                    return $item;
                })->toArray();
                $title = [
                    ['title' => '企业名称', 'field' => 'name', 'width' => '30'],
                    ['title' => '注册地址', 'field' => 'mb_reg_address', 'width' => '30'],
                    ['title' => '生产经营地址', 'field' => 'mb_operate_address', 'width' => '30'],
                    ['title' => '所属行政区', 'field' => 'mb_region', 'width' => '20'],
                    ['title' => '法人', 'field' => 'legal', 'width' => '20'],
                    ['title' => '法人联系方式', 'field' => 'legal_mobile', 'width' => '20'],
                    ['title' => '法人邮箱', 'field' => 'legal_email', 'width' => '20'],
                    ['title' => '企业传真', 'field' => 'fax', 'width' => '20'],
                    ['title' => '邮政编码', 'field' => 'postal_code', 'width' => '20'],
                    ['title' => '国民经济行业', 'field' => 'mb_economy_sector', 'width' => '20'],
                    ['title' => '行业', 'field' => 'industry', 'width' => '20'],
                    ['title' => '专业', 'field' => 'specialty', 'width' => '20'],
                    ['title' => '统一社会信用代码', 'field' => 'license_number', 'width' => '20'],
                    ['title' => '信用代码有效期', 'field' => 'license_date', 'width' => '20'],
                    ['title' => '经济类型', 'field' => 'mb_economy_type', 'width' => '20'],
                    ['title' => '企业规模', 'field' => 'enterprise_size', 'width' => '20'],
                    ['title' => '注册资本', 'field' => 'reg_money', 'width' => '20'],
                    ['title' => '固定资产', 'field' => 'fixed_asset', 'width' => '20'],
                    ['title' => '安全管理人员', 'field' => 'manager', 'width' => '20'],
                    ['title' => '安全管理人员联系方式', 'field' => 'manager_mobile', 'width' => '20'],
                    ['title' => '安全管理人员邮箱', 'field' => 'manager_email', 'width' => '20'],
                    ['title' => '年营业收入', 'field' => 'revenue', 'width' => '20'],
                    ['title' => '营业面积', 'field' => 'area', 'width' => '20'],
                    ['title' => '员工总数', 'field' => 'personnel', 'width' => '20'],
                    ['title' => '专职安全管理人数', 'field' => 'personnel_full', 'width' => '20'],
                    ['title' => '兼职安全管理人数', 'field' => 'personnel_part', 'width' => '20'],
                    ['title' => '特种作业人数', 'field' => 'personnel_special', 'width' => '20'],
                    ['title' => '所属集团公司', 'field' => 'group_name', 'width' => '20'],
                    ['title' => '经营范围', 'field' => 'business', 'width' => '20'],
                ];
                ExcelModel::exportExcel($title, $res, '企业基本信息导出', true);
            }
            $res = $res->paginate($limit)->each(function ($item, $key) {
                $item = CompanyModel::codeToText($item);
                return $item;
            });
            result($res);
        } else {
            View::assign('title', '首页');
            return view();
        }
    }

    /**
     * @Apidoc\Title("创标申请管理")
     * @Apidoc\Desc("创标申请管理")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function standard($limit = 20, $excel = 0)
    {
        if (request()->isAjax()) {
            $param = $this->request->param();
            $company_name = $this->request->param('company_name', '', 'trim');
            $where = [];
            if (!empty($company_name)) {
                $where[] = ['company_name', 'like', "%{$company_name}%"];
            }
            $res = Db::table('top_standard_apply')->where($where)->order('id');
            if ($excel == 1) {
                set_time_limit(0);
                $res = $res->select()->each(function ($item, $key) {
                    $item = CompanyModel::codeToText($item);
                    //状态
                    $status = [
                        1 => '待审核',
                        7 => '审核通过',
                        5 => '已驳回',
                    ];
                    $item['mb_status'] = $status[$item['status']];
                    return $item;
                })->toArray();
                $title = [
                    ['title' => '企业名称', 'field' => 'company_name', 'width' => '30'],
                    ['title' => '申请状态', 'field' => 'mb_status', 'width' => '30'],
                    ['title' => '行业/专业', 'field' => 'industry', 'width' => '30'],
                    ['title' => '申请标准', 'field' => 'standard_name', 'width' => '20'],
                    ['title' => '评定级别', 'field' => 'level', 'width' => '20'],

                ];
                ExcelModel::exportExcel($title, $res, '创标申请信息导出', true);
            }
            $res = $res->paginate($limit)->each(function ($item, $key) {
                $item = CompanyModel::codeToText($item);
                return $item;
            });
            result($res);
        } else {
            View::assign('title', '首页');
            return view();
        }
    }

    /**
     * @Apidoc\Title("定级申请管理")
     * @Apidoc\Desc("定级申请管理")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function grading($limit = 20, $excel = 0)
    {
        if (request()->isAjax()) {
            $param = $this->request->param();
            $where = [];

            if (isset($param['company_name']) && !empty($param['company_name'])) {
                $where[] = ['company_name', 'like', "%{$param['company_name']}%"];
            }

            $res = Db::table('top_grading')->where($where)->order('id');
            if ($excel == 1) {
                $res = $res->select()->each(function ($item, $key) {
                    $item = CompanyModel::codeToText($item);
                    return $item;
                })->toArray();
                $title = [
                    ['title' => '企业名称', 'field' => 'name', 'width' => '30'],
                    ['title' => '注册地址', 'field' => 'mb_reg_address', 'width' => '30'],
                    ['title' => '生产经营地址', 'field' => 'mb_operate_address', 'width' => '30'],
                    ['title' => '所属行政区', 'field' => 'mb_region', 'width' => '20'],
                    ['title' => '法人', 'field' => 'legal', 'width' => '20'],
                    ['title' => '法人联系方式', 'field' => 'legal_mobile', 'width' => '20'],
                    ['title' => '法人邮箱', 'field' => 'legal_email', 'width' => '20'],
                    ['title' => '企业传真', 'field' => 'fax', 'width' => '20'],
                    ['title' => '邮政编码', 'field' => 'postal_code', 'width' => '20'],
                    ['title' => '国民经济行业', 'field' => 'mb_economy_sector', 'width' => '20'],
                    ['title' => '行业', 'field' => 'industry', 'width' => '20'],
                    ['title' => '专业', 'field' => 'specialty', 'width' => '20'],
                    ['title' => '统一社会信用代码', 'field' => 'license_number', 'width' => '20'],
                    ['title' => '信用代码有效期', 'field' => 'license_date', 'width' => '20'],
                    ['title' => '经济类型', 'field' => 'mb_economy_type', 'width' => '20'],
                    ['title' => '企业规模', 'field' => 'enterprise_size', 'width' => '20'],
                    ['title' => '注册资本', 'field' => 'reg_money', 'width' => '20'],
                    ['title' => '固定资产', 'field' => 'fixed_asset', 'width' => '20'],
                    ['title' => '安全管理人员', 'field' => 'manager', 'width' => '20'],
                    ['title' => '安全管理人员联系方式', 'field' => 'manager_mobile', 'width' => '20'],
                    ['title' => '安全管理人员邮箱', 'field' => 'manager_email', 'width' => '20'],
                    ['title' => '年营业收入', 'field' => 'revenue', 'width' => '20'],
                    ['title' => '营业面积', 'field' => 'area', 'width' => '20'],
                    ['title' => '员工总数', 'field' => 'personnel', 'width' => '20'],
                    ['title' => '专职安全管理人数', 'field' => 'personnel_full', 'width' => '20'],
                    ['title' => '兼职安全管理人数', 'field' => 'personnel_part', 'width' => '20'],
                    ['title' => '特种作业人数', 'field' => 'personnel_special', 'width' => '20'],
                    ['title' => '所属集团公司', 'field' => 'group_name', 'width' => '20'],
                    ['title' => '经营范围', 'field' => 'business', 'width' => '20'],
                ];
                ExcelModel::exportExcel($title, $res, '创标申请信息导出', true);
            }
            $res = $res->paginate($limit)->each(function ($item, $key) {
                $item = CompanyModel::codeToText($item);
                return $item;
            });
            result($res);
        } else {
            View::assign('title', '首页');
            return view();
        }
    }

    /**
     * @param $id
     * @return void
     * @sysLog "修改企业信息"
     */
    public function companySave($id = 0)
    {
        $request = $this->request->post();
        $id = CompanyModel::companySave($request, $id);
        if ($id > 0) {
            result(['id' => $id], 0, '提交成功');
        } else {
            result('', 7001, $id);
        }
    }

    /**
     * @param $id
     * @return void
     * @sysLog "修改CA信息"
     */
    public function caSave($id = 0)
    {
        $request = $this->request->post();
        $id = CompanyModel::caSave($request, $id);
        result(['id' => $id]);
    }

    /**
     * @param $model
     * @return void
     * @sysLog "上传了附件"
     */
    public function upload($model = '')
    {
        $file = request()->file('file');
        $result = FileModel::upload($file, $model);
        result($result);
    }

    public function getConfig()
    {
        $result['pca'] = SettingModel::getPcasAll(1);
        $result['pca3'] = SettingModel::getPcasAll(1, 3);
        $result['economyType'] = SettingModel::getEconomyType(1);
        $result['economySector'] = SettingModel::getEconomySector(1);
        $result['industry'] = SettingModel::getIndustry(1);
        $result['industrialPark'] = SettingModel::getIndustrialPark(1);
        $result['standard'] = Db::table('top_standard_name')->where(['is_del' => 0])->field('id,name')->select()->toArray();
        result($result);
    }

    public function getCompanyInfo($id = 0)
    {
        $re = Db::table('top_company_info')->where(['id' => $id])->find();
        if (!empty($re)) {
            $re = CompanyModel::codeToText($re);

            //获取粉尘 - 认证的信息
            $dustList_apply = Db::table('top_company_param')->field("name,param_value")
                ->where(['company_id' => $re['id'], 'category' => 'dust'])->select()->toArray();

            if (is_array($dustList_apply) && count($dustList_apply)) {
                $re["dust_list"] = $dustList_apply;
            } else {
                $re["dust_list"] = [];
            }

            //获取是否涉高温熔融金属 - 认证的信息
            $dustList_apply = Db::table('top_company_param')->field("name,param_value")
                ->where(['company_id' => $re['id'], 'category' => 'hot'])->select()->toArray();

            if (is_array($dustList_apply) && count($dustList_apply)) {
                $re["hot_list"] = $dustList_apply;
            } else {
                $re["hot_list"] = [];
            }

            $re['industry'] = [$re['industry'], $re['specialty']];
        } else {
            $re = [
                'name' => '',
                'reg_address' => [],
                'reg_address_info' => '',
                'license' => '',
                'licenseUrl' => '',
                'aoc' => '',
                'aocUrl' => '',
                'operate_address' => [],
                'operate_address_info' => '',
                'region' => [],
                'legal' => '',
                'legal_mobile' => '',
                'legal_email' => '',
                'fax' => '',
                'phone' => '',
                'industrial_park' => '',
                'postal_code' => '',
                'economy_sector' => [],
                'industry' => [],
                'license_number' => '',
                'license_start' => '',
                'license_end' => '',
                'economy_type' => [],
                'enterprise_size' => '',
                'reg_money' => '',
                'manager' => '',
                'manager_mobile' => '',
                'manager_email' => '',
                'date' => '',
                'fixed_assets' => '',
                'revenue' => '',
                'personnel' => '',
                'area' => '',
                'personnel_full' => '',
                'personnel_part' => '',
                'personnel_special' => '',
                'group_name' => '',
                'status' => '1',
                'business' => '',

                'is_dust_explosion' => '否',
                'is_ammonia_cold' => '否',
                'is_hot_melting' => '否',
                'is_light_industry' => '否',
                'sector' => '',
                'ammonia_use' => '',
                'ammonia_storage_method' => '',
                'limited_space_type' => '',
                'ammonia_usage' => '',
                'ammonia_storage_capacity' => '',
                'gas_alarm_number' => '',

                'blast_furnace_number' => '',
                'nonferrous_furnace_number' => '',
                'ferroalloy_furnace_number' => '',
                'soaring_furnace_number' => '',

                'dust_list' => [
                    [
                        'name' => '木粉尘',
                        'param_value' => '0',
                    ]
                ],

                'hot_list' => [
                    [
                        'name' => '＜0.5t',
                        'param_value' => '0'
                    ],
                    [
                        'name' => '0.5t≤X＜1t',
                        'param_value' => '0'
                    ],
                    [
                        'name' => '1t≤X＜3t',
                        'param_value' => '0'
                    ],
                    [
                        'name' => '3t≤X＜5t',
                        'param_value' => '0'
                    ],
                    [
                        'name' => '5t≤X＜10t',
                        'param_value' => '0'
                    ],
                    [
                        'name' => '10t≤X＜30t',
                        'param_value' => '0'
                    ],
                    [
                        'name' => '≥30t',
                        'param_value' => '0'
                    ]
                ]
            ];
        }
        result($re);
    }


    public function companyInfo($id = 0)
    {
        \app\model\CompanyModel::getCompanyInfo($id);
        exit();
    }

    public function caList($id = 0)
    {
        $re = Db::table('top_company_ca')->alias('a')
            ->leftJoin('top_standard_name b', 'a.standard_id = b.id')
            ->where(['a.company_id' => $id])
            ->field('a.*,b.name standard_name')->select()
            ->each(function ($item, $key) {
                $item['standardUrl'] = empty($item['standard']) ? '' : FileModel::getFile(0, $item['standard']);
                $item['imageUrl'] = empty($item['image']) ? '' : FileModel::getFile(0, $item['image']);
                $item['date'] = $item['start'] . '至' . $item['ends'];
                return $item;
            });
        result($re);
    }

    //创标申请记录
    public function createApplyList()
    {
        $param = $this->request->param();
        $where = [];
        if (isset($param['id']) && !empty($param['id'])) {
            $where[] = ['company_id', '=', $param['id']];
        }
        $res = Db::table('top_standard_apply')->where($where)->order('id desc')->select()->toArray();
        result($res);
    }

    //定级申请记录
    public function levelApplyList()
    {
        $param = $this->request->param();
        $where = [];
        if (isset($param['id']) && !empty($param['id'])) {
            $where[] = ['company_id', '=', $param['id']];
        }
        $res = Db::table('top_grading')->where($where)->order('id desc')->select()->toArray();
        result($res);
    }

    //获取企业登录用户
    public function getLoginUser()
    {
        $param = $this->request->param();
        $where = [];
        if (isset($param['id']) && !empty($param['id'])) {
            $where[] = ['id', '=', $param['id']];
        }
        //从公司信息表中查询用户id
        $userId = Db::table('top_company_info')->where($where)->value('user_id');
        $res = Db::table('top_company_user')->where('id', $userId)->find();
        $res['reg_time'] = date('Y-m-d H:i:s', strtotime(substr($res['reg_time'], 0, 19)));
        result($res);
    }

    //导入模板
    public function importTemplate()
    {
        $fields = [
            'f0' => '企业名称',
            'ap' => '注册地址（省）',
            'ac' => '注册地址（市）',
            'aa' => '注册地址（区）',
            'f1' => '注册地址',
            'bp' => '生产经营地点（省）',
            'bc' => '生产经营地点（市）',
            'ba' => '生产经营地点（区）',
            'f2' => '生产经营地点',
            'p' => '所属行政区（省）',
            'c' => '所属行政区（市）',
            'a' => '所属行政区（区）',
            'f4' => '法定代表人',
            'f5' => '法人联系电话',
            'f6' => '法人邮箱',
            'f7' => '企业传真',
            'f8' => '邮政编码',
            'f9' => '国民经济行业',
            'f10' => '行业',
            'f11' => '专业',
            'f12' => '统一社会信用代码',
            'f14' => '信用代码有效期',
            'f15' => '经济类型',
            'f16' => '企业规模',
            'f18' => '注册资本',
            'f19' => '安全管理联系人',
            'f20' => '联系电话',
            'f21' => '邮箱',
            'f22' => '成立日期',
            'f23' => '固定资产',
            'f24' => '年营业收入',
            'f25' => '员工总数',
            'f26' => '营业场所面积',
            'f27' => '专职安全管理人数',
            'f28' => '兼职安全管理人数',
            'f29' => '特种作业人数',
            'f30' => '所属集团名称',
            'f31' => '状态',
            'f32' => '经营范围',
        ];
        foreach ($fields as $k => $v) {
            $title[] = ['title' => $v, 'field' => $k, 'width' => '20', 'type' => 'string'];
        }
        $data[] = [
            'ap' => '四川省',
            'ac' => '成都市',
            'aa' => '锦江区',
            'bp' => '四川省',
            'bc' => '成都市',
            'ba' => '锦江区',
            'p' => '四川省',
            'c' => '成都市',
            'a' => '锦江区',
            'f0' => '企业名称',
            'f1' => '注册地址',
            'f2' => '生产经营地点',
            'f3' => '所属行政区',
            'f4' => '法定代表人',
            'f5' => '法人联系电话',
            'f6' => '法人邮箱',
            'f7' => '企业传真',
            'f8' => '6543212',
            'f9' => '农、林、牧、渔业,农业/棉、麻、糖、烟草种植/糖料种植',
            'f10' => '冶金',
            'f11' => '轧钢',
            'f12' => '34567890',
            'f14' => '2023-01-30至2099-12-30',
            'f15' => '内资/股份有限（公司）',
            'f16' => '企业规模',
            'f18' => '5000',
            'f19' => '李四',
            'f20' => '联系电话',
            'f21' => '邮箱',
            'f22' => '2023-01-30',
            'f23' => '固定资产',
            'f24' => '999',
            'f25' => '999',
            'f26' => '999',
            'f27' => '99',
            'f28' => '9',
            'f29' => '9',
            'f30' => '集团名称',
            'f31' => '正常',
            'f32' => '经营范围',
        ];
        ExcelModel::exportExcel($title, $data, '企业信息导入模板');
    }


    //数据导入

    /**
     * @return void
     * @throws \think\Exception
     * @sysLog 导入企业数据
     */
    public function import()
    {
        $data = import("input");
        $dataHeader = importHeader("input");
        $result = CompanyModel::import($data, $dataHeader);
        $msg = count($result['success']) . '条数据保存成功，' . count($result['repeat']) . '条数据重复导入，' . count($result['fail']) . '条数据处理失败。';
        result($result, 0, $msg);
    }

}
