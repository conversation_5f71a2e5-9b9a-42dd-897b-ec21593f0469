# 格尔密码机 HSM 客户端 JAR 使用说明

## 概述
本JAR包是格尔密码机HSM客户端命令行工具，提供密码机的各种操作功能，包括加解密、签名验签、密钥生成、摘要计算等。特别为PHP调用进行了优化，提供了简化的命令接口。

## 环境要求
- Java 8 或更高版本
- 网络连接到密码机服务器
- 正确的配置文件

## 配置文件
在JAR包同目录下创建 `hsm-config.properties` 文件：

```properties
# HSM服务器配置
hsm.ip=**************
hsm.port=10000
hsm.pool.max=4

# SSL配置（可选）
hsm.ssl=false
hsm.keystore=/path/to/client.p12
hsm.truststore=/path/to/ca.cer
```

## 基本使用格式
```bash
java -jar hsm-client.jar <command> [options]
```

## PHP简化命令（推荐）

### 1. 数据加密
```bash
java -jar hsm-client.jar enc "Hello World"
```
- 使用内部密钥索引1进行SM4-CBC加密
- 输出Base64编码的加密数据
- PHP调用示例：
```php
$encrypted = trim(shell_exec('java -jar hsm-client.jar enc "Hello World" 2>nul'));
```

### 2. 数据解密
```bash
java -jar hsm-client.jar dec "base64_encrypted_data"
```
- 解密Base64编码的加密数据
- 输出原始明文
- PHP调用示例：
```php
$decrypted = trim(shell_exec('java -jar hsm-client.jar dec "' . $encrypted . '" 2>nul'));
```

### 3. HMAC计算
```bash
java -jar hsm-client.jar hmac "Hello World"
```
- 使用内部密钥索引2计算SM3-HMAC
- 输出十六进制HMAC值
- PHP调用示例：
```php
$hmac = trim(shell_exec('java -jar hsm-client.jar hmac "Hello World" 2>nul'));
```

### 4. HMAC验证
```bash
java -jar hsm-client.jar verify "Hello World" "hmac_hex_value"
```
- 验证数据的HMAC值
- 输出 `true` 或 `false`
- PHP调用示例：
```php
$isValid = trim(shell_exec('java -jar hsm-client.jar verify "Hello World" "' . $hmac . '" 2>nul')) === 'true';
```

## 完整命令列表

### 随机数生成
```bash
java -jar hsm-client.jar random 16
```
生成16字节的随机数，输出十六进制格式。

### 摘要计算
```bash
java -jar hsm-client.jar digest SM3 "Hello World"
java -jar hsm-client.jar digest SHA2_256 "test data"
```
支持的算法：SM3, MD5, SHA1, SHA2_256, SHA2_384, SHA2_512

### 完整加密命令
```bash
# 使用内部密钥加密
java -jar hsm-client.jar encrypt internal 1 "test data" CBC 00000000000000000000000000000000 PKCS7Padding

# 使用外部密钥加密
java -jar hsm-client.jar encrypt external 0123456789ABCDEF0123456789ABCDEF "test data" CBC 00000000000000000000000000000000 PKCS7Padding
```

### 完整解密命令
```bash
# 使用内部密钥解密
java -jar hsm-client.jar decrypt internal 1 "encrypted_hex_data" CBC 00000000000000000000000000000000 PKCS7Padding

# 使用外部密钥解密
java -jar hsm-client.jar decrypt external 0123456789ABCDEF0123456789ABCDEF "encrypted_hex_data" CBC 00000000000000000000000000000000 PKCS7Padding
```

### 数字签名
```bash
# 使用内部私钥签名（需要密码）
java -jar hsm-client.jar sign internal SM2 1 "test data" SM3 "password123"

# 使用外部私钥签名
java -jar hsm-client.jar sign external SM2 "private_key_hex" "test data" SM3
```

### 签名验证
```bash
java -jar hsm-client.jar verify internal SM2 1 "test data" SM3 "signature_hex"
```

### 密钥生成
```bash
# 生成对称密钥
java -jar hsm-client.jar genkey symmetric 32

# 生成非对称密钥对
java -jar hsm-client.jar genkey asymmetric SM2
java -jar hsm-client.jar genkey asymmetric RSA_2048
```

### 会话管理
```bash
# 打开会话
java -jar hsm-client.jar session open

# 关闭会话
java -jar hsm-client.jar session close 12345

# 生成会话密钥
java -jar hsm-client.jar session genkey 12345 1 SYMMETRIC 16
```

### MAC计算
```bash
# HMAC
java -jar hsm-client.jar mac hmac 2 SM3 "test data"

# CMAC
java -jar hsm-client.jar mac cmac 1 SM4 "test data" 00000000000000000000000000000000

# GMAC
java -jar hsm-client.jar mac gmac 1 SM4 "test data" 123456781234 "aad_data"
```

### 文件操作
```bash
# 创建文件
java -jar hsm-client.jar file create "test.dat" 1024

# 读取文件
java -jar hsm-client.jar file read "test.dat" 0 100

# 写入文件
java -jar hsm-client.jar file write "test.dat" 0 "48656C6C6F"

# 删除文件
java -jar hsm-client.jar file delete "test.dat"
```

### 祖冲之算法
```bash
# ZUC完整性算法
java -jar hsm-client.jar zuc eia "test data" "0123456789ABCDEF0123456789ABCDEF" 123 5 1

# ZUC加密算法
java -jar hsm-client.jar zuc eea "test data" "0123456789ABCDEF0123456789ABCDEF" 123 5 1
```

## PHP集成示例

创建 `hsm.php` 文件：

```php
<?php
class HSMClient {
    private $jarPath;
    
    public function __construct($jarPath = 'hsm-client.jar') {
        $this->jarPath = $jarPath;
    }
    
    public function encrypt($data) {
        $result = shell_exec("java -jar {$this->jarPath} enc \"$data\" 2>nul");
        return trim($result);
    }
    
    public function decrypt($encryptedData) {
        $result = shell_exec("java -jar {$this->jarPath} dec \"$encryptedData\" 2>nul");
        return trim($result);
    }
    
    public function hmac($data) {
        $result = shell_exec("java -jar {$this->jarPath} hmac \"$data\" 2>nul");
        return trim($result);
    }
    
    public function verify($data, $hmac) {
        $result = shell_exec("java -jar {$this->jarPath} verify \"$data\" \"$hmac\" 2>nul");
        return trim($result) === 'true';
    }
    
    public function random($length = 16) {
        $result = shell_exec("java -jar {$this->jarPath} random $length 2>nul");
        return trim($result);
    }
    
    public function digest($data, $algorithm = 'SM3') {
        $result = shell_exec("java -jar {$this->jarPath} digest $algorithm \"$data\" 2>nul");
        return trim($result);
    }
}

// 使用示例
$hsm = new HSMClient();

$data = "Hello World";
echo "原始数据: $data\n";

// 加密解密
$encrypted = $hsm->encrypt($data);
echo "加密数据: $encrypted\n";

$decrypted = $hsm->decrypt($encrypted);
echo "解密数据: $decrypted\n";

// HMAC计算和验证
$hmac = $hsm->hmac($data);
echo "HMAC值: $hmac\n";

$isValid = $hsm->verify($data, $hmac);
echo "HMAC验证: " . ($isValid ? "通过" : "失败") . "\n";

// 生成随机数
$random = $hsm->random(32);
echo "随机数: $random\n";

// 计算摘要
$hash = $hsm->digest($data, 'SM3');
echo "SM3摘要: $hash\n";
?>
```

## 错误处理

### 常见错误码
- `16777245`: 输入参数错误
- `16777263`: 会话过期或丢失
- `16777216`: 连接失败

### 调试技巧
1. 检查配置文件是否正确
2. 确认密码机服务是否运行
3. 验证网络连接：`ping **************`
4. 测试端口连接：`telnet ************** 10000`
5. 检查密钥索引是否存在

### 静默运行
为了避免SLF4J警告信息，建议使用：
```bash
# Windows
java -jar hsm-client.jar enc "data" 2>nul

# Linux/Mac
java -jar hsm-client.jar enc "data" 2>/dev/null
```

## 注意事项

1. **密钥管理**：确保在密码机管理界面预先创建所需的密钥
2. **会话管理**：长时间操作建议重用会话，注意3小时超时
3. **线程安全**：HsmClient支持多线程，可以并发调用
4. **错误处理**：生产环境建议添加适当的错误处理和重试机制
5. **安全性**：避免在命令行中直接传递敏感数据，考虑使用文件或环境变量

## 性能优化

1. **连接池**：合理配置 `hsm.pool.max` 参数
2. **会话复用**：对于频繁操作，考虑复用会话
3. **批量操作**：对于大量数据，考虑批量处理
4. **网络优化**：确保网络延迟最小化
