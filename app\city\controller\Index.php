<?php

namespace app\city\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use app\api\model\SecsModel;
use app\company\model\CompanyModel;
use hg\apidoc\annotation as Apidoc;
use think\facade\View;
use think\Request;
use think\facade\Db;

/**
 * @Apidoc\Title("市应急局首页")
 * @Apidoc\Group("Index")
 * @Apidoc\Sort(1)
 */
class Index extends Base {

    /**
     * @Apidoc\Title("市应急局首页")
     * @Apidoc\Desc("市应急局首页")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function index() {
        View::assign('title','首页');
        $url = 'index/main';
        View::assign('user',$_SESSION['city']);
        View::assign('url',$url);
        return view();
    }

    public function getMessage() {
        $where = [
            ['city_id','=',$_SESSION['city']['id']],
            ['dept_id','=',$_SESSION['city']['dept_id']],
            ['prcs_id','in',[3,7]],
        ];
        $count['num3'] = Db::table('top_grading')->where($where)->count();
        $count['num4'] = Db::table('top_org_tasks_appeal')->where(['status'=>0])->count();
        foreach ($count as $k=>$v){
            $count[$k] = $v>0?$v:'';
        }
        $data['count'] = $count;
        result($data);
    }

    /**
     * @Apidoc\Title("区县应急局首页")
     * @Apidoc\Desc("区县应急局首页")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function main() {
        if (request()->isAjax()) {
            $container1 = Db::table('top_company_info')->where(['city_id'=>$_SESSION['city']['id'],'dept_id'=>$_SESSION['city']['dept_id']])->field('industry name,count(id) value')->group('industry')->select();
            foreach ($container1 as $v){
                if(!empty($v['name'])){
                    $res['container']['container1'][] = [
                        'name' => $v['name'],
                        'value' => $v['VALUE'],
                    ];
                }
            }
            $container2 = Db::table('top_certificate')->where(['city_id'=>$_SESSION['city']['id'],'dept_id'=>$_SESSION['city']['dept_id']])->field('level name,count(id) value')->group('level')->select();
            foreach ($container2 as $v){
                if(!empty($v['name'])){
                    $res['container']['container2'][] = [
                        'name' => $v['name'],
                        'value' => $v['VALUE'],
                    ];
                }
            }
            $container3 = Db::table('top_org_tasks')->where(['city_id'=>$_SESSION['city']['id'],'dept_id'=>$_SESSION['city']['dept_id']])->field('date,count(id) value')->group('date')->order('date')->select()->toArray();
            $tmp = [];
            foreach ($container3 as $v){
                $tmp[date('Y',strtotime($v['date']))] += $v['value'];
            }
            $res['container']['container3'] = [
                'year' => ['2019','2020','2021','2022','2023'],
                'value' => ['98','76','56','82','65'],
            ];
            foreach ($tmp as $k=>$v){
                $res['container']['container3']['year'][] = $k;
                $res['container']['container3']['value'][] = $v;
            }
            result($res);
        } else {
            $total['company']['sum'] = number_format(Db::table('top_company_info')->where(['city_id'=>$_SESSION['city']['id'],'dept_id'=>$_SESSION['city']['dept_id']])->count());
            $total['company']['sum1'] = number_format(Db::table('top_company_info')->where(['city_id'=>$_SESSION['city']['id'],'dept_id'=>$_SESSION['city']['dept_id']])->where(['ca_status'=>1])->count());
            $total['company']['sum2'] = number_format(Db::table('top_company_info')->where(['city_id'=>$_SESSION['city']['id'],'dept_id'=>$_SESSION['city']['dept_id']])->where(['ca_status'=>2])->count());
            $total['company']['sum3'] = number_format(Db::table('top_company_info')->where(['city_id'=>$_SESSION['city']['id'],'dept_id'=>$_SESSION['city']['dept_id']])->where(['ca_status'=>0])->count());
            $total['ca']['sum'] = number_format(Db::table('top_certificate')->where(['city_id'=>$_SESSION['city']['id'],'dept_id'=>$_SESSION['city']['dept_id']])->where('status','in',[8,7])->count());
            $total['ca']['sum1'] = number_format(Db::table('top_certificate')->where(['city_id'=>$_SESSION['city']['id'],'dept_id'=>$_SESSION['city']['dept_id']])->where(['status'=>7])->count());
            $total['ca']['sum2'] = number_format(Db::table('top_certificate')->where(['city_id'=>$_SESSION['city']['id'],'dept_id'=>$_SESSION['city']['dept_id']])->where(['status'=>8])->count());
            $total['task']['sum'] = number_format(Db::table('top_org_tasks')->where(['city_id'=>$_SESSION['city']['id'],'dept_id'=>$_SESSION['city']['dept_id']])->where('status','in',[9,7])->count());
            $total['task']['sum1'] = number_format(Db::table('top_org_tasks')->where(['city_id'=>$_SESSION['city']['id'],'dept_id'=>$_SESSION['city']['dept_id']])->where(['status'=>9])->count());
            $total['task']['sum2'] = number_format(Db::table('top_org_tasks')->where(['city_id'=>$_SESSION['city']['id'],'dept_id'=>$_SESSION['city']['dept_id']])->where(['status'=>7])->count());
            $result['notify'] = Db::table('top_notify')->alias('a')
                ->leftJoin('top_notify_type t','a.type = t.id')
                ->where(['a.is_del'=>0,'a.is_show'=>1])
                ->order('date desc,create_time desc')
                ->field('a.*,t.name mb_type')
                ->page(1,10)->select()->toArray();
            $date = date('Y-m-d',strtotime('+13month'));
            $result['ca'] = Db::table('top_certificate')
                ->where([['ends','<',$date],['status','=',7]])
                ->order('ends')
                ->page(1,10)->select()->toArray();
            View::assign('total',$total);
            View::assign('result',$result);
            View::assign('title','首页');
            return view();
        }
    }


    public function policyInfo($id = 0){
        $where[] = ['is_del','=',0];
        $where[] = ['id','=',$id];
        $res = Db::table('top_notify')
            ->where($where)
            ->find();
        if(empty($res)){
            result('',1002,'数据有误');
        }
        result($res);
    }

    public function loginout() {
        $_SESSION['area'] = [];
        return redirect('/general/toppingsoft/index.php/city/login/login');
    }

    public function editPassword() {
        $old_password = request()->param('old_password');
        $new_password = request()->param('new_password');
        $confirm_password = request()->param('confirm_password');
        $user = Db::table('top_city_user')->where(['id'=>$_SESSION['city']['user_id']])->find();
        if($user['password']!==crypt($old_password,$user['salt'])){
            result('',1003,'原密码错误');
        }
        $pattern = '/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$|^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$|^(?=.*[a-z])(?=.*[A-Z])(?=.*[\W_]).{8,}$|^(?=.*[a-z])(?=.*\d)(?=.*[\W_]).{8,}$|^(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$/';
        // 使用preg_match函数进行匹配
        if (!preg_match($pattern, $new_password)) {
            result('',1001,'请填写6-18位密码，并且须由大写字母、小写字母、数字及特殊符号中的三种或三种以上进行组合');
        }
        if($new_password!==$confirm_password){
            result('',1005,'两次密码不一致');
        }
        $data['salt'] = create_nonce_str(8);
        $data['password'] = crypt($new_password,$data['salt']);
        $re = Db::table('top_city_user')->where(['id'=>$_SESSION['city']['user_id']])->update($data);
        if($re){
            result('',0,'密码修改成功，请重新登陆');
        }else{
            result('',1007,'修改失败');
        }
    }


    /**
     * @Apidoc\Title("跳转至专家系统")
     * @Apidoc\Desc("跳转至专家系统")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     */
    function gotoVideoUrl($url){
        $base = "https://221.237.111.6:8020/";
        $urlExt = "?token=b9613d2e244a487299fd6ecfa8e26ebd";
        $url = $base.$url.$urlExt;
        header("Location: $url");
    }
}
