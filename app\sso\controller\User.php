<?php

namespace app\sso\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use hg\apidoc\annotation as Apidoc;
use app\Request;
use think\App;
use think\facade\Db;

/**
 * @Apidoc\Title("用户相关接口")
 * @Apidoc\Group("User")
 * @Apidoc\Sort(3)
 */
class User extends Base
{

    /**
     * @Apidoc\Title("获取角色列表")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("priv", type="string",require=true, desc="角色名称")
     * @Apidoc\Returned("dept_id", type="string",desc="部门id")
     * @Apidoc\Returned("dept_name", type="string",desc="部门名称")
     */
    function privList(Request $request){
        $priv = $request->post('priv');
        $where = [];
        if(!empty($priv)){
            $where[] = ['PRIV_NAME','=',$priv];
        }
        $field = 'USER_PRIV priv_id,PRIV_NAME priv_name';
        $results = Db::table('user_priv')
            ->where($where)->field($field)->select()->toArray();
        result($results,0,'获取成功');
    }

    /**
     * @Apidoc\Title("获取部门列表")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("department", type="string",require=true, desc="部门名称")
     * @Apidoc\Returned("dept_id", type="string",desc="部门id")
     * @Apidoc\Returned("dept_name", type="string",desc="部门名称")
     */
    function deptList(Request $request){
        $department = $request->param('department');
        $where = [];
        if(!empty($department)){
            $where[] = ['DEPT_NAME','=',$department];
        }
        $field = 'dept_id,dept_name';
        $results = Db::table('department')
            ->where($where)->field($field)->select()->toArray();
        result($results,0,'获取成功');
    }

    /**
     * @Apidoc\Title("添加部门")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("dept_name", type="string",require=true, desc="部门名称")
     * @Apidoc\Param("parent_dept_id", type="string",require=true, desc="上级部门id")
     * @Apidoc\Param("dept_no", type="string",require=true, desc="部门编号")
     * @Apidoc\Returned("dept_id", type="string",desc="部门id")
     */
    function deptAdd(Request $request){
        $dept_name = $request->param('dept_name');
        $parent_dept_id = $request->param('parent_dept_id');
        $dept_no = $request->param('dept_no');
        $oadata = [];
        $oadata['DEPT_NO'] = trim($dept_no);
        $oadata['DEPT_NAME'] = trim($dept_name);
        $oadata['DEPT_PARENT'] = trim($parent_dept_id);
        $url = "app/sso/oa/dept_add.php?appid={$this->appid}&appkey={$this->appkey}";
        $oaResult = sso_curl_post($url,$oadata);
        $re = empty($oaResult)?[]:json_decode($oaResult,true);
        if($re['code']==0&&!empty($re)){
            result($re['data'],0,'新增成功');
        }else{
            result('',$re['code'],$re['msg']);
        }
    }

    /**
     * @Apidoc\Title("编辑部门")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("dept_id", type="string",require=true, desc="编辑部门id")
     * @Apidoc\Param("dept_name", type="string",require=true, desc="部门名称")
     */
    function deptEdit(Request $request){
        $dept_id = $request->param('dept_id');
        $dept_name = $request->param('dept_name','','trim');
        if(empty($dept_name)){
            result('',1001,'部门名称不能为空');
        }
        $up = Db::table('department')->where(['DEPT_ID'=>$dept_id])->update(['DEPT_NAME'=>$dept_name]);
        if($up){
            result('',0,'编辑成功');
        }else{
            result('',2001,'编辑失败');
        }
    }

    /**
     * @Apidoc\Title("获取用户列表")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("department", type="string",require=true, desc="部门名称")
     * @Apidoc\Param("username", type="string",require=true, desc="用户名")
     * @Apidoc\Param("name", type="string",require=true, desc="姓名")
     * @Apidoc\Param("phone", type="string",require=true, desc="手机号")
     * @Apidoc\Returned("dept_id", type="string",desc="部门id")
     * @Apidoc\Returned("dept_name", type="string",desc="部门名称")
     * @Apidoc\Returned("user_id", type="string",desc="用户user_id（唯一，不可编辑）")
     * @Apidoc\Returned("username", type="string",desc="用户登录名（唯一，可编辑）")
     * @Apidoc\Returned("name", type="string",desc="用户姓名")
     * @Apidoc\Returned("sex", type="string",desc="性别")
     * @Apidoc\Returned("mobile", type="string",desc="手机号")
     * @Apidoc\Returned("birthday", type="string",desc="出生日期")
     * @Apidoc\Returned("sex", type="string",desc="性别{0：男，1：女}")
     */
    function userList(Request $request){
        $department = $request->param('department');
        $username = $request->param('username');
        $name = $request->param('name');
        $phone = $request->param('phone');
        $where[] = ['u.DEPT_ID','>','0'];
        if(!empty($department)){
            $where[] = ['d.DEPT_NAME','=',$department];
        }
        if(!empty($username)){
            $where[] = ['u.BYNAME','=',$username];
        }
        if(!empty($name)){
            $where[] = ['u.USER_NAME','=',$name];
        }
        if(!empty($phone)){
            $where[] = ['u.MOBIL_NO','=',$phone];
        }
        $field = 'd.dept_id,d.dept_name,u.byname username,u.user_id,u.user_name name,u.MOBIL_NO mobile,u.sex,u.birthday';
        $results = Db::table('department')->alias('d')
            ->leftJoin(['td_user'=>'u'],'d.DEPT_ID = u.DEPT_ID')
            ->where($where)->field($field)->select()->toArray();
        result($results,0,'获取成功');
    }

    /**
     * @Apidoc\Title("添加用户")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("byname", type="string",require=true, desc="登录名")
     * @Apidoc\Param("user_name", type="string",require=true, desc="用户姓名")
     * @Apidoc\Param("user_priv", type="string",require=true, desc="角色id")
     * @Apidoc\Param("dept_id", type="string",require=true, desc="部门id")
     * @Apidoc\Param("sex", type="int",default="0", desc="性别{0：男，1：女}")
     * @Apidoc\Param("sort", type="int", desc="排序号")
     * @Apidoc\Param("birthday", type="string", desc="出生日期")
     * @Apidoc\Param("mobil_no", type="string", desc="手机号")
     * @Apidoc\Param("tel_no_dept", type="string", desc="座机号")
     * @Apidoc\Returned("user_id", type="string",desc="用户user_id")
     */
    function userAdd(Request $request){
        $param = $request->param();
        $data = [];
        $data['BYNAME'] = trim($param['byname']);
        $data['USER_NAME'] = trim($param['user_name']);
        $data['SEX'] = $param['sex']==1?1:0;
        $data['USER_PRIV'] = trim($param['user_priv']);
        $data['DEPT_ID'] = trim($param['dept_id']);
        $data['USER_NO'] = trim($param['sort']);
        $data['BIRTHDAY'] = trim($param['birthday']);
        $data['MOBIL_NO'] = trim($param['mobil_no']);
        $data['TEL_NO_DEPT'] = trim($param['tel_no_dept']);
        $url = "app/sso/oa/user_add.php?appid={$this->appid}&appkey={$this->appkey}";
        $oaResult = sso_curl_post($url,$data);
        $re = empty($oaResult)?[]:json_decode($oaResult,true);
        if($re['code']==0&&!empty($re)){
            result($re['data'],0,'新增成功');
        }else{
            result('',$re['code'],$re['msg']);
        }
    }

    /**
     * @Apidoc\Title("修改用户信息")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("user_id", type="string",require=true, desc="USER_ID")
     * @Apidoc\Param("user_name", type="string",require=false, desc="用户姓名(为空则不修改)")
     * @Apidoc\Param("user_priv", type="string",require=false, desc="角色id(为空则不修改)")
     * @Apidoc\Param("dept_id", type="string",require=false, desc="部门id(为空则不修改)")
     * @Apidoc\Param("sex", type="int",default="0", desc="性别{0：男，1：女}")
     * @Apidoc\Param("sort", type="int", desc="排序号(为空则不修改)")
     * @Apidoc\Param("birthday", type="string", desc="出生日期(为空则不修改)")
     * @Apidoc\Param("mobil_no", type="string", desc="手机号(为空则不修改)")
     * @Apidoc\Param("tel_no_dept", type="string", desc="座机号(为空则不修改)")
     * @Apidoc\Returned("user_id", type="string",desc="用户user_id")
     */
    function userEdit(Request $request){
        $param = $request->param();
        $data = [];
        $USER_ID = trim($param['user_id']);
        $data['USER_NAME'] = trim($param['user_name']);
        $data['SEX'] = $param['sex']==1?1:0;
        $data['USER_PRIV'] = trim($param['user_priv']);
        $data['DEPT_ID'] = trim($param['dept_id']);
        $data['USER_NO'] = trim($param['sort']);
        $data['BIRTHDAY'] = trim($param['birthday']);
        $data['MOBIL_NO'] = trim($param['mobil_no']);
        $data['TEL_NO_DEPT'] = trim($param['tel_no_dept']);
        $USER = Db::table('td_user')->where(['USER_ID'=>$USER_ID])->find();
        if(empty($USER)){
            result('',1001,'用户不存在');
        }
        $data['BYNAME'] = $USER['BYNAME'];
        $data['USER_PRIV'] = empty($data['USER_PRIV'])?$USER['USER_PRIV']:$data['USER_PRIV'];
        $data['DEPT_ID'] = empty($data['DEPT_ID'])?$USER['DEPT_ID']:$data['DEPT_ID'];
        $data['USER_NO'] = empty($data['USER_NO'])?$USER['USER_NO']:$data['USER_NO'];
        $data['MOBIL_NO'] = empty($data['MOBIL_NO'])?$USER['MOBIL_NO']:$data['MOBIL_NO'];
        $data['TEL_NO_DEPT'] = empty($data['TEL_NO_DEPT'])?$USER['TEL_NO_DEPT']:$data['TEL_NO_DEPT'];
        $data['USER_NAME'] = empty($data['USER_NAME'])?$USER['USER_NAME']:$data['USER_NAME'];
        $data['SEX'] = empty($data['SEX'])?$USER['SEX']:$data['SEX'];
        if($data['USER_PRIV']==$USER['USER_PRIV']&&$data['DEPT_ID']==$USER['DEPT_ID']){
            Db::table('td_user')->where(['USER_ID'=>$USER_ID])->update($data);
            result(['user_id'=>$USER_ID],0,'修改成功');
        }
        $data['PRIV_ID'] = $USER['USER_PRIV_OTHER'];
        $data['DEPT_ID_OTHER'] = $USER['DEPT_ID_OTHER'];
        $data['LEAVE_DEPT'] = $USER['LEAVE_DEPT'];
        $data['UID'] = $USER['UID'];
        $data['USER_ID'] = $USER['USER_ID'];
        $data['DEPT_ID_OTHER'] = $USER['DEPT_ID_OTHER'];
        $data['DEPT_NAME_OTHER'] = $USER['DEPT_NAME_OTHER'];
        $data['NOT_LOGIN'] = $USER['NOT_LOGIN'];
        $data['NOT_MOBILE_LOGIN'] = $USER['NOT_MOBILE_LOGIN'];
        $data['IM_RANGE'] = $USER['IM_RANGE'];
        $data['THEME'] = $USER['THEME'];
        $data['EMAIL'] = $USER['EMAIL'];
        $data['TO_ID'] = $USER['TO_ID'];
        $data['TO_NAME'] = $USER['TO_NAME'];
        $data['REMARK'] = $USER['REMARK'];
        $url = "app/sso/oa/user_update.php?appid={$this->appid}&appkey={$this->appkey}";
        $oaResult = sso_curl_post($url,$data);
        $re = empty($oaResult)?[]:json_decode($oaResult,true);
        if($re['code']==0&&!empty($re)){
            result($re['data'],0,'修改成功');
        }else{
            result('',$re['code'],$re['msg']);
        }
    }


    function reduce($USER_ID = '') {
        $url = "app/sso/oa/user_update.php?appid={$this->appid}&appkey={$this->appkey}";
        $USER = Db::table('td_user')->where(['USER_ID'=>$USER_ID])->find();
        if(empty($USER)){
            result('',1001,'用户不存在');
        }
        $data['USER_NAME'] = $USER['USER_NAME'];
        $data['SEX'] = $USER['SEX'];
        $data['USER_PRIV'] = $USER['USER_PRIV'];
        $data['DEPT_ID'] = 0;
        $data['USER_NO'] = $USER['USER_NO'];
        $data['BIRTHDAY'] = $USER['BIRTHDAY'];
        $data['MOBIL_NO'] = $USER['MOBIL_NO'];
        $data['TEL_NO_DEPT'] = $USER['TEL_NO_DEPT'];
        $data['BYNAME'] = $USER['BYNAME'];
        $data['UID'] = $USER['UID'];
        $data['USER_ID'] = $USER['USER_ID'];
        $data['DEPT_ID1'] = $USER['DEPT_ID'];
        $data['DEPT_ID_OTHER'] = $USER['DEPT_ID_OTHER'];
        $data['DEPT_NAME_OTHER'] = $USER['DEPT_NAME_OTHER'];
        $data['NOT_LOGIN'] = $USER['NOT_LOGIN'];
        $data['NOT_MOBILE_LOGIN'] = $USER['NOT_MOBILE_LOGIN'];
        $data['IM_RANGE'] = $USER['IM_RANGE'];
        $data['THEME'] = $USER['THEME'];
        $data['EMAIL'] = $USER['EMAIL'];
        $data['TO_ID'] = $USER['TO_ID'];
        $data['TO_NAME'] = $USER['TO_NAME'];
        $data['REMARK'] = $USER['REMARK'];
        foreach ($data as $k=>$v){
            $data[$k] = $v;
        }
        $oaResult = sso_curl_post($url,$data);
        $re = empty($oaResult)?[]:json_decode($oaResult,true);
        if($re['code']==0&&!empty($re)){
            result($re['data'],0,'操作成功');
        }else{
            result('',$re['code'],$re['msg']);
        }
    }

}
