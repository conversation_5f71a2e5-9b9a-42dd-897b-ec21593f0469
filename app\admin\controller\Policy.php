<?php


namespace app\admin\controller;

use app\BaseController;
use app\model\FileModel;
use app\model\UserModel;
use hg\apidoc\annotation as Apidoc;
use app\admin\model\PolicyModel;
use think\facade\Db;
use think\facade\View;

/**
 * @Apidoc\Title("政策法规管理")
 * @Apidoc\Group("UserInfo")
 * @Apidoc\Sort(3)
 */
class Policy extends BaseController
{

    /**
     * @param $id
     * @return void
     * @sysLog 保存政策法规
     */
    public function savePolicy($id=0){
        $id = $this->request->param('id');
        $request = $this->request->post();
        $id = PolicyModel::savePolicy($request,$id);
        result(['id'=>$id]);
    }

    //保存分类

    /**
     * @param $id
     * @return void
     * @sysLog 保存政策法规分类
     */
    public function savePolicyType($id=0){
        $id = $this->request->param('id');
        $request = $this->request->post();
        if(empty($request['name'])){
            result('',1000,'请填写分类名称');
        }
        $id = PolicyModel::savePolicyType($request,$id);
        result(['id'=>$id]);
    }

    /**
     * @param $id
     * @return void
     * @throws \think\db\exception\DbException
     * @sysLog 更新通知公告信息
     */
    public function policyDel($id=0){
        $id = $this->request->param('id');
        $re = Db::table('top_notify')->where(['id'=>$id])->update(['is_del'=>1]);
        if($re===false){
            result('',1002,'信息有误');
        }
        result('',0,'删除成功');
    }


    /**
     * @param $id
     * @return void
     * @throws \think\db\exception\DbException
     * @sysLog 删除通知公告类型
     */
    public function policyTypeDel($id=0){
        $id = $this->request->param('id');
        $re = Db::table('top_notify_type')->where(['id'=>$id])->update(['is_del'=>1]);
        if($re===false){
            result('',1002,'信息有误');
        }
        result('',0,'删除成功');
    }

    /**
     * @param $id
     * @return void
     * @sysLog 修改通知公告
     */
    public function upPolicy($id=0){
        $id = $this->request->param('id');
        $re = PolicyModel::upStatus('notify','show',$id);
        if($re===false){
            result('',1002,'信息有误');
        }
        result(['status'=>$re==1?true:false]);
    }

    public function policyList($limit = 20){
        if (request()->isAjax()) {
            $type = Db::table('top_notify_type')->where(['is_del'=>'0'])->order('sort')->select()->toArray();
            $types = [];
            foreach ($type as $v){
                $types[$v['id']] = $v['name'];
            }
            $where[] = ['a.is_del','=',0];
            $title = $this->request->param('title','','trim');
            $type = $this->request->param('type','','trim');
            if(!empty($title)){
                $where[] = ['a.title','like',"%$title%"];
            }
            if(!empty($type)){
                $where[] = ['a.type','=',$type];
            }
            $res = Db::table('top_notify')->alias('a')
                ->leftJoin('top_notify_type t','a.type = t.id')
                ->where($where)
                ->order('create_time desc')
                ->field('a.*,t.name mb_type')
                ->paginate($limit)
                ->each(function ($item, $key){
                    $item = PolicyModel::codeToText($item);
                    return $item;
                })->toArray();
            $res['types'] = $types;
            result($res);
        }else{
            View::assign('user_type',config('global.UserInfo.user_type'));
            return view();
        }
    }

    public function policyType($limit = 20){
        if (request()->isAjax()) {
            $where[] = ['is_del','=','0'];
            $res = Db::table('top_notify_type')
                ->where($where)
                ->order('sort')
                ->paginate($limit)
                ->each(function ($item, $key){
                    return $item;
                })->toArray();
            result($res);
        }else{
            return view();
        }
    }

    public function policyInfo($id = 0,$dept=''){
        $where[] = ['is_del','=',0];
        $where[] = ['id','=',$id];
        $res = Db::table('top_notify')
            ->where($where)
            ->find();
        $res = PolicyModel::codeToText($res);
        if(empty($res)){
            result('',1002,'数据有误');
        }
        result($res);
    }

    /**
     * 上传文件
     * @param \think\Request $request
     * @return \think\response\Json
     * @sysLog 添加附件
     */
    function upload(\think\Request $request){
        $file = request()->file('file');
        $re = FileModel::upload($file);
        if(empty($re['id'])){
            result('',1006,$re);
        }
        result($re);
    }


}