<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>企业安全生产标准化信息管理系统</title>
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/plugs/echarts/echarts.min.js"></script>
<script type="text/javascript" src="https://api.map.baidu.com/api?v=3.0&ak=Liamy9Myi2S6cflOLl1nSF2wTw1ud8vF"></script>
    <style>
        body { background: #0a1a2a; }
        .dashboard-root { display: flex; height: 100vh; background: #0a1a2a; }
        .dashboard-left { width: 340px; background: rgba(10,30,60,0.95); padding: 24px 12px 24px 24px; display: flex; flex-direction: column; }
        .dashboard-center { flex: 1; display: flex; flex-direction: column; justify-content: flex-start; align-items: center; padding: 24px 0; }
        .dashboard-right { width: 420px; background: rgba(15,35,70,0.98); padding: 24px 24px 24px 12px; display: flex; flex-direction: column; }
        .dashboard-title { color: #fff; font-size: 32px; font-weight: bold; text-align: center; letter-spacing: 2px; margin-bottom: 18px; }
        .overview-cards { display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px; margin-bottom: 18px; }
        .overview-card { background: linear-gradient(135deg,#1a2a4a 60%,#223a6a 100%); border-radius: 10px; padding: 12px 8px; color: #fff; display: flex; align-items: center; gap: 8px; box-shadow: 0 2px 8px #0a1a2a44; }
        .overview-icon { font-size: 28px; color: #3fa7ff; }
        .overview-info { flex: 1; }
        .overview-label { font-size: 13px; color: #b3cfff; }
        .overview-value { font-size: 18px; font-weight: bold; color: #fff; }
        .industry-section, .district-section, .top-section { background: #162a48; border-radius: 10px; margin-bottom: 16px; padding: 14px 10px; color: #fff; }
        .section-title { font-size: 24px; font-weight: bold; color: #fff; margin-bottom: 8px; }
        .industry-chart, .district-chart, .top-list { width: 100%; height: 140px; }
        .top-list-item { display: flex; align-items: center; justify-content: space-between; margin-bottom: 6px; }
        .top-rank { font-size: 16px; font-weight: bold; color: #ffd700; margin-right: 8px; }
        .top-score { color: #3fa7ff; font-weight: bold; margin-left: 8px; }
        .dashboard-map { width: 98%; height: 640px; background: #1a2a4a; border-radius: 12px; margin-bottom: 18px; position: relative; box-shadow: 0 2px 12px #0a1a2a55; }
        .map-placeholder { color: #3fa7ff; font-size: 20px; text-align: center; line-height: 340px; }
        .review-section { width: 98%; background: #162a48; border-radius: 10px; padding: 14px 10px; color: #fff; }
        .review-table { width: 100%; color: #fff; }
        .review-table th, .review-table td { padding: 6px 8px; border-bottom: 1px solid #223a6a; }
        .review-table th { color: #3fa7ff; font-weight: bold; background: #1a2a4a; }
        .review-progress { color: #ffd700; font-weight: bold; }
        .field-section { background: #1a2a4a; border-radius: 10px; margin-bottom: 16px; padding: 14px 10px; color: #fff; }
        .section-title { font-size: 26px; font-weight: bold; color: #fff; margin-bottom: 8px; }
        .sub-title { font-size: 20px; font-weight: bold; color: #fff; margin-bottom: 8px; }
        .field-chart { width: 100%; height: 110px; margin-bottom: 8px; }
        .field-desc { font-size: 13px; color: #b3cfff; margin-bottom: 4px; }
        .ammonia-desc { font-size: 13px; color: #b3cfff; margin-bottom: 2px; }
        .field-flex { display: flex; gap: 8px; }
        .field-half { flex: 1; }
        ::-webkit-scrollbar { width: 6px; background: #223a6a; }
        ::-webkit-scrollbar-thumb { background: #3fa7ff; border-radius: 3px; }
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 18px;">
        <div class="dashboard-title" style="flex: 1; text-align: center;">企业安全生产标准化信息管理系统</div>
        <div class="datetime-weather" style="color: #fff; font-size: 16px; display: flex; gap: 20px;">
            <span id="weather-info">晴 25°C</span>
            <span id="current-date"></span>
            <span id="current-time"></span>
        </div>
    </div>
    <div class="dashboard-root">
        <!-- 左侧数据区 -->
        <div class="dashboard-left">
            <div class="section-title" style="margin-bottom: 8px;">总体概览</div>
            <div class="overview-cards">
                <div class="overview-card"><span class="overview-icon el-icon-office-building"></span><div class="overview-info"><div class="overview-label">在库企业数</div><div class="overview-value">3421</div></div></div>
                <div class="overview-card"><span class="overview-icon el-icon-s-flag"></span><div class="overview-info"><div class="overview-label">达标企业数</div><div class="overview-value">3211</div></div></div>
                <div class="overview-card"><span class="overview-icon el-icon-s-check"></span><div class="overview-info"><div class="overview-label">达标有效企业数</div><div class="overview-value">2378</div></div></div>
                <div class="overview-card"><span class="overview-icon el-icon-s-data"></span><div class="overview-info"><div class="overview-label">总体通过率</div><div class="overview-value">93.8%</div></div></div>
                <div class="overview-card"><span class="overview-icon el-icon-close"></span><div class="overview-info"><div class="overview-label">未通过数</div><div class="overview-value">210</div></div></div>
                <div class="overview-card"><span class="overview-icon el-icon-check"></span><div class="overview-info"><div class="overview-label">通过数</div><div class="overview-value">3211</div></div></div>
            </div>
            <div class="industry-section">
                <div class="section-title">分行业概览</div>
                <div id="industry-chart" class="industry-chart"></div>
            </div>
            <div class="district-section">
                <div class="section-title">高新区应急局概览</div>
                <div id="district-chart" class="district-chart"></div>
                <div class="district-info" style="margin-top:8px;">
                    <span>企业总数：<b>298</b></span>&nbsp;&nbsp;
                    <span>达标企业：<b>168</b></span>&nbsp;&nbsp;
                    <span>评审任务数：<b>168</b></span>&nbsp;&nbsp;
                    <span>评审及时率：<b>99%</b></span>
                </div>
            </div>
            <div class="top-section">
                <div class="section-title">高分评审企业 TOP3</div>
                <div class="top-list">
                    <div class="top-list-item"><span class="top-rank">NO.1</span>冶金 <span class="top-score">99.6</span></div>
                    <div class="top-list-item"><span class="top-rank">NO.2</span>建材 <span class="top-score">99.5</span></div>
                    <div class="top-list-item"><span class="top-rank">NO.3</span>机械 <span class="top-score">99.4</span></div>
                </div>
            </div>
        </div>
        <!-- 中间地图与评审实时区 -->
        <div class="dashboard-center">
            <div class="dashboard-map">
                <div id="baidu-map" style="width:100%;height:100%;"></div>
            </div>
            <div class="review-section">
                <div class="section-title">评审实时情况</div>
                <table class="review-table">
                    <thead>
                        <tr><th>评审企业</th><th>所属区域</th><th>评审机构</th><th>评审日期</th><th>评审专家组</th><th>评审进度</th></tr>
                    </thead>
                    <tbody>
                        <tr><td>成都金牛机械厂</td><td>金牛区</td><td>城安院</td><td>2024.03.06 13:00:00</td><td>专家组A</td><td class="review-progress">企业整改</td></tr>
                        <tr><td>成都青羊化工厂</td><td>青羊区</td><td>城安院</td><td>2024.03.06 13:00:00</td><td>专家组B</td><td class="review-progress">企业整改</td></tr>
                        <tr><td>成都高新电子厂</td><td>高新区</td><td>城安院</td><td>2024.03.06 13:00:00</td><td>专家组C</td><td class="review-progress">企业整改</td></tr>
                    </tbody>
                </table>
            </div>
        </div>
        <!-- 右侧重点领域监管区 -->
        <div class="dashboard-right">
            <div class="section-title">重点领域监管</div>
            <div class="field-section">
                <div class="sub-title">有限空间</div>
                <div id="space-chart" class="field-chart"></div>
                <div class="field-desc">污水处理池、腌渍池等领域数量统计</div>
            </div>
            <div class="field-section">
                <div class="field-title">粉尘涉爆</div>
                <div class="field-flex">
                    <div class="field-half"><div id="dust-pie" class="field-chart"></div></div>
                    <div class="field-half"><div id="dust-bar" class="field-chart"></div></div>
                </div>
                <div class="field-desc">饼图：金属制品加工等行业占比；柱状图：木粉尘、金属粉尘等类型数量</div>
            </div>
            <div class="field-section">
                <div class="field-title">液氨制冷</div>
                <div class="ammonia-desc">用途：制气 999t/a，调节PH值 399t/a</div>
                <div class="field-flex">
                    <div class="field-half"><div id="ammonia-pie" class="field-chart"></div></div>
                </div>
                <div class="ammonia-desc">存储量：瓶装 21（62%），罐装 13（38%）</div>
            </div>
            <div class="field-section">
                <div class="field-title">涉高温熔融金属</div>
                <div class="field-flex">
                    <div class="field-half"><div id="metal-bar1" class="field-chart"></div></div>
                    <div class="field-half"><div id="metal-bar2" class="field-chart"></div></div>
                </div>
                <div class="field-desc">冶炼炉（高炉、有色冶金炉等数量）、熔炼炉（按吨位分段数量）</div>
            </div>
        </div>
    </div>
</div>
<script>
// 更新日期和时间
function updateDateTime() {
    const now = new Date();
    document.getElementById('current-date').textContent = now.toLocaleDateString('zh-CN', {year: 'numeric', month: '2-digit', day: '2-digit'});
    document.getElementById('current-time').textContent = now.toLocaleTimeString('zh-CN', {hour12: false, hour: '2-digit', minute: '2-digit'});
}

// 初始化时间更新
updateDateTime();
setInterval(updateDateTime, 1000);

// 初始化百度地图
function initBaiduMap() {
    console.log('开始初始化百度地图');
    var map = new BMap.Map('baidu-map');
    map.centerAndZoom(new BMap.Point(104.06, 30.67), 12);
    map.enableScrollWheelZoom();
    map.setMapStyle({
        style: 'midnight'
    });
    // 添加企业标记点示例
    var point1 = new BMap.Point(104.06, 30.67);
    var marker1 = new BMap.Marker(point1);
    map.addOverlay(marker1);
    // 可以添加更多企业标记点
    console.log('百度地图初始化完成');
}

// 新增：统一接口请求函数
function fetchDashboardData() {
    return fetch('data', {method: 'GET'})
        .then(res => res.json())
        .catch(() => null);
}

// 新增：默认数据
const defaultData = {
    overview: {
        total: 3421,
        passed: 3211,
        valid: 2378,
        rate: '93.8%',
        unpassed: 210,
        passedCount: 3211
    },
    industry: [120, 200, 150, 80, 70],
    district: [80, 90, 70, 60, 50],
    districtInfo: {total: 298, passed: 168, task: 168, timely: '99%'},
    top: [
        {name: '冶金', score: 99.6},
        {name: '建材', score: 99.5},
        {name: '机械', score: 99.4}
    ],
    review: [
        {company: '成都金牛机械厂', area: '金牛区', org: '城安院', date: '2024.03.06 13:00:00', group: '专家组A', progress: '企业整改'},
        {company: '成都青羊化工厂', area: '青羊区', org: '城安院', date: '2024.03.06 13:00:00', group: '专家组B', progress: '企业整改'},
        {company: '成都高新电子厂', area: '高新区', org: '城安院', date: '2024.03.06 13:00:00', group: '专家组C', progress: '企业整改'}
    ],
    space: [
        {value: 30, name: '污水处理池'},
        {value: 20, name: '腌渍池'},
        {value: 10, name: '其他'}
    ],
    dustPie: [
        {value: 40, name: '金属制品'},
        {value: 30, name: '木制品'},
        {value: 20, name: '其他'}
    ],
    dustBar: [18, 12],
    ammoniaPie: [
        {value: 21, name: '瓶装'},
        {value: 13, name: '罐装'}
    ],
    metalBar1: [5, 3],
    metalBar2: [2, 4, 1]
};

// 新增：Vue 实例
new Vue({
    el: '#app',
    data() {
        return {
            dashboard: JSON.parse(JSON.stringify(defaultData))
        };
    },
    mounted() {
        console.log('Vue mounted 生命周期开始');
        initBaiduMap();
        fetchDashboardData().then(data => {
            data = data.data;
            if (data && typeof data === 'object') {
                // 合并后端数据与默认值
                this.dashboard = Object.assign({}, defaultData, data);
                // 针对嵌套对象/数组做深度合并
                if(data.overview) this.dashboard.overview = Object.assign({}, defaultData.overview, data.overview);
                if(data.districtInfo) this.dashboard.districtInfo = Object.assign({}, defaultData.districtInfo, data.districtInfo);
                if(data.top) this.dashboard.top = Array.isArray(data.top) && data.top.length ? data.top : defaultData.top;
                if(data.review) this.dashboard.review = Array.isArray(data.review) && data.review.length ? data.review : defaultData.review;
            }
            this.initCharts();
        });
    },
    methods: {
        initCharts() {
            if (window.echarts) {
                // 行业分布图
                var industryChart = echarts.init(document.getElementById('industry-chart'));
                industryChart.setOption({
                    title: {text: '', left: 'center', textStyle: {color: '#fff', fontSize: 14}},
                    tooltip: {},
                    xAxis: {type: 'category', data: ['冶金', '建材', '机械', '化工', '电子'], axisLabel: {color: '#b3cfff'}},
                    yAxis: {type: 'value', axisLabel: {color: '#b3cfff'}},
                    series: [{data: this.dashboard.industry, type: 'bar', itemStyle: {color: '#3fa7ff'}}]
                });
                // 区域分布图
                var districtChart = echarts.init(document.getElementById('district-chart'));
                districtChart.setOption({
                    tooltip: {},
                    legend: {data:['企业数'], textStyle: {color: '#fff'}},
                    radar: {indicator: [
                        {name: '金牛区', max: 100},
                        {name: '青羊区', max: 100},
                        {name: '高新区', max: 100},
                        {name: '武侯区', max: 100},
                        {name: '成华区', max: 100}
                    ], name: {color: '#b3cfff'}},
                    series: [{
                        name: '企业数',
                        type: 'radar',
                        data: [{value: this.dashboard.district, name: '企业数', areaStyle: {color: 'rgba(63,167,255,0.3)'}}],
                        lineStyle: {color: '#3fa7ff'},
                        itemStyle: {color: '#3fa7ff'}
                    }]
                });
                // 重点领域图表
                var spaceChart = echarts.init(document.getElementById('space-chart'));
                spaceChart.setOption({
                    tooltip: {},
                    series: [{type: 'pie', radius: '60%', data: this.dashboard.space, label: {color: '#fff'}}]
                });
                var dustPie = echarts.init(document.getElementById('dust-pie'));
                dustPie.setOption({
                    tooltip: {},
                    series: [{type: 'pie', radius: '60%', data: this.dashboard.dustPie, label: {color: '#fff'}}]
                });
                var dustBar = echarts.init(document.getElementById('dust-bar'));
                dustBar.setOption({
                    tooltip: {},
                    xAxis: {type: 'category', data: ['木粉尘', '金属粉尘'], axisLabel: {color: '#b3cfff'}},
                    yAxis: {type: 'value', axisLabel: {color: '#b3cfff'}},
                    series: [{data: this.dashboard.dustBar, type: 'bar', itemStyle: {color: '#ffd700'}}]
                });
                var ammoniaPie = echarts.init(document.getElementById('ammonia-pie'));
                ammoniaPie.setOption({
                    tooltip: {},
                    series: [{type: 'pie', radius: '60%', data: this.dashboard.ammoniaPie, label: {color: '#fff'}}]
                });
                var metalBar1 = echarts.init(document.getElementById('metal-bar1'));
                metalBar1.setOption({
                    tooltip: {},
                    xAxis: {type: 'category', data: ['高炉', '有色冶金炉'], axisLabel: {color: '#b3cfff'}},
                    yAxis: {type: 'value', axisLabel: {color: '#b3cfff'}},
                    series: [{data: this.dashboard.metalBar1, type: 'bar', itemStyle: {color: '#3fa7ff'}}]
                });
                var metalBar2 = echarts.init(document.getElementById('metal-bar2'));
                metalBar2.setOption({
                    tooltip: {},
                    xAxis: {type: 'category', data: ['10吨以下', '10-50吨', '50吨以上'], axisLabel: {color: '#b3cfff'}},
                    yAxis: {type: 'value', axisLabel: {color: '#b3cfff'}},
                    series: [{data: this.dashboard.metalBar2, type: 'bar', itemStyle: {color: '#ffd700'}}]
                });
            }
        }
    }
});
</script>
</body>
</html>