<template>
  <el-dialog :title="title" :close-on-click-modal="false" modal="false" :visible.sync="visible" width="500px"
             @close="refresh()" append-to-body="true" label-position="top">
    <el-form ref="form"  class="tableSearch" label-position="top"
             enctype="multipart/form-data">
      <el-row :gutter="20">
        <el-col :span="24" style="margin-bottom: 10px">
          导入模板下载
          <el-button size="small" @click="down_template" type="primary" icon="el-icon-download">下载</el-button>

        </el-col>
        <el-col :span="24">
          <el-upload
              class="upload-demo"
              drag
              :data="otherData"
              :show-file-list="false"
              :on-success="handleSuccess"
              :action="submitUrl"
              multiple>
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">只能上传jpg/png文件，且不超过500kb</div>
          </el-upload>
        </el-col>
      </el-row>
      <el-row style="margin-top: 10px">
        <el-col :span="24">
          <el-button style="float: right" size="small" type="info" @click="visible =false">关闭</el-button>
        </el-col>
      </el-row>
    </el-form>
  </el-dialog>
</template>
<script>
module.exports = {
  name: "add",
  data: function () {
    return {
      id: 0,
      isDisabled: false,
      visible: false,
      title: '导入',
      loading: false,
      user_id: 0,
      otherData: {
        industry_ids:  '',
        standard_name_id: '',
      },
      templateUrl: 'importTemplate',
      submitUrl: this.submitUrl,
      currentTab: 'first', // 当前tab来源
    }
  },
  methods: {
    down_template() {
      location.href = this.templateUrl;
    },
    handleSuccess(res, file) {
      this.$alert(res.msg, '系统提示', {confirmButtonText: '知道了'});
    },
    /**
     * 打开弹窗调用方法
     * */
    open: function (id, tab) {
      console.log('导入参数:', id, 'tab来源:', tab)
      this.currentTab = tab || 'first';

      // 重置数据
      this.otherData.industry_ids = '';
      this.otherData.standard_name_id = '';
      // 根据tab来源设置接口和参数
      if (this.currentTab === 'second') {
        // 专业库
        this.submitUrl = 'importSpecialityTestInfo';
        this.otherData.standard_name_id = id;
        this.title = '导入专业库试题';
      } else {
        // 基础库
        this.submitUrl = 'importBaseTestInfo';
        this.otherData.industry_ids = id;
        this.title = '导入基础库试题';
      }

      this.visible = true;
    },
    closeDialog: function () {
      this.visible = false;
    },
    refresh: function () {
      this.$emit("refresh");
    }
  }
}
</script>


