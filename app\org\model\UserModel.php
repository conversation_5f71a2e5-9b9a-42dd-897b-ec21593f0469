<?php


namespace app\org\model;


use app\model\FileModel;
use think\Model;
use think\App;
use think\facade\Db;
use app\model\SettingModel;

//权限配置模块
class UserModel extends Model
{
    //参数格式化
    public static function codeToText($info){
        if(empty($info)){
            return $info;
        }
        foreach (config('global.role') as $v){
            if($v['value']==$info['role']||$v['value']==$info['ROLE']){
                $info['role_name'] = $v['label'];
            }
        }
        $info['role'] = is_numeric($info['ROLE'])?$info['ROLE']:$info['role'];
        $info['status_name'] = $info['status']==1?'正常':'禁用';
        $info['headUrl'] = empty($info['head'])?'':FileModel::getFile(0,$info['head']);
        foreach ($info as $k=>$v){
            $info[$k] = $v==='0000-00-00'?'':$v;
            if(is_int($v)){
//                $info[$k] = (string)$v;
            }
            if($v===null){
                $info[$k] = '';
            }
        }
        return $info;
    }

}