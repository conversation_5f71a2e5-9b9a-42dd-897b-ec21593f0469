<?php

namespace app\demo\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use hg\apidoc\annotation as Apidoc;
use app\BaseController;
use app\setting\controller\Config;

/**
 * @Apidoc\Title("权限管理")
 * @Apidoc\Group("Demo")
 * @Apidoc\Sort(1)
 */
class Setting extends BaseController {

    /**
     * @Apidoc\Title("获取权限")
     * @Apidoc\Param("key", type="string",default="", desc="参数")
     * @Apidoc\Param("model", type="string",default="", desc="模块 crm")
     * @Apidoc\Returned("data", type="object", desc="所有权限返回true，部门权限返回部门id数组，没有权限返回false")
     */
    public function isAdmin($key='module_crm_admin',$model='crm',$dept=true) {
        $result = Config::isAdmin($key,$model,$dept);
        result($result);
    }

    /**
     * @Apidoc\Title("获取配置项")
     * @Apidoc\Param("key", type="string",default="", desc="参数")
     * @Apidoc\Param("model", type="string",default="", desc="模块 crm")
     * @Apidoc\Returned("data", type="string", desc="返回配置值")
     */
    public function getValue($key='module_crm_admin',$model='crm') {
        $result = Config::getValue($key,$model);
        result($result);
    }





}
