<?php
namespace app\api\model;

use think\facade\Cache;
use think\facade\Db;
use Medoo\Mdb;

class SecsModel
{
     //专家库系统相关接口
     private static $secsInterface = [
        '查询所有安标专家类别' => 'secsExpert/getAllCategory',
        '评审任务接收接口' =>'secsTask/receive',
        '专家请假类别字典' =>'secsExpert/dict-data/page',
        '任务取消通知'=>'secsTask/cancalTaskNotice',
        '任务完成通知'=>'secsTask/finishedTaskNotice',
        '通过专家ids获取专家请假次数'=>'secsTask/getApply4LeaveCountByExpertIds',
        '专家临时请假接口'=>'secsTask/apply4Leave',
        '发送手机短信验证码'=>'secs/expert-regist-apply/send-sms-code',
        '专家休假记录列表'=>'secsTask/page/vacation-record',
        '新增专家休假记录'=>'secsTask/create/vacation',
        '待专家反馈的任务列表'=>'secsTask/taskList/getWait_feedback',
        '获取专家注册状态'=>'secsExpert/getSecsExpertRegistStatus',
    ];

    public function __construct()   
    {

    }

    /**
     * 获取专家系统配置
     * @return array
     **/
    public static function getSecsConfig() {
        //专家接口对应的ID
        $id = config('app.secs_expert_id');
        $secsConfig = Db::table('top_login_app')->where(['id'=>$id])->find();
        //根据配置判断是否是政务内容
        $checkZwNet = config('app.checkZwNet');
        if($checkZwNet){
            //http://172.42.136.164/platform/expert/admin-api/
            $secsConfig['redirect_uri'] =  str_replace('http://',"https://",$secsConfig['redirect_uri']);
            $secsConfig['redirect_uri'] = str_replace('172.42.136.164/platform/expert','zj.cdsafety.org.cn',$secsConfig['redirect_uri']);
        }
        return $secsConfig;
    }


    public static function getSecsInterfaceData($apiName,$param=[],$method="post")
    {
        if(empty($apiName)){
            $titles = [];
            foreach(self::$secsInterface as $k=>$v){
                $titles[] = $k."=>".$v;
            }

            return result('',401,'apiName参数错误，可以从['.implode("，",$titles).']中选择。');
        }

        // 将各种可能的true值统一转换为布尔型true
        $secsConfig = self::getSecsConfig();
        $baseUrl = $secsConfig['redirect_uri'];
        if(empty($baseUrl)){
            return result('',401,'专家系统接口地址未配置(APP/secs_expert_id)。');
        }
        $url = $baseUrl.$apiName;
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);

        // 准备请求头信息
        $headers = [
            'appid:'.$secsConfig['appid'],
            'appkey:'.$secsConfig['appkey'],
        ];

		if(!empty($param['taskcode'])){
			$time = date("Y-m-d H:i:s");
			$param['time'] = $time;
			$param['sign'] = md5($param['taskcode'].$time.$secsConfig['appkey']);
		}
	
        $method=strtolower($method);
        if($method=='post') {
            // POmethodT请求设置
            curl_setopt($curl, CURLOPT_POST, true);
			curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($param));
            $headers[] = 'Content-Type: application/json;charset=utf-8';
        }else if($method=='put'){
			curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "PUT");
			curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($param));
			$headers[] = 'Content-Type: application/json;charset=utf-8';
        } else {
            // GET请求设置
            curl_setopt($curl, CURLOPT_POST, false);
            if(!empty($param)) {
                $url .= '?' . http_build_query($param);
                curl_setopt($curl, CURLOPT_URL, $url);
            }
        }
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        
        // 记录请求信息，包含完整URL和请求头
	    self::logs("request", [$apiName, $param, $method], [
	        'url' => $url,
	        'headers' => $headers
	    ]);
        // 执行请求并获取响应
        $res = curl_exec($curl);
        
        // 获取请求信息用于调试
        $info = curl_getinfo($curl);
        
        // 记录响应信息
		self::logs("response", $res, [
		    'info' => $info,
		    'status' => $info['http_code']
		]);
        
        curl_close($curl);
        $res = json_decode($res,true);
        return $res;
    }

    /**
     * 安标系统通过手机号获取跳转url后缀
     * @return string
     **/
    public static function getSsoUrlToken()
    {
    	$phone = Db::table('top_expert')->where(['id'=>$_SESSION['expert']['id']])->value('mobile');
        $res = self::getSecsInterfaceData('system/auth/secs_login',['phone'=>$phone],'post');
        if($res['code']!=0){
            return result('',$res['code'],$res['msg']);
        }else{
            return $res['data'];
        }
    }


	/**
	 * 记录日志
	 * @param string $type 日志类型 (request/response)
	 * @param mixed $data 日志数据
	 * @param array $extra 额外信息
	 * @return void
	 */
	public static function logs($type, $data=[], $extra=[])
	{
		$path = config('app.secs_log_path')."/SecsModel.log";
        // 检查目录或文件是否存在，如果不存在则创建
        if (!file_exists($path)) {
            $dir = dirname($path);
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
            touch($path);	
        }
		
		$logData = [];
		$logData['time'] = date("Y-m-d H:i:s");
		$logData['type'] = $type;
		
		if ($type == 'request') {
			// 请求日志格式化
			$apiName = isset($data[0]) ? $data[0] : '';
			$params = isset($data[1]) ? $data[1] : [];
			$method = isset($data[2]) ? $data[2] : 'post';
			$url = isset($extra['url']) ? $extra['url'] : '';
			$headers = isset($extra['headers']) ? $extra['headers'] : [];
			
			$logData['request'] = [
				'url' => $url,
				'method' => strtoupper($method),
				'api_name' => $apiName,
				'headers' => $headers,
				'params' => $params
			];
		} elseif ($type == 'response') {
			// 响应日志格式化
			$logData['response'] = $data;
		} else {
			// 其他类型日志
			if (is_array($type)) {
				$type = json_encode($type, JSON_UNESCAPED_UNICODE);
			}
			$logData['message'] = $type;
			$logData['data'] = $data;
		}
		
		// 格式化为易于复制的JSON格式
		$formattedLog = json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
		file_put_contents($path, "\r\n" . $formattedLog . "\r\n", FILE_APPEND);
	}
    

}