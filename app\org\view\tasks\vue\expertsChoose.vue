<template>
  <el-dialog :title="title" :visible.sync="visible" width="960px" append-to-body>
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form :inline="true" :model="model"  size="mini" label-width="100px" ref="ruleForm" class="tableSearch floatForm">
          <el-form-item label="">
            <el-input v-model="model.keyword" placeholder="请输入搜索内容~"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm('ruleForm')" :loading="loading" size="mini">查询</el-button>
          </el-form-item>
        </el-form>
        <el-table
            ref="multipleTable"
            :data="tableData"
            tooltip-effect="dark"
            style="width: 100%"
            stripe
            size="small"
            row-key="user_id"
            @selection-change="handleSelectionChange">
          <el-table-column
              type="selection"
              width="55">
          </el-table-column>
          <el-table-column
              label="序号"
              type="index"
              width="55">
          </el-table-column>
          <el-table-column
              prop="user_name"
              label="姓名">
          </el-table-column>
          <el-table-column
              prop="mobile"
              label="手机号">
          </el-table-column>
          <el-table-column
              prop="avoid_name"
              label="回避企业">
          </el-table-column>
          <el-table-column
              prop="employ_date"
              label="聘用日期">
          </el-table-column>
          <el-table-column
              prop="offer_info"
              label="专家受聘情况">
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <div style="text-align: right;margin-top: 15px;">
      <el-button @click="cancelBtn">取消</el-button>
      <el-button type="primary" @click="confirmBtn">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>

module.exports  = {
  name: "dataSource",
  data(){
    return{
      title:'专家选择',
      visible:false,
      model:{
        keyword:'',
      },
      loading:false,
      tableData:[],
      multipleSelection:[],
      checkedData:[],
      url:{
        getlist:'/general/secure/index.php/org/manager/expertsAddList'
      }
    }
  },
  mounted(){
    //this.getData();
  },
  watch:{

  },
  methods:{
    getData(){
      var that=this;
      var httpurl=that.url.getlist;
      Person(httpurl,that.model,function (res) {
        that.loading=false;
        that.tableData=res.data;
      },that);
    },
    submitForm(formName) {
      var that=this
      this.$refs[formName].validate((valid) => {
        if (valid) {
          that.loading=true;
          this.getData();//验证通过，发起请求
        }
      });
    },
    /**
     * 表格选中
     * */
    handleSelectionChange(val) {
      this.checkedData = val;
      // var arr=[]
      // for(var i in val){
      //   arr.push(val[i].id)
      // }
      // this.checkedData=arr;
    },
    /**
     *
     * */
    initialize(data){
      this.visible=true;
      this.checkedData = [];
      data.keyword = '';
      this.model = data;
      this.getData();
    },
    cancelBtn(){
      this.visible=false;
    },
    confirmBtn(){
      this.$emit("ok",this.checkedData)
      this.visible=false
    }
  }
}
</script>

<style scoped>
.floatForm{margin-top: -15px;}
.titleHeader{font-size: 16px;font-weight: 600;letter-spacing: 2px;padding-left: 5px;border-left: 3px solid #4C6DC1;
  margin-bottom: 11px;}
.fontcolor{color: #409eff;cursor: pointer;line-height: 24px;display: inline-block;}
</style>