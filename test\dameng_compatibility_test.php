<?php
/**
 * 达梦数据库兼容性测试脚本
 * 
 * 使用方法：
 * php test/dameng_compatibility_test.php
 */

// 引入ThinkPHP框架
require_once __DIR__ . '/../vendor/autoload.php';

use think\facade\Db;
use app\admin\service\DatabaseCompatibilityService;

class DamengCompatibilityTest {
    
    public function __construct() {
        echo "=== 达梦数据库兼容性测试 ===\n";
    }
    
    /**
     * 运行所有测试
     */
    public function runAllTests() {
        try {
            $this->testDatabaseType();
            $this->testGetTables();
            $this->testGetTableFields();
            $this->testTableExists();
            $this->testFieldExists();
            
            echo "\n=== 所有测试完成 ===\n";
        } catch (Exception $e) {
            echo "测试失败: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * 测试数据库类型检测
     */
    private function testDatabaseType() {
        echo "\n1. 测试数据库类型检测...\n";
        
        $dbType = DatabaseCompatibilityService::getDatabaseType();
        echo "检测到的数据库类型: {$dbType}\n";
        
        $config = DatabaseCompatibilityService::getDatabaseConfig();
        echo "数据库配置类型: " . ($config['type'] ?? 'unknown') . "\n";
        
        if (in_array($dbType, ['dm', 'dameng'])) {
            echo "✓ 达梦数据库类型检测成功\n";
        } else {
            echo "! 当前不是达梦数据库，类型为: {$dbType}\n";
        }
    }
    
    /**
     * 测试获取表列表
     */
    private function testGetTables() {
        echo "\n2. 测试获取表列表...\n";
        
        try {
            $tables = DatabaseCompatibilityService::getTables();
            echo "成功获取到 " . count($tables) . " 个表\n";
            
            // 显示前5个表作为示例
            $displayCount = min(5, count($tables));
            for ($i = 0; $i < $displayCount; $i++) {
                echo "  - " . $tables[$i]['table_name'] . "\n";
            }
            
            if (count($tables) > 5) {
                echo "  ... 还有 " . (count($tables) - 5) . " 个表\n";
            }
            
            echo "✓ 获取表列表测试成功\n";
        } catch (\Exception $e) {
            echo "✗ 获取表列表失败: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * 测试获取表字段
     */
    private function testGetTableFields() {
        echo "\n3. 测试获取表字段...\n";
        
        try {
            // 先获取一个表名
            $tables = DatabaseCompatibilityService::getTables();
            if (empty($tables)) {
                echo "! 没有可用的表进行测试\n";
                return;
            }
            
            $testTable = $tables[0]['table_name'];
            echo "使用表 '{$testTable}' 进行测试\n";
            
            $fields = DatabaseCompatibilityService::getTableFields($testTable);
            echo "成功获取到 " . count($fields) . " 个字段\n";
            
            // 显示字段信息
            foreach ($fields as $field) {
                echo "  - {$field['field_name']} ({$field['field_type']})";
                if (!empty($field['field_comment']) && $field['field_comment'] !== $field['field_name']) {
                    echo " - {$field['field_comment']}";
                }
                echo "\n";
            }
            
            echo "✓ 获取表字段测试成功\n";
        } catch (\Exception $e) {
            echo "✗ 获取表字段失败: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * 测试表是否存在
     */
    private function testTableExists() {
        echo "\n4. 测试表存在性检查...\n";
        
        try {
            // 测试一个存在的表
            $tables = DatabaseCompatibilityService::getTables();
            if (!empty($tables)) {
                $existingTable = $tables[0]['table_name'];
                $exists = DatabaseCompatibilityService::tableExists($existingTable);
                if ($exists) {
                    echo "✓ 表 '{$existingTable}' 存在性检查成功\n";
                } else {
                    echo "✗ 表 '{$existingTable}' 存在性检查失败\n";
                }
            }
            
            // 测试一个不存在的表
            $nonExistingTable = 'non_existing_table_' . time();
            $exists = DatabaseCompatibilityService::tableExists($nonExistingTable);
            if (!$exists) {
                echo "✓ 不存在表 '{$nonExistingTable}' 检查成功\n";
            } else {
                echo "✗ 不存在表 '{$nonExistingTable}' 检查失败\n";
            }
            
        } catch (\Exception $e) {
            echo "✗ 表存在性检查失败: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * 测试字段是否存在
     */
    private function testFieldExists() {
        echo "\n5. 测试字段存在性检查...\n";
        
        try {
            // 获取一个表和其字段
            $tables = DatabaseCompatibilityService::getTables();
            if (empty($tables)) {
                echo "! 没有可用的表进行测试\n";
                return;
            }
            
            $testTable = $tables[0]['table_name'];
            $fields = DatabaseCompatibilityService::getTableFields($testTable);
            
            if (!empty($fields)) {
                $existingField = $fields[0]['field_name'];
                $exists = DatabaseCompatibilityService::fieldExists($testTable, $existingField);
                if ($exists) {
                    echo "✓ 字段 '{$testTable}.{$existingField}' 存在性检查成功\n";
                } else {
                    echo "✗ 字段 '{$testTable}.{$existingField}' 存在性检查失败\n";
                }
            }
            
            // 测试一个不存在的字段
            $nonExistingField = 'non_existing_field_' . time();
            $exists = DatabaseCompatibilityService::fieldExists($testTable, $nonExistingField);
            if (!$exists) {
                echo "✓ 不存在字段 '{$testTable}.{$nonExistingField}' 检查成功\n";
            } else {
                echo "✗ 不存在字段 '{$testTable}.{$nonExistingField}' 检查失败\n";
            }
            
        } catch (\Exception $e) {
            echo "✗ 字段存在性检查失败: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * 测试达梦数据库特定SQL
     */
    private function testDamengSpecificSQL() {
        echo "\n6. 测试达梦数据库特定SQL...\n";
        
        $dbType = DatabaseCompatibilityService::getDatabaseType();
        if (!in_array($dbType, ['dm', 'dameng'])) {
            echo "! 当前不是达梦数据库，跳过特定SQL测试\n";
            return;
        }
        
        try {
            // 测试获取用户表
            echo "测试 USER_TABLES 查询...\n";
            $sql = "SELECT TABLE_NAME FROM USER_TABLES ORDER BY TABLE_NAME";
            $tables = Db::query($sql);
            echo "通过 USER_TABLES 获取到 " . count($tables) . " 个表\n";
            
            if (!empty($tables)) {
                $testTable = $tables[0]['TABLE_NAME'];
                echo "测试 USER_TAB_COLUMNS 查询...\n";
                $sql = "SELECT COLUMN_NAME, DATA_TYPE, COMMENTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = ? ORDER BY COLUMN_ID";
                $fields = Db::query($sql, [$testTable]);
                echo "通过 USER_TAB_COLUMNS 获取到 " . count($fields) . " 个字段\n";
            }
            
            echo "✓ 达梦数据库特定SQL测试成功\n";
        } catch (\Exception $e) {
            echo "✗ 达梦数据库特定SQL测试失败: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * 性能测试
     */
    private function performanceTest() {
        echo "\n7. 性能测试...\n";
        
        try {
            $startTime = microtime(true);
            
            // 测试获取表列表的性能
            $tables = DatabaseCompatibilityService::getTables();
            $tablesTime = microtime(true) - $startTime;
            
            echo "获取 " . count($tables) . " 个表耗时: " . round($tablesTime * 1000, 2) . " ms\n";
            
            if (!empty($tables)) {
                $startTime = microtime(true);
                $testTable = $tables[0]['table_name'];
                $fields = DatabaseCompatibilityService::getTableFields($testTable);
                $fieldsTime = microtime(true) - $startTime;
                
                echo "获取表 '{$testTable}' 的 " . count($fields) . " 个字段耗时: " . round($fieldsTime * 1000, 2) . " ms\n";
            }
            
            echo "✓ 性能测试完成\n";
        } catch (\Exception $e) {
            echo "✗ 性能测试失败: " . $e->getMessage() . "\n";
        }
    }
}

// 运行测试
if (php_sapi_name() === 'cli') {
    $test = new DamengCompatibilityTest();
    $test->runAllTests();
    
    // 如果是达梦数据库，运行额外的测试
    if (in_array(DatabaseCompatibilityService::getDatabaseType(), ['dm', 'dameng'])) {
        $test->testDamengSpecificSQL();
        $test->performanceTest();
    }
} else {
    echo "请在命令行环境下运行此测试脚本\n";
}
