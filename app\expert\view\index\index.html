﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>专家管理</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/expert.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .el-badge__content.is-fixed { right: 20px;z-index: 9;}
        .is-active { background-color:#3556B8 !important;}
        .el-menu-item i { color:#ffffff;}
        .el-form-item{margin-bottom: 0;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <el-container>
        <el-header style="text-align:center;font-size: 12px;height:50px;line-height:50px;">
            <b style="font-size:16px;float:left">成都市企业安全生产标准化信息管理系统——专家端</b>
            <span></span>
            <el-dropdown style="float:right">
                    <span><el-image
                            style="width: 30px; height: 30px;border-radius:30px;float:left;margin:10px;"
                            :src="head"
                            fit="cover"></el-image>
                        {$user.name}
                        <i class="el-icon-more" style="margin:0 15px;transform: rotate(90deg);color:#999;"></i></span>
                <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item @click.native="editpassword">修改密码</el-dropdown-item>
                    <el-dropdown-item @click.native="logout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
            </el-dropdown>
        </el-header>
        <el-container>
            <el-aside width="200px">
                <el-menu
                        default-active="{$url}"
                        :style="{height:(height-50)+'px'}"
                        :height="height"
                        @select="open"
                        background-color="#436CE6"
                        text-color="#ffffff"
                        active-text-color="#ffffff">
                    <el-menu-item index="index/main" v-if="'{$status}'==7">
                        <i class="el-icon-house"></i>
                        <span slot="title">首页</span>
                    </el-menu-item>
                    <!--<el-menu-item index="index/info">
                        <i class="el-icon-office-building"></i>
                        <span slot="title">我的信息</span>
                    </el-menu-item>
                    <el-menu-item index="schedule/index" v-if="'{$status}'==7">
                        <i class="el-icon-office-building"></i>
                        <span slot="title">我的日程</span>
                    </el-menu-item>-->
                    <el-menu-item index="task/index" v-if="'{$status}'==7">
                        <i class="el-icon-price-tag"></i>
                        <span slot="title">评审任务</span>
                    </el-menu-item>
                    <!-- <el-menu-item index="index/gotoSecsUrl?url=safetyLabelTask/abTaskTodo" v-if="'{$status}'==7">
                        <i class="el-icon-price-tag"></i>
                        <span slot="title">待办任务</span>
                    </el-menu-item>
                     <el-menu-item index="index/gotoSecsUrl?url=safetyLabelTask/abTaskDone">
                        <i class="el-icon-price-tag"></i>
                        <span slot="title">已办任务</span>
                    </el-menu-item> -->
                    <el-menu-item index="discuss/index" v-if="'{$status}'==7">
                        <i class="el-icon-price-tag"></i>
                        <span slot="title">在线交流</span>
                    </el-menu-item>
                    <el-menu-item index="index/gotoSecsUrl?url=adjustVacation/abContinueToEngageSb">
                        <i class="el-icon-price-tag"></i>
                        <span slot="title">续聘申请及退出</span>
                    </el-menu-item>
                    <el-menu-item index="index/gotoSecsUrl?url=adjustVacation/safetyLabelChange">
                        <i class="el-icon-price-tag"></i>
                        <span slot="title">资料变更申请</span>
                    </el-menu-item>
                    <el-menu-item index="index/gotoSecsUrl?url=adjustVacation/askForLeave">
                        <i class="el-icon-price-tag"></i>
                        <span slot="title">休假管理</span>
                    </el-menu-item>
                    
                </el-menu>
            </el-aside>
            <el-main :style="{height:(height-50)+'px'}">
                <iframe id="iframe" :src="iframeSrc" width="100%" height="100%" border="0" frameborder="0" framespacing="0" marginheight="0" marginwidth="0" sandbox="allow-modals allow-scripts allow-same-origin allow-popups allow-downloads allow-popups-to-escape-sandbox allow-storage-access-by-user-activation"></iframe>
            </el-main>
        </el-container>
    </el-container>
    <el-dialog title="修改密码" width="500px" :visible.sync="visible">
        <el-form :model="form">
            <el-descriptions class="margin-top" title="" :column="1" size="small" border>
                <el-descriptions-item label="原密码">
                    <el-form-item label="">
                        <el-input type="password" v-model="form.old_password" ></el-input>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label="新密码">
                    <el-form-item label="">
                        <el-input type="password" v-model="form.new_password" ></el-input>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label="确认新密码">
                    <el-form-item label="">
                        <el-input type="password" v-model="form.confirm_password" ></el-input>
                    </el-form-item>
                </el-descriptions-item>
            </el-descriptions>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="visible = false">取消</el-button>
            <el-button type="primary" @click="onSubmit">保存</el-button>
        </div>
    </el-dialog>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                visible:false,
                title: '',
                head: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
                iframeSrc: '',
                form:{
                    old_password:'',
                    new_password:'',
                    confirm_password:'',
                },
                loading: false,
                height: document.documentElement.clientHeight,
            };
        },
        components: {
        },
        methods: {
            open(key, keyPath) {
                if(key){
                    this.iframeSrc = '/general/toppingsoft/index.php/expert/'+key;
                }
            },
            logout() {
                location.href = 'loginout';
            },
            editpassword:function (){
                this.form = {
                    old_password:'',
                    new_password:'',
                    confirm_password:'',
                };
                this.visible = true;
            },
            onSubmit:function (){
                var _this = this;
                var params = _this.form;
                _this.loading = true;
                axios.post('editPassword', params).then(function (res) {
                    if (res.data.code == 0) {
                        _this.visible = false;
                        _this.$alert('密码修改成功，请重新登陆', '提示', {
                            confirmButtonText: '确定',
                            callback: function action() {
                                _this.logout();
                            }
                        });
                    }else{
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                    }
                }).catch(function (error) {
                    console.log("出现错误:",error);
                });
            },
        },
        mounted() {
            this.open('{$url}');
        }
    })

    window.addEventListener("message", function (e) {
        if (e.data && e.data.redirectUrl) {
            window.location.href = e.data.redirectUrl;
        }
    });
</script>


</body>
</html>