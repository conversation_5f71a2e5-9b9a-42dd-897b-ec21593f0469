# 周报生成器使用说明

本工具用于根据`功能内容.txt`文件自动生成从2024年9月到2025年6月的项目周报文档（docx格式）。

## 功能特点

- 自动解析`功能内容.txt`中的项目阶段和每周工作内容
- 生成标准格式的周报docx文档
- 自动填充项目名称、日期、工作内容等信息
- 自动计算每周的起止日期
- 前两个月（8周）自动添加需求调研和文档编写内容

## 使用前准备

### 安装依赖

运行脚本前，请确保已安装以下Python库：

```bash
pip install python-docx
```

### 文件结构

确保以下文件结构：

```
docs/
  ├── 功能内容.txt     # 项目功能内容描述文件
  ├── generate_weekly_reports.py  # 周报生成脚本
  └── 周报/            # 生成的周报将保存在此目录（自动创建）
```

## 使用方法

1. 打开命令行终端
2. 切换到项目根目录
3. 运行以下命令：

```bash
python docs/generate_weekly_reports.py
```

4. 脚本将在`docs/周报/`目录下生成所有周报文档

## 周报格式说明

生成的周报包含以下内容：

1. **标题**：成都市企业安全生产标准化信息管理系统(升级改造建设项目) 项目施工周报
2. **日期**：当前周的起止日期
3. **项目名称**：成都市企业安全生产标准化信息管理系统（升级改造建设项目）
4. **本周项目情况及存在问题**：
   - 本周项目开展的工作（根据功能内容.txt自动提取）
   - 项目会议、沟通和文档编写等工作
   - 当前存在的问题
5. **下周工作要点**：
   - 下周计划开展的功能开发（根据功能内容.txt自动提取）
   - 其他常规工作
6. **签名信息**：
   - 承建单位：成都市城市安全与应急管理研究院
   - 项目经理：陈倩
   - 日期：当前周的结束日期

## 注意事项

- 脚本会自动在前两个月（8周）添加需求调研和文档编写的内容
- 如需修改周报格式或内容，请编辑`generate_weekly_reports.py`文件
- 如需修改项目起止日期，请修改脚本中的`START_DATE`和`END_DATE`变量