<?php

namespace app\org\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use app\api\model\SecsModel;
use hg\apidoc\annotation as Apidoc;
use think\facade\View;
use think\Request;
use think\facade\Db;

/**
 * @Apidoc\Title("评审单位首页")
 * @Apidoc\Group("Index")
 * @Apidoc\Sort(1)
 */
class Index extends Base {

    /**
     * @Apidoc\Title("评审单位首页")
     * @Apidoc\Desc("评审单位首页")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function index() {
        View::assign('title','首页');
        $url = 'index/main';
        View::assign('user',$_SESSION['org']);
        View::assign('url',$url);
        return view();
    }
    /**
     * @Apidoc\Title("区县应急局首页")
     * @Apidoc\Desc("区县应急局首页")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function main() {

        if (request()->isAjax()) {
            $container1['title'] =['90分以上','80-89分','70-79分','60-69分','60分以下'];
            $container1['value'][] = Db::table('top_cay_exam_paper')->where('total_score','>=',90)->count();
            $container1['value'][] = Db::table('top_cay_exam_paper')->where('total_score','>=',80)->where('total_score','<',90)->count();
            $container1['value'][] = Db::table('top_cay_exam_paper')->where('total_score','>=',70)->where('total_score','<',80)->count();
            $container1['value'][] = Db::table('top_cay_exam_paper')->where('total_score','>=',60)->where('total_score','<',70)->count();
            $container1['value'][] = Db::table('top_cay_exam_paper')->where('total_score','<',60)->count();
            $res['container']['container1'] = $container1;
            result($res);
        } else {
//            $total['company']['sum'] = number_format(Db::table('top_org_tasks')->where("org_id={$_SESSION['org']['id']} and (status in (7,1) or status > 8)")->count());
            $total['company']['sum1'] = number_format(Db::table('top_org_tasks')->where(['org_id'=>$_SESSION['org']['id']])->where('status','>',8)->count());
            $total['company']['sum2'] = number_format(Db::table('top_org_tasks')->where(['org_id'=>$_SESSION['org']['id']])->where(['result_status'=>7])->count());
            $total['company']['sum3'] = number_format(Db::table('top_org_tasks')->where(['org_id'=>$_SESSION['org']['id']])->where(['status'=>1])->count());
            $total['company']['sum'] = $total['company']['sum1'] + $total['company']['sum2'] + $total['company']['sum3'];
            $result['notify'] = Db::table('top_notify')->alias('a')
                ->leftJoin('top_notify_type t','a.type = t.id')
                ->where(['a.is_del'=>0,'a.is_show'=>1])
                ->order('date desc,create_time desc')
                ->field('a.*,t.name mb_type')
                ->page(1,10)->select()->toArray();
            $result['ca'] = Db::table('top_org_tasks')
                ->where([['org_id','=',$_SESSION['org']['id']],['status','=',1]])
                ->order('id desc')
                ->page(1,10)->select()->toArray();
            View::assign('total',$total);
            View::assign('result',$result);
            View::assign('title','首页');
            return view();
        }
    }

    public function policyInfo($id = 0){
        $where[] = ['is_del','=',0];
        $where[] = ['id','=',$id];
        $res = Db::table('top_notify')
            ->where($where)
            ->find();
        if(empty($res)){
            result('',1002,'数据有误');
        }
        result($res);
    }

    public function editPassword() {
        $old_password = request()->param('old_password');
        $new_password = request()->param('new_password');
        $confirm_password = request()->param('confirm_password');
        $user = Db::table('top_org_user')->where(['id'=>$_SESSION['org']['user_id']])->find();
        if($user['password']!==crypt($old_password,$user['salt'])){
            result('',1003,'原密码错误');
        }
        $pattern = '/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$|^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$|^(?=.*[a-z])(?=.*[A-Z])(?=.*[\W_]).{8,}$|^(?=.*[a-z])(?=.*\d)(?=.*[\W_]).{8,}$|^(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$/';
        // 使用preg_match函数进行匹配
        if (!preg_match($pattern, $new_password)) {
            result('',1001,'请填写6-18位密码，并且须由大写字母、小写字母、数字及特殊符号中的三种或三种以上进行组合');
        }
        if($new_password!==$confirm_password){
            result('',1005,'两次密码不一致');
        }
        $data['salt'] = create_nonce_str(8);
        $data['password'] = crypt($new_password,$data['salt']);
        $re = Db::table('top_org_user')->where(['id'=>$_SESSION['org']['user_id']])->update($data);
        if($re){
            result('',0,'密码修改成功，请重新登陆');
        }else{
            result('',1007,'修改失败');
        }
    }

    public function loginout() {
        $_SESSION['area'] = [];
        return redirect('/general/toppingsoft/index.php/org/login/login');
    }

    function gotoSecsUrl($url,$type='admin'){
        $phones = array(
            'admin'=>'13012345678',
            'zcys'=>'13188889999',
            'zczs'=>'13377778888',
            'xpys'=>'13422223333',
            'xpzs'=>'13599990000',
            'bgsp'=>'13611112222',
            'zjtj'=>'19988889999',
        );
        //如果登录已经超时，则退出
        if(empty($_SESSION['org']['id'])){
            header("Location: /general/toppingsoft/index.php/org/login/login");
        }

        if(empty($phones[$type])){
            result('',1002,'参数错误:'.$type);
        }
        $phone = '13012345678';
        $SecsConfig = SecsModel::getSecsConfig();
        $res = SecsModel::getSecsInterfaceData('system/auth/secs_login',['phone'=>$phone],'post');
        if($res['code']!=0){
            return result('',$res['code'],$res['msg']);
        }
        $urlExt = $res['data'];
        $url = $SecsConfig['domain'].$url.$urlExt;
        header("Location: $url");
    }


}
