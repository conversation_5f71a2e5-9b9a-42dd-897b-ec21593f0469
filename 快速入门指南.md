# DmQuery 快速入门指南

## 🚀 5分钟上手

### 第一步：打开工具
1. 在浏览器中访问 `DmQuery.php`
2. 确认页面顶部显示绿色连接状态 🟢

### 第二步：查看表数据
1. 点击左侧表列表中的任意表名
2. 数据会自动显示在右侧

### 第三步：执行自定义查询
1. 在SQL编辑器中输入：`SELECT * FROM 表名 LIMIT 10`
2. 点击"执行SQL"按钮
3. 查看结果

## 🎯 核心功能速览

| 功能 | 操作方法 | 用途 |
|------|----------|------|
| **查看表数据** | 点击左侧表名 | 快速浏览表内容 |
| **执行SQL** | 编辑器输入→执行SQL | 自定义查询 |
| **列过滤** | 列过滤→点击列标题 | 筛选特定数据 |
| **编辑数据** | 点击行末"编辑" | 修改单行数据 |
| **批量操作** | 批量操作→选择行 | 批量编辑/删除 |
| **查看表结构** | 表结构标签 | 查看字段信息 |

## 🔍 列过滤使用技巧

### 启用过滤
```
1. 点击"列过滤"按钮
2. 点击要过滤的列标题
3. 选择过滤模式：前端过滤 or 后端查询
```

### 过滤语法
```
张三          # 包含"张三"
=张三         # 精确等于"张三"  
>100          # 大于100
<50           # 小于50
张*           # 以"张"开头
*管理*        # 包含"管理"
```

### 模式选择
- **前端过滤**：当前页数据实时过滤，适合小数据量
- **后端查询**：修改SQL重新查询，适合大数据量

## 📝 常用操作示例

### 1. 查找特定用户
```sql
SELECT * FROM users WHERE name LIKE '%张%'
```

### 2. 按日期范围查询
```sql
SELECT * FROM orders WHERE create_time >= '2024-01-01'
```

### 3. 统计查询
```sql
SELECT status, COUNT(*) as count FROM orders GROUP BY status
```

### 4. 更新数据
```sql
UPDATE users SET status = '激活' WHERE id = 1
```

## ⚡ 快捷键

| 快捷键 | 功能 |
|--------|------|
| `Ctrl + Enter` | 执行SQL |
| `Ctrl + /` | 注释/取消注释 |
| `Escape` | 关闭弹窗 |

## 🛠️ 故障排除

### 连接问题
- 🔴 红色状态 → 检查数据库服务
- 🟡 黄色状态 → 正在连接，请等待

### 过滤问题
- 过滤不准确 → 点击"调试表格"查看控制台
- 中文不显示 → 点击"测试注释"检查

### 性能问题
- 查询慢 → 使用分页，添加WHERE条件
- 数据量大 → 选择"后端查询"模式

## 📋 最佳实践

### 查询优化
✅ **推荐**
```sql
-- 使用具体字段
SELECT id, name, status FROM users WHERE status = '激活'

-- 使用分页
SELECT * FROM large_table LIMIT 100
```

❌ **避免**
```sql
-- 避免全表扫描
SELECT * FROM large_table

-- 避免复杂子查询
SELECT * FROM table1 WHERE id IN (SELECT id FROM table2 WHERE ...)
```

### 数据编辑
✅ **推荐**
- 重要操作前先备份
- 使用事务处理批量操作
- 验证数据完整性

❌ **避免**
- 直接删除大量数据
- 不验证就批量更新

## 🎨 界面提示

### 状态指示器
- 🟢 **绿色**：正常运行
- 🔴 **红色**：连接异常  
- 🟡 **黄色**：处理中
- 🔵 **蓝色**：已过滤

### 按钮颜色含义
- **蓝色**：主要操作（执行、新增）
- **绿色**：成功操作（保存、确认）
- **黄色**：警告操作（编辑、导入）
- **红色**：危险操作（删除）
- **灰色**：辅助操作（取消、清除）

## 📞 获取帮助

### 内置调试工具
1. **测试注释** - 检查字段注释功能
2. **调试表格** - 输出表格结构信息  
3. **测试表结构** - 验证表结构查询

### 查看详细信息
- 执行时间和行数显示在结果下方
- 错误信息会以红色提示显示
- 操作成功会显示绿色确认信息

---

💡 **提示**：更多详细功能请参考《DmQuery使用说明.md》完整文档。
