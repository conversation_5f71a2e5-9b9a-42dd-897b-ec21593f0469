<?php

namespace app\model;

use think\Model;

/**
 * 微信接口
 */
class Wechat extends Model {

    private $appId = '';
    private $appSecret = '';


    public function __construct(array $data = [])
    {
        parent::__construct($data);
        $this->appId = config('wechat.chat.APPID');
        $this->appSecret = config('wechat.chat.APPSECRET');
    }

    public function userInfo($openid) {
        $accessToken = $this->getAccessToken();
        $url = "https://api.weixin.qq.com/cgi-bin/user/info?access_token=$accessToken&openid=$openid&lang=zh_CN";
        return json_decode(http_get($url), true);
    }

    /**
     * 获取微信 JS-SDK 的签名包
     *
     * 本函数用于生成微信 JS-SDK 所需的签名包，该签名包包含了 appId、nonceStr、timestamp、url 和 signature 等信息
     * 签名包用于确保微信客户端能够安全地调用微信 JS-SDK 提供的接口服务
     *
     * @param string $url JS-SDK 页面的 URL，必须是协议相对路径（如 /path ），不能是完整 URL（如 http://example.com/path）
     * @return array 包含签名信息的数组
     */
    public function getSignPackage($url) {
        $jsapiTicket = $this->getJsApiTicket();
        // 注意 URL 一定要动态获取，不能 hardcode.
        if (empty($url)) {
            $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
            $url = "$protocol$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
        }
        $timestamp = time();
        $nonceStr = create_nonce_str();
        // 这里参数的顺序要按照 key 值 ASCII 码升序排序
        $string = "jsapi_ticket=$jsapiTicket&noncestr=$nonceStr&timestamp=$timestamp&url=$url";
        $signature = sha1($string);
        $signPackage = array(
            "appId" => $this->appId,
            "nonceStr" => $nonceStr,
            "timestamp" => $timestamp,
            "url" => $url,
            "signature" => $signature,
        );
        return $signPackage;
    }

    /**
     * 获取微信JS-SDK的jsapi_ticket
     *
     * jsapi_ticket是在执行微信JS-SDK相关操作时需要的一个票据，
     * 需要全局存储与更新，本函数示范了如何从文件系统读取和更新此票据。
     * 如果票据过期，则会自动请求新的票据并存储。
     *
     * @return string jsapi_ticket 票据，用于微信JS-SDK的配置
     */
    public function getJsApiTicket() {
        // jsapi_ticket 应该全局存储与更新，以下代码以写入到文件中做示例
        $data = json_decode(file_get_contents("jsapi_ticket.json"));
        if ($data->expire_time < time()) {
            $accessToken = $this->getAccessToken();
            // 如果是企业号用以下 URL 获取 ticket
            // $url = "https://qyapi.weixin.qq.com/cgi-bin/get_jsapi_ticket?access_token=$accessToken";
            $url = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?type=jsapi&access_token=$accessToken";
            $res = json_decode(http_get($url));
            $ticket = $res->ticket;
            if ($ticket) {
                $data->expire_time = time() + 7000;
                $data->jsapi_ticket = $ticket;
                $fp = fopen("jsapi_ticket.json", "w");
                fwrite($fp, json_encode($data));
                fclose($fp);
            }
        } else {
            $ticket = $data->jsapi_ticket;
        }

        return $ticket;
    }
    /**
     * 获取微信access_token
     * access_token是调用微信接口的凭证，需要在过期前进行更新
     *
     * @return string 有效的access_token
     */
    public function getAccessToken() {
        // access_token 应该全局存储与更新，以下代码以写入到文件中做示例
        $data = json_decode(file_get_contents("access_token.json"));
        if ($data->expire_time < time()) {
            // 如果是企业号用以下URL获取access_token
            // $url = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=$this->appId&corpsecret=$this->appSecret";
            $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=$this->appId&secret=$this->appSecret";
            $res = json_decode(http_get($url));
            $access_token = $res->access_token;
            if ($access_token) {
                $data->expire_time = time() + 7000;
                $data->access_token = $access_token;
                $fp = fopen("access_token.json", "w");
                fwrite($fp, json_encode($data));
                fclose($fp);
            }
        } else {
            $access_token = $data->access_token;
        }
        return $access_token;
    }

    //发送模板消息
    public function send($template, $openid, $data, $url = '') {
        $accesstoken = $this->getAccessToken();
        $url = 'https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=' . $accesstoken;
        $param = [
            'touser' => $openid,
            'template_id' => $template,
            'url' => $url,
            'data' => $data,
        ];
        $result = http_post($url, json_encode($param));
        return json_decode($result,true);
    }

}
