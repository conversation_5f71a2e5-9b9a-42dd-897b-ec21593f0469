<style>
.my-autocomplete li{line-height: normal;padding: 7px;}
.my-autocomplete li .name{text-overflow: ellipsis;overflow: hidden;}
.my-autocomplete li .addr{font-size: 12px;color: #b4b4b4;}
.my-autocomplete li .highlighted{color: #ddd;}
.el-cascader { width:100%;}
.el-form-item__content .el-input-group { vertical-align: middle;}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 150px;
  height: 150px;
  line-height: 150px;
  text-align: center;
}
.avatar {
  width: 150px;
  height: 150px;
  display: block;
}
</style>
<template>
  <el-dialog :title="title" :close-on-click-modal="false"  modal="false" :visible.sync="visible" width="95%" top="10px" @close="refresh()" append-to-body="true" label-position="top">
    <el-form ref="form" :model="data" class="tableSearch" label-position="right" label-width="200px" :rules="rules">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="企业安全生产标准化评定标准" prop="standard_id">
            <el-select v-model="data.standard_id" size="mini" placeholder="请选择">
              <el-option v-for="item in config.standard" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="企业安全生产标准化级别" prop="level">
            <el-select v-model="data.level" size="mini" placeholder="请选择">
              <el-option key="三级" label="三级" value="三级">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="现场评审报告" prop="standard">
            <el-upload
                class="avatar-uploader"
                action="upload"
                :show-file-list="false"
                :on-success="(response,file,fileList)=>uploadSuccess(response,file,fileList,'standard')"
                :before-upload="uploadBefore">
              <img v-if="data.standardUrl" :src="data.standardUrl" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="企业安全生产标准化证书" prop="image">
            <el-upload
                class="avatar-uploader"
                action="upload"
                :show-file-list="false"
                :on-success="(response,file,fileList)=>uploadSuccess(response,file,fileList,'image')"
                :before-upload="uploadBefore">
              <img v-if="data.imageUrl" :src="data.imageUrl" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="企业安全生产标准化证书编号" prop="legal">
            <el-input v-model="data.code" size="mini"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="证书有效期" prop="start">
            <el-date-picker
                v-model="data.start"
                style="width:130px;"
                size="mini"
                type="date"
                placeholder="选择日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd">
            </el-date-picker>
            ~
            <el-date-picker
                v-model="data.end"
                style="width:130px;"
                size="mini"
                type="date"
                placeholder="选择日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="24" style="text-align: center;">
          <el-button type="primary" @click="submit()" v-loading.fullscreen.lock="loading">保存</el-button>
          <el-button @click="visible = false">关闭</el-button>
        </el-col>
      </el-row>
    </el-form>
  </el-dialog>
</template>
<script>
module.exports = {
  name: "add",
  // 模板导入区
  components: {
  },
  data:function() {
    return {
      id:0,
      isAdmin: false,
      visible: false,
      title: '新增',
      model: '',
      loading: false,
      user_id: 0,
      date_object:new Date(),
      user_name:'',
      data: {
      },
      rules: {
      },
      config:[],
      contractData:[],
      salesData:[],
      userData:[],
      dels:[],
      dels2:[],
      imageUrl:'',
    }
  },
  mounted: function(){
    // this.getConfig();
  },
  created:function(){
  },
  methods: {
    /**
     * 打开弹窗调用方法
     * */
    open: function (row) {
      console.log(row)
      row = row?row:{id:0};
      var _this =this;
      _this.data = {
        company_id:row.id,
        standard_id:'',
        level:'三级',
        standard:'',
        standardUrl:'',
        image:'',
        imageUrl:'',
        code:'',
        start:'',
        end:'',
      };
      _this.visible = true;
      _this.getConfig();
      // _this.getInfo(row.id);
    },
    submit: function () {
      var _this = this;
      var param = _this.data;
      this.$refs.form.validate(function (valid) {
        if(valid){
          _this.loading = true;
          axios.post("caSave", param).then(function (res) {
            _this.$message({
              message: res.data.msg,
              type: res.data.type
            });
            if (res.data.code == 0) {
              _this.visible = false;
              _this.$emit("ok");
            }
            _this.loading = false;
          }).catch(function (error) {
            console.log("出现错误:",error);
            _this.loading = false;
          });
        }
      });
    },
    uploadBefore(file) {
      const isJPG = file.type === 'image/jpeg';
      const isPNG = file.type === 'image/png';
      if(!isJPG&&!isPNG){
        this.$message.error('请上传jpg图片');
      }
      return isJPG||isPNG;
    },
    uploadSuccess(res, file,fileList,field) {
      var files = [];
      console.log(res)
      for(var i in fileList){
        files.push(fileList[i].response??fileList[i]);
      }
      this.data[field] = res.data.code;
      this.data[field+'Url'] = res.data.url;
    },
    getConfig:function(){
      var _this = this;
      axios.post('getConfig', {}).then(function (res) {
        if (res.data.code == 0) {
          _this.config = res.data.data;
        }
      }).catch(function (error) {
        console.log("出现错误:",error);
      });
    },
    getInfo:function(id){
      var _this = this;
      _this.loading = true;
      axios.post('getCaInfo', {id:id}).then(function (res) {
        _this.loading = false;
        if (res.data.code == 0) {
          _this.data = res.data.data;
        }
      }).catch(function (error) {
        _this.loading = false;
        console.log("出现错误:",error);
      });
    },
    refresh: function () {
      this.$emit("refresh");
    },
    changeweekBegin(data){
      console.log(data);
    },
  }
}
</script>