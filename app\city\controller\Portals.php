<?php

namespace app\city\controller;

use app\BaseController;
use app\model\ExcelModel;
use app\model\AuthModel;
use hg\apidoc\annotation as Apidoc;
use app\model\ListModel;
use think\App;
use think\facade\Db;
use think\facade\View;

/**
 * @Apidoc\Title("统计大屏")
 * @Apidoc\Group("Portals")
 * @Apidoc\Sort(3)
 */
class Portals extends Base
{
    public function index($limit = 20,$excel=0){

        if (request()->isAjax()) {
            result();
        }else{
            //获取行业类别字典表
            $industry = Db::table("top_industry")->field('code,name')->where('pid',0)->select()->toArray();
            $res = Db::table("top_company_info")
                ->field("id,name as title,reg_address_info as address,jd as lng,wd as lat,'/general/toppingsoft/public/screen/images/danwei2.gif' as icon")
                ->where("jd",">",0)
                ->select();
            //涉轻工行业有限空间企业数
            $limitSpaceData = Db::table("top_company_info")
                ->field("count(*) as theCount,limited_space_type")
                ->where("length(limited_space_type)>0")
                ->group("limited_space_type")->select()->toArray();
            //粉尘涉爆
            $dustType = [
                '金属制品加工',
                '木制品加工',
                '农副产品加工',
                '纺织品加工',
                '橡胶塑料制品加工',
                '冶金/有色/建材煤粉制备',
                '烟草',
                '其他'
            ];
            //粉尘行业类型对应的企业
            $allDustIndestryTypeDatas = Db::table("top_company_info")
                ->field('count(*) as "value",sector as name')
                ->where("sector","in",$dustType)
                ->where("length(sector)>0")
                ->group("sector")->select()->toArray();
            //获取各粉尘行业类型对应的企业
            $allDustIndestryTypeData = [];
            foreach ($allDustIndestryTypeDatas as $k=>$v) {
                $allDustIndestryTypeData[] = [
                    'value'=>$v['value'],
                    'name'=>$v['name']
                ];
            }

            //获取坐标数据
            View::assign('pointData', json_encode($res));
            //获取行业类别字典表
            View::assign('industry', json_encode($industry));
            //有限空间名称
            View::assign('limitSpaceData_name', json_encode(array_column($limitSpaceData,'limited_space_type')));
            //各有限空间企业数量
            View::assign('limitSpaceData_count', json_encode(array_column($limitSpaceData,'theCount')));
            //粉尘行业类型
            View::assign('allDustIndestryType',json_encode($dustType));
            //粉尘行业类型数据
            View::assign('allDustIndestryTypeData',json_encode($allDustIndestryTypeData));
            //粉尘行业类型数据
            View::assign('dustTypeData_name',json_encode(['木粉尘', '金属粉尘', '塑料橡胶粉尘','农副产品粉尘','纸浆粉尘','其他']));
            //粉尘行业类型数据
            View::assign('dustTypeData_count',json_encode(['101', '41', '80','81','78','100']));
            return view();
        }
    }


    /**
     * 获取企业坐标
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function filterCompanies(){
        $year = input('year',date("Y"));
        $industry = input('industry',"");
        $industry = str_replace("行业","",$industry);
        $keyArea = input('keyArea',"1");

        $data = Db::table("top_company_info")
            ->field("id,name as title,reg_address_info as address,jd as lng,wd as lat,'/general/toppingsoft/public/screen/images/danwei2.gif' as icon")
            ->where("industry",$industry)
            ->where("jd",">",0)
            ->select();

        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => $data
        ]);
    }

    /**
     * 获取总体概览数据
     */
    public function getOverviewData()
    {
        $year = input('year',"");
        $where = "1=1";
        if($year !=""){
            $where = "year(date)='$year'";
        }
        $data = [];

        // 在库企业数
        $data['total_company_count'] = Db::table('top_company_info')->count();

        // 达标企业数(有证书的企业)
        $data['standard_company_count'] = Db::table('top_certificate')->count();

        // 达标有效企业数(有证书的企业且在有效期内)
        $data['valid_standard_company_count'] = Db::table('top_certificate')->where("ends",">=",date("Y-m-d"))->count();

        // 总体通过率（现场评审通过的和总数的比例）
        //通过的数据
        $data['pass_count'] = Db::table('top_grading_record')
            ->where("status",7)
            ->where($where)
            ->count();
        $data['total_pass_count'] = Db::table('top_grading_record')->count();
        $data['pass_rate']= 0;
        if($data['total_pass_count']!=0){
            $data['pass_rate'] = round($data['pass_count']/$data['total_pass_count']*100,2);
        }

        // 未通过数
        $data['not_pass_count'] = Db::table('top_grading_record')
            ->where('status', "5")
            ->where($where)
            ->count();

        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => $data
        ]);
    }

    /**
     * 获取分行业概览数据
     */
    /**
     * 获取分行业概览数据，并按照 getChart1Data 的格式返回
     */
    public function getIndustryData()
    {
        $year = input('year',"");
        $where = "1=1";
        if($year !=""){
            $where = "year(date)='$year'";
        }
        // 获取各行业数据
        $industryData = Db::table('top_company_info')
            ->field('industry as name, COUNT(*) as thevalue1')
            ->where("industry<>''")
            ->group('industry')
            ->order("thevalue1 desc")
            ->limit(5)
            ->select()->toArray();

        foreach ($industryData as $k=>$v){
            $industryData[$k]['thevalue2']= Db::table('top_grading_record')
                ->where("industry",$v['name'])
                ->count();
            $industryData[$k]['thevalue3']= Db::table('top_grading_record')
                ->where("industry",$v['name'])
                ->where('status',7)
                ->count();
            if($industryData[$k]['thevalue2']==0){
                $industryData[$k]['thevalue4'] = 0;
            }else{
                $industryData[$k]['thevalue4'] = round($industryData[$k]['thevalue3']/$industryData[$k]['thevalue2'],2);
            }
        }


        // 构造 legend 数据
        $legend = ['达标企业', '评审数', '通过数', '总体通过率'];

        // 提取 xAxis 数据（行业名称）
        $xAxis = array_column($industryData, 'name');

        // 构造 series 数据
        $series = [
            [
                'name' => '达标企业',
                'type' => 'bar',
                'barWidth' => 15,
                'itemStyle' => [
                    'normal' => [
                        'color' => [
                            'type' => 'linear',
                            'x' => 0, 'y' => 0, 'x2' => 0, 'y2' => 1,
                            'colorStops' => [
                                ['offset' => 0, 'color' => '#00FFE3'],
                                ['offset' => 1, 'color' => '#4693EC']
                            ]
                        ],
                        'barBorderRadius' => 12,
                    ]
                ],
                'data' => array_column($industryData, 'thevalue1')
            ],
            [
                'name' => '评审数',
                'type' => 'bar',
                'barWidth' => 15,
                'itemStyle' => [
                    'normal' => [
                        'color' => [
                            'type' => 'linear',
                            'x' => 0, 'y' => 0, 'x2' => 0, 'y2' => 1,
                            'colorStops' => [
                                ['offset' => 0, 'color' => '#FAD961'],
                                ['offset' => 1, 'color' => '#F76B1C']
                            ]
                        ],
                        'barBorderRadius' => 12,
                    ]
                ],
                'data' => array_column($industryData, 'thevalue2')
            ]
            ,
            [
                'name' => '通过数',
                'type' => 'bar',
                'barWidth' => 15,
                'itemStyle' => [
                    'normal' => [
                        'color' => [
                            'type' => 'linear',
                            'x' => 0, 'y' => 0, 'x2' => 0, 'y2' => 1,
                            'colorStops' => [
                                ['offset' => 0, 'color' => '#54FF9F'],
                                ['offset' => 1, 'color' => '#1E90FF']
                            ]
                        ],
                        'barBorderRadius' => 12,
                    ]
                ],
                'data' => array_column($industryData, 'thevalue3')
            ],
            [
                'name' => '总体通过率',
                'type' => 'line',
                'yAxisIndex' => 1,
                'smooth' => true,
                'itemStyle' => [
                    'normal' => [
                        'color' => '#FFD700'
                    ]
                ],
                'data' => array_column($industryData, 'thevalue4')
            ]
        ];

        // 返回统一格式的 JSON 数据
        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => [
                'legend' => $legend,
                'xAxis' => $xAxis,
                'series' => $series
            ]
        ]);
    }

    /**
     * 获取各区(市)县概览数据
     */
    public function getAreaData()
    {
        $year = input('year', date('Y'));
        $area = input('area', '510104');

        $query = Db::table('top_company_info');
        if($area) {
            $query = $query->where('reg_address','like', '%,'.$area.',%');
        }
        //@todo 计算真实的数据
        $areaData = $query->field('SUBSTR(region,9,6) as name, COUNT(*) as total_company_count,COUNT(*) as standard_company_count,COUNT(*) as review_task_count,100 as review_timeliness_rate')
            ->group('SUBSTR(region,9,6)')
            ->find();

        $areaOptions = Db::table('top_pca')
            ->field('name,code')
            ->where('level', 3)
            ->order('code')
            ->select()->toArray();

        //获取当前区域名称
        $curAreaName = Db::table('top_pca')
            ->field('name')
            ->where('code', $area)
            ->value('name');
        $areaData['area_name'] = $curAreaName;

        $data['area_data']=$areaData;
        $data['area_options']= $areaOptions;
        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => $data
        ]);
    }

    /**
     * 获取高分评审企业TOP三
     */
    public function getTopThreeCompanies()
    {
        $year = input('year', date('Y'));
        $industryArr = ["1"=>"工贸","2"=>"危化","3"=>"商业商务楼宇"];
        $industryInt = (int) input('industry', 1);
        $industry  = $industryArr[$industryInt] ?? "工贸";

        $topThree = Db::table('top_org_tasks')
            ->field('company_name, score, industry')
            ->where('industry', $industry)
            ->where('score', '>', 0)
            ->where("year(date)", $year)
            ->order('score', 'desc')
            ->limit(3)
            ->select()->toArray();
        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => $topThree
        ]);
    }

    /**
     * 获取重点领域监管数据
     */
    public function getKeyAreaData()
    {
        $year = input('year', date('Y'));

        // 有限空间数据
        $limitedSpaceData = Db::table('top_company_info')
            ->field('limited_space_type as name, COUNT(*) as value')
            ->where('has_limited_space', 1)
            ->group('limited_space_type')
            ->select()->toArray();

        // 粉尘涉爆数据
        $dustData = Db::table('top_company_info')
            ->field('dust_type as name, COUNT(*) as value')
            ->where('has_dust', 1)
            ->group('dust_type')
            ->select()->toArray();

        // 液氨制冷数据
        $ammoniaData = Db::table('top_company_info')
            ->field('ammonia_usage as name, COUNT(*) as value')
            ->where('has_ammonia', 1)
            ->group('ammonia_usage')
            ->select()->toArray();

        // 涉高温熔融金属数据
        $metalData = Db::table('top_company_info')
            ->field('furnace_type as name, COUNT(*) as value')
            ->where('has_metal', 1)
            ->group('furnace_type')
            ->select()->toArray();

        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => [
                'limitedSpace' => $limitedSpaceData,
                'dust' => $dustData,
                'ammonia' => $ammoniaData,
                'metal' => $metalData
            ]
        ]);
    }

    /**
     * 获取评审状态数据
     */
    public function getChart1Data()
    {
        $data = [
            'legend' => ["达标企业", "评审数", "通过数", "总体通过率"],
            'xAxis' => ['工贸', '危化', '商业商务楼宇'],
            'series' => [
                [
                    'name' => '达标企业',
                    'type' => 'bar',
                    'barWidth' => 15,
                    'itemStyle' => [
                        'normal' => [
                            'color' => [
                                'type' => 'linear',
                                'x' => 0, 'y' => 0, 'x2' => 0, 'y2' => 1,
                                'colorStops' => [
                                    ['offset' => 0, 'color' => '#00FFE3'],
                                    ['offset' => 1, 'color' => '#4693EC']
                                ]
                            ],
                            'barBorderRadius' => 12,
                        ]
                    ],
                    'data' => [120, 200, 150]
                ],
                [
                    'name' => '评审数',
                    'type' => 'bar',
                    'barWidth' => 15,
                    'itemStyle' => [
                        'normal' => [
                            'color' => [
                                'type' => 'linear',
                                'x' => 0, 'y' => 0, 'x2' => 0, 'y2' => 1,
                                'colorStops' => [
                                    ['offset' => 0, 'color' => '#FAD961'],
                                    ['offset' => 1, 'color' => '#F76B1C']
                                ]
                            ],
                            'barBorderRadius' => 12,
                        ]
                    ],
                    'data' => [100, 180, 130]
                ],
                [
                    'name' => '通过数',
                    'type' => 'bar',
                    'barWidth' => 15,
                    'itemStyle' => [
                        'normal' => [
                            'color' => [
                                'type' => 'linear',
                                'x' => 0, 'y' => 0, 'x2' => 0, 'y2' => 1,
                                'colorStops' => [
                                    ['offset' => 0, 'color' => '#54FF9F'],
                                    ['offset' => 1, 'color' => '#1E90FF']
                                ]
                            ],
                            'barBorderRadius' => 12,
                        ]
                    ],
                    'data' => [80, 160, 110]
                ],
                [
                    'name' => '总体通过率',
                    'type' => 'line',
                    'yAxisIndex' => 1,
                    'smooth' => true,
                    'itemStyle' => [
                        'normal' => [
                            'color' => '#FFD700'
                        ]
                    ],
                    'data' => [80, 85, 75]
                ]
            ]
        ];

        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => $data
        ]);
    }

    /**
     * 获取评审实时情况
     */
    public function getReviewStatus()
    {
        $year = input('year', date('Y'));
        $statusTags = [
            -1 => "已取消",
            0 => "待接收",
            1 => "已接收",
            2 => "已推送",
            3 => "待确认",
            5 => "已驳回",
            7 => "待评审",
            8 => "评审中",
            9 => "评审结束",
            10 => "任务结束"
        ];
        $orgTages = Db::table("top_org")->column("name","id");
        $pcaTages = Db::table("top_pca")->column("name","code");

        $reviewStatus = Db::table('top_org_tasks')->alias('t')
            ->leftJoin('top_company_info c','t.company_id = c.id')
            ->fieldRaw('t.id,t.company_name as name, t.date as thedate,t.status,t.org_id,substring(reg_address,9,6) as pca')
            ->order("t.date desc")
            ->limit(15)
            ->select()->each(function ($item) use($statusTags,$orgTages,$pcaTages){
                $experts = Db::table('top_org_tasks_experts')->where('tasks_id',$item['id'])->column('expert_name');
                $expertsString = implode('、', $experts);
                $item['experts'] = $expertsString;
                $item['status_name'] = isset($statusTags[$item['status']]) ? $statusTags[$item['status']] : '';
                $item['org_name'] = isset($orgTages[$item['org_id']]) ? $orgTages[$item['org_id']] : '';
                $item['pca_name'] = isset($pcaTages[$item['pca']]) ? $pcaTages[$item['pca']] : '';
                $item['thedate'] = date('Y-m-d',strtotime($item['thedate']));
                return $item;
            })->toArray();
            //数据不够20条自动补全
        while (count($reviewStatus) < 15) {
            $reviewStatus[count($reviewStatus)] = [
                'id' => '',
                'name' => '　',
                'thedate' => '',
                'status' => '',
                'org_id' => '',
                'experts' => '',
                'status_name' => '',
                'org_name' => '',
                'pca_name' => ''
            ];
        }



        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => $reviewStatus
        ]);
    }

    /**
     * 获取评审实时情况
     */
    public function getRealTimeReview()
    {
        $year = input('year', date('Y'));

        $reviewData = Db::table('top_company_info')
            ->alias('c')
            ->join('top_review_info r', 'c.id = r.company_id')
            ->field('c.name as company_name, c.area, r.review_org, r.review_date, r.expert_group, r.review_status')
            ->where('r.review_date', '>=', date('Y-m-d'))
            ->order('r.review_date', 'asc')
            ->select();

        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => $reviewData
        ]);
    }
}