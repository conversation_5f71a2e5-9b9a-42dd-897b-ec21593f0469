
ALTER TABLE "SYSDBA"."top_expert" ADD "check_status" TINYINT DEFAULT 1;
ALTER TABLE "SYSDBA"."top_expert" ADD "check_hash" VARCHAR2(128);
COMMENT ON COLUMN "SYSDBA"."top_expert"."check_status" IS '完整性验证状态';
COMMENT ON COLUMN "SYSDBA"."top_expert"."check_hash" IS '完整性验证码';


ALTER TABLE "SYSDBA"."top_org_user" ADD "check_status" TINYINT DEFAULT 1;
ALTER TABLE "SYSDBA"."top_org_user" ADD "check_hash" VARCHAR2(128);
COMMENT ON COLUMN "SYSDBA"."top_org_user"."check_status" IS '完整性验证状态';
COMMENT ON COLUMN "SYSDBA"."top_org_user"."check_hash" IS '完整性验证码';

ALTER TABLE "SYSDBA"."top_city_user" ADD "check_status" TINYINT DEFAULT 1;
ALTER TABLE "SYSDBA"."top_city_user" ADD "check_hash" VARCHAR2(128);
COMMENT ON COLUMN "SYSDBA"."top_city_user"."check_status" IS '完整性验证状态';
COMMENT ON COLUMN "SYSDBA"."top_city_user"."check_hash" IS '完整性验证码';

ALTER TABLE "SYSDBA"."top_area_user" ADD "check_status" TINYINT DEFAULT 1;
ALTER TABLE "SYSDBA"."top_area_user" ADD "check_hash" VARCHAR2(128);
COMMENT ON COLUMN "SYSDBA"."top_area_user"."check_status" IS '完整性验证状态';
COMMENT ON COLUMN "SYSDBA"."top_area_user"."check_hash" IS '完整性验证码';

ALTER TABLE "SYSDBA"."top_company_user" ADD "check_status" TINYINT DEFAULT 1;
ALTER TABLE "SYSDBA"."top_company_user" ADD "check_hash" VARCHAR2(128);
COMMENT ON COLUMN "SYSDBA"."top_company_user"."check_status" IS '完整性验证状态';
COMMENT ON COLUMN "SYSDBA"."top_company_user"."check_hash" IS '完整性验证码';