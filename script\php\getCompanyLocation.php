<?php
/**
 * 从API获取公司数据并存储到SQLite数据库
 * 
 * @param string $yearMonth 年月格式(YYYY-MM)，默认为当前年月
 * @return array|null 成功返回获取的数据数组，失败返回null
 */
function fetchCompanyData($yearMonth = null) {
    // 如果未提供年月，使用当前年月
    if ($yearMonth === null) {
        $current_date = new DateTime();
        $yearMonth = $current_date->format('Y-m');
    }
    
    // API配置
    $API_URL = 'http://10.1.235.89:2683/open-api/GjZdPkdxCoeyV9XsxAE9Fddglq7wF47S/WHPQYJBXX';
    $API_KEY = '878940';
    $API_SIGNATURE = '71d5e0df597371e14369d20686a0d8964c2b4061f8e1d3cf60936307feb7ee17';
    
    // 请求参数
    $params = [
        'SJ' => $yearMonth,
        'limit' => 1000, // 每次获取的记录数
        'start' => 0
    ];
    
    $headers = [
        'X-Ca-Key: ' . $API_KEY,
        'X-Ca-Signature: ' . $API_SIGNATURE
    ];
    
    $url = $API_URL . '?' . http_build_query($params);
    
    // 最大重试次数和重试间隔
    $MAX_RETRIES = 3;
    $RETRY_DELAY = 5; // 秒
    
    // 尝试获取数据
    for ($attempt = 0; $attempt < $MAX_RETRIES; $attempt++) {
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode == 200) {
                $data = json_decode($response, true);
                if (isset($data['data'])) {
                    return $data['data'];
                }
            }
            
            // 记录错误
            error_log("API请求失败，尝试 " . ($attempt + 1) . "，HTTP状态码: {$httpCode}");
        } catch (Exception $e) {
            error_log("API请求异常，尝试 " . ($attempt + 1) . ": " . $e->getMessage());
        }
        
        // 如果不是最后一次尝试，则等待后重试
        if ($attempt < $MAX_RETRIES - 1) {
            sleep($RETRY_DELAY);
        }
    }
    
    return null;
}