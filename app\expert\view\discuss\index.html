﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>在线聊天</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/expert.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .my-label { width: 200px;}
        .my-content { width: 450px;}
        .item { width: 30px;height:30px;line-height:30px;margin: 10px 0;border-radius:40px;background-color: #f0f0f0;text-align:center;cursor: pointer;}
        .item.checked { background-color: #1989FA;color:#fff;}
        .el-divider.el-divider--horizontal { margin:10px 0;}
        .list-item { padding:10px;border-buttom:1px sold #999;cursor: pointer;}
        .list-item.active { background-color:#f5f5f5;}
        .el-badge__content.is-fixed { top:50px; right:20px;}
        .content-list { display: flex;flex-direction: column-reverse;overflow:auto;}
        .content-list .system {color:#999;line-height:30px;margin-bottom: 10px;}
        .content-list .expert {line-height:30px;overflow:hidden;margin-bottom: 10px;}
        .content-list .expert >div {float:left;}
        .content-list .expert.right >div {float:right;}
        .content-list .expert .message { text-align:left;padding:0 10px;}
        .content-list .expert.right .message{ text-align:right;padding:0 10px;}
        .content-list .expert .message .username { font-size:12px;color:#999;}
        .content-list .expert .message .content { background-color:#fff; white-space: pre-wrap;}
        .el-descriptions__body {background:none;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <el-container :style="{height:height+'px'}">
        <el-aside width="250px">
            <ul class="list" v-infinite-scroll="getData" infinite-scroll-disabled="disabled">
                <li v-for="item in groupList" :class="id==item.id?'list-item active':'list-item'" @click="groupinfo(item)">
                        <el-row :gutter="10">
                            <el-col :span="18">
                                <p style="line-height:40px;font-size:14px;color:#333;font-weight:700;text-overflow: ellipsis;white-space: nowrap;overflow: hidden;">{{item.title}}</p>
                                <p style="line-height:30px;font-size:14px;color:#666;">评审日期：{{item.date}}</p>
                            </el-col>
                            <el-col :span="6">
                                <el-badge style="display:inline;" :value="item.readsum" :max="99">
                                    <p style="line-height:40px;font-size:14px;color:#666;text-align:right;">{{item.last_time}}</p>
                                </el-badge>
                            </el-col>
                        </el-row>
                </li>
            </ul>
            <!--<p v-if="loading">加载中...</p>
            <p v-if="noMore">没有更多了</p>-->
        </el-aside>
        <el-container>
            <el-header v-show="id>0">
                <el-row :gutter="10">
                    <el-col :span="24" style="text-align:left;">
                        <p style="line-height:30px;font-size:14px;color:#333;font-weight:700;text-overflow: ellipsis;white-space: nowrap;overflow: hidden;">{{data.title}}</p>
                        <p style="line-height:20px;font-size:14px;color:#666;font-weight:100;">评审日期：{{data.date}}</p>
                    </el-col>
                </el-row>
            </el-header>
            <el-main style="background-color:#fff;">
                <ul class="content-list" :style="{height:(height-250)+'px'}" v-infinite-scroll="getData" infinite-scroll-disabled="disabled">
                    <li v-for="item in data.content.data">
                        <div :class="item.user_type" v-if="item.user_type=='system'">{{item.content}}</div>
                        <div :class="item.user_type=='expert'&&item.user_id==user_id?'expert right':'expert'" v-if="item.user_type!='system'">
                            <div class="head"><el-image style="width: 40px; height: 40px;border-radio:40px;" src="/static/images/avatar/0.png" fit="cover"></el-image></div>
                            <div class="message" :style="{width:(width-620)+'px'}">
                                <div class="username">{{item.user_name}}({{item.time}})</div>
                                <div v-if="item.content" class="content">{{item.content}}</div>
                                <el-image v-if="item.image" style="width: 100px; height: 100px;background-color:#f5f5f5" :src="item.image" :preview-src-list="[item.image]" fit="fill"></el-image>
                            </div>
                        </div>
                        <!--<div :class="item.user_type=='company'&&item.user_id==user_id?item.user_type+' right':item.user_type" v-if="item.user_type=='company'">
                            <div class="head"><el-image style="width: 40px; height: 40px;border-radio:40px;" src="/static/images/avatar/0.png" fit="cover"></el-image></div>
                            <div class="message" :style="{width:(width-620)+'px'}">
                                <div class="username">{{item.user_name}}({{item.time}})</div>
                                <div v-if="item.content" class="content">{{item.content}}</div>
                                <el-image v-if="item.image" style="width: 100px; height: 100px;background-color:#f5f5f5" :src="item.image" :preview-src-list="[item.image]" fit="fill"></el-image>
                            </div>
                        </div>-->
                    </li>
                </ul>
            </el-main>
            <el-footer style="height:150px;border-top: 1px solid #f5f5f5" v-show="id>0">
                <div style="text-align:left;">
                    <el-upload
                            class="avatar-uploader"
                            action="upload"
                            accept=".jpg,.png"
                            :show-file-list="false"
                            :on-success="uploadSuccess"
                            :before-upload="uploadBefore">
                        <i style="margin-right:20px;" class="el-icon-picture"></i>
                    </el-upload>
                </div>
                <el-input
                        type="textarea"
                        :rows="3"
                        placeholder="请输入内容"
                        v-model="content">
                </el-input>
                <div style="text-align:right;"><el-button type="primary" size="mini" @click="send">发送</el-button></div>
            </el-footer>
        </el-container>
        <el-aside width="250px" style="background:#f5f5f5;border-left:1px solid #f5f5f5;padding:10px;">
            <el-descriptions v-if="id>0" label-style="width:120px;" content-style="width:300px;" title="群聊信息" :column="1">
                <el-descriptions-item label="企业名称">{{data.company_name}}</el-descriptions-item>
                <el-descriptions-item label="评审日期">{{data.date}}</el-descriptions-item>
                <el-descriptions-item label="地址">{{data.address}}</el-descriptions-item>
                <el-descriptions-item label="评审单位">{{data.org_name}}</el-descriptions-item>
                <el-descriptions-item label="专家组">{{data.experts}}</el-descriptions-item>
            </el-descriptions>
        </el-aside>
    </el-container>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                socket: null,
                title: '',
                active: '',
                id: '{$id}',
                user_id: '{$user.id}',
                content: '',
                WebSocket: "wss://{php}echo $_SERVER['HTTP_HOST'];{/php}/ws",
                WebSocketNum: 0,
                loading: false,
                data: {
                    content:{
                        data:[],
                    }
                },
                groupList: [],
                width: document.documentElement.clientWidth,
                height: document.documentElement.clientHeight-20,
            };
        },
        components: {
        },
        created() {
            this.socketSet();
        },
        computed: {
            noMore () {
                return true
            },
            disabled () {
                return this.loading || this.noMore
            }
        },
        methods: {
            socketSet(){
                var _this = this;
                _this.socket = new WebSocket(_this.WebSocket);
                _this.socket.onopen = () => {
                    axios.post('auth', {}).then(function (res) {
                        if (res.data.code == 0) {
                            _this.socket.send('type=auth&key='+res.data.data);
                        }
                    }).catch(function (error) {
                        console.log(error);
                    });
                    _this.WebSocketNum = 0;
                };
                _this.socket.onmessage = (res) => {
                    var data = JSON.parse(res.data);
                    if(data.type=='close'){
                        _this.$message.error('身份认证失败');
                    }else if(data.type=='content'){
                        var group = {
                            id:data.data.group_id,
                            date:data.data.date,
                            last_content:data.data.content,
                            last_time:data.data.time,
                            readsum:1,
                            status:1,
                            title:data.data.title,
                        };
                        if(data.data.group_id==_this.id){
                            _this.data.content.data.splice(0, 0, data.data);
                        }
                        for(i in _this.groupList){
                            if(_this.groupList[i].id==data.data.group_id){
                                group.readsum = data.data.group_id==_this.id?'':_this.groupList[i].readsum*1 + 1;
                                _this.groupList.splice(i,1);
                            }
                        }
                        _this.groupList.splice(0,0,group);
                    }
                };
                _this.socket.onclose = () => {
                    if(_this.WebSocketNum<10){
                        setTimeout(function() {
                            // 重新连接
                            _this.WebSocketNum += 1;
                            _this.socketSet();
                        }, 10000);
                    }
                };
                _this.socket.onerror = () => {
                    console.log("错误消息"); // ojIckSD2jqNzOqIrAGzL
                };
            },
            handleOpen(key, keyPath) {
                console.log(key, keyPath);
            },
            handleClose(key, keyPath) {
                console.log(key, keyPath);
            },
            uploadBefore(file) {
                const isJPG = file.type === 'image/jpeg';
                const isPNG = file.type === 'image/png';
                if(!isJPG&&!isPNG){
                    this.$message.error('请上传jpg/png图片');
                }
                return isJPG||isPNG;
            },
            uploadSuccess(res, file,fileList) {
                if(res.code==0){
                    this.send(res.data.url);
                }else{
                    this.$message({
                        message: res.msg,
                        type: "error"
                    });
                }
            },
            //数据加载
            getData() {
                var _this = this;
                _this.loading = true;
                var param = {};
                param.id = '{$id}';
                param._ajax = 1;
                axios.post('index', param).then(function (res) {
                    if (res.data.code == 0) {
                        _this.groupList = res.data.data.data;
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                    _this.loading = false;
                }).catch(function (error) {
                    console.log(error);
                });
            },
            //数据加载
            groupinfo(row) {
                var _this = this;
                _this.id = row.id;
                var param = {};
                param.id = row.id;
                var loading = this.$loading();
                axios.post('groupinfo', param).then(function (res) {
                    if (res.data.code == 0) {
                        row.readsum = '';
                        _this.data = res.data.data;
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                    loading.close();
                }).catch(function (error) {
                    console.log(error);
                });
            },
            //数据加载
            send(image) {
                var _this = this;
                if(!_this.content && typeof image !='string') {
                    _this.$message.error('请发送消息内容或图片');
                    return false;
                }
                var param = {
                    id:_this.id,
                    content:_this.content,
                    image:image,
                };
                axios.post('send', param).then(function (res) {
                    if (res.data.code == 0) {
                        _this.content = '';
                        var group = {
                            id:res.data.data.group_id,
                            date:res.data.data.date,
                            last_content:res.data.data.content,
                            last_time:res.data.data.time,
                            readsum:1,
                            status:1,
                            title:res.data.data.title,
                        };
                        if(res.data.data.group_id==_this.id){
                            _this.data.content.data.splice(0, 0, res.data.data);
                        }
                        for(i in _this.groupList){
                            if(_this.groupList[i].id==res.data.data.group_id){
                                group.readsum = res.data.data.group_id==_this.id?'':_this.groupList[i].readsum + 1;
                                _this.groupList.splice(i,1);
                            }
                        }
                        _this.groupList.splice(0,0,group);
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            change(key) {
                this.index = key+1;
                this.reviewdata = this.review[key];
            },
            // 使用splice方法在索引2的位置插入元素3
            // list.splice(index, 0, elementToInsert);
        },
        mounted() {
            this.getData();
            if(this.id>0){
                var row = {};
                row.id = this.id;
                this.groupinfo(row);
            }
            // 添加键盘事件监听
            document.addEventListener('keydown', (e) => {
                if(e.altKey && e.key === 'Enter') {
                    this.send();
                }
            });
            // console.log(this.textContent('2024-05-02'));
        }
    })
</script>


</body>
</html>