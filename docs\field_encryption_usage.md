# 数据库字段加密功能使用说明

## 功能概述

数据库字段加密功能允许管理员对指定数据表的指定字段进行批量加密或解密操作。该功能调用 `common.php` 中的 `hsmEncrypt` 和 `hsmDecrypt` 方法来处理数据。

## 访问路径

- 控制器路径：`app/admin/controller/FieldEncryption.php`
- 视图路径：`app/admin/view/field_encryption/index.html`
- 访问URL：`/admin/field_encryption/index`

## 功能特性

### 1. 四步操作流程

1. **选择数据表**：从数据库中选择需要操作的表
2. **选择字段**：选择需要加密/解密的字段，并设置操作类型和WHERE条件
3. **预览操作**：预览操作结果，确认无误后再执行
4. **执行操作**：执行实际的加密/解密操作

### 2. 安全特性

- **事务保护**：所有数据库操作都在事务中执行，确保数据一致性
- **预览功能**：在实际执行前可以预览操作结果
- **错误处理**：详细的错误信息和异常处理
- **批量处理**：支持批量处理多个字段和多条记录

### 3. 灵活配置

- **WHERE条件**：支持自定义WHERE条件来限制操作范围
- **字段选择**：可以选择多个字段同时进行操作
- **操作类型**：支持加密和解密两种操作

## API接口说明

### 1. 获取数据库表列表
- **URL**: `/admin/field_encryption/getTables`
- **方法**: GET
- **返回**: 数据库中所有表的列表

### 2. 获取表字段列表
- **URL**: `/admin/field_encryption/getTableFields`
- **方法**: GET
- **参数**: `table_name` - 表名
- **返回**: 指定表的所有字段信息

### 3. 预览操作结果
- **URL**: `/admin/field_encryption/previewOperation`
- **方法**: POST
- **参数**:
  - `table_name` - 表名
  - `fields` - 字段数组
  - `operation` - 操作类型（encrypt/decrypt）
  - `where_condition` - WHERE条件（可选）
  - `limit` - 预览记录数（可选，默认10）

### 4. 执行加密操作
- **URL**: `/admin/field_encryption/encryptFields`
- **方法**: POST
- **参数**:
  - `table_name` - 表名
  - `fields` - 字段数组
  - `where_condition` - WHERE条件（可选）

### 5. 执行解密操作
- **URL**: `/admin/field_encryption/decryptFields`
- **方法**: POST
- **参数**:
  - `table_name` - 表名
  - `fields` - 字段数组
  - `where_condition` - WHERE条件（可选）

## 使用步骤

### 步骤1：选择数据表
1. 访问字段加密管理页面
2. 在下拉列表中选择需要操作的数据表
3. 点击"下一步"

### 步骤2：配置操作参数
1. 选择操作类型（加密或解密）
2. 勾选需要操作的字段（可多选）
3. 可选：输入WHERE条件来限制操作范围
   - 例如：`status = 1 AND created_time > '2023-01-01'`
4. 点击"下一步"

### 步骤3：预览操作结果
1. 设置预览记录数（1-50条）
2. 点击"加载预览"查看操作前后的数据对比
3. 确认无误后点击"确认执行"

### 步骤4：执行操作
1. 再次确认操作信息
2. 点击"确认执行加密/解密操作"
3. 查看执行结果统计

## 注意事项

### 1. 数据安全
- 执行操作前请务必备份数据库
- 建议先在测试环境中验证功能
- 重要数据操作前请进行预览确认

### 2. 性能考虑
- 大量数据操作可能需要较长时间
- 建议分批处理大数据量的表
- 可以使用WHERE条件来限制操作范围

### 3. 加密判断
- 系统使用启发式方法判断数据是否已加密
- 判断依据：字符串长度、特殊字符等
- 如有特殊需求，可修改 `isEncrypted()` 方法

### 4. 错误处理
- 操作失败时会显示详细错误信息
- 部分记录失败不会影响其他记录的处理
- 所有操作都有完整的日志记录

## 技术实现

### 1. 加密方法
使用 `common.php` 中的 `hsmEncrypt()` 函数：
```php
function hsmEncrypt($data) {
    $result = shell_exec("java -jar hsm-client.jar enc \"$data\" 2>nul");
    return trim($result);
}
```

### 2. 解密方法
使用 `common.php` 中的 `hsmDecrypt()` 函数：
```php
function hsmDecrypt($encryptedData) {
    $result = shell_exec("java -jar hsm-client.jar dec \"$encryptedData\" 2>nul");
    return trim($result);
}
```

### 3. 数据库兼容性
系统支持多种数据库类型：

#### 支持的数据库
- **MySQL**：使用 `SHOW TABLES` 和 `SHOW FULL COLUMNS` 语句
- **达梦数据库**：使用 `USER_TABLES` 和 `USER_TAB_COLUMNS` 系统视图
- **PostgreSQL**：使用 `information_schema` 和系统函数
- **SQLite**：使用 `sqlite_master` 和 `PRAGMA` 语句
- **SQL Server**：使用 `INFORMATION_SCHEMA` 视图
- **通用支持**：基于 `INFORMATION_SCHEMA` 标准

#### 达梦数据库特殊处理
```php
// 获取表列表
SELECT TABLE_NAME FROM USER_TABLES ORDER BY TABLE_NAME

// 获取字段信息
SELECT
    COLUMN_NAME,
    DATA_TYPE,
    DATA_LENGTH,
    DATA_PRECISION,
    DATA_SCALE,
    NULLABLE,
    DATA_DEFAULT,
    COMMENTS
FROM USER_TAB_COLUMNS
WHERE TABLE_NAME = ?
ORDER BY COLUMN_ID
```

### 4. 数据库操作
- 使用ThinkPHP6的数据库操作类
- 支持事务处理
- 批量更新优化
- 跨数据库兼容性处理

## 扩展说明

如需扩展功能，可以考虑以下方向：

1. **定时任务**：支持定时批量加密
2. **日志记录**：详细的操作日志
3. **权限控制**：基于角色的操作权限
4. **数据导出**：加密后数据的导出功能
5. **配置管理**：加密字段的配置管理

## 故障排除

### 常见问题

1. **HSM服务不可用**
   - 检查 `hsm-client.jar` 文件是否存在
   - 确认Java环境是否正确配置

2. **数据库连接失败**
   - 检查数据库配置
   - 确认数据库服务是否正常

3. **权限不足**
   - 确认用户是否有相应的数据库操作权限
   - 检查文件系统权限

4. **内存不足**
   - 大数据量操作时可能出现内存不足
   - 建议分批处理或增加服务器内存
