<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HSM 缓存加密解密功能演示</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .demo-container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .demo-section { margin-bottom: 30px; padding: 20px; background: white; border-radius: 8px; box-shadow: 0 2px 12px rgba(0,0,0,0.1); }
        .performance-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .cache-stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 20px; }
        .stat-item { background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; text-align: center; }
        .stat-value { font-size: 24px; font-weight: bold; margin-bottom: 5px; }
        .stat-label { font-size: 14px; opacity: 0.9; }
        .test-result { margin-top: 15px; padding: 10px; background: #f9f9f9; border-radius: 4px; font-family: monospace; }
        .performance-comparison { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 20px; }
        .comparison-item { padding: 15px; border-radius: 8px; text-align: center; }
        .fast { background: #e8f5e8; border: 2px solid #67c23a; }
        .slow { background: #fef0f0; border: 2px solid #f56c6c; }
    </style>
</head>
<body>
    <div id="app" class="demo-container">
        <h1>HSM 缓存加密解密功能演示</h1>
        
        <!-- 功能介绍 -->
        <div class="demo-section">
            <h3>🚀 功能特性</h3>
            <el-row :gutter="20">
                <el-col :span="8">
                    <el-card>
                        <div slot="header">智能缓存</div>
                        <p>自动缓存加密解密结果，避免重复计算，大幅提升性能</p>
                    </el-card>
                </el-col>
                <el-col :span="8">
                    <el-card>
                        <div slot="header">安全存储</div>
                        <p>使用SHA256哈希生成缓存键，确保缓存数据的安全性</p>
                    </el-card>
                </el-col>
                <el-col :span="8">
                    <el-card>
                        <div slot="header">容错机制</div>
                        <p>Redis不可用时自动降级到直接加密解密，保证功能可用</p>
                    </el-card>
                </el-col>
            </el-row>
        </div>

        <!-- 缓存统计 -->
        <div class="demo-section performance-card">
            <h3 style="margin-top: 0;">📊 缓存统计信息</h3>
            <el-button @click="loadCacheStats" type="primary" size="small" style="margin-bottom: 15px;">
                <i class="el-icon-refresh"></i> 刷新统计
            </el-button>
            
            <div class="cache-stats">
                <div class="stat-item">
                    <div class="stat-value">{{ cacheStats.encrypt_cache_count }}</div>
                    <div class="stat-label">加密缓存数量</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{{ cacheStats.decrypt_cache_count }}</div>
                    <div class="stat-label">解密缓存数量</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{{ cacheStats.total_cache_count }}</div>
                    <div class="stat-label">总缓存数量</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{{ cacheStats.estimated_memory_mb }} MB</div>
                    <div class="stat-label">估算内存使用</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">
                        <i :class="cacheStats.redis_connected ? 'el-icon-success' : 'el-icon-error'" 
                           :style="{color: cacheStats.redis_connected ? '#67c23a' : '#f56c6c'}"></i>
                    </div>
                    <div class="stat-label">Redis 连接状态</div>
                </div>
            </div>
        </div>

        <!-- 加密测试 -->
        <div class="demo-section">
            <h3>🔐 加密功能测试</h3>
            <el-form :model="encryptForm" label-width="120px">
                <el-form-item label="原始文本">
                    <el-input 
                        v-model="encryptForm.plaintext" 
                        placeholder="请输入要加密的文本"
                        type="textarea"
                        :rows="3">
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <el-button @click="testEncrypt" type="primary" :loading="encryptLoading">
                        <i class="el-icon-lock"></i> 执行加密
                    </el-button>
                    <el-button @click="testEncryptPerformance" type="success" :loading="performanceLoading">
                        <i class="el-icon-timer"></i> 性能测试
                    </el-button>
                </el-form-item>
            </el-form>
            
            <div v-if="encryptResult" class="test-result">
                <strong>加密结果：</strong><br>
                {{ encryptResult }}
            </div>
            
            <div v-if="performanceResult" class="performance-comparison">
                <div class="comparison-item fast">
                    <h4>缓存加密</h4>
                    <div style="font-size: 20px; margin: 10px 0;">{{ performanceResult.cached_time }}ms</div>
                    <div>{{ performanceResult.cache_hit ? '缓存命中' : '首次加密' }}</div>
                </div>
                <div class="comparison-item slow">
                    <h4>直接加密</h4>
                    <div style="font-size: 20px; margin: 10px 0;">{{ performanceResult.direct_time }}ms</div>
                    <div>每次都重新计算</div>
                </div>
            </div>
        </div>

        <!-- 解密测试 -->
        <div class="demo-section">
            <h3>🔓 解密功能测试</h3>
            <el-form :model="decryptForm" label-width="120px">
                <el-form-item label="加密文本">
                    <el-input 
                        v-model="decryptForm.ciphertext" 
                        placeholder="请输入要解密的文本"
                        type="textarea"
                        :rows="3">
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <el-button @click="testDecrypt" type="primary" :loading="decryptLoading">
                        <i class="el-icon-unlock"></i> 执行解密
                    </el-button>
                    <el-button @click="useEncryptResult" type="info" :disabled="!encryptResult">
                        <i class="el-icon-document-copy"></i> 使用加密结果
                    </el-button>
                </el-form-item>
            </el-form>
            
            <div v-if="decryptResult" class="test-result">
                <strong>解密结果：</strong><br>
                {{ decryptResult }}
            </div>
        </div>

        <!-- 缓存管理 -->
        <div class="demo-section">
            <h3>🗂️ 缓存管理</h3>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-button @click="clearEncryptCache" type="warning" :loading="clearLoading">
                        <i class="el-icon-delete"></i> 清除加密缓存
                    </el-button>
                </el-col>
                <el-col :span="12">
                    <el-button @click="clearDecryptCache" type="warning" :loading="clearLoading">
                        <i class="el-icon-delete"></i> 清除解密缓存
                    </el-button>
                </el-col>
            </el-row>
            <el-button @click="clearAllCache" type="danger" style="margin-top: 10px;" :loading="clearLoading">
                <i class="el-icon-delete"></i> 清除所有HSM缓存
            </el-button>
        </div>

        <!-- 批量测试 -->
        <div class="demo-section">
            <h3>⚡ 批量性能测试</h3>
            <p>测试大量数据的加密解密性能，对比缓存和直接操作的效率差异</p>
            <el-form inline>
                <el-form-item label="测试数量">
                    <el-input-number v-model="batchTestCount" :min="10" :max="1000" :step="10"></el-input-number>
                </el-form-item>
                <el-form-item>
                    <el-button @click="runBatchTest" type="primary" :loading="batchTestLoading">
                        <i class="el-icon-cpu"></i> 开始批量测试
                    </el-button>
                </el-form-item>
            </el-form>
            
            <div v-if="batchTestResult" style="margin-top: 20px;">
                <el-alert 
                    :title="`批量测试完成：${batchTestCount} 条数据`"
                    type="success"
                    :closable="false">
                </el-alert>
                <div style="margin-top: 15px; display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <el-card>
                        <div slot="header">缓存模式</div>
                        <p><strong>总耗时：</strong>{{ batchTestResult.cached_total_time }}ms</p>
                        <p><strong>平均耗时：</strong>{{ batchTestResult.cached_avg_time }}ms</p>
                        <p><strong>缓存命中率：</strong>{{ batchTestResult.cache_hit_rate }}%</p>
                    </el-card>
                    <el-card>
                        <div slot="header">直接模式</div>
                        <p><strong>总耗时：</strong>{{ batchTestResult.direct_total_time }}ms</p>
                        <p><strong>平均耗时：</strong>{{ batchTestResult.direct_avg_time }}ms</p>
                        <p><strong>性能提升：</strong>{{ batchTestResult.performance_improvement }}x</p>
                    </el-card>
                </div>
            </div>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    // 缓存统计
                    cacheStats: {
                        encrypt_cache_count: 0,
                        decrypt_cache_count: 0,
                        total_cache_count: 0,
                        estimated_memory_mb: 0,
                        redis_connected: false
                    },
                    
                    // 加密测试
                    encryptForm: {
                        plaintext: '这是一个测试文本，用于演示HSM缓存加密功能。'
                    },
                    encryptResult: '',
                    encryptLoading: false,
                    
                    // 解密测试
                    decryptForm: {
                        ciphertext: ''
                    },
                    decryptResult: '',
                    decryptLoading: false,
                    
                    // 性能测试
                    performanceResult: null,
                    performanceLoading: false,
                    
                    // 缓存管理
                    clearLoading: false,
                    
                    // 批量测试
                    batchTestCount: 50,
                    batchTestResult: null,
                    batchTestLoading: false
                }
            },
            mounted() {
                this.loadCacheStats();
            },
            methods: {
                // 加载缓存统计
                loadCacheStats() {
                    // 模拟API调用
                    setTimeout(() => {
                        this.cacheStats = {
                            encrypt_cache_count: Math.floor(Math.random() * 1000) + 100,
                            decrypt_cache_count: Math.floor(Math.random() * 800) + 80,
                            total_cache_count: 0,
                            estimated_memory_mb: (Math.random() * 10 + 1).toFixed(2),
                            redis_connected: true
                        };
                        this.cacheStats.total_cache_count = this.cacheStats.encrypt_cache_count + this.cacheStats.decrypt_cache_count;
                    }, 500);
                },
                
                // 测试加密
                testEncrypt() {
                    if (!this.encryptForm.plaintext.trim()) {
                        this.$message.warning('请输入要加密的文本');
                        return;
                    }
                    
                    this.encryptLoading = true;
                    // 模拟加密API调用
                    setTimeout(() => {
                        this.encryptLoading = false;
                        this.encryptResult = 'HSM_ENCRYPTED_' + btoa(this.encryptForm.plaintext) + '_' + Date.now();
                        this.$message.success('加密完成');
                    }, 1000);
                },
                
                // 测试解密
                testDecrypt() {
                    if (!this.decryptForm.ciphertext.trim()) {
                        this.$message.warning('请输入要解密的文本');
                        return;
                    }
                    
                    this.decryptLoading = true;
                    // 模拟解密API调用
                    setTimeout(() => {
                        this.decryptLoading = false;
                        if (this.decryptForm.ciphertext.includes('HSM_ENCRYPTED_')) {
                            const base64Part = this.decryptForm.ciphertext.split('_')[2];
                            try {
                                this.decryptResult = atob(base64Part);
                                this.$message.success('解密完成');
                            } catch (e) {
                                this.decryptResult = '解密失败：无效的加密格式';
                                this.$message.error('解密失败');
                            }
                        } else {
                            this.decryptResult = '解密失败：不是有效的HSM加密数据';
                            this.$message.error('解密失败');
                        }
                    }, 800);
                },
                
                // 使用加密结果
                useEncryptResult() {
                    this.decryptForm.ciphertext = this.encryptResult;
                },
                
                // 性能测试
                testEncryptPerformance() {
                    if (!this.encryptForm.plaintext.trim()) {
                        this.$message.warning('请输入要加密的文本');
                        return;
                    }
                    
                    this.performanceLoading = true;
                    
                    // 模拟性能测试
                    setTimeout(() => {
                        const cachedTime = Math.floor(Math.random() * 50) + 10; // 10-60ms
                        const directTime = Math.floor(Math.random() * 500) + 200; // 200-700ms
                        const cacheHit = Math.random() > 0.3; // 70% 缓存命中率
                        
                        this.performanceResult = {
                            cached_time: cacheHit ? cachedTime : directTime,
                            direct_time: directTime,
                            cache_hit: cacheHit
                        };
                        
                        this.performanceLoading = false;
                        this.$message.success('性能测试完成');
                    }, 2000);
                },
                
                // 清除缓存
                clearEncryptCache() {
                    this.clearCache('encrypt');
                },
                
                clearDecryptCache() {
                    this.clearCache('decrypt');
                },
                
                clearAllCache() {
                    this.clearCache('all');
                },
                
                clearCache(type) {
                    this.clearLoading = true;
                    setTimeout(() => {
                        this.clearLoading = false;
                        this.$message.success(`${type === 'all' ? '所有' : type === 'encrypt' ? '加密' : '解密'}缓存清除成功`);
                        this.loadCacheStats();
                    }, 1000);
                },
                
                // 批量测试
                runBatchTest() {
                    this.batchTestLoading = true;
                    
                    setTimeout(() => {
                        const cachedTotalTime = this.batchTestCount * (Math.random() * 20 + 5); // 5-25ms per item
                        const directTotalTime = this.batchTestCount * (Math.random() * 300 + 200); // 200-500ms per item
                        const cacheHitRate = Math.floor(Math.random() * 30 + 60); // 60-90%
                        
                        this.batchTestResult = {
                            cached_total_time: Math.round(cachedTotalTime),
                            cached_avg_time: Math.round(cachedTotalTime / this.batchTestCount),
                            direct_total_time: Math.round(directTotalTime),
                            direct_avg_time: Math.round(directTotalTime / this.batchTestCount),
                            cache_hit_rate: cacheHitRate,
                            performance_improvement: (directTotalTime / cachedTotalTime).toFixed(1)
                        };
                        
                        this.batchTestLoading = false;
                        this.$message.success('批量测试完成');
                    }, 3000);
                }
            }
        });
    </script>
</body>
</html>
