<?php

namespace app\admin\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use app\admin\model\SettingModel;
use app\BaseController;
use app\model\FileModel;
use hg\apidoc\annotation as Apidoc;
use think\facade\View;
use think\Request;
use think\facade\Db;
use app\model\ListModel;
use app\model\ExcelModel;
use think\facade\Cache;

/**
 * @Apidoc\Title("基础信息设置")
 * @Apidoc\Group("Index")
 * @Apidoc\Sort(1)
 */
class Setting extends BaseController {


    public function datas() {
        $res = Db::table('cay_accessment_element')->where(['main_id'=>'11'])->select()->toArray();
        foreach ($res as $v){
            $v['main_id'] = 1;
            unset($v['numrow']);
//            Db::table('top_standard_element')->insertGetId($v);
        }
        $res = Db::table('cay_accessment_content')->where(['main_id'=>'11'])->select()->toArray();
        foreach ($res as $v){
            $v['main_id'] = 1;
            unset($v['numrow']);
            unset($v['self']);
            unset($v['weight']);
            print_r($v);
            Db::table('top_standard_content')->insertGetId($v);

        }
    }
    /**
     * @Apidoc\Title("行政区划设置")
     * @Apidoc\Desc("行政区划设置")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function pca($excel=0) {
        $setting = config('setting_list.pca');
        if (request()->isAjax()||$excel==1) {
            $res = Db::table($setting['table'])->where([])->order('code')->select()->toArray();
            if($excel==1){
                foreach ($setting['fields'] as $k=>$v){
                    $title[] = ['title'=>$v,'field'=>$k,'type'=>'string'];
                }
                ExcelModel::exportExcel($title, $res, $setting['title'].'导出',true);
            }
            $res = get_tree_children($res);
            result($res);
        }else{
            View::assign('title',$setting['title']);
            View::assign('fields',$setting['fields']);
            return view();
        }
    }

    /**
     * @param $id
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @sysLog "修改行政区划信息"
     */
    public function pcaSave($id=0){
        $setting = config('setting_list.pca');
        $request = $this->request->post();
        foreach ($setting['fields'] as $k=>$v){
            if(empty($request[$k])&&in_array($k,['code','category'])){
                result('',1001,'请填写'.$v);
            }
        }
        $data = [
            'code' => $request['code'],
            'name' => $request['name'],
            'enabled' => $request['enabled'],
        ];
        $re = Db::table($setting['table'])->where([['code','=',$data['code']],['id','<>',$id]])->find();
        if($re){
            result('',1003,'类型重复');
        }
        if($id){
            $re = Db::table($setting['table'])->where(['id'=>$id])->find();
            if(empty($re)){
                result('',1003,'数据不存在或已删除');
            }
            $re = Db::table($setting['table'])->where(['id'=>$id])->update($data);
        }else{
            $data['pid'] = empty($request['pid'])?0:$request['pid'];
            if(!empty($data['pid'])){
                $par = Db::table($setting['table'])->where(['id'=>$data['pid']])->find();
                $data['pid'] = empty($par)?0:$par['id'];
                $data['level'] = empty($par)?1:$par['level']+1;
            }else{
                $data['pid'] = 0;
                $data['level'] = 1;
            }
            $id = Db::table($setting['table'])->insertGetId($data);
        }
        Cache::tag('pca')->clear();
        result(['id'=>$id]);
    }

    /**
     * @param $id
     * @return void
     * @sysLog 删除省市区
     */
    public function pcaDel($id=0){
        $setting = config('setting_list.pca');
        $re = SettingModel::del($setting['table'],$id);
        if($re===false){
            result('',1002,'信息有误');
        }
        Cache::tag('pca')->clear();
        result('',0,'删除成功');
    }

    /**
     * @param $id
     * @param $enabled
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @sysLog 更新区县信息
     */
    public function pcaEnabled($id=0,$enabled=0){
        $setting = config('setting_list.pca');
        $re = Db::table($setting['table'])->where(['id'=>$id])->find();
        if(empty($re)){
            result('',1002,'信息不存在');
        }
        $up = Db::table($setting['table'])->where(['id'=>$id])->update(['enabled'=>$enabled]);
        Cache::tag('pca')->clear();
        result('',0,'成功');
    }

    //导入模板
    public function pcaImportTemplate()
    {
        $setting = config('setting_list.pca');
        foreach ($setting['fields'] as $k=>$v){
            $title[] = ['title'=>$v,'field'=>$k,'width'=>'20','type'=>'string'];
        }
        ExcelModel::exportExcel($title, [], $setting['title'].'导入模板');
    }

    //数据导入
    public function pcaImport()
    {
        $setting = config('setting_list.pca');
        $data = import("input");
        $dataHeader = importHeader("input");
        foreach ($dataHeader as $k=>$v){
            foreach ($setting['fields'] as $k1=>$v1){
                if($v1==$v){
                    $tit[$k] = $k1;
                }
            }
        }
        foreach ($data as $k=>$v){
            $tmp = [];
            foreach($v as $k1=>$v1){
                if(in_array($tit[$k1],[])){
                    $v1 = str_replace('.','-',$v1);
                    if(date('Y',strtotime($v1))>=1900){
                        $tmp[$tit[$k1]] = date('Y-m-d',strtotime($v1));
                    }else{
                        $tmp[$tit[$k1]] = gmdate('Y-m-d',\PHPExcel_Shared_Date::ExcelToPHP($v1));
                    }
                }else{
                    $tmp[$tit[$k1]] = str_replace(' ','',$v1);
                }
            }
            $dataList[] = $tmp;
        }
        foreach ($dataList as $v) {
            if(!empty($v['code'])&&!empty($v['name'])){
                $datauser[] = $v;
            }
        }
        foreach ($datauser as $k=>$v){
            $re = self::pcaImportSave($setting,$v);
            if($re['code']==0) {
                $result['success'][] = ['row' => $k + 2, 'user_name' => $v['user_name'], 'msg' => '保存成功'];
            }else if($re['code']==-1){
                $result['repeat'][] = ['row' => $k + 2, 'user_name' => $v['user_name'], 'msg' => '单据重复'];
            }else{
                $result['fail'][] = ['row'=>$k+2,'user_name'=>$v['user_name'],'msg'=>$re['msg']];
            }
        }
        Cache::tag('pca')->clear();
        $msg = count($result['success']).'条数据保存成功，'.count($result['repeat']).'条数据重复导入，'.count($result['fail']).'条数据处理失败。';
        result($result,0,$msg);
    }

    /**
     * @param $setting
     * @param $param
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function pcaImportSave($setting,$param){
        foreach ($setting['fields'] as $k=>$v){
            $data[$k] = $param[$k];
        }
        $length = strlen($param['code']);
        $code = '';
        for($i=0;$i<9;$i++){
            $code .= $length<=$i?'0':substr($param['code'], $i, 1);
        }
        $data['code'] = $code;
        $re = Db::table($setting['table'])->where($data)->field('id')->find();
        if($re){
            return ['','code'=>-1,'msg'=>'重复'];
        }else{
            $code1 = substr($code, 0, 2);
            $code2 = substr($code, 2, 2);
            $code3 = substr($code, 4, 2);
            $code4 = substr($code, 6, 3);
            if($code4>0){
                $p_code = $code1.$code2.$code3.'000';
            }else if($code3>0){
                $p_code = $code1.$code2.'00000';
            }else if($code2>0){
                $p_code = $code1.'0000000';
            }else{
                $p_code = '000000000';
            }
            $p_model = Db::table($setting['table'])->where('code', $p_code)->order('id desc')->find();
            $data['pid'] = empty($p_model)?0:$p_model['id'];
            $data['level'] = empty($p_model)?1:$p_model['level']+1;
            $id = Db::table($setting['table'])->insertGetId($data);
        }
        return ['data'=>['id'=>$id],'code'=>0,'msg'=>'保存成功'];
    }


    /**************************产业园区*******************Start*******************/
    /**
     * @Apidoc\Title("产业园区设置")
     * @Apidoc\Desc("产业园区设置")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function industrialPark($excel=0) {
        $setting = config('setting_list.industrialPark');
        if (request()->isAjax()||$excel==1) {
            $res = Db::table($setting['table'])->where([])->order('code')->select()->toArray();

            if($excel==1){
                foreach ($setting['fields'] as $k=>$v){
                    $title[] = ['title'=>$v,'field'=>$k];
                }
                ExcelModel::exportExcel($title, $res, $setting['title'].'导出',true);
            }
            $res = get_tree_children($res);
            result($res);
        }else{
            View::assign('title',$setting['title']);
            View::assign('fields',$setting['fields']);
            return view();
        }
    }

    /**
     * @param $id
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @sysLog 产业园区设置保存
     */
    public function industrialParkSave($id=0){
        $setting = config('setting_list.industrialPark');
        $request = $this->request->post();
        foreach ($setting['fields'] as $k=>$v){
            if(empty($request[$k])&&in_array($k,['code','category'])){
                result('',1001,'请填写'.$v);
            }
        }
        $data = [
            'code' => $request['code'],
            'name' => $request['name'],
            'enabled' => $request['enabled'],
        ];
        $re = Db::table($setting['table'])->where([['code','=',$data['code']],['id','<>',$id]])->find();
        if($re){
            result('',1003,'类型重复');
        }
        if($id){
            $re = Db::table($setting['table'])->where(['id'=>$id])->find();
            if(empty($re)){
                result('',1003,'数据不存在或已删除');
            }
            $re = Db::table($setting['table'])->where(['id'=>$id])->update($data);
        }else{
            $data['pid'] = $request['pid'];
            if(!empty($data['pid'])){
                $par = Db::table($setting['table'])->where(['id'=>$data['pid']])->find();
                $data['pid'] = empty($par)?0:$par['id'];
                $data['level'] = empty($par)?1:$par['level']+1;
            }else{
                $data['pid'] = 0;
                $data['level'] = 1;
            }
            $id = Db::table($setting['table'])->insertGetId($data);
        }
        Cache::tag('industrialPark')->clear();
        result(['id'=>$id]);
    }

    /**
     * @param $id
     * @return void
     * @sysLog 删除产业园区
     */
    public function industrialParkDel($id=0){
        $setting = config('setting_list.industrialPark');
        $re = SettingModel::del($setting['table'],$id);
        if($re===false){
            result('',1002,'信息有误');
        }
        Cache::tag('industrialPark')->clear();
        result('',0,'删除成功');
    }

    /**
     * @param $id
     * @param $enabled
     * @return void
     * @sysLog 启用/禁用产业园区
     */
    public function industrialParkEnabled($id=0,$enabled=0){
        $setting = config('setting_list.industrialPark');
        $re = Db::table($setting['table'])->where(['id'=>$id])->find();
        if(empty($re)){
            result('',1002,'信息不存在');
        }
        $up = Db::table($setting['table'])->where(['id'=>$id])->update(['enabled'=>$enabled]);
        Cache::tag('industrialPark')->clear();
        result('',0,'成功');
    }

    //导入模板
    public function industrialParkImportTemplate()
    {
        $setting = config('setting_list.industrialPark');
        foreach ($setting['fields'] as $k=>$v){
            $title[] = ['title'=>$v,'field'=>$k,'width'=>'20','type'=>'string'];
        }
        ExcelModel::exportExcel($title, [], $setting['title'].'导入模板');
    }

    //数据导入
    public function industrialParkImport()
    {
        $setting = config('setting_list.industrialPark');
        $data = import("input");
        $dataHeader = importHeader("input");
        foreach ($dataHeader as $k=>$v){
            foreach ($setting['fields'] as $k1=>$v1){
                if($v1==$v){
                    $tit[$k] = $k1;
                }
            }
        }
        foreach ($data as $k=>$v){
            $tmp = [];
            foreach($v as $k1=>$v1){
                if(in_array($tit[$k1],[])){
                    $v1 = str_replace('.','-',$v1);
                    if(date('Y',strtotime($v1))>=1900){
                        $tmp[$tit[$k1]] = date('Y-m-d',strtotime($v1));
                    }else{
                        $tmp[$tit[$k1]] = gmdate('Y-m-d',\PHPExcel_Shared_Date::ExcelToPHP($v1));
                    }
                }else{
                    $tmp[$tit[$k1]] = str_replace(' ','',$v1);
                }
            }
            $dataList[] = $tmp;
        }
        foreach ($dataList as $v) {
            if(!empty($v['code'])&&!empty($v['name'])){
                $datauser[] = $v;
            }
        }
        foreach ($datauser as $k=>$v){
            $re = self::industrialParkImportSave($setting,$v);
            if($re['code']==0) {
                $result['success'][] = ['row' => $k + 2, 'user_name' => $v['user_name'], 'msg' => '保存成功'];
            }else if($re['code']==-1){
                $result['repeat'][] = ['row' => $k + 2, 'user_name' => $v['user_name'], 'msg' => '单据重复'];
            }else{
                $result['fail'][] = ['row'=>$k+2,'user_name'=>$v['user_name'],'msg'=>$re['msg']];
            }
        }
        Cache::tag('industrialPark')->clear();
        $msg = count($result['success']).'条数据保存成功，'.count($result['repeat']).'条数据重复导入，'.count($result['fail']).'条数据处理失败。';
        result($result,0,$msg);
    }

    /**
     * @param $setting
     * @param $param
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @sysLog "插入产业园区"
     */
    public function industrialParkImportSave($setting,$param){
        foreach ($setting['fields'] as $k=>$v){
            $data[$k] = $param[$k];
        }
        $re = Db::table($setting['table'])->where($data)->field('id')->find();
        if($re){
            return ['','code'=>-1,'msg'=>'重复'];
        }else{
            $p_code = substr($data['code'],0,(strlen($data['code'])-3));
            $p_model = Db::table($setting['table'])->where('code', $p_code)->find();
            $data['pid'] = empty($p_model)?0:$p_model['id'];
            $data['level'] = empty($p_model)?1:$p_model['level']+1;
            $id = Db::table($setting['table'])->insertGetId($data);
        }
        return ['data'=>['id'=>$id],'code'=>0,'msg'=>'保存成功'];
    }
    /**************************产业园区*******************End*******************/




    /**
     * @Apidoc\Title("经济类型设置")
     * @Apidoc\Desc("经济类型设置")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     * @sysLog “经济类型设置导出”
     */
    public function economyType($excel=0) {
        $setting = config('setting_list.economyType');
        if (request()->isAjax()||$excel==1) {
            $res = Db::table($setting['table'])->where([])->order('code')->select()->toArray();
            if($excel==1){
                foreach ($setting['fields'] as $k=>$v){
                    $title[] = ['title'=>$v,'field'=>$k];
                }
                ExcelModel::exportExcel($title, $res, $setting['title'].'导出',true);
            }
            $res = get_tree_children($res);
            result($res);
        }else{
            View::assign('title',$setting['title']);
            View::assign('fields',$setting['fields']);
            return view();
        }
    }

    /**
     * @param $id
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @sysLog 经济类型设置
     */
    public function economyTypeSave($id=0){
        $setting = config('setting_list.economyType');
        $request = $this->request->post();
        foreach ($setting['fields'] as $k=>$v){
            if(empty($request[$k])&&in_array($k,['code','category'])){
                result('',1001,'请填写'.$v);
            }
        }
        $data = [
            'code' => $request['code'],
            'name' => $request['name'],
            'enabled' => $request['enabled'],
        ];
        $re = Db::table($setting['table'])->where([['code','=',$data['code']],['id','<>',$id]])->find();
        if($re){
            result('',1003,'类型重复');
        }
        if($id){
            $re = Db::table($setting['table'])->where(['id'=>$id])->find();
            if(empty($re)){
                result('',1003,'数据不存在或已删除');
            }
            $re = Db::table($setting['table'])->where(['id'=>$id])->update($data);
        }else{
            $data['pid'] = $request['pid'];
            if(!empty($data['pid'])){
                $par = Db::table($setting['table'])->where(['id'=>$data['pid']])->find();
                $data['pid'] = empty($par)?0:$par['id'];
                $data['level'] = empty($par)?1:$par['level']+1;
            }else{
                $data['pid'] = 0;
                $data['level'] = 1;
            }
            $id = Db::table($setting['table'])->insertGetId($data);
        }
        Cache::tag('economyType')->clear();
        result(['id'=>$id]);
    }

    /**
     * @param $id
     * @return void
     * @sysLog 经济类型删除
     */
    public function economyTypeDel($id=0){
        $setting = config('setting_list.economyType');
        $re = SettingModel::del($setting['table'],$id);
        if($re===false){
            result('',1002,'信息有误');
        }
        Cache::tag('economyType')->clear();
        result('',0,'删除成功');
    }

    /**
     * @param $id
     * @param $enabled
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @sysLog 经济类型信息更新
     */
    public function economyTypeEnabled($id=0,$enabled=0){
        $setting = config('setting_list.economyType');
        $re = Db::table($setting['table'])->where(['id'=>$id])->find();
        if(empty($re)){
            result('',1002,'信息不存在');
        }
        $up = Db::table($setting['table'])->where(['id'=>$id])->update(['enabled'=>$enabled]);
        Cache::tag('economyType')->clear();
        result('',0,'成功');
    }

    //导入模板
    public function economyTypeImportTemplate()
    {
        $setting = config('setting_list.economyType');
        foreach ($setting['fields'] as $k=>$v){
            $title[] = ['title'=>$v,'field'=>$k,'width'=>'20','type'=>'string'];
        }
        ExcelModel::exportExcel($title, [], $setting['title'].'导入模板');
    }

    //数据导入
    public function economyTypeImport()
    {
        $setting = config('setting_list.economyType');
        $data = import("input");
        $dataHeader = importHeader("input");
        foreach ($dataHeader as $k=>$v){
            foreach ($setting['fields'] as $k1=>$v1){
                if($v1==$v){
                    $tit[$k] = $k1;
                }
            }
        }
        foreach ($data as $k=>$v){
            $tmp = [];
            foreach($v as $k1=>$v1){
                if(in_array($tit[$k1],[])){
                    $v1 = str_replace('.','-',$v1);
                    if(date('Y',strtotime($v1))>=1900){
                        $tmp[$tit[$k1]] = date('Y-m-d',strtotime($v1));
                    }else{
                        $tmp[$tit[$k1]] = gmdate('Y-m-d',\PHPExcel_Shared_Date::ExcelToPHP($v1));
                    }
                }else{
                    $tmp[$tit[$k1]] = str_replace(' ','',$v1);
                }
            }
            $dataList[] = $tmp;
        }
        foreach ($dataList as $v) {
            if(!empty($v['code'])&&!empty($v['name'])){
                $datauser[] = $v;
            }
        }
        foreach ($datauser as $k=>$v){
            $re = self::economyTypeImportSave($setting,$v);
            if($re['code']==0) {
                $result['success'][] = ['row' => $k + 2, 'user_name' => $v['user_name'], 'msg' => '保存成功'];
            }else if($re['code']==-1){
                $result['repeat'][] = ['row' => $k + 2, 'user_name' => $v['user_name'], 'msg' => '单据重复'];
            }else{
                $result['fail'][] = ['row'=>$k+2,'user_name'=>$v['user_name'],'msg'=>$re['msg']];
            }
        }
        Cache::tag('economyType')->clear();
        $msg = count($result['success']).'条数据保存成功，'.count($result['repeat']).'条数据重复导入，'.count($result['fail']).'条数据处理失败。';
        result($result,0,$msg);
    }

    /**
     * @param $setting
     * @param $param
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @sysLog 经济类型导入保存
     */
    public function economyTypeImportSave($setting,$param){
        foreach ($setting['fields'] as $k=>$v){
            $data[$k] = $param[$k];
        }
        $re = Db::table($setting['table'])->where($data)->field('id')->find();
        if($re){
            return ['','code'=>-1,'msg'=>'重复'];
        }else{
            $p_code = substr($data['code'],0,(strlen($data['code'])-3));
            $p_model = Db::table($setting['table'])->where('code', $p_code)->find();
            $data['pid'] = empty($p_model)?0:$p_model['id'];
            $data['level'] = empty($p_model)?1:$p_model['level']+1;
            $id = Db::table($setting['table'])->insertGetId($data);
        }
        return ['data'=>['id'=>$id],'code'=>0,'msg'=>'保存成功'];
    }

    /**
     * @Apidoc\Title("经济类型设置")
     * @Apidoc\Desc("经济类型设置")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     * @sysLog 经济类型导出
     */
    public function economySector($excel=0) {
        $setting = config('setting_list.economySector');
        if (request()->isAjax()||$excel==1) {
            $res = Db::table($setting['table'])->where([])->order('code')->select()->toArray();
            if($excel==1){
                foreach ($setting['fields'] as $k=>$v){
                    $title[] = ['title'=>$v,'field'=>$k];
                }
                ExcelModel::exportExcel($title, $res, $setting['title'].'导出',true);
            }
            $res = get_tree_children($res);
            result($res);
        }else{
            View::assign('title',$setting['title']);
            View::assign('fields',$setting['fields']);
            return view();
        }
    }

    /**
     * @param $id
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @sysLog 经济类型更新
     */
    public function economySectorSave($id=0){
        $setting = config('setting_list.economySector');
        $request = $this->request->post();
        foreach ($setting['fields'] as $k=>$v){
            if(empty($request[$k])&&in_array($k,['code','category'])){
                result('',1001,'请填写'.$v);
            }
        }
        $data = [
            'code' => $request['code'],
            'name' => $request['name'],
            'enabled' => $request['enabled'],
        ];
        $re = Db::table($setting['table'])->where([['code','=',$data['code']],['id','<>',$id]])->find();
        if($re){
            result('',1003,'类型重复');
        }
        if($id){
            $re = Db::table($setting['table'])->where(['id'=>$id])->find();
            if(empty($re)){
                result('',1003,'数据不存在或已删除');
            }
            $re = Db::table($setting['table'])->where(['id'=>$id])->update($data);
        }else{
            $data['pid'] = $request['pid'];
            if(!empty($data['pid'])){
                $par = Db::table($setting['table'])->where(['id'=>$data['pid']])->find();
                $data['pid'] = empty($par)?0:$par['id'];
                $data['level'] = empty($par)?1:$par['level']+1;
            }else{
                $data['pid'] = 0;
                $data['level'] = 1;
            }
            $id = Db::table($setting['table'])->insertGetId($data);
        }
        Cache::tag('economySector')->clear();
        result(['id'=>$id]);
    }

    /**
     * @param $id
     * @return void
     */
    public function economySectorDel($id=0){
        $setting = config('setting_list.economySector');
        $re = SettingModel::del($setting['table'],$id);
        if($re===false){
            result('',1002,'信息有误');
        }
        Cache::tag('economySector')->clear();
        result('',0,'删除成功');
    }

    public function economySectorEnabled($id=0,$enabled=0){
        $setting = config('setting_list.economySector');
        $re = Db::table($setting['table'])->where(['id'=>$id])->find();
        if(empty($re)){
            result('',1002,'信息不存在');
        }
        $up = Db::table($setting['table'])->where(['id'=>$id])->update(['enabled'=>$enabled]);
        Cache::tag('economySector')->clear();
        result('',0,'成功');
    }

    //导入模板
    public function economySectorImportTemplate()
    {
        $setting = config('setting_list.economySector');
        foreach ($setting['fields'] as $k=>$v){
            $title[] = ['title'=>$v,'field'=>$k,'width'=>'20','type'=>'string'];
        }
        ExcelModel::exportExcel($title, [], $setting['title'].'导入模板');
    }

    //数据导入
    public function economySectorImport()
    {
        $setting = config('setting_list.economySector');
        $data = import("input");
        $dataHeader = importHeader("input");
        foreach ($dataHeader as $k=>$v){
            foreach ($setting['fields'] as $k1=>$v1){
                if($v1==$v){
                    $tit[$k] = $k1;
                }
            }
        }
        foreach ($data as $k=>$v){
            $tmp = [];
            foreach($v as $k1=>$v1){
                if(in_array($tit[$k1],[])){
                    $v1 = str_replace('.','-',$v1);
                    if(date('Y',strtotime($v1))>=1900){
                        $tmp[$tit[$k1]] = date('Y-m-d',strtotime($v1));
                    }else{
                        $tmp[$tit[$k1]] = gmdate('Y-m-d',\PHPExcel_Shared_Date::ExcelToPHP($v1));
                    }
                }else{
                    $tmp[$tit[$k1]] = str_replace(' ','',$v1);
                }
            }
            $dataList[] = $tmp;
        }
        foreach ($dataList as $v) {
            if(!empty($v['code'])&&!empty($v['name'])){
                $datauser[] = $v;
            }
        }
        foreach ($datauser as $k=>$v){
            $re = self::economySectorImportSave($setting,$v);
            if($re['code']==0) {
                $result['success'][] = ['row' => $k + 2, 'user_name' => $v['user_name'], 'msg' => '保存成功'];
            }else if($re['code']==-1){
                $result['repeat'][] = ['row' => $k + 2, 'user_name' => $v['user_name'], 'msg' => '单据重复'];
            }else{
                $result['fail'][] = ['row'=>$k+2,'user_name'=>$v['user_name'],'msg'=>$re['msg']];
            }
        }
        Cache::tag('economySector')->clear();
        $msg = count($result['success']).'条数据保存成功，'.count($result['repeat']).'条数据重复导入，'.count($result['fail']).'条数据处理失败。';
        result($result,0,$msg);
    }

    public function economySectorImportSave($setting,$param){
        foreach ($setting['fields'] as $k=>$v){
            $data[$k] = $param[$k];
        }
        $re = Db::table($setting['table'])->where($data)->field('id')->find();
        if($re){
            return ['','code'=>-1,'msg'=>'重复'];
        }else{
            $p_code = substr($data['code'],0,(strlen($data['code'])-3));
            $p_model = Db::table($setting['table'])->where('code', $p_code)->find();
            $data['pid'] = empty($p_model)?0:$p_model['id'];
            $data['level'] = empty($p_model)?1:$p_model['level']+1;
            $id = Db::table($setting['table'])->insertGetId($data);
        }
        return ['data'=>['id'=>$id],'code'=>0,'msg'=>'保存成功'];
    }

    /**
     * @Apidoc\Title("行业设置")
     * @Apidoc\Desc("行业设置")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function industry($excel = 0)
    {
        $setting = config('setting_list.industry');
        if (request()->isAjax() || $excel == 1) {
            $res = Db::table($setting['table'])->where([])->order('code')->select()->toArray();
            if ($excel == 1) {
                foreach ($setting['fields'] as $k => $v) {
                    // 如果字段是 name，则改成显示全路径
                    if ($k === 'name') {
                        $title[] = ['title' => $v, 'field' => $k];
                    } else {
                        $title[] = ['title' => $v, 'field' => $k];
                    }
                }
                //处理 上级行业全路径名称 字段 的数据
                foreach ($res as $k => $v) {
                    $res[$k]['name'] = self::getFullName($res[$k]);
                }

                ExcelModel::exportExcel($title, $res, $setting['title'] . '导出', true);
            }
            $res = get_tree_children($res);
            result($res);
        } else {
            View::assign('title', $setting['title']);
            View::assign('fields', $setting['fields']);
            return view();
        }
    }

    /**
     * 获取节点完整路径名称
     * @param $node
     * @return string
     */
    /**
     * 获取节点完整路径名称
     * @param $node
     * @return string
     */
    public static function getFullName($node)
    {
        $names = [];
        // 如果有父级，则递归获取父级名称
        if (!empty($node['pid'])) {
            $parent = Db::table('top_industry')->where(['id' => $node['pid']])->find();
            if ($parent) {
                $names[] = self::getFullName($parent); // 递归获取上级名称
            }
        }
        $names[] = $node['name']; // 只有非根节点才加上自己
        return implode('/', $names);
    }



    public function industrySave($id=0){
        $setting = config('setting_list.industry');
        $request = $this->request->post();
        foreach ($setting['fields'] as $k=>$v){
            if(empty($request[$k])&&in_array($k,['code','category'])){
                result('',1001,'请填写'.$v);
            }
        }
        $data = [
            'code' => $request['code'],
            'name' => $request['name'],
            'enabled' => $request['enabled'],
            'department' => $request['department'],
        ];
        $re = Db::table($setting['table'])->where([['code','=',$data['code']],['id','<>',$id]])->find();
        if($re){
            result('',1003,'类型重复');
        }
        if($id){
            $re = Db::table($setting['table'])->where(['id'=>$id])->find();
            if(empty($re)){
                result('',1003,'数据不存在或已删除');
            }
            $re = Db::table($setting['table'])->where(['id'=>$id])->update($data);
        }else{
            $data['pid'] = $request['pid'];
            if(!empty($data['pid'])){
                $par = Db::table($setting['table'])->where(['id'=>$data['pid']])->find();
                $data['pid'] = empty($par)?0:$par['id'];
                $data['level'] = empty($par)?1:$par['level']+1;
            }else{
                $data['pid'] = 0;
                $data['level'] = 1;
            }
            $id = Db::table($setting['table'])->insertGetId($data);
        }
        Cache::tag('industry')->clear();
        result(['id'=>$id]);
    }

    public function industryDel($id=0){
        $setting = config('setting_list.industry');
        $re = SettingModel::del($setting['table'],$id);
        if($re===false){
            result('',1002,'信息有误');
        }
        Cache::tag('industry')->clear();
        result('',0,'删除成功');
    }

    public function industryEnabled($id=0,$enabled=0){
        $setting = config('setting_list.industry');
        $re = Db::table($setting['table'])->where(['id'=>$id])->find();
        if(empty($re)){
            result('',1002,'信息不存在');
        }
        $up = Db::table($setting['table'])->where(['id'=>$id])->update(['enabled'=>$enabled]);
        Cache::tag('industry')->clear();
        result('',0,'成功');
    }

    //导入模板
    public function industryImportTemplate()
    {
        $setting = config('setting_list.industry');
        foreach ($setting['fields'] as $k=>$v){
            $title[] = ['title'=>$v,'field'=>$k,'width'=>'20','type'=>'string'];
        }
        ExcelModel::exportExcel($title, [], $setting['title'].'导入模板');
    }

    //数据导入
    public function industryImport()
    {
        $setting = config('setting_list.industry');
        $data = import("input");
        $dataHeader = importHeader("input");
        foreach ($dataHeader as $k=>$v){
            foreach ($setting['fields'] as $k1=>$v1){
                if($v1==$v){
                    $tit[$k] = $k1;
                }
            }
        }

        foreach ($data as $k=>$v){
            $tmp = [];
            foreach($v as $k1=>$v1){
                if(in_array($tit[$k1],[])){
                    $v1 = str_replace('.','-',$v1);
                    if(date('Y',strtotime($v1))>=1900){
                        $tmp[$tit[$k1]] = date('Y-m-d',strtotime($v1));
                    }else{
                        $tmp[$tit[$k1]] = gmdate('Y-m-d',\PHPExcel_Shared_Date::ExcelToPHP($v1));
                    }
                }else{
                    $tmp[$tit[$k1]] = str_replace(' ','',$v1);
                }
            }
            $dataList[] = $tmp;
        }

        foreach ($dataList as $v) {
            if(!empty($v['code'])&&!empty($v['name'])){
                $datauser[] = $v;
            }
        }

        try {
            foreach ($datauser as $k => $v) {
                $re = self::industryImportSave($setting, $v);
                if ($re['code'] == 0) {
                    $result['success'][] = ['row' => $k + 2, 'user_name' => $v['user_name'], 'msg' => '保存成功'];
                } else if ($re['code'] == -1) {
                    $result['repeat'][] = ['row' => $k + 2, 'user_name' => $v['user_name'], 'msg' => '单据重复'];
                } else {
                    $result['fail'][] = ['row' => $k + 2, 'user_name' => $v['user_name'], 'msg' => $re['msg']];
                }
            }
        }catch (\Exception $e){
            result($result,1000,$e->getMessage());
        }
        Cache::tag('industry')->clear();
        $msg = count($result['success']).'条数据保存成功，'.count($result['repeat']).'条数据重复导入，'.count($result['fail']).'条数据处理失败。';
        result($result,0,$msg);
    }

    public function industryImportSave($setting, $param)
    {
        foreach ($setting['fields'] as $k => $v) {
            $data[$k] = $param[$k];
        }

        // 检查是否已存在该记录
        $re = Db::table($setting['table'])->where($data)->field('id')->find();
        if ($re) {
            return ['', 'code' => -1, 'msg' => '重复'];
        }

        // 处理 parent_name 字段
        $parentId = 0;
        if (!empty($data['name'])) {
            $names = explode('/', $data['name']); // 分割路径
            $currentLevelParentId = 0;

            foreach ($names as $name) {
                $parent = Db::table($setting['table'])
                    ->where(['name' => trim($name)])
                    ->where(function ($query) use ($currentLevelParentId) {
                        $query->where('pid', $currentLevelParentId);
                    })
                    ->find();

                if (!$parent) {
                    // 创建新父级节点
                    $parentId = Db::table($setting['table'])->insertGetId([
                        'name' => trim($name),
                        'pid' => $currentLevelParentId,
                        'level' => $currentLevelParentId ? Db::table($setting['table'])->where('id', $currentLevelParentId)->value('level') + 1 : 1,
                        'enabled' => 1,
                        'code' => $data['code'],
                        'department' => $data['department'],
                    ]);
                } else {
                    $parentId = $parent['id'];
                }
                $currentLevelParentId = $parentId; // 下一级父ID更新
            }
        }

        // 设置当前节点的 pid 和 level
        $data['pid'] = $currentLevelParentId ?: 0;
        $data['level'] = $data['pid'] ? Db::table($setting['table'])->where('id', $data['pid'])->value('level') + 1 : 1;
        $data['name'] = trim(end($names)); // 只保存当前层级的名称

        // 插入数据
        $id = Db::table($setting['table'])->insertGetId($data);
        return ['data' => ['id' => $id], 'code' => 0, 'msg' => '保存成功'];
    }


    //评审标准管理
    public function standard(){
        if (request()->isAjax()) {
            $where = ['is_del' => 0];
            $res = Db::table('top_standard_name')->where($where)->order('sort')->select()->toArray();
            result($res);
        }else{
            View::assign('title','评审标准管理');
            return view("standardList");
        }
    }

    public function standardNameSave($id=0){
        $param = $this->request->param();
        $data = [
            'name' => $param['name'],
            'element' => $param['element'],
            'sort' => $param['sort'],
        ];
        $re = Db::table('top_standard_name')->where([['id','<>',$id],['name','=',$data['name']],['is_del','=','0']])->find();
        if($re){
            result('',1003,'标准已存在');
        }
        if($id){
            $re = Db::table('top_standard_name')->where(['id'=>$id])->find();
            if(empty($re)){
                result('',1003,'数据不存在或已删除');
            }
            $data['update_user_id'] = $_SESSION['LOGIN_USER_ID'];
            $data['update_user_name'] = $_SESSION['LOGIN_USER_NAME'];
            $data['update_time'] = date("Y-m-d H:i:s");
            $re = Db::table('top_standard_name')->where(['id'=>$id])->update($data);
        }else{
            $data['create_user_id'] = $_SESSION['LOGIN_USER_ID'];
            $data['create_user_name'] = $_SESSION['LOGIN_USER_NAME'];
            $data['create_time'] = date("Y-m-d H:i:s");
            $id = Db::table('top_standard_name')->insertGetId($data);
        }
        result(['id'=>$id]);
    }

    public function standardElementSave($id=0){
        $param = $this->request->param();
        $pids = $param['pid'];
        $data = [
            'name' => $param['name'],
            'pid' => $pids[count($pids) - 1] * 1,
            'pids' => ',' . implode(',', $pids) . ',',
            'mark' => $param['mark'],
            'weight' => $param['weight'],
            'sort' => $param['sort'],
        ];
        if($id){
            $re = Db::table('top_standard_element')->where(['id'=>$id])->find();
            if(empty($re)){
                result('',1003,'数据不存在或已删除');
            }
            $re = Db::table('top_standard_element')->where(['id'=>$id])->update($data);
        }else{
            $data['main_id'] = $param['main_id']*1;
            $id = Db::table('top_standard_element')->insertGetId($data);
        }
        result(['id'=>$id]);
    }

    public function standardContentSave($id=0){
        $param = $this->request->param();
        $element_ids = $param['element_id'];
        $data = [
            'content' => $param['content'],
            'score' => $param['score'],
            'cycle' => $param['cycle'],
            'ask' => $param['ask'],
            'standards' => $param['standards'],
            'method' => $param['method'],
            'sort' => $param['sort'],
            'file_remark' => $param['file_remark'],
            'element_id' => $element_ids[count($element_ids) - 1] * 1,
            'element_ids' => ',' . implode(',', $element_ids) . ',',
        ];
        $re = Db::table('top_standard_content')->where([['main_id','=',$param['main_id']],['id','<>',$id],['is_del','=','0']])->find();
        if($re){
            //result('',1003,'要素名称重复');
        }
        $files = [];
        foreach ($param['files'] as $v){
            if(is_array($v)){
                $files[] = $v['code'];
                FileModel::saveFile($v['code'],'standrads');
            }else{
                $files[] = $v;
                FileModel::saveFile($v,'standrads');
            }
        }
        $data['files'] = implode(',',$files);
        if($id){
            $re = Db::table('top_standard_content')->where(['id'=>$id])->find();
            if(empty($re)){
                result('',1003,'数据不存在或已删除');
            }
            $re = Db::table('top_standard_content')->where(['id'=>$id])->update($data);
        }else{
            $data['main_id'] = $param['main_id']*1;
            $id = Db::table('top_standard_content')->insertGetId($data);
        }
        self::setElementScore($data['element_id']);
        result(['id'=>$id]);
    }

    public function standardDel($id=0){
        Db::table('top_standard_name')->where(['id' => explode(',', $id)])->update(['is_del' => 1]);
        Db::table('top_standard_element')->where(['main_id' => explode(',', $id)])->update(['is_del' => 1]);
        Db::table('top_standard_content')->where(['main_id' => explode(',', $id)])->update(['is_del' => 1]);
        result(['id'=>$id]);
    }

    public function standardElementDelete($id=0){
        self::elementDeleteList($id);
        self::setElementScore($id);
        result(['id'=>$id]);
    }

    public function standardContentDelete($id=0){
        Db::table('top_standard_content')->where(['id' => explode(',', $id)])->update(['is_del' => 1]);
        $re = Db::table('top_standard_content')->where(['id' => explode(',', $id)])->find();
        self::setElementScore($re['element_id']);
        result(['id'=>$id]);
    }


    public function elementDeleteList($id)
    {
        Db::table('top_standard_element')->where('id', 'in', $id)->update(['is_del' => 1]);
        Db::table('top_standard_content')->where('element_id', 'in', $id)->update(['is_del' => 1]);
        $children = Db::table('top_standard_element')->where('pid', 'in', $id)->select();
        foreach ($children as $v) {
            self::elementDeleteList($v['id']);
        }
        return true;
    }

    public function setElementScore($element_id = 0)
    {
        $element = Db::table('top_standard_element')->where(['id' => $element_id])->find();
        $score = Db::table('top_standard_element')->where(['pid' => $element_id, 'is_del' => 0])->sum('score');
        if (empty($score)) {
            $score = Db::table('top_standard_content')->where(['element_id' => $element_id, 'is_del' => 0])->sum('score');
        }
        Db::table('top_standard_element')->where(['id' => $element_id, 'is_del' => 0])->update(['score' => $score * 1]);
        if (!empty($element['pid'])) {
            self::setElementScore($element['pid']);
        }
    }

    public function standardElementList($id=0){
        $re = Db::table('top_standard_name')->where(['id' => $id, 'is_del' => 0])->find();
        if ($re) {
            $re['elementTitle'] = empty($re['element']) ? [] : explode(',', $re['element']);
            $content = Db::table('top_standard_content')->where(['main_id' => $id, 'is_del' => 0])->order('sort,id')->select()->toArray();
            foreach ($content as $k=>$v){
                $file = empty($v['files'])?[]:explode(',',$v['files']);
                $content[$k]['files'] = [];
                foreach ($file as $v1){
                    if(!empty($v1)){
                        $content[$k]['files'][] = FileModel::getFile(0,$v1,'');
                    }
                }
            }
            if (empty($re['elementTitle'])) {
                $re['content'] = $content;
            } else {
                $element = Db::table('top_standard_element')->where(['main_id' => $id, 'is_del' => 0])->order('sort,id')->select()->toArray();
                $element = get_tree_children($element);
                foreach ($content as $v) {
                    $v['jzd'] = json_decode($v['method'], true);
                    $method = json_decode($v['method'], true);
                    $str = '';
                    if (!empty($method)) {
                        foreach ($method as $itemKey) {
                            if ($itemKey['val']) {
                                $str .= '★' . $itemKey['key'];
                            } else {
                                $str .= $itemKey['key'];
                            }
                        }
                        $v['method'] = $str;
                    }
                    $tmp[$v['element_id']][] = $v;
                }
                for ($i = 1; $i <= count($re['elementTitle']); $i++) {
                    $re['elementParent' . $i] = self::getTreeElement($element, 1, $i);
                }
                $content = self::getTreeContent($element, $tmp, 1, count($re['elementTitle']))['res'];
                $re['content'] = self::getTreeContentList($content, [], 0, count($re['elementTitle']));
            }
        } else {
            $re = [];
        }
        result($re);
    }


    public function standardExcel($id=0){
        $re = Db::table('top_standard_name')->where(['id' => $id, 'is_del' => 0])->find();
        if ($re) {
            $re['elementTitle'] = empty($re['element']) ? [] : explode(',', $re['element']);
            $title = self::getelementTitle($re['elementTitle']);
//            dd($title);
            $content = Db::table('top_standard_content')->where(['main_id' => $id, 'is_del' => 0])->order('sort,id')->select()->toArray();
            if (empty($re['elementTitle'])) {
                $re['content'] = $content;
            } else {
                $element = Db::table('top_standard_element')->where(['main_id' => $id, 'is_del' => 0])->order('sort,id')->select()->toArray();
                $element = get_tree_children($element);
                foreach ($content as $v) {
                    $v['files'] = empty($v['files']) ? [] : json_decode($v['files'], true);
                    $v['jzd'] = json_decode($v['method'], true);
                    $method = json_decode($v['method'], true);
                    $str = '';
                    if (!empty($method)) {
                        foreach ($method as $itemKey) {
                            if ($itemKey['val']) {
                                $str .= '★' . $itemKey['key'];
                            } else {
                                $str .= $itemKey['key'];
                            }
                        }
                        $v['method'] = $str;
                    }
                    $tmp[$v['element_id']][] = $v;
                }
                for ($i = 1; $i <= count($re['elementTitle']); $i++) {
                    $re['elementParent' . $i] = self::getTreeElement($element, 1, $i);
                }
                $content = self::getTreeContent($element, $tmp, 1, count($re['elementTitle']))['res'];
//                $re['content'] = self::getTreeContentList($content, [], 0, count($re['elementTitle']));
            }
//            dd($content);
            ExcelModel::exportExcel($title,$content,$re['name']);
        } else {
            $re = [];
        }
        result($re);
    }


    public function getelementTitle($elementTitle)
    {
        $title = [];
        if(empty($elementTitle)){
            $title = [
                ['title'=>'考评内容','field'=>'content','width'=>'25'],
                ['title'=>'标准分值','field'=>'score','width'=>'5'],
                ['title'=>'填报周期','field'=>'cycle','width'=>'5'],
                ['title'=>'基本规范要求','field'=>'ask','width'=>'25'],
                ['title'=>'企业达标标准','field'=>'standards','width'=>'25'],
                ['title'=>'评分方式','field'=>'method','width'=>'25'],
                ['title'=>'自评/评审描述','field'=>'file_remark','width'=>'25'],
            ];
        }else{
            foreach ($elementTitle as $k => $v) {
                unset($elementTitle[$k]);
                $title[] = ['title'=>$v,'field'=>'name','width'=>'15'];
                $title[] = ['title'=>'','field'=>'','children'=>self::getelementTitle($elementTitle)];
//                $title[] = ['title'=>'','field'=>'','children'=>self::getelementTitle($elementTitle)];
                break;
            }
        }
        return $title;
    }


    public function getTreeElement($element, $level = 1, $max = 1)
    {
        $res = [];
        foreach ($element as $k => $v) {
            $children = $v['children'];
            unset($v['children']);
            $res[$k] = $v;
            if ($level < $max) {
                $res[$k]['children'] = self::getTreeElement($children, ($level + 1), $max);
//                dd($children);
            }
        }
        return $res;
    }

    public function getTreeContent($element, $content, $level = 1, $max = 2)
    {
        $res = [];
        foreach ($element as $k => $v) {
            $children = $v['children'];
            unset($v['children']);
            $res[$k] = $v;
            if ($level < $max) {
                $child = self::getTreeContent($children, $content, ($level + 1), $max);
                $res[$k]['children'] = empty($child['res']) ? [] : $child['res'];
                $res[$k]['rows'] = $child['rows'] < 1 ? 1 : $child['rows'];
                $rows += $child['rows'] < 1 ? 1 : $child['rows'];
            } else {
                $row = count($content[$v['id']]) <= 1 ? 1 : count($content[$v['id']]);
                $res[$k]['children'] = empty($content[$v['id']]) ? [] : $content[$v['id']];
                $res[$k]['rows'] = $row;
                $rows += $row;
            }
        }
        return ['res' => $res, 'rows' => $rows];
    }

    public function getTreeContentList($content, $data = [], $level = 0, $max = 2)
    {
        $res = [];
        $content = empty($content) ? [['id' => 0, 'name' => '', 'mark' => '', 'score' => '', 'rows' => 1]] : $content;
        foreach ($content as $k => $v) {
            for ($a = 0; $a <= $max; $a++) {
                if ($data['rows' . $a] > 0) {
                    $data['rows' . $a] = $k == 0 ? $data['rows' . $a] : 0;
                }
            }
            if ($level >= $max) {
                $v = array_merge($v, $data);
                $res[] = $v;
            } else {
                $tmp = [
                    'id' . $level => $v['id'],
                    'name' . $level => $v['name'],
                    'mark' . $level => $v['mark'],
                    'weight' . $level => $v['weight'],
                    'score' . $level => $v['score'],
                    'rows' . $level => $v['rows'],
                    'sort' . $level => $v['sort'],
                ];
                $results = self::getTreeContentList($v['children'], array_merge($tmp, $data), $level + 1, $max);
                foreach ($results as $v) {
                    $res[] = $v;
                }
            }
        }
        return $res;
    }

    public function advisory(){

    }

    //评审标准管理-列表
    public function standardList()
    {
        return view("standardList");
    }

    //获取数据-评审标准管理-列表
    public function getStandardList()
    {
        $where = ['is_del' => 0];
        $res = Db::table('top_standard_name')->where($where)->order('status,sort')->select()->toArray();
        result($res);
    }

    //启用、停用
    public function updateStatus()
    {
        $param = $this->request->param();
        Db::table('top_standard_name')->where(['id' => explode(',', $param['id'])])->update(['status' => $param['status']]);
        result('', 0, '操作成功');
    }

    //评审标准管理
    public function standardDes(){
        if (request()->isAjax()) {
            $param = $this->request->param();

            $where = ['is_del' => 0,'id'=>$param['id']];
            $res = Db::table('top_standard_name')->where($where)->order('status,sort')->select()->toArray();
            result($res);
        }else{
            View::assign('title','评审标准管理');
            return view("standardDes");
        }
    }


    //导入模板
    public function standardImportTemplate($id=0)
    {
        $re = Db::table('top_standard_name')->where(['id' => $id, 'is_del' => 0])->find();
        $re['elementTitle'] = empty($re['element']) ? [] : explode(',', $re['element']);
        $title = self::getelementTitle($re['elementTitle']);
        ExcelModel::exportExcel($title, [], $re['name'].'导入模板');
    }

    //数据导入
    public function standardImport($id=0)
    {
        $re = Db::table('top_standard_name')->where(['id' => $id, 'is_del' => 0])->find();
        $re['elementTitle'] = empty($re['element']) ? [] : explode(',', $re['element']);
        $data = import("input");
        $dataHeader = importHeader("input");
        foreach ($dataHeader as $k=>$v){
            foreach ($re['elementTitle'] as $k1=>$v1){
                if($v1==$v){
                    $tit[$k] = 'name'.$k1;
                }
            }
            switch ($v){
                case '考评内容':
                    $tit[$k] = 'content';
                    break;
                case '标准分值':
                    $tit[$k] = 'score';
                    break;
                case '填报周期':
                    $tit[$k] = 'cycle';
                    break;
                case '基本规范要求':
                    $tit[$k] = 'ask';
                    break;
                case '企业达标标准':
                    $tit[$k] = 'standards';
                    break;
                case '评分方式':
                    $tit[$k] = 'method';
                    break;
                case '自评/评审描述':
                    $tit[$k] = 'file_remark';
                    break;
                default:
                    break;
            }
        }
        foreach ($data as $k=>$v){
            $tmp = [];
            foreach($v as $k1=>$v1){
                if(in_array($tit[$k1],[])){
                    $v1 = str_replace('.','-',$v1);
                    if(date('Y',strtotime($v1))>=1900){
                        $tmp[$tit[$k1]] = date('Y-m-d',strtotime($v1));
                    }else{
                        $tmp[$tit[$k1]] = gmdate('Y-m-d',\PHPExcel_Shared_Date::ExcelToPHP($v1));
                    }
                }else{
                    $tmp[$tit[$k1]] = str_replace(' ','',$v1);
                }
            }
            $dataList[] = $tmp;
        }
        foreach ($dataList as $v) {
            if(!empty($v['score'])||!empty($v['method'])){
                $datauser[] = $v;
            }
        }
        Db::table('top_standard_element')->where(['main_id'=>$id])->update(['is_del'=>1]);
        Db::table('top_standard_content')->where(['main_id'=>$id])->update(['is_del'=>1]);
        $pids[] = 0;
        $element_id = 0;
        foreach ($datauser as $k=>$v){
            $pid = 0;
            $dpids = ',,';
            foreach ($re['elementTitle'] as $k1=>$v1){
                if($k1>0){
                    $pel = Db::table('top_standard_element')->where(['main_id'=>$id,'id'=>$pids[($k1-1)]])->find();
                    $pid = empty($pel['id'])?0:$pel['id'];
                    $tmppids = empty(trim($pel['pids'],','))?[]:explode(',',trim($pel['pids'],','));
                    $tmppids[] = $pel['id'];
                    $dpids = ','.implode(',',$tmppids).',';
                }
//                echo $v['name'.$k1].'--';
                if(!empty($v['name'.$k1])){
                    $data_ele = [
                        'main_id' => $id,
                        'name' => $v['name'.$k1],
                        'mark' => '60',
                        'sort' => Db::table('top_standard_element')->where(['main_id'=>$id])->count()+1,
                        'pid' => $pid,
                        'pids' => $dpids,
                        'weight' => '1',
                    ];
//                    dd($data_ele);
                    Db::table('top_standard_element')->insert($data_ele);
                    $tmp_pid = Db::table('top_standard_element')->where($data_ele)->find()['id'];
                    if($k1==count($re['elementTitle'])-1){
                        $element_id = $tmp_pid;
                    }else{
                        $pids[$k1] = $tmp_pid;
                    }
                }
            }
//            echo '<br/>';
            $re1 = self::standardImportSave($v,$element_id,$id,$k+1);
            if($re1['code']==0) {
                $result['success'][] = ['row' => $k + 2, 'user_name' => $v['user_name'], 'msg' => '保存成功'];
            }else if($re1['code']==-1){
                $result['repeat'][] = ['row' => $k + 2, 'user_name' => $v['user_name'], 'msg' => '单据重复'];
            }else{
                $result['fail'][] = ['row'=>$k+2,'user_name'=>$v['user_name'],'msg'=>$re['msg']];
            }
        }
        Cache::tag('industry')->clear();
        $msg = count($result['success']).'条数据保存成功，'.count($result['repeat']).'条数据重复导入，'.count($result['fail']).'条数据处理失败。';
        result($result,0,'导入成功');
    }

    public function standardImportSave($param,$element_id,$id,$sort){
        $element = Db::table('top_standard_element')->where(['id'=>$element_id])->find();
        $data = [
            'main_id' => $id,
            'content' => $param['content'],
            'score' => $param['score'],
            'cycle' => $param['cycle'],
            'ask' => $param['ask'],
            'method' => $param['method'],
            'file_remark' => $param['file_remark'],
            'element_id' => empty($element)?'':$element['id'],
            'element_ids' => empty($element)?','.$element_id.',':$element['pids'].$element_id.',',
            'sort' => $sort,
        ];
        $re = Db::table('top_standard_content')->where($data)->field('id')->find();
        $data['standards'] = $param['standards'];
        if($re){
            return ['','code'=>-1,'msg'=>'重复'];
        }else{
            $id = Db::table('top_standard_content')->insert($data);
        }
        self::setElementScore($element_id);
        return ['data'=>['id'=>$id],'code'=>0,'msg'=>'保存成功'];
    }

    public function upload($model = '')
    {
        $file = request()->file('file');
        $result = FileModel::upload($file, $model);
        result($result);
    }


    /**************************参数设置*******************Start*******************/
    public function saveConfig(){
        $setting = config('setting_list.config');
        $configTimeLimit = Db::table($setting['table'])->where('param_key','=','apply_run_time_limit')->find();
        $apply_run_time_limit = !empty($configTimeLimit) ? $configTimeLimit['param_value'] : 180;
        if (request()->isAjax()) {
            $request = $this->request->post();
            foreach ($setting['fields'] as $k=>$v){
                if(empty($request[$k]) && in_array($k,['apply_run_time_limit'])){
                    result('',1001,'请填写'.$v);
                }
            }
            $data = [
                'param_key' => 'apply_run_time_limit',
                'param_value' =>  $request['apply_run_time_limit'],
            ];
            try {
                $re = Db::table($setting['table'])->where('param_key','=',$data['param_key'])->find();
                if($re){
                    //更新
                    $res = Db::table($setting['table'])->where('param_key','=',$data['param_key'])->update($data);
                }else{
                    //添加
                    $res = Db::table($setting['table'])->insert($data);
                }
                if($res){
                    result('',0,'设置成功');
                }else{
                    result('',1003,'设置失败');
                }
            }catch (\ErrorException $e){
                result('',1003,'设置失败');
            }

        }else{
            View::assign('apply_run_time_limit',$apply_run_time_limit);
            return view();
        }
    }
    /**************************参数设置*******************End*******************/
}
