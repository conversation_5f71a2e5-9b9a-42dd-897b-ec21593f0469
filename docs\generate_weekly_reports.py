import os
import re
import datetime
import os
from docx import Document
from docx.shared import Pt, Cm
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_ALIGN_VERTICAL
from docx.oxml.ns import qn
from docx.shared import RGBColor

# 获取脚本所在目录
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))

# 配置参数
INPUT_FILE = os.path.join(SCRIPT_DIR, '功能内容.txt')  # 输入文件路径
OUTPUT_DIR = os.path.join(SCRIPT_DIR, '周报')  # 输出目录
START_DATE = datetime.date(2024, 9, 2)  # 开始日期：2024年9月第一周
END_DATE = datetime.date(2025, 6, 30)  # 结束日期：2025年6月

# 确保输出目录存在
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 解析功能内容文件，提取每周的工作内容
def parse_content_file(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 使用正则表达式提取各阶段和周的内容
    stages = {}
    current_stage = None
    
    # 匹配阶段标题和内容
    stage_pattern = r'### ([^#]+?)\n\n(.*?)(?=###|$)'
    stage_matches = re.finditer(stage_pattern, content, re.DOTALL)
    
    for stage_match in stage_matches:
        stage_title = stage_match.group(1).strip()
        stage_content = stage_match.group(2).strip()
        stages[stage_title] = {}
        
        # 匹配每周的内容
        week_pattern = r'- \*\*第(\d+)周\*\*\n([\s\S]*?)(?=- \*\*第|$)'
        week_matches = re.finditer(week_pattern, stage_content, re.DOTALL)
        
        for week_match in week_matches:
            week_num = int(week_match.group(1))
            week_content = week_match.group(2).strip()
            
            # 匹配每个功能点
            feature_pattern = r'\s+- \*\*([^*]+?)\*\*：([^\n]+(?:\n[^\n-]+)*)'
            feature_matches = re.finditer(feature_pattern, week_content, re.DOTALL)
            
            features = []
            for feature_match in feature_matches:
                feature_name = feature_match.group(1).strip()
                feature_desc = feature_match.group(2).strip()
                features.append({
                    'name': feature_name,
                    'description': feature_desc
                })
            
            stages[stage_title][week_num] = features
    
    return stages

# 生成周报文档
def generate_weekly_report(week_start_date, week_number, stage_name, week_features):
    doc = Document()
    
    # 设置文档页面边距
    sections = doc.sections
    for section in sections:
        section.top_margin = Cm(2.54)
        section.bottom_margin = Cm(2.54)
        section.left_margin = Cm(3.18)
        section.right_margin = Cm(3.18)
    
    # 设置中文字体
    def set_chinese_font(run):
        run.font.name = '宋体'
        run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
    
    # 添加标题
    week_end_date = week_start_date + datetime.timedelta(days=6)
    title = f"成都市企业安全生产标准化信息管理系统\n(升级改造建设项目) 项目施工周报"
    subtitle = f"（{week_start_date.year}年{week_start_date.month}月{week_start_date.day}日—{week_end_date.month}月{week_end_date.day}日）"
    
    heading = doc.add_paragraph()
    heading.alignment = WD_ALIGN_PARAGRAPH.CENTER
    heading_run = heading.add_run(title)
    heading_run.font.size = Pt(16)
    heading_run.font.bold = True
    set_chinese_font(heading_run)
    
    subheading = doc.add_paragraph()
    subheading.alignment = WD_ALIGN_PARAGRAPH.CENTER
    subheading_run = subheading.add_run(subtitle)
    subheading_run.font.size = Pt(14)
    set_chinese_font(subheading_run)
    
    # 添加项目信息表格
    table = doc.add_table(rows=1, cols=4)
    table.style = 'Table Grid'
    table.autofit = False
    table.columns[0].width = Cm(2.5)
    table.columns[1].width = Cm(7.66)
    table.columns[2].width = Cm(3.83)
    table.columns[3].width = Cm(3.83)
    
    # 设置表格行高
    for row in table.rows:
        row.height = Cm(1.2)
    
    # 项目名称行
    row_cells = table.rows[0].cells
    row_cells[0].text = "项目名称"
    row_cells[0].vertical_alignment = WD_ALIGN_VERTICAL.CENTER
    p = row_cells[0].paragraphs[0]
    p.alignment = WD_ALIGN_PARAGRAPH.CENTER
    for run in p.runs:
        set_chinese_font(run)
        run.font.size = Pt(12)
    
    # 合并项目名称的后三列
    row_cells[1].merge(row_cells[2]).merge(row_cells[3])
    row_cells[1].text = "成都市企业安全生产标准化信息管理系统（升级改造建设项目）"
    row_cells[1].vertical_alignment = WD_ALIGN_VERTICAL.CENTER
    p = row_cells[1].paragraphs[0]
    p.alignment = WD_ALIGN_PARAGRAPH.CENTER
    for run in p.runs:
        set_chinese_font(run)
        run.font.size = Pt(12)
    
    # 本周项目情况及存在问题行
    table.add_row()
    table.rows[1].height = Cm(8.0)  # 设置内容行高度
    row_cells = table.rows[1].cells
    row_cells[0].text = "本\n周\n项\n目\n情\n况\n及\n存\n在\n问\n题"
    row_cells[0].vertical_alignment = WD_ALIGN_VERTICAL.CENTER
    p = row_cells[0].paragraphs[0]
    p.alignment = WD_ALIGN_PARAGRAPH.CENTER
    for run in p.runs:
        set_chinese_font(run)
        run.font.size = Pt(12)
    
    # 合并本周项目情况的后三列
    row_cells[1].merge(row_cells[2]).merge(row_cells[3])
    
    # 添加本周项目情况
    content_cell = row_cells[1]
    p = content_cell.add_paragraph()
    p.alignment = WD_ALIGN_PARAGRAPH.LEFT
    p.paragraph_format.first_line_indent = Pt(24)
    run = p.add_run("一、本周项目开展的工作：")
    run.font.size = Pt(12)
    run.font.bold = True
    set_chinese_font(run)
    
    # 根据周数生成不同的工作描述
    work_descriptions = [
        "本周项目组按照计划推进各项工作，重点完成了需求分析和系统设计相关任务。",
        "本周项目组继续开展系统开发工作，完成了多个核心功能模块的实现。",
        "本周项目组进行了系统集成和测试工作，解决了多个关键技术问题。",
        "本周项目组完成了系统优化和性能调优，提升了系统整体稳定性和响应速度。",
        "本周项目组进行了系统部署准备和用户培训工作，为系统上线做好充分准备。",
        "本周项目组开展了系统验收准备工作，完成了相关文档的编写和整理。",
        "本周项目组进行了系统运维和支持工作，确保系统稳定运行。",
        "本周项目组进行了项目总结和经验分享，为后续项目积累了宝贵经验。"
    ]
    
    # 选择当前周的工作描述
    selected_description = work_descriptions[week_number % len(work_descriptions)]
    
    p = content_cell.add_paragraph()
    p.alignment = WD_ALIGN_PARAGRAPH.LEFT
    p.paragraph_format.first_line_indent = Pt(24)
    run = p.add_run(selected_description)
    run.font.size = Pt(12)
    set_chinese_font(run)
    
    # 添加功能点 - 根据周数生成不同内容
    if week_features:
        for i, feature in enumerate(week_features, 1):
            
            p.paragraph_format.first_line_indent = Pt(24)  # 首行缩进2个中文字符
            
            # 根据周数和功能点索引生成不同的描述
            descriptions = [
                f"完成{feature['name']}的需求分析和设计方案制定。",
                f"进行{feature['name']}的开发工作，完成核心功能实现。",
                f"对{feature['name']}进行单元测试和集成测试。",
                f"完善{feature['name']}的文档和用户指南。",
                f"优化{feature['name']}的性能和用户体验。"
            ]
            
            # 使用周数和功能点索引的组合来选择描述，确保每周内容不同
            desc_index = (week_number + i) % len(descriptions)
            run = p.add_run(f"{i}. {descriptions[desc_index]}")
            run.font.size = Pt(12)
            set_chinese_font(run)
    
    # 添加其他工作内容 - 根据周数生成不同内容
    other_tasks = [
        ["组织召开项目周例会，讨论项目进展和问题。", "与项目主管、业代、监理沟通项目进展。", "编写项目周报和技术文档。"],
        ["组织项目组内部技术评审会议。", "与客户代表沟通需求变更事项。", "更新项目计划和风险管理文档。"],
        ["组织代码评审和质量检查。", "与测试团队协调测试计划和资源。", "编写技术方案和架构文档。"],
        ["组织项目阶段性总结会议。", "与客户进行阶段性演示和汇报。", "更新项目文档和交付物清单。"]
    ]
    
    # 选择本周的其他任务集合
    week_tasks = other_tasks[week_number % len(other_tasks)]
    
    for j, task in enumerate(week_tasks, 1):
        p = content_cell.add_paragraph()
        p.alignment = WD_ALIGN_PARAGRAPH.LEFT
        p.paragraph_format.first_line_indent = Pt(24)
        run = p.add_run(f"{len(week_features) + j}. {task}")
        run.font.size = Pt(12)
        set_chinese_font(run)
    
    # 二、目前存在的问题 - 根据周数生成不同内容
    p = content_cell.add_paragraph()
    p.alignment = WD_ALIGN_PARAGRAPH.LEFT
    run = p.add_run("\n二、目前存在的问题：")
    run.font.size = Pt(12)
    run.font.bold = True
    set_chinese_font(run)
    
    # 不同周的问题列表
    problems_by_week = [
        ["项目沟通机制仍有待建立。", "项目组内部工作有待进一步明确。"],
        ["部分功能的技术实现方案需要进一步优化。", "系统性能在高并发场景下有待提升。"],
        ["用户界面的交互体验需要进一步改进。", "部分功能与现有系统的集成存在兼容性问题。"],
        ["测试环境与生产环境存在差异，影响测试结果的准确性。", "部分非功能需求的实现方案需要调整。"]
    ]
    
    # 选择本周的问题集合
    week_problems = problems_by_week[week_number % len(problems_by_week)]
    
    for k, problem in enumerate(week_problems, 1):
        p = content_cell.add_paragraph()
        p.alignment = WD_ALIGN_PARAGRAPH.LEFT
        p.paragraph_format.first_line_indent = Pt(24)
        run = p.add_run(f"{k}. {problem}")
        run.font.size = Pt(12)
        set_chinese_font(run)
    
    # 添加其他工作内容
    p = content_cell.add_paragraph()
    p.alignment = WD_ALIGN_PARAGRAPH.LEFT
    p.paragraph_format.first_line_indent = Pt(24)
    run = p.add_run(f"{len(week_features) + 1}. 组织召开项目会议，讨论项目组织构成，人员安排等工作。")
    run.font.size = Pt(12)
    set_chinese_font(run)
    
    p = content_cell.add_paragraph()
    p.alignment = WD_ALIGN_PARAGRAPH.LEFT
    p.paragraph_format.first_line_indent = Pt(24)
    run = p.add_run(f"{len(week_features) + 2}. 与项目主管、业代、监理沟通。")
    run.font.size = Pt(12)
    set_chinese_font(run)
    
    p = content_cell.add_paragraph()
    p.alignment = WD_ALIGN_PARAGRAPH.LEFT
    p.paragraph_format.first_line_indent = Pt(24)
    run = p.add_run(f"{len(week_features) + 3}. 编写项目实施方案。")
    run.font.size = Pt(12)
    set_chinese_font(run)
    

    
    # 下周工作要点行
    table.add_row()
    table.rows[2].height = Cm(6.0)  # 设置下周工作要点行高度
    row_cells = table.rows[2].cells
    row_cells[0].text = "下\n周\n工\n作\n要\n点"
    row_cells[0].vertical_alignment = WD_ALIGN_VERTICAL.CENTER
    p = row_cells[0].paragraphs[0]
    p.alignment = WD_ALIGN_PARAGRAPH.CENTER
    for run in p.runs:
        set_chinese_font(run)
        run.font.size = Pt(12)
    
    # 合并下周工作要点的后三列
    row_cells[1].merge(row_cells[2]).merge(row_cells[3])
    
    # 添加下周工作要点
    content_cell = row_cells[1]
    p = content_cell.add_paragraph()
    p.alignment = WD_ALIGN_PARAGRAPH.LEFT
    p.paragraph_format.first_line_indent = Pt(24)
    run = p.add_run("三、下周工作要点：")
    run.font.size = Pt(12)
    run.font.bold = True
    set_chinese_font(run)
    
    # 获取下周的功能点（如果有）
    next_week_number = week_number + 1
    next_week_features = []
    
    # 尝试从当前阶段获取下周功能点
    for stage_title, weeks in all_stages.items():
        if next_week_number in weeks:
            next_week_features = weeks[next_week_number]
            break
    
    # 如果当前阶段没有下周的功能点，尝试从下一个阶段获取
    if not next_week_features:
        stages_list = list(all_stages.keys())
        current_stage_index = stages_list.index(stage_name)
        if current_stage_index + 1 < len(stages_list):
            next_stage = stages_list[current_stage_index + 1]
            if 1 in all_stages[next_stage]:
                next_week_features = all_stages[next_stage][1]
    
    # 添加下周工作要点 - 根据周数生成不同内容
    if next_week_features:
        for i, feature in enumerate(next_week_features, 1):
            p = content_cell.add_paragraph()
            p.alignment = WD_ALIGN_PARAGRAPH.LEFT
            p.paragraph_format.first_line_indent = Pt(24)
            
            # 根据周数和功能点索引生成不同的描述
            next_week_tasks = [
                f"开始{feature['name']}的需求分析和设计工作。",
                f"继续推进{feature['name']}的开发工作。",
                f"完成{feature['name']}的核心功能实现并进行测试。",
                f"优化{feature['name']}的性能和用户体验。",
                f"完成{feature['name']}的文档编写和用户培训准备。"
            ]
            
            # 使用下周数和功能点索引的组合来选择描述
            task_index = (next_week_number + i) % len(next_week_tasks)
            run = p.add_run(f"{i}. {next_week_tasks[task_index]}")
            run.font.size = Pt(12)
            set_chinese_font(run)
    else:
        # 如果没有找到下周的功能点，添加一些通用的工作要点
        general_tasks = [
            ["继续推进本周未完成的功能开发。", "组织项目组成员进行代码评审和功能测试。"],
            ["完善系统架构设计和技术方案。", "进行系统性能测试和优化。"],
            ["准备阶段性交付和演示。", "与客户沟通下一阶段的需求和计划。"],
            ["进行项目风险评估和应对措施制定。", "更新项目计划和资源分配。"]
        ]
        
        # 选择下周的通用任务集合
        selected_tasks = general_tasks[next_week_number % len(general_tasks)]
        
        for i, task in enumerate(selected_tasks, 1):
            p = content_cell.add_paragraph()
            p.alignment = WD_ALIGN_PARAGRAPH.LEFT
            p.paragraph_format.first_line_indent = Pt(24)
            run = p.add_run(f"{i}. {task}")
            run.font.size = Pt(12)
            set_chinese_font(run)
    
    # 添加其他通用工作要点 - 根据周数生成不同内容
    common_tasks = [
        ["整理项目文档，编写技术方案。", "召开项目例会，汇报进展情况。"],
        ["更新项目风险清单和应对措施。", "准备阶段性总结报告。"],
        ["协调各团队资源，确保项目顺利推进。", "进行内部技术培训和知识分享。"],
        ["与客户进行需求确认和变更管理。", "准备项目阶段性验收材料。"]
    ]
    
    # 选择下周的通用任务集合
    selected_common_tasks = common_tasks[next_week_number % len(common_tasks)]
    
    start_index = 1 if not next_week_features else len(next_week_features) + 1
    if not next_week_features:
        start_index = len(general_tasks[next_week_number % len(general_tasks)]) + 1
    
    for i, task in enumerate(selected_common_tasks, start_index):
        p = content_cell.add_paragraph()
        p.alignment = WD_ALIGN_PARAGRAPH.LEFT
        p.paragraph_format.first_line_indent = Pt(24)
        run = p.add_run(f"{i}. {task}")
        run.font.size = Pt(12)
        set_chinese_font(run)
    
    # 添加签名栏 - 承建单位行
    table.add_row()
    table.rows[3].height = Cm(1.25)  # 设置承建单位行高度
    row_cells = table.rows[3].cells
    
    # 承建单位内容 - 第一个单元格
    p = row_cells[0].add_paragraph()
    p.alignment = WD_ALIGN_PARAGRAPH.CENTER
    run = p.add_run("承建单位")
    run.font.size = Pt(12)
    set_chinese_font(run)
    
    # 承建单位内容 - 第二个单元格
    p = row_cells[1].add_paragraph()
    p.alignment = WD_ALIGN_PARAGRAPH.CENTER
    run = p.add_run("成都市城市安全与应急管理研究院")
    run.font.size = Pt(12)
    row_cells[1].merge(row_cells[2]).merge(row_cells[3])
    set_chinese_font(run)
    
    # 添加项目经理和日期行
    table.add_row()
    table.rows[4].height = Cm(1.25)  # 设置项目经理和日期行高度
    row_cells = table.rows[4].cells
    
    # 项目经理标题
    p = row_cells[0].add_paragraph()
    p.alignment = WD_ALIGN_PARAGRAPH.CENTER
    run = p.add_run("项目经理")
    run.font.size = Pt(12)
    set_chinese_font(run)
    
    # 项目经理姓名
    p = row_cells[1].add_paragraph()
    p.alignment = WD_ALIGN_PARAGRAPH.CENTER
    run = p.add_run("陈倩")
    run.font.size = Pt(12)
    set_chinese_font(run)
    
    # 日期标题
    p = row_cells[2].add_paragraph()
    p.alignment = WD_ALIGN_PARAGRAPH.CENTER
    run = p.add_run("日期")
    run.font.size = Pt(12)
    set_chinese_font(run)
    
    # 日期内容
    p = row_cells[3].add_paragraph()
    p.alignment = WD_ALIGN_PARAGRAPH.CENTER
    run = p.add_run(f"{week_end_date.year}.{week_end_date.month}.{week_end_date.day}")
    run.font.size = Pt(12)
    set_chinese_font(run)
    
    # 保存文档
    report_filename = f"周报_{week_start_date.strftime('%Y%m%d')}-{week_end_date.strftime('%Y%m%d')}.docx"
    report_path = os.path.join(OUTPUT_DIR, report_filename)
    doc.save(report_path)
    return report_path

# 主函数
def main():
    global all_stages
    # 解析功能内容文件
    all_stages = parse_content_file(INPUT_FILE)
    
    # 生成周报
    current_date = START_DATE
    week_counter = 0
    generated_reports = []
    
    # 添加前两个月的需求调研和文档编写
    requirement_analysis_weeks = 8  # 2个月，每月4周
    
    # 定义前两个月的详细工作内容
    requirement_analysis_content = [
        # 第1周 - 需求调研初期
        [
            {'name': '项目启动', 'description': '组织项目启动会议，明确项目目标、范围和时间节点'},
            {'name': '需求调研计划制定', 'description': '制定详细的需求调研计划，包括调研对象、方式和时间安排'},
            {'name': '用户访谈准备', 'description': '准备用户访谈提纲和相关材料'},
            {'name': '现有系统调研', 'description': '调研现有安全生产标准化信息管理系统的功能和架构'}
        ],
        # 第2周 - 需求调研深入
        [
            {'name': '用户访谈', 'description': '与市应急局、区（市）县应急局、评审单位、评审专家和企业等五类用户进行访谈'},
            {'name': '业务流程分析', 'description': '分析企业安全生产标准化评审的业务流程和关键节点'},
            {'name': '系统痛点收集', 'description': '收集现有系统的痛点和用户需求'},
            {'name': '需求初步整理', 'description': '整理访谈记录和需求信息'}
        ],
        # 第3周 - 需求分析
        [
            {'name': '需求分类整理', 'description': '对收集的需求进行分类整理和优先级排序'},
            {'name': '功能需求分析', 'description': '分析系统的功能需求和业务规则'},
            {'name': '非功能需求分析', 'description': '分析系统的性能、安全性、可用性等非功能需求'},
            {'name': '需求评审准备', 'description': '准备需求评审会议的材料'}
        ],
        # 第4周 - 需求确认
        [
            {'name': '需求评审会议', 'description': '组织需求评审会议，与各方确认需求的完整性和准确性'},
            {'name': '需求变更处理', 'description': '处理评审中提出的需求变更和补充'},
            {'name': '需求规格说明书初稿', 'description': '编写需求规格说明书初稿'},
            {'name': '原型设计准备', 'description': '准备系统原型设计的工具和方法'}
        ],
        # 第5周 - 系统设计开始
        [
            {'name': '系统架构设计', 'description': '设计系统的总体架构，包括技术架构和应用架构'},
            {'name': '数据库设计', 'description': '设计系统的数据库结构和数据模型'},
            {'name': '界面原型设计', 'description': '设计系统的界面原型和交互流程'},
            {'name': '接口设计', 'description': '设计系统的内部接口和外部接口'}
        ],
        # 第6周 - 系统设计深入
        [
            {'name': '详细设计', 'description': '进行系统的详细设计，包括模块设计和功能设计'},
            {'name': '安全设计', 'description': '设计系统的安全机制和权限控制'},
            {'name': '性能设计', 'description': '设计系统的性能优化方案'},
            {'name': '设计文档编写', 'description': '编写系统设计文档'}
        ],
        # 第7周 - 设计评审与完善
        [
            {'name': '设计评审', 'description': '组织设计评审会议，评审系统设计的合理性和可行性'},
            {'name': '设计优化', 'description': '根据评审意见优化系统设计'},
            {'name': '技术方案确定', 'description': '确定系统技术方案和开发框架'},
            {'name': '开发计划制定', 'description': '制定详细的开发计划和任务分解'}
        ],
        # 第8周 - 开发准备
        [
            {'name': '开发环境搭建', 'description': '搭建开发环境和测试环境'},
            {'name': '代码规范制定', 'description': '制定代码规范和开发规范'},
            {'name': '版本控制策略', 'description': '确定版本控制策略和分支管理方法'},
            {'name': '开发培训', 'description': '对开发团队进行技术培训和业务培训'}
        ]
    ]
    
    while current_date <= END_DATE:
        week_counter += 1
        
        # 确定当前周属于哪个阶段和周数
        if week_counter <= requirement_analysis_weeks:
            # 前两个月是需求调研和文档编写
            stage_name = "需求调研与文档编写"
            features = requirement_analysis_content[week_counter - 1]
        else:
            # 从功能内容文件中获取对应的阶段和周数
            actual_week = week_counter - requirement_analysis_weeks
            stage_found = False
            
            for stage_title, weeks in all_stages.items():
                if actual_week in weeks:
                    stage_name = stage_title
                    features = weeks[actual_week]
                    stage_found = True
                    break
            
            if not stage_found:
                # 如果没有找到对应的周，使用默认内容
                stage_name = "项目收尾阶段"
                features = [
                    {'name': '系统测试', 'description': '进行系统集成测试和验收测试'},
                    {'name': '文档完善', 'description': '完善系统文档和用户手册'},
                    {'name': '用户培训', 'description': '对用户进行系统使用培训'}
                ]
        
        # 生成周报
        report_path = generate_weekly_report(current_date, week_counter, stage_name, features)
        generated_reports.append(report_path)
        
        # 移动到下一周
        current_date += datetime.timedelta(days=7)
    
    print(f"成功生成 {len(generated_reports)} 份周报，保存在 {OUTPUT_DIR} 目录下。")
    for report in generated_reports:
        print(f"- {os.path.basename(report)}")

if __name__ == "__main__":
    main()

# 主函数
def main():
    global all_stages
    # 解析功能内容文件
    all_stages = parse_content_file(INPUT_FILE)
    
    # 生成周报
    current_date = START_DATE
    week_counter = 0
    generated_reports = []
    
    # 添加前两个月的需求调研和文档编写
    requirement_analysis_weeks = 8  # 2个月，每月4周
    
    # 定义前两个月的详细工作内容
    requirement_analysis_content = [
        # 第1周 - 需求调研初期
        [
            {'name': '项目启动', 'description': '组织项目启动会议，明确项目目标、范围和时间节点'},
            {'name': '需求调研计划制定', 'description': '制定详细的需求调研计划，包括调研对象、方式和时间安排'},
            {'name': '用户访谈准备', 'description': '准备用户访谈提纲和相关材料'},
            {'name': '现有系统调研', 'description': '调研现有安全生产标准化信息管理系统的功能和架构'}
        ],
        # 第2周 - 需求调研深入
        [
            {'name': '用户访谈', 'description': '与市应急局、区（市）县应急局、评审单位、评审专家和企业等五类用户进行访谈'},
            {'name': '业务流程分析', 'description': '分析企业安全生产标准化评审的业务流程和关键节点'},
            {'name': '系统痛点收集', 'description': '收集现有系统的痛点和用户需求'},
            {'name': '需求初步整理', 'description': '整理访谈记录和需求信息'}
        ],
        # 第3周 - 需求分析
        [
            {'name': '需求分类整理', 'description': '对收集的需求进行分类整理和优先级排序'},
            {'name': '功能需求分析', 'description': '分析系统的功能需求和业务规则'},
            {'name': '非功能需求分析', 'description': '分析系统的性能、安全性、可用性等非功能需求'},
            {'name': '需求评审准备', 'description': '准备需求评审会议的材料'}
        ],
        # 第4周 - 需求确认
        [
            {'name': '需求评审会议', 'description': '组织需求评审会议，与各方确认需求的完整性和准确性'},
            {'name': '需求变更处理', 'description': '处理评审中提出的需求变更和补充'},
            {'name': '需求规格说明书初稿', 'description': '编写需求规格说明书初稿'},
            {'name': '原型设计准备', 'description': '准备系统原型设计的工具和方法'}
        ],
        # 第5周 - 系统设计开始
        [
            {'name': '系统架构设计', 'description': '设计系统的总体架构，包括技术架构和应用架构'},
            {'name': '数据库设计', 'description': '设计系统的数据库结构和数据模型'},
            {'name': '界面原型设计', 'description': '设计系统的界面原型和交互流程'},
            {'name': '接口设计', 'description': '设计系统的内部接口和外部接口'}
        ],
        # 第6周 - 系统设计深入
        [
            {'name': '详细设计', 'description': '进行系统的详细设计，包括模块设计和功能设计'},
            {'name': '安全设计', 'description': '设计系统的安全机制和权限控制'},
            {'name': '性能设计', 'description': '设计系统的性能优化方案'},
            {'name': '设计文档编写', 'description': '编写系统设计文档'}
        ],
        # 第7周 - 设计评审与完善
        [
            {'name': '设计评审', 'description': '组织设计评审会议，评审系统设计的合理性和可行性'},
            {'name': '设计优化', 'description': '根据评审意见优化系统设计'},
            {'name': '技术方案确定', 'description': '确定系统技术方案和开发框架'},
            {'name': '开发计划制定', 'description': '制定详细的开发计划和任务分解'}
        ],
        # 第8周 - 开发准备
        [
            {'name': '开发环境搭建', 'description': '搭建开发环境和测试环境'},
            {'name': '代码规范制定', 'description': '制定代码规范和开发规范'},
            {'name': '版本控制策略', 'description': '确定版本控制策略和分支管理方法'},
            {'name': '开发培训', 'description': '对开发团队进行技术培训和业务培训'}
        ]
    ]
    
    while current_date <= END_DATE:
        week_counter += 1
        
        # 确定当前周属于哪个阶段和周数
        if week_counter <= requirement_analysis_weeks:
            # 前两个月是需求调研和文档编写
            stage_name = "需求调研与文档编写"
            features = requirement_analysis_content[week_counter - 1]
        else:
            # 从功能内容文件中获取对应的阶段和周数
            actual_week = week_counter - requirement_analysis_weeks
            stage_found = False
            
            for stage_title, weeks in all_stages.items():
                if actual_week in weeks:
                    stage_name = stage_title
                    features = weeks[actual_week]
                    stage_found = True
                    break
            
            if not stage_found:
                # 如果没有找到对应的周，使用默认内容
                stage_name = "项目收尾阶段"
                features = [
                    {'name': '系统测试', 'description': '进行系统集成测试和验收测试'},
                    {'name': '文档完善', 'description': '完善系统文档和用户手册'},
                    {'name': '用户培训', 'description': '对用户进行系统使用培训'}
                ]
        
        # 生成周报
        report_path = generate_weekly_report(current_date, week_counter, stage_name, features)
        generated_reports.append(report_path)
        
        # 移动到下一周
        current_date += datetime.timedelta(days=7)
    
    print(f"成功生成 {len(generated_reports)} 份周报，保存在 {OUTPUT_DIR} 目录下。")
    for report in generated_reports:
        print(f"- {os.path.basename(report)}")

if __name__ == "__main__":
    main()
    
    # 添加本周工作内容
    content_cell = row_cells[1]
    p = content_cell.add_paragraph()
    p.alignment = WD_ALIGN_PARAGRAPH.LEFT
    p.paragraph_format.first_line_indent = Pt(24)
    run = p.add_run("一、本周项目开展的工作：")
    run.font.size = Pt(12)
    run.font.bold = True
    set_chinese_font(run)
    
    # 根据周数生成不同的工作描述
    work_descriptions = [
        "本周项目组按照计划推进各项工作，重点完成了需求分析和系统设计相关任务。",
        "本周项目组继续开展系统开发工作，完成了多个核心功能模块的实现。",
        "本周项目组进行了系统集成和测试工作，解决了多个关键技术问题。",
        "本周项目组完成了系统优化和性能调优，提升了系统整体稳定性和响应速度。",
        "本周项目组进行了系统部署准备和用户培训工作，为系统上线做好充分准备。",
        "本周项目组开展了系统验收准备工作，完成了相关文档的编写和整理。",
        "本周项目组进行了系统运维和支持工作，确保系统稳定运行。",
        "本周项目组进行了项目总结和经验分享，为后续项目积累了宝贵经验。"
    ]
    
    # 选择当前周的工作描述
    selected_description = work_descriptions[week_number % len(work_descriptions)]
    
    p = content_cell.add_paragraph()
    p.alignment = WD_ALIGN_PARAGRAPH.LEFT
    p.paragraph_format.first_line_indent = Pt(24)
    run = p.add_run(selected_description)
    run.font.size = Pt(12)
    set_chinese_font(run)
    
    # 添加具体工作项
    if week_features:
        for i, feature in enumerate(week_features, 1):
            p = content_cell.add_paragraph()
            p.alignment = WD_ALIGN_PARAGRAPH.LEFT
            p.paragraph_format.first_line_indent = Pt(24)
            
            # 根据周数和功能点索引生成不同的描述
            work_tasks = [
                f"完成了{feature['name']}的需求分析和初步设计。",
                f"进行了{feature['name']}的详细设计和开发准备。",
                f"开发了{feature['name']}的核心功能模块。",
                f"完成了{feature['name']}的功能测试和问题修复。",
                f"优化了{feature['name']}的性能和用户体验。"
            ]
            
            # 使用周数和功能点索引的组合来选择描述
            task_index = (week_number + i) % len(work_tasks)
            run = p.add_run(f"{i}. {work_tasks[task_index]}")
            run.font.size = Pt(12)
            set_chinese_font(run)
    
    # 添加其他工作内容 - 根据周数生成不同内容
    other_tasks = [
        ["完成了项目周报和相关文档的编写。", "参加了项目例会，汇报了工作进展。"],
        ["进行了代码评审和质量检查。", "更新了项目计划和任务分配。"],
        ["协调了团队资源，解决了开发过程中的阻碍问题。", "完善了开发规范和流程文档。"],
        ["与客户进行了需求沟通和确认。", "进行了内部技术培训和知识分享。"]
    ]
    
    # 选择当前周的其他任务集合
    selected_other_tasks = other_tasks[week_number % len(other_tasks)]
    
    start_index = 1 if not week_features else len(week_features) + 1
    
    for i, task in enumerate(selected_other_tasks, start_index):
        p = content_cell.add_paragraph()
        p.alignment = WD_ALIGN_PARAGRAPH.LEFT
        p.paragraph_format.first_line_indent = Pt(24)
        run = p.add_run(f"{i}. {task}")
        run.font.size = Pt(12)
        set_chinese_font(run)
    
    # 二、目前存在的问题 - 根据周数生成不同内容
    p = content_cell.add_paragraph()
    p.alignment = WD_ALIGN_PARAGRAPH.LEFT
    run = p.add_run("\n二、目前存在的问题：")
    run.font.size = Pt(12)
    run.font.bold = True
    set_chinese_font(run)
    
    # 不同周的问题列表
    problems_by_week = [
        ["项目沟通机制仍有待建立。", "项目组内部工作有待进一步明确。"],
        ["部分功能的技术实现方案需要进一步优化。", "系统性能在高并发场景下有待提升。"],
        ["用户界面的交互体验需要进一步改进。", "部分功能与现有系统的集成存在兼容性问题。"],
        ["测试环境与生产环境存在差异，影响测试结果的准确性。", "部分非功能需求的实现方案需要调整。"]
    ]
    
    # 选择本周的问题集合
    week_problems = problems_by_week[week_number % len(problems_by_week)]
    
    for k, problem in enumerate(week_problems, 1):
        p = content_cell.add_paragraph()
        p.alignment = WD_ALIGN_PARAGRAPH.LEFT
        p.paragraph_format.first_line_indent = Pt(24)
        run = p.add_run(f"{k}. {problem}")
        run.font.size = Pt(12)
        set_chinese_font(run)
    
    # 添加其他工作内容
    p = content_cell.add_paragraph()
    p.alignment = WD_ALIGN_PARAGRAPH.LEFT
    p.paragraph_format.first_line_indent = Pt(24)
    run = p.add_run(f"{len(features) + 1}. 组织召开项目会议，讨论项目组织构成，人员安排等工作。")
    run.font.size = Pt(12)
    set_chinese_font(run)
    
    p = content_cell.add_paragraph()
    p.alignment = WD_ALIGN_PARAGRAPH.LEFT
    p.paragraph_format.first_line_indent = Pt(24)
    run = p.add_run(f"{len(features) + 2}. 与项目主管、业代、监理沟通。")
    run.font.size = Pt(12)
    set_chinese_font(run)
    
    p = content_cell.add_paragraph()
    p.alignment = WD_ALIGN_PARAGRAPH.LEFT
    p.paragraph_format.first_line_indent = Pt(24)
    run = p.add_run(f"{len(features) + 3}. 编写项目实施方案。")
    run.font.size = Pt(12)
    set_chinese_font(run)
    

    
    # 下周工作要点行
    table.add_row()
    row_cells = table.rows[2].cells
    row_cells[0].text = "下\n周\n工\n作\n要\n点"
    row_cells[0].vertical_alignment = WD_ALIGN_VERTICAL.CENTER
    p = row_cells[0].paragraphs[0]
    p.alignment = WD_ALIGN_PARAGRAPH.CENTER
    for run in p.runs:
        set_chinese_font(run)
        run.font.size = Pt(12)
    
    # 添加下周工作要点
    content_cell = row_cells[1]
    p = content_cell.add_paragraph()
    p.alignment = WD_ALIGN_PARAGRAPH.LEFT
    p.paragraph_format.first_line_indent = Pt(24)
    run = p.add_run("三、下周工作要点：")
    run.font.size = Pt(12)
    run.font.bold = True
    set_chinese_font(run)
    
    # 获取下周的功能点（如果有）
    next_week_number = week_number + 1
    next_week_features = []
    
    # 尝试从当前阶段获取下周功能点
    for stage_title, weeks in all_stages.items():
        if next_week_number in weeks:
            next_week_features = weeks[next_week_number]
            break
    
    # 如果当前阶段没有下周的功能点，尝试从下一个阶段获取
    if not next_week_features:
        stages_list = list(all_stages.keys())
        current_stage_index = stages_list.index(stage_name)
        if current_stage_index + 1 < len(stages_list):
            next_stage = stages_list[current_stage_index + 1]
            if 1 in all_stages[next_stage]:
                next_week_features = all_stages[next_stage][1]
    
    # 添加下周工作要点 - 根据周数生成不同内容
    if next_week_features:
        for i, feature in enumerate(next_week_features, 1):
            p = content_cell.add_paragraph()
            p.alignment = WD_ALIGN_PARAGRAPH.LEFT
            p.paragraph_format.first_line_indent = Pt(24)
            
            # 根据周数和功能点索引生成不同的描述
            next_week_tasks = [
                f"开始{feature['name']}的需求分析和设计工作。",
                f"继续推进{feature['name']}的开发工作。",
                f"完成{feature['name']}的核心功能实现并进行测试。",
                f"优化{feature['name']}的性能和用户体验。",
                f"完成{feature['name']}的文档编写和用户培训准备。"
            ]
            
            # 使用下周数和功能点索引的组合来选择描述
            task_index = (next_week_number + i) % len(next_week_tasks)
            run = p.add_run(f"{i}. {next_week_tasks[task_index]}")
            run.font.size = Pt(12)
            set_chinese_font(run)
    else:
        # 如果没有找到下周的功能点，添加一些通用的工作要点
        general_tasks = [
            ["继续推进本周未完成的功能开发。", "组织项目组成员进行代码评审和功能测试。"],
            ["完善系统架构设计和技术方案。", "进行系统性能测试和优化。"],
            ["准备阶段性交付和演示。", "与客户沟通下一阶段的需求和计划。"],
            ["进行项目风险评估和应对措施制定。", "更新项目计划和资源分配。"]
        ]
        
        # 选择下周的通用任务集合
        selected_tasks = general_tasks[next_week_number % len(general_tasks)]
        
        for i, task in enumerate(selected_tasks, 1):
            p = content_cell.add_paragraph()
            p.alignment = WD_ALIGN_PARAGRAPH.LEFT
            p.paragraph_format.first_line_indent = Pt(24)
            run = p.add_run(f"{i}. {task}")
            run.font.size = Pt(12)
            set_chinese_font(run)
    
    # 添加其他通用工作要点 - 根据周数生成不同内容
    common_tasks = [
        ["整理项目文档，编写技术方案。", "召开项目例会，汇报进展情况。"],
        ["更新项目风险清单和应对措施。", "准备阶段性总结报告。"],
        ["协调各团队资源，确保项目顺利推进。", "进行内部技术培训和知识分享。"],
        ["与客户进行需求确认和变更管理。", "准备项目阶段性验收材料。"]
    ]
    
    # 选择下周的通用任务集合
    selected_common_tasks = common_tasks[next_week_number % len(common_tasks)]
    
    start_index = 1 if not next_week_features else len(next_week_features) + 1
    if not next_week_features:
        start_index = len(general_tasks[next_week_number % len(general_tasks)]) + 1
    
    for i, task in enumerate(selected_common_tasks, start_index):
        p = content_cell.add_paragraph()
        p.alignment = WD_ALIGN_PARAGRAPH.LEFT
        p.paragraph_format.first_line_indent = Pt(24)
        run = p.add_run(f"{i}. {task}")
        run.font.size = Pt(12)
        set_chinese_font(run)
    
    # 添加签名栏 - 承建单位行
    table.add_row()
    table.rows[3].height = Cm(1.25)  # 设置承建单位行高度
    row_cells = table.rows[3].cells
    
    # 承建单位内容 - 第一个单元格
    p = row_cells[0].add_paragraph()
    p.alignment = WD_ALIGN_PARAGRAPH.CENTER
    run = p.add_run("承建单位")
    run.font.size = Pt(12)
    set_chinese_font(run)
    
    # 承建单位内容 - 第二个单元格
    p = row_cells[1].add_paragraph()
    p.alignment = WD_ALIGN_PARAGRAPH.CENTER
    run = p.add_run("成都市城市安全与应急管理研究院")
    run.font.size = Pt(12)
    set_chinese_font(run)
    
    # 添加项目经理和日期行
    table.add_row()
    table.rows[4].height = Cm(1.25)  # 设置项目经理和日期行高度
    row_cells = table.rows[4].cells
    
    # 项目经理标题
    p = row_cells[0].add_paragraph()
    p.alignment = WD_ALIGN_PARAGRAPH.CENTER
    run = p.add_run("项目经理")
    run.font.size = Pt(12)
    set_chinese_font(run)
    
    # 项目经理姓名
    p = row_cells[1].add_paragraph()
    p.alignment = WD_ALIGN_PARAGRAPH.CENTER
    run = p.add_run("陈倩")
    run.font.size = Pt(12)
    set_chinese_font(run)
    
    # 日期标题
    p = row_cells[2].add_paragraph()
    p.alignment = WD_ALIGN_PARAGRAPH.CENTER
    run = p.add_run("日期")
    run.font.size = Pt(12)
    set_chinese_font(run)
    
    # 日期内容
    p = row_cells[3].add_paragraph()
    p.alignment = WD_ALIGN_PARAGRAPH.CENTER
    run = p.add_run(f"{week_end_date.year}.{week_end_date.month}.{week_end_date.day}")
    run.font.size = Pt(12)
    set_chinese_font(run)
    
    # 保存文档
    report_filename = f"周报_{week_start_date.strftime('%Y%m%d')}-{week_end_date.strftime('%Y%m%d')}.docx"
    report_path = os.path.join(OUTPUT_DIR, report_filename)
    doc.save(report_path)
    # 返回生成的报告文件路径
    # 返回生成的报告文件路径
    # return report_path

# 主函数
def main():
    global all_stages
    # 解析功能内容文件
    all_stages = parse_content_file(INPUT_FILE)
    
    # 生成周报
    current_date = START_DATE
    week_counter = 0
    generated_reports = []
    
    # 添加前两个月的需求调研和文档编写
    requirement_analysis_weeks = 8  # 2个月，每月4周
    
    # 定义前两个月的详细工作内容
    requirement_analysis_content = [
        # 第1周 - 需求调研初期
        [
            {'name': '项目启动', 'description': '组织项目启动会议，明确项目目标、范围和时间节点'},
            {'name': '需求调研计划制定', 'description': '制定详细的需求调研计划，包括调研对象、方式和时间安排'},
            {'name': '用户访谈准备', 'description': '准备用户访谈提纲和相关材料'},
            {'name': '现有系统调研', 'description': '调研现有安全生产标准化信息管理系统的功能和架构'}
        ],
        # 第2周 - 需求调研深入
        [
            {'name': '用户访谈', 'description': '与市应急局、区（市）县应急局、评审单位、评审专家和企业等五类用户进行访谈'},
            {'name': '业务流程分析', 'description': '分析企业安全生产标准化评审的业务流程和关键节点'},
            {'name': '系统痛点收集', 'description': '收集现有系统的痛点和用户需求'},
            {'name': '需求初步整理', 'description': '整理访谈记录和需求信息'}
        ],
        # 第3周 - 需求分析
        [
            {'name': '需求分类整理', 'description': '对收集的需求进行分类整理和优先级排序'},
            {'name': '功能需求分析', 'description': '分析系统的功能需求和业务规则'},
            {'name': '非功能需求分析', 'description': '分析系统的性能、安全性、可用性等非功能需求'},
            {'name': '需求评审准备', 'description': '准备需求评审会议的材料'}
        ],
        # 第4周 - 需求确认
        [
            {'name': '需求评审会议', 'description': '组织需求评审会议，与各方确认需求的完整性和准确性'},
            {'name': '需求变更处理', 'description': '处理评审中提出的需求变更和补充'},
            {'name': '需求规格说明书初稿', 'description': '编写需求规格说明书初稿'},
            {'name': '原型设计准备', 'description': '准备系统原型设计的工具和方法'}
        ],
        # 第5周 - 系统设计开始
        [
            {'name': '系统架构设计', 'description': '设计系统的总体架构，包括技术架构和应用架构'},
            {'name': '数据库设计', 'description': '设计系统的数据库结构和数据模型'},
            {'name': '界面原型设计', 'description': '设计系统的界面原型和交互流程'},
            {'name': '接口设计', 'description': '设计系统的内部接口和外部接口'}
        ],
        # 第6周 - 系统设计深入
        [
            {'name': '详细设计', 'description': '进行系统的详细设计，包括模块设计和功能设计'},
            {'name': '安全设计', 'description': '设计系统的安全机制和权限控制'},
            {'name': '性能设计', 'description': '设计系统的性能优化方案'},
            {'name': '设计文档编写', 'description': '编写系统设计文档'}
        ],
        # 第7周 - 设计评审与完善
        [
            {'name': '设计评审', 'description': '组织设计评审会议，评审系统设计的合理性和可行性'},
            {'name': '设计优化', 'description': '根据评审意见优化系统设计'},
            {'name': '技术方案确定', 'description': '确定系统技术方案和开发框架'},
            {'name': '开发计划制定', 'description': '制定详细的开发计划和任务分解'}
        ],
        # 第8周 - 开发准备
        [
            {'name': '开发环境搭建', 'description': '搭建开发环境和测试环境'},
            {'name': '代码规范制定', 'description': '制定代码规范和开发规范'},
            {'name': '版本控制策略', 'description': '确定版本控制策略和分支管理方法'},
            {'name': '开发培训', 'description': '对开发团队进行技术培训和业务培训'}
        ]
    ]
    
    while current_date <= END_DATE:
        week_counter += 1
        
        # 确定当前周属于哪个阶段和周数
        if week_counter <= requirement_analysis_weeks:
            # 前两个月是需求调研和文档编写
            stage_name = "需求调研与文档编写"
            features = requirement_analysis_content[week_counter - 1]
        else:
            # 从功能内容文件中获取对应的阶段和周数
            actual_week = week_counter - requirement_analysis_weeks
            stage_found = False
            
            for stage_title, weeks in all_stages.items():
                if actual_week in weeks:
                    stage_name = stage_title
                    features = weeks[actual_week]
                    stage_found = True
                    break
            
            if not stage_found:
                # 如果没有找到对应的周，使用默认内容
                stage_name = "项目收尾阶段"
                features = [
                    {'name': '系统测试', 'description': '进行系统集成测试和验收测试'},
                    {'name': '文档完善', 'description': '完善系统文档和用户手册'},
                    {'name': '用户培训', 'description': '对用户进行系统使用培训'}
                ]
        
        # 生成周报
        report_path = generate_weekly_report(current_date, week_counter, stage_name, features)
        generated_reports.append(report_path)
        
        # 移动到下一周
        current_date += datetime.timedelta(days=7)
    
    print(f"成功生成 {len(generated_reports)} 份周报，保存在 {OUTPUT_DIR} 目录下。")
    for report in generated_reports:
        print(f"- {os.path.basename(report)}")

if __name__ == "__main__":
    main()