<?php

namespace app\company\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use app\company\model\CompanyModel;
use app\company\model\SeModel;
use app\validate\SeVerify as Verify;
use app\model\SettingModel;
use hg\apidoc\annotation as Apidoc;
use think\facade\View;
use think\Request;
use think\facade\Db;
use app\model\FileModel;
use PhpOffice\PhpWord\TemplateProcessor;

/**
 * @Apidoc\Title("自评管理")
 * @Apidoc\Group("Index")
 * @Apidoc\Sort(1)
 */
class Se extends Base {

    /**
     * @Apidoc\Title("自评列表")
     * @Apidoc\Desc("自评列表")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function index() {
        /*$re = SettingModel::setReport($_SESSION['company']['id']);
        dd($re);*/
        if (request()->isAjax()) {
            $where = [];
            $where[] = ['company_id','=',$_SESSION['company']['id']];
//            echo Db::table('cay_company_review_content_list')->where(['element_id'=>1])->fetchSql()->count('id');die;
            $result = Db::table('top_company_evaluate')->where($where)->order('id desc')->select()->each(function ($item) {
                $item['score'] = json_decode($item['score'],true);
                $f = FileModel::getFile(0,$item['files'],'');
                $item['files'] = [
                    'id' => $f['id'],
                    'name' => $f['name'],
                    'url' => $f['url'],
                ];
                return $item;
            });
            result($result);
        } else {
            $company = Db::table('top_company_info')->where(['id'=>$_SESSION['company']['id']])->find();
            $company = CompanyModel::codeToText($company);
            View::assign('company', $company);
            View::assign('title', '首页');
            return view();
        }
    }

    public function apply($id=0) {
        /*$re = SettingModel::setReport($_SESSION['company']['id']);
        dd($re);*/
        if (request()->isAjax()) {
            if($id){
                $re = Db::table('top_company_evaluate')->where(['id'=>$id,'company_id'=>$_SESSION['company']['id']])->find();
            }else{
                $re = Db::table('top_company_evaluate')->where(['company_id'=>$_SESSION['company']['id']])->order('id desc')->find();
            }
//            dd($re['personnels']);
            $company = Db::table('top_company_info')->where(['id'=>$_SESSION['company']['id']])->find();
            $company = CompanyModel::codeToText($company);
            $result = [
                'company_name' => $company['name'],
                'residence' => $company['mb_operate_address'],
                'government' => $re['government']??'',
                'legal' => $company['legal'],
                'legal_mobile' => $company['legal_mobile'],
                'legal_fax' => $company['fax'],
                'year' => $re['year']??date('Y'),
                'contacts' => $re['contacts']??$company['manager'],
                'contacts_tel' => $re['contacts_tel']??$company['manager_mobile'],
                'contacts_fax' => $re['contacts_fax']??$company['fax'],
                'contacts_mobile' => $re['contacts_mobile']??$company['manager_mobile'],
                'contacts_email' => $re['contacts_email']??$company['manager_email'],
                'business_type' => $re['business_type']??$company['business_type'],
                'old_level' => $re['old_level']??'无',
                'group_name' => $re['group_name']??$company['group_name'],
                'personnels' => empty($re['personnels'])?[['name'=>'','deptname'=>'','mobile'=>'','remark'=>''],['name'=>'','deptname'=>'','mobile'=>'','remark'=>''],['name'=>'','deptname'=>'','mobile'=>'','remark'=>''],['name'=>'','deptname'=>'','mobile'=>'','remark'=>''],['name'=>'','deptname'=>'','mobile'=>'','remark'=>''],['name'=>'','deptname'=>'','mobile'=>'','remark'=>'']]:json_decode($re['personnels'],true),
            ];
            result($result);
        } else {
            View::assign('id', $id);
            View::assign('title', '首页');
            return view();
        }
    }

    /*public function score($id=0)
    {
        if (request()->isAjax()) {
//            $expert = Db::table('top_org_tasks_experts')->where(['tasks_id'=>$id,'expert_id'=>$_SESSION['expert']['id']])->find();
            $expert = Db::table('top_org_tasks_experts')->where(['id'=>$id])->find();
            $task = Db::table('top_org_tasks')->where(['id'=>$expert['tasks_id']])->find();
            $elements = Db::table('top_org_tasks_element')->where(['tasks_id'=>$expert['tasks_id'],'expert_id'=>$expert['expert_id']])->select()->toArray();
            $company = Db::table('top_company_info')->where(['id'=>$task['company_id']])->find();
            foreach ($elements as $k=>$v){
                $elements[$k]['id'] = $v['element_id'];
                $elements[$k]['name'] = $v['element_name'];
                $elements[$k]['children'] = self::getElements($v['element_id'],$expert['tasks_id'],$company['review_id']);
            }
            $result['content'] = $elements;
            result($result);
        } else {
            View::assign('id', $id);
            View::assign('title', '首页');
            return view();
        }
    }*/

    public function score($id=0) {
        if (request()->isAjax()) {
            $company = Db::table('top_company_info')->where(['id'=>$_SESSION['company']['id']])->find();
            $elements = Db::table('top_company_review_element')->where(['main_id'=>$company['review_id'],'pid'=>0])->select()->toArray();
            foreach ($elements as $k=>$v){
                $elements[$k]['id'] = $v['id'];
                $elements[$k]['name'] = $v['name'];
                $elements[$k]['children'] = self::getElements($v['id'],$id,$company['review_id']);
            }
            $result['content'] = $elements;
            result($result);
        } else {
            View::assign('id', $id);
            View::assign('title', '首页');
            return view();
        }
    }

    public static function getElements($pid,$id,$review_id){
        $list = Db::table('top_company_review_content_list')->where([['review_id','=',$review_id]])->order('id')->select()->toArray();
        $tmp = [];
        foreach ($list as $k1=>$v1){
            $files = explode(',',$v1['sub_files']);
            $v1['sub_files'] = $files;
            $v1['mb_sub_files'] = [];
            $v1['edit'] = false;
            foreach ($files as $v2){
                if(!empty($v2)){
                    $v1['mb_sub_files'][] = FileModel::getFile(0,$v2,'');
                }
            }
            $tmp[$v1['content_id']][] = $v1;
        }
        $content = Db::table('top_company_review_content')->alias('a')
            ->leftJoin('top_company_review_content_score b',"a.id=b.content_id and b.evaluate_id = {$id}")
            ->where([['a.element_ids','like',','.$pid.',%']])
            ->order('a.sort,a.id')->field('a.id,a.ask,a.content,a.standards,a.score scores,a.cycle,a.method,a.element_id,a.element_ids,b.score,b.reform,b.miss,b.resion,b.summary,b.deduct')
            ->select()->each(function ($item) use ($tmp){
                $item['list'] = $tmp[$item['id']];
                $item['is_reform'] = $item['is_reform']==1;
                $item['have_reform'] = mb_strpos($item['method'], '★') !== false?1:0;
                $item['is_content'] = true;
                $item['score'] = is_numeric($item['score'])?$item['score']:'';
                $item['deduct'] = is_numeric($item['deduct'])?$item['deduct']:'';
                $item['miss'] = is_numeric($item['miss'])?$item['miss']:'';
                $item['name'] = $item['ask'];
                $files = empty($item['files'])?[]:explode(',',$item['files']);
                $item['files'] = [];
                foreach ($files as $v){
                    $f = FileModel::getFile('',$v,'');
                    $item['files'][] = [
                        'id' => $f['id'],
                        'code' => $f['code'],
                        'name' => $f['name'],
                        'url' => $f['url'],
                    ];
                }
                $deduct_material = empty($item['deduct_material'])?[]:explode(',',$item['deduct_material']);
                $item['deduct_material'] = [];
                foreach ($deduct_material as $v){
                    $f = FileModel::getFile('',$v,'');
                    $item['deduct_material'][] = [
                        'url' => $f['url'],
                        'code' => $f['code'],
                    ];
                }
                $item['reform'] = empty($item['reform'])?[0=>'']:explode(',',$item['reform']);
                return $item;
            })->toArray();
        return $content;
    }



    public function summarize($id=0) {
        if (request()->isAjax()) {
            $result = Db::table('top_company_evaluate')->where(['id'=>$id,'company_id'=>$_SESSION['company']['id']])->find();
            $result['score'] = empty($result['score'])?[]:json_decode($result['score'],true);
            result($result);
        } else {
            View::assign('id', $id);
            View::assign('title', '首页');
            return view();
        }
    }

    public function seSave($id=0) {
        $request = $this->request->post();
        $id = SeModel::seSave($request,$id);
        if($id>0){
            result(['id'=>$id],0,'保存成功');
        }else{
            result('',7001,$id);
        }
    }

    public function scoreSave($id=0) {
        $request = $this->request->post();
        $id = SeModel::scoreSave($request,$id);
        if($id>0){
            result(['id'=>$id],0,'保存成功');
        }else{
            result('',7001,$id);
        }
    }

    public function endSave($id=0) {
        $request = $this->request->post();
        if($request['status']==1){
            Verify::userCheck('end',$request);
        }
        $id = SeModel::endSave($request,$id);
        if($id>0){
            result(['id'=>$id],0,'保存成功');
        }else{
            result('',7001,$id);
        }
    }

    public function down($id=0) {
        $result = Db::table('top_company_evaluate')->where([['id','=',$id],['company_id','=',$_SESSION['company']['id']]])->find();
        if(empty($result)||$result['status']!=7){
            result('',1003,'自评未完成');
        }
        $score = json_decode($result['score'],true);
//        dd($score);
        $template = new TemplateProcessor(root_path() . 'public/word/se.docx');
        $template->setValue("company_name", $result['company_name']);
        $template->setValue("industry", $result['industry']);
        $template->setValue("specialty", $result['specialty']);
        $template->setValue("scorea", $score['total']['score']);
        $template->setValue("level", $result['level']);
        $template->setValue("year", date('Y',strtotime($result['date'])));
        $template->setValue("month", date('m',strtotime($result['date'])));
        $template->setValue("day", date('d',strtotime($result['date'])));
        $template->setValue("residence", $result['residence']);
        $template->setValue("type", $result['business_type']);
        $template->setValue("government", $result['government']);
        $template->setValue("legal", $result['legal']);
        $template->setValue("legal_mobile", $result['legal_mobile']);
        $template->setValue("legal_fax", $result['legal_fax']);
        $template->setValue("contacts", $result['contacts']);
        $template->setValue("contacts_tel", $result['contacts_tel']);
        $template->setValue("contacts_fax", $result['contacts_fax']);
        $template->setValue("contacts_mobile", $result['contacts_mobile']);
        $template->setValue("contacts_email", $result['contacts_email']);
        $template->setValue("group_name", $result['group_name']);
        $template->setValue("overview", $result['overview']);
        $template->setValue("accident", $result['accident']);
        $template->setValue("question", $result['question']);
        $template->setValue("conclusion", $result['conclusion']);
        $i = 1;
        $personnels = json_decode($result['personnels'],true);
        $template->cloneRow("name3", count($personnels)-2);
        foreach ($personnels as $k=>$v){
            $template->setValue("name".$i, $v['name']);
            $template->setValue("deptname".$i, $v['deptname']);
            $template->setValue("mobile".$i, $v['mobile']);
            $template->setValue("remark".$i, $v['remark']);
            $i++;
            if($i>3){
                $template->setValue("name3#".($i-2), $v['name']);
                $template->setValue("deptname3#".($i-2), $v['deptname']);
                $template->setValue("mobile3#".($i-2), $v['mobile']);
                $template->setValue("remark3#".($i-2), $v['remark']);
            }
        }
        $template->cloneRow("sort", count($score['score']));
        foreach ($score['score'] as $k => $v) {
            $template->setValue("sort#" . ($k + 1), $k+1);
            $template->setValue("name#" . ($k + 1), $v['name']);
            $template->setValue("sumscore#" . ($k + 1), $v['sumscore']);
            $template->setValue("score#" . ($k + 1), $v['score']);
            $template->setValue("deduct#" . ($k + 1), $v['deduct']);
            $template->setValue("miss#" . ($k + 1), $v['miss']);
            $template->setValue("summary#" . ($k + 1), $v['summary']);
        }
        //导出文件备份
        $copyPath = root_path() . "public/storage/tmp/" . time() . rand(1000, 9999) . '.docx';
        /*$template->saveAs($copyPath);
        header("Content-type: application/vnd.ms-word");
        header("Content-Disposition:attachment;filename=" . $result['company_name'].date('Y',strtotime($result['date'])) . '年度自评报告.docx');
        header('Cache-Control: max-age=0');
        readfile($copyPath);
        unlink($copyPath);
        exit;*/
        $pdfpath = root_path() . "public/storage/tmp/" . time() . rand(1000, 9999) . '.pdf';
        $template->saveAs($copyPath);
        header("Content-type: application/vnd.ms-word");
        header("Content-Disposition:attachment;filename=" . $result['company_name'] .date('Y',strtotime($result['date'])). '年度自评报告.docx');
        header('Cache-Control: max-age=0');
        FileModel::wordToPdf($copyPath,$pdfpath);
        header("Content-type: application/pdf");
        header("Content-Disposition:attachment;filename=" . $result['company_name'] .date('Y',strtotime($result['date'])). '年度自评报告.pdf');
        header('Cache-Control: max-age=0');
        readfile($pdfpath);
        unlink($copyPath);
        unlink($pdfpath);
        exit;
    }

    public function getConfig() {
        $result['standard'] = Db::table('top_standard_name')->where(['is_del'=>0])->field('id,name')->select()->toArray();
        result($result);
    }

    public function getInfo($id=0) {
        $company = Db::table('top_company_info')->where(['id'=>$_SESSION['company']['id']])->find();
        $re = Db::table('top_standard_apply')->where(['company_id'=>$_SESSION['company']['id']])->find();
        if(!empty($re)){
            $re = StandardModel::codeToText($re);
            $re['industry'] = $company['industry'].'/'.$company['specialty'];
        }else{
            $re = [
                'company_name' => $company['name'],
                'industry' => $company['industry'].'/'.$company['specialty'],
                'standard_id' => '',
                'level' => '',
                'is_advisory' => '',
                'advisory' => '',
            ];
        }
        result($re);
    }



}
