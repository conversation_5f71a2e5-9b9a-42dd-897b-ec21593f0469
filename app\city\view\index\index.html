<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>市应急局管理</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/city.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .el-badge__content.is-fixed { right: 0px;top:10px;z-index: 9;}
        .is-active { background-color:#3556B8 !important;}
        .el-menu-item i { color:#ffffff !important;}
        .el-submenu__title i  { color:#ffffff !important;}
        .el-form-item{margin-bottom: 0;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <el-container>
        <el-header style="text-align:center;font-size: 12px;height:50px;">
            <b style="font-size:16px;float:left">成都市企业安全生产标准化信息管理系统</b>
            <span>{$user.city_name}-{$user.dept_name}</span>
            <el-dropdown style="float:right">
                    <span><el-image
                            style="width: 30px; height: 30px;border-radius:30px;float:left;margin:10px;"
                            :src="head"
                            fit="cover"></el-image>
                        {$user.user_name}
                        <i class="el-icon-more" style="margin:0 15px;transform: rotate(90deg);color:#999;"></i></span>
                <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item @click.native="editpassword">修改密码</el-dropdown-item>
                    <el-dropdown-item @click.native="logout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
            </el-dropdown>
        </el-header>
        <el-container>
            <el-aside width="200px">
                <el-menu
                        default-active="index/main"
                        :style="{height:(height-60)+'px'}"
                        :height="height"
                        @select="open"
                        background-color="#436CE6"
                        text-color="#ffffff"
                        active-text-color="#ffffff">
                    <el-menu-item index="index/main">
                        <i class="el-icon-house"></i>
                        <span slot="title">首页</span>
                    </el-menu-item>

                    <el-menu-item index="company/index">
                        <i class="el-icon-office-building"></i>
                        <span slot="title">企业信息查询</span>
                    </el-menu-item>
                    <el-menu-item index="grading/index">
                        <i class="el-icon-discount"></i>
                        <span slot="title"><el-badge :value="count.num3" class="item">定级申请审核</el-badge></span>
                    </el-menu-item>
                    <!--                    <el-menu-item index="grading/appeal">-->
                    <!--                        <i class="el-icon-discount"></i>-->
                    <!--                        <span slot="title"><el-badge :value="count.num4" class="item">企业申诉审核</el-badge></span>-->
                    <!--                    </el-menu-item>-->
                    <el-menu-item index="grading/record">
                        <i class="el-icon-discount"></i>
                        <span slot="title">一、二级备案审批</span>
                    </el-menu-item>
                    <el-menu-item index="task/index">
                        <i class="el-icon-discount"></i>
                        <span slot="title">评审任务查看</span>
                    </el-menu-item>
                    <el-submenu>
                        <template slot="title">
                            <i class="el-icon-price-tag"></i>
                            <span>评审任务监督</span>
                        </template>
                        <el-menu-item index="task/video">
                            <i class="el-icon-discount"></i>
                            <span slot="title">现场评审监督</span>
                        </el-menu-item>
                        <el-menu-item index="index/gotoVideoUrl?url=Task">
                            <i class="el-icon-discount"></i>
                            <span slot="title">监督任务管理</span>
                        </el-menu-item>
                        <el-menu-item index="index/gotoVideoUrl?url=Logs">
                            <i class="el-icon-discount"></i>
                            <span slot="title">监督评价管理</span>
                        </el-menu-item>
                    </el-submenu>
                    <el-menu-item index="ca/publicty">
                        <i class="el-icon-tickets"></i>
                        <span slot="title">公示管理</span>
                    </el-menu-item>
                    <el-menu-item index="ca/index">
                        <i class="el-icon-tickets"></i>
                        <span slot="title">证书管理</span>
                    </el-menu-item>

                    <el-menu-item index="user/index" v-if="'{$user.role}'==1">
                        <i class="el-icon-user"></i>
                        <span slot="title">人员信息管理</span>
                    </el-menu-item>

                    <el-menu-item index="portals">
                        <i class="el-icon-office-building"></i>
                        <span slot="title">可视化大屏</span>
                    </el-menu-item>
                </el-menu>
            </el-aside>
            <el-main :style="{height:(height-50)+'px'}">
                <iframe id="iframe" :src="igrameSrc" width="100%" height="100%" border="0" frameborder="0" framespacing="0" marginheight="0" marginwidth="0"></iframe>
            </el-main>
        </el-container>
    </el-container>
    <el-dialog title="修改密码" width="500px" :visible.sync="visible">
        <el-form :model="form">
            <el-descriptions class="margin-top" title="" :column="1" size="small" border>
                <el-descriptions-item label="原密码">
                    <el-form-item label="">
                        <el-input type="password" v-model="form.old_password" ></el-input>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label="新密码">
                    <el-form-item label="">
                        <el-input type="password" v-model="form.new_password" ></el-input>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label="确认新密码">
                    <el-form-item label="">
                        <el-input type="password" v-model="form.confirm_password" ></el-input>
                    </el-form-item>
                </el-descriptions-item>
            </el-descriptions>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="visible = false">取消</el-button>
            <el-button type="primary" @click="onSubmit">保存</el-button>
        </div>
    </el-dialog>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                visible:false,
                title: '',
                head: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
                igrameSrc: '',
                count:{
                    num3:''
                },
                form:{
                    old_password:'',
                    new_password:'',
                    confirm_password:'',
                },
                loading: false,
                height: document.documentElement.clientHeight,
            };
        },
        components: {
        },
        methods: {
            open(key, keyPath) {
                if(key){
                    if(key=='portals'){
                        window.open('/general/toppingsoft/index.php/city/portals/index');
                    }else{
                        this.igrameSrc = '/general/toppingsoft/index.php/city/'+key;
                    }
                }
            },
            getMessage:function(){
                var _this = this;
                axios.post('getMessage', {}).then(function (res) {
                    if (res.data.code == 0) {
                        _this.count = res.data.data.count;
                    }
                }).catch(function (error) {
                    console.log("出现错误:",error);
                });
            },
            logout() {
                location.href = 'loginout';
            },
            editpassword:function (){
                this.form = {
                    old_password:'',
                    new_password:'',
                    confirm_password:'',
                };
                this.visible = true;
            },
            onSubmit:function (){
                var _this = this;
                var params = _this.form;
                _this.loading = true;
                axios.post('editPassword', params).then(function (res) {
                    if (res.data.code == 0) {
                        _this.visible = false;
                        _this.$alert('密码修改成功，请重新登陆', '提示', {
                            confirmButtonText: '确定',
                            callback: function action() {
                                _this.logout();
                            }
                        });
                    }else{
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                    }
                }).catch(function (error) {
                    console.log("出现错误:",error);
                });
            },
        },
        mounted() {
            this.getMessage();
            this.open('index/main');
            window.getMessage = this.getMessage;
        },
    })

    window.addEventListener("message", function (e) {
        if (e.data && e.data.redirectUrl) {
            window.location.href = e.data.redirectUrl;
        }
    });
</script>


</body>
</html>