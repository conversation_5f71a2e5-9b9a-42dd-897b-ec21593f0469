<?php

namespace app\api\model;

use think\facade\Cache;
use think\facade\Db;
use Medoo\Mdb;

class WxModel
{

    protected $APPID = "wx818733ea46d3bf93";//旧：wxd665f9a7bdafc2d3
    protected $APPSECRET = "23904500457079a18cdb6ea85d7ef543";//旧：efb0d7a7d519b2517ea4e0d493502ae9
    protected $domain = "http://172.42.136.164/platform/wchat";//旧：efb0d7a7d519b2517ea4e0d493502ae9

    public function login($code){
        $url = "{$this->domain}/sns/jscode2session?appid={$this->APPID}&secret={$this->APPSECRET}&js_code={$code}&grant_type=authorization_code";
        $res = http_get($url);
        $res = json_decode($res,true);
        return $res;
    }

    public function getuserphone($code){
        $token = $this->getToken();
        $url = "{$this->domain}/wxa/business/getuserphonenumber?access_token=$token";
        $res = http_post($url,json_encode(['code'=>$code]));
        $res = json_decode($res,true);
        return $res;
    }

    public function getToken(){
        $token = Cache::get('token_dev');
        if(empty($token)){
            $url = "{$this->domain}/cgi-bin/token?grant_type=client_credential&appid={$this->APPID}&secret={$this->APPSECRET}";
            $res = http_get($url);
            $data = json_decode($res,true);
            $token = $data['access_token'];
            Cache::set('token_dev',$token,$data['expires_in']);
        }
        return $token;
    }


}