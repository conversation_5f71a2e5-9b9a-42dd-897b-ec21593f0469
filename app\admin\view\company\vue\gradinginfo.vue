<style>
.my-label { width: 200px;}
.my-content { width: 450px;}
.margin-bottom { margin-bottom: 15px;}
.form-header { background-color: #E9F2F3; line-height: 25px; margin-bottom: 15px; padding: 5px 10px;}
.el-dialog__body { padding: 15px 20px;}
.el-tabs__content { overflow: auto;}
.el-descriptions .is-bordered td { width:20%;}
</style>
<template>
  <el-dialog :title="title" :close-on-click-modal="false"  modal="false" :visible.sync="visible" width="95%" top="10px" @close="refresh()" append-to-body="true" label-position="top">
    <el-steps :active="data.stepsActive" finish-status="finish" align-center>
      <el-step v-for="item in data.steps" :title="item.label" :status="item.status"></el-step>
    </el-steps>
    <el-tabs type="border-card">
      <el-tab-pane label="审批记录">
        <el-table border
                  v-loading="loading"
                  :data="data.prcs"
                  style="width: 100%;margin-bottom: 20px;"
                  ref="qtable"
                  :height="height"
                  size="small">
          <el-table-column
              type="index"
              label="序号"
              align="center"
              width="50">
          </el-table-column>
          <el-table-column
              prop="prcs_name"
              label="办理阶段"
              align="center"
              show-overflow-tooltip
              width="250">
          </el-table-column>
          <el-table-column
              prop="status_name"
              label="办理状态"
              align="center"
              width="100">
          </el-table-column>
          <el-table-column
              prop="end_time"
              label="办理时间"
              align="center"
              width="150">
          </el-table-column>
          <el-table-column
              prop="remark"
              label="备注"
              align="center"
              min-width="100">
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="申请详情" :style="{overflow:'auto',height: height+'px'}">
        <el-descriptions :column="3" border :labelStyle="{'width': '10%'}" :contentStyle="{'width': '15%'}">
          <el-descriptions-item label="企业名称">
            {{data.company_name}}
          </el-descriptions-item>
          <el-descriptions-item label="注册地址" :span="2">
            {{data.address}}
          </el-descriptions-item>
          <el-descriptions-item label="统一社会信用代码">
            {{data.company_code}}
          </el-descriptions-item>
          <el-descriptions-item label="法定代表人">
            {{data.legal}}
          </el-descriptions-item>
          <el-descriptions-item label="电话">
            {{data.legal_mobile}}
          </el-descriptions-item>
          <el-descriptions-item label="联系人">
            {{data.manager}}
          </el-descriptions-item>
          <el-descriptions-item label="电话">
            {{data.manager_mobile}}
          </el-descriptions-item>
          <el-descriptions-item label="电子邮箱">
            {{data.manager_email}}
          </el-descriptions-item>
          <el-descriptions-item label="申请等级">
            {{data.level}}
          </el-descriptions-item>
          <el-descriptions-item label="申请类型">
            {{data.type}}
          </el-descriptions-item>
          <el-descriptions-item label="创建性质">
            <div v-if="data.nature==0">自主创建</div>
            <div v-if="data.nature==1">“{{data.advisory}}”指导</div>
          </el-descriptions-item>
          <template v-for="item in data.files">
            <el-descriptions-item :label="item.title">
              <el-image
                  v-if="data[item.field+'Url']"
                  style="width: 100px; height: 100px"
                  :src="data[item.field+'Url']"
                  :preview-src-list="[data[item.field+'Url']]">
              </el-image>
            </el-descriptions-item>
          </template>
        </el-descriptions>
      </el-tab-pane>
    </el-tabs>
    <!--编辑联系人-->
  </el-dialog>
</template>
<script>
module.exports = {
  name: "add",
  // 模板导入区
  components: {
  },
  data:function() {
    return {
      id:0,
      isAdmin:false,
      visible: false,
      dialogFormVisible: false,
      title: '详情',
      loading: false,
      noMore: false,
      user_id: 0,
      data: {
        files:[],
      },
      is_see:0,
      ca:[],
      details:[],
      cards:{},
      type:{},
      pcas: [],
      restaurants:[],
      restaurants2:[],
      height: document.documentElement.clientHeight - 250,
    }
  },
  computed: {
    disabled () {
      return this.loading || this.noMore
    }
  },
  mounted: function(){
    //this.getConfig();
  },
  created:function(){
  },
  methods: {
    /**
     * 打开弹窗调用方法
     * */
    open: function (row) {
      var _this =this;
      _this.id = row.id;
      _this.visible = true;
      _this.getInfo(row.id);
    },
    closeDialog: function () {
      this.visible = false;
    },
    getInfo:function(id){
      var _this = this;
      if(id){
        _this.loading = true;
        axios.post("gradingInfo", {
          id:id
        }).then(function (res) {
          if (res.data.code == 0) {
            _this.data=res.data.data;
          }else {
            _this.$message({
              message: res.data.msg,
              type: res.data.type
            });
          }
          _this.loading = false;
        }).catch(function (error) {
          console.log("出现错误:",error);
        });
      }
    },
    addContacts: function(){
      this.form = {};
      this.dialogFormVisible = true;
    },
    editContacts: function(row){
      this.form = row;
      this.dialogFormVisible = true;
    },
  }
}
</script>


