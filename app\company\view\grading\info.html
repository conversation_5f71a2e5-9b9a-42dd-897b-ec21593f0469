<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>定级申请详情</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/company.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .el-divider.el-divider--horizontal { margin:10px 0;}
        .el-descriptions .is-bordered td { width:20%;}
        .mytable {border-collapse:collapse;width: 100%;}
        .mytable thead tr td {border: 1px solid #333;font-weight: 700; padding: 10px;background: #f5f7fa;text-align: center;}
        .mytable tbody tr th {border: 1px solid #333;font-weight: 700; padding: 10px;text-align: center;font-size: 16px;}
        .mytable tbody tr td {border: 1px solid #333; padding: 10px;text-align: center;font-size: 16px;font-family: 宋体;text-align: left;}
        .mytable .active td{ background: #f2f2f2;}
        .mytable tbody tr td p{line-height: 30px;}

        .demo-image__lazy{
            margin-top: 10px;
        }
        .el-image {
            position: relative;
            display: inline-block;
            overflow: hidden;
            width: 150px;
            height: 100px;
            border: 1px dashed #d9d9d9;
        }
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer" v-loading="loading">
        <el-page-header @back="goBack" content="申请详情"></el-page-header>
        <el-divider style="margin:10px 0;"></el-divider>
        <el-steps :active="data.stepsActive" finish-status="finish" align-center>
            <el-step v-for="item in data.steps" :title="item.label" :status="item.status"></el-step>
        </el-steps>
        <el-tabs type="border-card">
            <el-tab-pane label="审批记录" :style="{height:height+'px'}">
                <el-table border
                          v-loading="loading"
                          :data="data.prcs"
                          style="width: 100%;margin-bottom: 20px;"
                          ref="qtable"
                          :height="height"
                          size="small">
                    <el-table-column
                            type="index"
                            label="序号"
                            align="center"
                            width="50">
                    </el-table-column>
                    <el-table-column
                            prop="prcs_name"
                            label="办理阶段"
                            align="center"
                            show-overflow-tooltip
                            width="250">
                    </el-table-column>
                    <el-table-column
                            prop="status_name"
                            label="办理状态"
                            align="center"
                            width="100">
                    </el-table-column>
                    <el-table-column
                            prop="end_time"
                            label="办理时间"
                            align="center"
                            width="150">
                    </el-table-column>
                    <el-table-column
                            prop="files"
                            label="附件"
                            align="center"
                            min-width="100">
                        <template slot-scope="scope">
                            <p v-for="item in scope.row.files"><el-link @click="preview(item)" type="primary" v-html="item.name"></el-link></p>
                        </template>
                    </el-table-column>
                    <el-table-column
                            prop="remark"
                            label="备注"
                            align="center"
                            min-width="100">
                    </el-table-column>
                </el-table>
            </el-tab-pane>
            <el-tab-pane label="申请详情" :style="{overflow:'auto',height: height+'px'}">
                <div id="print-content">
                    <h3 style="height: 40px;line-height: 40px;text-align: center;">企业安全生产标准化定级申请表</h3>
                    <table class="mytable">
                        <tbody>
                        <tr>
                            <th style="width: 120px;"><label class="my-label">企&nbsp;业&nbsp;名&nbsp;称</label></th>
                            <td colspan="5" style="width: 700px;"><div class="my-online">{{data.company_name}}</div></td>
                        </tr>
                        <tr>
                            <th style="width: 120px;"><label class="my-label">住&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;所</label></th>
                            <td colspan="5" style="width: 700px;"><div class="my-online">{{data.address}}</div></td>
                        </tr>
                        <tr>
                            <th style="width: 120px;"><label class="my-label">统&nbsp;一&nbsp;社&nbsp;会<br/>信&nbsp;用&nbsp;代&nbsp;码</label></th>
                            <td colspan="5" style="width: 700px;"><div class="my-online">{{data.company_code}}</div></td>
                        </tr>
                        <tr>
                            <th style="width: 120px;"><label class="my-label">法定代表人</label></th>
                            <td style="width: 200px;"><div class="my-online">{{data.legal}}</div></td>
                            <th style="width: 120px;"><label class="my-label">电话</label></th>
                            <td colspan="3" style="width: 500px;"><div class="my-online">{{data.legal_mobile}}</div></td>
                        </tr>
                        <tr>
                            <th style="width: 120px;"><label class="my-label">联&nbsp;&nbsp;&nbsp;系&nbsp;&nbsp;&nbsp;人</label></th>
                            <td style="width: 200px;"><div class="my-online">{{data.manager}}</div></td>
                            <th style="width: 120px;"><label class="my-label">电话</label></th>
                            <td style="width: 200px;"><div class="my-online">{{data.manager_mobile}}</div></td>
                            <th style="width: 120px;"><label class="my-label">电子邮箱</label></th>
                            <td style="width: 200px;"><div class="my-online">{{data.manager_email}}</div></td>
                        </tr>
                        <tr>
                            <th style="width: 120px;"><label class="my-label">申&nbsp;请&nbsp;类&nbsp;型</label></th>
                            <td colspan="5" style="width: 700px;">
                                <template v-if="data.type=='初次申请'">
                                    <label>☑ 初次申请</label>
                                    <label style="margin-left: 20px;">□ 复评申请</label>
                                </template>
                                <template v-if="data.type=='复评申请'">
                                    <label>□ 初次申请</label>
                                    <label style="margin-left: 20px;">☑ 复评申请</label>
                                </template>
                            </td>
                        </tr>
                        <tr>
                            <th style="width: 120px;"><label class="my-label">创&nbsp;建&nbsp;性&nbsp;质</label></th>
                            <td colspan="5" style="width: 700px;">
                                <template v-if="data.nature==0">
                                    <label>☑ 自主创建</label>
                                    <label style="margin-left: 20px;">□ 第三方机构指导</label>
                                </template>
                                <template v-if="data.nature==1">
                                    <label>□ 自主创建</label>
                                    <label style="margin-left: 20px;">☑ 第三方机构指导</label>
                                    {{data.advisory}}
                                </template>
                            </td>
                        </tr>
                        <tr>
                            <th style="width: 120px;" colspan="6"><label class="my-label">附&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;件</label></th>
                        </tr>
                        <tr>
                            <td colspan="6" style="text-align: left;">
                                <div v-for="(item,key) in files" style="min-height:60px;">
                                    <p>{{key+1}}、{{item.title}}</p>
                                    <p v-for="file in data[item.field]">
                                        <el-link @click="preview(file)" type="primary">{{file.name}}</el-link>
                                    </p>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </el-tab-pane>
            <el-tab-pane label="整改详情" :style="{overflow:'auto',height: height+'px'}">
                <el-table
                        :data="tableData"
                        style="width: 100%">
                    <el-table-column
                            type="index"
                            label="序号"
                            align="center"
                            width="50">
                    </el-table-column>
                    <el-table-column
                            prop="reform"
                            label="整改内容"
                            min-width="180">
                    </el-table-column>
                    <el-table-column
                            prop="reform_date"
                            label="整改时间"
                            width="150">
                    </el-table-column>
                    <el-table-column
                            prop="name"
                            label="整改前情况"
                            min-width="380">
                        <template slot-scope="scope">
<!--                            <el-input v-model="scope.row.reform_before_info" disabled type="textarea" :autosize="{minRows: 2}"></el-input>-->
                            <div>{{scope.row.reform_before_info}}</div>
                            <div class="demo-image__lazy">
                                <el-image fit="fill" v-for="(item, index) in scope.row.reform_before_files" :preview-src-list="scope.row.reform_before_files_preview" :key="item.url" :src="item.url"></el-image>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                            prop="address"
                            label="整改后情况"
                            min-width="380">
                        <template slot-scope="scope">
                            <div>{{scope.row.reform_affter_info}}</div>
<!--                            <el-input v-model="scope.row.reform_affter_info" disabled type="textarea" :autosize="{minRows: 2}"></el-input>-->

                            <div class="demo-image__lazy">
                                <el-image fit="scale-down" v-for="(item, index) in scope.row.reform_affter_files" :preview-src-list="scope.row.reform_affter_files_preview" :key="item.url" :src="item.url"></el-image>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                            prop="name"
                            label="备注"
                            min-width="200">
                        <template slot-scope="scope">
                            <div>{{scope.row.remark}}</div>
<!--                            <el-input v-model="scope.row.remark" disabled type="textarea" :autosize="{minRows: 2}" placeholder="请输入备注"></el-input>-->
                        </template>
                    </el-table-column>
                </el-table>
            </el-tab-pane>
        </el-tabs>
    </div>
    <preview ref="preview"></preview>

    <el-dialog :visible.sync="dialogVisible">
        <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>

</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.all.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        // 模板导入区
        components: {
            'preview': 'url:/general/toppingsoft/public/vue/preview.vue',
        },
        data() {
            return {
                title: '新增',
                data: {},
                files: [],
                visible: false,
                loading: false,
                height: document.documentElement.clientHeight - 235,
                dialogImageUrl: '',
                dialogVisible: false,
                tableData: [],
                imageList: [],
                // 滚动容器选择器（用于懒加载）
                scrollContainer: '.image-list-container',
            };
        },
        computed: {
            // 预览大图列表（自动提取所有图片地址）
            previewList() {
                return this.imageList.map(item => item.url)
            }
        },
        methods: {
            handlePictureCardPreview(file) {
                this.dialogImageUrl = file.url;
                this.dialogVisible = true;
            },
            getInfo:function(){
                var _this = this;
                _this.loading = true;
                var param = {
                    _ajax:1,
                };
                axios.post('', param).then(function (res) {
                    _this.loading = false;
                    if (res.data.code == 0) {
                        _this.data = res.data.data;
                        _this.files=res.data.data.files;
                    }
                }).catch(function (error) {
                    _this.loading = false;
                    console.log("出现错误:",error);
                });
            },
            preview: function (file) {
                file = file.response?file.response.data:file;
                this.$refs.preview.open(file.url,file.name);
            },
            goBack() {
                // location.href = 'index';
                // window.history.go(-1);

                if (history.length > 2) {
                    // 存在可返回的历史记录
                    window.history.go(-1);
                } else {
                    // 无历史记录，阻止默认行为或提示用户
                    location.href = 'index';
                }

            },
            getReformInfo:function(){
                var _this = this;
                _this.loading = true;
                axios.post('getReformInfo', {grading_id: {$id}}).then(function (res) {
                    _this.loading = false;
                    if (res.data.code == 0) {
                        _this.tableData = res.data.data;
                    }
                }).catch(function (error) {
                    _this.loading = false;
                    console.log("出现错误:",error);
                });
            },
        },
        mounted() {
            this.getInfo();
            this.getReformInfo();
        }
    })
</script>


</body>
</html>