//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package kl.hsm.common;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;
import kl.hsm.pool.CryptoClientException;
import koal.org.apache.commons.lang3.StringUtils;

public class HsmConfig {
    private static volatile HsmConfig hsmConfig;
    private List<String> ips;
    private int port;
    private String strategy;
    private boolean ssl;
    private String keyStorePath;
    private String trustStorePath;
    private int poolMax;

    public static HsmConfig getHsmConfig() {
        return hsmConfig;
    }

    public String getKeyStorePath() {
        return this.keyStorePath;
    }

    public void setKeyStorePath(String keyStorePath) {
        this.keyStorePath = keyStorePath;
    }

    public String getTrustStorePath() {
        return this.trustStorePath;
    }

    public void setTrustStorePath(String trustStorePath) {
        this.trustStorePath = trustStorePath;
    }

    public void setSsl(boolean ssl) {
        this.ssl = ssl;
    }

    public boolean getSsl() {
        return this.ssl;
    }

    public String getStrategy() {
        return this.strategy;
    }

    public void setStrategy(String strategy) {
        this.strategy = strategy;
    }

    public List<String> getIps() {
        return this.ips;
    }

    public void setIps(List<String> ips) {
        this.ips = ips;
    }

    public int getPort() {
        return this.port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public int getPoolMax() {
        return this.poolMax;
    }

    public void setPoolMax(int poolMax) {
        this.poolMax = poolMax;
    }

    public static void resetHsmConfig() {
        hsmConfig = null;
    }

    private HsmConfig(String path) {
        Properties properties = new Properties();
        InputStream is = null;
        boolean isLoad = true;

        try {
            is = new FileInputStream(new File(path));
            properties.load(is);
        } catch (IOException var14) {
            isLoad = false;
        } finally {
            try {
                if (null != is) {
                    is.close();
                }
            } catch (IOException var13) {
                isLoad = false;
            }

        }

        if (!isLoad) {
            throw new CryptoClientException("config init error");
        } else {
            String ipList = properties.getProperty("hsm.ips");
            this.ips = Arrays.asList(ipList.split(";"));
            this.port = Integer.parseInt(properties.getProperty("hsm.port"));
            this.strategy = properties.getProperty("strategy");
            this.ssl = Boolean.parseBoolean(properties.getProperty("ssl"));
            this.keyStorePath = properties.getProperty("keyStore");
            if (StringUtils.isNotEmpty(this.keyStorePath)) {
                this.keyStorePath = this.keyStorePath.trim();
            }

            this.trustStorePath = properties.getProperty("trustStore");
            if (StringUtils.isNotEmpty(this.trustStorePath)) {
                this.trustStorePath = this.trustStorePath.trim();
            }

            String poolMax = properties.getProperty("poolMax");
            if (StringUtils.isNotEmpty(poolMax)) {
                this.poolMax = Integer.parseInt(poolMax);
            } else {
                this.poolMax = 16;
            }

        }
    }

    private HsmConfig(String ip, int port, boolean ssl, String keyStore, String trustStore, String strategy, int poolMax) {
        this.ips = Arrays.asList(ip.split(";"));
        this.port = port;
        this.strategy = strategy;
        this.ssl = ssl;
        this.keyStorePath = keyStore;
        this.trustStorePath = trustStore;
        this.poolMax = poolMax;
    }

    public static HsmConfig getInstance(String path) {
        if (null == hsmConfig) {
            Class var1 = HsmConfig.class;
            synchronized(HsmConfig.class) {
                if (null == hsmConfig) {
                    hsmConfig = new HsmConfig(path);
                }
            }
        }

        return hsmConfig;
    }

    public static HsmConfig getInstance(String ip, int port, boolean ssl, String keyStore, String trustStore, int poolMax) {
        if (null == hsmConfig) {
            Class var6 = HsmConfig.class;
            synchronized(HsmConfig.class) {
                if (null == hsmConfig) {
                    hsmConfig = new HsmConfig(ip, port, ssl, keyStore, trustStore, "poll", poolMax);
                }
            }
        }

        return hsmConfig;
    }
}
