<?php

namespace app\company\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use app\company\model\CompanyModel;
use app\controller\Sms;
use app\validate\CompanyVerify as Verify;
use app\model\SettingModel;
use app\model\FileModel;
use hg\apidoc\annotation as Apidoc;
use think\facade\View;
use think\Request;
use think\facade\Db;

/**
 * @Apidoc\Title("企业管理")
 * @Apidoc\Group("Index")
 * @Apidoc\Sort(1)
 */
class Index extends Base
{

    /**
     * @Apidoc\Title("企业首页")
     * @Apidoc\Desc("企业首页")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function index()
    {
        View::assign('title', '首页');
        if (empty($_SESSION['company']['id'])) {
            $company = Db::table('top_company_info')->where(['user_id' => $_SESSION['company']['user_id']])->field('id,name,status')->find();
            $_SESSION['company']['id'] = $company['id'];
            $_SESSION['company']['company_name'] = $company['name'];
        } else {
            $company = Db::table('top_company_info')->where(['id' => $_SESSION['company']['id']])->field('id,status')->find();
        }
        $url = empty($company) ? 'index/info' : 'index/main';
        View::assign('user', $_SESSION['company']);
        View::assign('url', $url);
        return view();
    }

    public function getMessage()
    {
        $message = Db::table('top_message')
            ->where(['user_type' => 'company', 'user_id' => $_SESSION['company']['user_id'], 'is_read' => 0])
            ->field('id,sms_type,sms_content,sms_time')
            ->select()->toArray();
        $data['message'] = $message;
        result($data);
    }

    public function getMessageInfo($id = '')
    {
        $data = Db::table('top_message')
            ->where(['user_type' => 'company', 'user_id' => $_SESSION['company']['user_id'], 'id' => $id])
            ->field('id,sms_type,sms_content,sms_time,sms_url')
            ->find();
        result($data);
    }

    public function loginout()
    {
        $_SESSION['company'] = [];
        return redirect('/general/toppingsoft/index.php/company/login/login');
    }


    /**
     * @Apidoc\Title("企业首页")
     * @Apidoc\Desc("企业首页")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function main()
    {
        $company = Db::table('top_company_info')->where(['id' => $_SESSION['company']['id']])->find();
        $company = CompanyModel::codeToText($company);
        $message = Db::table('top_message')
            ->where(['user_type' => 'company', 'user_id' => $_SESSION['company']['user_id']])
            ->order('sms_time desc,id desc')
            ->field('id,sms_type,sms_content,sms_time')
            ->page(1, 5)->select()->each(function ($item) {
                $item['sms_time'] = date('Y-m-d', strtotime($item['sms_time']));
                return $item;
            })->toArray();
        $result['message'] = $message;
        $result['notify'] = Db::table('top_notify')->alias('a')
            ->leftJoin('top_notify_type t', 'a.type = t.id')
            ->where(['a.is_del' => 0, 'a.is_show' => 1])
            ->order('date desc,create_time desc')
            ->field('a.*,t.name mb_type')
            ->page(1, 5)->select()->toArray();
        View::assign('company', $company);
        View::assign('result', $result);
        View::assign('title', '首页');
        return view();
    }

    public function review()
    {
        $where = [];
        $company = Db::table('top_company_info')->where(['id' => $_SESSION['company']['id']])->find();
        $where[] = ['main_id', '=', $company['review_id']];
//            $where[] = ['company_id','=',$company['id']];
        $where[] = ['pid', '=', 0];
//            echo Db::table('cay_company_review_content_list')->where(['element_id'=>1])->fetchSql()->count('id');die;
        $result['elementTitle'] = Db::table('top_company_review_element')->where($where)->field('id,name,main_id')->order('sort')->select()->each(function ($item) {
            $num = Db::table('top_company_review_content_list')->where([['element_ids', 'like', "%,{$item['id']},%"], ['is_sub', '=', '0']])->count('id');
            $item['num'] = empty($num) ? '' : $num;
            return $item;
        });
        result($result);
    }

    /**
     * unoconv -f pdf -o /opt/tdoa/webroot/general/toppingsoft/public/word/se.pdf /opt/tdoa/webroot/general/toppingsoft/public/word/se.docx
     * @Apidoc\Title("企业信息管理")
     * @Apidoc\Desc("企业信息管理")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function info()
    {
        //企业详细信息表
        $company = Db::table('top_company_info')->where(['id' => $_SESSION['company']['id']])->find();
        //企业资料认证申请表
        $company_apply = Db::table('top_company_info_apply')->where(['user_id' => $this->user['user_id'], 'status' => [1, 5]])->order('id desc')->find();

        if (empty($company) && empty($company_apply)) {
            return redirect('auth');
        }
        $company = CompanyModel::codeToText($company);
        $company_apply = CompanyModel::codeToText($company_apply);
        if (!empty($company)) {
            //获取粉尘,获取是否涉高温熔融金属,获取是否涉有限空间 - 认证信息
            \app\model\CompanyModel::getCompanyAuthInfo($company);
        }
        if (!empty($company_apply)) {
            //获取粉尘,获取是否涉高温熔融金属,获取是否涉有限空间 - 认证中的信息 - 公司参数待审核表
            \app\model\CompanyModel::getCompanyAuthInfo($company_apply,'top_company_param_apply');
        }
//        dd($company_apply);
        View::assign('company', $company);
        View::assign('company_apply', $company_apply);
        return view();
    }

    /**
     * @Apidoc\Title("企业资料认证")
     * @Apidoc\Desc("企业资料认证")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function auth($id = 0)
    {
        View::assign('id', $id);
        return view();
    }

    public function companySave($id = 0)
    {
        $request = $this->request->post();
        Verify::userCheck('save', $request);
        $id = CompanyModel::companySave($request, 0);
        if ($id > 0) {
            result(['id' => $id], 0, '提交成功');
        } else {
            result('', 7001, $id);
        }
    }

    public function upload($model = 'company')
    {
        $file = request()->file('file');
        $result = FileModel::upload($file, $model);
        result($result);
    }

    public function getConfig()
    {
        $result['pca'] = SettingModel::getPcasAll(1);
        $result['economyType'] = SettingModel::getEconomyType(1);
        $result['economySector'] = SettingModel::getEconomySector(1);
        $result['industry'] = SettingModel::getIndustry(1);
        $result['industrialPark'] = SettingModel::getIndustrialPark(1);
        $result['standard'] = Db::table('top_standard_name')->where(['is_del' => 0])->field('id,name')->select()->toArray();
        result($result);
    }

    public function getCompanyInfo($id = 0)
    {
        $re = Db::table('top_company_info')->where(['id' => $_SESSION['company']['id']])->find();
        $tabapply = 'top_company_param';
        if (!empty($id)) {
            $re = Db::table('top_company_info_apply')->where(['id' => $id])->find();
            $tabapply = 'top_company_param_apply';
        }
        if (!empty($re)) {
            $re = CompanyModel::codeToText($re);
            //获取粉尘 - 认证的信息
            $dustList_apply = Db::table($tabapply)->field("name,param_value")
                ->where(['company_id' => $re['id'], 'category' => 'dust'])->select()->toArray();
            if (is_array($dustList_apply) && count($dustList_apply)) {
                $re["dust_list"] = $dustList_apply;
            } else {
                $re["dust_list"] = [
                    [
                        'name' => '木粉尘',
                        'param_value' => '0',
                    ]
                ];
            }
            //获取是否涉高温熔融金属 - 认证的信息
            $hotList_apply = Db::table($tabapply)->field("name,param_value")
                ->where(['company_id' => $re['id'], 'category' => 'hot'])->select()->toArray();
            if (is_array($hotList_apply) && count($hotList_apply)) {
                $re["hot_list"] = $hotList_apply;
            } else {
                $re["hot_list"] = [
                    [
                        'name' => '＜0.5t',
                        'param_value' => '0'
                    ],
                    [
                        'name' => '0.5t≤X＜1t',
                        'param_value' => '0'
                    ],
                    [
                        'name' => '1t≤X＜3t',
                        'param_value' => '0'
                    ],
                    [
                        'name' => '3t≤X＜5t',
                        'param_value' => '0'
                    ],
                    [
                        'name' => '5t≤X＜10t',
                        'param_value' => '0'
                    ],
                    [
                        'name' => '10t≤X＜30t',
                        'param_value' => '0'
                    ],
                    [
                        'name' => '≥30t',
                        'param_value' => '0'
                    ]
                ];
            }
            //获取是否涉高温熔融金属 - 认证的信息
            $limitedList_apply = Db::table($tabapply)->field("name,param_value")
                ->where(['company_id' => $re['id'], 'category' => 'limited'])->select()->toArray();
            if (is_array($limitedList_apply) && count($limitedList_apply)) {
                $re["limited_list"] = $limitedList_apply;
            } else {
                $re["limited_list"] = [
                    [
                        'name' => '污水处理池',
                        'param_value' => '0'
                    ],
                ];
            }
            unset($re['id']);
            $re['industry'] = explode(',', $re['mb_industry']);
        } else {
            $re = [
                'name' => '',
                'reg_address' => [],
                'reg_address_info' => '',
                'license' => '',
                'licenseUrl' => '',
                'aoc' => '',
                'aocUrl' => '',
                'operate_address' => [],
                'operate_address_info' => '',
                'region' => [],
                'legal' => '',
                'legal_mobile' => '',
                'legal_email' => '',
                'fax' => '',
                'phone' => '',
                'industrial_park' => '',
                'postal_code' => '',
                'economy_sector' => [],
                'industry' => [],
                'license_number' => '',
                'license_start' => '',
                'license_end' => '',
                'economy_type' => [],
                'enterprise_size' => '',
                'reg_money' => '',
                'manager' => '',
                'manager_mobile' => '',
                'manager_email' => '',
                'date' => '',
                'fixed_asset' => '',
                'revenue' => '',
                'personnel' => '',
                'area' => '',
                'personnel_full' => '',
                'personnel_part' => '',
                'personnel_special' => '',
                'group_name' => '',
                'business' => '',
                'is_dust_explosion' => '否',
                'is_ammonia_cold' => '否',
                'is_hot_melting' => '否',
                'is_light_industry' => '否',
                'sector' => '',
                'ammonia_use' => '',
                'ammonia_storage_method' => '',
                'limited_space_type' => '',
                'ammonia_usage' => '',
                'ammonia_storage_capacity' => '',
                'gas_alarm_number' => '',
                'blast_furnace_number' => '',
                'nonferrous_furnace_number' => '',
                'ferroalloy_furnace_number' => '',
                'soaring_furnace_number' => '',
                'dust_list' => [
                    [
                        'name' => '木粉尘',
                        'param_value' => '0',
                    ]
                ],
                'hot_list' => [
                    [
                        'name' => '＜0.5t',
                        'param_value' => '0'
                    ],
                    [
                        'name' => '0.5t≤X＜1t',
                        'param_value' => '0'
                    ],
                    [
                        'name' => '1t≤X＜3t',
                        'param_value' => '0'
                    ],
                    [
                        'name' => '3t≤X＜5t',
                        'param_value' => '0'
                    ],
                    [
                        'name' => '5t≤X＜10t',
                        'param_value' => '0'
                    ],
                    [
                        'name' => '10t≤X＜30t',
                        'param_value' => '0'
                    ],
                    [
                        'name' => '≥30t',
                        'param_value' => '0'
                    ]
                ],
                'limited_list' => [
                    [
                        'name' => '污水处理池',
                        'param_value' => '0',
                    ]
                ],
            ];
        }

        result($re);
    }


    public function policyInfo($id = 0)
    {
        $where[] = ['is_del', '=', 0];
        $where[] = ['id', '=', $id];
        $res = Db::table('top_notify')
            ->where($where)
            ->find();
        if (empty($res)) {
            result('', 1002, '数据有误');
        }
        result($res);
    }

    public function messageInfo($id = 0)
    {
        $where[] = ['id', '=', $id];
        $res = Db::table('top_message')
            ->where($where)
            ->find();
        if (empty($res)) {
            result('', 1002, '数据有误');
        }
        $res['sms_time'] = date('Y-m-d H:i', strtotime($res['sms_time']));
        result($res);
    }

    /**
     * @Apidoc\Title("大数据公司信息")
     * @Apidoc\Desc("大数据公司信息")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("大数据")
     */
    public static function getBigDataCompanyInfo($name)
    {
        $out = CompanyModel::getBigDataCompanyInfo($name);
        result($out);
    }

    public function editPassword()
    {
        $old_password = request()->param('old_password');
        $new_password = request()->param('new_password');
        $confirm_password = request()->param('confirm_password');
        $user = Db::table('top_company_user')->where(['id' => $_SESSION['company']['user_id']])->find();
        if ($user['password'] !== crypt($old_password, $user['salt'])) {
            result('', 1003, '原密码错误');
        }
        $pattern = '/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$|^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$|^(?=.*[a-z])(?=.*[A-Z])(?=.*[\W_]).{8,}$|^(?=.*[a-z])(?=.*\d)(?=.*[\W_]).{8,}$|^(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$/';
        // 使用preg_match函数进行匹配
        if (!preg_match($pattern, $new_password)) {
            result('', 1001, '请填写6-18位密码，并且须由大写字母、小写字母、数字及特殊符号中的三种或三种以上进行组合');
        }
        if ($new_password !== $confirm_password) {
            result('', 1005, '两次密码不一致');
        }
        $data['salt'] = create_nonce_str(8);
        $data['password'] = crypt($new_password, $data['salt']);
        $re = Db::table('top_company_user')->where(['id' => $_SESSION['company']['user_id']])->update($data);
        if ($re) {
            result('', 0, '密码修改成功，请重新登陆');
        } else {
            result('', 1007, '修改失败');
        }
    }

    /**
     * 更新企业坐标
     *
     **/


    /**
     * @Apidoc\Title("获取个人信息")
     * @Apidoc\Desc("获取个人信息")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("个人信息")
     * @Apidoc\Returned("data", type="object", desc="用户信息")
     */
    public function getUserInfo()
    {
        $user = Db::table('top_company_user')->where(['id' => $_SESSION['company']['user_id']])->field('id,username,name,email,mobile,reg_time')->find();
        if ($user) {
            $user['reg_time'] = date('Y-m-d H:i:s', strtotime($user['reg_time']));
            result($user);
        } else {
            result('', 1002, '用户不存在');
        }
    }

    /**
     * @Apidoc\Title("修改个人信息-发送验证码")
     * @Apidoc\Desc("修改手机号时发送短信验证码")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("个人信息")
     * @Apidoc\Param("mobile", type="string", require=true, desc="新手机号")
     * @Apidoc\Returned("data", type="object", desc="发送结果")
     */
    public function sendUserInfoSms()
    {
        $mobile = request()->param('mobile');

        // 验证手机号格式
        if (empty($mobile) || preg_match('/^1[0-9]\d{10}$/', $mobile)) {
            result('', 1001, '请输入正确的手机号格式');
        }

        // 检查手机号是否被其他用户使用（排除当前用户）
        $existUser = Db::table('top_company_user')->where([
            ['mobile', '=', $mobile],
            ['id', '<>', $_SESSION['company']['user_id']]
        ])->find();
        if ($existUser) {
            result('', 1001, '该手机号已被其他用户使用');
        }

        // 生成6位随机验证码
        $code = create_nonce_str(6, '0123456789');
        $type = 4; // 用户信息修改类型

        // 发送短信
        $sms = new Sms();
        $content = '您正在修改登录手机号，验证码：' . $code . '，15分钟内有效。';
        $res = $sms->sendsms($mobile, $content);

        // 记录发送状态
        $sms->instate($type, $mobile, $code, $res);

        if ($res['code'] == '0') {
            result('', 0, '验证码发送成功');
        } else {
            result('', 2001, '短信发送失败，请检查手机号是否正确');
        }
    }

    /**
     * @Apidoc\Title("修改个人信息")
     * @Apidoc\Desc("修改个人信息")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("个人信息")
     * @Apidoc\Param("name", type="string", require=true, desc="姓名")
     * @Apidoc\Param("email", type="string", require=false, desc="邮箱")
     * @Apidoc\Param("mobile", type="string", require=true, desc="手机号")
     * @Apidoc\Param("sms_code", type="string", require=true, desc="短信验证码")
     * @Apidoc\Returned("data", type="object", desc="修改结果")
     */
    public function updateUserInfo()
    {
        $name = request()->param('name');
        $email = request()->param('email');
        $mobile = request()->param('mobile');
        $sms_code = request()->param('sms_code');

        // 验证手机号格式
        if (!empty($mobile) && !preg_match('/1[0-9]{10}$/', $mobile)) {
            result('', 1001, '手机号格式不正确');
        }

        // 验证邮箱格式（如果不为空）
        if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            result('', 1001, '邮箱格式不正确');
        }

        // 获取当前用户信息
        $currentUser = Db::table('top_company_user')->where(['id' => $_SESSION['company']['user_id']])->find();

        // 如果修改了手机号，需要验证短信验证码
        if (!empty($mobile) && $mobile !== $currentUser['mobile']) {
            if (empty($sms_code)) {
                result('', 1001, '修改手机号需要验证码');
            }

            // 验证短信验证码
            try {
                $sms = new \app\controller\Sms();
                $sms->checksms($mobile, $sms_code, 4); // 4是用户信息修改类型
            } catch (\Exception $e) {
                result('', 1001, '验证码验证失败');
            }

            // 检查手机号是否被其他用户使用（排除当前用户）
            $existUser = Db::table('top_company_user')->where([
                ['mobile', '=', $mobile],
                ['id', '<>', $_SESSION['company']['user_id']]
            ])->find();
            if ($existUser) {
                result('', 1001, '该手机号已被其他用户使用');
            }
        }

        // 更新用户信息
        $updateData = [];
        if (!empty($mobile)) {
            $updateData['mobile'] = $mobile;
            $updateData['username'] = $mobile;
        }
        if (!empty($email)) {
            $updateData['email'] = $email;
        }
        if (!empty($name)) {
            $updateData['name'] = $name;
        }

        if (!empty($updateData)) {
            $result = Db::table('top_company_user')->where(['id' => $_SESSION['company']['user_id']])->update($updateData);
            if ($result) {
                // 更新session中的用户名
                $_SESSION['company']['user_name'] = $name;
                result('', 0, '个人信息修改成功');
            } else {
                result('', 1007, '修改失败');
            }
        }
        result('', 0, '个人信息修改成功');
    }

    public function getCompanyLocationInfo()
    {
        $API_URL = 'http://10.1.235.89:2683/open-api/GjZdPkdxCoeyV9XsxAE9Fddglq7wF47S/WHPQYJBXX';
        $API_KEY = '878940';
        $API_SIGNATURE = '0f9d3be4e3daa5226532b7a5345ae3b1b10787f547c02db1717ed7d210cdbf2c';

        // 请求参数
        $params = [
            'limit' => 1000, // 每次获取的记录数
            'start' => 0
        ];

        $headers = [
            'X-Ca-Key: ' . $API_KEY,
            'X-Ca-Signature: ' . $API_SIGNATURE
        ];

        $url = $API_URL . '?' . http_build_query($params);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        $data = json_decode($response, true);
        $data = $data['data'];
        foreach ($data as $d) {
            $_update = [
                'jd' => $d['dbo.GXPT_WHPQYJBXX.JD'],
                'wd' => $d['dbo.GXPT_WHPQYJBXX.WD'],
            ];
            Db::table('top_company_info')->where(['license_number' => $d['dbo.GXPT_WHPQYJBXX.DM']])->update($_update);
        }
        echo "更新企业坐标完成!";
        exit;
    }
}
