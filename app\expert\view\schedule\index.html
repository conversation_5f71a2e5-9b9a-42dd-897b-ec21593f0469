﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>日程管理</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/company.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .el-badge__content.is-fixed { right: 20px;z-index: 9;}
        .is-active { background-color:#3556B8 !important;}
        .el-menu-item i { color:#ffffff;}
        .is-selected {color: #1989FA;}
        .el-calendar-table .el-calendar-day { padding:0;}
        .calendar-day {height:80%; padding:10px;}
        .calendar-day.selected { background:#f0f9eb; color:#67c23a;}
        .calendar-day.tasks { background:#fdf6ec; color:#e6a23c;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <el-container>
        <el-calendar>
            <!-- 这里使用的是 2.5 slot 语法，对于新项目请使用 2.6 slot 语法-->
            <template slot="dateCell" slot-scope="{date, data}">
                <!--自定义内容-->
                <div v-if="textContent(data.day).length==0&&taskContent(data.day).length==0" class="calendar-day" @click="addDate(data)">
                    <div style="text-align: center">
                        {{ data.day.split('-').slice(1).join('-') }}
                    </div>
                </div>
                <template v-for="(item, index) in taskContent(data.day)">
                    <div class="calendar-day tasks" @click.stop="taskinfo(data)">
                        <div style="text-align: center" @click.stop="taskinfo(data)">
                            {{ data.day.split('-').slice(1).join('-') }}
                        </div>
                        <div :key="index">
                            <div class="dutySchedule-content">
                                {{item.content}}
                            </div>
                        </div>
                    </div>
                </template>
                <template v-if="taskContent(data.day).length==0" v-for="(item, index) in textContent(data.day)">
                    <div class="calendar-day selected" @click.stop="delDate(data)">
                        <div style="text-align: center" @click.stop="delDate(data)">
                            {{ data.day.split('-').slice(1).join('-') }}
                        </div>
                        <div :key="index">
                            <div class="dutySchedule-content">
                                {{item.content}}
                            </div>
                        </div>
                    </div>
                </template>
            </template>
        </el-calendar>
    </el-container>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                title: '',
                loading: false,
                calendarData: [],
                taskData: [],
                height: document.documentElement.clientHeight,
            };
        },
        components: {
        },
        methods: {
            // 文字显示
            textContent(date) {
                return this.calendarData.filter(item => {
                    return date === item.date
                })
            },
            // 文字显示
            taskContent(date) {
                return this.taskData.filter(item => {
                    return date === item.date
                })
            },
            //数据加载
            getData() {
                var loading = this.$loading();
                var _this = this;
                var param = {};
                param._ajax = 1;
                axios.post('', param).then(function (res) {
                    if (res.data.code == 0) {
                        _this.calendarData = res.data.data.idle;
                        _this.taskData = res.data.data.tasks;
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                    loading.close();
                }).catch(function (error) {
                    console.log(error);
                });
            },
            addDate(data) {
                var _this = this;
                var param = {};
                param.date = data.day;
                var loading = this.$loading();
                axios.post('addDate', param).then(function (res) {
                    if (res.data.code == 0) {
                        _this.$message({
                            message: res.data.msg,
                            type: "success"
                        });
                        _this.getData();
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                    }
                    loading.close();
                }).catch(function (error) {
                    console.log(error);
                });
                /*console.log(data);
                _this.$confirm(data.day+"确认报名可参加评审？", "提示", {}).then(() => {
                    var param = {};
                    param.date = data.day;
                    axios.post('addDate', param).then(function (res) {
                        if (res.data.code == 0) {
                            _this.$message({
                                message: res.data.msg,
                                type: "success"
                            });
                            _this.getData();
                        } else {
                            _this.$message({
                                message: res.data.msg,
                                type: "error"
                            });
                        }
                    }).catch(function (error) {
                        console.log(error);
                    });
                });*/
            },
            delDate(data) {
                var _this = this;
                var param = {};
                param.date = data.day;
                var loading = this.$loading();
                axios.post('delDate', param).then(function (res) {
                    if (res.data.code == 0) {
                        _this.$message({
                            message: res.data.msg,
                            type: "success"
                        });
                        _this.getData();
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                    }
                    loading.close();
                }).catch(function (error) {
                    console.log(error);
                });
                /*_this.$confirm(data.day+"确认取消报名？", "提示", {}).then(() => {
                    var param = {};
                    param.date = data.day;
                    axios.post('delDate', param).then(function (res) {
                        if (res.data.code == 0) {
                            _this.$message({
                                message: res.data.msg,
                                type: "success"
                            });
                            _this.getData();
                        } else {
                            _this.$message({
                                message: res.data.msg,
                                type: "error"
                            });
                        }
                    }).catch(function (error) {
                        console.log(error);
                    });
                });*/
            },
            taskinfo(data) {

            },
        },
        mounted() {
            this.getData();
            // console.log(this.textContent('2024-05-02'));
        }
    })
</script>


</body>
</html>