<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 批量验证完整性功能演示</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
</head>
<body>
    <div id="app">
        <el-container style="padding: 20px;">
            <el-header>
                <h1>用户管理 - 数据完整性验证功能演示</h1>
            </el-header>
            
            <el-main>
                <!-- 搜索和操作区域 -->
                <el-card>
                    <div slot="header">
                        <span>搜索和批量操作</span>
                    </div>
                    
                    <el-form :inline="true" :model="searchForm" class="form-inline">
                        <el-form-item>
                            <el-input v-model="searchForm.name" size="mini" placeholder="用户名/姓名/手机号/邮箱"></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="getData()" size="mini">查询</el-button>
                            <el-button @click="reset()" size="mini">重置</el-button>
                        </el-form-item>
                        <el-form-item style="float: right">
                            <el-button :loading="verifyLoading" type="warning" size="mini" @click="batchVerifyIntegrity">
                                <i class="el-icon-check"></i> 批量验证完整性
                            </el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
                
                <!-- 用户列表 -->
                <el-card style="margin-top: 20px;">
                    <div slot="header">
                        <span>用户列表</span>
                    </div>
                    
                    <el-table 
                        border
                        v-loading="loading"
                        :data="userData"
                        style="width: 100%;"
                        size="small">
                        
                        <el-table-column type="index" label="序号" align="center" width="60"></el-table-column>
                        
                        <el-table-column prop="name" label="姓名" align="center" min-width="100"></el-table-column>
                        
                        <el-table-column prop="phone" label="手机号" align="center" min-width="120"></el-table-column>
                        
                        <el-table-column prop="email" label="邮箱" align="center" min-width="150"></el-table-column>
                        
                        <el-table-column prop="dept_name" label="科室" align="center" min-width="100"></el-table-column>
                        
                        <el-table-column prop="status" label="状态" align="center" width="80">
                            <template slot-scope="scope">
                                <el-tag v-if="scope.row.status != 1" type="danger">禁用</el-tag>
                                <el-tag v-if="scope.row.status == 1" type="success">正常</el-tag>
                            </template>
                        </el-table-column>
                        
                        <el-table-column prop="check_status" label="完整性检查" align="center" width="120">
                            <template slot-scope="scope">
                                <el-tag v-if="scope.row.check_status == null" type="info">未检查</el-tag>
                                <el-tag v-else-if="scope.row.check_status != 1" type="danger">异常</el-tag>
                                <el-tag v-else type="success">通过</el-tag>
                            </template>
                        </el-table-column>
                        
                        <el-table-column label="操作" align="center" fixed="right" width="180">
                            <template slot-scope="scope">
                                <el-button type="primary" @click="editUser(scope.row)" size="mini">编辑</el-button>
                                <el-button 
                                    type="warning" 
                                    @click="verifySingleRecord(scope.row)" 
                                    size="mini"
                                    :loading="scope.row.verifying"
                                    title="验证此记录的数据完整性">
                                    验证
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    
                    <!-- 分页 -->
                    <div style="margin-top: 20px; text-align: center;">
                        <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="page"
                            :page-sizes="[10, 20, 50, 100]"
                            :page-size="pageSize"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="total">
                        </el-pagination>
                    </div>
                </el-card>
                
                <!-- 功能说明 -->
                <el-card style="margin-top: 20px;">
                    <div slot="header">
                        <span>功能说明</span>
                    </div>
                    
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <h4>批量验证完整性</h4>
                            <ul>
                                <li>点击"批量验证完整性"按钮对所有用户数据进行完整性检查</li>
                                <li>系统会调用 <code>verifyIntegrity</code> 方法逐条验证用户数据</li>
                                <li>验证完成后会显示详细的统计结果</li>
                                <li>每条记录的 <code>check_status</code> 字段会被实时更新</li>
                            </ul>
                        </el-col>
                        <el-col :span="12">
                            <h4>单个记录验证</h4>
                            <ul>
                                <li>点击每行的"验证"按钮可以单独验证该用户的数据完整性</li>
                                <li>验证结果会立即反映在"完整性检查"列中</li>
                                <li>支持实时状态显示和错误提示</li>
                                <li>验证字段包括：姓名、手机号、邮箱、密码、真实姓名</li>
                            </ul>
                        </el-col>
                    </el-row>
                    
                    <el-divider></el-divider>
                    
                    <h4>验证逻辑</h4>
                    <el-steps :active="4" align-center>
                        <el-step title="数据检测" description="检测字段是否为加密数据"></el-step>
                        <el-step title="完整性验证" description="加密数据尝试解密，明文数据检查格式"></el-step>
                        <el-step title="状态更新" description="实时更新check_status字段"></el-step>
                        <el-step title="结果反馈" description="显示验证结果和错误信息"></el-step>
                    </el-steps>
                </el-card>
            </el-main>
        </el-container>
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    loading: false,
                    verifyLoading: false,
                    searchForm: {
                        name: ''
                    },
                    userData: [
                        // 模拟用户数据
                        { id: 1, name: '张三', phone: '13800138000', email: '<EMAIL>', dept_name: '技术部', status: 1, check_status: 1 },
                        { id: 2, name: '李四', phone: '13900139000', email: '<EMAIL>', dept_name: '市场部', status: 1, check_status: 0 },
                        { id: 3, name: '王五', phone: '15000150000', email: '<EMAIL>', dept_name: '人事部', status: 1, check_status: null },
                        { id: 4, name: '赵六', phone: '18600186000', email: '<EMAIL>', dept_name: '财务部', status: 0, check_status: 1 },
                        { id: 5, name: '钱七', phone: '17700177000', email: '<EMAIL>', dept_name: '技术部', status: 1, check_status: 0 }
                    ],
                    page: 1,
                    pageSize: 20,
                    total: 5
                }
            },
            methods: {
                // 获取数据
                getData() {
                    this.loading = true;
                    // 模拟API调用
                    setTimeout(() => {
                        this.loading = false;
                        this.$message.success('数据加载完成');
                    }, 1000);
                },
                
                // 重置搜索
                reset() {
                    this.searchForm.name = '';
                    this.getData();
                },
                
                // 分页处理
                handleSizeChange(val) {
                    this.pageSize = val;
                    this.getData();
                },
                
                handleCurrentChange(val) {
                    this.page = val;
                    this.getData();
                },
                
                // 编辑用户
                editUser(row) {
                    this.$message.info(`编辑用户: ${row.name}`);
                },
                
                // 批量验证数据完整性
                batchVerifyIntegrity() {
                    this.$confirm('确定要对当前用户表进行批量完整性验证吗？此操作将检查所有用户数据的完整性并更新check_status字段。', '批量验证确认', {
                        confirmButtonText: '确定验证',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.executeVerifyIntegrity();
                    }).catch(() => {
                        this.$message.info('已取消验证操作');
                    });
                },
                
                // 执行批量验证
                executeVerifyIntegrity() {
                    this.verifyLoading = true;
                    
                    // 模拟API调用
                    setTimeout(() => {
                        this.verifyLoading = false;
                        
                        // 模拟验证结果
                        const mockResult = {
                            code: 200,
                            msg: '完整性验证完成',
                            data: {
                                success_count: 5,
                                error_count: 0,
                                total_count: 5,
                                valid_count: 3,
                                invalid_count: 2,
                                errors: []
                            }
                        };
                        
                        this.$message.success('数据完整性验证完成！');
                        this.showVerifyResult(mockResult);
                        
                        // 更新表格数据
                        this.userData.forEach((user, index) => {
                            user.check_status = index < 3 ? 1 : 0;
                        });
                        
                    }, 2000);
                },
                
                // 显示验证结果
                showVerifyResult(result) {
                    const data = result.data;
                    const successRate = ((data.success_count / data.total_count) * 100).toFixed(1);
                    const validRate = ((data.valid_count / data.total_count) * 100).toFixed(1);
                    
                    const message = `
                        <div style="text-align: left;">
                            <h4>验证完整性结果统计</h4>
                            <p><strong>总记录数：</strong>${data.total_count} 条</p>
                            <p><strong>成功处理：</strong>${data.success_count} 条 (${successRate}%)</p>
                            <p><strong>处理失败：</strong>${data.error_count} 条</p>
                            <p style="color: #67C23A;"><strong>完整性正常：</strong>${data.valid_count} 条 (${validRate}%)</p>
                            <p style="color: #F56C6C;"><strong>完整性异常：</strong>${data.invalid_count} 条</p>
                        </div>
                    `;
                    
                    this.$alert(message, '验证完整性结果', {
                        dangerouslyUseHTMLString: true,
                        confirmButtonText: '确定'
                    });
                },
                
                // 验证单个记录
                verifySingleRecord(row) {
                    this.$confirm(`确定要验证用户 "${row.name}" 的数据完整性吗？`, '单个验证确认', {
                        confirmButtonText: '确定验证',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.executeSingleVerify(row);
                    }).catch(() => {
                        this.$message.info('已取消验证操作');
                    });
                },
                
                // 执行单个记录验证
                executeSingleVerify(row) {
                    this.$set(row, 'verifying', true);
                    
                    // 模拟API调用
                    setTimeout(() => {
                        this.$set(row, 'verifying', false);
                        
                        // 模拟验证结果
                        const isValid = Math.random() > 0.3; // 70%概率通过
                        this.$set(row, 'check_status', isValid ? 1 : 0);
                        
                        if (isValid) {
                            this.$message.success(`用户 "${row.name}" 的数据完整性验证通过！`);
                        } else {
                            this.$message.error(`用户 "${row.name}" 的数据完整性验证异常！`);
                        }
                    }, 1500);
                }
            },
            
            mounted() {
                this.getData();
            }
        });
    </script>
</body>
</html>
