<?php

namespace app\company\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use app\company\model\CompanyModel;
use app\company\model\StandardModel;
use app\validate\StandardVerify as Verify;
use app\model\SettingModel;
use hg\apidoc\annotation as Apidoc;
use think\facade\View;
use think\Request;
use think\facade\Db;
use app\model\FileModel;

/**
 * @Apidoc\Title("运行资料管理")
 * @Apidoc\Group("Index")
 * @Apidoc\Sort(1)
 */
class Data extends Base {

    /**
     * @Apidoc\Title("运行资料")
     * @Apidoc\Desc("运行资料")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function index($limit=20) {
        if (request()->isAjax()) {
            $where = [];
            $where[] = ['company_id','=',$_SESSION['company']['id']];
            $res = Db::table('top_standard_apply')->where($where)->order('id desc');
            $res = $res->paginate($limit)->each(function ($item, $key) {
                $item = StandardModel::codeToText($item);
                return $item;
            });
            result($res);
        } else {
            View::assign('title', '首页');
            return view();
        }
    }

    public function standardSave($id=0) {
        $request = $this->request->post();
        Verify::userCheck('save',$request);
        $id = StandardModel::standardSave($request,$id);
        if($id>0){
            result(['id'=>$id],0,'提交成功');
        }else{
            result('',7001,$id);
        }
    }

    public function getConfig() {
        $result['standard'] = Db::table('top_standard_name')->where(['is_del'=>0])->field('id,name')->select()->toArray();
        result($result);
    }

    public function getStandardInfo($id=0) {
        $company = Db::table('top_company_info')->where(['id'=>$_SESSION['company']['id']])->find();
        $re = Db::table('top_standard_apply')->where(['company_id'=>$_SESSION['company']['id']])->find();
        if(!empty($re)){
            $re = StandardModel::codeToText($re);
            $re['industry'] = $company['industry'].'/'.$company['specialty'];
        }else{
            $re = [
                'company_name' => $company['name'],
                'industry' => $company['industry'].'/'.$company['specialty'],
                'standard_id' => '',
                'level' => '',
                'is_advisory' => '',
                'advisory' => '',
            ];
        }
        result($re);
    }



}
