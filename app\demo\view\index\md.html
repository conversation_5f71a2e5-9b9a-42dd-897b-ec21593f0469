<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>列表DEMO</title>
    <link rel="stylesheet" href="__PUBLIC__/static/js/markdown/markdown-light.min.css">
    <link rel="stylesheet" href="__PUBLIC__/static/js/markdown/styles/an-old-hope.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/static/js/markdown/marked.min.js"></script>
    <script src="__PUBLIC__/static/js/markdown/highlight.min.js"></script>
    <script src="__PUBLIC__/static/js/markdown/highlightjs-line-numbers.min.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
    </style>
</head>
<body>
<div id="app" v-cloak>
    <textarea id="markdown" rows="10" cols="50">
ThinkPHP 6.0
===============
```
document.addEventListener("DOMContentLoaded", (event) => {
  document.querySelectorAll("pre code").forEach((el) => {
    hljs.highlightElement(el);
  });
});
```
> 运行环境要求PHP7.1+，兼容PHP8.0。

[官方应用服务市场](https://market.topthink.com) | [`ThinkAPI`——官方统一API服务](https://docs.topthink.com/think-api)

ThinkPHPV6.0版本由[亿速云](https://www.yisu.com/)独家赞助发布。

## 主要新特性

* 采用`PHP7`强类型（严格模式）
* 支持更多的`PSR`规范
* 原生多应用支持
* 更强大和易用的查询
* 全新的事件系统
* 模型事件和数据库事件统一纳入事件系统
* 模板引擎分离出核心
* 内部功能中间件化
* SESSION/Cookie机制改进
* 对Swoole以及协程支持改进
* 对IDE更加友好
* 统一和精简大量用法

## 安装

``` php
composer create-project topthink/think tp 6.0.*
composer create-project topthink/think tp 6.0.*
composer create-project topthink/think tp 6.0.*
composer create-project topthink/think tp 6.0.*
composer create-project topthink/think tp 6.0.*
```

如果需要更新框架使用
~~~
composer update topthink/framework
~~~

## 文档

[完全开发手册](https://www.kancloud.cn/manual/thinkphp6_0/content)

## 参与开发

请参阅 [ThinkPHP 核心框架包](https://github.com/top-think/framework)。

## 版权信息

ThinkPHP遵循Apache2开源协议发布，并提供免费使用。

本项目包含的第三方源码和二进制文件之版权信息另行标注。

版权所有Copyright © 2006-2020 by ThinkPHP (http://thinkphp.cn)

All rights reserved。

ThinkPHP® 商标和著作权所有者为上海顶想信息科技有限公司。

更多细节参阅 [LICENSE.txt](LICENSE.txt)

</textarea>

    <el-button @click="convertMarkdown">Convert to HTML</el-button>

    <div id="markdown-output" class="markdown-body dark"></div>
</div>

<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script>
    var app = new Vue({
        el: '#app',

        data() {
            return {
                form_name: '',
                markdownText: '',
                formOptions: [],
                deptData: [],
                userData: [],
                tableData: [],
                dialogFormVisible: false,
                visible: false,
                loading: true,
                roletotal: 0,
                currentPage: 1,
                pageSize: 10,
                form:{},
                formRules: {
                    form_name: [{required: true, message: "请输入表单名称", trigger: "blur"}],
                },
            };
        },
        components: {
            'deptchoose':      'url:../../../public/vue/deptChoose.vue',
            'personnelchoose': 'url:../../../public/vue/personnelChoose.vue',
        },
        methods: {
            convertMarkdown() {
                var markdownText = document.getElementById('markdown').value;
                var html = marked.parse(markdownText);
                document.getElementById('markdown-output').innerHTML = html;
                document.querySelectorAll("pre code").forEach((el) => {
                    hljs.highlightElement(el);
                });
                hljs.highlightAll();
            },
            // 日期格式化
            dateFormatter (row, column) {
                let datetime = row[column.property];
                if(datetime){
                    var date=new Date(parseInt(datetime)* 1000);

                    var year = date.getFullYear().toString().padStart(4, "0");
                    var mon = (date.getMonth() + 1).toString().padStart(2, "0");
                    var day = date.getDate().toString().padStart(2, "0");

                    var hours = date.getHours().toString().padStart(2, "0");
                    var minu = date.getMinutes().toString().padStart(2, "0");
                    var sec = date.getSeconds().toString().padStart(2, "0");

                    return year+'-'+mon+'-'+day+' '+hours+':'+minu;
                }
                return ''
            },
            handleSizeChange: function(val) {
                this.pageSize = val;
                this.getResult();
                console.log('每页 ${val} 条');
            },

            handleCurrentChange: function(val) {
                this.currentPage = val;
                this.getResult();
                console.log('当前页: ${val}');
            },
            add(){
                this.form = {},
                    this.dialogFormVisible = true;
            },
            edit(row){
                this.form = JSON.parse(JSON.stringify(row));
                this.dialogFormVisible = true;
            },
            deleteList(row){
                var _this = this;
                this.$confirm('此操作将删除该行, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    var url = "{:url('index/delete')}";
                    axios.post(url, {id:row.id}).then(function (res) {
                        console.log(res);

                        if( res.data.code != 200 )
                        {
                            _this.$message({
                                message: res.data,
                                type: "error"
                            });
                        }
                        _this.$message({
                            message: res.data.msg,
                            type: "success"
                        });
                        _this.getResult();

                    }).catch(function (error) {
                        console.log(error);
                    });
                }).catch(function (error) {
                    console.log(error);
                });
            },
            getResult() {
                var _this = this;
                var param = {
                    pageSize: _this.pageSize,
                    page: _this.currentPage,
                    keyword: _this.form_name,
                };
                var url = "{:url('index/getDataList')}";
                axios.post(url,param).then(function (res) {
                    console.log(res);

                    if( res.data.code < 0 ){
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }

                    _this.tableData = res.data.data;
                    _this.roletotal = res.data.total;
                    _this.loading = false;
                }).catch(function (error) {
                    console.log(error);
                });
            },
            addSubmit: function () {
                this.$refs.form.validate(valid => {
                    var _this = this;
                    if (valid) {

                        this.$confirm("确认提交吗？", "提示", {}).then(() => {
                            _this.addLoading = true;
                            var param = _this.form;
                            var url = "{:url('index/save')}";
                            axios.post(url,param).then(function (res) {
                                console.log(res);
                                _this.addLoading = false;

                                if( res.data.code != 200 )
                                {
                                    _this.$message({
                                        message: res.data.msg,
                                        type: "error"
                                    });
                                }
                                _this.$message({
                                    message: res.data.msg,
                                    type: "success"
                                });
                                _this.dialogFormVisible = false;
                                _this.getResult();
                            }).catch(function (error) {
                                console.log(error);
                            });

                        });
                    }
                });
            },


            /**
             * 部门选择
             * */
            choiceDept() {
                this.$refs.deptChoose.visible = true;
                this.$refs.deptChoose.isRadio = false; // 是否单选  true 单选  false 多选
                this.$refs.deptChoose.initialize(this.deptData);
            },
            /**
             * 部门选择 - 单选
             * */
            choiceDept2() {
                this.$refs.deptChoose.visible = true;
                this.$refs.deptChoose.isRadio = true; // 是否单选  true 单选  false 多选
                this.$refs.deptChoose.initialize(this.deptData);
            },
            /**
             * 人员选择
             * */
            choiceUser() {
                this.$refs.personnelChoose.visible = true;
                this.$refs.personnelChoose.isRadio = false; // 是否单选  true 单选  false 多选
                this.$refs.personnelChoose.initialize(this.userData);
            },
            /**
             * 人员选择 - 单选
             * */
            choiceUser2() {
                this.$refs.personnelChoose.visible = true;
                this.$refs.personnelChoose.isRadio = true; // 是否单选  true 单选  false 多选
                this.$refs.personnelChoose.initialize(this.userData);
            },
            /**
             * 部门选择回调数据
             * */
            deptFun(data) {
                this.deptData = data;
                console.log(data);
            },
            /**
             * 人员选择回调数据
             * */
            userFun(data) {
                this.userData = data;
                console.log(data);
            },
        },
        mounted() {
        }
    })
</script>


</body>
</html>