<?php
/**
 * HSM 缓存加密解密功能测试脚本
 * 用于验证 hsmCacheEncrypt 和 hsmCacheDecrypt 方法的功能
 */

// 引入 ThinkPHP 框架
require_once __DIR__ . '/vendor/autoload.php';

// 初始化应用
$app = new \think\App();
$app->initialize();

// 测试数据
$testData = [
    '测试文本1：这是一个中文测试',
    'Test Text 2: English test data',
    '123456789',
    '<EMAIL>',
    '{"name":"张三","age":30,"city":"北京"}',
    'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    // 添加一些看起来像加密数据的测试
    'SGVsbG8gV29ybGQhVGhpcyBpcyBhIGxvbmcgZW5vdWdoIGJhc2U2NCBzdHJpbmcgdG8gdGVzdCBlbmNyeXB0aW9uIGRldGVjdGlvbg==',
    'HSM_abc123def456ghi789jkl012mno345pqr678stu901vwx234yz',
    'ENC_encrypted_data_example_with_prefix_marker',
];

// 测试加密状态检测
echo "=== 加密状态检测测试 ===\n";
$encryptionService = new \app\admin\service\EncryptionStatusService();

foreach ($testData as $index => $data) {
    $isEncrypted = $encryptionService->isFieldEncrypted($data);
    echo "数据 " . ($index + 1) . ": " . ($isEncrypted ? '✅ 已加密' : '❌ 未加密') . " - " . substr($data, 0, 50) . "...\n";
}

echo "\n";

echo "=== HSM 缓存加密解密功能测试 ===\n\n";

// 测试1：基本加密解密功能
echo "1. 基本加密解密功能测试\n";
echo str_repeat("-", 50) . "\n";

foreach ($testData as $index => $originalText) {
    echo "测试数据 " . ($index + 1) . ": " . substr($originalText, 0, 30) . "...\n";
    
    // 测试加密
    $startTime = microtime(true);
    $encrypted = hsmCacheEncrypt($originalText);
    $encryptTime = round((microtime(true) - $startTime) * 1000, 2);
    
    if ($encrypted === false) {
        echo "  ❌ 加密失败\n";
        continue;
    }
    
    echo "  ✅ 加密成功 (耗时: {$encryptTime}ms)\n";
    echo "  加密结果: " . substr($encrypted, 0, 50) . "...\n";
    
    // 测试解密
    $startTime = microtime(true);
    $decrypted = hsmCacheDecrypt($encrypted);
    $decryptTime = round((microtime(true) - $startTime) * 1000, 2);
    
    if ($decrypted === false) {
        echo "  ❌ 解密失败\n";
        continue;
    }
    
    echo "  ✅ 解密成功 (耗时: {$decryptTime}ms)\n";
    
    // 验证数据一致性
    if ($originalText === $decrypted) {
        echo "  ✅ 数据一致性验证通过\n";
    } else {
        echo "  ❌ 数据一致性验证失败\n";
        echo "    原始: $originalText\n";
        echo "    解密: $decrypted\n";
    }
    
    echo "\n";
}

// 测试2：缓存性能测试
echo "2. 缓存性能测试\n";
echo str_repeat("-", 50) . "\n";

$testText = "性能测试文本：这是一个用于测试缓存性能的文本数据";

// 第一次加密（无缓存）
echo "第一次加密（无缓存）:\n";
$startTime = microtime(true);
$encrypted = hsmCacheEncrypt($testText);
$firstEncryptTime = round((microtime(true) - $startTime) * 1000, 2);
echo "  耗时: {$firstEncryptTime}ms\n";

// 第二次加密（有缓存）
echo "第二次加密（有缓存）:\n";
$startTime = microtime(true);
$encryptedCached = hsmCacheEncrypt($testText);
$secondEncryptTime = round((microtime(true) - $startTime) * 1000, 2);
echo "  耗时: {$secondEncryptTime}ms\n";

// 验证缓存结果一致性
if ($encrypted === $encryptedCached) {
    echo "  ✅ 缓存结果一致\n";
    $speedup = round($firstEncryptTime / $secondEncryptTime, 2);
    echo "  🚀 性能提升: {$speedup}x\n";
} else {
    echo "  ❌ 缓存结果不一致\n";
}

echo "\n";

// 第一次解密（无缓存）
echo "第一次解密（无缓存）:\n";
$startTime = microtime(true);
$decrypted = hsmCacheDecrypt($encrypted);
$firstDecryptTime = round((microtime(true) - $startTime) * 1000, 2);
echo "  耗时: {$firstDecryptTime}ms\n";

// 第二次解密（有缓存）
echo "第二次解密（有缓存）:\n";
$startTime = microtime(true);
$decryptedCached = hsmCacheDecrypt($encrypted);
$secondDecryptTime = round((microtime(true) - $startTime) * 1000, 2);
echo "  耗时: {$secondDecryptTime}ms\n";

// 验证缓存结果一致性
if ($decrypted === $decryptedCached) {
    echo "  ✅ 缓存结果一致\n";
    $speedup = round($firstDecryptTime / $secondDecryptTime, 2);
    echo "  🚀 性能提升: {$speedup}x\n";
} else {
    echo "  ❌ 缓存结果不一致\n";
}

echo "\n";

// 测试3：缓存统计信息
echo "3. 缓存统计信息\n";
echo str_repeat("-", 50) . "\n";

$stats = hsmCacheStats();
echo "加密缓存数量: " . $stats['encrypt_cache_count'] . "\n";
echo "解密缓存数量: " . $stats['decrypt_cache_count'] . "\n";
echo "总缓存数量: " . $stats['total_cache_count'] . "\n";
echo "估算内存使用: " . $stats['estimated_memory_mb'] . " MB\n";
echo "Redis连接状态: " . ($stats['redis_connected'] ? '✅ 已连接' : '❌ 未连接') . "\n";

if (!$stats['redis_connected'] && isset($stats['error'])) {
    echo "错误信息: " . $stats['error'] . "\n";
}

echo "\n";

// 测试4：错误处理测试
echo "4. 错误处理测试\n";
echo str_repeat("-", 50) . "\n";

// 测试空值处理
echo "测试空值处理:\n";
$result = hsmCacheEncrypt('');
echo "  空字符串加密: " . ($result === false ? '✅ 正确返回false' : '❌ 应该返回false') . "\n";

$result = hsmCacheEncrypt(null);
echo "  null值加密: " . ($result === false ? '✅ 正确返回false' : '❌ 应该返回false') . "\n";

$result = hsmCacheDecrypt('');
echo "  空字符串解密: " . ($result === false ? '✅ 正确返回false' : '❌ 应该返回false') . "\n";

$result = hsmCacheDecrypt(null);
echo "  null值解密: " . ($result === false ? '✅ 正确返回false' : '❌ 应该返回false') . "\n";

echo "\n";

// 测试5：批量操作性能
echo "5. 批量操作性能测试\n";
echo str_repeat("-", 50) . "\n";

$batchSize = 50;
$batchData = [];
for ($i = 1; $i <= $batchSize; $i++) {
    $batchData[] = "批量测试数据 {$i}: " . md5($i);
}

echo "批量加密 {$batchSize} 条数据:\n";
$startTime = microtime(true);
$encryptedBatch = [];
foreach ($batchData as $data) {
    $encryptedBatch[] = hsmCacheEncrypt($data);
}
$batchEncryptTime = round((microtime(true) - $startTime) * 1000, 2);
echo "  总耗时: {$batchEncryptTime}ms\n";
echo "  平均耗时: " . round($batchEncryptTime / $batchSize, 2) . "ms/条\n";

echo "批量解密 {$batchSize} 条数据:\n";
$startTime = microtime(true);
$decryptedBatch = [];
foreach ($encryptedBatch as $encrypted) {
    if ($encrypted !== false) {
        $decryptedBatch[] = hsmCacheDecrypt($encrypted);
    }
}
$batchDecryptTime = round((microtime(true) - $startTime) * 1000, 2);
echo "  总耗时: {$batchDecryptTime}ms\n";
echo "  平均耗时: " . round($batchDecryptTime / $batchSize, 2) . "ms/条\n";

// 验证批量操作的正确性
$successCount = 0;
for ($i = 0; $i < count($batchData); $i++) {
    if (isset($decryptedBatch[$i]) && $batchData[$i] === $decryptedBatch[$i]) {
        $successCount++;
    }
}

echo "  数据一致性: {$successCount}/{$batchSize} (" . round($successCount / $batchSize * 100, 1) . "%)\n";

echo "\n";

// 测试6：缓存清理功能
echo "6. 缓存清理功能测试\n";
echo str_repeat("-", 50) . "\n";

echo "清理前缓存统计:\n";
$statsBefore = hsmCacheStats();
echo "  总缓存数量: " . $statsBefore['total_cache_count'] . "\n";

echo "执行缓存清理...\n";
$clearResult = hsmClearCache();
echo "  清理结果: " . ($clearResult ? '✅ 成功' : '❌ 失败') . "\n";

echo "清理后缓存统计:\n";
$statsAfter = hsmCacheStats();
echo "  总缓存数量: " . $statsAfter['total_cache_count'] . "\n";

echo "\n=== 测试完成 ===\n";
