<?php

include_once "inc/td_core.php";

$re = mobileSms('17318618021','测试');
print_r($re);
function mobileSms($mobile,$content){
    $LoginName = 'DZ82000112';
    $Pwd = 'ul@2019';
    $FeeType = '2';
    $SignName = '【达州市中心医院】';
    $content = urlencode("OA消息提醒：".$content);
    $data = [
        'LoginName' => $LoginName,
        'Pwd' => $Pwd,
        'FeeType' => $FeeType,
        'Mobile' => $mobile,
        'content' => urldecode($content),
        'SignName' => urldecode($SignName),
        'TimingDate' => '',
        'ExtCode' => '',
    ];
    $url = "https://sdkulink.028lk.com:8082/Api/SendSms";
//    $url = "http://localhost/general/topcrm/index.php/sso/user/privList?appid=111731980118&appkey=WLLHKKPVHPV6C7LDUPNU9XUL65QQGO4Z";
    $url = "https://sdkulink.028lk.com:8082/Api/SendSms?LoginName={$LoginName}&Pwd={$Pwd}&FeeType={$FeeType}&Mobile={$mobile}&Content={$content}&SignName={$SignName}&TimingDate=&ExtCode=";
    // 初始化cURL会话
//    $data = ['priv'=>'职员'];
//    $result = postUrl($url,$data);
//    echo $url;die;
    $result = getUrl($url);
    print_r($result);die;
    $data = explode('|',$result);
    if($data[0]==='OK'){
        return ['code'=>0,'msg'=>'发送成功','data'=>['MessageID'=>$data[1],'msg'=>$data[2]]];
    }else{
        $file = 'D:\MYOA\logs/mobile_error.txt';
        file_put_contents($file,date('【Y-m-d H:i:s】').$url.'--'.$result."\n",FILE_APPEND);
        file_put_contents($file,date('【Y-m-d H:i:s】').$mobile.'--'.$content.'--'.$result."\n",FILE_APPEND);
        return ['code'=>1001,'msg'=>'发送失败'];
    }
}

function GetSmsReport($MessageID){
    $LoginName = 'DZ82000112';
    $Pwd = 'ul@2019';
    $url = "https://sdkulink.028lk.com:8082/Api/GetSmsReport?LoginName={$LoginName}&Pwd={$Pwd}&MessageID={$MessageID}";
    $result = file_get_contents($url);
    $data = explode('$$$',$result);
    $file = 'D:\MYOA\logs/mobile_error.txt';
//    file_put_contents($file,date('【Y-m-d H:i:s】').$url."\n",FILE_APPEND);
//    file_put_contents($file,date('【Y-m-d H:i:s】').$MessageID.'--'.$result."\n",FILE_APPEND);
    return $data[2];
}

function getUrl($url){
    return file_get_contents($url);
}


//curlPost请求
function postUrl($url, $params = []) {
    $ch = curl_init($url);
    $header = array(
        'Content-Type: application/x-www-form-urlencoded',
    );
    curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 1000);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $return_data = curl_exec($ch);
    curl_errno($ch);
    print_r($return_data);die;
    return $return_data;
}
