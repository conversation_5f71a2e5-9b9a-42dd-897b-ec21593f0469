<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>开放平台</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <!--引入原oa js-->
    <script src="/static/js/jquery/jquery-with-migrate.js"></script>
    <style>
        .el-form-item--mini.el-form-item, .el-form-item--small.el-form-item{margin-bottom: 0;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">
        <!--表头区-->
        <header>
            开放平台<el-link type="primary" style="margin-left: 30px;" href="/general/toppingsoft/public/apidoc/" target="_blank">接口文档</el-link>
        </header>
        <!--查询区-->
        <el-form ref="ruleForm" :inline="true" :model="searchData" size="mini" label-width="auto" class="tableSearch">
            <el-form-item label="系统名称">
                <el-input  size="mini" v-model="searchData.search"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="submitForm" :loading="loading" size="mini" icon="el-icon-search"  native-type="submit">查询</el-button>
                <el-button @click="resetForm('searchData')" size="mini" icon="el-icon-delete">重置</el-button>
                <el-button type="success" size="mini" @click="add">添加</el-button>
            </el-form-item>
        </el-form>
        <!--列表区-->
        <template>
            <el-table
                    :data="tableData"
                    border
                    size="small"
                    v-loading="loading"
                    style="width: 100%;margin-top: 15px;"
                    ref="qtable"
                    :height="height">
                <el-table-column
                        prop="id"
                        align="center"
                        width="60"
                        label="ID">
                </el-table-column>
                <el-table-column
                        prop="name"
                        align="center"
                        width="150"
                        label="系统名称">
                </el-table-column>
                <el-table-column
                        prop="appid"
                        align="center"
                        width="120"
                        label="appid">
                </el-table-column>
                <el-table-column
                        prop="appkey"
                        align="center"
                        width="200"
                        show-overflow-tooltip
                        label="appkey">
                </el-table-column>
                <el-table-column
                        prop="redirect_uri"
                        align="center"
                        width="150"
                        show-overflow-tooltip
                        label="跳转地址">
                </el-table-column>
                <el-table-column
                        prop="domain"
                        align="center"
                        width="150"
                        show-overflow-tooltip
                        label="域名">
                </el-table-column>
                <el-table-column
                        prop="tp_appid"
                        align="center"
                        width="150"
                        show-overflow-tooltip
                        label="对接appid">
                </el-table-column>
                <el-table-column
                        prop="tp_appkey"
                        align="center"
                        min-width="200"
                        show-overflow-tooltip
                        label="对接appkey">
                </el-table-column>
                <el-table-column
                        prop="param"
                        align="center"
                        min-width="100"
                        show-overflow-tooltip
                        label="关联参数">
                </el-table-column>
                <el-table-column
                        prop="create_user_name"
                        align="center"
                        width="100"
                        label="添加人">
                </el-table-column>
                <el-table-column
                        prop="create_time"
                        align="center"
                        width="150"
                        label="添加时间">
                </el-table-column>
                <el-table-column
                        label="操作"
                        fixed="right"
                        width="280">
                    <template slot-scope="scope">
                        <el-button @click="edit(scope.row)" type="waring" size="mini">编辑</el-button>
                        <el-button @click="updateAppkey(scope.row)" type="primary" size="mini">更新appkey</el-button>
                        <el-button @click="del(scope.row)" type="danger" size="mini">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </template>
        <!-- 分页区-->
        <div class="pagination">
            <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="searchData.page"
                    :page-size="searchData.limit"
                    layout="total, sizes, prev, pager, next,jumper"
                    :page-sizes="[10, 20, 30, 50,100,500,1000]"
                    :total="searchData.total">
            </el-pagination>
        </div>
    </div>
    <!--新增弹窗-->
    <el-dialog title="新增" width="600px" :visible.sync="dialogFormVisible">
        <el-form size="small" :model="form" :rules="formRules" ref="form" class="oa-form">
            <el-descriptions class="margin-top" :column="1" border label-style="width:120px" content-style="width:400px">
                <el-descriptions-item label="系统名称">
                    <el-form-item prop="name">
                        <el-input v-model="form.name"></el-input>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label="跳转地址">
                    <el-form-item prop="redirect_uri">
                        <el-input v-model="form.redirect_uri"></el-input>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label="域名">
                    <el-form-item prop="domain">
                        <el-input v-model="form.domain"></el-input>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label="关联参数">
                    <el-form-item prop="param">
                        <el-input v-model="form.param"></el-input>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label="对接appid">
                    <el-form-item prop="tp_appid">
                        <el-input v-model="form.tp_appid"></el-input>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label="对接appkey">
                    <el-form-item prop="tp_appkey">
                        <el-input v-model="form.tp_appkey"></el-input>
                    </el-form-item>
                </el-descriptions-item>
            </el-descriptions>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="dialogFormVisible = false" size="small">取 消</el-button>
            <el-button type="primary" @click="addSubmit" size="small">确 定</el-button>
        </div>
    </el-dialog>
</div>
</body>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>


<script src="__PUBLIC__/static/js/es6-promise.auto.min.js"></script>
<script src="__PUBLIC__/static/js/browser.min.js"></script>
<script src="__PUBLIC__/static/js/polyfill.min.js"></script>

<script type="text/babel">
    Vue.use(httpVueLoader);
    new Vue({
        el: '#app',
        // 模板导入区
        components: {
            'info': 'url:/general/toppingsoft/app/UserInfo/view/vacation/module/flow_info.vue?v=1',
        },
        data: function () {
            return {
                data_form: [],
                year_check_url:[],
                tableData: [],
                searchData: {
                    search: '',
                    page: 1,
                    total: 0,
                    limit: 20,
                },
                /* 查询折叠 */
                dialogFormVisible: false,
                loading: false,
                height: document.documentElement.clientHeight - 195,
                form:{
                    id:'',
                    name:'',
                    redirect_uri:'',
                    tp_appid:'',
                    tp_appkey:'',
                    domain:'',
                    param:'',
                },
                formRules: {
                    name: [{required: true, message: "请输入系统名称", trigger: "blur"}],
                },
            }
        },
        mounted: function(){
            var that = this;
            window.addEventListener('resize',function(){
                clearTimeout(that.resizeFlag);
                that.resizeFlag =setTimeout(function(){
                    that.height = document.documentElement.clientHeight -195;
                },300)
            });
            this.getData();
        },
        methods: {
            statusChange(){
                this.searchData.page = 1;
                this.getData();
            },
            /**
             * 搜索
             * */
            submitForm(formName) {
                console.log()
                var that = this
                that.tableData.page = 1;
                that.getData();
            },
            /**
             * 添加
             * */
            add() {
                this.form = {
                    id:'',
                    name:'',
                    redirect_uri:'',
                    tp_appid:'',
                    tp_appkey:'',
                    domain:'',
                    param:'',
                };
                this.dialogFormVisible = true;
            },
            edit(row) {
                this.form = row;
                this.dialogFormVisible = true;
            },
            updateAppkey(row) {
                var _this = this;
                _this.$confirm("确认更新Appkey，更新后原Appkey将失效？", "提示", {}).then(() => {
                    _this.addLoading = true;
                    var param = {};
                    param.id = row.id;
                    axios.post('updateAppkey', param).then(function (res) {
                        _this.addLoading = false;
                        if (res.data.code == 0) {
                            _this.$message({
                                message: res.data.msg,
                                type: "success"
                            });
                            _this.getData();
                        } else {
                            _this.$message({
                                message: res.data.msg,
                                type: "error"
                            });
                        }
                    }).catch(function (error) {
                        console.log(error);
                    });
                });
            },
            del(row) {
                var _this = this;
                _this.$confirm("确认删除？", "提示", {}).then(() => {
                    _this.addLoading = true;
                    var param = {};
                    param.id = row.id;
                    axios.post('del', param).then(function (res) {
                        _this.addLoading = false;
                        if (res.data.code == 0) {
                            _this.$message({
                                message: res.data.msg,
                                type: "success"
                            });
                            _this.getData();
                        } else {
                            _this.$message({
                                message: res.data.msg,
                                type: "error"
                            });
                        }
                    }).catch(function (error) {
                        console.log(error);
                    });
                });
            },
            /**
             * 初始数据
             * */
            getData() {
                var _this = this;
                _this.loading = true;
                var where = _this.searchData;
                where._ajax = 1;
                axios.post('list', where).then(function (res) {
                    _this.loading = false;
                    if (res.data.code == 0) {
                        _this.tableData = res.data.data.data;
                        _this.searchData.total = res.data.data.total;
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: res.data.type
                        });
                    }
                    _this.$nextTick(function(){
                        _this.$refs.qtable.doLayout();
                    })
                }).catch(function (error) {
                    _this.loading = false;
                    console.log(error);
                });

            },
            addSubmit: function () {
                var _this = this;
                this.$refs.form.validate(valid => {
                    if (valid) {
                        _this.addLoading = true;
                        var param = _this.form;
                        axios.post('save', param).then(function (res) {
                            _this.addLoading = false;
                            if (res.data.code == 0) {
                                _this.$message({
                                    message: res.data.msg,
                                    type: "success"
                                });
                                _this.dialogFormVisible = false;
                                _this.getData();
                            } else {
                                _this.$message({
                                    message: res.data.msg,
                                    type: "error"
                                });
                            }
                        }).catch(function (error) {
                            console.log(error);
                        });
                    }
                });
            },
            /**
             * 重置
             * */
            resetForm(formName) {
                this.searchData.name = '';
                this.searchData.checkname = '';
                this.getData();
            },
            /**
             * 分页
             * */
            handleCurrentChange(val) {
                this.searchData.page = val;
                this.getData()
            },
            handleSizeChange(val) {
                console.log(`每页 ${val} 条`);
                this.searchData.limit = val;
                this.getData()
            },
        },
    })
</script>
</html>



