<template>
  <el-row>
    <div class="headerBox">
      <img src="../../../public/static/img/bg.png" alt="" class="bgImage">
      <div class="headerTitle">
        <div>成都市企业安全生产标准化</div>
        <div>{{ company_name }}</div>
      </div>
    </div>
    <div class="infoBox">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" class="demo-ruleForm">
        <div class="formItem">
          <div class="formItemLabel">
            姓名
          </div>
          <div class="formInput">
            <el-form-item prop="name">
              <el-input v-model="ruleForm.name" placeholder="请填写姓名"></el-input>
            </el-form-item>
          </div>
        </div>

        <div class="formItem">
          <div class="formItemLabel">
            职务
          </div>
          <div class="formInput">
            <el-form-item prop="job1">
              <el-select v-model="ruleForm.job1" placeholder="请选择职务">
                <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </div>
        </div>
        <div class="formBtn">
          <el-button type="primary" @click="submitForm('ruleForm')">开始考试</el-button>
        </div>
      </el-form>

    </div>
  </el-row>
</template>

<script>
module.exports = {
  name: "companyInfo",
  // 模板导入区
  components: {
    'check-view': 'url:../../../../../app/work/view/check/moudel/check.vue?v=1',
    'work': 'url:../../../../../app/work/view/work/moudel/wrokContent.vue?v=1',
  },
  props: {
    signName: {
      type: String,
      default: 'month',
    },
    taskId: {
      type: String,
      default: 'month',
    },
    companyName: {
      type: String,
      default: 'month',
    },
    standardId: {
      type: String,
      default: 'month',
    },
  },
  data: function () {
    return {
      sign: this.signName,
      task_id: this.taskId,
      company_name: this.companyName,
      standard_id: this.standardId,
      ruleForm: {
        name: '',
        phone: '',
        job1: '',
        job2: ''
      },
      options: [
        {
          value: '企业主要负责人',
          label: '企业主要负责人'
        },
        {
          value: '安全管理人员',
          label: '安全管理人员'
        }, {
          value: '特种作业人员',
          label: '特种作业人员'
        }, {
          value: '关键岗位作业人员',
          label: '关键岗位作业人员'
        },
        {
          value: '其他',
          label: '其他'
        },
      ],
      rules: {
        name: [
          {required: true, message: '请输入考生名称', trigger: 'blur'}
        ],
        job1: [
          {required: true, message: '请选择考生职务', trigger: 'change'}
        ]
      }
    };
  },
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // window.location.href = './examination.html';
          this.createPaper();
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    createPaper() {
      var vm = this;
      vm.loading = true;
      var url = "../paper/createOnePaper";
      this.$confirm('点击确定参加考试, 中途退出后需重写扫码重写考试；试卷提交后会立即公布本次成绩且不可重新考试', '考试提醒', {
        confirmButtonText: '参加考试',
        cancelButtonText: '取消考试',
        showClose: false,
        distinguishCancelAndClose: false,
        type: 'warning'
      }).then(() => {
        axios.post(url, {
          task_id: vm.task_id,
          sign: vm.sign,
          standard_id: vm.standard_id,
          position_name: vm.ruleForm.job1,
          user_name: vm.ruleForm.name,
        }).then(function (res) {
          vm.loading = false;
          if (res.data.code == 1000) {
            vm.$emit('ok', res.data.data)
          } else {
            vm.$alert( res.data.msg, '考试提醒', {
              showClose: false,
              distinguishCancelAndClose: false,
              confirmButtonText: '确定',
            });
          }
        }).catch(function (error) {
          vm.loading = false;

        });
      }).catch(() => {

      });


    },
  },
  mounted() {
    console.log('sign = ' + this.sign, 'task_id = ' + this.task_id)
  },
  created: function () {


  },
}
</script>
<style scoped>
/*修改表单下内边距*/
.el-form-item--mini.el-form-item {
  margin-bottom: 0px;
}


</style>
