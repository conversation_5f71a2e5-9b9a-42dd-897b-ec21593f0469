<style>
.my-autocomplete li{line-height: normal;padding: 7px;}
.my-autocomplete li .name{text-overflow: ellipsis;overflow: hidden;}
.my-autocomplete li .addr{font-size: 12px;color: #b4b4b4;}
.my-autocomplete li .highlighted{color: #ddd;}
</style>
<template>
  <el-dialog title="添加" width="600px" class="icon-dialog" :visible.sync="visible" append-to-body>
    <el-form size="small" ref="form" :model="form" :rules="rules" label-width="140px">
      <el-row v-if="options">
        <el-col :span="24">
          <el-form-item label="上级要素" prop="pid">
            <el-cascader v-model="form.pid" :options="options" :props="{value:'id',label:'name'}">
            </el-cascader>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row >
        <el-col :span="24">
          <el-form-item :label="form.title" prop="name">
            <el-input  v-model="form.name" autoComplete="off" clearable  placeholder="请输入评审标准名称"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="!options">
        <el-col :span="24">
          <el-form-item label="要素合格线（%）" prop="mark">
            <el-input  v-model="form.mark" autoComplete="off" clearable  placeholder="%"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="!options">
        <el-col :span="24">
          <el-form-item label="权重" prop="weight">
            <el-input-number v-model="form.weight" :precision="2" :step="0.01" :max="10"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row >
        <el-col :span="24">
          <el-form-item label="排序号" prop="sort">
            <el-input  v-model="form.sort" autoComplete="off" clearable  placeholder="请输入排序号"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" :loading="loading" type="primary" @click="submit" v-loading.fullscreen.lock="loading">保 存</el-button>
      <el-button size="small" @click="visible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>
<script>
module.exports = {
  name: "add",
  // 模板导入区
  components: {
  },
  data:function() {
    return {
      id:0,
      isAdmin: false,
      visible: false,
      title: '新增',
      options: [],
      form: {
        pid:'',
        main_id:'',
        pname:'',
        name:'',
        mark:'',
        weight:'',
        sort:'',
      },
      loading:false,
      rules: {
        pid:[
          {required: true, message: '上级要素不能为空', trigger: 'blur'},
        ],
        name:[
          {required: true, message: '要素名称名称不能为空', trigger: 'blur'},
        ],
      }
    }
  },
  mounted: function(){
  },
  created:function(){
  },
  methods: {
    /**
     * 打开弹窗调用方法
     * */
    open: function (row,options) {
      console.log(row)
      row = row?row:{id:0};
      var _this =this;
      _this.options = options;
      _this.visible = true;
      _this.form = row;
    },
    submit: function () {
      var _this = this;
      var param = _this.form;
      this.$refs.form.validate(function (valid) {
        if(valid){
          axios.post("standardElementSave", param).then(function (res) {
            _this.$message({
              message: res.data.msg,
              type: res.data.type
            });
            if (res.data.code == 0) {
              _this.visible = false;
              _this.$emit("ok");
            }
          }).catch(function (error) {
            console.log("出现错误:",error);
          });
        }
      });
    },
    refresh: function () {
      this.$emit("refresh");
    },
  }
}
</script>