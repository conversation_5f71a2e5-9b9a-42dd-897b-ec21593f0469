<?php
namespace app\job;

use app\model\MessageModel;
use GatewayClient\Gateway;
use think\facade\Db;
use think\queue\Job;

class Stock {

    public function fire(Job $job, $data) {
        $isJobDone = $this->doJob($data);
        if ($isJobDone){
            $job->delete();
        }else{
            if ($job->attempts() > 3){
                $file = 'D:\MYOA\webroot/general/toppingsoft/socketerror.txt';
                file_put_contents($file,json_encode($data)."\n",FILE_APPEND);
                $job->delete();
            }
        }
        // 你的逻辑代码
        // $data 是从队列中获取的原始数据
        if ($job->attempts() > 3) {
            // 如果尝试超过3次则删除任务
            $file = 'D:\MYOA\webroot/general/toppingsoft/socketerror.txt';
            file_put_contents($file,json_encode($data)."\n",FILE_APPEND);
            $job->delete();
        }
    }

    public function doJob($data){
        try {
            $exclude_client_id = Gateway::getClientIdByUid($data['content']['user_type'].$_SESSION[$data['content']['user_type']]['id']);
            $message = [
                'type' => 'content',
                'data' => $data['content'],
            ];
            if(!empty($data['group_id'])){
                Gateway::sendToGroup($data['group_id'], json_encode($message), $exclude_client_id);
            }
            $group_user = Db::table('top_discuss_group_user')->where(['group_id'=>$data['group_id']])->select()->toArray();
            foreach ($group_user as $v){
                if(empty(Gateway::getClientIdByUid($v['user_type'].$v['user_id']))){
                    $url = "/general/toppingsoft/index.php/{$v['user_type']}/discuss/index?id={$data['group_id']}";
                    $a = MessageModel::sendSms($v['user_type'],$v['user_id'],'message','您有新的聊天消息',$url);
                }
            }
            return true;
        }catch (\Exception $e){
            // 如果尝试超过3次则删除任务
            $file = 'D:\MYOA\webroot/general/toppingsoft/socketerror.txt';
            file_put_contents($file,json_encode($e)."\n",FILE_APPEND);
            return false;
        }
    }

}