<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>首页</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/company.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        .centainer { padding: 20px;}
        .el-card__header { padding: 10px 20px;}
        .el-badge__content.is-fixed { right: 70px;top:60px;height:30px;line-height:30px;border-radius:30px;width:18px;z-index: 9;}
        .success .el-descriptions__header {
            border-left:5px solid #67c23a;
            color:#67c23a;
            line-height: 40px;
            padding-left:20px;
            margin-bottom: 0;
            margin-top: 20px;
            background: -ms-linear-gradient(left, #f0f9eb, #fff); /* IE 10+ */
            background: -moz-linear-gradient(left, #f0f9eb, #fff); /* Firefox */
            background: -webkit-linear-gradient(left, #f0f9eb, #fff); /* Chrome, Safari, Opera */
            background: linear-gradient(to right, #f0f9eb, #fff); /* 标准语法 */
        }
        .primary .el-descriptions__header {
            margin-top: 20px;
            border-left:5px solid #436CE6;
            color:#436CE6;
            line-height: 40px;
            padding-left:20px;
            background: -ms-linear-gradient(left, #F4F7FE, #fff); /* IE 10+ */
            background: -moz-linear-gradient(left, #F4F7FE, #fff); /* Firefox */
            background: -webkit-linear-gradient(left, #F4F7FE, #fff); /* Chrome, Safari, Opera */
            background: linear-gradient(to right, #F4F7FE, #fff); /* 标准语法 */
        }
        .total { background-color: #fff;border-radius: 5px; padding: 20px;}
        .total .num { line-height: 60px; font-size: 32px;}
        .total .item { background-color: #f5f5f5; padding: 10px; border-radius: 3px;}
        .total .item .num { font-size: 24px;}
        .announcement-wrapper { background-color: #fff;}
        .el-descriptions__header { margin-bottom: 0;}
        .announcement-wrapper {overflow: hidden;}
        .announcement-wrapper .announcement-scrollbar .announcement-content {border-bottom:1px dashed #f5f5f5;padding: 10px; cursor: pointer;}
    </style>
</head>
<body style="padding: 0;">
<div id="app" v-cloak>
    <div style="padding: 10px;">
        <el-row :gutter="20">
            <el-col :span="12">
                <div class="total">
                    <p>企业总数</p>
                    <p class="num">{$total.company.sum}</p>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <div class="item">
                                <span>达标企业数</span><br/>
                                <span class="num">{$total.company.sum1}</span>
                            </div>
                        </el-col>
                        <el-col :span="12">
                            <div class="item">
                                <span>评审中企业数</span><br/>
                                <span class="num">{$total.company.sum2}</span>
                            </div>
                        </el-col>
                        <!--<el-col :span="8">
                            <div class="item">
                                <span>过期未评审企业数</span><br/>
                                <span class="num">{$total.company.sum3}</span>
                            </div>
                        </el-col>-->
                    </el-row>
                </div>
            </el-col>
            <el-col :span="12">
                <div class="total">
                    <p>证书总数</p>
                    <p class="num">{$total.ca.sum}</p>
                    <el-row :gutter="24">
                        <el-col :span="6">
                            <div class="item">
                                <span>有效证书数</span><br/>
                                <span class="num">{$total.ca.sum1}</span>
                            </div>
                        </el-col>
                        <el-col :span="6">
                            <div class="item">
                                <span>待制证数</span><br/>
                                <span class="num">{$total.ca.sum2}</span>
                            </div>
                        </el-col>
                        <el-col :span="6">
                            <div class="item">
                                <span>过期证书数</span><br/>
                                <span class="num">{$total.ca.sum3}</span>
                            </div>
                        </el-col>
                        <el-col :span="6">
                            <div class="item">
                                <span>撤销证书数</span><br/>
                                <span class="num">{$total.ca.sum4}</span>
                            </div>
                        </el-col>
                    </el-row>
                </div>
            </el-col>
            <el-col style="height: 20px;"></el-col>
            <el-col :span="8">
                <el-card class="box-card my-card">
                    <div slot="header" class="clearfix">
                        <span>企业类型统计</span>
<!--                        <el-button style="float: right; padding: 3px 0;transform: rotate(90deg);color: #BFBFBF;" type="text" icon="el-icon-more"></el-button>-->
                    </div>
                    <div id="container1" style="height: 200px"></div>
                </el-card>
            </el-col>
            <el-col :span="8">
                <el-card class="box-card my-card">
                    <div slot="header" class="clearfix">
                        <span>证书类别统计</span>
<!--                        <el-button style="float: right; padding: 3px 0;transform: rotate(90deg);color: #BFBFBF;" type="text" icon="el-icon-more"></el-button>-->
                    </div>
                    <div id="container2" style="height: 200px"></div>
                </el-card>
            </el-col>
            <el-col :span="8">
                <el-card class="box-card my-card">
                    <div slot="header" class="clearfix">
                        <span>待整改企业</span>
<!--                        <el-button style="float: right; padding: 3px 0;transform: rotate(90deg);color: #BFBFBF;" type="text" icon="el-icon-more"></el-button>-->
                    </div>
                    <div class="announcement-wrapper">
                        <el-scrollbar class="announcement-scrollbar">
                            {volist name="result.company" id="item"}
                            <div class="announcement-content">
                                <span style="display: inline-block;float: left;margin-right: 10px;color: #AF3230;">●</span>
                                <span style="display: inline-block;width: 250px;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;">
                                <span>{$item.company_name}</span>
                            </span>
                                <span style="display: inline-block;width: 120px; float: right;">{$item.date}</span>
                            </div>
                            {/volist}
                        </el-scrollbar>
                    </div>
                </el-card>
            </el-col>
            <el-col :span="12" style="">
                <el-descriptions class="primary" title="通知公告">
                </el-descriptions>
                <div class="announcement-wrapper">
                    <el-scrollbar class="announcement-scrollbar">
                        {volist name="result.notify" id="item"}
                        <div class="announcement-content" @click="notifyInfo('{$item.id}')">
                            <span style="display: inline-block;float: left;margin-right: 10px;color: #AF3230;">●</span>
                            <span style="display: inline-block;width: 350px;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;">
                                <span>【{$item.mb_type}】</span>
                                <span>{$item.title}</span>
                            </span>
                            <span style="display: inline-block;width: 120px; float: right;">{$item.date}</span>
                        </div>
                        {/volist}
                    </el-scrollbar>
                </div>
            </el-col>
            <el-col :span="12" style="">
                <el-descriptions class="primary" title="证书到期提醒">
                </el-descriptions>
                <div class="announcement-wrapper">
                    <el-scrollbar class="announcement-scrollbar">
                        {volist name="result.ca" id="item"}
                        <div class="announcement-content">
                            <span style="display: inline-block;float: left;margin-right: 10px;color: #AF3230;">●</span>
                            <span style="display: inline-block;width: 250px;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;">{$item.company_name}</span>
                            <span style="display: inline-block;width: 120px; float: right;">{$item.ends}</span>
                        </div>
                        {/volist}
                    </el-scrollbar>
                </div>
            </el-col>
        </el-row>
    </div>
    <notify ref="notify"></notify>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/echarts/echarts.min.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                title: '',
                loading: false,
                data: [],
                config:[],
                container:{
                    container1:[
                        { value: 1200, name: '制造业' },
                        { value: 800, name: '服务业' },
                        { value: 500, name: '建筑业' },
                        { value: 346, name: '其他行业' }
                    ],
                    container2:[
                        { value: 1200, name: '制造业' },
                        { value: 800, name: '服务业' },
                        { value: 500, name: '建筑业' },
                        { value: 346, name: '其他行业' }
                    ],
                    container3:{
                        year:[],
                        value:[],
                    },
                },
                height: document.documentElement.clientHeight,
            };
        },
        components: {
            'notify': 'url:/general/toppingsoft/public/vue/notifyInfo.vue?v=1',
        },
        methods: {
            notifyInfo(id){
                this.$refs.notify.open(id);
            },
            getData:function(){
                var _this = this;
                axios.post('main', {_ajax:1}).then(function (res) {
                    if (res.data.code == 0) {
                        console.log(res.data.data.container)
                        _this.container = res.data.data.container;
                        _this.init_charts('','container1');
                        _this.init_charts('','container2');
                        _this.init_charts('','container3');
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                }).catch(function (error) {
                    console.log("出现错误:",error);
                });
            },
            init_charts:function(data,id){
                var _this = this;
                var offsetwidth = document.getElementById(id).offsetWidth-100;
                var option = {};
                if(id=='container1'){
                    option = {
                        color:['#37CAFB','#0091FF','#00B263','#FF1778'],
                        grid: {
                            left: 0,
                            right: 0,
                            bottom: 0,
                            top: 20,
                            containLabel: true
                        },
                        animation: false,
                        tooltip: {
                            trigger: 'item',
                            formatter: '{b}: {c} ({d}%)'
                        },
                        series: [{
                            type: 'pie',
                            radius: '70%',
                            data: _this.container.container1,
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }]
                    };
                }else if(id=='container2'){
                    option = {
                        color:['#37CAFB','#0091FF','#00B263','#FF1778'],
                        grid: {
                            left: 0,
                            right: 0,
                            bottom: 0,
                            top: 20,
                            containLabel: true
                        },
                        animation: false,
                        tooltip: {
                            trigger: 'item',
                            formatter: '{b}: {c} ({d}%)'
                        },
                        series: [{
                            type: 'pie',
                            radius: ['40%', '70%'],
                            data: _this.container.container2
                        }]
                    };
                }else if(id=='container3'){
                    option = {
                        grid: {
                            left: 0,
                            right: 0,
                            bottom: 0,
                            top: 20,
                            containLabel: true
                        },
                        animation: false,
                        tooltip: {
                            trigger: 'axis'
                        },
                        xAxis: {
                            type: 'category',
                            data: _this.container.container3.year
                        },
                        yAxis: {
                            type: 'value'
                        },
                        series: [{
                            data: _this.container.container3.value,
                            itemStyle: {
                                normal:{
                                    color:'#0092FF',
                                    borderRadius: 5,
                                },
                            },
                            label: {
                                show: true,
                                position: 'insideTop'
                            },
                            type: 'bar',
                            barWidth:'25px',
                        }]
                    };
                }
                var chart = echarts.init(document.getElementById(id));
                chart.setOption(option);
            },
        },
        mounted() {
            // this.init_charts('','container1');
            // this.init_charts('','container2');
            // this.init_charts('','container3');
            this.getData();
        }
    })
</script>


</body>
</html>