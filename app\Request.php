<?php
namespace app;

// 应用请求对象类
class Request extends \think\Request
{
    public function paramToInt($name, $defaultValue = null)
    {
        $p = parent::param($name, $defaultValue);
        return intval($p);
    }

    public function queryParamToInt($name, $defaultValue = null)
    {
        $p = parent::get($name, $defaultValue);
        return intval($p);
    }

    public function paramWithTrim($name, $defaultValue = null)
    {
        $p = parent::param($name, $defaultValue);
        return trim($p);
    }

    public function paramWidthBase64Decode($name, $defaultValue = null)
    {
        $p = parent::param($name, $defaultValue);
        return base64_decode($p);
    }
}
