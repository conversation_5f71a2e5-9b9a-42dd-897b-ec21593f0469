<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索功能测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-container { max-width: 800px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .test-section { margin-bottom: 30px; padding: 20px; background: #f9f9f9; border-radius: 6px; }
        .debug-info { background: #e8f4fd; padding: 10px; border-radius: 4px; margin-top: 10px; font-size: 12px; }
    </style>
</head>
<body>
    <div id="app" class="test-container">
        <h1>搜索功能测试页面</h1>
        
        <div class="test-section">
            <h3>1. 表搜索测试</h3>
            <p>测试前端过滤功能是否正常工作</p>
            
            <el-select 
                v-model="selectedTable" 
                placeholder="请选择数据表（支持搜索）" 
                style="width: 400px;"
                filterable
                :filter-method="filterTableOptions"
                clearable
                @change="onTableChange">
                <el-option
                    v-for="table in tableList"
                    :key="table.table_name"
                    :label="table.display_name"
                    :value="table.table_name">
                    <span style="float: left; font-weight: bold;">{{ table.table_name }}</span>
                    <span style="float: right; color: #8492a6; font-size: 12px" v-if="table.table_comment">{{ table.table_comment }}</span>
                </el-option>
            </el-select>
            
            <div class="debug-info">
                <strong>调试信息：</strong><br>
                当前选中表: {{ selectedTable }}<br>
                表总数: {{ tableList.length }}<br>
                最后搜索关键词: {{ lastSearchKeyword }}<br>
                过滤方法调用次数: {{ filterCallCount }}
            </div>
        </div>

        <div class="test-section" v-if="selectedTable">
            <h3>2. 字段搜索测试</h3>
            <p>测试字段实时过滤功能</p>
            
            <el-input
                v-model="fieldSearchKeyword"
                placeholder="搜索字段名、类型或备注..."
                prefix-icon="el-icon-search"
                clearable
                @input="filterFields"
                style="width: 300px; margin-bottom: 15px;">
            </el-input>
            
            <div style="border: 1px solid #ddd; padding: 10px; border-radius: 4px; max-height: 200px; overflow-y: auto;">
                <el-checkbox-group v-model="selectedFields">
                    <div v-for="field in filteredFieldList" :key="field.field_name" style="margin: 5px 0;">
                        <el-checkbox :label="field.field_name">
                            <span style="font-weight: bold;">{{ field.field_name }}</span>
                            <span style="color: #e67e22; margin: 0 5px;">({{ field.field_type }})</span>
                            <span style="color: #606266;">{{ field.field_comment }}</span>
                        </el-checkbox>
                    </div>
                </el-checkbox-group>
                
                <div v-if="filteredFieldList.length === 0 && fieldList.length > 0" style="text-align: center; color: #999; padding: 20px;">
                    未找到匹配的字段
                </div>
            </div>
            
            <div class="debug-info">
                <strong>字段调试信息：</strong><br>
                字段总数: {{ fieldList.length }}<br>
                过滤后字段数: {{ filteredFieldList.length }}<br>
                搜索关键词: "{{ fieldSearchKeyword }}"<br>
                已选字段: {{ selectedFields.length }} 个
            </div>
        </div>

        <div class="test-section">
            <h3>3. 手动测试</h3>
            <el-button @click="testFilterFunction" type="primary">测试过滤函数</el-button>
            <el-button @click="logTableData" type="info">打印表数据</el-button>
            <el-button @click="resetData" type="warning">重置数据</el-button>
            
            <div class="debug-info" v-if="testResult">
                <strong>测试结果：</strong><br>
                {{ testResult }}
            </div>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    selectedTable: '',
                    tableList: [
                        { table_name: 'user_info', display_name: 'user_info (用户信息表)', table_comment: '用户信息表' },
                        { table_name: 'user_role', display_name: 'user_role (用户角色表)', table_comment: '用户角色表' },
                        { table_name: 'product_info', display_name: 'product_info (产品信息表)', table_comment: '产品信息表' },
                        { table_name: 'order_detail', display_name: 'order_detail (订单详情表)', table_comment: '订单详情表' },
                        { table_name: 'system_config', display_name: 'system_config (系统配置表)', table_comment: '系统配置表' },
                        { table_name: 'log_operation', display_name: 'log_operation (操作日志表)', table_comment: '系统操作日志记录表' }
                    ],
                    fieldList: [],
                    filteredFieldList: [],
                    selectedFields: [],
                    fieldSearchKeyword: '',
                    lastSearchKeyword: '',
                    filterCallCount: 0,
                    testResult: ''
                }
            },
            methods: {
                // 前端表过滤方法
                filterTableOptions(value, option) {
                    this.filterCallCount++;
                    this.lastSearchKeyword = value;
                    
                    console.log('过滤方法被调用:', { value, option });
                    
                    if (!value) {
                        return true;
                    }
                    
                    const keyword = value.toLowerCase();
                    
                    // 获取当前选项的表信息
                    const table = this.tableList.find(t => t.table_name === option.value);
                    if (!table) {
                        console.log('未找到表:', option.value);
                        return false;
                    }
                    
                    const tableName = table.table_name.toLowerCase();
                    const tableComment = (table.table_comment || '').toLowerCase();
                    const displayName = table.display_name.toLowerCase();
                    
                    const result = tableName.includes(keyword) || 
                                   tableComment.includes(keyword) || 
                                   displayName.includes(keyword);
                    
                    console.log('过滤结果:', { table: table.table_name, keyword, result });
                    return result;
                },

                onTableChange() {
                    console.log('表选择改变:', this.selectedTable);
                    this.loadMockFields();
                },

                loadMockFields() {
                    const mockFields = {
                        'user_info': [
                            { field_name: 'user_id', field_type: 'VARCHAR(32)', field_comment: '用户唯一标识ID' },
                            { field_name: 'user_name', field_type: 'VARCHAR(50)', field_comment: '用户登录名称' },
                            { field_name: 'real_name', field_type: 'VARCHAR(50)', field_comment: '用户真实姓名' },
                            { field_name: 'email', field_type: 'VARCHAR(100)', field_comment: '用户邮箱地址' },
                            { field_name: 'phone', field_type: 'VARCHAR(20)', field_comment: '手机号码' },
                            { field_name: 'birthday', field_type: 'DATE', field_comment: '出生日期' }
                        ],
                        'user_role': [
                            { field_name: 'role_id', field_type: 'INT(11)', field_comment: '角色唯一ID' },
                            { field_name: 'role_name', field_type: 'VARCHAR(30)', field_comment: '角色名称' },
                            { field_name: 'permissions', field_type: 'TEXT', field_comment: '权限列表JSON' }
                        ]
                    };
                    
                    this.fieldList = mockFields[this.selectedTable] || [];
                    this.filteredFieldList = this.fieldList;
                    this.fieldSearchKeyword = '';
                    this.selectedFields = [];
                },

                filterFields() {
                    const keyword = this.fieldSearchKeyword.toLowerCase().trim();
                    
                    if (!keyword) {
                        this.filteredFieldList = this.fieldList;
                        return;
                    }
                    
                    this.filteredFieldList = this.fieldList.filter(field => {
                        const fieldName = field.field_name.toLowerCase();
                        const fieldType = field.field_type.toLowerCase();
                        const fieldComment = (field.field_comment || '').toLowerCase();
                        
                        return fieldName.includes(keyword) || 
                               fieldType.includes(keyword) || 
                               fieldComment.includes(keyword);
                    });
                },

                testFilterFunction() {
                    const testKeyword = 'user';
                    const testOption = { value: 'user_info' };
                    const result = this.filterTableOptions(testKeyword, testOption);
                    this.testResult = `测试关键词 "${testKeyword}" 对表 "user_info" 的过滤结果: ${result}`;
                },

                logTableData() {
                    console.log('表数据:', this.tableList);
                    this.testResult = '表数据已打印到控制台，请按F12查看';
                },

                resetData() {
                    this.selectedTable = '';
                    this.fieldList = [];
                    this.filteredFieldList = [];
                    this.selectedFields = [];
                    this.fieldSearchKeyword = '';
                    this.lastSearchKeyword = '';
                    this.filterCallCount = 0;
                    this.testResult = '';
                }
            }
        });
    </script>
</body>
</html>
