<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>评审打分</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/company.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        .my-autocomplete li{line-height: normal;padding: 7px;}
        .my-autocomplete li .name{text-overflow: ellipsis;overflow: hidden;}
        .my-autocomplete li .addr{font-size: 12px;color: #b4b4b4;}
        .my-autocomplete li .highlighted{color: #ddd;}
        .mytable {border-collapse:collapse;width: 100%;}
        .mytable thead tr td {border: 1px solid #333;font-weight: 700; padding: 10px;background: #f5f7fa;text-align: center;}
        .mytable tbody tr th {border: 1px solid #333;font-weight: 700; padding: 10px;text-align: center;font-size: 16px;height: 20px;}
        .mytable tbody tr td {border: 1px solid #333; padding: 10px;text-align: center;font-size: 16px;}
        .mytable .active td{ background: #f2f2f2;}
        .mytable tbody tr td {font-family: 宋体;text-align: left;height: 20px;}
        .mytable tbody tr td p{line-height: 30px;}
        .bufuIndex{
            display: inline-block;
            width: 30px;
            height: 50px;
            line-height: 50px;
            text-align: left;
        }
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div id="print-content">
        <h3 style="height: 60px;line-height: 60px;text-align: center;font-size: 22px;">现场评审报告</h3>
        <div style="width: 500px;margin: 0 auto;overflow:hidden;font-size: 16px;">
            <div style="width: 100%;height: 20px;float: left;"></div>
            <div style="float: left;height: 30px;line-height: 30px;">评审单位</div>
            <div style="float: left;width: 410px;border-bottom: 2px solid #000;height: 30px;line-height: 30px;font-weight: 700;text-align: center;">成都市城市安全管理研究院</div>
            <div style="float: left;width: 100%;height: 20px;"></div>
            <div style="float: left;height: 30px;line-height: 30px;">定级企业（盖章）</div>
            <div style="float: left;width: 345px;border-bottom: 2px solid #000;height: 30px;line-height: 30px;font-weight: 700;text-align: center;">{{data.company_name}}</div>
            <div style="float: left;width: 100%;height: 20px;"></div>
            <div style="float: left;height: 30px;line-height: 30px;">行&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;业</div>
            <div style="float: left;width: 175px;border-bottom: 2px solid #000;height: 30px;line-height: 30px;font-weight: 700;text-align: center;">{{data.industry}}</div>
            <div style="float: left;height: 30px;line-height: 30px;">专&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;业</div>
            <div style="float: left;width: 175px;border-bottom: 2px solid #000;height: 30px;line-height: 30px;font-weight: 700;text-align: center;">{{data.specialty}}</div>
            <div style="float: left;width: 100%;height: 20px;"></div>
            <div style="float: left;height: 30px;line-height: 30px;">评审性质</div>
            <div style="float: left;width: 170px;border-bottom: 2px solid #000;height: 30px;line-height: 30px;font-weight: 700;text-align: center;">{{data.type}}</div>
            <div style="float: left;height: 30px;line-height: 30px;">评审等级</div>
            <div style="float: left;width: 170px;border-bottom: 2px solid #000;height: 30px;line-height: 30px;font-weight: 700;text-align: center;">{{data.level}}</div>
            <div style="float: left;width: 100%;height: 20px;"></div>
            <div style="float: left;height: 30px;line-height: 30px;">评审日期</div>
            <div style="float: left;width: 120px;border-bottom: 2px solid #000;height: 30px;line-height: 30px;font-weight: 700;text-align: center;">{{data.year}}</div>
            <div style="float: left;height: 30px;line-height: 30px;">年</div>
            <div style="float: left;width: 120px;border-bottom: 2px solid #000;height: 30px;line-height: 30px;font-weight: 700;text-align: center;">{{data.month}}</div>
            <div style="float: left;height: 30px;line-height: 30px;">月</div>
            <div style="float: left;width: 120px;border-bottom: 2px solid #000;height: 30px;line-height: 30px;font-weight: 700;text-align: center;">{{data.day}}</div>
            <div style="float: left;height: 30px;line-height: 30px;">日</div>
        </div>
        <table class="mytable" style="margin-top: 20px;">
            <tbody>
            <tr>
                <th style="width: 120px;"><label class="my-label">单位名称</label></th>
                <td colspan="5" style="width: 700px;"><div class="my-online">{{data.company_name}}</div></td>
            </tr>
            <tr>
                <th style="width: 120px;"><label class="my-label">单位地址</label></th>
                <td colspan="5" style="width: 700px;"><div class="my-online">{{data.address}}</div></td>
            </tr>
            <tr>
                <th style="width: 120px;"><label class="my-label">法定代表人</label></th>
                <td style="width: 200px;"><div class="my-online">{{data.legal}}</div></td>
                <th style="width: 120px;"><label class="my-label">手机</label></th>
                <td style="width: 200px;"><div class="my-online">{{data.legal_mobile}}</div></td>
                <th style="width: 120px;"><label class="my-label">邮箱</label></th>
                <td style="width: 500px;"><div class="my-online">{{data.legal_email}}</div></td>
            </tr>
            <tr>
                <th rowspan="2" style="width: 120px;"><label class="my-label">联&nbsp;&nbsp;&nbsp;系&nbsp;&nbsp;&nbsp;人</label></th>
                <td rowspan="2" style="width: 200px;"><div class="my-online">{{data.manager}}</div></td>
                <th style="width: 120px;"><label class="my-label">电话</label></th>
                <td style="width: 200px;"><div class="my-online">{{data.contacts_tel}}</div></td>
                <th style="width: 120px;"><label class="my-label">传真</label></th>
                <td style="width: 200px;"><div class="my-online">{{data.contacts_email}}</div></td>
            </tr>
            <tr>
                <th style="width: 120px;"><label class="my-label">手机</label></th>
                <td style="width: 200px;"><div class="my-online">{{data.manager_mobile}}</div></td>
                <th style="width: 120px;"><label class="my-label">电子邮箱</label></th>
                <td style="width: 200px;"><div class="my-online">{{data.manager_email}}</div></td>
            </tr>
            <tr>
                <th :rowspan="data.experts.length+1" style="width: 120px;"><label class="my-label">评审组成员</label></th>
                <th style="width: 120px;"><label class="my-label"></label></th>
                <th style="width: 120px;"><label class="my-label">姓名</label></th>
                <th style="width: 120px;"><label class="my-label">单位/职务/职称</label></th>
                <th style="width: 120px;"><label class="my-label">电话</label></th>
                <th style="width: 120px;"><label class="my-label">备注</label></th>
            </tr>
            <tr>
                <th style="width: 120px;"><label class="my-label">技术组长</label></th>
                <td style="width: 200px;"><div class="my-online">{{data.experts[0].expert_name}}</div></td>
                <td style="width: 200px;"><div class="my-online">{{data.experts[0].unit_name}}</div></td>
                <td style="width: 200px;"><div class="my-online">{{data.experts[0].mobile}}</div></td>
                <td style="width: 200px;"><div class="my-online"></div></td>
            </tr>
            <tr v-for="(item,k) in data.experts" v-if="k>0">
                <th v-if="k==1" :rowspan="data.experts.length-1" style="width: 120px;"><label class="my-label">成员</label></th>
                <td style="width: 200px;"><div class="my-online">{{item.expert_name}}</div></td>
                <td style="width: 200px;"><div class="my-online">{{item.unit_name}}</div></td>
                <td style="width: 200px;"><div class="my-online">{{item.mobile}}</div></td>
                <td style="width: 200px;"><div class="my-online"></div></td>
            </tr>
            <tr v-for="(item,k) in data.citys">
                <th v-if="k==0" colspan="2" :rowspan="data.citys.length" style="width: 120px;"><label class="my-label">(区)市、县应急局人员</label></th>
                <td style="width: 200px;"><div class="my-online">{{item.name}}</div></td>
                <td style="width: 200px;"><div class="my-online">{{item.unit}}</div></td>
                <td style="width: 200px;"><div class="my-online">{{item.mobile}}</div></td>
                <td style="width: 200px;"><div class="my-online"></div></td>
            </tr>
            <tr>
                <th colspan="6" style="width: 120px;"><label class="my-label">企业标准化运行情况概述</label></th>
            </tr>
            <tr>
                <td colspan="6" style="width: 200px;">
                    <p style="white-space:pre-line;text-indent:2em;">{{data.gaishu}}</p>
                </td>
            </tr>
            </tbody>
        </table>
        <table class="mytable">
            <tbody>
            <tr>
                <th colspan="8" style="width: 120px;"><label class="my-label">现场评审项目与得分</label></th>
            </tr>
            <tr>
                <th style="width: 120px;"><label class="my-label">序号</label></th>
                <th style="width: 200px;"><label class="my-label">评审类目</label></th>
                <th style="width: 120px;"><label class="my-label">标准分</label></th>
                <th style="width: 120px;"><label class="my-label">得分</label></th>
                <th style="width: 120px;"><label class="my-label">得分率</label></th>
                <th style="width: 120px;"><label class="my-label">扣分</label></th>
                <th style="width: 120px;"><label class="my-label">缺项分</label></th>
                <th style="width: 120px;"><label class="my-label">评审专家</label></th>
            </tr>
            <tr v-for="(item,k) in data.review_list">
                <td><div class="my-online">{{k+1}}</div></td>
                <td><div class="my-online">{{item.element_name}}</div></td>
                <td><div class="my-online">{{item.total}}</div></td>
                <td><div class="my-online">{{item.score}}</div></td>
                <td><div class="my-online">{{item.rate}}%</div></td>
                <td><div class="my-online">{{item.deduct}}</div></td>
                <td><div class="my-online">{{item.miss}}</div></td>
                <td><div class="my-online">{{item.expert_name}}</div></td>
            </tr>
            </tbody>
        </table>
        <table class="mytable">
            <tbody>
            <tr>
                <td colspan="8"><div class="my-online">注：行业标准化评审类目与此表不同的，适用其行业评审类目。</div></td>
            </tr>
            <tr>
                <th><label class="my-label">标准分<br/>（汇总）</label></th>
                <td><div class="my-online">{{data.total}}</div></td>
                <th><label class="my-label">缺项分<br/>（汇总）</label></th>
                <td><div class="my-online">{{data.miss}}</div></td>
                <th><label class="my-label">笔试平均<br/>成绩（分）</label></th>
                <td><div class="my-online">{{data.bishi}}</div></td>
                <th><label class="my-label">实得分<br/>（汇总）</label></th>
                <td><div class="my-online">{{data.score}}</div></td>
            </tr>
            <tr>
                <th><label class="my-label">总得分<br/>（分）</label></th>
                <td colspan="7"><div class="my-online">{{data.score}}</div></td>
            </tr>
            </tbody>
        </table>
        <table class="mytable" style="border: 2px solid #000;">
            <tbody>
            <tr>
                <th colspan="2"><label class="my-label">现场评审结果</label></th>
            </tr>
            <tr>
                <td><div class="my-online">是否达到拟定等级条件：<el-radio v-model="data.status" label="是">是</el-radio><el-radio v-model="data.status" label="否">否</el-radio></div></td>
                <td><div class="my-online">现场评审得分（百分制）：{{data.scores}}</div></td>
            </tr>
            <tr>
                <td colspan="2">
                    <p>现场评审组组长签字：</p>
                    <p style="margin-bottom: 40px;">成员签字：</p>
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <p>现场评审情况：</p>
                    <p style="white-space:pre-line;text-indent:2em;">{{data.qingkuang}}</p>
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <p>现场评审不符合项：</p>
                    <template v-for="(item,key) in data.reform">
                        <p style="white-space:pre-line;padding-left:2em;"><span class="bufuIndex">{{key+1}}.</span>{{data.reform[key]}}</p>
                    </template>
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <p>工作建议（整改建议）：</p>
                    <p style="white-space:pre-line;text-indent:2em;">{{data.jianyi}}</p>
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <p>申请定级企业意见（整改承诺）：</p>
                    <p>主要负责人签字：</p>
                    <p>（企业盖章）</p>
                    <p style="text-align: right;margin: 20px 0;">{{data.year}}&nbsp;年&nbsp;&nbsp;{{data.month}}&nbsp;月&nbsp;&nbsp;{{data.day}}&nbsp;日</p>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.all.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                title: '新增',
                id: '{$id}',
                index: 1,
                data: {
                },
                param: [],
                visible: false,
                loading: true,
                dialogImageUrl:'',
                dialogVisible:false,
                height: document.documentElement.clientHeight - 155,
            };
        },
        methods: {
            //数据加载
            getData() {
                var _this = this;
                var param = {};
                param.id = _this.id;
                param._ajax = 1;
                axios.post('baogaodayin', param).then(function (res) {
                    if (res.data.code == 0) {
                        _this.data = res.data.data;
                        // _this.form = res.data.data.content[0];
                        _this.$nextTick(() => {
                            // 这里的代码会在DOM更新完成后执行
                            _this.loading = false;
                            setTimeout(() => {
                                window.print();
                                window.close();
                            }, 1000);
                        });
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
        },
        mounted() {
            //获取列表
            this.getData();
        }
    })
</script>


</body>
</html>