<?php

namespace app\model;

use think\facade\Db;
use think\Model;

class TriggerModel extends Model
{

    //获取流程数据
    public static function getFlowData($flow_id,$run_id,$flowconfig){
        $children = Db::query("SHOW TABLES LIKE 'bpm_data_{$flow_id}_child'");
        $list = $flowconfig['list'];
        if($children){
            $flow_data = Db::table("bpm_data_".$flow_id)->alias('a')
                ->leftJoin("bpm_data_".$flow_id.'_child b','a.run_id = b.run_id')
                ->where('a.run_id',$run_id)->field('b.*,a.*')->find();
        }else{
            $flow_data = Db::table("bpm_data_".$flow_id)->where('run_id',$run_id)->find();
        }
        if($list){
            foreach ($list as $k=>$v){
                $flow_data['list'][$k] = Db::table($v['flow_table'])->where('run_id',$run_id)->order('id')->select()->toArray();
                $listchildren = Db::query("SHOW TABLES LIKE '{$v['flow_table']}_child'");
                if($listchildren){
                    $listchild = Db::table($v['flow_table'].'_child')->where('run_id',$run_id)->order('id')->select()->toArray();
                    foreach ($flow_data['list'][$k] as $k1=>$v1){
                        $listchild[$k1]['begin_user'] = $flow_data['begin_user'];
                        $flow_data['list'][$k][$k1] = array_merge($v1,$listchild[$k1]);
                    }
                }
            }
        }
        return $flow_data;
    }

    //转存流程数据（通用）
    public static function saveData($flow_data,$flowconfig = [],$data = [],$listdata = []){
        Db::startTrans();
        try{
            $main_data = self::fieldsData($flowconfig['fields'],$flow_data,$data);
            $main = Db::table($flowconfig['table'])->where('run_id',$flow_data['run_id'])->find();
            if(!empty($main['id'])){
                Db::table($flowconfig['table'])->where('id',$main['id'])->update($main_data);
            }else{
                $main_data['run_id'] = $flow_data['run_id'];
                $main['id'] = Db::table($flowconfig['table'])->insertGetId($main_data);
            }
            if($flowconfig['list']&&!empty($flow_data['list'])){
                foreach ($flowconfig['list'] as $key=>$value){
                    $list = Db::table($value['table'])->where([$value['main_field']=>$main['id']])->order('id')->select()->toArray();
                    if(count($flow_data['list'][$key])>=count($list)){
                        foreach ($flow_data['list'][$key] as $k=>$v){
                            $list_data = self::fieldsData($value['fields'],$v,$listdata);
                            if(empty($list[$k])){
                                $list_data['main_id'] = $main['id'];
                                Db::table($value['table'])->insert($list_data);
                            }else{
                                Db::table($value['table'])->where('id',$list[$k]['id'])->update($list_data);
                            }
                        }
                    }else{
                        foreach ($list as $k=>$v){
                            $list_data = self::fieldsData($value['fields'],$v,$listdata);
                            if(empty($flow_data['list'][$key][$k])){
                                Db::table($value['table'])->where('id',$v['id'])->delete();
                            }else{
                                Db::table($value['table'])->where('id',$v['id'])->update($list_data);
                            }
                        }
                    }
                }
            }
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            return ['code' => 1001, 'msg' => $e->getMessage()];
        }
    }

    public static function fieldsData($fields,$flow_data,$data){
        $main_data = [];
        foreach ($fields as $k=>$v){
            $f = explode(':',$v);
            switch ($f[0]){
                case 'flow':
                    $value = $flow_data[$f[1]];
                    break;
                case 'string':
                    $value = $f[1];
                    break;
                case 'user':
                    $user = Db::table('td_user')->alias('a')->leftJoin('department b','a.DEPT_ID = b.DEPT_ID')->where(['USER_ID'=>$flow_data['begin_user']])->find();
                    $value = $user[$f[1]];
                    break;
                default:
                    $value = $flow_data;
                    foreach ($f as $v1){
                        $value = $value[$v1];
                    }
                    break;
            }
            $main_data[$k] = $value;
        }
        foreach ($data as $k=>$v){
            $main_data[$k] = $v;
        }
        return $main_data;
    }

    //资产领用申请
    public static function propertyReceive($params,$flow_data,$flowconfig = [])
    {
        switch ($params['flow_prcs']){
            case 1://流程开始步骤
                $data['status'] = 1;
                break;
            case 5://流程结束步骤
                $data['status'] = 2;
                break;
            case 8://流程结束步骤
                $data['status'] = 7;
                break;
            default:
                break;
        }
        return self::saveData($flow_data,$flowconfig,$data);
    }

    //资产领用申请
    public static function taskJss($params,$flow_data,$flowconfig = [])
    {
        $work_id = $flow_data[$flowconfig['fields']['work_id']];
        if(!empty($work_id)){
            $prcs = Db::table('bpm_run_prcs')->where(['RUN_ID'=>$flow_data['run_id']])->field('USER_ID user_id')->select()->toArray();
            foreach ($prcs as $v){
                $u = Db::table('top_task_work_user')->where(['work_id'=>$work_id,'user_id'=>$v['user_id']])->find();
                if(empty($u)){
                    Db::table('top_task_work_user')->insert(['work_id'=>$work_id,'user_id'=>$v['user_id'],'model'=>'check','run_id'=>$flow_data['run_id']]);
                }
            }
        }
        return self::saveData($flow_data,$flowconfig);
    }

    //加班申请
    public static function holidays($params,$flow_data,$flowconfig = [])
    {
        foreach ($flow_data['list'] as $k=>$v){
            if($v['data_m963']=='是'){
                unset($flow_data['list'][$k]);
            }
        }
        return self::saveData($flow_data,$flowconfig,$data = [],$listdata = ['run_id'=>$flow_data['run_id']]);
    }

    //请假申请
    public static function vacation($params,$flow_data,$flowconfig = [])
    {
        if(in_array($flow_data['data_m298'],['带薪年休假','事假'])){
            $re = Db::table('top_crm_list_config_leave')->where(['charge_user_id'=>$flow_data['begin_user'],'leave_type'=>$flow_data['data_m298']])->inc('yx',$flow_data['data_m303']);
        }else if($flow_data['data_m298']=='调休'){
            $date = date('Y-m-d',strtotime('-3month'));
            $where = [
                ['charge_user_id','=',$flow_data['begin_user']],
                ['date','>=',$date],
            ];
            $days = $flow_data['data_m303'];
            $res = Db::table('top_crm_list_holidays')->where($where)->order('date')->select()->toArray();
            foreach ($res as $v){
                if($v['num']-$v['yixiu']>=$days){
                    Db::table('top_crm_list_holidays')->where(['id'=>$v['id']])->inc('yx',$days);
                    break;
                }else{
                    Db::table('top_crm_list_holidays')->where(['id'=>$v['id']])->inc('yx',$v['num']);
                    $days -= $v['num'];
                }
            }
        }else{
            $re = Db::table('top_crm_list_config_leave')->where(['charge_user_id'=>$flow_data['begin_user'],'leave_type'=>$flow_data['data_m298']])->find();
            $user = Db::table('td_user')->where(['USER_ID'=>$flow_data['begin_user']])->find();
            if(empty($re)){
                $data = [
                    'date' => date('Y-m-d'),
                    'charge_user_id' => $user['USER_ID'],
                    'charge_user_name' => $user['USER_NAME'],
                    'charge_dept_id' => $user['DEPT_ID'],
                    'charge_dept_name' => $flow_data['data_m297'],
                    'leave_type' => $flow_data['data_m298'],
                    'num' => 0,
                    'wx' => 0,
                    'yx' => $flow_data['data_m303'],
                ];
                Db::table('top_crm_list_config_leave')->insert($data);
            }else{
                Db::table('top_crm_list_config_leave')->where(['id'=>$re['id']])->inc('yx',$flow_data['data_m303']);
            }
        }
        return self::saveData($flow_data,$flowconfig);
    }

}