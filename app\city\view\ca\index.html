<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>证书管理</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .el-table .danger-row {
            background: #fbc4c4;
        }
        .el-table .warning-row {
            background: oldlace;
        }
        .el-table .success-row {
            background: #f0f9eb;
        }
        .el-badge__content.is-fixed { right: 20px;z-index: 9;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">
        <el-form :inline="true" size="mini" :model="searchFrom" class="form-inline" @submit.native.prevent>
            <el-form-item>
                <el-radio-group @change="statusChange" size="mini" v-model="searchFrom.status">
                    <el-radio-button label="">全部</el-radio-button>
                    <el-radio-button label="1">未生效</el-radio-button>
                    <el-radio-button label="7">生效中</el-radio-button>
                    <el-radio-button label="8">已过期</el-radio-button>
                    <el-radio-button label="9">已撤销</el-radio-button>
                </el-radio-group>
            </el-form-item>
            <el-form-item>
                <el-input v-model="searchFrom.title" size="mini" placeholder="企业名称/证书编号"></el-input>
            </el-form-item>
            <el-form-item>
                <el-select size="mini" v-model="searchFrom.level" placeholder="证书等级">
                    <el-option label="证书等级" value=""></el-option>
                    <el-option label="一级" value="一级"></el-option>
                    <el-option label="二级" value="二级"></el-option>
                    <el-option label="三级" value="三级"></el-option>
                    <el-option label="小型" value="小型"></el-option>
                    <el-option label="微型" value="微型"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-date-picker
                        v-model="searchFrom.year"
                        type="year"
                        format="yyyy"
                        value-format="yyyy"
                        placeholder="发证年度">
                </el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-select size="mini" v-model="searchFrom.area" placeholder="区县">
                    <el-option label="区县" value=""></el-option>
                    {volist name="area" id="item"}
                    <el-option label="{$item.name}" value="{$item.id}"></el-option>
                    {/volist}
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-select size="mini" v-model="searchFrom.industry" placeholder="行业">
                    <el-option label="行业" value=""></el-option>
                    {volist name="industry" id="item"}
                    <el-option label="{$item.name}" value="{$item.name}"></el-option>
                    {/volist}
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="getData()" size="mini">查询</el-button>
                <el-button @click="reset()" size="mini">重置</el-button>
                <el-button v-if="searchFrom.status==1" type="primary" @click="publicity()" size="mini">公示</el-button>
            </el-form-item>
            <el-form-item style="float: right">
                <el-button :loading="loading" type="primary" size="mini" @click="export1">导出</el-button>
            </el-form-item>
        </el-form>
        <el-table border
                  v-loading="loading"
                  :data="data"
                  @selection-change="handleSelectionChange"
                  style="width: 100%;margin-bottom: 20px;"
                  ref="qtable"
                  :height="height"
                  size="small">
            <el-table-column
                    v-if="searchFrom.status==1"
                    type="selection"
                    :selectable="checkSelectable"
                    width="55">
            </el-table-column>
            <el-table-column
                    type="index"
                    label="序号"
                    align="center"
                    width="50">
            </el-table-column>
            <el-table-column
                    prop="company_name"
                    label="企业名称"
                    align="center"
                    show-overflow-tooltip
                    min-width="200">
            </el-table-column>
            <el-table-column
                    prop="specialty"
                    label="行业/专业"
                    align="center"
                    show-overflow-tooltip
                    min-width="200">
                <template slot-scope="scope">
                    {{scope.row.industry}}/{{scope.row.specialty}}
                </template>
            </el-table-column>
            <el-table-column
                    prop="code"
                    label="证书编号"
                    align="center"
                    width="200">
            </el-table-column>
            <el-table-column
                    prop="ends"
                    label="有效期"
                    align="center"
                    width="120">
            </el-table-column>
            <el-table-column
                    prop="mb_status"
                    label="证书状态"
                    align="center"
                    show-overflow-tooltip
                    width="120">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.status==1" type="info">未生效</el-tag>
                    <el-tag v-if="scope.row.status==7" type="success">生效中</el-tag>
                    <el-tag v-if="scope.row.status==8" type="warning">已过期</el-tag>
                    <el-tag v-if="scope.row.status==9" type="danger">已撤销</el-tag>
                </template>
            </el-table-column>
            <el-table-column
                    prop="publicity"
                    label="公示状态"
                    align="center"
                    show-overflow-tooltip
                    width="120">
                <template slot-scope="scope">
                    {{scope.row.publicity_status}}
                </template>
            </el-table-column>
            <el-table-column
                    prop="notify"
                    label="公告状态"
                    align="center"
                    show-overflow-tooltip
                    width="120">
                <template slot-scope="scope">
                    {{scope.row.notify_status}}
                </template>
            </el-table-column>
            <el-table-column
                    label="操作"
                    align="center"
                    width="120">
                <template slot-scope="scope">
                    <el-button v-if="scope.row.status==7" type="danger" @click="revoke(scope.row)" size="mini">撤销</el-button>
                    <!--                    <el-button v-else  @click="check(scope.row)" size="mini">详情</el-button>-->
                </template>
            </el-table-column>
        </el-table>
        <!--分页条total, sizes, prev, pager, next, jumper-->
        <div class="block">
            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="page"
                    :page-sizes="[10, 20, 50, 100, 500, 1000]"
                    :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total">
            </el-pagination>
        </div>
    </div>
    <publicity ref="publicity" @ok="getData()"></publicity>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>
<script src="__PUBLIC__/static/js/html2canvas.js"></script>

<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.all.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                title: '新增',
                searchFrom: {
                    status: 1,
                    title: '',
                    industry: '',
                    area: '',
                },
                thisrow: {},
                thisuser: '',
                data: [],
                filelist: [],
                area: [],
                page: 1,
                pageSize: 20,
                total: 0,
                deptData: [],
                multipleSelection: [],
                dialogFormVisible: false,
                visible: false,
                loading: true,
                form: {},
                formRules: {
                    form_name: [{required: true, message: "请输入表单名称", trigger: "blur"}],
                },
                ueObj: null,
                height: document.documentElement.clientHeight - 155,
            };
        },
        components: {
            'publicity': 'url:/general/toppingsoft/app/city/view/ca/vue/publicity.vue?v=1',
        },
        watch: {
            $route() {
                //切换标签时
                this.handleRefreshTable();
            }
        },
        created() {
            let _this = this;
            //窗口改变时
            window.onresize = function () {
                _this.handleRefreshTable();
            };
        },
        updated() {
            //数据改变时
            this.handleRefreshTable();
        },
        methods: {
            handleSelectionChange(val) {
                this.multipleSelection = val;
            },
            checkSelectable(row,rowIndex){
                return row.publicity_id==0;
            },
            tableRowClassName({row, rowIndex}) {
                if (row.back) {
                    return row.back;
                }
                return '';
            },
            handleClick(tab, event) {
                this.searchFrom = {
                    our: '',
                    status: '',
                    level: '',
                    type: '',
                    company: '',
                    charge_user_name: '',
                };
                this.getData();
            },
            statusChange() {
                this.page = 1;
                this.getData();
            },
            handleSizeChange: function (val) {
                this.pageSize = val;
                this.getData();
                console.log('每页 ${val} 条');
            },
            handleCurrentChange: function (val) {
                this.page = val;
                this.getData();
                console.log('当前页: ${val}');
            },
            check(row) {
                this.thisrow = row;
                this.$refs.check.open(row);
            },
            publicity() {
                if( this.multipleSelection == undefined || this.multipleSelection == null || this.multipleSelection == '' )
                {
                    this.$alert('请选择证书');
                    return false;
                }
                this.$refs.publicity.open(this.multipleSelection);
            },
            //数据初始化
            reset() {
                this.searchFrom.title = '';
                this.searchFrom.area = '';
                this.searchFrom.industry = '';
                this.searchFrom.level = '';
                this.searchFrom.year = '';
                this.page = 1;
                this.pageSize = 20;
                this.getData();
            },
            revoke(row){
                var _this = this;
                _this.$prompt('请填写撤销原因', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    inputPattern: /^\S{5,}$/,
                    inputErrorMessage: '撤销原因不少于5个字'
                }).then(({ value }) => {
                    _this.$confirm(row.company_name+'('+row.code+')', '确定撤销证书？', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                    }).then(() => {
                        _this.loading = true;
                        var param = {};
                        param.id = row.id;
                        param.remark = value;
                        axios.post('revoke', param).then(function (res) {
                            _this.loading = false;
                            if (res.data.code == 0) {
                                _this.$message({
                                    message: res.data.msg,
                                    type: "success"
                                });
                                _this.getData();
                            } else {
                                _this.$message({
                                    message: res.data.msg,
                                    type: "error"
                                });
                            }
                        }).catch(function (error) {
                            console.log(error);
                        });
                    }).catch(function (error){
                        console.log(error);
                    });

                }).catch(function (error){
                    console.log(error);
                });
            },
            //数据加载
            getData() {
                var _this = this;
                var param = _this.searchFrom;
                param._ajax = 1;
                param.page = _this.page;
                param.limit = _this.pageSize;
                param.our = 1;
                axios.post('index', param).then(function (res) {
                    if (res.data.code == 0) {
                        _this.data = res.data.data.data;
                        _this.page = res.data.data.current_page;
                        _this.pageSize = res.data.data.per_page;
                        _this.total = res.data.data.total;
                        _this.$nextTick(() => {
                            // 这里的代码会在DOM更新完成后执行
                            _this.loading = false;
                        });
                        _this.handleRefreshTable();
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            import1: function () {
                this.$refs.import1.templateUrl = 'importTemplate';
                this.$refs.import1.submitUrl = 'import';
                this.$refs.import1.title = '企业信息导入';
                this.$refs.import1.open();
            },
            export1: function () {
                let _this = this;
                let where = "";
                let type = '';
                //获得where
                where = '';
                for (let index in this.searchFrom) {
                    if (this.searchFrom[index] !== '' && this.searchFrom[index] !== [] && this.searchFrom[index] !== {}) {
                        let str = "";
                        if (index == 'type') {
                            for (let i in this.searchFrom.type) {
                                type += this.searchFrom.type[i] + ',';
                            }
                            str += 'type=' + type;
                        } else {
                            str += index + '=' + this.searchFrom[index];
                        }
                        where += "&" + str;
                    }
                }
                let url = "index?excel=1&" + where;
                //window.open(url);
                location.href = url;
            },
            handleRefreshTable: function () {
                this.$nextTick(function () {
                    var refList = this.$refs;
                    if (refList) {
                        for (var i of Object.keys(refList)) {
                            if (refList[i] && refList[i].doLayout) {
                                refList[i].doLayout();
                            }
                        }
                    }
                });
            },
        },
        mounted() {
            //获取列表
            this.getData();

        }
    })
</script>


</body>
</html>