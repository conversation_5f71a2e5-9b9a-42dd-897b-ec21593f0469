<?php
declare (strict_types=1);

namespace app\service;

use app\admin\model\TopAreaUserModel;
use app\admin\model\TopCityUserModel;
use app\admin\model\TopOrgUserModel;
use think\facade\Session;
use traits\think\Instance;

class Login extends \think\Service
{
	/**
	 * 注册服务
	 *
	 * @return mixed
	 */
	public function register()
	{
		$this->app->bind('login', Login::class);
	}

	/**
	 * 执行服务
	 *
	 * @return mixed
	 */
	public function boot()
	{
		//
	}


	/**
	 * 用户登录
	 * @param string $username 用户名
	 * @param string $password 密码
	 * @return bool 登录是否成功
	 */
	public function login($modelName, $username, $input_password)
	{
		$model = new $modelName;
		$user = $model::where('username', $username)->find();

		if (empty($user)) return '用户不存在';
		if ($user->status == 0) return '账号已禁用';
		if ($user->status == 2) return '账号已删除';
		if ($user->status == 3) return '账号未审核';
		if ($user->status == 1) {
			$password = $user['password'];
			if ($password !== crypt($input_password, $password)) return '密码错误';
			// 登录成功，保存用户信息到会话中
			$_SESSION[$model->sessionName] = $user;
			$_SESSION[$model->sessionName]['user_id'] = $user->id;
			$_SESSION[$model->sessionName]['user_name'] = $user->name;
			return true;
		}
		return '账号无法使用';
	}

	/**
	 * 检查用户是否登录
	 * @return bool 用户是否登录
	 */
	public function isLogin($modelName)
	{
		$model = new $modelName;
		return $_SESSION[$model->sessionName];
	}

	/**
	 * 获取当前登录用户信息
	 * @return User|null 用户模型或null
	 */
	public function getUserInfo($modelName)
	{
		$model = new $modelName;
		$user = $_SESSION[$model->sessionName];
		if ($user) {
			return $user;
		}
		return null;
	}

	/**
	 * 用户登出
	 */
	public function logout($modelName)
	{
		$model = new $modelName;
		unset($_SESSION[$model->sessionName]);
	}

	/**
	 * 新建
	 * @param $modelName
	 * @param $data
	 * @return mixed
	 */
	public function registerUser($modelName, $data)
	{
		$model = new $modelName;
		$data['status'] = 2;
		$info = $model->newSave($model, $data);
		return $info;
	}

}
