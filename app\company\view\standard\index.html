<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>我的创标申请</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/company.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .el-table .danger-row {
            background: #fbc4c4;
        }
        .el-table .warning-row {
            background: oldlace;
        }
        .el-table .success-row {
            background: #f0f9eb;
        }
        .el-badge__content.is-fixed { right: 20px;z-index: 9;}
        .success .el-descriptions__header {
            border-left:5px solid #67c23a;
            color:#67c23a;
            line-height: 40px;
            padding-left:20px;
            background: -ms-linear-gradient(left, #f0f9eb, #fff); /* IE 10+ */
            background: -moz-linear-gradient(left, #f0f9eb, #fff); /* Firefox */
            background: -webkit-linear-gradient(left, #f0f9eb, #fff); /* Chrome, Safari, Opera */
            background: linear-gradient(to right, #f0f9eb, #fff); /* 标准语法 */
        }
        .primary .el-descriptions__header {
            margin-top: 20px;
            border-left:5px solid #436CE6;
            color:#436CE6;
            line-height: 40px;
            padding-left:20px;
            background: -ms-linear-gradient(left, #F4F7FE, #fff); /* IE 10+ */
            background: -moz-linear-gradient(left, #F4F7FE, #fff); /* Firefox */
            background: -webkit-linear-gradient(left, #F4F7FE, #fff); /* Chrome, Safari, Opera */
            background: linear-gradient(to right, #F4F7FE, #fff); /* 标准语法 */
        }
        .success .el-descriptions-item__label.is-bordered-label { background-color: #f0f9eb; width:140px;}
        .primary .el-descriptions-item__label.is-bordered-label { background-color: #F4F7FE; width:140px;}
        .el-descriptions .is-bordered td { width:250px;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">
        {if $standard.standard_id==0}
        <el-button :loading="loading" type="primary" size="mini" @click="add">创标申请</el-button>
        {/if}
        {if $standard.standard_id>0}
        <el-descriptions class="success" title="当前执行标准" :column="2" border>
            <template slot="extra">
                <el-button :loading="loading" type="success" size="mini" @click="update">标准变更</el-button>
            </template>
            <el-descriptions-item label="企业名称">
                {$standard.name}
            </el-descriptions-item>
            <el-descriptions-item label="行业/专业">
                {$standard.industry}/{$standard.specialty}
            </el-descriptions-item>
            <el-descriptions-item label="评审标准">
                {$standard.standard_name}
            </el-descriptions-item>
            <el-descriptions-item label="标准等级">
                {$standard.standard_level}
            </el-descriptions-item>
        </el-descriptions>
        {/if}
        {if $standard_apply}
        <el-descriptions class="primary" title="标准申请"  :column="2" border>
            <el-descriptions-item label="企业名称">
                {$standard_apply.company_name}
            </el-descriptions-item>
            <el-descriptions-item label="行业/专业">
                {$standard_apply.industry}/{$standard_apply.specialty}
            </el-descriptions-item>
            <el-descriptions-item label="评审标准">
                {$standard_apply.standard_name}
            </el-descriptions-item>
            <el-descriptions-item label="标准等级">
                {$standard_apply.level}
            </el-descriptions-item>
            <el-descriptions-item label="是否机构参与">
                {$standard_apply.advisory}
            </el-descriptions-item>
            <el-descriptions-item label="审核状态">
                <el-tag v-if="'{$standard_apply.status}'==7" type="success">已通过</el-tag>
                <el-tag v-if="'{$standard_apply.status}'==5" type="danger">已驳回</el-tag>
                <el-tag v-if="'{$standard_apply.status}'==1" type="warning">待审核</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="审核说明">
                {$standard_apply.reason}
            </el-descriptions-item>
        </el-descriptions>
        {/if}
        <!--<el-form :inline="true" :model="searchFrom" class="form-inline" @submit.native.prevent>
            <el-form-item style="float: right">
                <el-button :loading="loading" type="success" size="mini" @click="add">新增</el-button>
            </el-form-item>
        </el-form>
        <el-table border
                  v-loading="loading"
                  :data="data"
                  style="width: 100%;margin-bottom: 20px;"
                  ref="qtable"
                  show-summary
                  :height="height"
                  size="small"
                  :row-class-name="tableRowClassName">
            <el-table-column
                    type="index"
                    label="序号"
                    align="center"
                    width="50">
            </el-table-column>
            <el-table-column
                    prop="company_name"
                    label="企业名称"
                    align="center"
                    show-overflow-tooltip
                    min-width="200">
            </el-table-column>
            <el-table-column
                    prop="industry"
                    label="行业/专业"
                    align="center"
                    show-overflow-tooltip
                    min-width="100">
                <template slot-scope="scope">
                    {{scope.row.industry}}/{{scope.row.specialty}}
                </template>
            </el-table-column>
            <el-table-column
                    prop="standard_name"
                    label="申请标准"
                    align="center"
                    show-overflow-tooltip
                    min-width="100">
            </el-table-column>
            <el-table-column
                    prop="level"
                    label="评定级别"
                    align="center"
                    show-overflow-tooltip
                    min-width="100">
            </el-table-column>
            <el-table-column
                    prop="mb_status"
                    label="申请状态"
                    align="center"
                    show-overflow-tooltip
                    width="120">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.status==1" type="info">待审核</el-tag>
                    <el-tag v-if="scope.row.status==7" type="primary">审核通过</el-tag>
                    <el-tag v-if="scope.row.status==5" type="success">已驳回</el-tag>
                </template>
            </el-table-column>
            &lt;!&ndash;<el-table-column
                    label="操作"
                    align="center"
                    width="120">
                <template slot-scope="scope">
                    <el-dropdown split-button size="small" type="success" @click="info(scope.row)">
                        详情
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item @click.native="edit(scope.row)" type="warning">编辑</el-dropdown-item>
                            <el-dropdown-item @click.native="del(scope.row)" type="danger">删除</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </template>
            </el-table-column>&ndash;&gt;
        </el-table>
        &lt;!&ndash;分页条total, sizes, prev, pager, next, jumper&ndash;&gt;
        <div class="block">
            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="page"
                    :page-sizes="[10, 20, 50, 100, 500, 1000]"
                    :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total">
            </el-pagination>
        </div>-->
    </div>
    <add ref="add" @ok="getData()"></add>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                title: '新增',
                searchFrom: {
                    work_type: [],
                    title: '',
                    is_delivery: '',
                    is_end: '',
                },
                thisrow: {},
                thisuser: '',
                data: [],
                filelist: [],
                page: 1,
                pageSize: 20,
                total: 0,
                deptData: [],
                userData: [],
                dialogFormVisible: false,
                visible: false,
                loading: true,
                form: {},
                formRules: {
                    form_name: [{required: true, message: "请输入表单名称", trigger: "blur"}],
                },
                ueObj: null,
                height: document.documentElement.clientHeight - 155,
            };
        },
        components: {
            'add': 'url:/general/toppingsoft/app/company/view/standard/vue/add.vue?v=1',
        },
        watch: {
            $route() {
                //切换标签时
                this.handleRefreshTable();
            }
        },
        created() {
            let _this = this;
            //窗口改变时
            window.onresize = function () {
                _this.handleRefreshTable();
            };
        },
        updated() {
            //数据改变时
            this.handleRefreshTable();
        },
        methods: {
            tableRowClassName({row, rowIndex}) {
                if (row.back) {
                    return row.back;
                }
                return '';
            },
            handleClick(tab, event) {
                this.searchFrom = {
                    our: '',
                    status: '',
                    level: '',
                    type: '',
                    company: '',
                    charge_user_name: '',
                };
                this.getData();
            },
            statusChange() {
                this.page = 1;
                this.getData();
            },
            handleSizeChange: function (val) {
                this.pageSize = val;
                this.getData();
                console.log('每页 ${val} 条');
            },
            handleCurrentChange: function (val) {
                this.page = val;
                this.getData();
                console.log('当前页: ${val}');
            },
            add() {
                this.$refs.add.title="创标申请";
                this.$refs.add.open();
            },
            update() {
                this.$refs.add.title="标准变更";
                this.$refs.add.open();
            },
            edit(row) {
                this.$refs.add.title="修改企业信息";
                this.$refs.add.open(row);
            },
            info(row) {
                this.thisrow = row;
                this.$refs.info.open(row);
            },
            del(row) {
                var _this = this;
                _this.$confirm("确认删除？", "提示", {}).then(() => {
                    var param = {};
                    var url = "del";
                    param.id = row.id;
                    axios.post(url, param).then(function (res) {
                        if (res.data.code == 0) {
                            _this.$message({
                                message: res.data.msg,
                                type: "success"
                            });
                            document.location.reload();
                        } else {
                            _this.$message({
                                message: res.data.msg,
                                type: "error"
                            });
                        }
                    }).catch(function (error) {
                        console.log(error);
                    });
                });
            },
            //数据初始化
            reset() {
                this.searchFrom = {};
                this.page = 1;
                this.pageSize = 20;
                this.getData();
            },
            //数据加载
            getData() {
                var _this = this;
                var param = _this.searchFrom;
                param._ajax = 1;
                param.page = _this.page;
                param.limit = _this.pageSize;
                param.our = 1;
                axios.post('', param).then(function (res) {
                    if (res.data.code == 0) {
                        _this.data = res.data.data.data;
                        _this.page = res.data.data.current_page;
                        _this.pageSize = res.data.data.per_page;
                        _this.total = res.data.data.total;
                        _this.$nextTick(() => {
                            // 这里的代码会在DOM更新完成后执行
                            _this.loading = false;
                        });
                        _this.handleRefreshTable();
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            import1: function () {
                this.$refs.import1.templateUrl = 'importTemplate';
                this.$refs.import1.submitUrl = 'import';
                this.$refs.import1.title = '企业信息导入';
                this.$refs.import1.open();
            },
            export1: function () {
                let _this = this;
                let where = "";
                let type = '';
                //获得where
                where = '';
                for (let index in this.searchFrom) {
                    if (this.searchFrom[index] !== '' && this.searchFrom[index] !== [] && this.searchFrom[index] !== {}) {
                        let str = "";
                        if (index == 'type') {
                            for (let i in this.searchFrom.type) {
                                type += this.searchFrom.type[i] + ',';
                            }
                            str += 'type=' + type;
                        } else {
                            str += index + '=' + this.searchFrom[index];
                        }
                        where += "&" + str;
                    }
                }
                let url = "index?excel=1&" + where;
                //window.open(url);
                location.href = url;
            },
            handleRefreshTable: function () {
                this.$nextTick(function () {
                    var refList = this.$refs;
                    if (refList) {
                        for (var i of Object.keys(refList)) {
                            if (refList[i] && refList[i].doLayout) {
                                refList[i].doLayout();
                            }
                        }
                    }
                });
            },
        },
        mounted() {
            var _this = this;
            //获取列表
            this.getData();
            <?php if(empty($standard)&&empty($standard_apply)){ ?>
                setTimeout(function(){
                    _this.add();
                },500);
            <?php }?>
        }
    })
</script>


</body>
</html>