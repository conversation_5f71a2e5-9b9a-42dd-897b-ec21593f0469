<?php
$appid = $_GET['appid'];
$appkey = $_GET['appkey'];
include_once "inc/td_core.php";
if($appid==='admin'){
    if($appkey!==md5('admin'.time())){
        exit(json_encode(array('code' => 8001, 'msg' => '非法调用')));
    }
}else{
    $sql = "SELECT id,appid,appkey FROM top_login_app where appid = '{$appid}' and appkey = '{$appkey}'";
    $cursor = exequery(TD::conn(), $sql);
    if ($ROW = mysqli_fetch_array($cursor)) {
        $id = $ROW["id"];
        $appid = $ROW["appid"];
        $appkey = $ROW["appkey"];
    }
    if(empty($id)){
        exit(json_encode(array('code' => 8001, 'msg' => '非法调用')));
    }
}
