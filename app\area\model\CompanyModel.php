<?php


namespace app\area\model;


use app\model\FileModel;
use think\Model;
use think\App;
use think\facade\Db;
use app\model\SettingModel;

//权限配置模块
class CompanyModel extends Model
{


    public static function companyUpdate($param)
    {
        $data = [
            'name' => $param['name'],
            'license' => $param['license'],
            'aoc' => $param['aoc'],
            'reg_address' => $param['reg_address'],
            'reg_address_info' => $param['reg_address_info'],
            'operate_address' =>$param['operate_address'],
            'operate_address_info' => $param['operate_address_info'],
            'region' => $param['region'],
            'legal' => $param['legal'],
            'legal_mobile' => $param['legal_mobile'],
            'legal_email' => $param['legal_email'],
            'phone' => $param['phone'],
            'fax' => $param['fax'],
            'industrial_park' => $param['industrial_park'],
            'postal_code' => $param['postal_code'],
            'economy_sector' => $param['economy_sector'],
            'industry' => $param['industry'],
            'specialty' => $param['specialty'],
            'mb_industry' => $param['mb_industry'],
            'license_number' => $param['license_number'],
            'license_start' => $param['license_start'],
            'license_end' => $param['license_end'],
            'economy_type' => $param['economy_type'],
            'enterprise_size' => $param['enterprise_size'],
            'reg_money' => $param['reg_money'],
            'manager' => $param['manager'],
            'manager_mobile' => $param['manager_mobile'],
            'manager_email' => $param['manager_email'],
            'date' => $param['date'],
            'fixed_asset' => $param['fixed_asset'],
            'revenue' => $param['revenue'],
            'personnel' => $param['personnel'],
            'area' => $param['area'],
            'personnel_full' => $param['personnel_full'],
            'personnel_part' => $param['personnel_part'],
            'personnel_special' => $param['personnel_special'],
            'group_name' => $param['group_name'],
            'status' => 1,
            'business' => $param['business'],
            'business_type' => $param['business_type'],
            'area_id' => $param['area_id'],
            'city_id' => $param['city_id'],
            'dept_id' => $param['dept_id'],
            'is_dust_explosion' => $param['is_dust_explosion'],
            'sector' => $param['sector'],
            'is_ammonia_cold' => $param['is_ammonia_cold'],
            'ammonia_use' => $param['ammonia_use'],
            'ammonia_usage' => $param['ammonia_usage'],
            'ammonia_storage_method' => $param['ammonia_storage_method'],
            'ammonia_storage_capacity' => $param['ammonia_storage_capacity'],
            'gas_alarm_number' => $param['gas_alarm_number'],
            'is_hot_melting' => $param['is_hot_melting'],
            'is_light_industry' => $param['is_light_industry'],
            'limited_space_type' => $param['limited_space_type'],
            'blast_furnace_number' => $param['blast_furnace_number'],
            'nonferrous_furnace_number' => $param['nonferrous_furnace_number'],
            'ferroalloy_furnace_number' => $param['ferroalloy_furnace_number'],
            'soaring_furnace_number' => $param['soaring_furnace_number'],
            'limited_space_num' => $param['limited_space_num'],
        ];
        $re = Db::table('top_company_info')->where([['user_id','=',$param['user_id']]])->find();
        if(empty($re)){
            $data['user_id'] = $param['user_id'];
            $id = Db::table('top_company_info')->where([['user_id','=',$param['user_id']]])->insert($data);
            $id = Db::table('top_company_info')->where($data)->find()['id'];
        }else{
            Db::table('top_company_info')->where([['user_id','=',$param['user_id']]])->update($data);
            $id = Db::table('top_company_info')->where([['user_id','=',$param['user_id']]])->find()['id'];
        }
        /*******处理粉尘字段*******Start***********/
        //删除之前的旧数据
        Db::table('top_company_param')->where("company_id=".$id)->delete();

        //获取粉尘 - 认证中的信息
        $dustList_apply = Db::table('top_company_param_apply')->field("name,param_value,category")
            ->where(['company_id'=>$param['id']])->select()->toArray();

        if( is_array($dustList_apply) && count($dustList_apply) )
        {
            foreach ($dustList_apply as $k => $item)
            {
                $dustList_apply[$k]['company_id'] = $id;
            }
            Db::table('top_company_param')->strict(false)->insertAll($dustList_apply);
        }
        /*******处理粉尘字段*******End***********/
        return $id;
    }


    //参数格式化
    public static function codeToText($info){
        if(empty($info)){
            return $info;
        }
        $pcas = SettingModel::getPcasAll();
        $info['reg_address'] = empty($info['reg_address'])?[]:explode(',',$info['reg_address']);
        $info['mb_reg_address'] = '';
        foreach ($info['reg_address'] as $k=>$v){
            $info['mb_reg_address'] .= $pcas[$v];
        }
        $info['mb_reg_address'] .= $info['reg_address_info'];
        $info['operate_address'] = empty($info['operate_address'])?[]:explode(',',$info['operate_address']);
        $info['mb_operate_address'] = '';
        foreach ($info['operate_address'] as $k=>$v){
            $info['mb_operate_address'] .= $pcas[$v];
        }
        $info['mb_operate_address'] .= $info['operate_address_info'];
        $info['region'] = empty($info['region'])?[]:explode(',',$info['region']);
        $info['mb_region'] = '';
        foreach ($info['region'] as $k=>$v){
            $info['mb_region'] .= $pcas[$v];
        }
        $info['economy_sector'] = explode(',',$info['economy_sector']);
        $info['mb_economy_sector'] = implode('/',$info['economy_sector']);
        $info['economy_type'] = explode(',',$info['economy_type']);
        $info['mb_economy_type'] = implode('/',$info['economy_type']);
        $info['license_date'] = $info['license_start'].'至'.$info['license_end'];
        $info['licenseUrl'] = empty($info['license'])?'':FileModel::getFile(0,$info['license']);
        $info['aocUrl'] = empty($info['aoc'])?'':FileModel::getFile(0,$info['aoc']);
        foreach ($info as $k=>$v){
            $info[$k] = $v==='0000-00-00'?'':$v;
            if(is_int($v)){
                $info[$k] = (string)$v;
            }
            if($v===null){
                $info[$k] = '';
            }
        }
        //非企业端 查看信息无需展示认证中的信息,则直接使用的&$info
        \app\model\CompanyModel::getCompanyAuthInfo($info);
        return $info;
    }

}