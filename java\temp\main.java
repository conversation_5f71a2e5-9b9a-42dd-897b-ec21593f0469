package org.example;
import kl.hsm.client.sdk.HsmClient;
import kl.hsm.server.svc.base.*;
import org.bouncycastle.util.encoders.Hex;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
public class Main {
    //    private final static byte[] IV_16 = new byte[]{1, 2, 3, 4, 5, 6, 7, 8, 1, 2, 3, 4, 5, 6, 7, 8};

    private static HsmClient hsmClient = new HsmClient();
    private static final Charset CHARSET = StandardCharsets.UTF_8;
    static {
        try {
            hsmClient.init("172.43.147.100", 10000);
        } catch (SvcException e) {
            System.err.println("HSM初始化失败: " + e.getMessage());
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args)  {
        try {
            if (args.length == 2) {
                if ("encrypt".equals(args[0])) {
                    // 使用内置的密钥对进行加密
                    String data = args[1];
                    byte[] text = data.getBytes(CHARSET);
                    String encryptedData = encrypt(text);
                    System.out.println("Encrypted data:" + encryptedData);
                }else if ("decrypt".equals(args[0])) {
                    // 使用内置的密钥对进行解密
                    String data = args[1];
                    byte[] text = Hex.decode(data);
                    String decryptedData = decrypt(text);
                    System.out.println("Decrypted data:" + decryptedData);
                }else{
                    System.out.println("encrypt\" <data> | \"decrypt\" <data>");
                }
            }else{
                System.out.println("encrypt\" <data> | \"decrypt\" <data>");
            }
        } catch (IllegalArgumentException e) {
            System.err.println("输入格式错误: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("系统错误: " + e.getMessage());
        }
        finally {
            if (hsmClient != null) {
                hsmClient.closePool();
            }
        }
    }

    // 每次加密时生成随机IV
    public static String encrypt(byte[] plaintext) throws Exception {
        try {
            // 生成16字节随机IV
            byte[] iv_16 = hsmClient.generateRandom(16);
            //组装对称加密参数
            SymParam symParam = new SymParam();
            //工作模式
            symParam.setMode(EncMode.CBC);
            //初始向量
            symParam.setIv(iv_16);
            //设置补码
            symParam.setPadding(Padding.PKCS7Padding);
            //内部密钥加密
            byte[] encryptBytes = hsmClient.encrypt(1, symParam, plaintext);
            byte[] result = new byte[iv_16.length + encryptBytes.length];
            System.arraycopy(iv_16, 0, result, 0, iv_16.length);
            System.arraycopy(encryptBytes, 0, result, iv_16.length, encryptBytes.length);
            //        System.out.println("内部对称密钥加密:" + Hex.toHexString(result));
            return Hex.toHexString(result);
        } catch (SvcException e) {
            System.err.println("调用加密机异常:" + e.getErrCode() + ":" + e.getErr());
            return null;
        }
    }

    // 从密文中提取IV并解密
    public static String decrypt(byte[] combined) throws SvcException {
        if (combined == null || combined.length < 16) {
            System.err.println("解密失败: 无效的密文长度");
            return null;
        }
        try {
            // 分离IV和密文
            byte[] iv = new byte[16];
            byte[] ciphertext = new byte[combined.length - 16];

            System.arraycopy(combined, 0, iv, 0, 16);
            System.arraycopy(combined, 16, ciphertext, 0, ciphertext.length);

            // 组装解密参数
            SymParam symParam = new SymParam();
            symParam.setMode(EncMode.CBC);
            symParam.setIv(iv);
            symParam.setPadding(Padding.PKCS7Padding);

            byte[] bytes1 = hsmClient.decrypt(1, symParam, ciphertext);
            //        System.out.println("内部对称密钥解密:" + new String(bytes1));
            return new String(bytes1,CHARSET);
        } catch (SvcException e) {
            System.err.println("调用加密机异常:" + e.getErrCode() + ":" + e.getErr());
            throw e;
        }
    }
}