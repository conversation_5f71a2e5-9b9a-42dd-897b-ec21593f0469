<style scoped>
.my-autocomplete li{line-height: normal;padding: 7px;}
.my-autocomplete li .name{text-overflow: ellipsis;overflow: hidden;}
.my-autocomplete li .addr{font-size: 12px;color: #b4b4b4;}
.my-autocomplete li .highlighted{color: #ddd;}
.el-cascader { width:100%;}
.el-form-item__content .el-input-group { vertical-align: middle;}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 150px;
  height: 150px;
  line-height: 150px;
  text-align: center;
}
.avatar {
  width: 150px;
  height: 150px;
  display: block;
}
.el-form-item__error {
    color: #F56C6C;
    font-size: 12px;
    line-height: 1;
    padding-top: 4px;
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 10;
}
</style>

<template>
  <el-dialog :title="title" :close-on-click-modal="false"  modal="false" :visible.sync="visible" width="700px" append-to-body="true" label-position="top">
    <el-form ref="form" :model="data" class="tableSearch" label-position="right" label-width="150px" :rules="rules">
      <el-row :gutter="20">
<!--        <el-col :span="24">-->
<!--          <el-form-item label="ORC识别" prop="name">-->
<!--            <el-upload-->
<!--                class="upload-demo"-->
<!--                action="testOrc"-->
<!--                :show-file-list="false"-->
<!--                :on-success="handleAvatarSuccess">-->
<!--              <img v-if="imageUrl" :src="imageUrl" class="avatar">-->
<!--              <i v-else class="el-icon-plus avatar-uploader-icon"></i>-->
<!--            </el-upload>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
        <el-col :span="24">
          <el-form-item label="姓名" prop="name">
            <el-input style="width: 150px;float: left;" v-model="data.name" size="small" placeholder="请输入姓名"></el-input>
            <el-upload
                style="float: left;margin-left: 20px;"
                class="upload-demo"
                action="testOrc"
                :show-file-list="false"
                :before-upload="uploadBefore"
                accept=".jpg,.png,.pdf"
                :on-success="handleAvatarSuccess">
              <el-link size="mini" type="primary" :underline="false">点击上传文件自动识别</el-link>
            </el-upload>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="职务" prop="job">
            <el-input v-model="data.job" size="small" placeholder="请输入职务"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="证书发放时间" prop="grant_date">
            <el-date-picker
                style="width: 140px"
                v-model="data.grant_date"
                size="small"
                type="date"
                placeholder="选择日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="有效期" prop="validity">
            <el-date-picker
                style="width: 140px"
                v-model="data.validity_start"
                :picker-options="startPickerOptions"
                @change="handleStartChange"
                size="small"
                type="date"
                placeholder="选择日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd">
            </el-date-picker>
            ~
            <el-date-picker
                style="width: 140px"
                v-model="data.validity_end"
                :picker-options="endPickerOptions"
                @change="handleEndChange"
                size="small"
                type="date"
                placeholder="选择日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="年审时间" prop="year_review_time">
            <el-date-picker
                style="width: 140px"
                v-model="data.year_review_time"
                size="small"
                type="date"
                placeholder="选择日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="年审记录上传" prop="year_review_info">
              <el-upload
                  class="upload-demo"
                  action="upload"
                  :on-success="uploadSuccess1"
                  :before-upload="uploadBefore1"
                  :on-remove="handleRemove1"
                  :on-preview="preview"
                  accept=".jpg,.png,.pdf"
                  multiple
                  :limit="3"
                  :file-list="data.year_review_info">
                <el-button size="small" type="primary">点击上传</el-button>
                <div slot="tip" class="el-upload__tip">只能上传jpg/png/pdf文件</div>
              </el-upload>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="证书扫描件上传" prop="standard_id">
            <el-upload
                class="upload-demo"
                action="upload"
                :on-success="uploadSuccess"
                :before-upload="uploadBefore"
                :on-remove="handleRemove"
                :on-preview="preview"
                accept=".jpg,.png,.pdf"
                multiple
                :limit="3"
                :file-list="data.files">
              <el-button size="small" type="primary">点击上传</el-button>
              <div slot="tip" class="el-upload__tip">只能上传jpg/png/pdf文件</div>
            </el-upload>
          </el-form-item>
        </el-col>
        <el-col :span="24" style="text-align: center;margin-top: 20px;">
          <el-button type="primary" size="small" @click="submit()">保存</el-button>
          <el-button size="small" @click="visible = false">关闭</el-button>
        </el-col>
      </el-row>
    </el-form>
    <preview ref="preview"></preview>
  </el-dialog>
</template>
<script>
module.exports = {
  name: "add",
  components:{
    'preview': 'url:/general/toppingsoft/public/vue/preview.vue',
  },
  data:function() {
    return {
      visible: false,
      title: '培训合格证书编辑',
      loading: false,
      data: {
        id: '',
        name: '',
        job: '',
        grant_date: '',
        validity_start: '',
        validity_end: '',
        year_review_time: '',
        year_review_info: [],
        files:[],
      },
      startPickerOptions: {
        disabledDate: (time) => {
          if (this.data.validity_end) {
            return time.getTime() > new Date(this.data.validity_end).getTime()
          }
          return false
        }
      },
      endPickerOptions: {
        disabledDate: (time) => {
          if (this.data.validity_start) {
            return time.getTime() < new Date(this.data.validity_start).getTime()
          }
          return false
        }
      },
      rules: {
        name: [{ required: true, message: '请填写姓名', trigger: 'blur' }],
      },
      imageUrl:'',
    }
  },
  mounted: function(){
  },
  created:function(){
  },
  methods: {
    handleStartChange(val) {
      if (this.data.validity_end && new Date(val) > new Date(this.data.validity_end)) {
        this.data.validity_end = ''
      }
    },
    handleEndChange(val) {
      if (this.data.validity_start && new Date(val) < new Date(this.data.validity_start)) {
        this.data.validity_start = ''
      }
    },
    handleAvatarSuccess(res, file) {
      console.log(res)
      if(res.code == 200){
        this.data.name = res.data.name;
        this.data.grant_date = res.data.grant_date;
        this.data.validity_start = res.data.grant_date;
        this.data.validity_end = res.data.validity_end;
        this.data.year_review_time = res.data.year_review_time;
      }
      this.imageUrl = URL.createObjectURL(file.raw);
    },
    /**
     * 打开弹窗调用方法
     * */
    open: function (row) {
      var _this =this;
      _this.visible = true;
      _this.data = {
        id: '',
        name: '',
        job: '',
        grant_date: '',
        validity_start: '',
        validity_end: '',
        year_review_time: '',
        year_review_info: [],
        files:[],
      };
      if( row != '' && row != null && row != undefined )
      {
        _this.data = row;
      }
    },
    submit: function () {
      var _this = this;
      var param = _this.data;
      this.$refs.form.validate(function (valid) {
        if(valid){
          axios.post("trainingSave", param).then(function (res) {
            _this.$message({
              message: res.data.msg,
              type: res.data.type
            });
            if (res.data.code == 0) {
              _this.visible = false;
              _this.$emit("ok");
            }
          }).catch(function (error) {
            console.log("出现错误:",error);
          });
        }
      });
    },
    uploadBefore(file) {
      console.log(file)
      const isJPG = file.type === 'image/jpeg';
      const isPNG = file.type === 'image/png';
      const isPDF = file.type === 'application/pdf';
      if(!isJPG&&!isPNG&&!isPDF){
        this.$message.error('请上传jpg/png/pdf文件');
      }
      return isJPG||isPNG||isPDF;
    },
    uploadSuccess(res, file,fileList) {
      this.data.files.push(file.response.data);
    },
    handleRemove(file, fileList) {
      var files = [];
      for(var i in fileList){
        files.push(fileList[i]);
      }
      this.data.files = files;
    },
    uploadBefore1(file) {
      console.log(file)
      const isJPG = file.type === 'image/jpeg';
      const isPNG = file.type === 'image/png';
      const isPDF = file.type === 'application/pdf';
      if(!isJPG&&!isPNG&&!isPDF){
        this.$message.error('请上传jpg/png/pdf文件');
      }
      return isJPG||isPNG||isPDF;
    },
    uploadSuccess1(res, file,fileList) {
      this.data.year_review_info.push(file.response.data);
    },
    handleRemove1(file, fileList) {
      var files = [];
      for(var i in fileList){
        files.push(fileList[i]);
      }
      this.data.year_review_info = files;
    },
    preview: function (file) {
      file = file.response?file.response.data:file;
      this.$refs.preview.open(file.url,file.name);
    },
    Previewf(file) {
      console.log(file);
      if (file) {
        const addTypeArray = file.name.split(".");
        const addType = addTypeArray[addTypeArray.length - 1];
        const url = file.response?file.response.data.url:file.url;
        console.log(addType);
        if (addType === "pdf") {
          let routeData = this.$router.resolve({
            path: "/insurancePdf",
            query: { url: url, showBack: false },
          });
          window.open(routeData.href, "_blank");
        } else if (addType === "txt") {
          window.open(url);
        } else if (["png", "jpg", "jpeg"].includes(addType)) {
          window.open(url);
        } else {
          this.$message({
            message: "该文件类型暂不支持预览",
            type: "warning",
          });
          return false;
        }
      }
    },
  }
}
</script>