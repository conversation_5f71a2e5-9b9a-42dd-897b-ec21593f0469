-- 企业信息表
CREATE TABLE IF NOT EXISTS `top_company_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT '企业名称',
  `industry` varchar(50) DEFAULT NULL COMMENT '所属行业',
  `area` varchar(50) DEFAULT NULL COMMENT '所属区域',
  `reg_address_info` varchar(255) DEFAULT NULL COMMENT '注册地址',
  `jd` decimal(10,6) DEFAULT NULL COMMENT '经度',
  `wd` decimal(10,6) DEFAULT NULL COMMENT '纬度',
  `is_standard` tinyint(1) DEFAULT '0' COMMENT '是否达标 0否 1是',
  `standard_expire_time` date DEFAULT NULL COMMENT '达标有效期',
  `review_status` tinyint(1) DEFAULT '0' COMMENT '评审状态 0未评审 1评审中 2已通过',
  `review_score` decimal(5,2) DEFAULT '0.00' COMMENT '评审得分',
  `review_timely_rate` decimal(5,2) DEFAULT '0.00' COMMENT '评审及时率',
  `has_limited_space` tinyint(1) DEFAULT '0' COMMENT '是否有有限空间',
  `limited_space_type` varchar(50) DEFAULT NULL COMMENT '有限空间类型',
  `has_dust` tinyint(1) DEFAULT '0' COMMENT '是否有粉尘涉爆',
  `dust_type` varchar(50) DEFAULT NULL COMMENT '粉尘类型',
  `has_ammonia` tinyint(1) DEFAULT '0' COMMENT '是否有液氨制冷',
  `ammonia_usage` varchar(50) DEFAULT NULL COMMENT '液氨用途',
  `ammonia_storage` varchar(50) DEFAULT NULL COMMENT '液氨存储方式',
  `has_metal` tinyint(1) DEFAULT '0' COMMENT '是否涉高温熔融金属',
  `furnace_type` varchar(50) DEFAULT NULL COMMENT '熔炉类型',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_industry` (`industry`),
  KEY `idx_area` (`area`),
  KEY `idx_standard` (`is_standard`),
  KEY `idx_review_status` (`review_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业信息表';

-- 评审信息表
CREATE TABLE IF NOT EXISTS `top_review_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `company_id` int(11) NOT NULL COMMENT '企业ID',
  `review_org` varchar(255) DEFAULT NULL COMMENT '评审机构',
  `review_date` date DEFAULT NULL COMMENT '评审日期',
  `expert_group` varchar(255) DEFAULT NULL COMMENT '评审专家组',
  `review_status` tinyint(1) DEFAULT '0' COMMENT '评审状态 0未评审 1评审中 2已通过',
  `review_score` decimal(5,2) DEFAULT '0.00' COMMENT '评审得分',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_review_date` (`review_date`),
  KEY `idx_review_status` (`review_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评审信息表'; 