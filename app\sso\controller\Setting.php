<?php

namespace app\sso\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use app\model\DbModel;
use hg\apidoc\annotation as Apidoc;
use app\BaseController;
use think\App;
use think\facade\Db;
use think\facade\View;

/**
 * @Apidoc\Title("单点登录")
 * @Apidoc\Group("Sales")
 * @Apidoc\Sort(3)
 */
class Setting extends BaseController
{


    function create_nonce_str(int $length = 16) {
        $chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        $str = "";
        for ($i = 0; $i < $length; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }
        return $str;
    }

    //单点登录系统列表
    public function list($limit=20){
        $db = [
            'table' => 'top_login_app',
            'title' => '开放平台配置表',
            'fields' => [
                'db' => [
                    ['field'=>'name','title'=>'系统名称','type'=>'varchar(50)'],
                    ['field'=>'appid','title'=>'自动生成appid','type'=>'varchar(50)'],
                    ['field'=>'appkey','title'=>'自动生成appkey','type'=>'varchar(50)'],
                    ['field'=>'redirect_uri','title'=>'接口地址','type'=>'varchar(250)'],
                    ['field'=>'create_user_id','title'=>'添加人','type'=>'varchar(50)'],
                    ['field'=>'create_user_name','title'=>'添加人','type'=>'varchar(50)'],
                    ['field'=>'create_time','title'=>'添加时间','type'=>'varchar(50)'],
                    ['field'=>'tp_appid','title'=>'对接appid','type'=>'varchar(50)'],
                    ['field'=>'tp_appkey','title'=>'对接appkey','type'=>'varchar(50)'],
                    ['field'=>'domain','title'=>'域名地址','type'=>'varchar(200)'],
                    ['field'=>'param','title'=>'关联参数','type'=>'varchar(255)'],
                    ['field'=>'del','title'=>'是否删除','type'=>'tinyint(1)'],
                ],
            ],
        ];
        DbModel::isDb($db);
        $db = [
            'table' => 'top_login_app_user',
            'title' => '开放平台用户绑定关系表',
            'fields' => [
                'db' => [
                    ['field'=>'login_app_id','title'=>'系统id','type'=>'varchar(50)'],
                    ['field'=>'user_id','title'=>'用户id','type'=>'varchar(50)'],
                    ['field'=>'byname','title'=>'登录名','type'=>'varchar(50)'],
                    ['field'=>'bind_byname','title'=>'关联第三方登录名','type'=>'varchar(50)'],
                ],
            ],
        ];
//        DbModel::isDb($db);
        if (request()->isAjax()) {
            $search = request()->param('search','','trim');
            $where[] = ['del','=',0];
            if(!empty($search)){
                $where[] = ['name','like',"%$search%"];
            }
            $res = Db::table('top_login_app')->where($where)
                ->order('create_time desc')
                ->paginate($limit)->each(function ($item, $key) {
                return $item;
            });
            result($res);
        }else{
            View::assign('isAdmin',true);
            return view();
        }
    }

    //保存单点登录系统
    public function save($id=0){
        $request = request()->post();
        $data = [
            'name' => trim($request['name']),
            'redirect_uri' => trim($request['redirect_uri']),
            'tp_appid' => trim($request['tp_appid']),
            'tp_appkey' => trim($request['tp_appkey']),
            'domains' => trim($request['domain']),
            'param' => trim($request['param']),
        ];
        if(empty($data['name'])){
            result('',1002,'请填写系统名称');
        }
        if(empty($id)){
            $data['appid'] = '1'.date('md') . str_shuffle(substr(microtime(), 2, 6)) . sprintf('%02d', rand(0, 99));
            $data['appkey'] = self::create_nonce_str(32);
            $data['create_user_id'] = $_SESSION['LOGIN_USER_ID'];
            $data['create_user_name'] = $_SESSION['LOGIN_USER_NAME'];
            $data['create_time'] = date('Y-m-d H:i');
            $id = Db::name('login_app')->insertGetId($data);
        }else{
            $up = Db::name('login_app')->where(['id'=>$id])->update($data);
        }
        result(['id'=>$id]);
    }

    /*
     * 删除第三方系统
     */
    public function del($id = 0){
        $up = Db::name('login_app')->where(['id'=>$id])->update(['del'=>1]);
        result('',0,'删除成功');
    }

    //更新appkey
    public function updateAppkey($id = 0){
        $data['appkey'] = self::create_nonce_str(32);
        $up = Db::name('login_app')->where(['id'=>$id])->update($data);
        result('',0,'更新成功');
    }

}
