<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>自评打分</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/company.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .el-descriptions .is-bordered td { width:250px;}
        .item { width: 30px;height:30px;line-height:30px;margin: 10px 0;border-radius:40px;background-color: #f0f0f0;text-align:center;cursor: pointer;}
        .item.checked { background-color: #1989FA;color:#fff;}
        .item.success { background-color: #67c23a;color:#fff;}
        .el-divider.el-divider--horizontal { margin:10px 0;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">
        <el-page-header @back="goBack" content="自评打分"></el-page-header>
        <el-divider style="margin:10px 0;"></el-divider>
        <el-row :gutter="20">
            <el-col :span="8" :style="{overflow: 'auto',height:height+'px'}">
                <el-col v-for="review in data">
                    <p>{{review.name}}</p>
                    <el-col v-for="(item,key) in review.children" style="width:50px;">
                        <!--                    <el-tooltip effect="dark" placement="top">-->
                        <!--                        <div slot="content" v-html="item.ask"></div>-->
                        <div :class="form==item?'item checked':(item.scores-item.score-item.miss==0||item.summary?'item success':'item')" @click="change(item)">{{key+1}}</div>
                        <!--                    </el-tooltip>-->
                    </el-col>
                </el-col>
<!--                <el-col v-for="(item,key) in data" style="width:50px;">-->
                    <!--                    <el-tooltip effect="dark" placement="top">-->
                    <!--                        <div slot="content" v-html="item.ask"></div>-->
<!--                    <div :class="(key+1)==index?'item checked':(item.scores-item.score-item.miss==0||item.summary?'item success':'item')" @click="change(key)">{{key+1}}</div>-->
                    <!--                    </el-tooltip>-->
<!--                </el-col>-->
            </el-col>
            <el-col :span="16" :style="{overflow: 'auto',height:height+'px'}">
                <el-form v-model="form" label-width="150px">
                    <el-form-item label="考评内容" v-show="form.content!=''&&form.content!='/'">
                        <div v-html="form.content"></div>
                    </el-form-item>
                    <el-form-item label="基本规范要求" v-show="form.ask!=''&&form.ask!='/'">
                        <div v-html="form.ask"></div>
                    </el-form-item>
                    <el-form-item label="企业达标标准" v-show="form.standards!=''&&form.standards!='/'">
                        <div v-html="form.standards"></div>
                    </el-form-item>
                    <el-form-item label="评分方式" v-show="form.method!=''&&form.method!='/'">
                        <div style="white-space:pre-line" v-html="form.method"></div>
                        <!--                        <div v-html="item.method"></div>-->
                    </el-form-item>
                    <el-form-item label="自评/评审描述" v-show="form.file_remark!=''&&form.file_remark!='/'">
                        <div v-html="form.file_remark"></div>
                    </el-form-item>
                    <el-form-item label="上报记录">
                        <el-table border
                                  :data="form.list"
                                  style="width: 100%;margin-bottom: 20px;"
                                  size="small">
                            <el-table-column
                                    type="index"
                                    label="序号"
                                    align="center"
                                    width="50">
                            </el-table-column>
                            <el-table-column
                                    prop="time"
                                    label="上报时间"
                                    align="center"
                                    width="100">
                            </el-table-column>
                            <el-table-column
                                    prop="sub_files"
                                    label="上报材料"
                                    align="center"
                                    show-overflow-tooltip
                                    min-width="100">
                                <template slot-scope="scope">
                                    <el-upload
                                            v-if="scope.row.edit"
                                            action="upload"
                                            :file-list="scope.row.mb_sub_files"
                                            limit="10"
                                            :on-success="(response,file,fileList)=>uploadSuccess(response,file,fileList,scope.row)"
                                            :before-upload="uploadBefore"
                                            :on-remove="(file,fileList)=>handleRemove(file,fileList,scope.row)">
                                        <el-button size="small" type="primary">点击上传</el-button>
                                        <!--                                <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>-->
                                    </el-upload>
                                    <div v-if="!scope.row.edit" v-for="v in scope.row.mb_sub_files">
                                        <el-image v-if="v.ext=='jpg'||v.ext=='png'||v.ext=='JPG'||v.ext=='PNG'" style="width:60px;height: 60px;border: 1px solid #999;"
                                                  :title="v.name"
                                                  :src="v.url"
                                                  :preview-src-list="[v.url]"></el-image>
                                        <el-link style="color: #2c89ff;" v-else :href="v.url" target="_blank">{{v.name}}</el-link>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column
                                    prop="hazard"
                                    label="运行检查"
                                    align="center"
                                    show-overflow-tooltip
                                    min-width="100">
                                <template slot-scope="scope">
                                    <el-input v-if="scope.row.edit" type="textarea" v-model="scope.row.hazard"></el-input>
                                    <div v-if="!scope.row.edit">{{scope.row.hazard}}</div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-form-item>
                    <el-form-item label="标准分值">
                        <div>{{form.scores}}</div>
                    </el-form-item>
                    <el-form-item label="自评打分">
                        <el-input v-model="form.score" :min="0" :max="form.scores-form.miss" label=""></el-input>
                    </el-form-item>
                    <el-form-item label="缺项分值及原因">
                        <el-input v-model="form.miss" :min="0" :max="form.scores-form.score" label=""></el-input>
                        <el-input v-show="form.miss>0" style="overflow:hidden;" type="textarea" :autosize="{minRows: 2}" v-model="form.resion" placeholder="缺项原因"></el-input>
                    </el-form-item>
                    <el-form-item v-show="form.scores-form.score-form.miss>0" label="扣分说明">
                        <el-input style="overflow:hidden;" type="textarea" :autosize="{minRows: 2}" v-model="form.summary"></el-input>
                    </el-form-item>
                    <el-form-item label="所需整改内容">
                        <el-input style="overflow:hidden;" type="textarea" :autosize="{minRows: 2}" v-model="form.reform"></el-input>
                    </el-form-item>
                    <el-form-item>
<!--                        <el-button type="primary" size="small" @click="next">下一个</el-button>-->
                    </el-form-item>
                </el-form>
            </el-col>
            <el-col>
                <div style="text-align: center;margin: 20px auto;">
                    <el-button @click="submit(0)" v-loading.fullscreen.lock="loading">保存</el-button>
                    <el-button type="primary"  @click="submit(1)">完成打分并汇总</el-button>
                </div>
            </el-col>
        </el-row>
    </div>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.all.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                title: '新增',
                id: '{$id}',
                index: 1,
                form: {
                },
                data: {
                },
                param: [],
                visible: false,
                loading: true,
                height: document.documentElement.clientHeight - 155,
            };
        },
        methods: {
            //数据加载
            getData() {
                var _this = this;
                var param = {};
                param.id = _this.id;
                param._ajax = 1;
                axios.post('score', param).then(function (res) {
                    if (res.data.code == 0) {
                        _this.data = res.data.data.content;
                        _this.form = res.data.data.content[0]['children'][0];
                        _this.$nextTick(() => {
                            // 这里的代码会在DOM更新完成后执行
                            _this.loading = false;
                        });
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            getchildren(data){
                for(var i in data){
                    if(data[i]['is_content']){
                        row = {
                            id:data[i].id,
                            scores:data[i].scores,
                            score:data[i].score,
                            miss:data[i].miss,
                            reform:data[i].reform,
                            summary:data[i].summary,
                            resion:data[i].resion,
                            element_id:data[i].element_id,
                            element_ids:data[i].element_ids,
                        };
                        this.param.push(data[i]);
                    }else{
                        this.getchildren(data[i]['children']);
                    }
                }
                return true;
            },
            submit: function (status) {
                var _this = this;
                this.getchildren(this.data);
                var param = {
                    status:status,
                    data:this.param,
                };
                if(status==1){
                    _this.$confirm("确认完成打分，确认后将自动计算生成最终得分，生成后不可再修改打分？", "提示", {}).then(() => {
                        _this.loading = true;
                        axios.post("scoreSave?id="+_this.id, param).then(function (res) {
                            _this.loading = false;
                            _this.$message({
                                message: res.data.msg,
                                type: res.data.type
                            });
                            if (res.data.code == 0) {
                                location.href = 'summarize?id='+_this.id;
                            }
                        }).catch(function (error) {
                            _this.loading = false;
                            console.log("出现错误:",error);
                        });
                    });
                }else{
                    _this.loading = true;
                    axios.post("scoreSave?id="+_this.id, param).then(function (res) {
                        _this.loading = false;
                        _this.$message({
                            message: res.data.msg,
                            type: res.data.type
                        });
                    }).catch(function (error) {
                        _this.loading = false;
                        console.log("出现错误:",error);
                    });
                }
            },
            next() {
                if(this.index<this.data.length){
                    this.change(this.index);
                }else{
                    this.$message({
                        message: '已经是最后一个了',
                        type: 'error'
                    });
                }
            },
            change(item) {
                // this.index = key+1;
                this.form = item;
            },
            goBack() {
                location.href = 'index';
            },
        },
        mounted() {
            //获取列表
            this.getData();

        }
    })
</script>


</body>
</html>