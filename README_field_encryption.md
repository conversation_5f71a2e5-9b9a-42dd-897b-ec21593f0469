# 数据库字段加密功能

## 项目概述

本项目为F:\cay\app\admin\controller下的数据库字段加密功能，允许用户选择需要加密的表和其下的多个字段，调用common.php的hsmEncrypt加密方法或hsmDecrypt解密方法，并更新到数据库。

## 文件结构

```
app/admin/controller/
├── FieldEncryption.php          # 主控制器文件

app/admin/service/
├── EncryptionStatusService.php      # 加密状态管理服务
├── DatabaseCompatibilityService.php # 数据库兼容性服务

app/admin/view/field_encryption/
├── index.html                   # 前端管理页面

config/
├── field_encryption.php        # 功能配置文件
├── cache.php                    # 缓存配置（已更新Redis配置）

docs/
├── field_encryption_usage.md           # 基础使用说明
├── field_encryption_advanced_usage.md  # 高级功能说明

test/
├── field_encryption_test.php   # 功能测试脚本

README_field_encryption.md      # 本文件
```

## 功能特性

### 🔐 核心功能
- **批量字段加密**：支持选择多个字段同时进行加密操作
- **批量字段解密**：支持选择多个字段同时进行解密操作
- **表和字段选择**：动态获取数据库表和字段列表供用户选择
- **条件过滤**：支持WHERE条件来限制操作范围

### 🛡️ 安全特性
- **事务保护**：所有数据库操作都在事务中执行
- **预览功能**：执行前可预览操作结果
- **错误处理**：完善的异常处理和错误提示
- **操作确认**：多步骤确认流程防止误操作
- **数据保护**：原始值哈希存储，加密值二次保护
- **缓存安全**：所有缓存键经过SHA256哈希处理

### 🚀 性能优化
- **智能检测**：高效的加密状态判断，避免重复加密
- **Redis缓存**：使用Redis缓存加密状态，大幅提升性能
- **批量处理**：支持大数据量的批量处理
- **启发式算法**：基于HSM加密特征的智能识别

### 🎯 用户体验
- **四步向导**：简洁的四步操作流程
- **实时预览**：操作前后数据对比预览
- **进度反馈**：详细的操作结果统计
- **响应式界面**：基于Element UI的现代化界面
- **统计分析**：实时查看字段加密统计信息
- **缓存管理**：支持手动清除缓存数据

## 快速开始

### 1. 访问功能
```
URL: /admin/field_encryption/index
```

### 2. 操作流程
1. **选择数据表** → 从下拉列表选择要操作的表
2. **配置参数** → 选择字段、操作类型、设置条件
3. **预览结果** → 查看操作前后的数据对比
4. **执行操作** → 确认后执行实际的加密/解密

### 3. API接口
```php
// 获取表列表
GET /admin/field_encryption/getTables

// 获取字段列表
GET /admin/field_encryption/getTableFields?table_name=表名

// 预览操作
POST /admin/field_encryption/previewOperation

// 执行加密
POST /admin/field_encryption/encryptFields

// 执行解密
POST /admin/field_encryption/decryptFields

// 获取加密统计
GET /admin/field_encryption/getEncryptionStats?table_name=表名&field_name=字段名

// 清除缓存
POST /admin/field_encryption/clearEncryptionCache
```

## 数据库兼容性

### 🗄️ 支持的数据库

系统支持多种主流数据库，通过 `DatabaseCompatibilityService` 提供统一的接口：

| 数据库类型 | 获取表列表 | 获取字段信息 | 特殊处理 |
|-----------|-----------|-------------|---------|
| **MySQL** | `SHOW TABLES` | `SHOW FULL COLUMNS` | 标准MySQL语法 |
| **达梦数据库** | `USER_TABLES` | `USER_TAB_COLUMNS` | 系统视图查询 |
| **PostgreSQL** | `pg_tables` | `information_schema.columns` | 系统函数支持 |
| **SQLite** | `sqlite_master` | `PRAGMA table_info` | 轻量级处理 |
| **SQL Server** | `INFORMATION_SCHEMA.TABLES` | `INFORMATION_SCHEMA.COLUMNS` | 标准SQL |

### 🔧 达梦数据库特殊支持

针对达梦数据库的特殊处理：

```php
// 获取所有表
SELECT TABLE_NAME FROM USER_TABLES ORDER BY TABLE_NAME

// 获取表字段详细信息
SELECT
    COLUMN_NAME,        -- 字段名
    DATA_TYPE,          -- 数据类型
    DATA_LENGTH,        -- 数据长度
    DATA_PRECISION,     -- 精度
    DATA_SCALE,         -- 小数位数
    NULLABLE,           -- 是否可空
    DATA_DEFAULT,       -- 默认值
    COMMENTS            -- 字段注释
FROM USER_TAB_COLUMNS
WHERE TABLE_NAME = ?
ORDER BY COLUMN_ID
```

### ⚙️ 自动检测机制

系统会自动检测当前数据库类型并使用相应的SQL语句：

```php
$dbType = DatabaseCompatibilityService::getDatabaseType();
// 根据数据库类型自动选择合适的SQL语句
```

## 技术实现

### 后端技术栈
- **框架**：ThinkPHP 6
- **数据库**：支持MySQL、达梦、PostgreSQL、SQLite、SQL Server等
- **加密**：调用common.php中的HSM加密函数
- **兼容性**：跨数据库兼容性处理

### 前端技术栈
- **框架**：Vue.js 2
- **UI组件**：Element UI
- **HTTP客户端**：Axios

### 核心代码示例

#### 智能加密实现
```php
public function encryptFields() {
    $tableName = input('table_name');
    $fields = input('fields/a', []);

    Db::startTrans();
    try {
        $records = Db::table($tableName)->select();
        foreach ($records as $record) {
            $updateData = [];
            $recordId = $record['id'] ?? 0;

            foreach ($fields as $field) {
                if (isset($record[$field]) && !empty($record[$field])) {
                    // 智能检测是否已加密，避免重复加密
                    if (!$this->encryptionService->isFieldEncrypted($tableName, $field, $recordId, $record[$field])) {
                        $originalValue = $record[$field];
                        $encryptedValue = hsmEncrypt($originalValue);
                        $updateData[$field] = $encryptedValue;

                        // 安全存储加密映射到Redis
                        $this->encryptionService->storeEncryptionMapping(
                            $tableName, $field, $recordId, $originalValue, $encryptedValue
                        );
                    }
                }
            }
            if (!empty($updateData)) {
                Db::table($tableName)->where('id', $record['id'])->update($updateData);
            }
        }
        Db::commit();
        return json(['code' => 200, 'msg' => '加密完成']);
    } catch (\Exception $e) {
        Db::rollback();
        return json(['code' => 500, 'msg' => '加密失败：' . $e->getMessage()]);
    }
}
```

#### 前端操作流程
```javascript
// 加载表列表
loadTables() {
    axios.get('/admin/field_encryption/getTables').then(res => {
        this.tableList = res.data.data;
    });
},

// 执行加密
executeOperation() {
    const url = this.operationType === 'encrypt' 
        ? '/admin/field_encryption/encryptFields' 
        : '/admin/field_encryption/decryptFields';
    
    axios.post(url, {
        table_name: this.selectedTable,
        fields: this.selectedFields,
        where_condition: this.whereCondition
    }).then(res => {
        this.executeResult = res.data;
    });
}
```

## 高级特性

### 🧠 智能加密检测

系统使用多层次的智能检测机制来判断字段是否已加密：

1. **Redis缓存优先**：首先检查Redis中的加密状态缓存
2. **启发式检测**：基于HSM加密特征进行智能识别
3. **配置化规则**：支持自定义检测规则和排除模式

```php
// 智能检测示例
$isEncrypted = $encryptionService->isFieldEncrypted($table, $field, $recordId, $value);
```

### 🔐 Redis安全存储

- **键值哈希**：所有缓存键使用SHA256哈希处理
- **数据保护**：原始值哈希存储，加密值二次AES保护
- **过期管理**：支持配置化的缓存过期时间

```php
// 安全存储加密映射
$encryptionService->storeEncryptionMapping($table, $field, $recordId, $original, $encrypted);
```

### 📊 统计分析

- **实时统计**：查看字段的加密率和统计信息
- **缓存优化**：统计数据缓存提升查询性能
- **可视化展示**：前端图表展示加密进度

### ⚙️ 配置化管理

所有功能都支持通过配置文件进行自定义：

```php
// config/field_encryption.php
'detection' => [
    'heuristic' => [
        'min_length' => 32,
        'max_length' => 1024,
        'check_base64' => true,
    ],
],
'security' => [
    'cipher' => 'AES-256-CBC',
    'protect_suffix' => 'encrypt_protect',
],
```

## 安全注意事项

### ⚠️ 重要提醒
1. **数据备份**：执行操作前务必备份数据库
2. **测试环境**：建议先在测试环境验证功能
3. **权限控制**：确保只有授权用户可以访问此功能
4. **HSM服务**：确保HSM加密服务正常运行
5. **Redis安全**：确保Redis服务的安全配置

### 🔒 安全措施
- 事务回滚机制
- 输入参数验证
- SQL注入防护
- 错误信息脱敏
- 多层数据保护
- 缓存键哈希处理

## 测试

### 运行测试脚本
```bash
php test/field_encryption_test.php
```

### 测试内容
- 表和字段获取功能
- 加密解密操作
- 事务处理
- 错误处理

## 故障排除

### 常见问题

1. **HSM服务不可用**
   ```
   检查hsm-client.jar文件
   确认Java环境配置
   ```

2. **数据库连接失败**
   ```
   检查数据库配置文件
   确认数据库服务状态
   ```

3. **权限不足**
   ```
   确认用户数据库权限
   检查文件系统权限
   ```

## 扩展开发

### 可扩展功能
- 定时批量加密任务
- 操作日志记录
- 权限管理集成
- 加密算法配置
- 数据导入导出

### 自定义配置
```php
// 修改加密判断逻辑
private function isEncrypted($value) {
    // 根据实际HSM加密特征调整判断逻辑
    return preg_match('/特定模式/', $value);
}
```

## 版本信息

- **版本**：1.0.0
- **创建日期**：2025-07-21
- **兼容性**：ThinkPHP 6.x, PHP 7.3+
- **数据库**：MySQL, 达梦数据库

## 联系支持

如有问题或建议，请联系开发团队或查看详细文档：
- 基础使用说明：`docs/field_encryption_usage.md`
- 高级功能说明：`docs/field_encryption_advanced_usage.md`
- 配置文件：`config/field_encryption.php`
- 测试脚本：`test/field_encryption_test.php`

### 📚 文档索引
- **快速开始**：本文档的"快速开始"部分
- **基础功能**：`docs/field_encryption_usage.md`
- **高级特性**：`docs/field_encryption_advanced_usage.md`
- **配置说明**：`config/field_encryption.php`中的详细注释
- **API文档**：控制器中的@Apidoc注释

---

**注意**：本功能涉及敏感数据操作，请在生产环境使用前充分测试并做好数据备份。
