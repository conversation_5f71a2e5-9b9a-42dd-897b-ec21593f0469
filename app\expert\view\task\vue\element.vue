<style>
.my-autocomplete li{line-height: normal;padding: 7px;}
.my-autocomplete li .name{text-overflow: ellipsis;overflow: hidden;}
.my-autocomplete li .addr{font-size: 12px;color: #b4b4b4;}
.my-autocomplete li .highlighted{color: #ddd;}
.el-cascader { width:100%;}
.el-form-item__content .el-input-group { vertical-align: middle;}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 150px;
  height: 150px;
  line-height: 150px;
  text-align: center;
}
.avatar {
  width: 150px;
  height: 150px;
  display: block;
}
</style>
<template>
  <el-dialog :title="title" :close-on-click-modal="false"  modal="false" :visible.sync="visible" width="800px" @close="refresh()" append-to-body="true" label-position="top">
    <el-form ref="form" :model="data" class="tableSearch" label-position="right" label-width="150px" :rules="rules">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="企业名称" prop="company_name">
            {{data.company_name}}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="行业/专业" prop="industry">
            {{data.industry}}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="评审标准" prop="industry">
            {{data.review_name}}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="要素分配" prop="is_advisory">
            <el-table border
                      v-loading="loading"
                      :data="data.elements"
                      style="width: 100%;margin-bottom: 20px;"
                      size="small">
              <el-table-column
                  type="index"
                  label="序号"
                  align="center"
                  width="50">
              </el-table-column>
              <el-table-column
                  prop="name"
                  label="要素名称"
                  align="center"
                  show-overflow-tooltip
                  min-width="200">
              </el-table-column>
              <el-table-column
                  prop="position"
                  label="评审人员"
                  align="center"
                  width="200">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.expert_id" placeholder="请选择">
                    <el-option v-for="item in data.experts" :key="item.expert_id" :label="item.expert_name" :value="item.expert_id" :disabled="item.status!=7"></el-option>
                  </el-select>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-col>
        <el-col :span="24" style="text-align: center;">
          <el-button type="primary" @click="submit()">保存</el-button>
          <el-button @click="visible = false">关闭</el-button>
        </el-col>
      </el-row>
    </el-form>
  </el-dialog>
</template>
<script>
module.exports = {
  name: "add",
  // 模板导入区
  components: {
  },
  data:function() {
    return {
      id:0,
      visible: false,
      title: '新增',
      loading: false,
      data: {
      },
      rules: {
      },
      experts:[],
      imageUrl:'',
    }
  },
  mounted: function(){
    // this.getConfig();
  },
  created:function(){
  },
  methods: {
    /**
     * 打开弹窗调用方法
     * */
    open: function (row) {
      console.log(row)
      row = row?row:{id:0};
      var _this =this;
      _this.visible = true;
      _this.getInfo(row.id);
    },
    submit: function () {
      var _this = this;
      var param = _this.data;
      this.$refs.form.validate(function (valid) {
        if(valid){
          axios.post("elementSave", param).then(function (res) {
            _this.$message({
              message: res.data.msg,
              type: res.data.type
            });
            if (res.data.code == 0) {
              _this.visible = false;
              _this.$emit("ok");
            }
          }).catch(function (error) {
            console.log("出现错误:",error);
          });
        }
      });
    },
    getInfo:function(id){
      var _this = this;
      _this.loading = true;
      axios.post('getElementInfo', {id:id}).then(function (res) {
        _this.loading = false;
        if (res.data.code == 0) {
          _this.data = res.data.data;
          _this.experts = res.data.data.experts;
        }
      }).catch(function (error) {
        _this.loading = false;
        console.log("出现错误:",error);
      });
    },
    refresh: function () {
      this.$emit("refresh");
    },
    changeweekBegin(data){
      console.log(data);
    },
  }
}
</script>