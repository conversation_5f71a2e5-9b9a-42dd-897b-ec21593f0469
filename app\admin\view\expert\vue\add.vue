<style>
.my-autocomplete li{line-height: normal;padding: 7px;}
.my-autocomplete li .name{text-overflow: ellipsis;overflow: hidden;}
.my-autocomplete li .addr{font-size: 12px;color: #b4b4b4;}
.my-autocomplete li .highlighted{color: #ddd;}
.el-cascader { width:100%;}
.el-form-item__content .el-input-group { vertical-align: middle;}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 150px;
  height: 150px;
  line-height: 150px;
  text-align: center;
}
.avatar {
  width: 150px;
  height: 150px;
  display: block;
}
.el-date-editor.el-input, .el-date-editor.el-input__inner{width:auto;}
</style>
<template>
  <el-dialog :title="title" :close-on-click-modal="false"  modal="false" :visible.sync="visible" width="95%" top="10px" @close="refresh()" append-to-body="true" label-position="top">
    <el-form ref="form" :model="data" class="tableSearch" label-position="right" label-width="170px" :rules="rules">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="照片" prop="head">
            <el-upload
                class="avatar-uploader"
                action="upload"
                :show-file-list="false"
                :on-success="(response,file,fileList)=>uploadSuccess(response,file,fileList,'head')"
                :before-upload="uploadBefore">
              <img v-if="data.headUrl" :src="data.headUrl" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="data.name" size="mini"></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input v-model="data.mobile" size="mini"></el-input>
          </el-form-item>
          <el-form-item label="性别" prop="sex">
            <el-radio v-model="data.sex" label="男">男</el-radio>
            <el-radio v-model="data.sex" label="女">女</el-radio>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="所属评审单位" prop="org_id">
            <el-select v-model="data.org_id" size="mini" placeholder="请选择">
              <el-option v-for="v in config.orgs" :key="v.id" :label="v.name" :value="v.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="data.email" size="mini"></el-input>
          </el-form-item>
          <el-form-item label="出生日期" prop="birthday">
            <el-date-picker
                v-model="data.birthday"
                size="mini"
                type="date"
                placeholder="选择日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col></el-col>
        <el-col :span="8">
          <el-form-item label="民族" prop="nation">
            <el-input v-model="data.nation" size="mini"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="QQ" prop="qq">
            <el-input v-model="data.qq" size="mini"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="现住址" prop="address">
            <el-input v-model="data.address" size="mini"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="学校" prop="school">
            <el-input v-model="data.school" size="mini"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="专业" prop="speciality">
            <el-input v-model="data.speciality" size="mini"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="学历" prop="education">
            <el-input v-model="data.education" size="mini"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="现工作单位" prop="employer">
            <el-input v-model="data.employer" size="mini"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="职务" prop="position">
            <el-input v-model="data.position" size="mini"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="参加工作时间" prop="work_date">
            <el-date-picker
                v-model="data.work_date"
                size="mini"
                type="date"
                placeholder="选择日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="从事安全生产工作时间" prop="position_date">
            <el-date-picker
                v-model="data.position_date"
                size="mini"
                type="date"
                placeholder="选择日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="专业技术职称" prop="professional">
            <el-input v-model="data.professional" size="mini"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="职称证书编号" prop="professional_number">
            <el-input v-model="data.professional_number" size="mini"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="安全评价资格师等级" prop="secure">
            <el-input v-model="data.secure" size="mini"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="安全评价师证书编号" prop="secure_number">
            <el-input v-model="data.secure_number" size="mini"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="注册安全工程师证书编号" prop="reg_secure_number">
            <el-input v-model="data.reg_secure_number" size="mini"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="其他证书编号" prop="other_number">
            <el-input v-model="data.other_number" size="mini"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="擅长专业" prop="major">
            <el-input v-model="data.major" size="mini"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="聘用日期" prop="employ_date">
            <el-date-picker
                v-model="data.employ_date"
                size="mini"
                type="date"
                placeholder="选择日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="受聘情况" prop="offer_info">
            <el-input type="textarea" row="3" v-model="data.offer_info" size="mini"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="个人学习及工作简历" prop="resume">
            <el-input type="textarea" row="3" v-model="data.resume" size="mini"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="data.status">
              <el-radio label="1">正常</el-radio>
              <el-radio label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="24" style="text-align: center;">
          <el-button type="primary" @click="submit()">保存</el-button>
          <el-button @click="visible = false">关闭</el-button>
        </el-col>
      </el-row>
    </el-form>
  </el-dialog>
</template>
<script>
module.exports = {
  name: "add",
  // 模板导入区
  components: {
  },
  data:function() {
    return {
      id:0,
      isAdmin: false,
      visible: false,
      title: '新增',
      model: '',
      loading: false,
      user_id: 0,
      date_object:new Date(),
      user_name:'',
      data: {
      },
      rules: {
      },
      config:[],
      contractData:[],
      salesData:[],
      userData:[],
      dels:[],
      dels2:[],
      imageUrl:'',
    }
  },
  mounted: function(){
    // this.getConfig();
  },
  created:function(){
  },
  methods: {
    handleChange(value) {
      var data = [];
      for (var i=0;i<3;i++){
        data.push(value[i]);
      }
      this.data.region = data;
    },
    /**
     * 打开弹窗调用方法
     * */
    open: function (row) {
      console.log(row)
      row = row?row:{id:0};
      var _this =this;
      _this.visible = true;
      _this.getConfig();
      _this.getInfo(row.id);
    },
    submit: function () {
      var _this = this;
      var param = _this.data;
      this.$refs.form.validate(function (valid) {
        if(valid){
          axios.post("expertSave", param).then(function (res) {
            _this.$message({
              message: res.data.msg,
              type: res.data.type
            });
            if (res.data.code == 0) {
              _this.visible = false;
              _this.$emit("ok");
            }
          }).catch(function (error) {
            console.log("出现错误:",error);
          });
        }
      });
    },
    uploadBefore(file) {
      const isJPG = file.type === 'image/jpeg';
      const isPNG = file.type === 'image/png';
      if(!isJPG&&!isPNG){
          this.$message.error('请上传jpg图片');
      }
      return isJPG||isPNG;
    },
    uploadSuccess(res, file,fileList,field) {
      var files = [];
      console.log(res)
      for(var i in fileList){
        files.push(fileList[i].response??fileList[i]);
      }
      this.data[field] = res.data.code;
      this.data[field+'Url'] = res.data.url;
    },
    getConfig:function(){
      var _this = this;
      axios.post('getConfig', {}).then(function (res) {
        if (res.data.code == 0) {
          _this.config = res.data.data;
        }
      }).catch(function (error) {
        console.log("出现错误:",error);
      });
    },
    getInfo:function(id){
      var _this = this;
      _this.loading = true;
      axios.post('getExpertInfo', {id:id}).then(function (res) {
        _this.loading = false;
        if (res.data.code == 0) {
          _this.data = res.data.data;
        }
      }).catch(function (error) {
        _this.loading = false;
        console.log("出现错误:",error);
      });
    },
    refresh: function () {
      this.$emit("refresh");
    },
    changeweekBegin(data){
      console.log(data);
    },
  }
}
</script>