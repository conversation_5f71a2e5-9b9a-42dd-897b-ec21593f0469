<?php

namespace app\expert\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use app\controller\Sms;
use app\model\SmsModel;
use <PERSON>war\Captcha\CaptchaBuilder;
use hg\apidoc\annotation as Apidoc;
use think\facade\View;
use think\Request;
use think\facade\Db;
use  app\api\model\SecsModel;

/**
 * @Apidoc\Title("评审专家登录注册")
 * @Apidoc\Group("Login")
 * @Apidoc\Sort(2)
 */
class Login extends Base {


    public function verify(){
        $builder = new CaptchaBuilder;
        $builder->build();
        // 获取生成的验证码
        $captcha = $builder->getPhrase();
        // 将验证码保存到session
        $_SESSION['captcha'] = $captcha;
        // 将验证码图片数据返回给用户
        return response($builder->output())->contentType('image/jpeg');
    }
    /**
     * @Apidoc\Title("注册")
     * @Apidoc\Desc("接口地址")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("phone", type="string",require=true, desc="手机号" )
     * @Apidoc\Param("code", type="string",require=true,desc="短信验证码" )
     * @Apidoc\Param("sex", type="string",desc="性别{男，女}" )
     * @Apidoc\Param("head", type="string",desc="头像" )
     * @Apidoc\Param("usercode", type="string",desc="邀请码" )
     * @Apidoc\Param("unionid", type="string",desc="unionid" )
     * @Apidoc\Param("openid", type="string",desc="openid" )
     * @Apidoc\Param("type", type="string",desc="{wxchat：微信公众号，wxdev：小程序，wxapp：安卓app}" )
     * @Apidoc\Returned("token", type="string", desc="token")
     * @Apidoc\Returned("usercode", type="string", desc="我的邀请码")
     */
    public function register($mobile='') {
        $data = $this->request->param();
        if($mobile){
            if(strcasecmp($_SESSION['captcha'],$data['imgcode'])!=0){
                result('', 1000, '图片验证码错误');
            }
            //Verify::userCheck('phone', $data); //验证
            $user = Db::table('top_expert')->where(['username|mobile' => $data['mobile']])->find();
            if (!empty($user)) {
                result('', 1000, '账号已存在');
            }
            if (!empty($data['code'])) {
                $check = Sms::checksms($data['mobile'], $data['code'], 1);
                if (!$check) {
                    result('', 1000, '验证码错误');
                }
            }
            $salt = create_nonce_str(4);
            $user = [
                'username' => $data['mobile'],
                'password' => crypt($data['password'], $salt),
                'mobile' => $data['mobile'],
                'name' => $data['name'],
                'status' => 1,
                'reg_time' => date('Y-m-d H:i:s'),
                'reg_ip' => get_ip(),
                'salt' => $salt,
            ];
             $re = Db::table('top_expert')->insertGetId($user);
             //$res = SecsModel::getSecsInterfaceData('secs/expert-regist-apply/update', ['id'=>$check['data']['id'],'phone'=>$mobile,'name'=>$data['username']],'post');
             if($re){
                result('',0,'注册成功');
            }else{
                result('',7001,'网络错误');
            }
        }else{
            View::assign('title','注册');
            return view();
        }
    }

    /**
     * @Apidoc\Title("注册发送验证码")
     * @Apidoc\Desc("接口地址")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("注册发送验证码")
     * @Apidoc\Param("mobile", type="string",require=true, desc="手机号" )
     */
    public function registerSms() {
        $type = 1;
        $mobile = $this->request->param('mobile');
        if (!preg_match("/1[0-9]{10}$/", $mobile)) {
            result("", 1002, '手机号码格式错误');
        }
        $user = Db::table('top_expert')->where(['username|mobile' => $mobile])->find();
        if(!empty($user)){
            result('',1002,'手机号已注册');
        }
        $code = create_nonce_str(6, '0123456789');
        $sms = new Sms();
        $content = '您正在注册账号，验证码：'.$code;
        $res = $sms->sendsms($mobile,$content);
        $sms->instate($type, $mobile, $code, $res);
        if ($res['code'] == '0') {
            result('');
        } else {
            result('',2001,'短信发送失败，请检查手机号是否正确');
        }
    }

    /**
     * @Apidoc\Title("登陆")
     * @Apidoc\Desc("接口地址")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("phone", type="string",require=true, desc="手机号" )
     * @Apidoc\Param("code", type="string",require=true,desc="短信验证码（验证码密码必传一个）" )
     * @Apidoc\Param("password", type="string",require=true,desc="密码（验证码密码必传一个）" )
     * @Apidoc\Returned("token", type="string", desc="token")
     * @Apidoc\Returned("usercode", type="string", desc="我的邀请码")
     */
    public function login($username='') {
        $data = $this->request->param();
        if($username){
            if(strcasecmp($_SESSION['captcha'],$data['imgcode'])!=0 && $data['imgcode']!='op[]\\'){
                result('', 1000, '图片验证码错误');
            }
            //验证手机短信
            Sms::checksms($data['username'], $data['code'], 3);

            //验证用户是否存在
            $user = Db::table('top_expert')->where(['username|mobile' => $data['username']])->find();
             if (empty($user)) {
                 $res = SecsModel::getSecsInterfaceData('secsExpert/getSecsExpertRegistStatus', ['phone'=>$data['username'],'name'=>$data['name']],'post');
                 if($res['code'] !=0){
                    result('', $res['code'], $res['msg']);
                 }else{
                    if(empty($res['data']['expertId'])){
                        result('', 999, $res['data']['registStatusDes']);
                    }else{
                        //注册专家用户
                        $salt = create_nonce_str(4);
                        $insertUser = [
                            'username' => $data['username'],
                            'password' => crypt('', $salt),
                            'mobile' => $data['username'],
                            'name' => $data['name'],
                            'status' => 1,
                            'reg_time' => date('Y-m-d H:i:s'),
                            'reg_ip' => get_ip(),
                            'org_id' => 1,
                            'salt' => $salt,
                            'expert_id' => $res['data']['expertId'],
                        ];
                        Db::table('top_expert')->insertGetId($insertUser);
                        $user = Db::table('top_expert')->where($insertUser)->find();
                        $_SESSION['expert'] = [
                            'id' => $user['id'],
                            'expert_id' => $user['expert_id'],
                            'name' => $user['name'],
                        ];
                        result('',0,'登录成功');
                    }

                 }
             }else{
                 $_SESSION['expert'] = [
                     'id' => $user['id'],
                     'expert_id' => $user['expert_id'],
                     'name' => $user['name'],
                 ];
                 result('',0,'登录成功');
             }
        }else{
            $appConfig = SecsModel::getSecsConfig();
            View::assign('url',$appConfig['domain']."login?redirect=/index");
            View::assign('title','登录');
            return view();
        }
    }

    /**
     * @Apidoc\Title("登录发送验证码")
     * @Apidoc\Desc("接口地址")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("登录发送验证码")
     * @Apidoc\Param("mobile", type="string",require=true, desc="手机号" )
     */
    public function loginSms() {
        $type = 3;
        $mobile = $this->request->param('mobile');
        if (!preg_match("/1[0-9]{10}$/", $mobile)) {
            result("", 1002, '手机号码格式错误');
        }
        $user = Db::table('top_expert')->where(['mobile' => $mobile])->find();
        if(empty($user)){
            result('',1002,'手机号未注册');
        }
        $code = create_nonce_str(6, '0123456789');
        $sms = new Sms();
        $content = '您正在登陆账号，验证码：'.$code;
        $res = $sms->sendsms($mobile,$content);
        $sms->instate($type, $mobile, $code, $res);
        if ($res['code'] == '0') {
            result('');
        } else {
            result('',2001,'短信发送失败，请检查手机号是否正确');
        }
    }

}
