<style scoped>
.my-autocomplete li{line-height: normal;padding: 7px;}
.my-autocomplete li .name{text-overflow: ellipsis;overflow: hidden;}
.my-autocomplete li .addr{font-size: 12px;color: #b4b4b4;}
.my-autocomplete li .highlighted{color: #ddd;}
.el-cascader { width:100%;}
.el-form-item__content .el-input-group { vertical-align: middle;}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 150px;
  height: 150px;
  line-height: 150px;
  text-align: center;
}
.avatar {
  width: 150px;
  height: 150px;
  display: block;
}
.el-form-item__error {
    color: #F56C6C;
    font-size: 12px;
    line-height: 1;
    padding-top: 4px;
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 10;
}
</style>
<template>
  <el-dialog :title="title" :close-on-click-modal="false"  modal="false" :visible.sync="visible" width="700px" append-to-body="true" label-position="top">
    <el-form ref="form" :model="data" class="tableSearch" label-position="right" label-width="150px" :rules="rules">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="作业项目" prop="work_item">
            <el-input v-model="data.work_item" size="small" placeholder="请输入作业项目"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="作业时间段" prop="validity">
            <el-date-picker
                style="width: 140px"
                v-model="data.work_time_start"
                :picker-options="startPickerOptions"
                @change="handleStartChange"
                size="small"
                type="date"
                placeholder="选择日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd">
            </el-date-picker>
            ~
            <el-date-picker
                style="width: 140px"
                v-model="data.work_time_end"
                :picker-options="endPickerOptions"
                @change="handleEndChange"
                size="small"
                type="date"
                placeholder="选择日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="作业地点" prop="work_place">
            <el-input v-model="data.work_place" size="small" placeholder="请输入作业地点"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="作业风险" prop="work_risk">
            <el-input v-model="data.work_risk" size="small" placeholder="请输入作业风险"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="审批人" prop="examin_user">
            <el-input v-model="data.examin_user" size="small" placeholder="请输入审批人"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="上传作业许可证" prop="standard_id">
            <el-upload
                class="upload-demo"
                action="upload"
                :on-success="uploadSuccess"
                :before-upload="uploadBefore"
                :on-remove="handleRemove"
                :on-preview="Previewf"
                accept=".jpg,.png,.pdf"
                multiple
                :limit="3"
                :file-list="data.files">
              <el-button size="small" type="primary">点击上传</el-button>
              <div slot="tip" class="el-upload__tip">只能上传jpg/png/pdf文件</div>
            </el-upload>
          </el-form-item>
        </el-col>
        <el-col :span="24" style="text-align: center;margin-top: 20px;">
          <el-button type="primary" size="small" @click="submit()">保存</el-button>
          <el-button size="small" @click="visible = false">关闭</el-button>
        </el-col>
      </el-row>
    </el-form>
  </el-dialog>
</template>
<script>
module.exports = {
  name: "add",
  data:function() {
    return {
      visible: false,
      title: '作业许可编辑',
      loading: false,
      data: {
        id: '',
        work_item: '',
        work_time_start: '',
        work_time_end: '',
        work_place: '',
        work_risk: '',
        examin_user: '',
        work_permit: '',
        files:[],
      },
      startPickerOptions: {
        disabledDate: (time) => {
          if (this.data.work_time_end) {
            return time.getTime() > new Date(this.data.work_time_end).getTime()
          }
          return false
        }
      },
      endPickerOptions: {
        disabledDate: (time) => {
          if (this.data.work_time_start) {
            return time.getTime() < new Date(this.data.work_time_start).getTime()
          }
          return false
        }
      },
      rules: {
        work_item: [{ required: true, message: '请填写作业项目', trigger: 'blur' }],
      },
      imageUrl:'',
    }
  },
  mounted: function(){
  },
  created:function(){
  },
  methods: {
    handleStartChange(val) {
      if (this.data.work_time_end && new Date(val) > new Date(this.data.work_time_end)) {
        this.data.work_time_end = ''
      }
    },
    handleEndChange(val) {
      if (this.data.work_time_start && new Date(val) < new Date(this.data.work_time_start)) {
        this.data.work_time_start = ''
      }
    },
    /**
     * 打开弹窗调用方法
     * */
    open: function (row) {
      var _this =this;
      _this.visible = true;

      if( row != '' && row != null && row != undefined )
      {
        _this.data = row;
      }
    },
    submit: function () {
      var _this = this;
      var param = _this.data;
      this.$refs.form.validate(function (valid) {
        if(valid){
          axios.post("specialWorkSave", param).then(function (res) {
            _this.$message({
              message: res.data.msg,
              type: res.data.type
            });
            if (res.data.code == 0) {
              _this.visible = false;
              _this.$emit("ok");
            }
          }).catch(function (error) {
            console.log("出现错误:",error);
          });
        }
      });
    },
    uploadBefore(file) {
      const isJPG = file.type === 'image/jpeg';
      const isPNG = file.type === 'image/png';
      const isPDF = file.type === 'application/pdf';
      if(!isJPG&&!isPNG&&!isPDF){
        this.$message.error('请上传jpg/png/pdf文件');
      }
      return isJPG||isPNG||isPDF;
    },
    uploadSuccess(res, file,fileList) {
      this.data.files.push(file.response.data);
    },
    handleRemove(file, fileList) {
      var files = [];
      for(var i in fileList){
        files.push(fileList[i].response.data??fileList[i]);
      }
      this.data.files = files;
    },
    Previewf(file) {
      if (file) {
        const addTypeArray = file.name.split(".");
        const addType = addTypeArray[addTypeArray.length - 1];
        const url = file.response?file.response.data.url:file.url;
        console.log(addType);
        if (addType === "pdf") {
          let routeData = this.$router.resolve({
            path: "/insurancePdf",
            query: { url: url, showBack: false },
          });
          window.open(routeData.href, "_blank");
        } else if (addType === "txt") {
          window.open(url);
        } else if (["png", "jpg", "jpeg"].includes(addType)) {
          window.open(url);
        } else {
          this.$message({
            message: "该文件类型暂不支持预览",
            type: "warning",
          });
          return false;
        }
      }
    },
  }
}
</script>