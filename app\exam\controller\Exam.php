<?php

namespace app\exam\controller;

use app\BaseController;
use app\model\ExcelModel;
use app\model\SettingModel;
use PHPExcel_IOFactory;
use think\Exception;
use think\facade\Db;
use think\Request;
use think\facade\View;

class Paper extends BaseController
{

    protected $type = 'paper';//

    public function createOnePaper(Request $request)
    {
        try {
            $task_id = $request->param('task_id', "");
            $position_name = $request->param('position_name', "");
            $phone = $request->param('phone', "");
            if (empty($task_id)) {
                throw new Exception("任务id为空");
            }
            if (empty($position_name)) {
                throw new Exception("岗位未填写");
            }
            if (empty($phone)) {
                throw new Exception("手机号未填写");
            }
            $file = $request->file('file');
            set_time_limit(0);
            ini_set('memory_limit', '2048M');
            $file_type = PHPExcel_IOFactory::identify($file);
            $reader = PHPExcel_IOFactory::createReader($file_type);
            $spreadSheet = $reader->load($file);
            $sheet = $spreadSheet->getActiveSheet();
            $data = $sheet->toArray(null, true, true, true);
            if (empty($data) || count($data) == 1) {
                throw new Exception("数据不能为空！");
            }
            Db::startTrans();
            unset($data[1]);
            foreach ($data as $item) {

                //解析类型
                $type_id = Db::table('top_cay_exam_test_type')->where('type_name', $item['D'])->value('id');
                if (empty($type_id)) {
                    throw new Exception("试题类型未选择");
                }

                //解析选项
                $test_title = $item['A'];
                $test_answer = $item['B'];
                $test_analysis = empty($item['C']) ? "" : $item['C'];
                unset($item['A']);
                unset($item['C']);
                unset($item['B']);
                unset($item['D']);
                $test_option = self::removeNullValues($item);
                $test_option_res = [];
                foreach ($test_option as $key => $value) {
                    $test_option_res[] = $value;
                }
                $test_option_str = json_encode($test_option_res, JSON_UNESCAPED_UNICODE);
                //解析答案
                $test_answer = str_replace('、', ",", $test_answer);
                $test_answer = str_replace('选项', "", $test_answer);
                $test_answer = explode(',', $test_answer);
                $test_answer_char = "";
                if (count($test_answer) != 1) {
                    throw new Exception("答案有误");
                }

                $test_answer = implode(',', $test_answer);
                if ($type_id == 1) {

                    $char = self::IntToChr($test_answer - 1);
                    $test_answer_char = $char;
                } else {
                    //解析答案
                    $test_answer = $test_answer + 5;
                    $char = self::IntToChr($test_answer);

                    $test_answer_char = $item[$char];
                }

                $insertData = [
                    'test_title' => $test_title,
                    'test_option' => $test_option_str,
                    'test_answer' => $test_answer_char,
                    'test_analysis' => $test_analysis,
                    'type_id' => $type_id,
                    'industry_ids' => $industry_ids,
                    'create_user_name' => $_SESSION['LOGIN_USER_NAME'],
                    'create_user_id' => $_SESSION['LOGIN_USER_ID'],
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                    'deleted_at' => null,
                ];
                //插入数据
                Db::table('top_cay_exam_test_info')
                    ->insert($insertData);
            }
            Db::commit();
            return json(['code' => 1000, 'type' => 'success', 'msg' => "导入成功"]);
        } catch (\Exception $exception) {
            Db::rollback();
            return json(['code' => 201, 'type' => 'error', 'msg' => $exception->getMessage()]);
        }
    }


}