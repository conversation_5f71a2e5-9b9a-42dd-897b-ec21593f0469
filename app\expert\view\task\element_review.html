<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>现场评审</title>
    <link href="__PUBLIC__/assets/css/base.css" rel="stylesheet">
    <link href="__PUBLIC__/assets/element/index.css" rel="stylesheet">
    <link href="__PUBLIC__/static/css/style.css" rel="stylesheet" >
    <script src="__PUBLIC__/static/js/browser.min.js" type="text/javascript"></script>
    <script src="__PUBLIC__/static/js/polyfill.min.js"></script>
    <script src="__PUBLIC__/assets/element/vue.js"></script>
    <script src="__PUBLIC__/assets/element/index.js"></script>
    <script src="__PUBLIC__/assets/js/axios.min.js"></script>
    <script src="__PUBLIC__/static/js/es6-promise.auto.min.js"></script>
    <script src="__PUBLIC__/static/js/request.js"></script>
    <script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
    <script src="__PUBLIC__/assets/libs/jquery/jquery.min.js"></script>
    <style>
        .my-title { font-weight: 700;line-height: 30px; margin-top: 20px;}
        .my-content { text-indent: 30px;line-height: 40px;}
        .my-files { margin-left: 30px;}
        .my-files label { float: left;}
        .my-upload .el-upload--picture-card {width: 115px;height: 115px;line-height: 115px;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <el-row :gutter="20">
        <el-col>
            <el-descriptions class="margin-top" title="现场评审" column="3" border>
                <template slot="extra">
                    <el-button v-if="'{$plan.status}'==8" type="primary" size="small" @click="submit(1)">提交</el-button>
                    <el-button v-if="'{$plan.status}'==7||'{$plan.status}'==8" type="success" size="small" @click="submit()">保存</el-button>
                </template>
                <el-descriptions-item :labelStyle="{'width':'100px'}">
                    <template slot="label">
                        <i class="el-icon-office-building"></i>
                        单位名称
                    </template>
                    {$company.company_name}
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        <i class="el-icon-office-building"></i>
                        行业
                    </template>
                    {$company.industry_name}
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        <i class="el-icon-office-building"></i>
                        申请等级
                    </template>
                    {$company.apply_level}级
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        <i class="el-icon-user"></i>
                        法定代表人
                    </template>
                    {$company.legal_person}
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        <i class="el-icon-mobile-phone"></i>
                        电话
                    </template>
                    {$company.legal_person_tel}
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        <i class="el-icon-mobile-phone"></i>
                        手机
                    </template>
                    {$company.legal_person_tel}
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        <i class="el-icon-user"></i>
                        安全管理联系人
                    </template>
                    {$company.contacts}
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        <i class="el-icon-mobile-phone"></i>
                        电话
                    </template>
                    {$company.contacts_tel}
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        <i class="el-icon-mobile-phone"></i>
                        手机
                    </template>
                    {$company.contacts_tel}
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        <i class="el-icon-mobile-phone"></i>
                        状态
                    </template>
                    <el-tag v-if="'{$plan.status}'==7" type="success">等待现场评审</el-tag>
                    <el-tag v-if="'{$plan.status}'==8" type="warning">现场评审中</el-tag>
                    <el-tag v-if="'{$plan.status}'==9" type="info">结束评审</el-tag>
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        <i class="el-icon-location-outline"></i>
                        单位地址
                    </template>
                    {$company.company_residence}
                </el-descriptions-item>
            </el-descriptions>
        </el-col>
        <el-col>
            <h3 style="line-height: 50px;font-size: 18px;">要素评分</h3>
            <table class="mytable">
                <thead>
                <tr>
                    <td style="width: 130px;" v-for="item in data.elementTitle">{{item}}</td>
                    <td style="width: 200px;">基本规范要求</td>
                    <td style="width: 200px;">企业达标标准</td>
                    <td style="width: 80px;">标准分值</td>
                    <td style="width: 80px;">评分方式</td>
                    <td style="width: 200px;">上报材料</td>
                    <td style="width: 80px;">得分</td>
                    <td style="width: 200px;">扣分</td>
                    <td style="width: 130px;">扣分说明</td>
                    <td style="width: 130px;">缺项分值</td>
                    <td style="width: 130px;">是否必须整改</td>
                    <td style="width: 130px;">备注</td>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(item,key) in data.content">
                    <td :rowspan="item['rows'+k]" v-for="(v,k) in data.elementTitle" v-if="item['rows'+k]>0">
                        {{item['name'+k]}}
                    </td>
                    <td>{{item.ask}}</td>
                    <td>{{item.standards}}</td>
                    <td>{{item.scoret}}</td>
                    <td v-html="item.method"></td>
                    <td>
                        <el-link style="margin-right:5px; font-size:13px;color: #2444d8" v-for="(v,i) in item.sub_files" target="_blank" :href="v.url"  :key="i">{{v.name}}</el-link>
                    </td>
                    <td>
                        <el-input-number style="width: 80px;" v-model="item.score" @change="handleChange(item)" controls-position="right" :min="0" :max="item.scoret"></el-input-number>
                    </td>
                    <td>{{item.koufen}}
                    </td>
                    <td>
                        <el-input :style="{border: item.koufen>0?'red solid 1px':'0'}" type="textarea" :rows="2" :autosize="{minRows:3}" placeholder="" v-model="item.summary"></el-input>
                        <el-button type="primary" size="small">佐证材料选择</el-button>
                        <el-upload
                                class="my-upload"
                                action="/module/upload/upload.php"
                                list-type="picture-card"
                                :file-list="item.filelist"
                                :limit="10"
                                :on-success="(response,file,fileList)=>uploadSuccess(response,file,fileList,item)"
                                :before-upload="uploadBefore"
                                :on-preview="handlePictureCardPreview"
                                :on-remove="(file,fileList)=>handleRemove(file,fileList,item)">
                            <i class="el-icon-plus"></i>
                        </el-upload>
                    </td>
                    <td>
                        <el-input :autosize="{minRows:3}" placeholder="" v-model="item.que"></el-input>
                    </td>
                    <td>
                        <el-checkbox v-model="item.is_zg" label="1" :checked="item.is_zg==1">是</el-checkbox>
                    </td>
                    <td>
                        <el-input type="textarea" :rows="2" :autosize="{minRows:3}" placeholder="" v-model="item.remark"></el-input>
                    </td>
                </tr>
                </tbody>
            </table>
        </el-col>
    </el-row>
    <el-dialog :visible.sync="dialogVisible">
        <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
    <pingshen ref="pingshen" @ok="index"></pingshen>
</div>
<script src="__PUBLIC__/assets/js/common.js"></script>
<script type="text/babel">
    Vue.use(httpVueLoader);
    new Vue({
        el: '#app',
        data: function () {
            return{
                active:0,
                dialogImageUrl:'',
                dialogVisible:false,
                searchData:{
                    status:1,
                },
                review_flow:[],
                form: {
                    'company_name':''
                },
                loading: false,
                page_data: {
                    limit: 10,
                    page: 1,
                    total:10,
                },
                order:'',
                sort:'',
                files: [],
                single:true,
                multiple:true,
                data: [],
                exceldata:[],
                dumppage:1,
                ws:{},
                dumpshow:false,
                percentage:0,
                filename:'',
                tableHeight:document.documentElement.clientHeight-250,
                isAdvanced:false,
                is_clear:false,
                tabsActive:1,
                num_v1:0,
                num_v2:0,
                num_v3:0,
                num_v4:0,
            }
        },
        components:{
            'pingshen':'url:/general/secure/app/expert/view/reviewtasks/vue/pingshen.vue?v=1',
            'info':'url:/general/secure/app/city/view/check/vue/info.vue?v=1',
            'check':'url:/general/secure/app/city/view/check/vue/check.vue?v=1',
        },
        methods: {
            on_click(e){
                console.log(e)
                if(e != "" || e != null){ this.active = e }
            },
            next(){
                this.active++;
            },
            handleChange(item){
                item.koufen = item.scoret-item.score.toFixed();
            },
            start(){
                this.$confirm('确定评审人员已到齐，开始评审?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.loading = true;
                    axios.post('reviewStart',{id:{$plan.id}}).then(res => {
                        this.loading = false
                        if(res.data.code === 0){
                            location.reload();
                        }else{
                            this.$message.error(res.data.msg);
                        }
                    })
                }).catch(() => {
                    /*this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });*/
                });
            },
            index(){
                var _this = this;
                _this.loading = true
                axios.post('elementReview',{id:'{$re.id}'}).then(res => {
                    _this.loading = false
                    if(res.data.code === 0){
                        _this.data = res.data.data
                    }else{
                        _this.$message.error(res.data.msg);
                    }
                })
            },
            uploadBefore(file) {
                const isJPG = file.type === 'image/jpeg';
                const isPNG = file.type === 'image/png';
                if(!isJPG&&!isPNG){
                    this.$message.error('请上传jpg图片');
                }
                return isJPG||isPNG;
            },
            uploadSuccess(res, file,fileList,item) {
                item.filelist = fileList;
            },
            handleRemove(file, fileList,item) {
                item.filelist = fileList;
            },
            handlePictureCardPreview(file) {
                this.dialogImageUrl = file.url;
                this.dialogVisible = true;
            },
            pingshen() {
                this.$refs.pingshen.open('{$plan.id}');
            },
            success(id) {
                this.$confirm('确定该项工作已完成?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.loading = true;
                    axios.post('worksuccess',{id:id}).then(res => {
                        this.loading = false
                        if(res.data.code === 0){
                            this.index();
                        }else{
                            this.$message.error(res.data.msg);
                        }
                    })
                }).catch(() => {
                });
            },
            /*流程办理*/
            info : function(row) {
                this.$refs.info.open(row);
            },
            /*流程办理*/
            checkData : function(row) {
                this.$refs.check.open(row);
            },
            selection(selection) {
                this.ids = selection.map(item => item.id)
                this.single = selection.length !== 1
                this.multiple = !selection.length
            },
            handleRowClick(row, rowIndex,event){
                if(event.target.className !== 'el-input__inner'){
                    this.$refs.multipleTable.toggleRowSelection(row)
                }
            },
            rowClass ({ row, rowIndex }) {
                for(let i=0;i<this.ids.length;i++) {
                    if (row.id === this.ids[i]) {
                        return 'rowLight'
                    }
                }
            },
            sortChange(val){
                if(val.order === 'descending'){
                    this.order= 'desc'
                }
                if(val.order === 'ascending'){
                    this.order= 'asc'
                }
                this.sort = val.prop
                this.index()
            },
            fn(method){
                this[method](this.ids)
            },
            onSubmit() {
                this.page_data.page = 1;
                this.index();
            },
            submit(sub) {
                var msg = sub==1?'确定提交？提交后不可修改。':'确定保存？';
                var _this = this;
                _this.$confirm(msg, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    _this.loading = true;
                    var param = _this.data;
                    param.id = '{$re.id}';
                    param.sub = sub;
                    axios.post('elementReviewSave',param).then(res => {
                        this.loading = false
                        if(res.data.code === 0){
                            this.index();
                        }else{
                            this.$message.error(res.data.msg);
                        }
                    })
                }).catch(() => {
                });
            },
            searchFun(){
                this.page_data.page = 1;
                this.index();
            },
            advancedToggle(data){
                this.isAdvanced=data;
            },
            clearSearch : function() {
                this.form.company_name = '';
                this.index();
            },
            selectTab(tab){
                this.searchData.status = tab
                this.tabsActive = tab
                this.form.company_name = '';
                this.index()
            },
            resetSearch:  function(){
                var _this = this;
                _this.searchData.manage_address = '';
                _this.searchData.create_time = '';
            },
            //计算table高度(动态设置table高度)
            getTableHeight() {
                let tableH = 250; //距离页面下方的高度
                let tempHeight = window.innerHeight - tableH;
                if (tempHeight <= 250) {
                    this.tableHeight =250;
                }
                this.tableHeight = tempHeight;
            },
        },
        mounted(){
            //挂载window.onresize事件(动态设置table高度)
            let _this = this;
            window.onresize = () => {
                if (_this.resizeFlag) {
                    clearTimeout(_this.resizeFlag);
                }
                _this.resizeFlag = setTimeout(() => {
                    _this.getTableHeight();
                    _this.resizeFlag = null;
                }, 1000);
            };
            _this.index()
        },
    })
</script>
</body>
</html>