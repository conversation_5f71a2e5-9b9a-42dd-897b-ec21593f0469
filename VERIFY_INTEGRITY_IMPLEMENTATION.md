# 验证完整性功能实现文档

## 功能概述

根据用户需求："增加一个批量操作'验证完整性'，逐一检测数据的完整性，并实时更新check_status字段的状态"，我们实现了完整的数据完整性验证功能。

## 实现的功能

### 1. 后端API实现

#### 新增方法：`verifyIntegrity()`
- **位置**: `app/admin/controller/FieldEncryption.php`
- **功能**: 批量验证数据完整性并实时更新check_status字段
- **参数**:
  - `table_name`: 表名（必需）
  - `fields`: 要验证的字段列表（必需）
  - `where_condition`: WHERE条件（可选）

#### 核心验证逻辑：`verifyFieldIntegrity()`
- **功能**: 验证单个字段的完整性
- **验证规则**:
  - **加密数据**: 尝试解密，检查解密是否成功
  - **明文数据**: 检查数据格式和长度是否合理
  - **空值处理**: 空值认为是有效的
  - **异常处理**: 捕获并记录所有验证异常

### 2. 前端界面增强

#### 操作类型扩展
- 在现有的"加密"和"解密"选项基础上，新增"验证完整性"选项
- 位置：`app/admin/view/field_encryption/index.html` 第98-104行

#### 结果显示优化
- 新增完整性验证特有的统计信息显示
- 显示"完整性正常"和"完整性异常"的记录数量
- 使用不同颜色区分正常和异常状态

#### 操作逻辑更新
- 扩展`executeOperation()`方法支持验证完整性操作
- 新增`getOperationText()`方法动态显示操作类型文本

### 3. 预览功能支持

#### 预览操作扩展
- 修改`previewOperation()`方法支持验证完整性预览
- 在预览中显示每个字段的完整性状态
- 显示格式：`原始数据 (完整性状态说明)`

## 技术实现细节

### 1. 数据完整性检测算法

```php
private function verifyFieldIntegrity($value, $fieldName) {
    // 1. 空值检查
    if (empty($value)) {
        return ['is_valid' => true]; // 空值认为有效
    }
    
    // 2. 加密数据检测
    $isEncrypted = heuristicEncryptionCheck($value);
    
    if ($isEncrypted) {
        // 3. 加密数据解密验证
        $decryptedValue = hsmDecrypt($value);
        if ($decryptedValue === false) {
            return ['is_valid' => false, 'error_message' => '解密失败'];
        }
    } else {
        // 4. 明文数据格式验证
        if (strlen($value) > 65535) {
            return ['is_valid' => false, 'error_message' => '数据长度超限'];
        }
    }
    
    return ['is_valid' => true];
}
```

### 2. 批量处理流程

1. **参数验证**: 检查表名、字段列表、check_status字段存在性
2. **主键检测**: 动态获取表的主键字段
3. **记录查询**: 根据WHERE条件获取需要验证的记录
4. **逐一验证**: 对每条记录的每个字段进行完整性验证
5. **实时更新**: 立即更新每条记录的check_status字段
6. **统计汇总**: 统计验证成功、失败、正常、异常的记录数量

### 3. 错误处理机制

- **事务保护**: 使用数据库事务确保数据一致性
- **异常捕获**: 捕获并记录所有验证过程中的异常
- **详细日志**: 记录每个失败记录的具体错误信息
- **回滚机制**: 发生严重错误时自动回滚事务

## API接口规范

### 请求格式
```json
POST /admin/field_encryption/verifyIntegrity
{
    "table_name": "users",
    "fields": ["name", "email", "phone"],
    "where_condition": "id > 0 AND status = 1"
}
```

### 响应格式
```json
{
    "code": 200,
    "msg": "完整性验证完成",
    "data": {
        "success_count": 95,
        "error_count": 5,
        "total_count": 100,
        "valid_count": 90,
        "invalid_count": 10,
        "errors": [
            "记录id 123: 解密失败",
            "记录id 456: 数据长度超限"
        ]
    }
}
```

## 使用说明

### 1. 前端操作步骤
1. 选择要验证的数据库表
2. 选择操作类型为"验证完整性"
3. 选择要验证的字段
4. 可选：设置WHERE条件筛选记录
5. 预览验证结果（可选）
6. 执行验证操作

### 2. 验证结果解读
- **成功处理**: 成功验证的记录数量
- **失败记录**: 验证过程中出错的记录数量
- **完整性正常**: check_status设置为1的记录数量
- **完整性异常**: check_status设置为0的记录数量

### 3. 注意事项
- 确保目标表包含check_status字段
- 验证过程会实时更新数据库，请谨慎操作
- 建议先使用WHERE条件在小范围内测试
- 大量数据验证可能需要较长时间

## 测试文件

1. **PHP测试脚本**: `test_verify_integrity.php`
   - 测试heuristicEncryptionCheck函数
   - 测试字段完整性验证逻辑
   - 模拟批量验证操作

2. **HTML测试页面**: `test_verify_integrity.html`
   - 提供可视化的API测试界面
   - 实时显示验证结果和统计信息
   - 包含详细的使用说明

## 集成说明

该功能已完全集成到现有的字段加密管理系统中：
- 与现有的加密/解密操作保持一致的用户界面
- 复用现有的数据库兼容性服务
- 使用统一的HSM加密/解密函数
- 遵循现有的错误处理和日志记录规范

验证完整性功能现在可以与加密和解密操作一起使用，为数据安全管理提供了完整的解决方案。
