<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>申请自评</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/company.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .mytable {border-collapse:collapse;width: 100%;}
        .mytable thead tr td {border: 1px solid #333;font-weight: 700; padding: 10px;background: #f5f7fa;text-align: center;}
        .mytable tbody tr th {border: 1px solid #333;font-weight: 700; padding: 10px;text-align: center;font-size: 16px;}
        .mytable tbody tr td {border: 1px solid #333; padding: 10px;text-align: center;font-size: 16px;}
        .mytable .active td{ background: #f2f2f2;}
        .mytable tbody tr td {font-family: 宋体;text-align: left;}
        .mytable tbody tr td p{line-height: 30px;}
        .el-divider.el-divider--horizontal { margin:10px 0;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">
        <el-page-header @back="goBack" content="申请自评"></el-page-header>
        <el-divider style="margin:10px 0;"></el-divider>
        <table class="mytable">
            <tbody>
            <tr>
                <th colspan="2" style="width: 120px;"><label class="my-label">年&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;度</label></th>
                <td colspan="5" style="width: 700px;">
                    <el-date-picker
                            size="small"
                            v-model="data.year"
                            type="year"
                            placeholder="选择年"
                            format="yyyy"
                            value-format="yyyy">
                    </el-date-picker>
                </td>
            </tr>
            <tr>
                <th colspan="2" style="width: 120px;"><label class="my-label">企&nbsp;业&nbsp;名&nbsp;称</label></th>
                <td colspan="5" style="width: 700px;"><div class="my-online">{{data.company_name}}</div></td>
            </tr>
            <tr>
                <th colspan="2" style="width: 120px;"><label class="my-label">住&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;所</label></th>
                <td colspan="5" style="width: 700px;"><div class="my-online">{{data.residence}}</div></td>
            </tr>
            <tr>
                <th colspan="2" style="width: 120px;"><label class="my-label">类&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;型</label></th>
                <td colspan="5" style="width: 700px;">
                    {{data.economy_type}}
                </td>
            </tr>
            <tr>
                <th colspan="2" style="width: 120px;"><label class="my-label">安全管理机构</label></th>
                <td colspan="5" style="width: 700px;">
                    <el-input size="small" v-model="data.government" placeholder="请输入内容">{{data.government}}</el-input>
                </td>
            </tr>
            <tr>
                <th colspan="2" style="width: 120px;"><label class="my-label">法定代表人</label></th>
                <td style="width: 200px;"><div class="my-online">{{data.legal}}</div></td>
                <th style="width: 120px;"><label class="my-label">电话</label></th>
                <td style="width: 500px;"><div class="my-online">{{data.legal_mobile}}</div></td>
                <th style="width: 120px;"><label class="my-label">传真</label></th>
                <td style="width: 200px;"><div class="my-online">{{data.legal_fax}}</div></td>
            </tr>
            <tr>
                <th colspan="2" rowspan="2" style="width: 120px;"><label class="my-label">联&nbsp;&nbsp;&nbsp;系&nbsp;&nbsp;&nbsp;人</label></th>
                <td rowspan="2" style="width: 200px;">
                    <el-input size="small" v-model="data.contacts" placeholder="请输入内容"></el-input>
                </td>
                <th style="width: 120px;"><label class="my-label">电话</label></th>
                <td style="width: 200px;">
                    <el-input size="small" ref="contacts_tel" @change="validatePhone" maxlength="12" v-model="data.contacts_tel" placeholder="请输入内容"></el-input>
                </td>
                <th style="width: 120px;"><label class="my-label">传真</label></th>
                <td style="width: 200px;">
                    <el-input size="small" ref="contacts_fax" @change="validatePhone2" maxlength="12" v-model="data.contacts_fax" placeholder="请输入内容"></el-input>
                </td>
            </tr>
            <tr>
                <th style="width: 120px;"><label class="my-label">手机</label></th>
                <td style="width: 200px;">
                    <el-input size="small" ref="contacts_mobile" @change="validateTel" maxlength="11" v-model="data.contacts_mobile" placeholder="请输入内容"></el-input>
                </td>
                <th style="width: 120px;"><label class="my-label">电子邮箱</label></th>
                <td style="width: 200px;">
                    <el-input size="small" ref="contacts_email" @change="validateEmail" maxlength="254" v-model="data.contacts_email" placeholder="请输入内容"></el-input>
                </td>
            </tr>
            <tr>
                <th colspan="3" style="width: 120px;"><label class="my-label">本次自评前本企业曾经取得的标准化等级：</label></th>
                <td colspan="4" style="width: 700px;">
                    <el-radio-group v-model="data.old_level">
                        <el-radio label="一级">一级</el-radio>
                        <el-radio label="二级">二级</el-radio>
                        <el-radio label="三级">三级</el-radio>
                        <el-radio label="小微企业">小微企业</el-radio>
                        <el-radio label="无">无</el-radio>
                    </el-radio-group>
                </td>
            </tr>
            <tr>
                <th colspan="4" style="width: 120px;"><label class="my-label">如果是某企业集团的成员单位，请注明企业集团名称：</label></th>
                <td colspan="3" style="width: 700px;">
                    <el-input size="small" v-model="data.group_name" placeholder="请输入内容">{{data.group_name}}</el-input>
                </td>
            </tr>
            </tbody>
        </table>
        <table class="mytable">
            <tbody>
            <tr>
                <th :rowspan="data.personnels.length+1" style="width: 120px;"><label class="my-label">企业<br/>安全<br/>生产<br/>标准<br/>化工<br/>作组<br/>重要<br/>成员</label></th>
                <th style="width: 120px;"><label class="my-label"></label></th>
                <th style="width: 120px;"><label class="my-label">姓名</label></th>
                <th colspan="2" style="width: 120px;"><label class="my-label">所在部门及职务/职称</label></th>
                <th style="width: 120px;"><label class="my-label">电话</label></th>
                <th style="width: 120px;"><label class="my-label">备注</label></th>
                <th style="width: 60px;"><label class="my-label">操作</label></th>
            </tr>
            <tr>
                <th><label class="my-label">组长</label></th>
                <td><el-input size="small" v-model="data.personnels[0].name" placeholder=""></el-input></td>
                <td colspan="2"><el-input size="small" v-model="data.personnels[0].deptname" placeholder=""></el-input></td>
                <td><el-input size="small" v-model="data.personnels[0].mobile" placeholder=""></el-input></td>
                <td><el-input size="small" v-model="data.personnels[0].remark" placeholder=""></el-input></td>
                <td></td>
            </tr>
            <tr>
                <th :rowspan="data.personnels.length-1"><label class="my-label">成员</label></th>
                <td ><el-input size="small" v-model="data.personnels[1].name" placeholder=""></el-input></td>
                <td colspan="2" ><el-input size="small" v-model="data.personnels[1].deptname" placeholder=""></el-input></td>
                <td><el-input size="small" v-model="data.personnels[1].mobile" placeholder=""></el-input></td>
                <td><el-input size="small" v-model="data.personnels[1].remark" placeholder=""></el-input></td>
                <td style="text-align: center;"><i @click="addPer" style="color: #409eff;font-size:32px;cursor: pointer;" class="el-icon-circle-plus"></i></td>
            </tr>
            <tr v-for="(item,k) in data.personnels" v-if="k>=2">
                <td><el-input size="small" v-model="item.name" placeholder=""></el-input></td>
                <td colspan="2"><el-input size="small" v-model="item.deptname" placeholder=""></el-input></td>
                <td><el-input size="small" v-model="item.mobile" placeholder=""></el-input></td>
                <td><el-input size="small" v-model="item.remark" placeholder=""></el-input></td>
                <td style="text-align: center;"><i @click="delPer(k)" style="color: red;font-size:28px;cursor: pointer;" class="el-icon-delete-solid"></i></td>
            </tr>
            </tbody>
        </table>
        <div style="text-align: center;margin: 20px auto;">
            <el-button type="primary" @click="submit" v-loading.fullscreen.lock="loading">下一步</el-button>
            <el-button @click="location.href = 'index'">返回</el-button>
        </div>
    </div>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.all.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                title: '新增',
                id: '{$id}',
                data: {
                    old_level:'无',
                    personnels:[
                        {name:'',deptname:'',mobile:'',remark:''},
                        {name:'',deptname:'',mobile:'',remark:''},
                        {name:'',deptname:'',mobile:'',remark:''},
                        {name:'',deptname:'',mobile:'',remark:''},
                        {name:'',deptname:'',mobile:'',remark:''},
                        {name:'',deptname:'',mobile:'',remark:''},
                    ],
                },
                visible: false,
                loading: true,
                height: document.documentElement.clientHeight - 155,
            };
        },
        methods: {
            addPer(){
                this.data.personnels.push({name:'',deptname:'',mobile:'',remark:''});
            },
            delPer(k){
                this.data.personnels.splice(k,1);
            },
            validatePhone(value){
                const phoneReg = /^\d{3,4}-\d{7,8}(-\d{1,4})?$/;
                if (!phoneReg.test(value))
                {
                    /*this.$alert('电话格式不正确！');
                    this.$refs.contacts_tel.focus();
                    return false;*/
                }
                return true;
            },
            validatePhone2(value){
                const phoneReg = /^\d{3,4}-\d{7,8}(-\d{1,4})?$/;
                if (!phoneReg.test(value))
                {
                    /*this.$alert('传真格式不正确！');
                    this.$refs.contacts_fax.focus();
                    return false;*/
                }
                return true;
            },
            validateTel(value){
                const phoneReg = /^1[3-9]\d{9}$/;
                if (!phoneReg.test(value))
                {
                    /*this.$alert('手机格式不正确！');
                    this.$refs.contacts_mobile.focus();
                    return false;*/
                }
                return true;
            },
            validateEmail(value){
                const emailReg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                if (!emailReg.test(value))
                {
                    /*this.$alert('电子邮件格式不正确！');
                    this.$refs.contacts_email.focus();
                    return false;*/
                }
                return true;
            },
            //数据加载
            getData() {
                var _this = this;
                var param = {};
                param.id = _this.id;
                param._ajax = 1;
                axios.post('apply', param).then(function (res) {
                    if (res.data.code == 0) {
                        _this.data = res.data.data;
                        _this.$nextTick(() => {
                            // 这里的代码会在DOM更新完成后执行
                            _this.loading = false;
                        });
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            submit: function () {
                var _this = this;
                var param = _this.data;

                var flag = _this.validatePhone(_this.data.contacts_tel);
                if( !flag ){
                    return false;
                }
                flag = _this.validatePhone2(_this.data.contacts_fax);
                if( !flag ){
                    return false;
                }

                flag = _this.validateTel(_this.data.contacts_mobile);
                if( !flag ){
                    return false;
                }
                flag = _this.validateEmail(_this.data.contacts_email);
                if( !flag ){
                    return false;
                }

                _this.loading = true;
                axios.post("seSave?id="+_this.id, param).then(function (res) {
                    _this.loading = false;
                    _this.$message({
                        message: res.data.msg,
                        type: res.data.type
                    });
                    if (res.data.code == 0) {
                        location.href = 'score?id='+res.data.data.id;
                    }
                }).catch(function (error) {
                    _this.loading = false;
                    console.log("出现错误:",error);
                });
            },
            goBack() {
                location.href = 'index';
            },
        },
        mounted() {
            //获取列表
            this.getData();

        }
    })
</script>


</body>
</html>