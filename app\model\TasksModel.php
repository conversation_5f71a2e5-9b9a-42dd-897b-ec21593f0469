<?php
namespace app\model;

use think\facade\Db;
use think\Model;
use think\facade\Cache;

class TasksModel extends Model
{

    /**
     * 考试状态验证
     */
    public function examVerify($id=0,$sign='') {
        $re = Db::table('top_org_tasks')->where(['id'=>$id])->find();
        if(empty($re)){
            return ['code'=>2001,'msg'=>'评审信息不存在'];
        }
        if($re['status']!=8){
            return ['code'=>2002,'msg'=>'当前状态不允许考试'];
        }
        if($re['exam_sign']!==$sign&&!empty($re['exam_sign'])){
            return ['code'=>2003,'msg'=>'二维码已失效'];
        }
        return ['code'=>0,'data'=>['exam_num'=>$re['exam_num']]];
    }

}
