<?php

namespace app\admin\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use app\BaseController;
use hg\apidoc\annotation as Apidoc;
use think\facade\View;
use think\Request;
use think\facade\Db;
use app\admin\model\TopOrgModel;
use app\admin\model\TopOrgUserModel;
use app\model\ExcelModel;

/**
 * @Apidoc\Title("企业管理")
 * @Apidoc\Group("Index")
 * @Apidoc\Sort(1)
 */
class Org extends BaseController
{

	/**
	 * @Apidoc\Title("评审单位首页")
	 * @Apidoc\Desc("评审单位首页")
	 * @Apidoc\Method("POST")
	 * @Apidoc\Tag("首页")
	 * @Apidoc\Returned("data", type="object", desc="基础数据")
	 */
	public function index()
	{
		View::assign('title', '首页');
		return view();
	}

	/**
	 * 获取列表数据
	 */
	public function get_list()
	{
		$param = $this->request->param();
		$model = new TopOrgModel();
		$data = $model->getList($param, $param['page'], $param['limit']);
		result($data);
	}

	/**
	 * 保存数据
     * @sysLog 保存评审机构数据
	 */
	public function save_data()
	{
		$param = $this->request->param();
		$model = new TopOrgModel();
		$id = intval($param['id']);
		if ($id) $model = $model::find($id);
		$data = $model->newSave($model, $param, $id);
		result(null, 0, $data);
	}

	/**
	 * 删除
     * @sysLog 删除评审机构数据
	 */
	public function delete()
	{
		$param = $this->request->param();
		$id = intval($param['id']);
		$model = TopOrgModel::find($id);
		$data = $model->delData($model);
		result(null, 0, $data);
	}

	/**
	 * 导出
     * @sysLog 导出评审机构数据
	 */
	public function export()
	{
		$param = $this->request->param();
		$model = new TopOrgModel();
		$data = $model->getList($param, 0, 0, true);
		$title = $model->getTitle();
		ExcelModel::exportExcel($title, $data, '评审单位基本信息导出', true);
	}

	/**
	 * 导入模板
	 */
	public function importTemplate()
	{
		$model = new TopOrgModel();
		$title = $model->getTitle();
		$ord = ord('A');
		foreach ($title as $k => $v) {
			if ($v['field'] == 'statusStr') {
				unset($title[$k]);
				continue;
			}
			$title[$k]['field'] = chr($ord);
			$ord += 1;
		}

		ExcelModel::exportExcel(array_merge_recursive($title), [], '评审单位基本信息导入模板');
	}

	/**
	 * 导入
     * @sysLog 导入评审机构数据
	 */
	public function import()
	{
		$data = import("input");
		$dataHeader = importHeader("input");
		$model = new TopOrgModel();
		$result = $model->importData($data, $dataHeader);
		$msg = count($result['success']) . '条数据保存成功，' . count($result['repeat']) . '条数据重复导入，' . count($result['fail']) . '条数据处理失败。';
		result($result, 0, $msg);
	}


	public function personnel()
	{
		View::assign('title', '用户管理');
		return view();
	}

	/**
	 * 获取列表数据
	 */
	public function get_list_p()
	{
		$param = $this->request->param();
		$model = new TopOrgUserModel();
		$orgModel = new TopOrgModel();
		$data = $model->getList($param, $param['page'], $param['limit']);
		result(array('list' => $data, 'org' => $orgModel->getList([], 0, 0, true), 'role' => $model->role_arr));
	}

	/**
	 * 保存数据
     * @sysLog 保存评审机构用户信息
	 */
	public function save_data_p()
	{
		$param = $this->request->param();
		$model = new TopOrgUserModel();
		$id = intval($param['id']);
		if ($id) $model = $model::find($id);
		$data = $model->newSave($model, $param, $id);
		result(null, 0, $data);
	}

	/**
	 * 删除
     * @sysLog 删除评审机构用户信息
	 */
	public function delete_p()
	{
		$param = $this->request->param();
		$id = intval($param['id']);
		$model = TopOrgUserModel::find($id);
		$data = $model->delData($model);
		result(null, 0, $data);
	}

	/**
	 * 导出
     * @sysLog 导出评审机构用户信息
	 */
	public function export_p()
	{
		$param = $this->request->param();
		$model = new TopOrgUserModel();
		$data = $model->getList($param, 0, 0, true);
		$title = $model->getTitle();
		ExcelModel::exportExcel($title, $data, '区县应急局用户信息导出', true);
	}

	/**
	 * 导入模板
	 */
	public function importTemplate_p()
	{
		$model = new TopOrgUserModel();
		$orgModel = new TopOrgModel();
		$title = $model->getTitle();
		$ord = ord('A');
		foreach ($title as $k => $v) {
			if ($v['field'] == 'statusStr' || $v['field'] == 'salt' || $v['field'] == 'reg_time' || $v['field'] == 'reg_ip') {
				unset($title[$k]);
				continue;
			}
			
			if ($v['field'] == 'roleName') {
				$title[$k]['type'] = 'list';
				$roleArr = $model->role_arr;
				foreach ($roleArr as $item) {
					$role[] = $item['label'];
				}
				$title[$k]['listData'] = $role;
			}
			if ($v['field'] == 'orgName') {
				$title[$k]['type'] = 'list';
				$orgArr = $orgModel->getList([], 0, 0, true);
				foreach ($orgArr as $item) {
					$org[] = $item['name'];
				}
				$title[$k]['listData'] = $org;
			}

			$title[$k]['field'] = chr($ord);
			$ord += 1;
		}
//		dd(array_merge_recursive($title));die;
		$data = array(array('姓名', '用户名', '电话', '邮箱', '所属应急局', '部门', '角色'));

		ExcelModel::exportExcel(array_merge_recursive($title), $data, '区县应急局用户信息导入模板');
	}

	/**
	 * 导入
     * @sysLog 导入评审机构用户信息
	 */
	public function import_p()
	{
		$data = import("input");
		$dataHeader = importHeader("input");
		$model = new TopOrgUserModel();
		$result = $model->importData($data, $dataHeader);
		$msg = count($result['success']) . '条数据保存成功，' . count($result['repeat']) . '条数据重复导入，' . count($result['fail']) . '条数据处理失败。';
		result($result, 0, $msg);
	}

	/**
	 * 修改密码
     * @sysLog 修改评审机构用户密码
	 */
	public function update_password(){
		$param = $this->request->param();
		$id = intval($param['id']);
		$model = TopOrgUserModel::find($id);
		$data = $model->updatePassword($model, $param['password']);
		result(null, 0, $data);
	}

}
