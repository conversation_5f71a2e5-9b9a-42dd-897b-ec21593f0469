<?php

namespace app\company\model;

use app\model\SettingModel;
use think\facade\Db;
use think\Model;
use Medoo\Mdb;
use app\model\FileModel;

class CompanyModel extends Model
{
    public static function companySave($param,$id=0)
    {
        try {
            $data = [
                'name' => $param['name'],
                'license' => $param['license'],
                'aoc' => $param['aoc'],
                'reg_address' => implode(',',$param['reg_address']),
                'reg_address_info' => $param['reg_address_info'],
                'operate_address' =>implode(',', $param['operate_address']),
                'operate_address_info' => $param['operate_address_info'],
                'region' => implode(',',$param['operate_address']),
                'legal' => $param['legal'],
                'legal_mobile' => $param['legal_mobile'],
                'legal_email' => $param['legal_email'],
                'fax' => $param['fax'],
                'phone' => $param['phone'],
                'postal_code' => $param['postal_code'],
                'industrial_park' => implode(',',$param['industrial_park']),
                'economy_sector' => implode(',',$param['economy_sector']),
                'industry' => $param['industry'][0],
                'specialty' => $param['industry'][count($param['industry'])-1],
                'mb_industry' => implode(',',$param['industry']),
                'license_number' => $param['license_number'],
                'license_start' => $param['license_start'],
                'license_end' => $param['license_end'],
                'economy_type' => implode(',',$param['economy_type']),
                'enterprise_size' => $param['enterprise_size'],
                'reg_money' => $param['reg_money'],
                'manager' => $param['manager'],
                'manager_mobile' => $param['manager_mobile'],
                'manager_email' => $param['manager_email'],
                'date' => $param['date'],
                'fixed_asset' => $param['fixed_asset'],
                'revenue' => $param['revenue'],
                'personnel' => $param['personnel'],
                'area' => $param['area'],
                'personnel_full' => $param['personnel_full'],
                'personnel_part' => $param['personnel_part'],
                'personnel_special' => $param['personnel_special'],
                'group_name' => $param['group_name'],
                'status' => 1,
                'business' => $param['business'],
                'business_type' => $param['business_type'],
                'is_dust_explosion' => $param['is_dust_explosion'],
                'is_ammonia_cold' => $param['is_ammonia_cold'],
                'is_hot_melting' => $param['is_hot_melting'],
                'is_light_industry' => $param['is_light_industry'],
                'sector' => $param['sector'],
                'ammonia_use' => $param['ammonia_use'],
                'ammonia_storage_method' => $param['ammonia_storage_method'],
                'limited_space_type' => $param['limited_space_type'],
                'ammonia_usage' => $param['ammonia_usage'],
                'ammonia_storage_capacity' => $param['ammonia_storage_capacity'],
                'gas_alarm_number' => $param['gas_alarm_number'],
                'blast_furnace_number' => $param['blast_furnace_number'],
                'nonferrous_furnace_number' => $param['nonferrous_furnace_number'],
                'ferroalloy_furnace_number' => $param['ferroalloy_furnace_number'],
                'soaring_furnace_number' => $param['soaring_furnace_number'],
                'limited_space_num' => $param['limited_space_num'],
            ];
            $area = Db::table('top_area')->where([['pcas','like',"%{$param['operate_address'][2]}%"],['status','=',1],['del_flag','=',0]])->find();
            $city = Db::table('top_city')->where([['pcas','like',"%{$param['operate_address'][1]}%"],['status','=',1],['del_flag','=',0]])->find();
            $dept = Db::table('top_industry')->where([['name','=',$data['specialty']]])->find();
            $data['area_id'] = $area['id'];
            $data['city_id'] = $city['id'];
            $data['dept_id'] = $dept['department'];
            $user_id = $_SESSION['company']['user_id'];
            $re = Db::table('top_company_info_apply')->where([['user_id','=',$user_id],['status','=','1']])->find();
            if($re){
                result('',1003,'已有待审核的申请');
            }
            FileModel::saveFile($data['license'],'company/info/'.date('Ym'));
            FileModel::saveFile($data['aoc'],'company/info/'.date('Ym'));
            if($id) {
                $re = Db::table('top_company_info_apply')->where(['id'=>$id])->find();
                if(empty($re)){
                    result('',1003,'数据不存在或已删除');
                }
                $re = Db::table('top_company_info_apply')->where(['id'=>$id])->update($data);
            } else {
                $data['user_id'] = $user_id;
                Db::table('top_company_info_apply')->insert($data);
                //判断数据保存成功后进行操作
                $id = Db::table('top_company_info_apply')->where($data)->value('id');
            }
            if( $id ) {
                //保存参数
                $saveParam = [];
                if( is_array($param['dust_list']) && count($param['dust_list']) ) {
                    foreach ($param['dust_list'] as $item) {
                        if( !empty($item['param_value']) ) {
                            $saveParam[] = [
                                'company_id' => $id,
                                'name' => $item['name'],
                                'param_value' => $item['param_value'],
                                'category' => 'dust'
                            ];
                        }
                    }
                }
                if( is_array($param['hot_list']) && count($param['hot_list']) ) {
                    foreach ($param['hot_list'] as $item) {
                        if( !empty($item['param_value']) ) {
                            $saveParam[] = [
                                'company_id' => $id,
                                'name' => $item['name'],
                                'param_value' => $item['param_value'],
                                'category' => 'hot'
                            ];
                        }
                    }
                }
                if( is_array($param['limited_list']) && count($param['limited_list']) ) {
                    foreach ($param['limited_list'] as $item) {
                        if( !empty($item['param_value']) ) {
                            $saveParam[] = [
                                'company_id' => $id,
                                'name' => $item['name'],
                                'param_value' => $item['param_value'],
                                'category' => 'limited'
                            ];
                        }
                    }
                }
                if( count($saveParam) ) {
                    Db::table('top_company_param_apply')->strict(false)->insertAll($saveParam);
                }
            }
            return $id;
        }catch (\Exception $e){
            dd($e->getMessage());
        }
    }


    //参数格式化
    public static function codeToText($info){
        if(empty($info)){
            return $info;
        }
        $pcas = SettingModel::getPcasAll();
        $info['reg_address'] = empty($info['reg_address'])?[]:explode(',',$info['reg_address']);
        $info['mb_reg_address'] = '';
        foreach ($info['reg_address'] as $k=>$v){
            $info['mb_reg_address'] .= $pcas[$v];
        }
        $info['mb_reg_address'] .= $info['reg_address_info'];

        $info['operate_address'] = empty($info['operate_address'])?[]:explode(',',$info['operate_address']);
        $info['mb_operate_address'] = '';
        foreach ($info['operate_address'] as $k=>$v){
            $info['mb_operate_address'] .= $pcas[$v];
        }
        $info['mb_operate_address'] .= $info['operate_address_info'];

        $info['region'] = empty($info['region'])?[]:explode(',',$info['region']);
        $info['mb_region'] = '';
        foreach ($info['region'] as $k=>$v){
            $info['mb_region'] .= $pcas[$v];
        }
        $info['economy_sector'] = explode(',',$info['economy_sector']);
        $info['mb_economy_sector'] = implode('/',$info['economy_sector']);
        $info['economy_type'] = explode(',',$info['economy_type']);
        $info['mb_economy_type'] = implode('/',$info['economy_type']);
        $info['industrial_park'] = explode(',',$info['industrial_park']);
        $info['mb_industrial_park'] = implode('/',$info['industrial_park']);
        $info['license_date'] = $info['license_start'].'至'.$info['license_end'];
        $info['licenseUrl'] = empty($info['license'])?'':FileModel::getFile(0,$info['license']);
        $info['aocUrl'] = empty($info['aoc'])?'':FileModel::getFile(0,$info['aoc']);
        foreach ($info as $k=>$v){
            $info[$k] = $v==='0000-00-00'?'':$v;
            if(is_int($v)){
                $info[$k] = (string)$v;
            }
            if($v===null){
                $info[$k] = '';
            }
        }
        return $info;
    }

    /**
     *从企业大数据表中获取企业数据
     * @param $companyName string 企业名称
     * @return array|false|mixed|\PDOStatement|string|Model
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException

     **/
    public static function getBigDataCompanyInfo($companyName){

        //分页获取sqlite数据库中的数据
        $dbFile = 'sqlite:'.config('database.connections.sqlite.path');
        $pdo = new \PDO($dbFile);
        $pdo->setAttribute(\PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(\PDO::ATTR_DEFAULT_FETCH_MODE, \PDO::FETCH_ASSOC);

        // 构建SQL查询语句
        $sql = "SELECT * FROM company_data WHERE ztmc LIKE ? LIMIT 1";
        $params = ["{$companyName}"];
        // 执行查询
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $row = $stmt->fetch();
        if(!$row) return [];
        //根据已有的表转换数据名称 company_data 表里没有的字段不用输出
        $out = [
            'date' => $row['clrq'],//成立日期
            'legal' => $row['fddbr'],//法定代表人（负责人）
            'business' => $row['jyfw'],//经营范围
            'license_number' => $row['tyshxydm'],//统一社会信用代码
            'reg_money' => $row['zczb'],//注册资本
            'business_type' => $row['scztlxzwmc'],//市场主体类型
            'industry' => $row['scztdm'],//市场主体代码
            'specialty' => $row['scztdm'],//市场主体代码
            'personnel' => $row['rzrs'],//注册人数
            'reg_address_info' => $row['zs'],//注册所在地
            'operate_address_info' => $row['zs'],//注册所在地
            'personnel_full' => $row['rzrs'],//注册人数
            'personnel_part' => $row['rzrs'],//注册人数
            'personnel_special' => $row['rzrs'],//注册人数
            'name' => $row['ztmc'],//企业名称
            'manager' => $row['fddbr'],
        ];
        return $out;
    }


}