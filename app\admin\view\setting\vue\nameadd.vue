<style scoped>
.my-autocomplete li{line-height: normal;padding: 7px;}
.my-autocomplete li .name{text-overflow: ellipsis;overflow: hidden;}
.my-autocomplete li .addr{font-size: 12px;color: #b4b4b4;}
.my-autocomplete li .highlighted{color: #ddd;}
.el-dialog__header {
  padding: 20px 20px 10px;
}
</style>
<template>
  <el-dialog title="添加" width="600px" class="icon-dialog" :visible.sync="visible" append-to-body>
    <el-form  ref="form" :model="form" :rules="rules" label-width="120px">
      <el-row >
        <el-col :span="24">
          <el-form-item label="评审标准名称" prop="name">
            <el-input v-model="form.name" autoComplete="off" clearable  placeholder="请输入评审标准名称"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row >
        <el-col :span="24">
          <el-form-item label="要素名称（多个用“,”隔开）" prop="element">
            <el-input  v-model="form.element" autoComplete="off" clearable  placeholder="例：考评类目,考评项目"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row >
        <el-col :span="24">
          <el-form-item label="排序号" prop="sort">
            <el-input  v-model="form.sort" autoComplete="off" clearable  placeholder="请输入排序号"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" :loading="loading" type="primary" @click="submit" v-loading.fullscreen.lock="loading">保 存</el-button>
      <el-button size="small" @click="visible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>
<script>
module.exports = {
  name: "add",
  // 模板导入区
  components: {
  },
  data:function() {
    return {
      id:0,
      isAdmin: false,
      visible: false,
      title: '新增',
      form: {
        name:'',
        element:'',
        sort:'',
      },
      role_ids:[],
      loading:false,
      rules: {
        name:[
          {required: true, message: '评审标准名称不能为空', trigger: 'blur'},
        ],
      }
    }
  },
  mounted: function(){
  },
  created:function(){
  },
  methods: {
    /**
     * 打开弹窗调用方法
     * */
    open: function (row) {
      console.log(row)
      row = row?row:{id:0};
      var _this =this;
      _this.visible = true;
      _this.form = row;
    },
    submit: function () {
      var _this = this;
      var param = _this.form;
      this.$refs.form.validate(function (valid) {
        if(valid){
          axios.post("standardNameSave", param).then(function (res) {
            _this.$message({
              message: res.data.msg,
              type: res.data.type
            });
            if (res.data.code == 0) {
              _this.visible = false;
              _this.$emit("ok");
            }
          }).catch(function (error) {
            console.log("出现错误:",error);
          });
        }
      });
    },
    refresh: function () {
      this.$emit("refresh");
    },
  }
}
</script>