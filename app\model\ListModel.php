<?php

namespace app\model;

use think\facade\Db;
use think\Model;

class ListModel extends Model
{
    /*
     * 判断数据表是否存在
     */
    public static function isDb($setting){
        if(empty($setting['fields']['db'])){
            return true;
        }
        $res = Db::query("SHOW TABLES LIKE '{$setting['table']}'");
        if(!$res){
            $sql = "CREATE TABLE `{$setting['table']}`  (`id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,";
            foreach ($setting['fields']['db'] as $k=>$v){
                if(strpos($v['type'],'varchar')!==false){
                    $sql .= "`{$v['field']}` {$v['type']} CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '{$v['title']}',";
                }else if(strpos($v['type'],'int')!==false){
                    $sql .= "`{$v['field']}` tinyint(1) NOT NULL COMMENT '{$v['title']}',";
                }else if(strpos($v['type'],'text')!==false){
                    $sql .= "`{$v['field']}` {$v['type']} CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '{$v['title']}',";
                }else{
                    $sql .= "`{$v['field']}` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '{$v['title']}',";
                }
            }
            $sql .= "PRIMARY KEY (`id`) USING BTREE) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '{$setting['title']}' ROW_FORMAT = Dynamic;";
//            dd($sql);
            Db::query($sql);
        }else{
            $res = Db::query("show full columns from {$setting['table']}");
            foreach ($res as $k=>$v){
                $fields[] = $v['Field'];
            }
            $sql = "";
            $f = 'id';
            foreach ($setting['fields']['db'] as $k=>$v){
                if(!in_array($v['field'],$fields)){
                    if(strpos($v['type'],'varchar')!==false){
                        $sql .= "ADD COLUMN `{$v['field']}` {$v['type']} CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '{$v['title']}' AFTER `$f`,";
                    }else if(strpos($v['type'],'int')!==false){
                        $sql .= "ADD COLUMN `{$v['field']}` tinyint(1) NOT NULL COMMENT '{$v['title']}' AFTER `$f`,";
                    }else if(strpos($v['type'],'text')!==false){
                        $sql .= "ADD COLUMN `{$v['field']}` {$v['type']} CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '{$v['title']}' AFTER `$f`,";
                    }else{
                        $sql .= "ADD COLUMN `{$v['field']}` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '{$v['title']}' AFTER `$f`,";
                    }
                }
                $f = $v['field'];
            }
            if(!empty($sql)){
                $sql = "ALTER TABLE `{$setting['table']}` ".trim($sql,',').';';
                Db::query($sql);
            }
//            dd($sql);
        }
        return true;
    }
    /*
     * 保存数据
     */
    public static function listSave($setting,$param,$id = 0){
        $fields = empty($id)?$setting['fields']['add']:$setting['fields']['edit'];
        $data = [];
        foreach ($fields as $k=>$v){
            $data[$v['field']] = $data[$v['field']]??self::defaultext($v['default']);
            if($v['show']){
                foreach ($v['bind'] as $v1){
                    $data[$v1] = $param[$v1];
                }
                switch ($v['type']){
                    case 'files':
                        $data[$v['field']] = json_encode($param[$v['field']]);
                        break;
                    case 'images':
                        $data[$v['field']] = json_encode($param[$v['field']]);
                        break;
                    default:
                        $data[$v['field']] = $param[$v['field']];
                        break;
                }
                if($v['require']){
                    if(empty($data[$v['field']])){
                        result('',1001,'请填写'.$v['title']);
                    }
                }
            }
        }
        if($id){
            $re = Db::table($setting['table'])->where(['id'=>$id])->find();
            if(empty($re)){
                result('',1003,'数据不存在或已删除');
            }
            $re = Db::table($setting['table'])->where(['id'=>$id])->update($data);
        }else{
            $id = Db::table($setting['table'])->insertGetId($data);
        }
        return $id;
    }

    //状态变更
    public static function upStatus($table,$field,$id){
        $field = 'is_'.$field;
        $re = Db::table($table)->where(['id'=>$id])->field($field)->find();
        if(empty($re)){
            return false;
        }
        $value = $re[$field]==1?0:1;
        $up = Db::table($table)->where(['id'=>$id])->update([$field=>$value]);
        return $up?$value:false;
    }

    //删除
    public static function del($table,$id){
        $re = Db::table($table)->where(['id'=>$id])->find();
        $re = Db::table($table)->where(['id'=>$id])->delete();
        return true;
    }

    //导入
    public static function import($setting,$data,$dataHeader){
        $dates = [];
        foreach ($dataHeader as $k=>$v){
            foreach ($setting['fields']['import'] as $k1=>$v1){
                if($v1['type']=='date'){
                    $dates[] = $v1['field'];
                }
                if($v1['require']){
                    $require[] = $v1['field'];
                }
                if($v1['title']==$v){
                    $tit[$k] = $v1['field'];
                }
            }
        }
        foreach ($data as $k=>$v){
            $tmp = [];
            foreach($v as $k1=>$v1){
                if(in_array($tit[$k1],$dates)){
                    $v1 = str_replace('.','-',$v1);
                    if(strlen($v1)>6){
                        $tmp[$tit[$k1]] = date('Y-m-d',strtotime($v1));
                    }else{
                        $tmp[$tit[$k1]] = gmdate('Y-m-d',\PHPExcel_Shared_Date::ExcelToPHP($v1));
                    }
                }else{
                    $tmp[$tit[$k1]] = str_replace(' ','',$v1);
                }
            }
            $dataList[] = $tmp;
        }
        foreach ($dataList as $v) {
            $a = true;
            foreach ($require as $v1){
                $a = !empty($v[$v1]);
            }
            if($a){
                $datauser[] = $v;
            }
        }
        foreach ($datauser as $k=>$v){
            $re = self::importSave($setting,$v);
            if($re['code']==0) {
                $result['success'][] = ['row' => $k + 2, 'user_name' => $v['user_name'], 'msg' => '保存成功'];
            }else if($re['code']==-1){
                $result['repeat'][] = ['row' => $k + 2, 'user_name' => $v['user_name'], 'msg' => '单据重复'];
            }else{
                $result['fail'][] = ['row'=>$k+2,'user_name'=>$v['user_name'],'msg'=>$re['msg']];
            }
        }
        return $result;
    }


    public static function importSave($setting,$param){
        foreach ($setting['fields']['import'] as $k=>$v){
            $data[$v['field']] = $data[$v['field']]??self::defaultext($v['default']);
            if($v['show']){
                $r = self::importext($param[$v['field']],$v,$setting['global']);
                if(is_array($r)){
                    foreach ($r as $k1=>$v1){
                        $data[$k1] = $v1;
                    }
                }else{
                    $data[$v['field']] = $r;
                }
            }
        }
        $re = Db::table($setting['table'])->where($data)->field('id')->find();
        if($re){
            return ['','code'=>-1,'msg'=>'重复'];
        }else{
            $id = Db::table($setting['table'])->insertGetId($data);;
        }
        return ['data'=>['id'=>$id],'code'=>0,'msg'=>'保存成功'];
    }


    //默认参数转换
    public static function defaultext($value){
        switch ($value){
            case 'user_id':
                $data = $_SESSION['LOGIN_USER_ID'];
                break;
            case 'user_name':
                $data = $_SESSION['LOGIN_USER_NAME'];
                break;
            case 'dept_id':
                $data = $_SESSION['LOGIN_DEPT_ID'];
                break;
            case 'dept_name':
                $data = Db::table('department')->where(['DEPT_ID'=>$_SESSION['LOGIN_DEPT_ID']])->value('DEPT_NAME');
                break;
            case 'date':
                $data = date('Y-m-d H:i:s');
                break;
            case 'time':
                $data = time();
                break;
            default:
                $data = $value;
                break;
        }
        return $data;
    }


    //导入参数转换
    public static function importext($value,$field,$global){
        $data = $value;
        switch ($field['type']){
            case 'select':
                foreach ($global[$field['field']] as $k=>$v){
                    if($v['label']==$value){
                        $data = $v['value'];
                    }
                }
                break;
            case 'user':
                $data = [];
                $user = Db::table('td_user')->where(['USER_NAME'=>$value])->find();
                if($field['bind'][0]){
                    $data[$field['bind'][0]] = $user['USER_ID'];
                }
                if($field['bind'][1]){
                    $data[$field['bind'][1]] = $user['USER_NAME'];
                }
                if($field['bind'][2]){
                    $data[$field['bind'][2]] = $user['DEPT_ID'];
                }
                if($field['bind'][3]){
                    $data[$field['bind'][3]] = Db::table('department')->where(['DEPT_ID'=>$user['DEPT_ID']])->value('DEPT_NAME');
                }
                $data[$field['field']] = empty($data[$field['field']])?$value:$data[$field['field']];
                break;
            default:
                break;
        }
        return $data;
    }




}