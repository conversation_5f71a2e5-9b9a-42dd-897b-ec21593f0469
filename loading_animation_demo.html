<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统计 Loading 动画演示</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .demo-container { max-width: 800px; margin: 0 auto; padding: 20px; }
        .demo-section { margin-bottom: 30px; padding: 20px; background: white; border-radius: 8px; box-shadow: 0 2px 12px rgba(0,0,0,0.1); }
        .field-item { display: flex; align-items: center; margin: 8px 0; padding: 8px; background: #f9f9f9; border-radius: 4px; }
    </style>
</head>
<body>
    <div id="app" class="demo-container">
        <h1>统计功能 Loading 动画演示</h1>
        
        <div class="demo-section">
            <h3>字段列表（模拟）</h3>
            <p>点击"查看统计"按钮体验 loading 效果</p>
            
            <div v-for="field in mockFields" :key="field.field_name" class="field-item">
                <el-checkbox :label="field.field_name">
                    <span style="font-weight: bold;">{{ field.field_name }}</span>
                    <span style="color: #e67e22; margin: 0 8px;">({{ field.field_type }})</span>
                    <span style="color: #606266;">{{ field.field_comment }}</span>
                </el-checkbox>
                <el-button
                    size="mini"
                    type="text"
                    @click="showFieldStats(field.field_name)"
                    :loading="statsLoading && currentStatsField === field.field_name"
                    style="margin-left: auto;">
                    <i class="el-icon-data-line" v-if="!statsLoading || currentStatsField !== field.field_name"></i>
                    查看统计
                </el-button>
            </div>
        </div>

        <div class="demo-section">
            <h3>控制面板</h3>
            <el-button @click="simulateQuickLoad" type="primary">模拟快速加载 (1秒)</el-button>
            <el-button @click="simulateSlowLoad" type="warning">模拟慢速加载 (3秒)</el-button>
            <el-button @click="simulateError" type="danger">模拟加载错误</el-button>
        </div>

        <!-- 统计对话框 -->
        <el-dialog title="字段加密统计" :visible.sync="statsDialogVisible" width="500px">
            <!-- 加载状态 -->
            <div v-if="statsLoading" style="text-align: center; padding: 40px;">
                <el-icon class="is-loading" style="font-size: 32px; color: #409eff;">
                    <i class="el-icon-loading"></i>
                </el-icon>
                <p style="margin-top: 15px; color: #606266; font-size: 16px;">正在计算加密统计信息...</p>
                <p style="margin-top: 5px; color: #909399; font-size: 12px;">
                    数据量较大时可能需要较长时间，请耐心等待
                </p>
            </div>

            <!-- 统计内容 -->
            <div v-else-if="fieldStats">
                <el-descriptions :column="2" border>
                    <el-descriptions-item label="表名">{{ selectedTable }}</el-descriptions-item>
                    <el-descriptions-item label="字段名">{{ currentStatsField }}</el-descriptions-item>
                    <el-descriptions-item label="总记录数">
                        <span style="font-weight: bold; color: #2c3e50;">{{ fieldStats.total.toLocaleString() }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="已加密">
                        <span style="font-weight: bold; color: #67c23a;">{{ fieldStats.encrypted.toLocaleString() }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="未加密">
                        <span style="font-weight: bold; color: #f56c6c;">{{ fieldStats.unencrypted.toLocaleString() }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="加密率">
                        <span style="font-weight: bold; color: #409eff;">{{ fieldStats.encryption_rate }}%</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="最后更新" :span="2">
                        <span style="color: #909399;">{{ new Date(fieldStats.last_updated * 1000).toLocaleString() }}</span>
                    </el-descriptions-item>
                </el-descriptions>

                <!-- 加密进度条 -->
                <div style="margin-top: 20px;">
                    <div style="margin-bottom: 8px; color: #606266; font-size: 14px;">
                        <span>加密进度</span>
                        <span style="float: right;">{{ fieldStats.encryption_rate }}%</span>
                    </div>
                    <el-progress
                        :percentage="fieldStats.encryption_rate"
                        :stroke-width="12"
                        :color="fieldStats.encryption_rate > 80 ? '#67c23a' : fieldStats.encryption_rate > 50 ? '#e6a23c' : '#f56c6c'">
                    </el-progress>
                </div>

                <!-- 错误信息显示 -->
                <div v-if="fieldStats.error" style="margin-top: 15px;">
                    <el-alert
                        title="统计过程中出现错误"
                        :description="fieldStats.error"
                        type="warning"
                        show-icon
                        :closable="false">
                    </el-alert>
                </div>

                <!-- 操作按钮 -->
                <div style="margin-top: 20px; text-align: right;">
                    <el-button size="small" @click="clearCache" :loading="clearCacheLoading">
                        <i class="el-icon-delete"></i> 清除缓存
                    </el-button>
                    <el-button size="small" @click="refreshStats" type="primary" :loading="statsLoading">
                        <i class="el-icon-refresh"></i> 刷新统计
                    </el-button>
                </div>
            </div>

            <!-- 无数据状态 -->
            <div v-else style="text-align: center; padding: 40px;">
                <el-icon style="font-size: 48px; color: #c0c4cc;">
                    <i class="el-icon-warning"></i>
                </el-icon>
                <p style="margin-top: 15px; color: #909399;">暂无统计数据</p>
                <el-button size="small" @click="refreshStats" type="primary" style="margin-top: 15px;">
                    <i class="el-icon-refresh"></i> 重新加载
                </el-button>
            </div>
        </el-dialog>
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    selectedTable: 'user_info',
                    currentStatsField: '',
                    statsDialogVisible: false,
                    statsLoading: false,
                    clearCacheLoading: false,
                    fieldStats: null,
                    mockFields: [
                        { field_name: 'user_id', field_type: 'VARCHAR(32)', field_comment: '用户唯一标识ID' },
                        { field_name: 'user_name', field_type: 'VARCHAR(50)', field_comment: '用户登录名称' },
                        { field_name: 'real_name', field_type: 'VARCHAR(50)', field_comment: '用户真实姓名' },
                        { field_name: 'email', field_type: 'VARCHAR(100)', field_comment: '用户邮箱地址' },
                        { field_name: 'phone', field_type: 'VARCHAR(20)', field_comment: '手机号码' },
                        { field_name: 'id_card', field_type: 'VARCHAR(18)', field_comment: '身份证号码' }
                    ]
                }
            },
            methods: {
                showFieldStats(fieldName) {
                    this.currentStatsField = fieldName;
                    this.fieldStats = null; // 清空之前的数据
                    this.statsDialogVisible = true;
                    this.loadFieldStats();
                },

                loadFieldStats() {
                    this.statsLoading = true;
                    
                    // 模拟API调用
                    setTimeout(() => {
                        this.statsLoading = false;
                        this.fieldStats = {
                            total: Math.floor(Math.random() * 100000) + 10000,
                            encrypted: Math.floor(Math.random() * 50000) + 5000,
                            unencrypted: 0,
                            encryption_rate: Math.floor(Math.random() * 100),
                            last_updated: Date.now() / 1000
                        };
                        this.fieldStats.unencrypted = this.fieldStats.total - this.fieldStats.encrypted;
                        this.fieldStats.encryption_rate = Math.round((this.fieldStats.encrypted / this.fieldStats.total) * 100);
                    }, 2000); // 2秒延迟模拟加载
                },

                refreshStats() {
                    this.loadFieldStats();
                },

                clearCache() {
                    this.clearCacheLoading = true;
                    setTimeout(() => {
                        this.clearCacheLoading = false;
                        this.$message.success('缓存清除成功');
                        this.loadFieldStats(); // 重新加载
                    }, 1000);
                },

                simulateQuickLoad() {
                    this.showFieldStats('user_id');
                    // 覆盖默认的2秒延迟，改为1秒
                    setTimeout(() => {
                        if (this.statsLoading) {
                            this.statsLoading = false;
                            this.fieldStats = {
                                total: 50000,
                                encrypted: 45000,
                                unencrypted: 5000,
                                encryption_rate: 90,
                                last_updated: Date.now() / 1000
                            };
                        }
                    }, 1000);
                },

                simulateSlowLoad() {
                    this.showFieldStats('email');
                    // 覆盖默认的2秒延迟，改为3秒
                    setTimeout(() => {
                        if (this.statsLoading) {
                            this.statsLoading = false;
                            this.fieldStats = {
                                total: 120000,
                                encrypted: 60000,
                                unencrypted: 60000,
                                encryption_rate: 50,
                                last_updated: Date.now() / 1000
                            };
                        }
                    }, 3000);
                },

                simulateError() {
                    this.showFieldStats('phone');
                    setTimeout(() => {
                        if (this.statsLoading) {
                            this.statsLoading = false;
                            this.fieldStats = {
                                total: 0,
                                encrypted: 0,
                                unencrypted: 0,
                                encryption_rate: 0,
                                last_updated: Date.now() / 1000,
                                error: '数据库连接超时，请稍后重试'
                            };
                        }
                    }, 2000);
                }
            }
        });
    </script>
</body>
</html>
