<?php

namespace app\sso\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use hg\apidoc\annotation as Apidoc;
use app\Request;
use think\App;
use think\facade\Db;

/**
 * @Apidoc\Title("专家端接口")
 * @Apidoc\Group("User")
 * @Apidoc\Sort(3)
 */
class Expert extends Base
{

    /**
     * @Apidoc\Title("回避单位列表")
     * @Apidoc\Method("POST")
     * @Apidoc\Param ("page", type="int",default="1",desc="页码")
     * @Apidoc\Param ("limit", type="int",default="100", desc="每页数量")
     * @Apidoc\Returned("data", type="List",desc="单位列表",children={
     * @Apidoc\Returned("company_name", type="string",desc="单位名称"),
     * @Apidoc\Returned("license_number", type="string",desc="统一社会信用代码"),
*     })
     *  @Apidoc\Returned("totle", type="string",desc="数据总数")
     *  @Apidoc\Returned("per_page", type="string",desc="每页数量")
     *  @Apidoc\Returned("current_page", type="string",desc="当前页码")
     *  @Apidoc\Returned("last_page", type="string",desc="最后一页页码")
     */
    function avoidanceUnit($limit=100,$page=1){
        //分页获取sqlite数据库中的数据
        $dbFile = 'sqlite:'.config('database.connections.sqlite.path');
        $pdo = new \PDO($dbFile);
        $pdo->setAttribute(\PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(\PDO::ATTR_DEFAULT_FETCH_MODE, \PDO::FETCH_ASSOC);

		// 获取查询参数
        $avoidCompanyName = input('avoidCompanyName', '');
        
        // 构建SQL查询语句
        $sql = "SELECT tyshxydm as license_number, ztmc as company_name 
                FROM company_data 
                WHERE 1=1";
        
        // 如果有公司名称搜索条件，添加到查询中
        $params = [];
        if (!empty($avoidCompanyName)) {
            $sql .= " AND ztmc LIKE ?";
            $params[] = "%{$avoidCompanyName}%";
        }
        
        // 添加分页
        $sql .= " LIMIT $limit OFFSET " . ($page-1)*$limit;
        
        // 执行查询
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $rows = $stmt->fetchAll();
        
        // 获取总数
        $countSql = "SELECT COUNT(tyshxydm) as thetotal FROM company_data";
        if (!empty($avoidCompanyName)) {
            $countSql .= " WHERE ztmc LIKE ?";
            $stmt = $pdo->prepare($countSql);
            $stmt->execute(["%{$avoidCompanyName}%"]);
        } else {
            $stmt = $pdo->prepare($countSql);
            $stmt->execute();
        }
        $count =(int)$stmt->fetchColumn();

        $pdo = null;
        $result = [
            'total' => $count, 
            'per_page'=>$limit,
            'current_page'=>$page,
            'last_page'=>ceil($count/$limit),
            'data' => $rows,
        ];
        result($result);



        // $data = Db::table('top_company_info')->where([])->field('name,license_number')->paginate($limit)->each(function ($item, $key) {
        //     return $item;
        // });
        // result($data);
    }

}
