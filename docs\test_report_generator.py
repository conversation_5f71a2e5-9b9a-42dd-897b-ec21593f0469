# 测试周报生成器功能

import os
import re

def test_parse_content():
    """测试解析功能内容文件的功能"""
    input_file = 'docs/功能内容.txt'
    
    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"错误：找不到文件 {input_file}")
        return False
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 简单检查文件内容是否符合预期格式
        stage_pattern = r'### ([^#]+?)\n\n'
        stage_matches = re.findall(stage_pattern, content)
        
        if not stage_matches:
            print("错误：未能在文件中找到阶段标题")
            return False
            
        print(f"成功：找到 {len(stage_matches)} 个阶段标题")
        for i, stage in enumerate(stage_matches, 1):
            print(f"  {i}. {stage.strip()}")
            
        # 检查是否有周的内容
        week_pattern = r'- \*\*第(\d+)周\*\*'
        week_matches = re.findall(week_pattern, content)
        
        if not week_matches:
            print("错误：未能在文件中找到周信息")
            return False
            
        weeks = [int(w) for w in week_matches]
        print(f"成功：找到 {len(weeks)} 个周的信息，周数范围：{min(weeks)}-{max(weeks)}")
        
        # 检查是否有功能点
        feature_pattern = r'\s+- \*\*([^*]+?)\*\*：'
        feature_matches = re.findall(feature_pattern, content)
        
        if not feature_matches:
            print("错误：未能在文件中找到功能点信息")
            return False
            
        print(f"成功：找到 {len(feature_matches)} 个功能点")
        print(f"  示例功能点：{feature_matches[:3]}...")
        
        return True
    except Exception as e:
        print(f"错误：解析文件时出现异常：{e}")
        return False

def test_output_directory():
    """测试输出目录是否可创建"""
    output_dir = 'docs/周报'
    
    try:
        os.makedirs(output_dir, exist_ok=True)
        print(f"成功：输出目录 {output_dir} 已创建或已存在")
        return True
    except Exception as e:
        print(f"错误：创建输出目录时出现异常：{e}")
        return False

def main():
    print("===== 周报生成器功能测试 =====")
    
    # 测试解析功能
    print("\n[测试1] 解析功能内容文件")
    parse_result = test_parse_content()
    
    # 测试输出目录
    print("\n[测试2] 检查输出目录")
    dir_result = test_output_directory()
    
    # 总结测试结果
    print("\n===== 测试结果汇总 =====")
    if parse_result and dir_result:
        print("所有测试通过！周报生成器可以正常工作。")
        print("\n使用方法：")
        print("1. 安装依赖：pip install -r docs/requirements.txt")
        print("2. 运行脚本：python docs/generate_weekly_reports.py")
    else:
        print("测试未通过，请检查上述错误信息。")

if __name__ == "__main__":
    main()