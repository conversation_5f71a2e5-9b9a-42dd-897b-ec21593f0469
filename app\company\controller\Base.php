<?php
declare (strict_types = 1);

namespace app\company\controller;

use think\App;
use think\facade\Cache;
use think\facade\Db;
use \liliuwei\think\Jump;

/**
 * 控制器基础类
 */
abstract class Base
{
    use Jump;

    /**
     * Request实例
     * @var \think\Request
     */
    protected $request;

    /**
     * 应用实例
     * @var \think\App
     */
    protected $app;

    /**
     * 是否批量验证
     * @var bool
     */
    protected $batchValidate = false;

    /**
     * 控制器中间件
     * @var array
     */
    protected $middleware = [];

    //空方法
    public function __call($method, $args)
    {
        $data = request()->param();
        return view($method, ['data' => $data]);
    }

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        error_reporting(E_ERROR | E_PARSE);
        $this->app     = $app;
        $this->request = $this->app->request;
        session_start();
        //初始化
        $this->initialize();
        // 关闭未定义对象报错
    }

    // 初始化
    protected function initialize()
    {
        $vars = $this->request;
        /*$_SESSION['company'] = [
            'id' => '1',
            'user_id' => '1',
        ];*/
        $token = $this->request->param('token');
        $this->user = empty($_SESSION['company'])?Cache::get('company_'.$token):$_SESSION['company'];
        if (empty($this->user)) {
            //后面改成实际ip
            $controller = $vars->controller();
            $action = $vars->action();
//            dd($_SERVER);
            if ($controller != 'Login'&&$action != 'login') {
                $host = $_SERVER['HTTP_HOST'];
                $port = $_SERVER['SERVER_PORT'];
                if (!empty($_SERVER['REQUEST_METHOD']) && strtolower($_SERVER['REQUEST_METHOD']) == 'post') {
                    // 接口请求处理
                    result('',9001,'请先登陆');
                } else {
                    // 非接口请求处理
                    $scheme = $_SERVER['REQUEST_SCHEME'];
                    header("Location: ".config('app.http_host'). "/general/toppingsoft/index.php/company/login/login");
                    exit();
                }
            }
        }

        //日志记录
        $action = $this->request->action();
        $res=  method_exists($this,$action);
        if($res){
            $Reflection = new \ReflectionMethod($this,$action);
            $remark =  $Reflection->getDocComment();
            if(!empty($remark)){
                $tmp = preg_match_all('/@sysLog(.*?)\n/',$remark,$tmps);
                if($tmp){
                    $user_id = !empty($this->user) ? $this->user['user_name'] : '';
                    if (strstr($remark, '删除') !== false) {
                        $param = json_encode($this->request->param(),  JSON_UNESCAPED_UNICODE);
                    } elseif (strstr($remark, '修改') !== false || strstr($remark, '保存') !== false || strstr($remark, '新增') || strstr($remark, '导入')  || strstr($remark, '导出') !== false) {
                        $param = json_encode($this->request->param(),JSON_UNESCAPED_UNICODE);
                    } elseif (strstr($remark, '登录') !== false) {
                        $params = $this->request->param();
                        $user_id = $params['username'];
                        $param = json_encode($this->request->param(),JSON_UNESCAPED_UNICODE);
                    } else {
                        $param = '';
                    }
                    $arr = [];
                    $arr['USER_ID']  =  $user_id;
                    $arr['TIME']  = date('Y-m-d H:i:s',time());
                    $arr['IP']  = $_SERVER['REMOTE_ADDR'];
                    $arr['TYPE']  ="10";
                    $arr['REMARK']  =$tmps[1][0];
                    $arr['PARAM_JSON'] = $param;
                    Db::table('top_sys_log')->insert($arr);
                }
            }
        }
    }

}
