<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据表和字段搜索功能演示</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .demo-container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 12px rgba(0,0,0,0.1); }
        .demo-section { margin-bottom: 40px; }
        .demo-title { color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px; margin-bottom: 20px; }
        .feature-highlight { background: #e8f4fd; padding: 15px; border-radius: 6px; margin: 15px 0; border-left: 4px solid #3498db; }
        .code-block { background: #f8f9fa; padding: 15px; border-radius: 6px; font-family: 'Courier New', monospace; font-size: 14px; margin: 10px 0; }
    </style>
</head>
<body>
    <div id="app" class="demo-container">
        <h1 style="text-align: center; color: #2c3e50; margin-bottom: 30px;">
            <i class="el-icon-search"></i> 前端模糊搜索功能演示
        </h1>

        <!-- 功能介绍 -->
        <div class="demo-section">
            <h2 class="demo-title">功能介绍</h2>
            <div class="feature-highlight">
                <h4><i class="el-icon-star-on"></i> 前端搜索功能特性</h4>
                <ul>
                    <li><strong>表搜索</strong>：前端模糊搜索，按表名和表备注搜索</li>
                    <li><strong>字段搜索</strong>：实时过滤字段，按字段名、类型、备注搜索</li>
                    <li><strong>模糊匹配</strong>：支持部分关键词匹配，不区分大小写</li>
                    <li><strong>批量操作</strong>：全选可见字段、清空选择等便捷功能</li>
                    <li><strong>即时响应</strong>：纯前端处理，无网络延迟</li>
                </ul>
            </div>
        </div>

        <!-- 表搜索演示 -->
        <div class="demo-section">
            <h2 class="demo-title">1. 数据表前端搜索演示</h2>
            <p>纯前端模糊搜索，支持表名和表备注搜索，即时响应</p>

            <el-form :inline="true">
                <el-form-item label="选择数据表:">
                    <el-select
                        v-model="selectedTable"
                        placeholder="请选择数据表（支持前端搜索）"
                        style="width: 450px;"
                        filterable
                        :filter-method="filterTableOptions"
                        clearable>
                        <el-option
                            v-for="table in tableList"
                            :key="table.table_name"
                            :label="table.display_name"
                            :value="table.table_name">
                            <span style="float: left; font-weight: bold;">{{ table.table_name }}</span>
                            <span style="float: right; color: #8492a6; font-size: 12px" v-if="table.table_comment">{{ table.table_comment }}</span>
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>

            <div style="margin-top: 10px; color: #909399; font-size: 12px;">
                <i class="el-icon-search"></i>
                支持按表名或表备注模糊搜索，输入关键词即可快速筛选
            </div>
        </div>

        <!-- 字段搜索演示 -->
        <div class="demo-section" v-if="selectedTable">
            <h2 class="demo-title">2. 字段搜索演示</h2>
            <p>选择表后，可以搜索和筛选字段</p>
            
            <!-- 字段搜索框 -->
            <div style="margin-bottom: 15px;">
                <el-input
                    v-model="fieldSearchKeyword"
                    placeholder="搜索字段名、类型或备注..."
                    prefix-icon="el-icon-search"
                    clearable
                    @input="filterFields"
                    style="width: 300px;">
                </el-input>
                <el-button 
                    size="small" 
                    type="primary" 
                    @click="selectAllVisibleFields"
                    style="margin-left: 10px;">
                    全选可见字段
                </el-button>
                <el-button 
                    size="small" 
                    type="default" 
                    @click="clearFieldSelection">
                    清空选择
                </el-button>
            </div>
            
            <!-- 字段列表 -->
            <div style="border: 1px solid #dcdfe6; padding: 15px; border-radius: 4px; min-height: 100px; max-height: 300px; overflow-y: auto;">
                <el-checkbox-group v-model="selectedFields">
                    <div
                        v-for="field in filteredFieldList"
                        :key="field.field_name"
                        style="display: flex; align-items: center; margin: 8px 0; padding: 8px; background: #f9f9f9; border-radius: 4px;">
                        <el-checkbox :label="field.field_name" style="margin-right: 10px;">
                            <span style="font-weight: bold; color: #2c3e50;">{{ field.field_name }}</span>
                            <span style="color: #e67e22; margin: 0 8px; font-weight: bold;">({{ field.field_type }})</span>
                            <span style="color: #606266;">{{ field.field_comment }}</span>
                        </el-checkbox>
                    </div>
                </el-checkbox-group>
                
                <!-- 无匹配字段提示 -->
                <div v-if="filteredFieldList.length === 0 && fieldList.length > 0" 
                     style="text-align: center; color: #909399; padding: 30px;">
                    <i class="el-icon-search" style="font-size: 48px; margin-bottom: 10px;"></i>
                    <p>未找到匹配的字段</p>
                    <p style="font-size: 12px;">尝试使用其他关键词搜索</p>
                </div>
                
                <!-- 无字段提示 -->
                <div v-if="fieldList.length === 0" 
                     style="text-align: center; color: #909399; padding: 30px;">
                    <i class="el-icon-info" style="font-size: 48px; margin-bottom: 10px;"></i>
                    <p>该表暂无字段信息</p>
                </div>
            </div>
            
            <!-- 选择统计 -->
            <div style="margin-top: 15px; padding: 10px; background: #f0f9ff; border-radius: 4px; color: #1890ff;">
                <i class="el-icon-data-line"></i>
                <strong>统计信息：</strong>
                已选择 <span style="color: #52c41a; font-weight: bold;">{{ selectedFields.length }}</span> 个字段，
                显示 <span style="color: #1890ff; font-weight: bold;">{{ filteredFieldList.length }}</span> / {{ fieldList.length }} 个字段
            </div>
        </div>

        <!-- 前端搜索实现说明 -->
        <div class="demo-section">
            <h2 class="demo-title">3. 前端搜索实现说明</h2>

            <h4>表搜索实现</h4>
            <div class="code-block">
// Element UI 的 filter-method 属性
&lt;el-select filterable :filter-method="filterTableOptions"&gt;

// 前端过滤方法
filterTableOptions(value) {
    if (!value) return true;
    const keyword = value.toLowerCase();
    return this.tableList.some(table =&gt; {
        const tableName = table.table_name.toLowerCase();
        const tableComment = (table.table_comment || '').toLowerCase();
        return tableName.includes(keyword) || tableComment.includes(keyword);
    });
}
            </div>

            <h4>字段搜索实现</h4>
            <div class="code-block">
// 实时过滤字段
filterFields() {
    const keyword = this.fieldSearchKeyword.toLowerCase().trim();
    if (!keyword) {
        this.filteredFieldList = this.fieldList;
        return;
    }
    this.filteredFieldList = this.fieldList.filter(field =&gt; {
        const fieldName = field.field_name.toLowerCase();
        const fieldType = field.field_type.toLowerCase();
        const fieldComment = (field.field_comment || '').toLowerCase();
        return fieldName.includes(keyword) ||
               fieldType.includes(keyword) ||
               fieldComment.includes(keyword);
    });
}
            </div>

            <h4>优势特点</h4>
            <div class="code-block">
✅ 无网络延迟：纯前端处理，即时响应
✅ 模糊匹配：支持部分关键词匹配
✅ 多维搜索：表名、备注、字段名、类型、备注
✅ 不区分大小写：自动转换为小写进行匹配
✅ 简单高效：无需复杂的后端接口
            </div>
        </div>

        <!-- 使用提示 -->
        <div class="demo-section">
            <h2 class="demo-title">4. 使用提示</h2>
            <el-alert
                title="功能说明"
                type="info"
                :closable="false"
                show-icon>
                <ul style="margin: 10px 0;">
                    <li>表搜索采用前端过滤，支持表名和表备注模糊搜索</li>
                    <li>字段搜索是实时本地过滤，响应速度极快</li>
                    <li>支持按字段名、类型、备注等多个维度搜索</li>
                    <li>提供批量选择功能，提高操作效率</li>
                    <li>纯前端实现，无网络延迟，用户体验更佳</li>
                </ul>
            </el-alert>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    selectedTable: '',
                    tableList: [
                        { table_name: 'user_info', display_name: 'user_info (用户信息表)', table_comment: '用户信息表' },
                        { table_name: 'user_role', display_name: 'user_role (用户角色表)', table_comment: '用户角色表' },
                        { table_name: 'product_info', display_name: 'product_info (产品信息表)', table_comment: '产品信息表' },
                        { table_name: 'order_detail', display_name: 'order_detail (订单详情表)', table_comment: '订单详情表' },
                        { table_name: 'system_config', display_name: 'system_config (系统配置表)', table_comment: '系统配置表' },
                        { table_name: 'log_operation', display_name: 'log_operation (操作日志表)', table_comment: '系统操作日志记录表' },
                        { table_name: 'config_params', display_name: 'config_params (配置参数表)', table_comment: '系统配置参数表' }
                    ],
                    fieldList: [],
                    filteredFieldList: [],
                    selectedFields: [],
                    fieldSearchKeyword: ''
                }
            },
            watch: {
                selectedTable(newVal) {
                    if (newVal) {
                        this.loadMockFields(newVal);
                    }
                }
            },
            methods: {
                // 前端表过滤方法
                filterTableOptions(value) {
                    if (!value) {
                        return true;
                    }

                    const keyword = value.toLowerCase();

                    // 在当前选项中搜索
                    return this.tableList.some(table => {
                        const tableName = table.table_name.toLowerCase();
                        const tableComment = (table.table_comment || '').toLowerCase();
                        const displayName = table.display_name.toLowerCase();

                        return tableName.includes(keyword) ||
                               tableComment.includes(keyword) ||
                               displayName.includes(keyword);
                    });
                },

                loadMockFields(tableName) {
                    // 模拟字段数据
                    const mockFields = {
                        'user_info': [
                            { field_name: 'user_id', field_type: 'VARCHAR(32)', field_comment: '用户唯一标识ID' },
                            { field_name: 'user_name', field_type: 'VARCHAR(50)', field_comment: '用户登录名称' },
                            { field_name: 'real_name', field_type: 'VARCHAR(50)', field_comment: '用户真实姓名' },
                            { field_name: 'email', field_type: 'VARCHAR(100)', field_comment: '用户邮箱地址' },
                            { field_name: 'phone', field_type: 'VARCHAR(20)', field_comment: '手机号码' },
                            { field_name: 'birthday', field_type: 'DATE', field_comment: '出生日期' },
                            { field_name: 'id_card', field_type: 'VARCHAR(18)', field_comment: '身份证号码' },
                            { field_name: 'address', field_type: 'TEXT', field_comment: '详细地址信息' }
                        ],
                        'user_role': [
                            { field_name: 'role_id', field_type: 'INT(11)', field_comment: '角色唯一ID' },
                            { field_name: 'role_name', field_type: 'VARCHAR(30)', field_comment: '角色名称' },
                            { field_name: 'role_desc', field_type: 'VARCHAR(200)', field_comment: '角色描述信息' },
                            { field_name: 'permissions', field_type: 'TEXT', field_comment: '权限列表JSON' }
                        ],
                        'product_info': [
                            { field_name: 'product_id', field_type: 'VARCHAR(32)', field_comment: '产品ID' },
                            { field_name: 'product_name', field_type: 'VARCHAR(100)', field_comment: '产品名称' },
                            { field_name: 'product_desc', field_type: 'TEXT', field_comment: '产品详细描述' },
                            { field_name: 'price', field_type: 'DECIMAL(10,2)', field_comment: '产品价格' }
                        ]
                    };

                    this.fieldList = mockFields[tableName] || [];
                    this.filteredFieldList = this.fieldList;
                    this.fieldSearchKeyword = '';
                    this.selectedFields = [];
                },

                filterFields() {
                    const keyword = this.fieldSearchKeyword.toLowerCase().trim();
                    
                    if (!keyword) {
                        this.filteredFieldList = this.fieldList;
                        return;
                    }
                    
                    this.filteredFieldList = this.fieldList.filter(field => {
                        const fieldName = field.field_name.toLowerCase();
                        const fieldType = field.field_type.toLowerCase();
                        const fieldComment = (field.field_comment || '').toLowerCase();
                        
                        return fieldName.includes(keyword) || 
                               fieldType.includes(keyword) || 
                               fieldComment.includes(keyword);
                    });
                },

                selectAllVisibleFields() {
                    const visibleFieldNames = this.filteredFieldList.map(field => field.field_name);
                    const allSelected = [...new Set([...this.selectedFields, ...visibleFieldNames])];
                    this.selectedFields = allSelected;
                    this.$message.success(`已选择 ${visibleFieldNames.length} 个可见字段`);
                },

                clearFieldSelection() {
                    this.selectedFields = [];
                    this.$message.info('已清空字段选择');
                }
            }
        });
    </script>
</body>
</html>
