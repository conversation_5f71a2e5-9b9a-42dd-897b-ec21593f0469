﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>登录</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/login.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <script src="__PUBLIC__/plugs/element/axios.min.js"></script>
    <script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
    <script src="__PUBLIC__/static/js/request.js"></script>
</head>
<body>
<div class="y_noSelect" id="app" v-cloak>
    <div class="login_bg">
        <div class="login_box">
            <div class="login_title">
                <p class="login_tit">成都市企业安全生产标准化信息管理系统</p>
                <span style="line-height: 50px;font-size:22px;color:#999999">- 评审单位登录 -</span>
            </div>
            <div class="login_content">
                <ul class="login_nav">
                    <li :class="'login_nav_item '+(nav_on==k?'active':'')" v-for="(v, k) in nav" @click="navChang(k)">{{v}}</li>
                </ul>
                <div class="login_item_box" v-show="nav_on==0">
                    <div class="login_item">
                        <input class="login_inp name" type="text" v-model="form.username" placeholder="请输入账号/手机号" @keyup.enter="doSub" />
                    </div>
                    <div class="login_item">
                        <input class="login_inp psd" type="password" v-model="form.password" placeholder="请输入密码" @keyup.enter="doSub" />
                    </div>
                    <div class="login_item">
                        <input class="login_inp code" type="tel" v-model="form.imgcode" placeholder="请输入图片验证码" @keyup.enter="doSub" />
                        <img class="login_cod" :src="codeurl" @click="getCode"/>
                    </div>
                </div>
                <div class="login_item_box" v-show="nav_on==1">
                    <div class="login_item">
                        <input class="login_inp name" type="text" v-model="form.username" placeholder="请输入手机号" @keyup.enter="doSub" />
                    </div>
                    <div class="login_item">
                        <input class="login_inp code" type="tel" v-model="form.code" placeholder="请输入短信验证码" @keyup.enter="doSub" />
                        <span :class="'login_inp_btn '+(yzm_boo?'active':'')" @click="getCod">{{yzmtex}}</span>
                    </div>
                    <div class="login_item">
                        <input class="login_inp code" type="tel" v-model="form.imgcode" placeholder="请输入图片验证码" @keyup.enter="doSub" />
                        <img class="login_cod" :src="codeurl" @click="getCode"/>
                    </div>
                </div>
                <button class="login_btn" @click="doSub">登 录</button>
<!--                <p style="text-align: center;font-size:14px;color:#999;">还没有账号？<a style="color:#409eff;cursor: pointer;" href="register">立即注册</a></p>-->
            </div>
        </div>
        <!-- <div class="login_box">
            <img class="login_box_bg" src="__PUBLIC__/static/images/img/login_bg_in.png"/>
            <img class="login_logo" src="__PUBLIC__/static/images/icon/login_logo.png"/>
            <div class="login_cont">
                <img class="login_title" src="__PUBLIC__/static/images/icon/login_title.png"/>
                <input type="text" class="login_input user" v-model="form." placeholder="" />
                <input type="password" class="login_input pasd" v-model="form.password" placeholder="请输入密码" @keyup.enter="doSub"/>
                <div class="y_textRight">
                    <span :class="'login_check '+(form.remb?'active':'')" @click="rember">记住密码</span>
                </div>
                <button class="login_btn" @click="doSub">登 录</button>
            </div>
        </div> -->
    </div>
</div>
</body>
</html>
<script type="text/javascript">
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data: {
            nav_on: 0,
            nav: ['密码登录', '验证码登录'],
            form: {type: 0},
            codeurl: '',
            yzmtex: '获取验证码',
            yzm_boo: false,
        },
        methods: {
            navChang: function(k){
                this.nav_on = k
                this.form.type = k
            },
            getCod: function(){
                var _this = this;
                if(this.yzm_boo){return}
                if(!this.form.username){
                    _this.$message({
                        message: '请填写手机号码',
                        type: "error"
                    });
                    return;
                }
                const loading = this.$loading();
                axios.post('loginSms', {mobile: _this.form.username}).then(function (res) {
                    loading.close();
                    if (res.data.code == 0) {
                        _this.yzm_boo = true;
                        var time = 60;
                        _this.yzmtex = '重新获取（'+time+'）';
                        var timer = setInterval(function(){
                            --time;
                            if(time < 0){
                                clearInterval(timer);
                                _this.yzm_boo = false;
                                _this.yzmtex = '获取验证码';
                            }else{
                                _this.yzmtex = '重新获取（'+time+'）';
                            }
                        }, 1000)
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            getCode: function(){
                this.codeurl = 'verify?t='+new Date().getMilliseconds();
            },
            doSub: function () {
                let _this = this;
                if (!_this.form.username) {
                    _this.$message({
                        message: '请输入账号/手机号',
                        type: "error"
                    });
                    return;
                }
                if (!_this.form.password&&_this.nav_on==0) {
                    /*_this.$message({
                        message: '请输入密码',
                        type: "error"
                    });
                    return;*/
                }
                if (!_this.form.imgcode) {
                    _this.$message({
                        message: '请输入图片验证码',
                        type: "error"
                    });
                    return;
                }
                if (!_this.form.code&&_this.nav_on==1) {
                    _this.$message({
                        message: '请输入验短信证码',
                        type: "error"
                    });
                    return;
                }
                const loading = this.$loading();
                axios.post('login', _this.form).then(function (res) {
                    if (res.data.code == 0) {
                        location.href = '/general/toppingsoft/index.php/org/index/index';
                    } else {
                        loading.close();
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        _this.getCode();
                        return false;
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            }
        },
        mounted: function () {
            let _this = this;
            _this.getCode();
            if (window.top !== window.self) {
                // 子iframe中发送消息给父窗口
                window.parent.postMessage({ redirectUrl: window.location.href }, "*");
            }
        }
    })
</script>