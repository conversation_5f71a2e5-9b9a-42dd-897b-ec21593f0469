<?php

namespace app\model;

use Dompdf\Options;
use Html2image\Assets\Html2img;
use think\facade\Db;
use think\facade\Filesystem;
use think\facade\Request;
use think\Model;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Settings;
use Dompdf\Dompdf;

class FileModel extends Model {

    /**
     * @Apidoc\Title("上传文件到临时文件夹")
     * @Apidoc\Param("file", type="string",default="", desc="文件")
     * @Apidoc\Param("model", type="string",default="", desc="上传模块区分{为空则默认为：控制器名_方法名}")
     * @Apidoc\Returned("id", type="object", desc="文件id")
     * @Apidoc\Returned("filepath", type="int", desc="文件路径")
     */
    public static function upload($file,$model = ''){
        $user_id = $user_name = '';
        switch (\request()->root()){
            case '/admin':
                $user_id = $_SESSION['LOGIN_USER_ID'];
                $user_name = $_SESSION['LOGIN_USER_NAME'];
                break;
            case '/company':
                $user_id = $_SESSION['company']['user_id'];
                $user_name = $_SESSION['company']['user_name'];
                break;
            case '/expert':
                $user_id = $_SESSION['expert']['id'];
                $user_name = $_SESSION['expert']['name'];
                break;
            case '/area':
                $user_id = $_SESSION['area']['user_id'];
                $user_name = $_SESSION['area']['user_name'];
                break;
            case '/city':
                $user_id = $_SESSION['city']['user_id'];
                $user_name = $_SESSION['city']['user_name'];
                break;
            case '/org':
                $user_id = $_SESSION['org']['user_id'];
                $user_name = $_SESSION['org']['user_name'];
                break;
            default:
                break;
        }
        if(empty($user_id)){
            result('',9002,'请先登录');
        }
        try {
            Db::startTrans();
            $type = $file->getOriginalMime();
            $name = $file->getOriginalName();
            $size = $file->getSize();
            $ext = $file->extension();
            $model = empty($model)?request()->controller().'_'.request()->action():$model;
            $path = 'tmp/'.date('Ymd');
            $fileName = md5(time().rand(100,999));
            // 上传到本地服务器
            $savepath = Filesystem::disk('oa')->putFileAs($path, $file,$fileName.'.'.$ext);
            $data = [
                'model' => $model,
                'code' => $fileName,
                'filename' => $name,
                'filetype' => $type,
                'fileext' => $ext,
                'filesize' => round($size/1024,2).'k',
                'filepath' => $savepath,
                'create_user' => $user_id,
                'create_user_name' => $user_name,
                'create_time' => date('Y-m-d H:i:s'),
            ];
            $id = Db::table('top_files')->insertGetId($data);
            $id = Db::table('top_files')->where($data)->field('id')->find()['id'];//兼容达梦
            Db::commit();
            return ['id'=>$id,'name'=>$name,'code'=>$fileName,'url'=>'/general/toppingsoft/index.php/file/info?code='.$fileName];
        } catch (Exception $exception) {
            Db::rollback();
            return $exception->getMessage();
        }
    }


    /**
     * @Apidoc\Title("保存文件到正式文件夹")
     * @Apidoc\Param("file", type="string",default="", desc="文件id或文件路径")
     * @Apidoc\Param("folder", type="string",default="", desc="正式文件夹目录")
     * @Apidoc\Param("filename", type="string",default="", desc="文件名，为空则随机生成")
     * @Apidoc\Returned("id", type="object", desc="文件id")
     * @Apidoc\Returned("filepath", type="int", desc="文件路径")
     */
    public static function saveFile($file,$folder,$filename = ''){
        $user_id = $user_name = '';
        switch (\request()->root()){
            case '/admin':
                $user_id = $_SESSION['LOGIN_USER_ID'];
                $user_name = $_SESSION['LOGIN_USER_NAME'];
                break;
            case '/company':
                $user_id = $_SESSION['company']['user_id'];
                $user_name = $_SESSION['company']['user_name'];
                break;
            case '/expert':
                $user_id = $_SESSION['expert']['id'];
                $user_name = $_SESSION['expert']['name'];
                break;
            case '/area':
                $user_id = $_SESSION['area']['user_id'];
                $user_name = $_SESSION['area']['user_name'];
                break;
            case '/city':
                $user_id = $_SESSION['city']['user_id'];
                $user_name = $_SESSION['city']['user_name'];
                break;
            case '/org':
                $user_id = $_SESSION['org']['user_id'];
                $user_name = $_SESSION['org']['user_name'];
                break;
            default:
                break;
        }
        if(empty($user_id)){
            result('',9002,'请先登录');
        }
        $refile = Db::table('top_files')->where([['filepath|code','=',$file]])->find();
        if($refile['formal']==1){
            return ['id'=>$refile['id'],'code'=>$refile['code'],'filepath'=>$refile['filepath']];
        }
        $filepath = empty($refile)?$file:$refile['filepath'];
        $filename = empty($filename)?create_nonce_str(32):$filename;
        try {
            Db::startTrans();
            $re = move_file($filepath,$folder,$filename);
            if($re){
                if($refile){
                    Db::table('top_files')->where(['id'=>$refile['id']])->update(['filepath'=>$re,'formal'=>'1']);
                    $id = $refile['id'];
                }else{
                    $fileinfo = pathinfo($filepath);
                    $finfo = finfo_open(FILEINFO_MIME);
                    $mimeType = finfo_file($finfo, config('filesystem.disks.oa.root').'/'.$re);
                    finfo_close($finfo);
                    $data = [
                        'model' => request()->controller().'_'.request()->action(),
                        'code' => $filename,
                        'filename' => $fileinfo['basename'],
                        'filetype' => $mimeType,
                        'fileext' => $fileinfo['extension'],
                        'filesize' => round(filesize(config('filesystem.disks.oa.root').'/'.$re)/1024,2).'k',
                        'filepath' => $re,
                        'create_user' => $user_id,
                        'create_user_name' => $user_name,
                        'create_time' => date('Y-m-d H:i:s'),
                        'formal' => '1',
                    ];
                    $id = Db::table('top_files')->insertGetId($data);
                }
            }else{
                Db::rollback();
                return false;
            }
            Db::commit();
            return ['id'=>$id,'code'=>$filename,'filepath'=>$re];
        } catch (Exception $exception) {
            Db::rollback();
            return false;
        }
    }

    public static function getFile($id=0,$code='',$type='url'){
        if(!empty($id)){
            $re = Db::table('top_files')->where('id','=',$id)->find();
        }else if(!empty($code)){
            $re = Db::table('top_files')->where('code','=',$code)->find();
        }
        if(empty($re)){
            return empty($type)?['id'=>'','code'=>'','name'=>'','ext'=>'','url'=>'','path'=>'']:'';
        }
        $data = [
            'id' => $re['id'],
            'code' => $re['code'],
            'name' => $re['filename'],
            'ext' => $re['fileext'],
            'url' => '/general/toppingsoft/index.php/file/info/'.$re['filename'].'?code='.$re['code'],
            'path' => $re['formal']==1?get_oa_attach().$re['filepath']:str_replace('webroot\general\toppingsoft\\','attach\toppingsoft',root_path()).$re['filepath'],
        ];
        return empty($type)?$data:$data[$type];
    }

    public static function getFiles($ids=''){
        $files = [];
        if(empty($ids)){
            return $files;
        }
        $res = Db::table('top_files')->where('id','in',$ids)->select()->toArray();
        foreach ($res as $k=>$v){
            $files[] = [
                'id' => $v['id'],
                'name' => $v['filename'],
                'url' => $v['filepath'],
            ];
        }
        return $files;
    }

    public static function down($filepath='',$filename=''){
        if(!file_exists($filepath)){
            exit('文件不存在');
        }else{
            $ext = strrchr($filepath,'.');
            $file = fopen($filepath,'rb');
            header("Content-type:application/octet-stream");
            header("Accept-Ranges:bytes");
            header("Accept-Length:".filesize($filepath));
            header("Content-Disposition: attachment; filename=".$filename.$ext);
            echo fread($file,filesize($filepath));
            fclose($file);
            exit();
        }
    }

    public static function wordToPdf($old_file,$new_file,$file_format='pdf'){

        $libreOfficePath = config('app.libreoffice_path');
        if(!empty($libreOfficePath)){
            $command = '"'.$libreOfficePath.'" --headless  --convert-to pdf --outdir '.dirname($new_file).' '.$old_file;
            //exit($command);
            $result = shell_exec($command);
            return $result;
        }else{
            $old_path = $old_file;
            $new_path = $new_file;
//        echo "sudo unoconv -f ".$file_format." -o ".$new_path." ".$old_path;die;
        $result = shell_exec("sudo unoconv -f ".$file_format." -o ".$new_path." ".$old_path);
        //$output = shell_exec("sudo /usr/bin/unoconv --version 2>&1");
     //exit($output);
     
            return $result;
        }

    }

    //根据code数组删除附件【删除】
    public static function deleteFile($codeArr)
    {
        if( empty($codeArr) || count($codeArr) <= 0 )
        {
            return false;
        }

        //根据id查询附件地址
        foreach ($codeArr as $code)
        {
            $attchment = Db::table('top_files')->where(['code'=>$code])->find();
            //删除文件
            if( isset($attchment['filepath']) && !empty($attchment['filepath']) )
            {
                $path = $attchment['formal']==1?get_oa_attach().$attchment['filepath']:str_replace('webroot\general\toppingsoft\\','attach\toppingsoft',root_path()).'\\'.$attchment['filepath'];

                if(file_exists($path)){
                    unlink($path);
                }
            }

            //删除数据
            Db::table('top_files')->where(['id'=>$attchment['id']])->delete();
        }
        return true;
    }

}
