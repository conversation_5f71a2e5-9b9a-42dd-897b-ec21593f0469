<?php

namespace app\demo\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use hg\apidoc\annotation as Apidoc;
use app\BaseController;
use think\facade\Db;

/**
 * 标题也可以这样直接写
 * @Apidoc\Title("common方法示例参考")
 * @Apidoc\Group("Demo")
 * @Apidoc\Sort(1)
 */
class Common extends BaseController {

    /**
     * @Apidoc\Title("移动文件")
     */
//    public function move_file() {
//        $result = move_file('storage/tmp/20220523/1653288099594.jpg','userInfo/avatar','123',true);
//        result($result);
//    }

    /**
     * @Apidoc\Title("生成随机数")
     */
    public function create_nonce_str() {
        $result = create_nonce_str(8);
        result($result);
    }

    /**
     * @Apidoc\Title("获取客户端ip")
     */
    public function get_ip() {
        $result = get_ip();
        result($result);
    }

    /**
     * @Apidoc\Title("生成树形结构")
     */
    public function get_tree_children() {
        $data = [
            ['id'=>1,'name'=>'中国','pid'=>0],
            ['id'=>2,'name'=>'四川','pid'=>1],
            ['id'=>3,'name'=>'成都','pid'=>2],
            ['id'=>4,'name'=>'北京','pid'=>1],
            ['id'=>5,'name'=>'广东','pid'=>1],
            ['id'=>6,'name'=>'深圳','pid'=>5],
        ];
        $result = get_tree_children($data);
        result($result);
    }

    /**
     * @Apidoc\Title("格式化分类")
     */
    public function tidy_tree() {
        $data = [
            ['id'=>1,'name'=>'中国','parent_id'=>0],
            ['id'=>2,'name'=>'四川','parent_id'=>1],
            ['id'=>3,'name'=>'成都','parent_id'=>2],
            ['id'=>4,'name'=>'北京','parent_id'=>1],
            ['id'=>5,'name'=>'广东','parent_id'=>1],
            ['id'=>6,'name'=>'深圳','parent_id'=>5],
        ];
        $result = tidy_tree($data);
        result($result);
    }

    /**
     * @Apidoc\Title("获取文件带域名的完整路径")
     */
    public function get_file_link() {
        $result = get_file_link('storage/tmp/20220523/1653288099594.jpg');
        result($result);
    }

    /**
     * @Apidoc\Title("sql 参数过滤")
     */
    public function sql_filter() {
        $result = sql_filter();
        result($result);
    }

    /**
     * @Apidoc\Title("时间戳人性化转化")
     */
    public function time_tran() {
        $result = time_tran(time()-86400);
        result($result);
    }

    /**
     * @Apidoc\Title("匿名处理处理用户昵称")
     */
    public function anonymity() {
        $result = anonymity('张三丰');
        result($result);
    }

    /**
     * @Apidoc\Title("分级排序")
     */
    public function sort_list_tier() {
        $data = [
            ['id'=>1,'name'=>'中国','pid'=>0],
            ['id'=>2,'name'=>'四川','pid'=>1],
            ['id'=>3,'name'=>'成都','pid'=>2],
            ['id'=>4,'name'=>'北京','pid'=>1],
            ['id'=>5,'name'=>'广东','pid'=>1],
            ['id'=>6,'name'=>'深圳','pid'=>5],
        ];
        $result = sort_list_tier($data);
        result($result);
    }

    /**
     * @Apidoc\Title("get提交")
     */
    public function http_get() {
        $result = http_get();
        result($result);
    }

    /**
     * @Apidoc\Title("post提交")
     */
    public function http_post() {
        $result = http_post();
        result($result);
    }

    /**
     * @Apidoc\Title("添加操作记录")
     */
    public function add_log() {
        $data = Db::table('top_user_info')->where(['user_id'=>'admin'])->find();
        $result = add_log('top_user_info','1','修改用户档案',json($data));
        result($result);
    }

    /**
     * @Apidoc\Title("根据姓名生成用户姓名索引")
     */
    public function getChnprefix() {
        $result = getChnprefix();
        result($result);
    }

    /**
     * @Apidoc\Title("获取省市区列表")
     * @Apidoc\Param("pid", type="int",default="2", desc="父级id")
     */
    public function get_pcas() {
        $result = get_pcas($this->request->param('pid'));
        result($result);
    }

    /**
     * @Apidoc\Title("获取所有省市区数据")
     * @Apidoc\Param("type", type="int",default="", desc="返回数据结构{默认0：简单code=>name结构，1：树形结构，2：code明细结构}")
     */
    public function get_pcas_all() {
        $result = get_pcas_all($this->request->param('type'));
        result($result);
    }


}
