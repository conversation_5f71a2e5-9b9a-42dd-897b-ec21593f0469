<?php

namespace app\admin\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use app\BaseController;
use hg\apidoc\annotation as Apidoc;
use think\facade\View;
use think\Request;
use think\facade\Db;
use app\admin\model\TopCityModel;
use app\admin\model\TopPcaModel;
use app\admin\model\TopCityUserModel;
use app\model\ExcelModel;

/**
 * @Apidoc\Title("企业管理")
 * @Apidoc\Group("Index")
 * @Apidoc\Sort(1)
 */
class City extends BaseController
{

	/**
	 * @Apidoc\Title("市应急局首页")
	 * @Apidoc\Desc("市应急局首页")
	 * @Apidoc\Method("POST")
	 * @Apidoc\Tag("首页")
	 * @Apidoc\Returned("data", type="object", desc="基础数据")
	 */
	public function index()
	{
		View::assign('title', '首页');
		return view();
	}

	/**
	 * 获取列表数据
	 */
	public function get_list()
	{
		$param = $this->request->param();
		$model = new TopCityModel();
		$pcaModel = new TopPcaModel();
		$data = $model->getList($param, $param['page'], $param['limit']);
		result(['list' => $data, 'pca' => $pcaModel->getPcaList(2)]);
	}

	/**
	 * 保存数据
     * @sysLog 保存市局数据
	 */
	public function save_data()
	{
		$param = $this->request->param();
		$model = new TopCityModel();
		$id = intval($param['id']);
		if ($id) $model = $model::find($id);
		$data = $model->newSave($model, $param, $id);
		result(null, 0, $data);
	}

	/**
	 * 删除市局数据
	 */
	public function delete()
	{
		$param = $this->request->param();
		$id = intval($param['id']);
		$model = TopCityModel::find($id);
		$data = $model->delData($model);
		result(null, 0, $data);
	}

	/**
	 * 导出
     * @sysLog 导出市局数据
	 */
	public function export()
	{
		$param = $this->request->param();
		$model = new TopCityModel();
		$data = $model->getList($param, 0, 0, true);
		$title = $model->getTitle(); 
		ExcelModel::exportExcel($title, $data, '市应急局基本信息导出', true);
	}

	/**
	 * 导入模板
	 */
	public function importTemplate()
	{
		$model = new TopCityModel();
		$title = $model->getTitle();
		$ord = ord('A');
		foreach ($title as $k => $v) {
			if ($v['field'] == 'statusStr' || $v['field'] == 'pcas_names') {
				unset($title[$k]);
				continue;
			}
			$title[$k]['field'] = chr($ord);
			$ord += 1;
		}

		ExcelModel::exportExcel(array_merge_recursive($title), [], '市应急局基本信息导入模板');
	}

	/**
	 * 导入
     * @sysLog 导入市局数据
	 */
	public function import()
	{
		$data = import("input");
		$dataHeader = importHeader("input");
		$model = new TopCityModel();
		$result = $model->importData($data, $dataHeader);
		$msg = count($result['success']) . '条数据保存成功，' . count($result['repeat']) . '条数据重复导入，' . count($result['fail']) . '条数据处理失败。';
		result($result, 0, $msg);
	}


	public function personnel()
	{
		View::assign('title', '用户管理');
		return view();
	}

	/**
	 * 获取列表数据
	 */
	public function get_list_p()
	{
		$param = $this->request->param();
		$model = new TopCityUserModel();
		$cityModel = new TopCityModel();
		$data = $model->getList($param, $param['page'], $param['limit']);
        $cityDepartment = \app\city\model\UserModel::getDepartmentMap();
		result(array('list' => $data, 'city' => $cityModel->getList([], 0, 0, true), 'department' => $cityDepartment, 'role' => config('global.role')));
	}

	/**
	 * 保存数据
     * @sysLog 保存区县应急局用户数据
	 */
	public function save_data_p()
	{
		$param = $this->request->param();
		$model = new TopCityUserModel();
		$id = intval($param['id']);
		if ($id) $model = $model::find($id);
		$data = $model->newSave($model, $param, $id);
		result(null, 0, $data);
	}

	/**
	 * 删除
     * @sysLog 删除区县应急局用户数据
	 */
	public function delete_p()
	{
		$param = $this->request->param();
		$id = intval($param['id']);
		$model = TopCityUserModel::find($id);
		$data = $model->delData($model);
		result(null, 0, $data);
	}

	/**
	 * 导出
     * @sysLog 导出区县应急局用户数据
	 */
	public function export_p()
	{
		$param = $this->request->param();
		$model = new TopCityUserModel();
		$data = $model->getList($param, 0, 0, true);
		$title = $model->getTitle();
		ExcelModel::exportExcel($title, $data, '区县应急局用户信息导出', true);
	}

	/**
	 * 导入模板
	 */
	public function importTemplate_p()
	{
		$model = new TopCityUserModel();
		$cityModel = new TopCityModel();
		$title = $model->getTitle();
		$ord = ord('A');
		foreach ($title as $k => $v) {
			if ($v['field'] == 'statusStr' || $v['field'] == 'salt' || $v['field'] == 'reg_time' || $v['field'] == 'reg_ip') {
				unset($title[$k]);
				continue;
			}
			if ($v['field'] == 'deptName') {
				$title[$k]['type'] = 'list';
				$deptArr = $model->department_arr;
				foreach ($deptArr as $item) {
					$dept[] = $item['label'];
				}
				$title[$k]['listData'] = $dept;
			}
			if ($v['field'] == 'roleName') {
				$title[$k]['type'] = 'list';
				$roleArr = $model->role_arr;
				foreach ($roleArr as $item) {
					$role[] = $item['label'];
				}
				$title[$k]['listData'] = $role;
			}
			if ($v['field'] == 'cityName') {
				$title[$k]['type'] = 'list';
				$cityArr = $cityModel->getList([], 0, 0, true);
				foreach ($cityArr as $item) {
					$city[] = $item['name'];
				}
				$title[$k]['listData'] = $city;
			}

			$title[$k]['field'] = chr($ord);
			$ord += 1;
		}
//		dd(array_merge_recursive($title));die;
		$data = array(array('姓名', '用户名', '电话', '邮箱', '所属应急局', '部门', '角色'));

		ExcelModel::exportExcel(array_merge_recursive($title), $data, '区县应急局用户信息导入模板');
	}

	/**
	 * 导入
     * @sysLog 导入区县应急局用户数据
	 */
	public function import_p()
	{
		$data = import("input");
		$dataHeader = importHeader("input");
		$model = new TopCityUserModel();
		$result = $model->importData($data, $dataHeader);
		$msg = count($result['success']) . '条数据保存成功，' . count($result['repeat']) . '条数据重复导入，' . count($result['fail']) . '条数据处理失败。';
		result($result, 0, $msg);
	}

	/**
	 * 修改密码
     * @sysLog 修改区县应急局用户密码
	 */
	public function update_password()
	{
		$param = $this->request->param();
		$id = intval($param['id']);
		$model = TopCityUserModel::find($id);
		$data = $model->updatePassword($model, $param['password']);
		result(null, 0, $data);
	}

}
