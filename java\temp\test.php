<?php

// 假设你的 Java 程序打包为 sm4cbc.jar，参数依次为明文、密钥、IV
$plaintext = "123456781234567812";
$key = "yourkeyhere";
$iv = "1234567812345678";
$cmd = "java -jar untitled-1.0-SNAPSHOT.jar encrypt \"$plaintext\" \"$key\" \"$iv\"";
echo $cmd;
exit;
//$cmd = 'java -Dhsm.config.path=f:\cay\java\CHSM-SDK-JAVA-1.4.0-20241209\HsmConfig.properties -cp hsm-sdk-java-1.4.0-SNAPSHOT.jar;bcprov-jdk15on-1.68.jar Csr encrypt "123456781234567812" "yourkeyhere" "1234567812345678"';


$encrypted = shell_exec($cmd);
echo "加密结果: " . $encrypted;

// 解密
$cmd = "java -cp untitled-1.0-SNAPSHOT.jar decrypt \"$encrypted\" \"$key\" \"$iv\"";
$decrypted = shell_exec($cmd);
echo "解密结果: " . $decrypted;
?>

