<?php
declare (strict_types=1);

namespace app\area\controller;

use app\model\ExcelModel;
use app\model\FileModel;
use hg\apidoc\annotation as Apidoc;
use think\facade\Db;
use think\facade\View;
use think\Request;
use app\area\model\CompanyModel;

class Company extends Base
{
    // 需要加密处理的字段配置
    private $smFields = ['legal','legal_mobile'];
    /**
     * @Apidoc\Title("企业管理列表")
     * @Apidoc\Desc("企业管理列表")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function index($limit = 20, $excel = 0)
    {
        if (request()->isAjax()) {
            $title = $this->request->param('title', '', 'trim');
            $enterprise_size = $this->request->param('enterprise_size', '', 'trim');
            $industry = $this->request->param('industry', '', 'trim');
            $mb_operate_address = $this->request->param('mb_operate_address', '', 'trim');
            $mb_status = $this->request->param('mb_status', '', 'trim');
            $ca_status = $this->request->param('ca_status', '', 'trim');
            
            $where = [
                ['area_id','=',$_SESSION['area']['id']],
                ['dept_id','in',$_SESSION['area']['manager']],
            ];
            if(!empty($title)){
                $where[] = ['name|legal','like',"%{$title}%"];
            }
            //企业规模
            if (!empty($enterprise_size)) {
                $where[] = ['enterprise_size', 'like', "%{$enterprise_size}%"];
            }
            //行业
            if (!empty($industry)) {
                $where[] = ['industry', 'like', "%{$industry}%"];
            }
            //生产经营地址
            if (!empty($mb_operate_address)) {
                $where[] = ['operate_address_info', 'like', "%{$mb_operate_address}%"];
            }

            //企业状态
            if (!empty($mb_status)) {
                switch ($mb_status) {
                    case 'info':
                        $where[] = ['stand_status', '=', 0];
                        break;
                    case 'primary':
                        $where[] = ['stand_status', '=', 1];
                        $where[] = ['ca_status', '=', 0];
                        break;
                    case 'success':
                        $where[] = ['stand_status', '=', 1];
                        $where[] = ['ca_status', '=', 1];
                        break;
                }
            }

            //证书状态
            if (!empty($ca_status)) {
                switch ($ca_status) {
                    case 'warning':
                        if (!$mb_status == 'primary') {
                            $where[] = ['ca_status', '=', 0];
                        }
                        break;
                    case 'success':
                        $where[] = ['ca_status', '=', 1];
                        break;
                    case 'danger':
                        $where[] = ['ca_status', '=', 2];
                }
            }

            $res = Db::table('top_company_info')->where($where)->order('id desc');
            if ($excel == 1) {
                $data = [];
                $res = $res->select()->toArray();
                foreach ($res as $k => $item) {
                    $item = \app\model\CompanyModel::codeToText($item);
                    $item['mb_status_text'] = \app\model\CompanyModel::getStatusText($item['stand_status'], $item['ca_status']);
                    $item['ca_status_text'] = \app\model\CompanyModel::getCaStatusText($item['ca_status']);
                    $item = $this->getDecryptData($data[$k]);
                    $data[$k] = $item;
                }
                $title = [
                    ['title' => '所属区县', 'field' => 'mb_region', 'width' => '20',],
                    ['title' => '企业名称', 'field' => 'name', 'width' => '30', 'type' => 'string'],
                    ['title' => '生产经营地址', 'field' => 'mb_operate_address', 'width' => '30', 'type' => 'string'],
                    ['title' => '企业状态', 'field' => 'mb_status_text', 'type' => 'string'],
                    ['title' => '是否取得证书', 'field' => 'ca_status_text', 'type' => 'string'],
                    ['title' => '行业/专业', 'field' => 'industry', 'type' => 'string'],
                    ['title' => '企业规模', 'field' => 'enterprise_size', 'type' => 'string'],
                ];
                ExcelModel::exportExcel($title, $data, '企业信息导出', true);
            }
            $res = $res->paginate($limit)->each(function ($item, $key) {
                $item = \app\model\CompanyModel::codeToText($item);
                return $item;
            });
            result($res);
        } else {
            $industry = Db::table('top_industry')->where('pid','=',0)->field(['name'])->order('sort')->select()->toArray();
            View::assign('industry', $industry);
            return view();
        }
    }

    /**
     * @Apidoc\Title("企业信息审核列表")
     * @Apidoc\Desc("企业信息审核列表")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function apply($limit = 20, $excel = 0)
    {
        if (request()->isAjax()) {
            $status = $this->request->param('status','','trim');
            $title = $this->request->param('title','','trim');
            $where = [
                ['area_id','=',$_SESSION['area']['id']],
                ['dept_id','in',$_SESSION['area']['manager']],
            ];
            if(!empty($status)){
                $where[] = ['status','=',$status];
            }
            if(!empty($title)){
                $where[] = ['name|legal','like',"%{$title}%"];
            }
            $res = Db::table('top_company_info_apply')->where($where)->order('id desc');
            //获取企业用户对于的企业的字典信息
            $userComanys = Db::table('top_company_info')->column('id','user_id');
            if ($excel == 1) {
                $res = $res->select()->each(function ($item, $key) {
                    $item = CompanyModel::codeToText($item);
                    $item['company_id'] = $userComanys[$item['user_id']] ?? 0;
                    return $item;
                })->toArray();
                $title = [
                    ['title' => '企业名称', 'field' => 'name', 'width' => '30'],
                    ['title' => '注册地址', 'field' => 'mb_reg_address', 'width' => '30'],
                    ['title' => '生产经营地址', 'field' => 'mb_operate_address', 'width' => '30'],
                    ['title' => '所属行政区', 'field' => 'mb_region', 'width' => '20'],
                    ['title' => '法人', 'field' => 'legal', 'width' => '20'],
                    ['title' => '法人联系方式', 'field' => 'legal_mobile', 'width' => '20'],
                    ['title' => '法人邮箱', 'field' => 'legal_email', 'width' => '20'],
                    ['title' => '座机电话号码', 'field' => 'phone', 'width' => '20'],
                    ['title' => '企业传真', 'field' => 'fax', 'width' => '20'],
                    ['title' => '产业园区', 'field' => 'industrial_park', 'width' => '20'],
                    ['title' => '邮政编码', 'field' => 'postal_code', 'width' => '20'],
                    ['title' => '国民经济行业', 'field' => 'mb_economy_sector', 'width' => '20'],
                    ['title' => '行业', 'field' => 'industry', 'width' => '20'],
                    ['title' => '专业', 'field' => 'specialty', 'width' => '20'],
                    ['title' => '统一社会信用代码', 'field' => 'license_number', 'width' => '20'],
                    ['title' => '信用代码有效期', 'field' => 'license_date', 'width' => '20'],
                    ['title' => '经济类型', 'field' => 'mb_economy_type', 'width' => '20'],
                    ['title' => '企业规模', 'field' => 'enterprise_size', 'width' => '20'],
                    ['title' => '注册资本', 'field' => 'reg_money', 'width' => '20'],
                    ['title' => '固定资产', 'field' => 'fixed_asset', 'width' => '20'],
                    ['title' => '安全管理人员', 'field' => 'manager', 'width' => '20'],
                    ['title' => '安全管理人员联系方式', 'field' => 'manager_mobile', 'width' => '20'],
                    ['title' => '安全管理人员邮箱', 'field' => 'manager_email', 'width' => '20'],
                    ['title' => '年营业收入', 'field' => 'revenue', 'width' => '20'],
                    ['title' => '营业面积', 'field' => 'area', 'width' => '20'],
                    ['title' => '员工总数', 'field' => 'personnel', 'width' => '20'],
                    ['title' => '专职安全管理人数', 'field' => 'personnel_full', 'width' => '20'],
                    ['title' => '兼职安全管理人数', 'field' => 'personnel_part', 'width' => '20'],
                    ['title' => '特种作业人数', 'field' => 'personnel_special', 'width' => '20'],
                    ['title' => '所属集团公司', 'field' => 'group_name', 'width' => '20'],
                    ['title' => '经营范围', 'field' => 'business', 'width' => '20'],
                    ['title' => '是否粉尘涉爆', 'field' => 'is_dust_explosion', 'width' => '20'],
                    ['title' => '所属行业', 'field' => 'sector', 'width' => '20'],
                    ['title' => '是否涉氨制冷', 'field' => 'is_ammonia_cold', 'width' => '20'],
                    ['title' => '液氨的用途', 'field' => 'ammonia_use', 'width' => '20'],
                    ['title' => '液氨使用量', 'field' => 'ammonia_usage', 'width' => '20'],
                    ['title' => '液氨储存方式', 'field' => 'ammonia_storage_method', 'width' => '20'],
                    ['title' => '液氨储存量', 'field' => 'ammonia_storage_capacity', 'width' => '20'],
                    ['title' => '气体泄露报警装置数', 'field' => 'gas_alarm_number', 'width' => '20'],
                    ['title' => '是否涉高温熔融金属', 'field' => 'is_hot_melting', 'width' => '20'],
                    ['title' => '熔炼炉', 'field' => 'melting_furnace', 'width' => '20'],
                    ['title' => '涉轻工行业有限空间', 'field' => 'is_light_industry', 'width' => '20'],
                    ['title' => '有限空间类型', 'field' => 'limited_space_type', 'width' => '20'],
                ];
                ExcelModel::exportExcel($title, $res, '企业信息申请导出', true);
            }
            $res = $res->paginate($limit)->each(function ($item, $key) use($userComanys) {
                $item['company_id'] = $userComanys[$item['user_id']] ?? 0;
                $item = CompanyModel::codeToText($item);
                return $item;
            });
            result($res);
        } else {
            View::assign('title', '首页');
            return view();
        }
    }

    public function companyInfo($id = 0)
    {
        \app\model\CompanyModel::getCompanyInfo($id);
        exit();
    }

    public function getReview($element_id = 0)
    {
        if (request()->isAjax()) {
            $request = $this->request->post();
            \app\model\CompanyModel::getAjaxReview($element_id, $request);
            exit();

        } else {
            View::assign('element_id', $element_id);
            View::assign('title', '首页');
            return view();
        }
    }


    public function companyApplyInfo($id = 0)
    {
        $re = Db::table('top_company_info_apply')->where(['id' => $id,'area_id'=>$_SESSION['area']['id'], ['dept_id','in',$_SESSION['area']['manager']]])->find();

        if (!empty($re)) {
            $re = CompanyModel::codeToText($re);
            //刷新认证中的信息
            //获取粉尘,获取是否涉高温熔融金属,获取是否涉有限空间 - 认证中的信息 - 公司参数待审核表
            \app\model\CompanyModel::getCompanyAuthInfo($re,'top_company_param_apply');
        } else {
            result('', 1002, '申请信息不存在');
        }
        result($re);
    }

    public function companyApplyCheck($id = 0,$status=0,$reason='')
    {
        $re = Db::table('top_company_info_apply')->where(['id' => $id,'area_id'=>$_SESSION['area']['id'], ['dept_id','in',$_SESSION['area']['manager']]])->find();
        if (!empty($re)) {
            $updateData = [
                'status'=>$status,
                'reason'=>$reason,
                'check_user_id'=>$_SESSION['area']['user_id'],
                'check_user_name'=>$_SESSION['area']['user_name'],
                'check_time'=>date("Y-m-d H:i:s"),
            ];
            Db::table('top_company_info_apply')->where(['id'=>$re['id']])->update($updateData);
            if($status==7){
                CompanyModel::companyUpdate($re);
            }
        } else {
            result('', 1002, '申请信息不存在');
        }
        result($re);
    }


    public function info($id = 0) {
        $company = Db::table('top_company_info')->where(['id' => $id])->find();
        $element_id = Db::table('top_company_review_element')->where(['main_id'=>$company['review_id']])->field('id')->order('sort')->find()['id'];
        View::assign('title','首页');
        View::assign('id',$id);
        View::assign('title',$company['name']);
        View::assign('element_id',$element_id);
        return view();
    }
    //严重数据的完整性校验
    public function verifyIntegrity($id) {
        $re = Db::table('top_company_user')->where(['id'=>$id])->find();
        $hashSource = '';
        foreach($this->smFields as $_feild){
            if(!empty($re[$_feild])){
                 $hashSource .= $re[$_feild];
            }
        }
        $out =(int) hsmVerify($hashSource,$re['check_hash']);
        result($out);
    }    


    //加密数据的机密性和完整性
    public function getEncryptData($data) {
        if(config("app.is_hsm_encrypt")==false){
            return $data;
        }
        //统一加密
        $hashSource = "";
        foreach($this->smFields as $_feild){
            if(!empty($data[$_feild])){
                $data[$_feild] = hsmCacheEncrypt($data[$_feild]);
                $hashSource .=$data[$_feild];
            }
        }
        
        $data['check_status'] =1;
        $data['check_hash'] = hsmHmac($hashSource);
        return $data; 
    }

    //解密数据
    public function getDecryptData($data) {
        //使用类属性中定义的加密字段
        foreach($this->smFields as $_feild){
            if(!empty($data[$_feild])){
                $data[$_feild] = hsmCacheDecrypt($data[$_feild]);
            }
        }
        return $data;
    }
}
