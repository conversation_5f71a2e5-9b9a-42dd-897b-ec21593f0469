<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{$title}</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">
        <el-breadcrumb separator-class="el-icon-arrow-right">
            <el-breadcrumb-item><header>{$title}</header></el-breadcrumb-item>
        </el-breadcrumb>
        <el-form :inline="true" :model="searchFrom" class="form-inline" @submit.native.prevent>
            <el-form-item>
                <el-input v-model="filterText" size="mini" placeholder="关键字"></el-input>
            </el-form-item>
            <el-form-item>
                <!--                <el-button type="success" size="mini" @click="add">添加</el-button>-->
                <el-button :loading="loading" type="primary"  size="mini" @click="export1">导出</el-button>
                <el-button :loading="loading" type="primary"  size="mini" @click="import1">导入</el-button>
                <el-button :loading="loading" type="success"  size="mini" @click="add">添加</el-button>
            </el-form-item>
        </el-form>
        <el-tree
                class="filter-tree"
                :data="data"
                :props="defaultProps"
                default-expand-all
                :expand-on-click-node="false"
                :filter-node-method="filterNode"
                ref="tree">
                    <span class="custom-tree-node" slot-scope="{ node, data }">
                         <span>({{ data.code }})</span>
                         <span>{{ data.name }} </span>
                         <span style="margin-left: 20px">
                             <el-tooltip :content="data.enabled=='1'?'启用':'禁用'" placement="top">
                             <el-switch
                                     @change="() => upEnabled(data)"
                                     v-model="data.enabled"
                                     active-color="#13ce66"
                                     inactive-color="#ff4949"
                                     :active-value="1"
                                     :inactive-value="0">
                            </el-switch>
                             </el-tooltip>
                             <el-button type="text" size="mini" @click.stop="add(data)">添加</el-button>
                            <el-button type="text" style="color: #cf9236" size="mini" @click.stop="edit(data)">编辑</el-button>
                            <el-button type="text" style="color: red" size="mini" @click.stop="del(data)">删除</el-button>
                         </span>
                    </span>
        </el-tree>
        <!--新增弹窗-->
        <el-dialog :title="title" width="50%" :visible.sync="dialogFormVisible" :close-on-click-modal="false">
            <el-form label-position="top" :model="form" :rules="formRules" ref="form" class="oa-form">
                <el-row :gutter="20">
                    <!--<el-col :span="20">
                        <el-form-item label="上级区域">
                            <el-input disabled v-model="form.pname" ></el-input>
                        </el-form-item>
                    </el-col>-->
                    <el-col :span="20">
                        <el-form-item label="类型编码">
                            <el-input  v-model="form.code" ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="20">
                        <el-form-item label="类型名称">
                            <el-input  v-model="form.name" ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="20">
                        <el-form-item label="是否启用">
                            <el-switch
                                    v-model="form.enabled"
                                    active-color="#13ce66"
                                    inactive-color="#ff4949"
                                    :active-value="1"
                                    :inactive-value="0">
                            </el-switch>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogFormVisible = false" size="small">取 消</el-button>
                <el-button type="primary" @click="addSubmit" size="small">保 存</el-button>
            </div>
        </el-dialog>
        <import1 ref="import1" @ok="getData()"></import1>
    </div>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.all.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                title:'新增',
                searchFrom: {
                    search: '',
                },
                filterText:'',
                defaultProps: {
                    label: 'name',
                    children: 'children'
                },
                data: [
                    {
                        id: 0,
                        name: '全部分类',
                        children: []
                    },
                ],
                page: 1,
                pageSize: 20,
                total: 0,
                deptData: [],
                userData: [],
                dialogFormVisible: false,
                visible: false,
                loading: true,
                form:{},
                formRules: {
                    code: [{required: true, message: "请输入产业园区编码", trigger: "blur"}],
                    name: [{required: true, message: "请输入产业园区名称", trigger: "blur"}],
                },
                ueObj:null,
                height: document.documentElement.clientHeight - 210,
            };
        },
        components: {
            'import1': 'url:/general/toppingsoft/public/vue/import.vue',
        },
        watch: {
            filterText(val) {
                this.$refs.tree.filter(val);
            }
        },
        methods: {
            filterNode(value, data) {
                if (!value) return true;
                return data.name.indexOf(value) !== -1;
            },
            add(row){
                this.title = '新增';
                this.form = {
                    code:'',
                    name:'',
                    enabled:1,
                    pid:row.id,
                    pname:row.name
                };
                this.dialogFormVisible = true;
            },
            edit(row){
                this.title = '编辑';
                this.form = JSON.parse(JSON.stringify(row));
                this.dialogFormVisible = true;
            },
            upEnabled(row){
                var _this = this;
                _this.loading = true;
                var param = {};
                param.id = row.id;
                param.enabled = row.enabled;
                axios.post('industrialParkEnabled',param).then(function (res) {
                    _this.loading = false;
                    if( res.data.code == 0 ) {
                        /*_this.$message({
                            message: res.data.msg,
                            type: "success"
                        });*/
                    }else{
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        row.enabled = row.enabled==1?0:1;
                    }
                }).catch(function (error) {
                    _this.loading = false;
                    row.enabled = row.enabled==1?0:1;
                    console.log(error);
                });
            },
            del(row){
                var _this = this;
                _this.$confirm("确认删除该类型及其下所有类型，删除后不可恢复？", "提示", {}).then(() => {
                    _this.loading = true;
                    var param = {};
                    param.id = row.id;
                    axios.post('industrialParkDel',param).then(function (res) {
                        _this.loading = false;
                        if( res.data.code == 0 ) {
                            _this.$message({
                                message: res.data.msg,
                                type: "success"
                            });
                            _this.dialogFormVisible = false;
                            _this.getData();
                        }else{
                            _this.$message({
                                message: res.data.msg,
                                type: "error"
                            });
                        }
                    }).catch(function (error) {
                        console.log(error);
                    });
                });
            },
            //数据加载
            getData() {
                var _this = this;
                var param = _this.searchFrom;
                param._ajax = 1;
                axios.post('',param).then(function (res) {
                    if( res.data.code == 0 ){
                        _this.data = res.data.data;
                    }else{
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                    }
                    _this.loading = false;
                }).catch(function (error) {
                    console.log(error);
                    _this.loading = false;
                });
            },
            addSubmit: function () {
                var _this = this;
                this.$refs.form.validate(valid => {
                    if (valid) {
                        _this.addLoading = true;
                        var param = _this.form;
                        axios.post('industrialParkSave',param).then(function (res) {
                            _this.addLoading = false;
                            if( res.data.code == 0 ) {
                                _this.$message({
                                    message: res.data.msg,
                                    type: "success"
                                });
                                _this.dialogFormVisible = false;
                                _this.getData();
                            }else{
                                _this.$message({
                                    message: res.data.msg,
                                    type: "error"
                                });
                            }
                        }).catch(function (error) {
                            console.log(error);
                        });
                    }
                });
            },
            import1: function () {
                this.$refs.import1.templateUrl = 'industrialParkImportTemplate';
                this.$refs.import1.submitUrl = 'industrialParkImport';
                this.$refs.import1.title = '{$title}导入';
                this.$refs.import1.open();
            },
            export1: function () {
                let where = "";
                let type = '';
                //获得where
                where = 'type='+type;
                for (let index in this.searchFrom) {
                    if (this.searchFrom[index] !== '' && this.searchFrom[index] !== [] && this.searchFrom[index] !== {}) {
                        let str = "";
                        if(index=='type'){
                            for(let i in this.searchFrom.type){
                                type += this.searchFrom.type[i]+',';
                            }
                            str += 'type='+type;
                        }else{
                            str += index+'='+this.searchFrom[index];
                        }
                        where += "&" + str;
                    }
                }
                let url = "?excel=1&" + where;
                //window.open(url);
                location.href = url;
            },
        },
        mounted() {
            //获取列表
            this.getData();
        }
    })
</script>


</body>
</html>