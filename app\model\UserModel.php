<?php
namespace app\model;

use think\facade\Db;
use think\Model;

class UserModel extends Model
{

    /**
     * 获取通达部门信息
     * @param $dept_id 部门id 0返回所有部门
     * @param $is_user  是否查询部门下会员
     * @param $data_type  会员数据类型  now 现有人员  old 曾管人员  all 所有人员
     * @return array
     */
    public static function getDepts($dept_id = 0, $is_user = 0, $data_type = 'now')
    {
        if (empty($dept_id)) {
            $depts   = Db::table('department')->select()->toArray();
            $results = get_tree_children($depts, 'children', 'DEPT_ID', 'DEPT_PARENT');
            return $is_user == 1 ? UserModel::get_depts_users($results, $data_type) : $results;
        } else {
            $dept = Db::table('department')->where(['DEPT_ID' => $dept_id])->find();
            return $is_user == 1 ? UserModel::getDeptUser($dept['DEPT_ID'], $data_type) : $dept;
        }
    }

    //获取部门下人员信息
    public static function getDeptUser($dept_id, $data_type = 'now')
    {
        $where = ['u.dept_id' => $dept_id];
        $users = UserModel::getUserInfoList($where, 0, $data_type);
        return $users;
    }

    //获取用户详细信息
    public static function getUserInfo($user_id, $family = true)
    {
        $field = 'a.byname,a.DEPT_ID dept_id,d.DEPT_NAME dept_name,user_priv,user_priv_name,user_name,mobil_no,tel_no_dept,birthday,a.avatar,a.user_id';
        $data  = Db::table('td_user')->alias('a')
            ->leftJoin('department d', 'a.DEPT_ID = d2.dept_id')
            ->field($field)
            ->where(['a.user_id' => $user_id])->find();
        if (empty($data)) {
            return $data;
        }
        return $data;
    }

    //获取用户姓名
    public static function getUserName($user_id)
    {
        $user = Db::table('td_user')->where(['user_id' => $user_id])->field('USER_NAME')->find();
        return $user['USER_NAME'];
    }

    //获取用户签名
    public static function getUserSign($user_id)
    {
        $user = Db::table('top_user_info')->where(['user_id' => $user_id])->field('sign_img')->find();
        return empty($user) ? '' : $user['sign_img'];
    }

    //获取用户姓名+部门
    public static function getUserDept($user_id)
    {
        $user = Db::table('td_user')->alias('a')->leftJoin('department d', 'a.dept_id = d.dept_id')->where(['a.user_id' => $user_id])->field('a.user_id,a.user_priv,a.user_name,d.dept_id,d.dept_name')->find();
        if(in_array($user['dept_id'],[52,6,7,43,29,56,65,61,71,69,31,30,72,74,70])){
            $user['branch'] = '成都';
        }else if(in_array($user['dept_id'],[8,11,19,46,49,68])){
            $user['branch'] = '西安';
        }else if(in_array($user['dept_id'],[26,63,27,66,28])){
            $user['branch'] = '贵阳';
        }else if(in_array($user['dept_id'],[17,64,21,23])){
            $user['branch'] = '昆明';
        }
        return $user;
    }

    public static function get_depts_users(array $depts, $data_type = 'now')
    {
        foreach ($depts as $key => $value) {
            $depts[$key]['users']    = UserModel::getDeptUser($value['DEPT_ID'], $data_type);
            $depts[$key]['children'] = empty($value['children']) ? [] : UserModel::get_depts_users($value['children'], $data_type);
        }
        return $depts;
    }

    /**
     * 获取通达用户的所有部门和角色信息
     * @param $user_id
     * @return array
     */
    public static function getTdUserDepts($user_id)
    {
        $userInfo = Db::table("td_user")->where("user_id", $user_id)->find();
        //合并多个部门
        $depts[] = intval($userInfo['DEPT_ID']);
        // 获取其他部门
        $where = [
            'uid'  => $userInfo['UID'],
            'type' => 1,
        ];
        $list = Db::table('user_other')->where($where)->select();
        if (!empty($list)) {
            $list = $list->toArray();
            foreach ($list as $key => $value) {
                if (!in_array($value['other_id'], $depts)) {
                    $depts[] = $value['other_id'];
                }
            }
        }
        //合并多个角色
        $roles[] = intval($userInfo['USER_PRIV']);
        // 获取其他部门
        $where = [
            'uid'  => $userInfo['UID'],
            'type' => 2,
        ];
        $list = Db::table('user_other')->where($where)->select();
        if (!empty($list)) {
            $list = $list->toArray();
            foreach ($list as $key => $value) {
                if (!in_array($value['other_id'], $roles)) {
                    $roles[] = $value['other_id'];
                }
            }
        }
        return array($depts, $roles);
    }

    // 根据真实姓名获取用户唯一编码
    public static function getUserIdByName($user_name)
    {
        $return = '';
        $user   = Db::table('td_user')->where(['USER_NAME' => $user_name])->field('USER_ID')->find();
        if (!empty($user)) {
            $return = $user['USER_ID'];
        }
        return $return;
    }

    // 根据真实姓名获取用户唯一编码
    public static function getUserIdByNames($user_names, $flag = '', $is_array = true)
    {
        $user_ids = [];
        if (!empty($user_names)) {
            $user_names = explode($flag, $user_names);
            if (!empty($user_names)) {
                $user_list = Db::table('td_user')->where(['USER_NAME' => $user_names])->field('USER_ID')->select();
                if (count($user_list) > 0) {
                    foreach ($user_list as $key => $value) {
                        $user_ids[] = $value['USER_ID'];
                    }
                }
            }
        }
        if ($is_array) {
            $return = $user_ids;
        } else {
            $return = implode(',', $user_ids);
        }
        return $return;
    }

    // 判断用户是否有某个角色
    public static function checkUserRole($user_id, $user_role_ids)
    {
        $flag     = false;
        $temp     = self::getTdUserDepts($user_id);
        $role_ids = $temp[1];
        foreach ($user_role_ids as $key => $value) {
            if (in_array($value, $role_ids)) {
                $flag = true;
            }
        }
        return $flag;
    }

}
