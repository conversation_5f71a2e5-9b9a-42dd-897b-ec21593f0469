<?php
declare (strict_types=1);

namespace app\model;

use think\Model;
use think\facade\Db;
use app\admin\model\TopAreaModel;
use app\admin\model\CompanyModel;

/**
 * @mixin \think\Model
 */
class TopGradingApproval extends Model
{
	//


	private $current_prcs_id = 2;
	private $_prefix = 'a';

	public function _setOption($param, $value)
	{
		if (property_exists(new self(), $param)) {
			$this->{$param} = $value;
		}
	}

	public function grading()
	{
		return $this->belongsTo(TopGrading::class, 'grading_id', 'id');
	}




	public function getList($where, $page = 1, $limit = 10)
	{
		$whereArr[] = ['params', '=', $this->_prefix . ':' . $where['id']];
		$whereArr[] = ['prcs_id', '=', $this->current_prcs_id];
		$whereG[] = ['status', '=', 1];
		if (isset($where['name']) && $where['name'] != '') $whereG[] = ['company_name', 'like', '%'.$where['name'].'%'];
		$gModel = new TopGrading();
		$data = $this->where($whereArr)->hasWhere(['grading' => function($query) use ($whereG){
			$query->where($whereG);
		}])->paginate($limit)->each(function ($item, $index) use ($gModel) {
			$item = $this->getInfo($item, $gModel);
			return $item;
		});
		return $data;
	}

	public function getInfo($item, &$model)
	{
		$grading = $item->grading ? $item->grading->toArray() : [];
		$item->grading = $grading;
		$item->apply_time = $item->grading['apply_time'] ? date('Y-m-d H:i:s', strtotime($item->grading['apply_time'])) : '';
		$item->statusStr = $this->getStatus($item->status);
		return $item;
	}

	public function getStatus($status)
	{
		$str = '';
		switch ($status) {
			case 1:
				$str = '未接收';
				break;
			case 2:
				$str = '已接收';
				break;
			case 5:
				$str = '已驳回';
				break;
			case 7:
				$str = '已审核';
				break;
			default:
				break;
		}
		return $str;
	}


	public function doCheck($model, $data)
	{
		$model->status = $data['check_status'];
		$model->end_user_id = $_SESSION['area']['user_id'];
		$model->end_user_name = $_SESSION['area']['user_name'];
		$model->end_time = date('Y-m-d H:i:s');
		$model->check_content = $data['reason'];
		$model->status_name = $this->getStatus($data['check_status']);
		if (!empty($data['files'])) {
			foreach ($data['files'] as $file) {
				$files[] = $file['code'];
			}
		}
		$model->check_files = implode(',', $files);
		$areaModel = new CompanyModel;
		$cityId = $areaModel->getParentCity($model->grading->company_id);
		Db::startTrans();
		try {
			$model->save();
			if ($data['check_status'] == 7) { // 审批通过
				$prcs = config('global.grading_prcs');
				foreach ($prcs as $key => $prc) {
					if ($key == $model->prcs_id) {
						if ($prc['next_prcs']) { // 还有下一步
							$next_prcs = $prc['next_prcs'];
						}
					}
				}
				if ($next_prcs) {  // 有下一步
					if ($cityId) { // 有对应的市应急局
						$data = [
							'grading_id' => $model->grading_id,
							'prcs_id' => $next_prcs,
							'prcs_name' => $prcs[$next_prcs]['title'],
							'status' => 1,
							'create_user_id' => $_SESSION['area']['user_id'],
							'create_user_name' => $_SESSION['area']['user_name'],
							'create_time' => date('Y-m-d H:i:s'),
							'params' => 'c:' . $cityId,
						];
						$this->save($data);
					} else {
						$model->grading->status = $data['check_status'];
					}
				} else { // 没有下一步
//					$model->grading->status = $data['check_status'];
//					$model->grading->save();
					$data = [
						'grading_id' => $model->grading_id,
						'prcs_id' => 4,
						'prcs_name' => '评审单位评审',
						'status' => 1,
						'create_user_id' => $_SESSION['city']['user_id'],
						'create_user_name' => $_SESSION['city']['user_name'],
						'create_time' => date('Y-m-d H:i:s'),
						'params' => 'e:' . $data['org_id'],
						'remark' => $data['remark'],
					];
					$this->save($data);
				}
			} else { // 审批不通过
				$model->grading->status = $data['check_status'];
			}
			$model->grading->save();
			Db::commit();
			return '数据处理成功';
		} catch (\Exception $e) {
			Db::rollback();
			return '数据处理失败';
		}

	}

	public function receiptTask($model, $mode = 'area')
	{
		if (!$model) return false;
		if ($model->status == 1) {

			$model->receive_user_id = $_SESSION[$mode]['user_id'];
			$model->receive_user_name = $_SESSION[$mode]['user_name'];
			$model->receive_time = date('Y-m-d H:i:s');
			$model->status = 2;
			$model->status_name = '已接收';
			$model->save();
		}
		return true;
	}
}
