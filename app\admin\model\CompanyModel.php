<?php


namespace app\admin\model;


use app\model\FileModel;
use think\Model;
use think\App;
use think\facade\Db;
use app\model\SettingModel;

//权限配置模块
class CompanyModel extends Model
{

    public static function companySave($param,$id=0)
    {
        try {
            $data = [
                'name' => $param['name'],
                'license' => $param['license'],
                'aoc' => $param['aoc'],
                'reg_address' => implode(',',$param['reg_address']),
                'reg_address_info' => $param['reg_address_info'],
                'operate_address' =>implode(',', $param['operate_address']),
                'operate_address_info' => $param['operate_address_info'],
                'region' => implode(',',$param['region']),
                'legal' => $param['legal'],
                'legal_mobile' => $param['legal_mobile'],
                'legal_email' => $param['legal_email'],
                'fax' => $param['fax'],
                'phone' => $param['phone'],
                'postal_code' => $param['postal_code'],
                'industrial_park' => implode(',',$param['industrial_park']),
                'economy_sector' => implode(',',$param['economy_sector']),
                'industry' => $param['industry'][0],
                'specialty' => $param['industry'][count($param['industry'])-1],
                'license_number' => $param['license_number'],
                'license_start' => $param['license_start'],
                'license_end' => $param['license_end'],
                'economy_type' => implode(',',$param['economy_type']),
                'enterprise_size' => $param['enterprise_size'],
                'reg_money' => $param['reg_money'],
                'manager' => $param['manager'],
                'manager_mobile' => $param['manager_mobile'],
                'manager_email' => $param['manager_email'],
                'date' => $param['date'],
                'fixed_asset' => $param['fixed_asset'],
                'revenue' => $param['revenue'],
                'personnel' => $param['personnel'],
                'area' => $param['area'],
                'personnel_full' => $param['personnel_full'],
                'personnel_part' => $param['personnel_part'],
                'personnel_special' => $param['personnel_special'],
                'group_name' => $param['group_name'],
                'status' => $param['status'],
                'business' => $param['business'],

                'business_type' => $param['business_type'],
                'is_dust_explosion' => $param['is_dust_explosion'],
                'is_ammonia_cold' => $param['is_ammonia_cold'],
                'is_hot_melting' => $param['is_hot_melting'],
                'is_light_industry' => $param['is_light_industry'],
                'sector' => $param['sector'],
                'ammonia_use' => $param['ammonia_use'],
                'ammonia_storage_method' => $param['ammonia_storage_method'],
                'limited_space_type' => $param['limited_space_type'],
                'ammonia_usage' => $param['ammonia_usage'],
                'ammonia_storage_capacity' => $param['ammonia_storage_capacity'],
                'gas_alarm_number' => $param['gas_alarm_number'],

                'blast_furnace_number' => $param['blast_furnace_number'],
                'nonferrous_furnace_number' => $param['nonferrous_furnace_number'],
                'ferroalloy_furnace_number' => $param['ferroalloy_furnace_number'],
                'soaring_furnace_number' => $param['soaring_furnace_number'],
            ];
            $area = Db::table('top_area')->where([['pcas','like',"%{$param['region'][2]}%"],['status','=',1],['del_flag','=',0]])->find();
            $city = Db::table('top_city')->where([['pcas','like',"%{$param['region'][1]}%"],['status','=',1],['del_flag','=',0]])->find();
            $dept = Db::table('top_industry')->where([['name','=',$data['specialty']]])->find();
            $data['area_id'] = $area['id'];
            $data['city_id'] = $city['id'];
            $data['dept_id'] = $dept['department'];
            $r = Db::table('top_company_info')->where([['id','<>',$id],['name','=',$data['name']]])->find();
            if($r){
                result('',1003,'企业名称重复');
            }
            $salt = create_nonce_str(4);
            $password = '';
            $user = [
                'username' => $data['manager_mobile'],
                'password' => crypt($password, $salt),
                'email' => $data['manager_email'],
                'mobile' => $data['manager_mobile'],
                'status' => 0,
                'reg_time' => date('Y-m-d H:i:s'),
                'reg_ip' => get_ip(),
                'salt' => $salt,
            ];
            $u = Db::table('top_company_user')->where(['username|mobile'=>$data['manager_mobile']])->find();
            if(empty($u)){
                Db::table('top_company_user')->insert($user);
                $user_id = Db::table('top_company_user')->where($user)->field('id')->order('id desc')->find()['id'];
            }else{
                $user_id = $u['id'];
            }

            FileModel::saveFile($data['license'],'company/'.date('Ym'));
            FileModel::saveFile($data['aoc'],'company/'.date('Ym'));

            if($id)
            {
                $re = Db::table('top_company_info')->where(['id'=>$id])->find();
                if(empty($re)){
                    result('',1003,'数据不存在或已删除');
                }

                //清空旧数据
                Db::table('top_company_param')->where(['company_id'=>$id])->delete();

                //保存参数
                $saveParam = [];
                if( is_array($param['dust_list']) && count($param['dust_list']) )
                {
                    foreach ($param['dust_list'] as $item)
                    {
                        if( !empty($item['param_value']) )
                        {
                            $saveParam[] = [
                                'company_id' => $id,
                                'name' => $item['name'],
                                'param_value' => $item['param_value'],
                                'category' => 'dust'
                            ];
                        }
                    }
                }

                if( is_array($param['hot_list']) && count($param['hot_list']) )
                {
                    foreach ($param['hot_list'] as $item)
                    {
                        if( !empty($item['param_value']) )
                        {
                            $saveParam[] = [
                                'company_id' => $id,
                                'name' => $item['name'],
                                'param_value' => $item['param_value'],
                                'category' => 'hot'
                            ];
                        }
                    }
                }

                if( count($saveParam) )
                {
                    Db::table('top_company_param')->strict(false)->insertAll($saveParam);
                }
                $re = Db::table('top_company_info')->where(['id'=>$id])->update($data);
            }
            else
            {
                $data['user_id'] = $user_id;
                $id = Db::table('top_company_info')->insert($data);
            }
            return $id;
        }catch (\Exception $e){
            dd($e->getMessage());
        }
    }


    public static function caSave($param,$id=0)
    {
        $data = [
            'company_id' => $param['company_id'],
            'standard_id' => $param['standard_id'],
            'level' => $param['level'],
            'standard' => $param['standard'],
            'image' => $param['image'],
            'code' => $param['code'],
            'start' => $param['start'],
            'ends' => $param['end'],
            'create_user_id' => $_SESSION['LOGIN_USER_ID'],
            'create_user_name' => $_SESSION['LOGIN_USER_NAME'],
            'create_time' => date('Y-m-d H:i:s'),
        ];
        $r = Db::table('top_company_ca')->where([['id','<>',$id],['code','=',$data['code']]])->find();
        $r1 = Db::table('top_certificate')->where([['code','=',$data['code']]])->find();
        if($r||$r1){
            result('',1003,'证书编号重复');
        }
        $standard = Db::table('top_standard_name')->where(['id'=>$data['standard_id']])->find();
        if(empty($standard)){
            result('',1003,'请选择评审标准');
        }
        $company = Db::table('top_company_info')->where(['id'=>$data['company_id']])->find();
        if(empty($company)){
            result('',1003,'企业信息不存在，请刷新重试');
        }
        $certificate = [
            'company_id' => $company['id'],
            'company_name' => $company['name'],
            'industry' => $company['industry'],
            'specialty' => $company['specialty'],
            'code' => $data['code'],
            'level' => $data['level'],
            'start' => $data['start'],
            'ends' => $data['ends'],
            'image' => $data['image'],
            'status' => 7,
        ];
//        dd($certificate);
        Db::table('top_certificate')->insertGetId($certificate);
        $com = [
            'standard_id' => $standard['id'],
            'standard_name' => $standard['name'],
            'standard_level' => $data['level'],
            'stand_status' => 1,
            'stand_date' => $data['start'],
            'ca_status' => 1,
            'ca_date' => $data['start'],
        ];
        Db::table('top_company_info')->where(['id'=>$data['company_id']])->update($com);
        SettingModel::setReview($data['company_id']);
        FileModel::saveFile($data['image'],'company/'.date('Ym'));
        FileModel::saveFile($data['standard'],'company/'.date('Ym'));
        if($id){
            $re = Db::table('top_company_ca')->where(['id'=>$id])->find();
            if(empty($re)){
                result('',1003,'数据不存在或已删除');
            }
//            dd($data);
            $re = Db::table('top_company_ca')->where(['id'=>$id])->update($data);
        }else{
            $id = Db::table('top_company_ca')->insertGetId($data);
        }
        return $id;
    }

    //导入
    public static function import($data,$dataHeader){
        $fields = [
            'name' => '企业名称',
            'reg_addressp' => '注册地址（省）',
            'reg_addressc' => '注册地址（市）',
            'reg_addressa' => '注册地址（区）',
            'reg_address_info' => '注册地址',
            'operate_addressp' => '生产经营地点（省）',
            'operate_addressc' => '生产经营地点（市）',
            'operate_addressa' => '生产经营地点（区）',
            'operate_address_info' => '生产经营地点',
            'regionp' => '所属行政区（省）',
            'regionc' => '所属行政区（市）',
            'regiona' => '所属行政区（区）',
            'legal' => '法定代表人',
            'legal_mobile' => '法人联系电话',
            'legal_email' => '法人邮箱',
            'fax' => '企业传真',
            'postal_code' => '邮政编码',
            'economy_sector' => '国民经济行业',
            'industry' => '行业',
            'specialty' => '专业',
            'license_number' => '统一社会信用代码',
            'license_date' => '信用代码有效期',
            'economy_type' => '经济类型',
            'enterprise_size' => '企业规模',
            'reg_money' => '注册资本',
            'manager' => '安全管理联系人',
            'manager_mobile' => '联系电话',
            'manager_email' => '邮箱',
            'date' => '成立日期',
            'fixed_asset' => '固定资产',
            'revenue' => '年营业收入',
            'personnel' => '员工总数',
            'area' => '营业场所面积',
            'personnel_full' => '专职安全管理人数',
            'personnel_part' => '兼职安全管理人数',
            'personnel_special' => '特种作业人数',
            'group_name' => '所属集团名称',
            'status' => '状态',
            'business' => '经营范围',
        ];
        foreach ($dataHeader as $k=>$v){
            foreach ($fields as $k1=>$v1){
                if($v1==$v){
                    $tit[$k] = $k1;
                }
            }
        }
        foreach ($data as $k=>$v){
            $tmp = [];
            foreach($v as $k1=>$v1){
                if(in_array($tit[$k1],['date'])) {
                    $v1 = str_replace('.', '-', $v1);
                    if (strlen($v1) > 6) {
                        $tmp[$tit[$k1]] = date('Y-m-d', strtotime($v1));
                    } else {
                        $tmp[$tit[$k1]] = gmdate('Y-m-d', \PHPExcel_Shared_Date::ExcelToPHP($v1));
                    }
                }else if(in_array($tit[$k1],['economy_sector','economy_type'])){
                    $tmp[$tit[$k1]] = str_replace('/',',',$v1);
                }else{
                    $tmp[$tit[$k1]] = str_replace(' ','',$v1);
                }
            }
            $dataList[] = $tmp;
        }
        foreach ($dataList as $v) {
            if(!empty($v['name'])){
                $datauser[] = $v;
            }
        }
        foreach ($datauser as $k=>$v){
            $re = self::importSave($v);
            if($re['code']==0) {
                $result['success'][] = ['row' => $k + 2, 'name' => $v['name'], 'msg' => '保存成功'];
            }else if($re['code']==-1){
                $result['repeat'][] = ['row' => $k + 2, 'name' => $v['name'], 'msg' => '重复'];
            }else{
                $result['fail'][] = ['row'=>$k+2,'name'=>$v['name'],'msg'=>$re['msg']];
            }
        }
        return $result;
    }


    public static function importSave($param){
        $data = [
            'name' => $param['name'],
            'reg_address_info' => $param['reg_address_info'],
            'operate_address_info' => $param['operate_address_info'],
            'legal' => $param['legal'],
            'legal_mobile' => $param['legal_mobile'],
            'legal_email' => $param['legal_email'],
            'fax' => $param['fax'],
            'postal_code' => $param['postal_code'],
            'economy_sector' => $param['economy_sector'],
            'industry' => $param['industry'],
            'specialty' => $param['specialty'],
            'license_number' => $param['license_number'],
            'economy_type' => $param['economy_type'],
            'enterprise_size' => $param['enterprise_size'],
            'reg_money' => $param['reg_money'],
            'manager' => $param['manager'],
            'manager_mobile' => $param['manager_mobile'],
            'manager_email' => $param['manager_email'],
            'date' => $param['date'],
            'fixed_asset' => $param['fixed_asset'],
            'revenue' => $param['revenue'],
            'personnel' => $param['personnel'],
            'area' => $param['area'],
            'personnel_full' => $param['personnel_full'],
            'personnel_part' => $param['personnel_part'],
            'personnel_special' => $param['personnel_special'],
            'group_name' => $param['group_name'],
            'business' => $param['business'],
            'business_type' => $param['business_type'],
        ];
        $a = fieldToNum('pca_code',$param['regiona']);
        $c = fieldToNum('pca_code',$param['regionc']);
        $area = Db::table('top_area')->where([['pcas','like',"%{$a}%"],['status','=',1],['del_flag','=',0]])->find();
        $city = Db::table('top_city')->where([['pcas','like',"%{$c}%"],['status','=',1],['del_flag','=',0]])->find();
        $dept = Db::table('top_industry')->where([['name','=',$data['specialty']]])->find();
        $data['area_id'] = $area['id'];
        $data['city_id'] = $city['id'];
        $data['dept_id'] = $dept['department'];
        $r = Db::table('top_company_info')->where([['name','=',$data['name']]])->find();
        if($r){
            return ['','code'=>-1,'msg'=>'重复'];
        }
        $salt = create_nonce_str(4);
        $password = '';
        $user = [
            'username' => $data['manager_mobile'],
            'password' => crypt($password, $salt),
            'email' => $data['manager_email'],
            'mobile' => $data['manager_mobile'],
            'status' => 0,
            'reg_time' => date('Y-m-d H:i:s'),
            'reg_ip' => get_ip(),
            'salt' => $salt,
        ];
        $u = Db::table('top_company_user')->where(['username|mobile'=>$data['manager_mobile']])->find();
        if(empty($u)){
            Db::table('top_company_user')->insertGetId($user);
            $user_id = Db::table('top_company_user')->where($user)->field('id')->order('id desc')->find()['id'];
        }else{
            $user_id = $u['id'];
        }
        $license_date = explode('至',$param['license_date']);
        $data['license_start'] = $license_date[0];
        $data['status'] = $param['status']=='正常'||$param['status']=='1'?'1':'0';
        $data['license_end'] = empty($license_date[1])?'2099-12-30':$license_date[1];
        $data['reg_address'] = fieldToNum('pca_code',$param['reg_addressp']).','.fieldToNum('pca_code',$param['reg_addressc']).','.fieldToNum('pca_code',$param['reg_addressa']);
        $data['operate_address'] = fieldToNum('pca_code',$param['operate_addressp']).','.fieldToNum('pca_code',$param['operate_addressc']).','.fieldToNum('pca_code',$param['operate_addressa']);
        $data['region'] = fieldToNum('pca_code',$param['regionp']).','.fieldToNum('pca_code',$param['regionc']).','.fieldToNum('pca_code',$param['regiona']);
        Db::startTrans();
        try {
            $data['user_id'] = $user_id;
//            dd($data);
            $id = Db::table('top_company_info')->insertGetId($data);
            Db::commit();
            return ['data'=>['id'=>$id],'code'=>0,'msg'=>'保存成功'];
        } catch (\Exception $e) {
            Db::rollback();
            return ['data'=>['id'=>$id],'code'=>1002,'msg'=>$e->getMessage()];
            return $e->getMessage();
        }
    }


    //参数格式化
    public static function codeToText($info){
        if(empty($info)){
            return $info;
        }
        $pcas = SettingModel::getPcasAll();
        $info['reg_address'] = empty($info['reg_address'])?[]:explode(',',$info['reg_address']);
        $info['mb_reg_address'] = '';
        foreach ($info['reg_address'] as $k=>$v){
            $info['mb_reg_address'] .= $pcas[$v];
        }
        $info['mb_reg_address'] .= $info['reg_address_info'];
        $info['operate_address'] = empty($info['operate_address'])?[]:explode(',',$info['operate_address']);
        $info['mb_operate_address'] = '';
        foreach ($info['operate_address'] as $k=>$v){
            $info['mb_operate_address'] .= $pcas[$v];
        }
        $info['mb_operate_address'] .= $info['operate_address_info'];
        $info['region'] = empty($info['region'])?[]:explode(',',$info['region']);
        $info['mb_region'] = '';
        foreach ($info['region'] as $k=>$v){
            $info['mb_region'] .= $pcas[$v];
        }
        $info['economy_sector'] = explode(',',$info['economy_sector']);
        $info['mb_economy_sector'] = implode('/',$info['economy_sector']);
        $info['economy_type'] = explode(',',$info['economy_type']);
        $info['mb_economy_type'] = implode('/',$info['economy_type']);
        $info['license_date'] = $info['license_start'].'至'.$info['license_end'];
        $info['licenseUrl'] = empty($info['license'])?'':FileModel::getFile(0,$info['license']);
        $info['aocUrl'] = empty($info['aoc'])?'':FileModel::getFile(0,$info['aoc']);
        foreach ($info as $k=>$v){
            $info[$k] = $v==='0000-00-00'?'':$v;
            if(is_int($v)){
                $info[$k] = (string)$v;
            }
            if($v===null){
                $info[$k] = '';
            }
        }
        return $info;
    }

    /**
     * 获取表头字段
     * @return array
     */
    public function getTitle()
    {
        $title = [
            ['title' => '名称', 'field' => 'name', 'width' => '30', 'type' => 'string'],
            ['title' => '状态', 'field' => 'statusStr', 'width' => '30', 'type' => 'string'],
            ['title' => '联系人', 'field' => 'contacts', 'width' => '30', 'type' => 'string'],
            ['title' => '联系电话', 'field' => 'tel', 'width' => '30', 'type' => 'string'],
            ['title' => '地址', 'field' => 'address', 'width' => '60', 'type' => 'string'],
            ['title' => '所属行政区', 'field' => 'pcas_name', 'width' => '100', 'type' => 'string'],
        ];
        return $title;
    }

}