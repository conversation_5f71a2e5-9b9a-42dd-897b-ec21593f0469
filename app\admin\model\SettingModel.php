<?php

namespace app\admin\model;

use think\Model;
use think\App;
use think\facade\Db;

//权限配置模块
class SettingModel extends Model
{

    /**
     * 判断是否是管理员
     * @return bool
     */
    public static function del($table,$id=0)
    {
        $res = Db::table($table)->where(['pid'=>$id])->select()->toArray();
        foreach ($res as $v){
            self::del($table,$v['id']);
        }
        Db::table($table)->where(['id'=>$id])->delete();
        return true;
    }

}