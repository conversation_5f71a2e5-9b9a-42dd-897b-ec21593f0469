<?php

// 应用公共文件
use think\facade\Db;
use think\Response;
use think\facade\Request;
use think\exception\HttpResponseException;
use think\facade\Cache;

/**
 * 获取专家系统配置
 * @return array
 **/
function getSecsConfig() {
    //专家接口对应的ID
    $id = config('app.secs_expert_id');
    $secsConfig = Db::table('top_login_app')->where(['id'=>$id])->find();
    return $secsConfig;
}


if (!function_exists('preprocess')) {

    /**
     * 文本预处理
     * @param string $text 文本信息
     * @return false|int
     */

    function preprocess($text) {
        $text = preg_replace('/[^\w\s]/u', '', $text); // 去除标点
        $text = mb_strtolower($text, 'UTF-8'); // 转换为小写
        return $text;
    }

}
if (!function_exists('get_oa_attach')) {

    /**
     * 移动文件
     * @param string $filepath 文件路径
     * @param string $folder  移动后路径
     * @param string $filename  文件名（带后缀）
     * @param boolean $is_reserve  是否保留原文件
     * @return false|int
     */
    function get_oa_attach() {
        $POS_PATH = Db::table('attachment_position')->where(['IS_ACTIVE'=>'1'])->find();
        return empty($POS_PATH)?config('filesystem.disks.oa.root'):$POS_PATH['POS_PATH'];
    }

}

if (!function_exists('sso_curl_post')) {

    function sso_curl_post($url, $data = array()) {
        $http = $_SERVER['REQUEST_SCHEME'];
        $host = $_SERVER['HTTP_HOST'];
        $port = $_SERVER['SERVER_PORT'];
        $url = $http."://" . $host . ":" . $port . "/general/toppingsoft/" . $url;
        $cookie = "PHPSESSID=" . session_id();
        session_write_close();
        $header = array(
            'Accept: application/json',
        );
        //初始化
        $curl = curl_init();
        //设置抓取的url
        curl_setopt($curl, CURLOPT_URL, $url);
        //设置头文件的信息作为数据流输出
        curl_setopt($curl, CURLOPT_HEADER, 0);
        //设置获取的信息以文件流的形式返回，而不是直接输出。
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        // 超时设置
        curl_setopt($curl, CURLOPT_TIMEOUT, 2);
        // 超时设置，以毫秒为单位
        // curl_setopt($curl, CURLOPT_TIMEOUT_MS, 500);
        // 设置请求头
        curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        //设置post方式提交
        curl_setopt($curl, CURLOPT_POST, 0);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        curl_setopt($curl, CURLOPT_COOKIE, $cookie);
        //执行命令
        $data = curl_exec($curl);
        //dd($url);
        // print_r($data);die;
        // 显示错误信息
        curl_close($curl);
        if (curl_error($curl)) {
            return "ERROR";
        } else {
            // 打印返回的内容
            return $data;
        }
    }

}

if (!function_exists('move_file')) {

    /**
     * 移动文件
     * @param string $filepath 文件路径
     * @param string $folder  移动后路径
     * @param string $filename  文件名（带后缀）
     * @param boolean $is_reserve  是否保留原文件
     * @return false|int
     */
    function move_file($filepath,$folder='',$filename = '',$is_reserve = false) {
        $filepath = config('filesystem.disks.oa.root').'/'.$filepath;
        if (file_exists($filepath)){
            $fileinfo = pathinfo($filepath);
            $ext = $fileinfo['extension'];
            $filename = $filename?$filename.'.'.$ext:basename($filepath).'.'.$ext;
            $new_path = $folder?$folder:'tmp/'.date('Ymd');
            if(!is_dir($new_path)){
                $p = explode('/',$new_path);
                $addpath = get_oa_attach();
                foreach ($p as $v){
                    $addpath .= '/'.$v;
                    mkdir($addpath);
                    chmod($addpath,0777);
                }
            }
            $re = copy($filepath,get_oa_attach().'/'.$new_path.'/'.$filename);
            if($re){
                if($is_reserve===false){
                    unlink($filepath);
                }
                return $new_path.'/'.$filename;
            }else{
                return false;
            }
        }else{
            return false;
        }
    }

}

if (!function_exists('get_time')) {

    /**
     * 获取星期几
     * @param int $length
     * @return string
     */
    function get_time($time) {
        if(date('Ymd')==date('Ymd',$time)){
            return date('H:i',$time);
        }else if(date('Ymd',strtotime('-1day'))==date('Ymd',$time)){
            return '昨天';
        }else if(date('Ymd',strtotime('-2day'))==date('Ymd',$time)){
            return '前天';
        }else{
            return date('y/n/j',$time);
        }
    }

}

if (!function_exists('get_week')) {

    /**
     * 获取星期几
     * @param int $length
     * @return string
     */
    function get_week($time) {
        $chars = ['日','一','二','三','四','五','六'];
        return $chars[date('w',$time)];
    }

}

if (!function_exists('create_uuid')) {

    /**
     * 生成唯一UUID
     * @param int $length
     * @return string
     */
    function create_uuid($prefix="") {
        $chars = strtoupper(md5(uniqid(mt_rand(),true)));
        $uuid = substr($chars,0,8).'-'
            .substr($chars,8,4).'-'
            .substr($chars,12,4).'-'
            .substr($chars,16,4).'-'
            .substr($chars,20,12);
        return $prefix.$uuid;
    }

}

if (!function_exists('create_runid')) {

    /**
     * 生成唯一runid
     * @param int $length
     * @return string
     */
    function create_runid($first='',$length=6) {
        $chars = "0123456789";
        $str = $first.date('YmdHis');
        for ($i = 0; $i < $length; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }
        return $str;
    }

}

if (!function_exists('create_nonce_str')) {

    /**
     * 生成随机数
     * @param int $length
     * @return string
     */
    function create_nonce_str(int $length = 16,$chars='abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789') {
        $str = "";
        for ($i = 0; $i < $length; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }
        return $str;
    }

}

if (!function_exists('get_ip')) {

    /**
     * 获取用户ip
     * @param int $length
     * @return string
     */
    function get_ip() {
        $forwarded = request()->header("x-forwarded-for");
        if ($forwarded) {
            $ip = explode(',', $forwarded)[0];
        } else {
            $ip = request()->ip();
        }
        return $ip;
    }

}

if (!function_exists('result')) {
    /**
     * 返回封装后的 API 数据到客户端
     * @access protected
     * @param mixed  $data   要返回的数据
     * @param int    $code   返回的 code
     * @param mixed  $msg    提示信息
     * @param string $type   返回数据格式
     * @param array  $header 发送的 Header 信息
     * @return void
     * @throws HttpResponseException
     */
    function result($data = '', $code = 0, $msg = '成功', $type = 'json', array $header = []) {
        $result = [
            'code' => $code,
            'msg' => $msg,
            'time' => Request::instance()->server('REQUEST_TIME'),
            'data' => $data,
            'type' => $code==0?'success':'error',
        ];
        exit(json_encode($result));
//        $type = $type ?: 'json';
//        $response = Response::create($result, $type)->header($header);
//        throw new HttpResponseException($response);
    }

}

if (!function_exists('get_tree_children')) {

    /**
     * tree 子菜单
     * @param array $data 数据
     * @param string $childrenname 子数据名
     * @param string $keyName 数据key名
     * @param string $pidName 数据上级key名
     * @return array
     */
    function get_tree_children(array $data, string $childrenname = 'children', string $keyName = 'id', string $pidName = 'pid') {
        $list = array();
        foreach ($data as $value) {
            $list[$value[$keyName]] = $value;
        }
        $tree = array(); //格式化好的树
        foreach ($list as $item) {
            if (isset($list[$item[$pidName]])) {
                $list[$item[$pidName]][$childrenname][] = &$list[$item[$keyName]];
            } else {
                $tree[] = &$list[$item[$keyName]];
            }
        }
        return $tree;
    }

}


if (!function_exists('tidy_tree')) {

    /**
     * 格式化分类
     * @param $menusList
     * @param int $pid
     * @param array $navList
     * @return array
     */
    function tidy_tree($menusList, $pid = 0, $navList = []) {
        foreach ($menusList as $k => $menu) {
            if ($menu['parent_id'] == $pid) {
                unset($menusList[$k]);
                $menu['children'] = tidy_tree($menusList, $menu['id']);
                if ($menu['children'])
                    $menu['expand'] = true;
                $navList[] = $menu;
            }
        }
        return $navList;
    }

}

if (!function_exists('get_file_link')) {
    /**
     * 获取文件带域名的完整路径
     * @param string $link
     * @return string
     */
    function get_file_link(string $link) {
        if (!$link) {
            return '';
        }
        if (strstr('http', $link) === false) {
            return app()->request->domain() . $link;
        } else {
            return $link;
        }
    }
}


if (!function_exists('sql_filter')) {

    /**
     * sql 参数过滤
     * @param string $str
     * @return mixed
     */
    function sql_filter(string $str) {
        $filter = ['select ', 'insert ', 'update ', 'delete ', 'drop', 'truncate ', 'declare', 'xp_cmdshell', '/add', ' or ', 'exec', 'create', 'chr', 'mid', ' and ', 'execute'];
        $toupper = array_map(function ($str) {
            return strtoupper($str);
        }, $filter);
        return str_replace(array_merge($filter, $toupper, ['%20']), '', $str);
    }

}


if (!function_exists('time_tran')) {

    /**
     * 时间戳人性化转化
     * @param $time
     * @return string
     */
    function time_tran($time) {
        $t = time() - $time;
        $f = array(
            '31536000' => '年',
            '2592000' => '个月',
            '604800' => '星期',
            '86400' => '天',
            '3600' => '小时',
            '60' => '分钟',
            '1' => '秒'
        );
        foreach ($f as $k => $v) {
            if (0 != $c = floor($t / (int) $k)) {
                return $c . $v . '前';
            }
        }
    }

}

if (!function_exists('anonymity')) {

    /**
     * 匿名处理处理用户昵称
     * @param $name
     * @return string
     */
    function anonymity($name, $type = 1) {
        if ($type == 1) {
            return mb_substr($name, 0, 1, 'UTF-8') . '**' . mb_substr($name, -1, 1, 'UTF-8');
        } else {
            $strLen = mb_strlen($name, 'UTF-8');
            $min = 3;
            if ($strLen <= 1)
                return '*';
            if ($strLen <= $min)
                return mb_substr($name, 0, 1, 'UTF-8') . str_repeat('*', $min - 1);
            else
                return mb_substr($name, 0, 1, 'UTF-8') . str_repeat('*', $strLen - 1) . mb_substr($name, -1, 1, 'UTF-8');
        }
    }

}

if (!function_exists('sort_list_tier')) {

    /**
     * 分级排序
     * @param $data
     * @param int $pid
     * @param string $field
     * @param string $pk
     * @param string $html
     * @param int $level
     * @param bool $clear
     * @return array
     */
    function sort_list_tier($data, $pid = 0, $field = 'pid', $pk = 'id', $html = '|-----', $level = 1, $clear = true) {
        static $list = [];
        if ($clear)
            $list = [];
        foreach ($data as $k => $res) {
            if ($res[$field] == $pid) {
                $res['html'] = str_repeat($html, $level);
                $list[] = $res;
                unset($data[$k]);
                sort_list_tier($data, $res[$pk], $field, $pk, $html, $level + 1, false);
            }
        }
        return $list;
    }

}

if (!function_exists('sort_city_tier')) {

    /**
     * 城市数据整理
     * @param $data
     * @param int $pid
     * @param string $field
     * @param string $pk
     * @param string $html
     * @param int $level
     * @param bool $clear
     * @return array
     */
    function sort_city_tier($data, $pid = 0, $navList = []) {
        foreach ($data as $k => $menu) {
            if ($menu['parent_id'] == $pid) {
                unset($menu['parent_id']);
                unset($data[$k]);
                $menu['c'] = sort_city_tier($data, $menu['v']);
                $navList[] = $menu;
            }
        }
        return $navList;
    }

}

if (!function_exists('http_get')) {

    /**
     * get提交数据
     *
     * @param string
     * @return string
     */
    function http_get($url) {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_TIMEOUT, 500);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl, CURLOPT_URL, $url);

        $res = curl_exec($curl);
        curl_close($curl);

        return $res;
    }

}

if (!function_exists('http_post')) {

    /**
     * post提交数据
     *
     * @param string
     * @return string
     */
    function http_post($url, $data = NULL) {
        $curl = curl_init();
        $header[] = 'Content-Type: application/json';
        curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        $output = curl_exec($curl);
        curl_close($curl);
        return $output;
    }

}
if (!function_exists('http_post1')) {

    /**
     * post提交数据
     *
     * @param string
     * @return string
     */
    function http_post1($url, $data = [],$header=[]) {
        //curlPost请求
        $ch = curl_init($url);
        $header[] = 'Content-Type: application/json';
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 1000);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_WRITEFUNCTION, function($ch, $data) {
            // 处理接收到的数据块...
            echo json_decode($data,true)['response']; // 或者存储到文件等操作...
            flush(); // 发送输出到浏览器
            ob_flush();
            return strlen($data); // 返回写入的字节数，确保cURL知道写入了多少数据
        });
        $return_data = curl_exec($ch);
        curl_errno($ch);
        return $return_data;
    }

}

if (!function_exists('add_log')) {

    /**
     * 添加操作记录
     * @param string $table 操作数据表
     * @param string $id 表id
     * @param string $remark 操作说明
     * @param string $old 操作前数据
     * @return string
     */
    function add_log($table, $id,$remark = '',$old = '') {
        $data = [
            'table' => $table,
            'tid' => $id,
            'opt_user' => $_SESSION['LOGIN_USER_ID'],
            'opt_time' => time(),
            'remark' => $remark,
            'old' => $old,
        ];
        Db::table('top_log')->insert($data);
    }

}

if (!function_exists('get_pcas')) {

    /**
     * 获取省市区列表
     * @param int $pid 操作数据表
     * @param int $type 表id
     * @return object
     */
    function get_pcas(int $pid = 0,int $type = 0) {
        $pcas = Cache::get('pcas_'.$pid);
        if(empty($pcas)){
            $pcas = Db::table('top_pcas')->where(['pid'=>$pid])->select()->toArray();
            Cache::tag('pca')->set('pcas_'.$pid,$pcas);
        }
        return $pcas;
    }

}
if (!function_exists('get_pcas_all')) {

    /**
     * 获取所有省市区数据
     * @param int $type 返回数据结构{默认0：简单结构，1：树形结构，2：code=>带完整地名}
     * @return object
     */
    function get_pcas_all(int $type = 0) {
        //Cache::tag('pca')->clear();
        $results = Cache::get('pcasAll_'.$type);
        if(empty($results)){
            $pcas = Cache::get('pcasAll');
            if(empty($pcas)){
                $pcas = Db::table('top_pcas')->order('sort,code')->select()->toArray();
                Cache::tag('pca')->set('pcasAll',$pcas);
            }
            if($type==4){
                foreach ($pcas as $k=>$v){
                    $results[] = [
                        'value' => $v['code'],
                        'label' => $v['name'],
                        'pid' => $v['pid'],
                        'id' => $v['id'],
                    ];
                }
                $results = get_tree_children($results);
            }else if($type==3){
                foreach ($pcas as $k=>$v){
                    $results[] = [
                        'code' => $v['code'],
                        'name' => $v['name'],
                        'names' => $v['names'],
                    ];
                }
            }else if($type==2){
                foreach ($pcas as $k=>$v){
                    $results[$v['code']] = $v['names'];
                }
            }else if($type==1){
                $results = get_tree_children($pcas);
            }else{
                foreach($pcas as $v){
                    $results[$v['code']] = $v['name'];
                }
            }
            Cache::tag('pca')->set('pcasAll_'.$type,$results);
        }
        return $results;
    }

}
if (!function_exists('isJson')) {

    /**
     * 获取所有省市区数据
     * @param int $type 返回数据结构{默认0：简单结构，1：树形结构，2：code=>带完整地名}
     * @return object
     */
    function isJson($string) {
        json_decode($string);
        return (json_last_error()==JSON_ERROR_NONE);
    }

}

//字符转换
function fieldToNum($field,$result)
{
    if(empty($result)){
        return '';
    }
    if (!is_numeric($result)) {
        if($field=='dept_id'){
            $re = Db::table('department')->where(['DEPT_NAME'=>$result])->find();
            $result = empty($re)?1:$re['DEPT_ID'];
        }
        if($field=='user_id'){
            $re = Db::table('td_user')->where(['USER_NAME'=>$result])->find();
            $result = empty($re)?0:$re['USER_ID'];
        }
        if($field=='user_priv'){
            $re = Db::table('user_priv')->where(['PRIV_NAME'=>$result])->find();
            $result = empty($re)?288:$re['USER_PRIV'];
        }
        if($field=='pca_code'){
            $re = Db::table('top_pca')->where(['name'=>$result])->find();
            $result = $re['code'];
        }
        $tmp = config("global.$field");
        foreach ($tmp as $k => $v) {
            if ($v == $result) {
                $result = $k;
                break;
            }
        }
    }
    return $result;
}



//导入excel数据
function import($file_name,$first_line=1){
    $file=request()->file($file_name);
    if(!$file){
        throw new \think\Exception("未找到文件");
    }
    $file_type=$file->getType();
    $file_path=$file->getRealPath();

    $reader=\PhpOffice\PhpSpreadsheet\IOFactory::createReader("Xlsx");
    if(!$reader->canRead($file_path)){
        throw new \think\Exception("无法读取excel");
    }
    $sheet=$reader->load($file_path)->getSheet(0);
    $all_columm=$sheet->getHighestColumn();
    $all_columm ++;
    $all_rows=$sheet->getHighestRow();
    $r=[];
    for ($i=1+$first_line;$i<=$all_rows;$i++){
        $d=[];
        for ($j='A';$j!=$all_columm;$j++){
            $d[]=$sheet->getCell($j.$i)->getValue();
        }
        $r[]=$d;
    }
    return $r;
}


function importHeader($file_name){
    $file=request()->file($file_name);
    if(!$file){
        throw new \think\Exception("未找到文件");
    }
    $file_type=$file->getType();
    $file_path=$file->getRealPath();

    $reader=\PhpOffice\PhpSpreadsheet\IOFactory::createReader("Xlsx");
    if(!$reader->canRead($file_path)){
        throw new \think\Exception("无法读取excel");
    }
    $sheet=$reader->load($file_path)->getSheet(0);
    $all_columm=$sheet->getHighestColumn();
    $all_columm ++;
    $r=[];
    for ($j='A';$j!=$all_columm;$j++){
        $r[]=$sheet->getCell($j.'1')->getValue();
    }
    return $r;
}
//获取部门下级部门
function getChildrenDepts($dept_id){
    $deptids[] = $dept_id;
    $res = Db::table('department')->where(['DEPT_PARENT'=>$dept_id])->field('DEPT_ID')->select()->toArray();
    foreach($res as $v){
        $depts = getChildrenDepts($v['DEPT_ID']);
        foreach ($depts as $v){
            $deptids[] = $v;
        }
    }
    return array_unique($deptids);
}


//数据处理 根据标志转换
function dataHand($data, $b = "__")
{

    if (!is_array($data)) {
        return $data;
    }

    foreach ($data as $k => $v) {
        if (is_array($v)) {
            $data[$k] = dataHand($v);
        }
        $kk = explode($b, $k);
        /*    if(request()->action()=="getOneData"){
                echo "<pre>";
                print_r($kk);
                echo "</pre>";
            }*/
        if (count($kk) == 2) {
            $data[$kk[0]][$kk[1]] = $v;
            unset($data[$k]);
        }
    }
    return $data;

}
function ids_parse($str, $dot_tmp = ',') {
    $idstr = $dot = '';
    if (!$str)
        return '';
    if (is_array($str)) {
        $idarr = $str;
    } else {
        $idarr = explode(',', $str);
    }
    $idarr = array_unique($idarr);
    foreach ($idarr as $id) {
        $id = intval($id);
        if ($id > 0) {
            $idstr .= $dot . $id;
            $dot = $dot_tmp;
        }
    }
    if (!$idstr)
        $idstr = 0;
    return $idstr;
}

/**
 * @param array $data
 * @return bool|string
 * 创建流程
 */
function create_flow($flow_id) {
    $res = curl_post("/oa/newFlow.php?flow_id=" . $flow_id, []);
    return $res;
}

function create_flow_left($flow_id, $left) {
    $res = curl_post("/oa/newFlowLeft.php?flow_id=" . $flow_id . '&left=' . $left, []);
    return $res;
}

/*
 * 获取流程查看url
 * */

function getRunInfo($run_id) {
    try {
        $data = Db::table("bpm_run_prcs brp")
            ->join('bpm_run br', 'brp.run_id=br.run_id', 'left')
            ->field("brp.*,br.FLOW_ID")
            ->where('brp.run_id', $run_id)
            ->order("brp.id desc")
            ->limit(1, 1)
            ->find();

        if ($data && $data['ID']) {
            $url = "/general/approve_center/list/print?RUN_ID=" . $data['RUN_ID'] . "&FLOW_ID=" . $data['FLOW_ID'] . '&FLOW_VIEW=123&READ_CONTENT=&PRCS_ID='
                . $data['PRCS_ID'] . "&FLOW_PRCS=" . $data['FLOW_PRCS'] . "&PRCS_KEY_ID=" . $data['ID'] . "&archive_time=&CHILD_RUN=" . $data['CHILD_RUN'];
            $url = "/general/approve_center/list/print/index.php?actionType=view&RUN_ID=" . $data['RUN_ID'] . "&FLOW_ID=" . $data['FLOW_ID'];

            $rdata = [
                'code' => 200,
                'url' => $url,
            ];
            return $rdata;
        } else {
            $rdata = [
                'code' => -100,
                'msg' => '流程信息获取失败',
            ];
            return $rdata;
        }
    } catch (\Exception $e) {
        $rdata = [
            'code' => -100,
            'msg' => '流程信息获取失败',
            's' => $e->getMessage()
        ];
        return $rdata;
    }
}

//获取当前服务器地址
function getHost() {
    $url = "http://" . $_SERVER['HTTP_HOST'] . ":" . $_SERVER['SERVER_PORT'];
    return $url;
}

//发送通知
function sendMessage($from_user, $to_user, $msg, $url, $code = "666") {
    //code  666  值班通知
    //
    //发送消息
    $curl_url = getHost() . "/general/toppingsoft/oa/sendSms.php?act=send_sms";
    //dump($curl_url);
    $params = array(
        'get_user' => $to_user,
        'send_user' => $from_user,
        'msg' => $msg,
        'code' => $code,
        'url' => $url,
    );
    $arr = Db::table('sms_body')->alias('a')->leftJoin('sms b','a.BODY_ID = b.BODY_ID')
        ->where([['REMIND_URL','=',$url],['TO_ID','in',explode(',',$to_user)]])->column('b.SMS_ID');
    if(!empty($arr)){
        Db::table('sms')->where('SMS_ID','in',$arr)->update(['REMIND_FLAG'=>0]);
    }
    postUrl($curl_url, $params);
}

//curlPost请求
function postUrl($url, $params = []) {
    $postdata = http_build_query($params);
    $options = array(
        'http' => array(
            'method' => 'POST',
            'header' => 'Content-type:application/x-www-form-urlencoded; charset=UTF-8',
            'content' => $postdata,
            'timeout' => 15 * 60 // 超时时间（单位:s）
        )
    );
    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);
    return $result;
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 1000);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $return_data = curl_exec($ch);
    curl_errno($ch);
    return $return_data;
}

//切割多文件上传
function picstoarr($str = '') {
    if ($str == '')
        return[];
    $data = array();
    $v = explode(',', $str);
    foreach ((array) $v as $r) {
        $r = explode(',', $r);
        $res['file'] = $r[1];
        $res['desc'] = $r[0];
        $data[] = $res;
    }
    return $data;
}

//上传文件
function uploadfile($file, $filename, $ym, $attach_id) {

    $res['code'] = 100;
    $res['attach_id'] = '';
    $res['attach_name'] = '';
    $path = substr($_SERVER['DOCUMENT_ROOT'], 0, 1) . ':/MYOA/attach/upload_temp/' . $ym;
    if (!is_dir($path)) {
        mkdir($path, 0755, true);
    }
    $info = $file->move($path, $attach_id . '.' . $filename, false);
    return $info;
}

//校验attach_id是否重复
function isAttach($ym) {
    $attach_id = $ym . mt_rand(1, 99999);
    $res = Db::table('attachment')->where('ATTACH_ID', $attach_id)->find();
    if ($res && $res['AID']) {
        $attach_id = isAttach($ym);
    }
    return $attach_id;
}

//添加上传文件数据
function writeFile($ym, $file_name, $module_id = "35") {
    $attach_id = isAttach($ym);
    $attach_file = $attach_name = $file_name;
    $res = Db::table('attachment')->max("AID");
    $aid = ++$res;
    $data = array(
        'AID' => $aid,
        'POSITION' => 2,
        'MODULE' => $module_id,
        'YM' => $ym,
        'ATTACH_ID' => $attach_id,
        'ATTACH_FILE' => $attach_file,
        'ATTACH_NAME' => $attach_name,
        'ATTACH_SIGN' => 0,
        'DEL_FLAG' => 0,
        'MYOA_OFFICE' => 0,
        'MYOA_OFFICE_VER' => '2003'
    );
    $res = Db::table('attachment')->insert($data);
    if ($res) {
        return $attach_id;
    } else {
        return -1;
    }
}

//获取文件后缀名
function file_ext($file_name) {
    $ext = explode('.', $file_name);
    $va = count($ext) - 1;
    return $ext[$va];
}

function get_client_ip(){
    $ip = $_SERVER['REMOTE_ADDR'];
    if(isset($_SERVER["HTTP_CLIENT_IP"])&&preg_match("/^([0-9]{1,3}\.){3}[0-9]{1,3}$/",$_SERVER['HTTP_CLIENT_IP'])){
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    }else{
        if(isset($_SERVER["HTTP_X_FORWARDED_FOR"]) && preg_match_all("#\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}#s",$_SERVER['HTTP_X_FORWARDED_FOR'],$matches)){
            foreach ($matches[0] as $xip){
                if($xip != "127.0.0.1"){
                    $ip = $xip;
                    break;
                }
            }
        }
    }
    return $ip;
}
function get_file_real_address($attachment_id, $attachment_name,$module) {

    $res = curl_post("/oa/getAttachAddress.php?attachment_id=".$attachment_id."&attachment_name=" . $attachment_name . '&module=' . $module, []);
    return $res;
}
function get_op_code($attachment_name) {

    $res = curl_post("/oa/getOpCode.php?attachment_name=".$attachment_name, []);
    return $res;
}
function attach_sign_key($ATTACHMENT_ID, $ATTACHMENT_NAME, $ID_IS_REAL = false)
{
    if(!is_office($ATTACHMENT_NAME))
        return 0;

    $ARRAY = attach_id_explode($ATTACHMENT_ID);
    $AID = $ARRAY['AID'];
    $ATTACHMENT_ID = $ID_IS_REAL ? attach_id_encode($ATTACHMENT_ID,$ATTACHMENT_NAME) : $ARRAY['ATTACHMENT_ID'];
    $YM = $ARRAY['YM'];
    $SIGN_KEY = $ARRAY['SIGN_KEY'];

    if($SIGN_KEY != "")
        return $SIGN_KEY;

    if($AID > 0)
    {
        $SIGN_KEY = $ATTACHMENT_ID*3+2;
    }
    else
    {
        if($YM != "")
            $SIGN_KEY = $ATTACHMENT_ID;
        else
            $SIGN_KEY = attach_id_decode($ATTACHMENT_ID,$ATTACHMENT_NAME)*3+2;
    }

    return $SIGN_KEY;
}
function attach_id_explode($ATTACHMENT_ID)
{
    $AID = 0;
    $POS = strpos($ATTACHMENT_ID,"@");
    if($POS !== FALSE)
    {
        $AID = intval(substr($ATTACHMENT_ID, 0, $POS));
        $ATTACHMENT_ID=substr($ATTACHMENT_ID, $POS+1);
    }

    $YM = "";
    $POS = strpos($ATTACHMENT_ID,"_");
    if($POS !== FALSE)
    {
        $YM_TMP = substr($ATTACHMENT_ID, 0, $POS);
        $ATTACHMENT_ID_TMP=substr($ATTACHMENT_ID, $POS+1);
        if(strlen($YM_TMP) == 4 && is_numeric($YM_TMP))
        {
            $YM = $YM_TMP;
            $ATTACHMENT_ID = $ATTACHMENT_ID_TMP;
        }
    }

    $SIGN_KEY = "";
    $POS = strpos($ATTACHMENT_ID,".");
    if($POS !== FALSE)
    {
        $SIGN_KEY_TMP = substr($ATTACHMENT_ID, $POS+1);
        $ATTACHMENT_ID_TMP=substr($ATTACHMENT_ID, 0, $POS);
        if(is_numeric($SIGN_KEY_TMP) && is_numeric($ATTACHMENT_ID_TMP))
        {
            $SIGN_KEY = $SIGN_KEY_TMP;
            $ATTACHMENT_ID = $ATTACHMENT_ID_TMP;
        }
    }

    return array('AID' => $AID, 'ATTACHMENT_ID' => $ATTACHMENT_ID, 'YM' => $YM, 'SIGN_KEY' => $SIGN_KEY);
}
function attach_id_decode($ATTACHMENT_ID,$ATTACHMENT_NAME)
{
    $ARRAY = attach_id_explode($ATTACHMENT_ID);
    $ATTACHMENT_ID = $ARRAY['ATTACHMENT_ID'];

    return $ATTACHMENT_ID^crc32($ATTACHMENT_NAME);
}
function is_office($FILE_NAME)
{
    $EXT_NAME=strtolower(substr($FILE_NAME,strrpos($FILE_NAME,".")));
    return $EXT_NAME==".doc" || $EXT_NAME==".xls" || $EXT_NAME==".ppt" || $EXT_NAME==".pps" || $EXT_NAME==".docx" || $EXT_NAME==".xlsx" || $EXT_NAME==".pptx"  || $EXT_NAME==".ppsx" || $EXT_NAME==".wps" || $EXT_NAME==".et" || $EXT_NAME==".ett";
}
function attach_sub_dir()
{
    $SCRIPT_NAME = strtolower($_SERVER["SCRIPT_NAME"]);

    if(substr($SCRIPT_NAME,0,16)=="/general/system/")
        $SCRIPT=substr($SCRIPT_NAME,16);
    else if(substr($SCRIPT_NAME,0,9)=="/general/")
        $SCRIPT=substr($SCRIPT_NAME,9);
    else if(substr($SCRIPT_NAME,0,9)=="/pda/pad/")
        $SCRIPT=substr($SCRIPT_NAME,9);
    else if(substr($SCRIPT_NAME,0,5)=="/pda/")
        $SCRIPT=substr($SCRIPT_NAME,5);
    else if(substr($SCRIPT_NAME,0,8)=="/mobile/")
        $SCRIPT=substr($SCRIPT_NAME,8);
    else
        $SCRIPT="unknown";

    return substr($SCRIPT,0,strpos($SCRIPT,"/"));
}
// 截取字符串
function msubstr($str, $start = 0, $length, $charset = "utf-8", $suffix = true)
{
    if (function_exists("mb_substr")) {
        return mb_substr($str, $start, $length, $charset);
    } elseif (function_exists('iconv_substr')) {
        return iconv_substr($str, $start, $length, $charset);
    }
    $re['utf-8']  = "/[\x01-\x7f]|[\xc2-\xdf][\x80-\xbf]|[\xe0-\xef][\x80-\xbf]{2}|[\xf0-\xff][\x80-\xbf]{3}/";
    $re['gb2312'] = "/[\x01-\x7f]|[\xb0-\xf7][\xa0-\xfe]/";
    $re['gbk']    = "/[\x01-\x7f]|[\x81-\xfe][\x40-\xfe]/";
    $re['big5']   = "/[\x01-\x7f]|[\x81-\xfe]([\x40-\x7e]|\xa1-\xfe])/";
    preg_match_all($re[$charset], $str, $match);
    $slice = join("", array_slice($match[0], $start, $length));
    if ($suffix) {
        return $slice . "…";
    }

    return $slice;
}

/**
 * 获取周一到周五的日期
 * @datetime 2023/8/28 11:19
 * @param $date
 * @param array $needWeek 需要的周
 * @return array
 */
function get_week_list($date, $needWeek = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'])
{
    $time = strtotime($date);
    //获取当前周几
    $week = date('w', $time);
    $weekname = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    //星期日排到末位
    if (empty($week)) {
        $week = 7;
    }
    $date = [];
    for ($i = 0; $i < 7; $i++) {
        if (in_array($weekname[$i], $needWeek)) {
            $date_time = date("Y-m-d", strtotime('+' . $i + 1 - $week . ' days', $time));
            $date[$i]['date'] = $date_time;
            $date[$i]['time'] = strtotime($date_time);
            $date[$i]['week'] = $weekname[$i];
        }
    }
    return $date;
}

/**
 * 获取一周内周一和周日的日期
 * @datetime 2023/8/29 10:33
 * @param $date
 * @return array
 */
function get_week_start_end_date($date)
{
    $time = strtotime($date);
    $weekday = date('N', $time); // 获取星期几
    $monday_offset = $weekday - 1; // 计算距离星期一的天数
    $sunday_offset = 7 - $weekday; // 计算距离星期日的天数
    $monday = date('Y-m-d', strtotime("-$monday_offset days", $time));
    $sunday = date('Y-m-d', strtotime("+$sunday_offset days", $time));
    return [$monday, $sunday];
}

/**
 * 两个浮点数比较相等
 * 免类似  300.3 + 144.9 != 445.2
 * @param $a
 * @param $b
 * @return boolean
 */
function eq($a, $b, $scale = 5)
{
    if (!empty($scale)) {
        return bccomp($a, $b, $scale) == 0;
    } else {
        return bccomp($a, $b) == 0;
    }
}

/**
 * 两个浮点数比较，大于
 * @param $a
 * @param $b
 * @return boolean
 */
function gt($a, $b, $scale = 5)
{
    return bccomp($a, $b, $scale) == 1;
}

/**
 * 两个浮点数比较，小于
 * @param $a
 * @param $b
 * @return boolean
 */
function lt($a, $b, $scale = 5)
{
    return bccomp($a, $b, $scale) == -1;
}


/**
 * HSM硬件加密模块 - 数据加密
 * 通过调用Java HSM程序对数据进行硬件级加密
 *
 * @param string $data 要加密的原始数据
 * @return string 加密后的数据（通常为Base64编码格式）
 */
function hsmEncrypt($data) {
    $result = shell_exec(config('app.java_path')." -jar ".config('app.hsm_path')."  enc \"$data\" 2>nul");
    return trim($result);
}

/**
 * HSM硬件加密模块 - 数据解密
 * 通过调用Java HSM程序对加密数据进行解密
 *
 * @param string $encryptedData 已加密的数据
 * @return string 解密后的原始数据
 */
function hsmDecrypt($encryptedData) {
    $result = shell_exec(config('app.java_path')." -jar ".config('app.hsm_path')."  dec \"$encryptedData\" 2>nul");
    return trim($result);
}

/**
 * HSM硬件加密模块 - 生成HMAC签名
 * 通过调用Java HSM程序为数据生成HMAC（Hash-based Message Authentication Code）签名
 * 用于数据完整性验证和身份认证
 *
 * @param string $data 要生成HMAC签名的数据
 * @return string HMAC签名值
 */
function hsmHmac($data) {
    $result = shell_exec(config('app.java_path')." -jar ".config('app.hsm_path')."  hmac \"$data\" 2>nul");
    return trim($result);
}

/**
 * HSM硬件加密模块 - 验证HMAC签名
 * 通过调用Java HSM程序验证数据的HMAC签名是否正确
 * 用于确保数据完整性和来源可信性
 *
 * @param string $data 原始数据
 * @param string $hmac 要验证的HMAC签名
 * @return bool 验证成功返回true，失败返回false
 */
function hsmVerify($data, $hmac) {
    $result = shell_exec(config('app.java_path')." -jar ".config('app.hsm_path')." verify \"$data\" \"$hmac\" 2>nul");
    return trim($result) === 'true';
}

/**
 * 带缓存的字段加密方法
 * 优先从Redis缓存中获取加密结果，如果没有则进行加密并缓存
 *
 * @param string $str 要加密的原始字符串
 * @param int $expire 缓存过期时间（秒），默认24小时
 * @return string|false 加密后的字符串，失败返回false
 */
function hsmCacheEncrypt($str, $expire = 86400) {
    //如果没有开启加密则直接返回 $str
    if(config('app.is_hsm_encrypt')==false){
        return $str;
    }
    // 参数验证
    if (empty($str) || !is_string($str)) {
        return $str;
    }

    // 首先判断字符串是否已经被加密
    if (heuristicEncryptionCheck($str)) {
        // 如果已经加密，直接返回原始参数
        return $str;
    }

    try {
        // 生成缓存键：使用原始值的哈希作为键
        $cacheKey = 'hsm_encrypt:' . hash('sha256', $str . "c1a2y3k4e5y6");

        // 尝试从Redis缓存获取
        $cache = \think\facade\Cache::store('redis');
        $cachedResult = $cache->get($cacheKey);

        if ($cachedResult !== null) {
            // 缓存命中，直接返回
            return $cachedResult;
        }

        // 缓存未命中，执行加密
        $encryptedValue = hsmEncrypt($str);

        if ($encryptedValue !== false && !empty($encryptedValue)) {
            // 加密成功，存入缓存
            $cache->set($cacheKey, $encryptedValue, $expire);
            return $encryptedValue;
        }

        return false;

    } catch (\Exception $e) {
        // Redis不可用时，直接进行加密
        error_log('hsmCacheEncrypt Redis error: ' . $e->getMessage());
        return hsmEncrypt($str);
    }
}

/**
 * 带缓存的字段解密方法
 * 优先从Redis缓存中获取解密结果，如果没有则进行解密并缓存
 *
 * @param string $encryptedStr 要解密的加密字符串
 * @param int $expire 缓存过期时间（秒），默认24小时
 * @return string|false 解密后的字符串，失败返回false
 */
function hsmCacheDecrypt($encryptedStr, $expire = 86400) {
    // 参数验证
    if (empty($encryptedStr) || !is_string($encryptedStr)) {
        return $encryptedStr;
    }

    // 首先判断字符串是否已经被加密
    if (!heuristicEncryptionCheck($encryptedStr)) {
        // 如果没有加密，直接返回原始参数
        return $encryptedStr;
    }

    try {
        // 生成缓存键：使用加密值的哈希作为键
        $cacheKey = 'hsm_decrypt:' . hash('sha256', $encryptedStr . "c1a2y3k4e5y6");

        // 尝试从Redis缓存获取
        $cache = \think\facade\Cache::store('redis');
        $cachedResult = $cache->get($cacheKey);

        if ($cachedResult !== null) {
            // 缓存命中，直接返回
            return $cachedResult;
        }
        // 缓存未命中，执行解密
        $decryptedValue = hsmDecrypt($encryptedStr);

        if ($decryptedValue !== false && !empty($decryptedValue)) {
            // 解密成功，存入缓存
            $cache->set($cacheKey, $decryptedValue, $expire);
            return $decryptedValue;
        }

        return false;

    } catch (\Exception $e) {
        // Redis不可用时，直接进行解密
        error_log('hsmCacheDecrypt Redis error: ' . $e->getMessage());
        return hsmDecrypt($encryptedStr);
    }
}

/**
 * 清除HSM加密解密缓存
 *
 * @param string $pattern 缓存键模式，默认清除所有HSM相关缓存
 * @return bool 清除是否成功
 */
function hsmClearCache($pattern = 'hsm_*') {
    try {
        $cache = \think\facade\Cache::store('redis');

        // 获取Redis实例
        $redis = $cache->handler();

        // 获取匹配的键
        $keys = $redis->keys($pattern);

        if (!empty($keys)) {
            // 批量删除
            $redis->del($keys);
            return true;
        }

        return true;

    } catch (\Exception $e) {
        error_log('hsmClearCache error: ' . $e->getMessage());
        return false;
    }
}


/**
 * 生成统一的缓存键
 * 使用与 hsmCacheEncrypt 相同的哈希逻辑
 *
 * @param string $data 要生成缓存键的数据
 * @param string $prefix 缓存键前缀
 * @return string 生成的缓存键
 */
function generateHsmCacheKey($data, $prefix = '') {
    $hashedKey = hash('sha256', $data . "c1a2y3k4e5y6");
    return $prefix . $hashedKey;
}

/**
 * 获取HSM缓存统计信息
 *
 * @return array 缓存统计信息
 */
function hsmCacheStats() {
    try {
        $cache = \think\facade\Cache::store('redis');
        $redis = $cache->handler();

        // 获取加密缓存键数量
        $encryptKeys = $redis->keys('hsm_encrypt:*');
        $encryptCount = count($encryptKeys);

        // 获取解密缓存键数量
        $decryptKeys = $redis->keys('hsm_decrypt:*');
        $decryptCount = count($decryptKeys);

        // 计算缓存占用的内存（估算）
        $totalMemory = 0;
        $sampleKeys = array_merge(
            array_slice($encryptKeys, 0, 10),
            array_slice($decryptKeys, 0, 10)
        );

        foreach ($sampleKeys as $key) {
            $totalMemory += strlen($redis->get($key));
        }

        // 估算总内存使用
        $avgSize = count($sampleKeys) > 0 ? $totalMemory / count($sampleKeys) : 0;
        $estimatedMemory = $avgSize * ($encryptCount + $decryptCount);

        return [
            'encrypt_cache_count' => $encryptCount,
            'decrypt_cache_count' => $decryptCount,
            'total_cache_count' => $encryptCount + $decryptCount,
            'estimated_memory_bytes' => round($estimatedMemory),
            'estimated_memory_mb' => round($estimatedMemory / 1024 / 1024, 2),
            'redis_connected' => true
        ];

    } catch (\Exception $e) {
        return [
            'encrypt_cache_count' => 0,
            'decrypt_cache_count' => 0,
            'total_cache_count' => 0,
            'estimated_memory_bytes' => 0,
            'estimated_memory_mb' => 0,
            'redis_connected' => false,
            'error' => $e->getMessage()
        ];
    }
}



/**
 * 启发式加密检查
 * 基于HSM加密后数据的特征进行判断，不依赖配置文件
 *
 * @param string $value 要检测的字符串值
 * @return bool 如果已加密返回true，否则返回false
 */
function heuristicEncryptionCheck($value)
{
    if (empty($value)) {
        return false;
    }

    // 排除模式（明显的明文特征）
    $excludePatterns = [
        '/^\d+$/',                                          // 纯数字
        '/^[a-zA-Z\s]+$/',                                 // 纯英文字母
        '/[\x{4e00}-\x{9fa5}]/u',                          // 中文字符
        '/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/', // 邮箱格式
        '/^1[3-9]\d{9}$/',                                 // 手机号格式
        '/^\d{17}[\dXx]$/',                                // 身份证号格式
    ];

    // 首先检查排除模式（明显的明文特征）
    foreach ($excludePatterns as $pattern) {
        if (preg_match($pattern, $value)) {
            return false;
        }
    }

    // 检查是否为Base64编码（HSM通常输出Base64）
    if (base64_encode(base64_decode($value, true)) === $value) {
        // 进一步检查长度和字符特征
        $length = strlen($value);
        $minLength = 2;   // 最小长度
        $maxLength = 10240; // 最大长度

        // HSM加密后的数据通常有特定的长度范围
        if ($length >= $minLength && $length <= $maxLength) {
            // 检查是否包含Base64字符集
            if (preg_match('/^[A-Za-z0-9+\/]*={0,2}$/', $value)) {
                return true;
            }
        }
    }

    // 检查是否包含HSM特定的前缀或后缀
    $prefixes = ['HSM_', 'ENC_'];
    $suffixes = ['_HSM', '_ENC'];

    foreach ($prefixes as $prefix) {
        if (strpos($value, $prefix) === 0) {
            return true;
        }
    }

    foreach ($suffixes as $suffix) {
        if (substr($value, -strlen($suffix)) === $suffix) {
            return true;
        }
    }

    return false;
}