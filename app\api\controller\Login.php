<?php

namespace app\api\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use app\api\model\WxModel;
use app\controller\Sms;
use app\model\SmsModel;
use Gregwar\Captcha\CaptchaBuilder;
use hg\apidoc\annotation as Apidoc;
use think\facade\Cache;
use think\facade\View;
use think\facade\Db;
use think\App;
use  app\api\model\SecsModel;

/**
 * @Apidoc\Title("登陆相关")
 * @Apidoc\Group("Login")
 * @Apidoc\Sort(1)
 */
class Login extends Base
{

    /**
     * NotHeaders
     * @Apidoc\Title("获取图片验证码")
     * @Apidoc\Desc("获取图片验证码")
     * @Apidoc\Method("POST")
     * @Apidoc\Returned("captcha_token", type="string", desc="验证码token，提交验证时需跟验证码一起提交")
     * @Apidoc\Returned("image", type="string", desc="base64图片")
     */
    public function verify(){
		$intstr = (string) create_nonce_str(4,"123456789");
        $builder = new CaptchaBuilder($intstr);
        $builder->build();
        // 获取生成的验证码
        $captcha = $builder->getPhrase();
        $captcha_token = md5($captcha);
        Cache::set($captcha_token,$captcha,300);
        // 将验证码图片数据返回给用户
//        $image = response($builder->output())->contentType('image/jpeg');
        result(['captcha_token'=>$captcha_token,'image'=>$builder->inline()]);
    }

    /**
     * NotHeaders
     * @Apidoc\Title("小程序登陆授权")
     * @Apidoc\Desc("小程序登陆授权")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("code", type="string",require=true, default="",desc="登录时获取的 code，可通过wx.login获取")
     * @Apidoc\Returned("token", type="string", desc="token（需保存本地，其他接口需要该token做身份验证）")
     * @Apidoc\Returned("user", type="object", desc="用户信息",children={
     * @Apidoc\Returned("user_type", type="string", desc="用户类型{tourist:游客，company:企业用户，area: 区县应急局用户，city：市应急局用户，org:评审单位用户，expert:评审专家用户}"),
     * @Apidoc\Returned("name", type="string", desc="用户姓名"),
     * @Apidoc\Returned("head", type="string", desc="用户头像"),
     * @Apidoc\Returned("position", type="string", desc="用户职务"),
     * @Apidoc\Returned("unionid", type="string", desc="微信unionid"),
     * @Apidoc\Returned("openid", type="string", desc="微信openid"),
     *     })
     */
    public function welogin($code='') {
        $wx = new WxModel();
        $res = $wx->login($code);
        $user = self::findUser('','',$res['openid'],$res['unionid']);
        $user['openid'] = empty($user['openid'])?$res['openid']:$user['openid'];
        $user['unionid'] = empty($user['unionid'])?$res['unionid']:$user['unionid'];
        $token = md5($res['session_key'].time());
        $data = [
            'token' => $token,
            'user' => [
                'user_type' => $user['user_type'],
                'name' => $user['userinfo']['name'],
                'openid' => $res['openid'],
                'unionid' => $res['unionid'],
                'head' => $user['userinfo']['head'],
                'position' => $user['userinfo']['position'],
            ],
        ];
		if($user['user_type']=='expert'){
			$data['user']['expert_id'] = $user["userinfo"]["expert_id"];
			$data['user']['regist_status'] = $user["userinfo"]["regist_status"];
		}
        Cache::set('token_'.$token,$user);
        result($data);
    }

    /**
     * NotHeaders
     * @Apidoc\Title("解绑微信")
     * @Apidoc\Desc("解绑微信")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("token", type="string",require=true, default="",desc="登录时获取的 token")
     *     })
     */
    public function unbind($token='') {
        $user = Cache::get('token_'.$token);
        $types = [
            'city'=>'top_city_user',
            'area'=>'top_area_user',
            'org'=>'top_org_user',
            'expert'=>'top_expert',
            'company'=>'top_company_user'];
        if(in_array($user['userinfo']['user_type'],['company','area','city','org','expert'])){
            Db::table($types[$user['userinfo']['user_type']])->where(['id'=>$user['userinfo']['user_id']])->update(['openid'=>'','unionid'=>'']);
            result('',0,'解绑成功');
        }else{
            result('',1002,'未登录');
        }
    }

    /**
     * NotHeaders
     * @Apidoc\Title("小程序手机号快速验证")
     * @Apidoc\Desc("小程序手机号快速验证")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("code", type="string",require=true, default="",desc="登录时获取的 code，可通过getPhoneNumber获取")
     * @Apidoc\Param("token", type="string",require=true, default="",desc="小程序登陆授权接口获取")
     * @Apidoc\Param("user_type", type="string",require=false, default="",desc="用户类型{company:企业用户，area: 区县应急局用户，city：市应急局用户，org:评审单位用户，expert:评审专家用户}")
     * @Apidoc\Returned("user", type="object", desc="用户信息",children={
     * @Apidoc\Returned("user_type", type="string", desc="用户类型{tourist:游客，company:企业用户，area: 区县应急局用户，city：市应急局用户，org:评审单位用户，expert:评审专家用户}"),
     * @Apidoc\Returned("name", type="string", desc="用户姓名"),
     * @Apidoc\Returned("head", type="string", desc="用户头像"),
     * @Apidoc\Returned("position", type="string", desc="用户职务"),
     *     })
     */
    public function wemobile($code='',$token='',$user_type='') {
        $wx = new WxModel();
        $res = $wx->getuserphone($code);
        $user = Cache::get('token_'.$token);
        if(empty($user)){
            result('',9001,'请先登陆');
        }
        $file = '/opt/tdoa/webroot/general/toppingsoft/public/api.txt';
        file_put_contents($file,json_encode($user)."\n",FILE_APPEND);
        $mobile = $res['phone_info']['purePhoneNumber'];
        $user = self::findUser($user_type, $mobile,$user['openid'],$user['unionid']);
        if(is_array($user)){
            $data = [
                'token' => $token,
                'user' => [
                    'user_type' => $user['user_type'],
                    'name' => $user['userinfo']['name'],
                    'head' => $user['userinfo']['head'],
                    'position' => $user['userinfo']['position'],
                ],
            ];
			//如果是专家系统返回的接口
			if(!empty($user['expertId'])){
				 //获取专家所有的资料
				 $userInfo =  SecsModel::getSecsInterfaceData('secsExpert/get-with-extension', ['expertId'=>$user['expertId']],"get");
				 $data['expert_info'] =  $userInfo['data'];
			}
        }else{
            $data = [
                'token' => $token,
                'user' => ['user_type' => 'tourist'],
                'msg' =>  $user,
            ];
        }
        if(!empty($user_type)){
            if($user['type']==='tourist'||empty($user['userinfo']['user_id'])){
                result('',1003,'账号不存在');
            }
        }
        Cache::set('token_'.$token,$user);
        result($data);
    }



    /**
     * NotHeaders
     * @Apidoc\Title("绑定手机号")
     * @Apidoc\Desc("绑定手机号")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("token", type="string",require=true, default="",desc="小程序登陆授权接口获取接口获取")
     * @Apidoc\Param("user_type", type="string",require=false, desc="用户类型{company:企业用户，area: 区县应急局用户，city：市应急局用户，org:评审单位用户，expert:评审专家用户}" )
     * @Apidoc\Param("phone", type="string",require=true, desc="手机号" )
     * @Apidoc\Param("code", type="string",require=true,desc="短信验证码（验证码密码必传一个）" )
     * @Apidoc\Param("password", type="string",require=true,desc="密码（验证码密码必传一个）" )
	 * @Apidoc\Param("type", type="string",require=false,desc="验证类型(1:短信验证码 2:密码）" )
     * @Apidoc\Param("imgcode", type="string",require=true,desc="图片验证码" )
     * @Apidoc\Param("captcha_token", type="string",require=true,desc="图片验证码token" )
     * @Apidoc\Returned("user", type="object", desc="用户信息",children={
     * @Apidoc\Returned("user_type", type="string", desc="用户类型{tourist:游客，company:企业用户，area: 区县应急局用户，city：市应急局用户，org:评审单位用户，expert:评审专家用户}"),
     * @Apidoc\Returned("name", type="string", desc="用户姓名"),
     * @Apidoc\Returned("head", type="string", desc="用户头像"),
     * @Apidoc\Returned("position", type="string", desc="用户职务"),
     *     })
     */
    public function bindMobile($token='',$user_type='',$type='') {
        $data = $this->request->param();
        if(empty($data['imgcode'])||strcasecmp(Cache::get($data['captcha_token']),$data['imgcode'])!=0){
            result('', 1000, '图片验证码错误');
        }
        //Verify::userCheck('phone', $data); //验证
        $user = self::findUser($user_type,$data['phone']);
        if ($user['user_type']=='tourist' && $user_type!='expert') {
            result('', 1000, '账号不存在');
        }
		$check=null;
		$mobile=$data['phone'];
        if ($data['type']==1) {
            $check = Sms::checksms($data['phone'], $data['code'], 3);
            if (!$check) {
                result('', 1000, '验证码错误');
            }
        } else {
            if ($user['userinfo']['password'] !== crypt($data['password'],$user['userinfo']['salt'])) {
                result('', 1000, '密码错误');
            }
        }
        $user = Cache::get('token_'.$token);
        if(empty($user)){
            result('',9001,'请先登陆');
        }
		//注册专家
        /*if($user_type=='expert'){
            $e = Db::table('top_expert')->where(['mobile'=>$data['phone']])->find();
            if(empty($e)){
                //写专家表
                $salt = create_nonce_str(4);
                $insertData = [
                    'openid'=>$user['userinfo']['openid'],
                    'unionid'=>$user['userinfo']['unionid'],
                    'mobile'=>$data['phone'],
                    'name'=>$data['username'],
                    'reg_ip'=>get_ip(),
                    'reg_time'=>date('Y-m-d H:i:s'),
                    'expert_id'=>$user['userinfo']['expert_id'],
                    'username'=>$data['phone'],
                    'password'=>'66Hky1XQL1oTg',
                    'salt'=>'66cbd97f70c51',
                    'status'=>'1',
                    'org_id'=>'1',
                ];
                Db::table('top_expert')->insert($insertData);
                //注册专家系统的专家
                $res = SecsModel::getSecsInterfaceData('secs/expert-regist-apply/update', ['id'=>$check['data']['id'],'phone'=>$mobile,'name'=>$data['username']],'post');
            }
        }*/
		//绑定openid
        $user = self::findUser($user_type,$data['phone'],$user['openid'],$user['unionid']);
        if(is_array($user)){
            $data = [
                'token' => $token,
                'user' => [
                    'user_type' => $user['user_type'],
                    'name' => $user['userinfo']['name'],
                    'head' => $user['userinfo']['head'],
                    'position' => $user['userinfo']['position'],
                ],
            ];
        }else{
            $data = [
                'token' => $token,
                'user' => ['user_type' => 'tourist'],
                'msg' =>  $user,
            ];
        }
        if($user['user_type']=='expert'){
            $data['user']['expert_id'] = $user["userinfo"]["expert_id"];
            $data['user']['regist_status'] = $user["userinfo"]["regist_status"];
        }
        Cache::set('token_'.$token,$user);
        result($data,0,'登录成功');
    }


    /**
     * @Apidoc\Title("微信绑定手机号发送验证码")
     * @Apidoc\Desc("接口地址")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("微信绑定手机号发送验证码")
     * @Apidoc\Param("mobile", type="string",require=true, desc="手机号" )
     */
    public function bindSms($mobile='') {
        $type = 3;
        if (!preg_match("/1[0-9]{10}$/", $mobile)) {
            result("", 1002, '手机号码格式错误');
        }
        $code = create_nonce_str(6, '0123456789');
        $sms = new Sms();
        $content = '您正在登陆账号，验证码：'.$code;
        $res = $sms->sendsms($mobile,$content);
        $sms->instate($type, $mobile, $code, $res);
        if ($res['code'] == '0') {
            result('');
        } else {
            result('',2001,'短信发送失败，请检查手机号是否正确');
        }
    }

    public function findUser($user_type='',$mobile='',$openid='',$unionid=''){
        //如果是专家端手机号必须传
        if( $user_type=='expert' && empty($mobile)){
            return "手机号码获取不正确！";
        }
        $types = [
            'city'=>'top_city_user',
            'area'=>'top_area_user',
            'org'=>'top_org_user',
            'expert'=>'top_expert',
            'company'=>'top_company_user'];
        $user = [
            'user_type' => 'tourist',
            'userinfo' => [
                'head' => 'http://oa.toppingsoft.com:40019/general/toppingsoft/public/wechat/headerImg.png',
                'name'=>'游客'.create_nonce_str(4),
                'position' => '',
                'openid' => $openid,
                'unionid' => $unionid,
            ],
        ];
        if(!empty($mobile)){
            $where = ['mobile'=>$mobile];
        }else if(!empty($unionid)){
            $where = ['unionid'=>$unionid];
        }else if(!empty($openid)){
            $where = ['openid'=>$openid];
        }else{
            return $user;
        }
        if(empty($user_type)){
            foreach ($types as $k=>$v){
                $u = Db::table($v)->where($where)->find();
                if(!empty($u)){
                    if(!empty($openid)&&empty($u['openid'])){
                        $data['openid'] = $openid;
                    }
                    if(!empty($unionid)&&empty($u['unionid'])){
                        $data['unionid'] = $unionid;
                    }
                    if(!empty($data)){
                        Db::table($v)->where(['id'=>$u['id']])->update($data);
                    }
                    $user['user_type'] = $k;
                    break;
                }
            }
        }else{
			$u = Db::table($types[$user_type])->where($where)->find();
			if(!empty($u)){
				if(!empty($openid)&&empty($u['openid'])){
					$data['openid'] = $openid;
				}
				if(!empty($unionid)&&empty($u['unionid'])){
					$data['unionid'] = $unionid;
				}
				if(!empty($data)){
					Db::table($types[$user_type])->where(['id'=>$u['id']])->update($data);
				}
				$user['user_type'] = $user_type;
			}
        }
        if($user['user_type']=='company'){
            $company = Db::table('top_company_info')->where(['user_id'=>$u['id']])->find();
            $user['userinfo'] = [
                'user_type' => $user['user_type'],
                'id' => $company['id'],
                'company_name' => $company['name'],
                'user_id' => $u['id'],
                'name' => $u['name'],
                'position' => $company['name'],
            ];
        }else if($user['user_type']=='area'){
            $area = Db::table('top_area')->where(['id'=>$u['area_id']])->find();
            $dept_name = '';
            $areaDepartment = \app\area\model\UserModel::getDepartmentMap();
            foreach ($areaDepartment as $v){
                if($v['value']==$u['department']){
                    $dept_name = $v['label'];
                }
            }
            $user['userinfo'] = [
                'user_type' => $user['user_type'],
                'id' => $area['id'],
                'area_name' => $area['name'],
                'pcas' => $area['pcas'],
                'user_id' => $u['id'],
                'name' => $u['name'],
                'dept_id' => $u['department'],
                'dept_name' => $dept_name,
                'role' => $u['role'],
                'position' => $area['name'],
            ];
        }else if($user['user_type']=='city'){
            $city = Db::table('top_city')->where(['id'=>$u['city_id']])->find();
            $dept_name = '';
            $cityDepartment = \app\city\model\UserModel::getDepartmentMap();
            foreach ($cityDepartment as $v){
                if($v['value']==$u['department']){
                    $dept_name = $v['label'];
                }
            }
            $user['userinfo'] = [
                'user_type' => $user['user_type'],
                'id' => $city['id'],
                'city_name' => $city['name'],
                'pcas' => $city['pcas'],
                'user_id' => $u['id'],
                'name' => $u['name'],
                'dept_id' => $u['department'],
                'dept_name' => $dept_name,
                'role' => $u['role'],
                'head' => 'https://bzh.cdsafety.org.cn/general/toppingsoft/public/wechat/headerImg.png',
                'position' => $city['name'],
            ];
        }else if($user['user_type']=='org'){
            $org = Db::table('top_org')->where(['id'=>$u['org_id']])->find();
            $user['userinfo'] = [
                'user_type' => $user['user_type'],
                'id' => $org['id'],
                'org_name' => $org['name'],
                'pcas' => $org['pcas'],
                'user_id' => $u['id'],
                'name' => $u['name'],
                'role' => $u['role'],
                'head' => 'https://bzh.cdsafety.org.cn/general/toppingsoft/public/wechat/headerImg.png',
                'position' => $org['name'],
            ];
        }else if($user['user_type']=='expert'){
			//调用专家系统数据
			$res = SecsModel::getSecsInterfaceData('secsExpert/getSecsExpertRegistStatus', ['phone'=>$u['mobile'],'name'=>$u['name']],'post');
            $user['userinfo'] = [
                'user_type' => $user['user_type'],
                'user_id' => $u['id'],
				'expert_id'=>$res["data"]["expertId"],
				'regist_status'=>$res["data"]['registStatusDes'],
                'name' => $u['name'],
                'head' => 'https://bzh.cdsafety.org.cn/general/toppingsoft/public/wechat/headerImg.png',
                'position' => '成都市城市安全管理研究院',
            ];
        }
        $user['userinfo']['password'] = $u['password'];
        $user['userinfo']['salt'] = $u['salt'];
        return $user;
    }
}
