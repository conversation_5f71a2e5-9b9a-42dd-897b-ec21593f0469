<?php

namespace app\sso\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use hg\apidoc\annotation as Apidoc;
use app\Request;
use think\App;
use think\facade\Db;

/**
 * @Apidoc\Title("专家端接口")
 * @Apidoc\Group("User")
 * @Apidoc\Sort(3)
 */
class Ca extends Base
{

    /**
     * @Apidoc\Title("证书列表")
     * @Apidoc\Method("POST")
     * @Apidoc\Param ("title", type="string",default="", desc="企业名称")
     * @Apidoc\Param ("page", type="int",default="1",desc="页码")
     * @Apidoc\Param ("limit", type="int",default="100", desc="每页数量")
     * @Apidoc\Returned("data", type="List",desc="证书列表",children={
     * @Apidoc\Returned("company_name", type="string",desc="企业名称"),
     * @Apidoc\Returned("code", type="string",desc="证书编号"),
     * @Apidoc\Returned("industry", type="string",desc="行业"),
     * @Apidoc\Returned("specialty", type="string",desc="专业"),
     * @Apidoc\Returned("level", type="string",desc="证书等级"),
     * @Apidoc\Returned("start", type="string",desc="发证日期"),
     * @Apidoc\Returned("ends", type="string",desc="有效期"),
*     })
     *  @Apidoc\Returned("totle", type="string",desc="数据总数")
     *  @Apidoc\Returned("per_page", type="string",desc="每页数量")
     *  @Apidoc\Returned("current_page", type="string",desc="当前页码")
     *  @Apidoc\Returned("last_page", type="string",desc="最后一页页码")
     */
    function caList($limit=100,$page=1){
        $title = $this->request->param('title','','trim');
        $where = [
            ['status','=',7],
        ];
        if(!empty($title)){
            $where[] = ['company_name','like',"%{$title}%"];
        }
        $fields = "company_name,code,industry,specialty,level,start,ends";
        $res = Db::table('top_certificate')
            ->where($where)->field($fields)->order('start');
        $res = $res->paginate($limit)->each(function ($item, $key) {
            if($item['status']==7){
                $item['status'] = strtotime($item['ends'])>strtotime(date('Y-m-d'))?7:8;
            }
            return $item;
        });
        result($res);
    }

}
