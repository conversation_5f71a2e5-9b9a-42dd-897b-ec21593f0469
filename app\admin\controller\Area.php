<?php

namespace app\admin\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use app\BaseController;
use hg\apidoc\annotation as Apidoc;
use think\facade\View;
use think\Request;
use think\facade\Db;
use app\admin\model\TopAreaModel;
use app\admin\model\TopAreaUserModel;
use app\admin\model\TopPcaModel;
use app\model\ExcelModel;

/**
 * @Apidoc\Title("企业管理")
 * @Apidoc\Group("Index")
 * @Apidoc\Sort(1)
 */
class Area extends BaseController
{

	/**
	 * @Apidoc\Title("区县应急局首页")
	 * @Apidoc\Desc("区县应急局首页")
	 * @Apidoc\Method("POST")
	 * @Apidoc\Tag("首页")
	 * @Apidoc\Returned("data", type="object", desc="基础数据")
	 */
	public function index()
	{
		View::assign('title', '首页');
		return view();
	}

	/**
	 * 获取列表数据
	 */
	public function get_list()
	{
		$param = $this->request->param();
		$model = new TopAreaModel();
		$data = $model->getList($param, $param['page'], $param['limit']);
		$pcaModel = new TopPcaModel();
		result(['list' => $data, 'pca' => $pcaModel->getPcaList(3)]);
	}

	/**
	 * 保存数据
     * @sysLog  保存区县数据
	 */
	public function save_data()
	{
		$param = $this->request->param();
		$model = new TopAreaModel();
		$id = intval($param['id']);
		if ($id) $model = $model::find($id);
		$data = $model->newSave($model, $param, $id);
		result(null, 0, $data);
	}

	/**
	 * 删除
     * @sysLog  保存区县数据
	 */
	public function delete()
	{
		$param = $this->request->param();
		$id = intval($param['id']);
		$model = TopAreaModel::find($id);
		$data = $model->delData($model);
		result(null, 0, $data);
	}

	/**
	 * 导出
     * @sysLog  导出区县数据
	 */
	public function export()
	{
		$param = $this->request->param();
		$model = new TopAreaModel();
		$data = $model->getList($param, 0, 0, true);
		$title = $model->getTitle();
		ExcelModel::exportExcel($title, $data, '区县应急局基本信息导出', true);
	}

	/**
	 * 导入模板
	 */
	public function importTemplate()
	{
		$model = new TopAreaModel();
		$title = $model->getTitle();
		$ord = ord('A');
		foreach ($title as $k => $v) {
			if ($v['field'] == 'statusStr' || $v['field'] == 'pcas_names') {
				unset($title[$k]);
				continue;
			}
			$title[$k]['field'] = chr($ord);
			$ord += 1;
		}
		ExcelModel::exportExcel(array_merge_recursive($title), [], '区县应急局基本信息导入模板');
	}

	/**
	 * 导出
	 */
	public function import()
	{
		$data = import("input");
		$dataHeader = importHeader("input");
		$model = new TopAreaModel();
		$result = $model->importData($data, $dataHeader);
		$msg = count($result['success']) . '条数据保存成功，' . count($result['repeat']) . '条数据重复导入，' . count($result['fail']) . '条数据处理失败。';
		result($result, 0, $msg);
	}


	public function personnel()
	{
		View::assign('title', '用户管理');
		return view();
	}

	/**
	 * 获取列表数据
	 */
	public function get_list_p()
	{
		$param = $this->request->param();
		$model = new TopAreaUserModel();
		$areaModel = new TopAreaModel();
		$data = $model->getList($param, $param['page'], $param['limit']);
		result(array('list' => $data, 'area' => $areaModel->getList([], 0, 0, true), 'department' => ['label' => '基础科', 'value' => 1], ['label' => '危化科', 'value' => 2], ['label' => '商务楼宇', 'value' => 3], 'role' => config('global.role')));
	}

	/**
	 * 保存数据
     * @sysLog  保存区县用户数据
	 */
	public function save_data_p()
	{
		$param = $this->request->param();
		$model = new TopAreaUserModel();
		$id = intval($param['id']);
		if ($id) $model = $model::find($id);
		$data = $model->newSave($model, $param, $id);
		result(null, 0, $data);
	}

	/**
	 * 删除
     * @sysLog  删除区县用户数据
	 */
	public function delete_p()
	{
		$param = $this->request->param();
		$id = intval($param['id']);
		$model = TopAreaUserModel::find($id);
		$data = $model->delData($model);
		result(null, 0, $data);
	}

	/**
	 * 导出
     * @sysLog  导出区县用户数据
	 */
	public function export_p()
	{
		$param = $this->request->param();
		$model = new TopAreaUserModel();
		$data = $model->getList($param, 0, 0, true);
		$title = $model->getTitle();
		ExcelModel::exportExcel($title, $data, '区县应急局用户信息导出', true);
	}

	/**
	 * 导入模板
	 */
	public function importTemplate_p()
	{
		$model = new TopAreaUserModel();
		$areaModel = new TopAreaModel();
		$title = $model->getTitle();
		$ord = ord('A');
		foreach ($title as $k => $v) {
			if ($v['field'] == 'statusStr' || $v['field'] == 'salt' || $v['field'] == 'reg_time' || $v['field'] == 'reg_ip') {
				unset($title[$k]);
				continue;
			}
			if ($v['field'] == 'deptName') {
				$title[$k]['type'] = 'list';
                $deptArr = \app\area\model\UserModel::getDepartmentMap();
				foreach ($deptArr as $item) {
					$dept[] = $item['label'];
				}
				$title[$k]['listData'] = $dept;
			}
			if ($v['field'] == 'roleName') {
				$title[$k]['type'] = 'list';
				$roleArr = $model->role_arr;
				foreach ($roleArr as $item) {
					$role[] = $item['label'];
				}
				$title[$k]['listData'] = $role;
			}
			if ($v['field'] == 'areaName') {
				$title[$k]['type'] = 'list';
				$areaArr = $areaModel->getList([], 0, 0, true);
				foreach ($areaArr as $item) {
					$area[] = $item['name'];
				}
				$title[$k]['listData'] = $area;
			}

			$title[$k]['field'] = chr($ord);
			$ord += 1;
		}
//		dd(array_merge_recursive($title));die;
		$data = array(array('姓名', '用户名', '电话', '邮箱', '所属应急局', '部门', '角色'));

		ExcelModel::exportExcel(array_merge_recursive($title), $data, '区县应急局用户信息导入模板');
	}

	/**
	 * 导入人员
     * sysLog  导入区县人员数据
	 */
	public function import_p()
	{
		$data = import("input");
		$dataHeader = importHeader("input");
		$model = new TopAreaUserModel();
		$result = $model->importData($data, $dataHeader);
		$msg = count($result['success']) . '条数据保存成功，' . count($result['repeat']) . '条数据重复导入，' . count($result['fail']) . '条数据处理失败。';
		result($result, 0, $msg);
	}

	/**
	 * 修改密码
     * @sysLog  修改区县人员密码
	 */
	public function update_password(){
		$param = $this->request->param();
		$id = intval($param['id']);
		$model = TopAreaUserModel::find($id);
		$data = $model->updatePassword($model, $param['password']);
		result(null, 0, $data);
	}

}
