<?php


namespace app\city\model;


use app\model\FileModel;
use think\Model;
use think\App;
use think\facade\Db;
use app\model\SettingModel;

//权限配置模块
class UserModel extends Model
{

    //参数格式化
    public static function codeToText($info, $depType = 'v1')
    {
        if (empty($info)) {
            return $info;
        }

        // 1. 获取部门映射
        $departmentMap = self::getDepartmentMap($depType);
        $info['dept_name'] = self::mapValueToLabel($departmentMap, $info['department'] ?? null);

        // 2. 获取角色映射
        $roleMap = config('global.role', []);
        $role = $info['role'] ?? $info['ROLE'] ?? null;
        $info['role_name'] = self::mapValueToLabel($roleMap, $role);

        // 3. 角色统一处理
        $info['role'] = is_numeric($role) ? $role : ($info['role'] ?? $info['ROLE'] ?? '');

        // 4. 状态转换
        $info['status_name'] = ($info['status'] ?? 0) == 1 ? '正常' : '禁用';

        // 5. 头像处理
        $info['headUrl'] = empty($info['head']) ? '' : FileModel::getFile(0, $info['head']);

        // 6. 字段清理（null、空日期、保留数字类型）
        foreach ($info as $k => $v) {
            if ($v === null || $v === 'null') {
                $info[$k] = '';
            } elseif ($v === '0000-00-00') {
                $info[$k] = '';
            } elseif (is_int($v)) {
                // 保留整数类型
            } elseif (is_string($v)) {
                // 保留原始字符串
            } else {
                $info[$k] = (string)$v;
            }
        }

        return $info;
    }

    public static function getDepartmentMap($depType = 'v1')
    {
        $depTypeArr = [
            //version,不同时期的维护，保留具体版本叫法
            'v1' => [
                ['label' => '基础处', 'value' => 1],
                ['label' => '危化处', 'value' => 2],
                ['label' => '商务楼宇', 'value' => 3],
            ],
        ];

        return $depTypeArr[$depType] ?? [];
    }

    private static function mapValueToLabel($map, $value)
    {
        if (empty($map) || $value === null) {
            return '';
        }

        foreach ($map as $item) {
            if ((string)$item['value'] === (string)$value) {
                return $item['label'];
            }
        }

        return '';
    }

}