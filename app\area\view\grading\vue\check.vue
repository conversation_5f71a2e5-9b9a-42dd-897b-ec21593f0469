<style>
.my-autocomplete li{line-height: normal;padding: 7px;}
.my-autocomplete li .name{text-overflow: ellipsis;overflow: hidden;}
.my-autocomplete li .addr{font-size: 12px;color: #b4b4b4;}
.my-autocomplete li .highlighted{color: #ddd;}
.mytable {border-collapse:collapse;width: 100%;}
.mytable thead tr td {border: 1px solid #333;font-weight: 700; padding: 10px;background: #f5f7fa;text-align: center;}
.mytable tbody tr th {border: 1px solid #333;font-weight: 700; padding: 10px;text-align: center;font-size: 16px;}
.mytable tbody tr td {border: 1px solid #333; padding: 10px;text-align: center;font-size: 16px;font-family: 宋体;text-align: left;}
.mytable .active td{ background: #f2f2f2;}
.mytable tbody tr td p{line-height: 30px;}
.el-image {
  position: relative;
  display: inline-block;
  overflow: hidden;
  width: 150px;
  height: 100px;
  border: 1px dashed #d9d9d9;
}
</style>
<template>
  <el-dialog :title="title" :close-on-click-modal="false"  modal="false" :visible.sync="visible" width="95%" top="10px" @close="refresh()" append-to-body="true" label-position="top">
    <el-steps :active="data.stepsActive" finish-status="finish" align-center>
      <el-step v-for="item in data.steps" :title="item.label" :status="item.status"></el-step>
    </el-steps>
    <el-tabs type="border-card" v-loading.fullscreen.lock="loading">
<!--      <el-tab-pane v-if="" label="整改详情" :style="{overflow:'auto',height: height+'px'}">
        <el-form v-for="item in data.reform" label-width="150px" class="demo-ruleForm">
          <el-form-item label="整改时间：" prop="status">
            {{item.end_time}}
          </el-form-item>
          <el-form-item label="整改说明：" prop="content">
            {{item.remark}}
          </el-form-item>
          <el-form-item label="整改报告：" prop="content">
            <p v-for="file in item.files"><el-link @click="preview(file)" type="primary">{{file.name}}</el-link></p>
          </el-form-item>
        </el-form>
      </el-tab-pane>-->
      <el-tab-pane label="申请详情" :style="{overflow:'auto',height: height+'px'}">
        <div id="print-content">
          <h3 style="height: 40px;line-height: 40px;text-align: center;">企业安全生产标准化定级申请表</h3>
          <table class="mytable">
            <tbody>
            <tr>
              <th style="width: 120px;"><label class="my-label">企&nbsp;业&nbsp;名&nbsp;称</label></th>
              <td colspan="5" style="width: 700px;"><div class="my-online">{{data.company_name}}</div></td>
            </tr>
            <tr>
              <th style="width: 120px;"><label class="my-label">住&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;所</label></th>
              <td colspan="5" style="width: 700px;"><div class="my-online">{{data.address}}</div></td>
            </tr>
            <tr>
              <th style="width: 120px;"><label class="my-label">统&nbsp;一&nbsp;社&nbsp;会<br/>信&nbsp;用&nbsp;代&nbsp;码</label></th>
              <td colspan="5" style="width: 700px;"><div class="my-online">{{data.company_code}}</div></td>
            </tr>
            <tr>
              <th style="width: 120px;"><label class="my-label">法定代表人</label></th>
              <td style="width: 200px;"><div class="my-online">{{data.legal}}</div></td>
              <th style="width: 120px;"><label class="my-label">电话</label></th>
              <td colspan="3" style="width: 500px;"><div class="my-online">{{data.legal_mobile}}</div></td>
            </tr>
            <tr>
              <th style="width: 120px;"><label class="my-label">联&nbsp;&nbsp;&nbsp;系&nbsp;&nbsp;&nbsp;人</label></th>
              <td style="width: 200px;"><div class="my-online">{{data.manager}}</div></td>
              <th style="width: 120px;"><label class="my-label">电话</label></th>
              <td style="width: 200px;"><div class="my-online">{{data.manager_mobile}}</div></td>
              <th style="width: 120px;"><label class="my-label">电子邮箱</label></th>
              <td style="width: 200px;"><div class="my-online">{{data.manager_email}}</div></td>
            </tr>
            <tr>
              <th style="width: 120px;"><label class="my-label">申&nbsp;请&nbsp;类&nbsp;型</label></th>
              <td colspan="5" style="width: 700px;">
                <template v-if="data.type=='初次申请'">
                  <label>☑ 初次申请</label>
                  <label style="margin-left: 20px;">□ 复评申请</label>
                </template>
                <template v-if="data.type=='复评申请'">
                  <label>□ 初次申请</label>
                  <label style="margin-left: 20px;">☑ 复评申请</label>
                </template>
              </td>
            </tr>
            <tr>
              <th style="width: 120px;"><label class="my-label">创&nbsp;建&nbsp;性&nbsp;质</label></th>
              <td colspan="5" style="width: 700px;">
                <template v-if="data.nature==0">
                  <label>☑ 自主创建</label>
                  <label style="margin-left: 20px;">□ 第三方机构指导</label>
                </template>
                <template v-if="data.nature==1">
                  <label>□ 自主创建</label>
                  <label style="margin-left: 20px;">☑ 第三方机构指导</label>
                  {{data.advisory}}
                </template>
              </td>
            </tr>
            <tr>
              <th style="width: 120px;" colspan="6"><label class="my-label">附&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;件</label></th>
            </tr>
            <tr>
              <td colspan="6" style="text-align: left;">
                <div v-for="(item,key) in files" style="min-height:60px;">
                  <p>{{key+1}}、{{item.title}}</p>
                  <p v-for="file in data[item.field]">
                    <el-link @click="preview(file)" type="primary">{{file.name}}</el-link>
                  </p>
                </div>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </el-tab-pane>
      <el-tab-pane label="审批记录" :style="{height:height+'px'}">
        <el-table border
                  v-loading="loading"
                  :data="data.prcs"
                  style="width: 100%;margin-bottom: 20px;"
                  ref="qtable"
                  :height="height"
                  size="small">
          <el-table-column
              type="index"
              label="序号"
              align="center"
              width="50">
          </el-table-column>
          <el-table-column
              prop="prcs_name"
              label="办理阶段"
              align="center"
              show-overflow-tooltip
              width="250">
          </el-table-column>
          <el-table-column
              prop="status_name"
              label="办理状态"
              align="center"
              width="100">
          </el-table-column>
          <el-table-column
              prop="end_time"
              label="办理时间"
              align="center"
              width="150">
          </el-table-column>
          <el-table-column
              prop="files"
              label="附件"
              align="center"
              min-width="100">
            <template slot-scope="scope">
              <p v-for="item in scope.row.files"><el-link @click="preview(item)" type="primary" v-html="item.name"></el-link></p>
            </template>
          </el-table-column>
          <el-table-column
              prop="remark"
              label="备注"
              align="center"
              min-width="100">
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="整改详情" :style="{overflow:'auto',height: height+'px'}">
        <el-table
            :data="tableData"
            style="width: 100%">
          <el-table-column
              type="index"
              label="序号"
              align="center"
              width="50">
          </el-table-column>
          <el-table-column
              prop="reform"
              label="整改内容"
              min-width="180">
          </el-table-column>
          <el-table-column
              prop="reform_date"
              label="整改时间"
              width="150">
          </el-table-column>
          <el-table-column
              prop="name"
              label="整改前情况"
              min-width="380">
            <template slot-scope="scope">
              <!--                            <el-input v-model="scope.row.reform_before_info" disabled type="textarea" :autosize="{minRows: 2}"></el-input>-->
              <div>{{scope.row.reform_before_info}}</div>
              <div class="demo-image__lazy">
                <el-image fit="fill" v-for="(item, index) in scope.row.reform_before_files" :preview-src-list="scope.row.reform_before_files_preview" :key="item.url" :src="item.url"></el-image>
              </div>
            </template>
          </el-table-column>
          <el-table-column
              prop="address"
              label="整改后情况"
              min-width="380">
            <template slot-scope="scope">
              <div>{{scope.row.reform_affter_info}}</div>
              <!--                            <el-input v-model="scope.row.reform_affter_info" disabled type="textarea" :autosize="{minRows: 2}"></el-input>-->

              <div class="demo-image__lazy">
                <el-image fit="scale-down" v-for="(item, index) in scope.row.reform_affter_files" :preview-src-list="scope.row.reform_affter_files_preview" :key="item.url" :src="item.url"></el-image>
              </div>
            </template>
          </el-table-column>
          <el-table-column
              prop="name"
              label="备注"
              min-width="200">
            <template slot-scope="scope">
              <div>{{scope.row.remark}}</div>
              <!--                            <el-input v-model="scope.row.remark" disabled type="textarea" :autosize="{minRows: 2}" placeholder="请输入备注"></el-input>-->
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
    <div slot="footer" class="dialog-footer">
        <el-button v-if="data.prcs_id==2||data.prcs_id==6" type="primary" @click="submit(7)" size="small">审核通过</el-button>
        <el-button v-if="data.prcs_id==2||data.prcs_id==6" type="danger" @click="submit(5)" size="small">驳回</el-button>
        <el-button @click="visible = false" size="small">关闭</el-button>
    </div>
    <el-dialog width="600" title="审核" :visible.sync="dialogFormVisible" :close-on-click-modal="false" append-to-body>
      <el-form :model="form" :rules="rules" ref="form" label-width="150px" class="demo-ruleForm">
        <el-form-item label="审批状态" prop="status">
          <el-radio v-model="form.status" :label="7">审批通过</el-radio>
          <el-radio v-model="form.status" :label="5">驳回</el-radio>
        </el-form-item>
        <el-form-item label="审批意见" prop="content">
          <el-input type="textarea" :rows="3" placeholder="请输入内容" v-model="form.content"></el-input>
        </el-form-item>
        <el-form-item v-if="form.status==7" :label="filename" prop="files">
          <el-upload
              class="upload-demo"
              action="upload"
              :on-success="uploadSuccess"
              :before-upload="uploadBefore"
              :on-remove="handleRemove"
              :on-preview="preview"
              multiple
              :limit="3"
              :file-list="form.files">
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png/pdf文件，且不超过2M</div>
          </el-upload>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit" v-loading.fullscreen.lock="loading">确定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <preview ref="preview"></preview>

    <el-dialog :visible.sync="dialogVisible" :modal=true>
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>

  </el-dialog>
</template>
<script>
module.exports = {
  name: "add",
  // 模板导入区
  components: {
    'preview': 'url:/general/toppingsoft/public/vue/preview.vue',
  },
  data:function() {
    return {
      id:0,
      isAdmin:false,
      visible: false,
      dialogFormVisible: false,
      title: '详情',
      loading: false,
      noMore: false,
      user_id: 0,
      filename: '企业定级申请表（区县级主管部门盖章扫描件）',
      data: {
        stepsActive:[],
      },
      form: {
        status:'',
        content:'',
        files:[],
      },
      is_see:0,
      files:[],
      details:[],
      cards:{},
      type:{},
      pcas: [],
      rules:[],
      restaurants2:[],
      height: document.documentElement.clientHeight - 370,
      dialogImageUrl: '',
      dialogVisible: false,
      tableData: [],
      imageList: [],
    }
  },
  computed: {
    disabled () {
      return this.loading || this.noMore
    }
  },
  mounted: function(){
    //this.getConfig();
  },
  created:function(){
  },
  methods: {
    getReformInfo:function(grading_id){
      var _this = this;
      _this.loading = true;
      axios.post('getReformInfo', {grading_id: grading_id}).then(function (res) {
        _this.loading = false;
        if (res.data.code == 0) {
          _this.tableData = res.data.data;
        }
      }).catch(function (error) {
        _this.loading = false;
        console.log("出现错误:",error);
      });
    },
    /**
     * 打开弹窗调用方法
     * */
    open: function (row) {
      var _this =this;
      _this.id = row.id;
      _this.visible = true;
      _this.noMore = false;
      _this.getInfo(row.id);
      _this.getReformInfo(row.id);
    },
    closeDialog: function () {
      this.visible = false;
    },
    refresh: function () {

    },
    uploadBefore(file) {
      console.log(file)
      const isJPG = file.type === 'image/jpeg';
      const isPNG = file.type === 'image/png';
      const isPDF = file.type === 'application/pdf';
      if(!isJPG&&!isPNG&&!isPDF){
        this.$message.error('请上传jpg/png/pdf文件');
      }
      return isJPG||isPNG||isPDF;
    },
    uploadSuccess(res, file,fileList) {
      var files = [];
      for(var i in fileList){
        files.push(fileList[i].response.data??fileList[i]);
      }
      this.form.files = files;
    },
    handleRemove(file, fileList) {
      var files = [];
      for(var i in fileList){
        files.push(fileList[i].response.data??fileList[i]);
      }
      this.form.files = files;
    },
    preview: function (file) {
      file = file.response?file.response.data:file;
      this.$refs.preview.open(file.url,file.name);
    },
    submit:function(status){
      var _this = this;
      _this.form.status = status;
      _this.dialogFormVisible = true;
    },
    onSubmit:function(status){
      var _this = this;
      var param = _this.form;
      param.id = _this.id;
      if(param.status==5&&param.content==''){
        _this.$message({
          message: '请填写驳回原因',
          type: 'error'
        });
        return false;
      }
      _this.loading = true;
      axios.post("gradingCheck", param).then(function (res) {
        _this.$message({
          message: res.data.msg,
          type: res.data.type
        });
        if (res.data.code == 0) {
          _this.visible = false;
          _this.dialogFormVisible = false;
          window.parent.getMessage();
          _this.$emit("ok");
        }
        _this.loading = false;
      }).catch(function (error) {
        console.log("出现错误:",error);
      });
    },
    getInfo:function(id){
      var _this = this;
      if(id){
        _this.loading = true;
        axios.post("gradingInfo", {id:id}).then(function (res) {
          if (res.data.code == 0) {
            _this.data=res.data.data;
            _this.files=res.data.data.files;
            if(_this.data.prcs_id==2){
              _this.filename = '企业定级申请表（区县级主管部门盖章扫描件）';
            }else if(_this.data.prcs_id==6){
              _this.filename = '现场检查复核意见书';
            }
          }else {
            _this.$message({
              message: res.data.msg,
              type: res.data.type
            });
          }
          _this.loading = false;
        }).catch(function (error) {
          console.log("出现错误:",error);
        });
      }
    },
    addContacts: function(){
      this.form = {};
      this.dialogFormVisible = true;
    },
    editContacts: function(row){
      this.form = row;
      this.dialogFormVisible = true;
    },
  }
}
</script>


