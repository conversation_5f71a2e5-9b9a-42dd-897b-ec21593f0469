<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;
use app\admin\model\CompanyModel;

/**
 * @mixin \think\Model
 */
class TopGrading extends Model
{
    //



	public function company(){
		return $this->belongsTo(CompanyModel::class, 'company_id', 'id');
	}
	public function gradingApproval()
	{
		return $this->hasOne(TopGradingApproval::class, 'grading_id', 'id');
	}
	public function tasks()
	{
		return $this->hasOne(TopOrgTasks::class, 'grading_id', 'id');
	}


	private $current_prcs_id = 2;
	private $_prefix = 'a';

	public function _setOption($param, $value)
	{
		if (property_exists(new self(), $param)) {
			$this->{$param} = $value;
		}
	}

	public function getList($where, $page = 1, $limit = 10)
	{
		$whereArr[] = ['params', '=', $this->_prefix . ':' . $where['id']];
		$whereArr[] = ['prcs_id', '=', $this->current_prcs_id];
		$whereG = [];
		if (isset($where['name']) && $where['name'] != '') $whereG[] = ['company_name', 'like', '%'.$where['name'].'%'];
		$gModel = new TopGrading();
		$data = $this->where($whereG)->hasWhere('gradingApproval', function($query) use ($whereArr){
			$query->where($whereArr);
		})->group('topGrading.id')->paginate($limit)->each(function ($item, $index) use ($gModel) {
			$item = $this->getInfo($item, $gModel);
			return $item;
		});
		return $data;
	}



	public function getInfo($item, &$model)
	{
		$gradingApproval = $item->gradingApproval ? $item->gradingApproval->toArray() : [];
		$item->company = $item->company ? $item->company->toArray() : [];
		$item->gradingApproval = $gradingApproval;
		$item->apply_time = $item->apply_time ? date('Y-m-d H:i:s', strtotime($item->apply_time)) : '';
		$item->statusStr = $this->getStatus($item->gradingApproval['status']);
		$item->tasks = $item->tasks ? $item->tasks : [];
		return $item;
	}

	public function getStatus($status)
	{
		$str = '';
		switch ($status) {
			case 1:
				$str = '未接收';
				break;
			case 2:
				$str = '已接收';
				break;
			case 5:
				$str = '已驳回';
				break;
			case 7:
				$str = '已审核';
				break;
			default:
				break;
		}
		return $str;
	}
}
