<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>第三方登录</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <!--引入原oa js-->
    <script src="/static/js/jquery/jquery-with-migrate.js"></script>
</head>
<body style="background: none;">
<div id="app" v-cloak>
    <div class="centainer">
        <!--表头区-->
        <h3 style="text-align: center;">
            登录
        </h3>
        <el-form :model="form" status-icon :rules="rules" ref="form" label-width="60px" class="demo-ruleForm">
            <el-form-item label="账号" prop="username">
                <el-input v-model="form.username" autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item label="密码" prop="password">
                <el-input type="password" v-model="form.password" autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="submitForm()" v-loading.fullscreen.lock="loading">登录</el-button>
            </el-form-item>
        </el-form>
    </div>
</div>
</body>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>


<script src="__PUBLIC__/static/js/es6-promise.auto.min.js"></script>
<script src="__PUBLIC__/static/js/browser.min.js"></script>
<script src="__PUBLIC__/static/js/polyfill.min.js"></script>

<script type="text/babel">
    Vue.use(httpVueLoader);
    new Vue({
        el: '#app',
        // 模板导入区
        components: {
            'info': 'url:/general/toppingsoft/app/UserInfo/view/vacation/module/flow_info.vue?v=1',
        },
        data: function () {
            return {
                loading: false,
                form:{
                    appid: '{$appid}',
                    username: '',
                    password: '',
                },
                rules: {
                    username: [{required: true, message: "请输入账号", trigger: "blur"}],
                    // password: [{required: true, message: "空密码账户暂不支持绑定", trigger: "blur"}],
                },
            }
        },
        methods: {
            submitForm: function () {
                var _this = this;
                this.$refs.form.validate(valid => {
                    if (valid) {
                        _this.loading = true;
                        var param = _this.form;
                        param._ajax = 1;
                        axios.post('login', param).then(function (res) {
                            _this.loading = false;
                            if (res.data.code == 0) {
                                window.parent.bindend(res.data.data);
                            } else {
                                _this.$message({
                                    message: res.data.msg,
                                    type: "error"
                                });
                            }
                        }).catch(function (error) {
                            _this.loading = false;
                            console.log(error);
                        });
                    }
                });
            },
        },
    })
</script>
</html>



