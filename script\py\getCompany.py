import requests
import sqlite3
import os
from time import sleep
from datetime import datetime

# 获取当前年份和月份
current_date = datetime.now()
current_year_month = current_date.strftime('%Y-%m')

# API配置
API_URL = 'http://10.1.235.89:2683/open-api/GjZdPkdxCoeyV9XsxAE9Fddglq7wF47S/SCZTJBXX'
API_KEY = '878940'
API_SIGNATURE = '71d5e0df597371e14369d20686a0d8964c2b4061f8e1d3cf60936307feb7ee17'

# 数据库配置
DB_FILE = 'company_data.db'
BATCH_SIZE = 1000  # 每次API调用获取的记录数
MAX_RETRIES = 3    # API调用失败的最大重试次数
RETRY_DELAY = 5    # 重试之间的等待时间(秒)

headers = {
    'X-Ca-Key': API_KEY,
    'X-Ca-Signature': API_SIGNATURE
}

def create_database():
    """
    创建SQLite数据库和表结构
    
    功能: 初始化数据库连接，创建公司数据表
    参数: 无
    返回值: 无
    """
    # 检查数据库文件是否已存在
    if os.path.exists(DB_FILE):
        print(f"数据库文件 {DB_FILE} 已存在，跳过创建表结构")
        return
        
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()
    
    cursor.execute('''CREATE TABLE IF NOT EXISTS company_data (
        areacode TEXT, -- 行政区划代码
        clrq TEXT, -- 成立日期
        djjgzwmc TEXT, -- 登记机关（中文名称）
        djjg TEXT, -- 登记机关
        djzt TEXT, -- 登记状态
        djztzwmc TEXT, -- 登记状态（中文名称）
        fddbr TEXT, -- 法定代表人（负责人）
        hyml TEXT, -- 行业门类
        hydm TEXT, -- 行业代码
        jyfw TEXT, -- 经营范围
        jyqxs TEXT, -- 经营(驻在)期限自
        scztlx TEXT, -- 市场主体类型
        scztlxzwmc TEXT, -- 市场主体类型（中文名称）
        tyshxydm TEXT PRIMARY KEY, -- 统一社会信用代码
        xzqh TEXT, -- 行政区划
        zhrc_sjly TEXT, -- 智慧蓉城数据来源
        ztmc TEXT, -- 主体名称
        zsszxzqh TEXT, -- 住所所在行政区划
        zczbbzzwmc TEXT, -- 注册资本(金)币种（中文名称）
        ztsfdm TEXT, -- 主体身份代码
        zhrc_wyzj TEXT, -- 智慧蓉城唯一主键
        ztid TEXT, -- 主体ID
        zch TEXT, -- 注册号
        zs TEXT, -- 住所(地区)
        zhrc_rksj TEXT, -- 智慧蓉城入库时间
        zczb TEXT -- 注册资本(金)(地区)
    )''')
    
    conn.commit()
    conn.close()

def fetch_data(start, limit):
    """
    从API分页获取数据
    
    参数:
        start: 起始记录位置
        limit: 每次获取的记录数
    
    返回值:
        成功: 返回获取的数据列表
        失败: 返回None
    """
    params = {
        'SJ': current_year_month,
        'limit': limit,
        'start': start
    }
    
    for attempt in range(MAX_RETRIES):
        try:
            response = requests.get(API_URL, headers=headers, params=params)
            response.raise_for_status()
            data = response.json()
            return data['data']
        except Exception as e:
            print(f"Attempt {attempt + 1} failed: {str(e)}")
            if attempt < MAX_RETRIES - 1:
                sleep(RETRY_DELAY)
    return None

def save_to_database(data):
    """
    将获取的数据保存到SQLite数据库
    
    参数:
        data: 要保存的数据列表
    
    返回值: 无
    """
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()
    
    for record in data:
        # Extract values from the record
        values = [
            record.get('dbo.YSK_SCJDGLJ_SCZTJBXX.areacode', ''),
            record.get('dbo.YSK_SCJDGLJ_SCZTJBXX.clrq', ''),
            record.get('dbo.YSK_SCJDGLJ_SCZTJBXX.djjgzwmc', ''),
            record.get('dbo.YSK_SCJDGLJ_SCZTJBXX.djjg', ''),
            record.get('dbo.YSK_SCJDGLJ_SCZTJBXX.djzt', ''),
            record.get('dbo.YSK_SCJDGLJ_SCZTJBXX.djztzwmc', ''),
            record.get('dbo.YSK_SCJDGLJ_SCZTJBXX.fddbr', ''),
            record.get('dbo.YSK_SCJDGLJ_SCZTJBXX.hyml', ''),
            record.get('dbo.YSK_SCJDGLJ_SCZTJBXX.hydm', ''),
            record.get('dbo.YSK_SCJDGLJ_SCZTJBXX.jyfw', ''),
            record.get('dbo.YSK_SCJDGLJ_SCZTJBXX.jyqxs', ''),
            record.get('dbo.YSK_SCJDGLJ_SCZTJBXX.scztlx', ''),
            record.get('dbo.YSK_SCJDGLJ_SCZTJBXX.scztlxzwmc', ''),
            record.get('dbo.YSK_SCJDGLJ_SCZTJBXX.tyshxydm', ''),
            record.get('dbo.YSK_SCJDGLJ_SCZTJBXX.xzqh', ''),
            record.get('dbo.YSK_SCJDGLJ_SCZTJBXX.zhrc_sjly', ''),
            record.get('dbo.YSK_SCJDGLJ_SCZTJBXX.ztmc', ''),
            record.get('dbo.YSK_SCJDGLJ_SCZTJBXX.zsszxzqh', ''),
            record.get('dbo.YSK_SCJDGLJ_SCZTJBXX.zczbbzzwmc', ''),
            record.get('dbo.YSK_SCJDGLJ_SCZTJBXX.ztsfdm', ''),
            record.get('dbo.YSK_SCJDGLJ_SCZTJBXX.zhrc_wyzj', ''),
            record.get('dbo.YSK_SCJDGLJ_SCZTJBXX.ztid', ''),
            record.get('dbo.YSK_SCJDGLJ_SCZTJBXX.zch', ''),
            record.get('dbo.YSK_SCJDGLJ_SCZTJBXX.zs', ''),
            record.get('dbo.YSK_SCJDGLJ_SCZTJBXX.zhrc_rksj', ''),
            record.get('dbo.YSK_SCJDGLJ_SCZTJBXX.zczb', '')
        ]
        
        # Insert or replace record
        cursor.execute('''INSERT OR REPLACE INTO company_data VALUES (
            ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?
        )''', values)
    
    conn.commit()
    conn.close()

def main():
    """
    主函数，协调整个数据获取和存储流程
    
    功能:
        1. 创建数据库
        2. 分批次从API获取数据
        3. 将数据保存到数据库
    """
    print("Creating database...")
    create_database()
    
    print("Starting data fetch...")
    total_records = 2006202  # 总记录数，可根据API响应调整
    processed = 0
    
    while processed < total_records:
        print(f"Fetching records {processed} to {processed + BATCH_SIZE}...")
        data = fetch_data(processed, BATCH_SIZE)
        
        if not data:
            print(f"Failed to fetch records {processed} to {processed + BATCH_SIZE}")
            break
            
        print(f"Saving {len(data)} records...")
        save_to_database(data)
        processed += len(data)
        
        # Throttle requests to avoid overwhelming the API
        sleep(1)
    
    print(f"Completed! Processed {processed} records in total.")

if __name__ == "__main__":
    main()