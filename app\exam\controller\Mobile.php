<?php

namespace app\exam\controller;


use think\facade\Db;
use think\Exception;
use think\Request;
use think\facade\View;

class Mobile extends Base
{

    public function joinText(Request $request)
    {
        try {

            $task_id = $request->param('task_id', 0);
            $sign = $request->param('sign', '');
            $standard_id = $request->param('standard_id', '');
            // dd($task_id, $sign, $industry);
            if (empty($task_id) || empty($sign) || empty($standard_id)) {
                throw new Exception("参数有误");
            }
            if (empty($standard_id)) {
                throw new Exception("评审标准为空");
            }
            $company_name = Db::table('top_org_tasks')->where('id', $task_id)->value('company_name');
            return View::fetch('index', ['task_id' => $task_id, 'sign' => $sign, 'company_name' => $company_name, 'standard_id' => $standard_id,]);
        } catch (Exception $e) {
            echo '<h1>' . $e->getMessage() . '</h1>';
        }
    }

    public function myd($id=0){
        $re = Db::table('top_org_tasks')->where(['id'=>$id])->find();
        View::assign('result',$re);
        return view();
    }

    public function mydSave(Request $request){
        $params = $request->param('', '','trim');
        $data = [
            'tasks_id' => $params['task_id'],
            'company_id' => $params['company_id'],
            'company_name' => $params['company_name'],
            'field2' => $params['field2'],
            'field3' => $params['field3'],
            'field4' => $params['field4'],
            'field5' => $params['field5'],
            'field6' => $params['field6'],
            'field7' => $params['field7'],
        ];
        Db::table('top_org_tasks_myd')->insert($data);
        result();
    }


}