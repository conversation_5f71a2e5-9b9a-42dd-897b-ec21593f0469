<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{$title}</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .el-tree-node__content { height: 40px;}
        .custom-tree-node { display: inline-block;width: 100%;}
        .mytable {border-collapse:collapse;width: 100%;}
        .mytable thead tr td {border: 1px solid #333;font-weight: 700; padding: 10px;background: #f5f7fa;text-align: center;}
        .mytable tbody tr td {border: 1px solid #333; padding: 10px;text-align: center;}
        .mytable .active td{ background: #bcbec2;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">
        <el-row :gutter="10">
            <el-col v-show="false" :span="6" style="background: #ffffff;width: 275px;overflow: auto;" :style="{'height' : (height-20)+'px'}">
                <el-row :gutter="10" style="margin: 10px 0;">
                    <el-col :span="20"><el-input placeholder="输入关键字进行过滤" v-model="filterText" size="mini"></el-input></el-col>
                    <el-col :span="4"><el-button type="primary" icon="el-icon-plus" size="mini" @click="nameAdd()"></el-button></el-col>
                </el-row>
                <el-tree
                        :data="nameList"
                        node-key="id"
                        :current-node-key="currentKey"
                        default-expand-all
                        :highlight-current="true"
                        :expand-on-click-node="false"
                        :filter-node-method="filterNode"
                        @node-click="active"
                        ref="tree">
                      <span class="custom-tree-node" slot-scope="{node, data}">
                          <el-tooltip class="item" effect="dark" :content="data.name" placement="top">
                            <span style="display: inline-block;width: 170px;overflow: hidden;white-space:nowrap; text-overflow: ellipsis;"><i class="el-icon-caret-right"></i>{{data.name}}</span>
                          </el-tooltip>
                        <span style="float: right;">
                          <el-button style="padding: 0;" type="text" size="mini" @click="nameUpdate(data)">编辑</el-button>
                          <el-button style="color: red;padding: 0;" type="text" size="mini" @click="nameDelete(data)">删除</el-button>
                        </span>
                      </span>
                </el-tree>
            </el-col>
            <el-col :style="{'width' : (width-40)+'px'}">
                <el-card shadow="never" style="overflow: auto;" :style="{'height' : (height-42)+'px'}">
                    <h3 style="height: 40px;text-align: center;">{{data.name}}</h3>
                    <el-button v-for="(item,key) in data.elementTitle" :key="key" type="primary" size="mini" icon="el-icon-plus" @click="elementAdd(key,item)">添加{{item}}</el-button>
                    <el-button v-show="data.id>0" type="primary" size="mini" icon="el-icon-plus" @click="contentAdd()">添加内容</el-button>
                    <el-button v-show="data.id>0" style="float: right;" type="primary" size="mini" @click="export1">导出</el-button>
                    <el-button v-show="data.id>0" style="float: right;" type="primary" size="mini" @click="import1">导入</el-button>
                    <div :style="{'height' : (height-170)+'px'}" style="overflow:auto;border: #000 solid 1px; margin-top: 15px;">
                        <table class="mytable" v-show="data.id>0">
                            <thead>
                            <tr>
                                <td style="width: 120px;" v-for="item in data.elementTitle">{{item}}</td>
                                <td style="width: 200px;">考评内容</td>
                                <td style="width: 70px;">标准分值</td>
                                <td style="width: 70px;">填报周期</td>
                                <td style="width: 200px;">基本规范要求</td>
                                <td style="width: 200px;">企业达标标准</td>
                                <!--								<td style="width: 130px;">上报材料</td>-->
                                <td style="width: 200px;">评分方式</td>
                                <td style="width: 200px;">自评/评审描述</td>
                            </tr>
                            </thead>
                            <tbody>
                            <tr v-for="(item,key) in data.content" @click="tableActive(item,$event)">
                                <td :rowspan="item['rows'+k]" v-for="(v,k) in data.elementTitle" v-if="item['rows'+k]>0">
                                    <el-popover
                                            placement="top-start"
                                            width="150"
                                            trigger="hover">
                                        <div slot="reference">{{item['name'+k]}}</div>
                                        <div style="text-align: center;">
                                            <p>分数：{{item['score'+k]}}</p>
                                            <p>合格线：{{item['mark'+k]}}%</p>
                                            <p v-if="k==0">权重：{{item['weight'+k]}}</p>
                                            <el-button type="warning" @click="elementUpdate(item,k,v)" size="mini">编辑</el-button>
                                            <el-button type="danger" @click="elementDelete(item,k)" size="mini">删除</el-button>
                                        </div>
                                    </el-popover>
                                </td>
                                <td>
                                    <el-popover
                                            placement="right-start"
                                            width="60"
                                            trigger="hover">
                                        <div slot="reference">{{item.content}}</div>
                                        <div style="text-align: center;">
                                            <el-button type="warning" @click="contentUpdate(item)" size="mini">编辑</el-button>
                                            <el-button type="danger" @click="contentDelete(item)" size="mini">删除</el-button>
                                        </div>
                                    </el-popover>
                                </td>
                                <td>{{item.score}}</td>
                                <td v-if="item.cycle==''">线下核实</td>
                                <td v-else>{{item.cycle}}</td>
                                <td>
                                    <el-tooltip class="item" effect="dark" :content="item.ask" placement="top">
                                        <p style="height: 40px;line-height: 40px;overflow: hidden;text-overflow: ellipsis;">{{item.ask}}</p>
                                    </el-tooltip>
                                </td>
                                <td>
                                    <el-tooltip class="item" effect="dark" :content="item.standards" placement="top">
                                        <p style="height: 40px;line-height: 40px;overflow: hidden;text-overflow: ellipsis;">{{item.standards}}</p>
                                    </el-tooltip>
                                </td>
                                <!--								<td>-->
                                <!--									<el-link style="margin-right:5px; font-size:13px;color: #2444d8" v-for="(v,i) in item.files" target="_blank" :href="v.url"  :key="i">{{v.name}}</el-link>-->
                                <!--								</td>-->
                                <td>
                                    <el-tooltip class="item" effect="dark" :content="item.method" placement="top">
                                        <p style="height: 40px;line-height: 40px;overflow: hidden;text-overflow: ellipsis;">{{item.method}}</p>
                                    </el-tooltip>
                                </td>
                                {/*							<td>1.未制定年度安全生产目标或未以文件形式发布生效的，不得分；,2.目标与企业安全生产实际不相符的， 一处扣0.5分,&nbsp;&nbsp;★3.未按照《安全生产法》、《成都市企业安全生产主体责任实施规范》等法律法规的相关规定设置安全管理机构或者配备专（兼）职安全管理人员的，不得分；,<br/></td>*/}
                                <td>{{item.file_remark}}</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </el-card>
            </el-col>
        </el-row>
        <import1 ref="import1" @ok="getData()"></import1>
        <nameadd ref="nameadd" @ok="getData()"></nameadd>
        <elementadd ref="elementadd" @ok="getData()"></elementadd>
        <contentadd ref="contentadd" @ok="getData()"></contentadd>
    </div>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.all.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                title:'新增',
                nameList:[],
                currentKey:0,
                searchFrom: {
                    search: '',
                },
                filterText:'',
                data:{
                    elementTitle:[],
                },
                dialogFormVisible: false,
                visible: false,
                loading: true,
                height:document.documentElement.clientHeight,
                width:document.documentElement.clientWidth,
            };
        },
        components: {
            'import1': 'url:/general/toppingsoft/public/vue/import.vue',
            'nameadd': 'url:/general/toppingsoft/app/admin/view/setting/vue/nameadd.vue?v=1',
            'elementadd': 'url:/general/toppingsoft/app/admin/view/setting/vue/elementadd.vue?v=1',
            'contentadd': 'url:/general/toppingsoft/app/admin/view/setting/vue/contentadd.vue?v=1',
        },
        created() {
            var _this = this;
            //窗口改变时
            window.onresize = function () {
                _this.height = document.documentElement.clientHeight;
                _this.width = document.documentElement.clientWidth;
            };
        },
        watch: {
            filterText(val) {
                this.$refs.tree.filter(val);
            }
        },
        methods: {
            filterNode(value, data) {
                if (!value) return true;
                return data.name.indexOf(value) !== -1;
            },
            //数据加载
            getData() {
                var _this = this;
                var param = {};
                param.id = {$_GET['id']};
                param._ajax = 1;
                axios.post('',param).then(function (res) {
                    if(res.data.code == 0){
                        _this.nameList = res.data.data;
                        _this.currentKey = _this.nameList[0].id;
                        _this.$nextTick(() => {
                            _this.$refs.tree.setCurrentKey(_this.currentKey)
                        })
                        _this.active(_this.nameList[0]);
                        _this.loading = false
                    }else{
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                    }
                    _this.loading = false;
                }).catch(function (error) {
                    console.log(error);
                    _this.loading = false;
                });
            },
            active(data,node,e){
                this.activeName = data;
                this.elementList();
            },
            tableActive(data,event){
                if(data){
                    jQuery('.active').removeClass('active');
                    jQuery(event.target).parents('tr').addClass('active');
                    this.activeData = data;
                }else{
                    jQuery('.active').removeClass('active');
                    this.activeData = {id:'',name:''};
                }
            },
            elementList(){
                let param = {
                    id: this.activeName.id,
                }
                this.loading = true
                axios.post('standardElementList',param).then(res => {
                    if(res.data.code == 0){
                        this.data = res.data.data;
                        this.tableActive();
                        this.loading = false
                    }else{
                        this.$message.error(res.data.msg);
                    }
                })
            },
            filterNode(value, data) {
                if (!value) return true;
                return data.name.indexOf(value) !== -1;
            },
            nameAdd(){
                this.$refs.nameadd.title="新增标准";
                this.$refs.nameadd.open();
            },
            nameUpdate(data){
                this.$refs.nameadd.title="编辑标准";
                this.$refs.nameadd.open(data);
            },
            elementAdd(key,title){
                var data = {
                    main_id:this.data.id,
                    pid:'',
                    pname:'无',
                    title:title,
                    name:'',
                    mark:'60',
                    weight:'1',
                    sort:'',
                };
                if(key>0){
                    var pid = [];
                    for(var i=0;i<key;i++){
                        pid.push(this.activeData['id'+i]);
                    }
                    data = {
                        main_id:this.data.id,
                        pid:pid,
                        pname:this.activeData['name'+key],
                        title:title,
                        name:'',
                        mark:'60',
                        weight:'1',
                        sort:'',
                    };
                }
                this.$refs.elementadd.title="新增要素";
                this.$refs.elementadd.open(data,this.data['elementParent'+key]);
            },
            elementUpdate(data,k,title){
                var pid = [];
                for(var i=0;i<k;i++){
                    pid.push(data['id'+i]);
                }
                var param = {
                    id:data['id'+k],
                    pid:pid,
                    title:title,
                    name:data['name'+k],
                    mark:data['mark'+k],
                    weight:data['weight'+k],
                    sort:data['sort'+k],
                };
                this.$refs.elementadd.title="编辑要素";
                this.$refs.elementadd.open(param,this.data['elementParent'+k]);
            },
            elementDelete(data,k){
                var _this = this;
                _this.$confirm('确定该要素及其下所有要素和指标吗?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    axios.post('standardElementDelete',{id:data['id'+k]}).then(res => {
                        if(res.data.code == 0){
                            _this.$message({message: res.data.msg, type: 'success'})
                            _this.elementList()
                        }else{
                            _this.$message.error(res.data.msg)
                        }
                    })
                }).catch(() => {})
            },
            contentAdd(){
                var element_id = [];
                for(var i=0;i<this.data.elementTitle.length;i++){
                    element_id.push(this.activeData['id'+i]);
                }
                var data = {
                    main_id:this.data.id,
                    element_id:element_id,
                    content:'',
                    score:'',
                    cycle:'',
                    ask:'',
                    standards:'',
                    method:'',
                    self:'',
                    sort:'',
                    weight:'1',
                    files:[],
                };
                this.$refs.contentadd.title="新增内容";
                this.$refs.contentadd.open(data,this.data['elementParent'+this.data.elementTitle.length]);
            },
            contentUpdate(data){
                var pid = [];
                for(var i=0;i<this.data.elementTitle.length;i++){
                    pid.push(data['id'+i]);
                }
                this.contentoptions = this.data['elementParent'+this.data.elementTitle.length];
                data['element_id'] = pid;
                this.$refs.contentadd.title="新增内容";
                this.$refs.contentadd.open(data,this.data['elementParent'+this.data.elementTitle.length]);
                this.contentupdateInfo = data;
                this.dialog.contentupdateDialogStatus = true
            },
            contentDelete(row){
                var _this = this;
                _this.$confirm('确定该要素及其下所有要素和指标吗?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    axios.post('standardContentDelete',{id:row.id}).then(res => {
                        if(res.data.code == 0){
                            _this.$message({message: res.data.msg, type: 'success'})
                            _this.elementList()
                        }else{
                            _this.$message.error(res.data.msg)
                        }
                    })
                }).catch(() => {})
            },
            nameDelete(row){
                var _this = this;
                _this.$confirm('确定删除该标准及其下所有信息吗?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    axios.post('standardDel',{id:row.id}).then(res => {
                        if(res.data.code == 0){
                            _this.$message({message: res.data.msg, type: 'success'})
                            _this.index()
                            _this.activeName = {};
                            _this.data = {};
                        }else{
                            _this.$message.error(res.data.msg)
                        }
                    })
                }).catch(() => {})
            },
            detail(row){
                this.dialog.detailDialogStatus = true
                this.detailInfo = {link_id:row.link_id ? row.link_id : this.ids.join(',')}
            },
            import1: function () {
                this.$refs.import1.templateUrl = "standardImportTemplate?id={$_GET['id']}";
                this.$refs.import1.submitUrl = "standardImport?id={$_GET['id']}";
                this.$refs.import1.title = this.data.name+'导入';
                this.$refs.import1.open();
            },
            export1: function () {
                let url = "standardExcel?id={$_GET['id']}";
                //window.open(url);
                location.href = url;
            },
        },
        mounted() {
            //获取列表
            this.getData();
        }
    })
</script>


</body>
</html>