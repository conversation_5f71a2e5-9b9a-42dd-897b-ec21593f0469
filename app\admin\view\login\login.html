﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>列表DEMO</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
	<link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
    </style>
</head>
<body>
<div id="app" v-cloak>
  <div class="centainer">
        <el-breadcrumb separator-class="el-icon-arrow-right">
            <el-breadcrumb-item><header>DEMO</header></el-breadcrumb-item>
        </el-breadcrumb>

        <el-form :inline="true" class="form-inline" @submit.native.prevent>
            <el-form-item>
                <el-input v-model="form_name" size="mini" placeholder="表单名称" @keyup.enter.native="getResult"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="getResult()" size="mini">查询</el-button>
                <el-link href="javascript:window.location.reload();" type="primary" size="mini">清空条件</el-link>
            </el-form-item>
            <el-form-item style="float: right">
                <el-button type="success" size="mini" @click="add">添加</el-button>
                <el-button @click="choiceDept">选择部门</el-button>
                <el-button @click="choiceDept2">选择部门-单选</el-button>

                <el-button @click="choiceUser">选择人员</el-button>
                <el-button @click="choiceUser2">选择人员-单选</el-button>
            </el-form-item>
        </el-form>

        <el-table border 
            v-loading="loading"
            :data="tableData"
            style="width: 100%;margin-bottom: 20px;"
            size="mini">
            <el-table-column
                    prop="id"
                    label="ID"
                    show-overflow-tooltip
                    width="80">
            </el-table-column>
            <el-table-column
                    prop="form_name"
                    label="表单名称"
                    show-overflow-tooltip
                    min-width="150">
            </el-table-column>
            <el-table-column
                    prop="form_desc"
                    label="表单描述"
                    show-overflow-tooltip
                    sortable
                    min-width="200">
            </el-table-column>
            <el-table-column
                    prop="update_user"
                    label="修改人"
                    show-overflow-tooltip
                    sortable
                    min-width="100">
            </el-table-column>
            <el-table-column
                    prop="update_time"
                    :formatter="dateFormatter"
                    label="最后修改时间"
                    show-overflow-tooltip
                    sortable
                    min-width="150">
            </el-table-column>
            <el-table-column
                    label="操作"
                    width="200">
                <template slot-scope="scope">
                    <el-button @click="fixedFormView(scope.row)" size="mini" type="text" >预览</el-button>
                    <el-button @click="setField(scope.row)" size="mini" type="text" >设置表单</el-button>
                    <el-button @click="edit(scope.row)" size="mini" type="text" >编辑</el-button>
                    <el-button @click="deleteList(scope.row)" size="mini" type="text" >删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <!--分页条total, sizes, prev, pager, next, jumper-->
        <div class="block">
            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-sizes="[10, 50, 100, 500, 1000]"
                    :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="roletotal">
            </el-pagination>
        </div>


        <!--新增弹窗-->
        <el-dialog title="新建表单" width="600px" :visible.sync="dialogFormVisible">
            <el-form  :model="form" :rules="formRules" ref="form" class="oa-form">
                <el-form-item label="表单名称" prop="form_name">
                    <el-input v-model="form.form_name" size="mini"></el-input>
                </el-form-item>
                <el-form-item label="表单描述" prop="form_desc">
                    <el-input type="textarea" :rows="5" v-model="form.form_desc" size="mini" style="width: 200px;"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogFormVisible = false" size="small">取 消</el-button>
                <el-button type="primary" @click="addSubmit" size="small">确 定</el-button>
            </div>
        </el-dialog>

      <deptchoose ref="deptChoose" @ok="deptFun" :deptData="deptData"></deptchoose>
      <personnelchoose ref="personnelChoose" @ok="userFun" :userData="userData"></personnelchoose>
    </div>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',

        data() {
            return {
                form_name: '',
                formOptions: [],
                deptData: [],
                userData: [],
                tableData: [],
                dialogFormVisible: false,
                visible: false,
                loading: true,
                roletotal: 0,
                currentPage: 1,
                pageSize: 10,
                form:{},
                formRules: {
                    form_name: [{required: true, message: "请输入表单名称", trigger: "blur"}],
                },
            };
        },
        components: {
            'deptchoose':      'url:../../../public/vue/deptChoose.vue',
            'personnelchoose': 'url:../../../public/vue/personnelChoose.vue',
        },
        methods: {
            // 日期格式化
            dateFormatter (row, column) {
                let datetime = row[column.property];
                if(datetime){
                    var date=new Date(parseInt(datetime)* 1000);

                    var year = date.getFullYear().toString().padStart(4, "0");
                    var mon = (date.getMonth() + 1).toString().padStart(2, "0");
                    var day = date.getDate().toString().padStart(2, "0");

                    var hours = date.getHours().toString().padStart(2, "0");
                    var minu = date.getMinutes().toString().padStart(2, "0");
                    var sec = date.getSeconds().toString().padStart(2, "0");

                    return year+'-'+mon+'-'+day+' '+hours+':'+minu;
                }
                return ''
            },
            handleSizeChange: function(val) {
                this.pageSize = val;
                this.getResult();
                console.log('每页 ${val} 条');
            },

            handleCurrentChange: function(val) {
                this.currentPage = val;
                this.getResult();
                console.log('当前页: ${val}');
            },
            add(){
                this.form = {},
                this.dialogFormVisible = true;
            },
            edit(row){
                this.form = JSON.parse(JSON.stringify(row));
                this.dialogFormVisible = true;
            },
            deleteList(row){
                var _this = this;
                this.$confirm('此操作将删除该行, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    var url = "{:url('index/delete')}";
                    axios.post(url, {id:row.id}).then(function (res) {
                        console.log(res);

                        if( res.data.code != 200 )
                        {
                            _this.$message({
                                message: res.data,
                                type: "error"
                            });
                        }
                        _this.$message({
                            message: res.data.msg,
                            type: "success"
                        });
                        _this.getResult();

                    }).catch(function (error) {
                        console.log(error);
                    });
                }).catch(function (error) {
                    console.log(error);
                });
            },
            getResult() {
                var _this = this;
                var param = {
                    pageSize: _this.pageSize,
                    page: _this.currentPage,
                    keyword: _this.form_name,
                };
                var url = "{:url('index/getDataList')}";
                axios.post(url,param).then(function (res) {
                    console.log(res);

                    if( res.data.code < 0 ){
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }

                    _this.tableData = res.data.data;
                    _this.roletotal = res.data.total;
                    _this.loading = false;
                }).catch(function (error) {
                    console.log(error);
                });
            },
            addSubmit: function () {
                this.$refs.form.validate(valid => {
                    var _this = this;
                    if (valid) {

                        this.$confirm("确认提交吗？", "提示", {}).then(() => {
                            _this.addLoading = true;
                            var param = _this.form;
                            var url = "{:url('index/save')}";
                            axios.post(url,param).then(function (res) {
                                console.log(res);
                                _this.addLoading = false;

                                if( res.data.code != 200 )
                                {
                                    _this.$message({
                                        message: res.data.msg,
                                        type: "error"
                                    });
                                }
                                _this.$message({
                                    message: res.data.msg,
                                    type: "success"
                                });
                                _this.dialogFormVisible = false;
                                _this.getResult();
                            }).catch(function (error) {
                                console.log(error);
                            });

                        });
                    }
                });
            },


            /**
             * 部门选择
             * */
            choiceDept() {
                this.$refs.deptChoose.visible = true;
                this.$refs.deptChoose.isRadio = false; // 是否单选  true 单选  false 多选
                this.$refs.deptChoose.initialize(this.deptData);
            },
             /**
             * 部门选择 - 单选
             * */
             choiceDept2() {
                this.$refs.deptChoose.visible = true;
                this.$refs.deptChoose.isRadio = true; // 是否单选  true 单选  false 多选
                this.$refs.deptChoose.initialize(this.deptData);
            },
             /**
             * 人员选择
             * */
             choiceUser() {
                this.$refs.personnelChoose.visible = true;
                this.$refs.personnelChoose.isRadio = false; // 是否单选  true 单选  false 多选
                this.$refs.personnelChoose.initialize(this.userData);
            },
              /**
             * 人员选择 - 单选
             * */
             choiceUser2() {
                this.$refs.personnelChoose.visible = true;
                this.$refs.personnelChoose.isRadio = true; // 是否单选  true 单选  false 多选
                this.$refs.personnelChoose.initialize(this.userData);
            },
            /**
             * 部门选择回调数据
             * */
            deptFun(data) {
                this.deptData = data;
                console.log(data);
            },
             /**
             * 人员选择回调数据
             * */
             userFun(data) {
                this.userData = data;
                console.log(data);
            },
        },
        mounted() {
            //获取列表
            this.getResult();
        }
    })
</script>


</body>
</html>