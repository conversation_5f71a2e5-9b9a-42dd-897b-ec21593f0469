<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>企业自评</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/company.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .el-form-item {margin-bottom:0;}
        .el-form--label-top .el-form-item__label { padding:0;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">
        <el-form :inline="true" :model="searchFrom" class="form-inline" @submit.native.prevent>
            <el-form-item style="float: right">
                <el-button :loading="loading" type="success" size="mini" @click="add">开始自评</el-button>
            </el-form-item>
        </el-form>
        <el-table border
                  v-loading="loading"
                  :data="data"
                  style="width: 100%;margin-bottom: 20px;"
                  ref="qtable"
                  :height="height"
                  size="small">
            <el-table-column
                    type="index"
                    label="序号"
                    align="center"
                    width="50">
            </el-table-column>
            <el-table-column
                    prop="year"
                    label="年度"
                    align="center"
                    width="100">
            </el-table-column>
            <el-table-column
                    prop="company_name"
                    label="企业名称"
                    align="center"
                    show-overflow-tooltip
                    min-width="200">
            </el-table-column>
            <el-table-column
                    prop="industry"
                    label="行业/专业"
                    align="center"
                    show-overflow-tooltip
                    min-width="100">
                <template slot-scope="scope">
                    {{scope.row.industry}}/{{scope.row.specialty}}
                </template>
            </el-table-column>
            <el-table-column
                    prop="date"
                    label="自评日期"
                    align="center"
                    show-overflow-tooltip
                    min-width="100">
            </el-table-column>
            <el-table-column
                    prop="score.total.score"
                    label="自评得分"
                    align="center"
                    min-width="100">
            </el-table-column>
            <el-table-column
                    prop="mb_status"
                    label="状态"
                    align="center"
                    show-overflow-tooltip
                    width="120">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.status==1" type="info">自评进行中</el-tag>
                    <el-tag v-if="scope.row.status==2" type="primary">已提交</el-tag>
                    <el-tag v-if="scope.row.status==7" type="success">审核完成</el-tag>
                    <el-tag v-if="scope.row.status==5" type="danger">已驳回</el-tag>
                </template>
            </el-table-column>
            <el-table-column
                    label="操作"
                    align="center"
                    width="150">
                <template slot-scope="scope">
                    <el-button v-if="scope.row.status==1" type="primary" @click="edit(scope.row)" size="small">编辑</el-button>
                    <el-button v-if="scope.row.status>=2" type="primary" @click="preview(scope.row.files)" size="small">自评报告</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-dialog title="企业信息确认" width="90%" :visible.sync="visible" :close-on-click-modal="false">
            <el-descriptions class="success" title="" :column="3" border>
                <el-descriptions-item label="企业名称">
                    {$company.name}
                </el-descriptions-item>
                <el-descriptions-item label="法定代表人">
                    {$company.legal}
                </el-descriptions-item>
                <el-descriptions-item label="法人联系电话">
                    {$company.legal_mobile}
                </el-descriptions-item>
                <el-descriptions-item label="法人邮箱">
                    {$company.legal_email}
                </el-descriptions-item>
                <el-descriptions-item label="企业传真">
                    {$company.fax}
                </el-descriptions-item>
                <el-descriptions-item label="邮政编码">
                    {$company.postal_code}
                </el-descriptions-item>
                <el-descriptions-item label="统一社会信用代码">
                    {$company.license_number}
                </el-descriptions-item>
                <el-descriptions-item label="信用代码有效期">
                    {$company.license_date}
                </el-descriptions-item>
                <el-descriptions-item label="经济类型">
                    {$company.mb_economy_type}
                </el-descriptions-item>
                <el-descriptions-item label="注册资本">
                    {$company.reg_money}万元
                </el-descriptions-item>
                <el-descriptions-item label="安全管理联系人">
                    {$company.manager}
                </el-descriptions-item>
                <el-descriptions-item label="联系电话">
                    {$company.manager_mobile}
                </el-descriptions-item>
                <el-descriptions-item label="邮箱">
                    {$company.manager_email}
                </el-descriptions-item>
                <el-descriptions-item label="成立日期">
                    {$company.date}
                </el-descriptions-item>
                <el-descriptions-item label="固定资产">
                    {$company.fixed_asset}
                </el-descriptions-item>
                <el-descriptions-item label="年营业收入">
                    {$company.revenue}万元
                </el-descriptions-item>
                <el-descriptions-item label="员工总数">
                    {$company.personnel}
                </el-descriptions-item>
                <el-descriptions-item label="营业场所面积">
                    {$company.area}m³
                </el-descriptions-item>
                <el-descriptions-item label="专职安全管理人数">
                    {$company.personnel_full}
                </el-descriptions-item>
                <el-descriptions-item label="兼职安全管理人数">
                    {$company.personnel_part}
                </el-descriptions-item>
                <el-descriptions-item label="特种作业人数">
                    {$company.personnel_special}
                </el-descriptions-item>
                <el-descriptions-item label="行业/专业">
                    {$company.industry}/{$company.specialty}
                </el-descriptions-item>
                <el-descriptions-item label="国民经济行业" :span="2">
                    {$company.mb_economy_sector}
                </el-descriptions-item>
                <el-descriptions-item label="所属集团名称">
                    {$company.group_name}
                </el-descriptions-item>
                <el-descriptions-item label="注册地址" :span="2">
                    {$company.mb_reg_address}
                </el-descriptions-item>
                <el-descriptions-item label="企业规模">
                    {$company.enterprise_size}
                </el-descriptions-item>
                <el-descriptions-item label="生产经营地点" :span="2">
                    {$company.mb_operate_address}
                </el-descriptions-item>
                <el-descriptions-item label="经营范围" :span="3">
                    {$company.business}
                </el-descriptions-item>
            </el-descriptions>
            <div slot="footer" class="dialog-footer">
                <el-button @click="visible = false" size="small">取 消</el-button>
                <el-button type="primary" @click="next" size="small">确认无误，开始自评</el-button>
            </div>
        </el-dialog>
        <el-dialog title="企业运行情况确认" width="700px" :visible.sync="visible1" :close-on-click-modal="false">
            <el-form label-position="top" label-width="80px" :model="form">
                <el-form-item label="本次自评前一年内本企业是否发生过死亡事件">
                    <el-radio v-model="form.f1" label="1">是</el-radio>
                    <el-radio v-model="form.f1" label="2">否</el-radio>
                </el-form-item>
                <el-form-item label="本次自评前一年内本企业是否发生过3人及以上重伤或直接经济损失总计100万元及以上的生产安全事故">
                    <el-radio v-model="form.f2" label="1">是</el-radio>
                    <el-radio v-model="form.f2" label="2">否</el-radio>
                </el-form-item>
                <el-form-item label="本企业是否发生过造成重大社会不良影响事件">
                    <el-radio v-model="form.f3" label="1">是</el-radio>
                    <el-radio v-model="form.f3" label="2">否</el-radio>
                </el-form-item>
                <el-form-item label="本企业是否被列入安全生产失信惩戒名单">
                    <el-radio v-model="form.f4" label="1">是</el-radio>
                    <el-radio v-model="form.f4" label="2">否</el-radio>
                </el-form-item>
                <el-form-item label="">
                    <span style="color:red;">温馨提示：请如实确认，如果在评审过程中发现瞒报后果自负。</span>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="visible1 = false" size="small">取 消</el-button>
                <el-button type="primary" @click="next1" size="small">确 认</el-button>
            </div>
        </el-dialog>
        <el-dialog title="" width="400px" :visible.sync="visible2" :close-on-click-modal="false">
            <el-result icon="error" title="不符合自评要求" subTitle="请达到自评要求后再申请自评">
                <template slot="extra">
                    <el-button type="primary" size="medium" @click="visible2 = false">返回</el-button>
                </template>
            </el-result>
        </el-dialog>
    </div>
    <preview ref="preview"></preview>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.all.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                title: '新增',
                searchFrom: {},
                form: {
                    f1:'',
                    f2:'',
                    f3:'',
                    f4:'',
                },
                data: [],
                visible: false,
                visible1: false,
                visible2: false,
                loading: true,
                height: document.documentElement.clientHeight - 155,
            };
        },
        components:{
            'preview': 'url:/general/toppingsoft/public/vue/preview.vue',
        },
        watch: {
            $route() {
                //切换标签时
                this.handleRefreshTable();
            }
        },
        created() {
            let _this = this;
            //窗口改变时
            window.onresize = function () {
                _this.handleRefreshTable();
            };
        },
        updated() {
            //数据改变时
            this.handleRefreshTable();
        },
        methods: {
            //数据加载
            getData() {
                var _this = this;
                var param = _this.searchFrom;
                param._ajax = 1;
                param.our = 1;
                axios.post('', param).then(function (res) {
                    if (res.data.code == 0) {
                        _this.data = res.data.data;
                        _this.$nextTick(() => {
                            // 这里的代码会在DOM更新完成后执行
                            _this.loading = false;
                        });
                        _this.handleRefreshTable();
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            preview: function (file) {
                file = file.response?file.response.data:file;
                this.$refs.preview.open(file.url,file.name);
            },
            add(data){
                this.visible = true;
            },
            next(){
                this.visible = false;
                this.visible1 = true;
            },
            next1(){
                if(this.form.f1=='2'&&this.form.f2=='2'&&this.form.f3=='2'&&this.form.f4=='2'){
                    location.href = 'apply';
                }else if(this.form.f1==''||this.form.f2==''||this.form.f3==''||this.form.f4==''){
                    this.$message({
                        message: '未确认完成',
                        type: "error"
                    });
                    return false;
                }else{
                    this.visible1 = false;
                    this.visible2 = true;
                }
            },
            edit(row){
                location.href = 'apply?id='+row.id;
            },
            down(row){
                location.href = 'down?id='+row.id;
            },
            handleRefreshTable: function () {
                this.$nextTick(function () {
                    var refList = this.$refs;
                    if (refList) {
                        for (var i of Object.keys(refList)) {
                            if (refList[i] && refList[i].doLayout) {
                                refList[i].doLayout();
                            }
                        }
                    }
                });
            },
        },
        mounted() {
            //获取列表
            this.getData();
            {if $company.review_id<=0}
            this.$confirm('系统检测到您未完成创标，是否现在创标?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                location.href = '/general/toppingsoft/index.php/company/standard/index';
            }).catch(() => {
            });
            {/if}
        }
    })
</script>


</body>
</html>