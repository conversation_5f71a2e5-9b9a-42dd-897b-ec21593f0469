<?php


namespace app\model;


use think\Model;
use think\App;
use think\facade\Db;

//权限配置模块
class AuthModel extends Model
{

    /**
     * 判断是否是管理员
     * @return bool
     */
    public static function isAdmin($key = '',$model='',$dept = false,$user=false)
    {
        $setting = config('auth')[$model];
        $admin = self::get($key,$model);
        $admin = $admin ? explode(",", $admin) : [];
        if (in_array($_SESSION['LOGIN_USER_ID'], $admin)) {
            return true;
        } else {
            if($user){
                $row = Db::table($setting['table'])->whereFindInSet('value',$_SESSION['LOGIN_USER_ID'])->where('model',$model)->where('key','like','dep_admin_%')->select()->toArray();
                $depts = [];
                foreach ($row as $v){
                    $dept_id = substr($v['key'],10);
                    $res = self::getChildrenDepts($dept_id);
                    foreach ($res as $v){
                        $depts[] = $v;
                    }
                }
                $deptids = empty($depts)?false:array_unique($depts);
                $user_ids = Db::table('td_user')->where('DEPT_ID','in',$deptids)->column('user_id');
                return $user_ids;
            }
            if($dept){
                $row = Db::table($setting['table'])->whereFindInSet('value',$_SESSION['LOGIN_USER_ID'])->where('model',$model)->where('key','like','dep_admin_%')->select()->toArray();
                $depts = [];
                foreach ($row as $v){
                    $dept_id = substr($v['key'],10);
                    $res = self::getChildrenDepts($dept_id);
                    foreach ($res as $v){
                        $depts[] = $v;
                    }
                }
                $deptids = empty($depts)?false:array_unique($depts);
                return $deptids;
            }
            return false;
        }
    }


//得到部门树形
    public function depTree($p_id=0,$depth=1,$where=""){

        $d=Db::table("department")
            ->where("DEPT_PARENT",$p_id)
            ->where($where)
            ->field("DEPT_ID,DEPT_NAME,DEPT_PARENT")
            ->order('DEPT_NO')
            ->select()->toArray();
        $r=[];
        foreach ($d as $v){
            $v["depth"]=$depth;
            $r[]=$v;
            $c=self::depTree($v["DEPT_ID"],$depth+1);
            $r=array_merge($r,$c);
        }
        return $r;
    }

    public static function getChildrenDepts($dept_id){
        $deptids[] = $dept_id;
        $res = Db::table('department')->where(['DEPT_PARENT'=>$dept_id])->field('DEPT_ID')->select()->toArray();
        foreach($res as $v){
            $depts = self::getChildrenDepts($v['DEPT_ID']);
            foreach ($depts as $v){
                $deptids[] = $v;
            }
        }
        return array_unique($deptids);
    }


    public static function get($key, $model = '')
    {
        $setting = config('auth')[$model];
            $v = Db::table($setting['table'])->where(["key"=>$key,'model'=>$model])->find();
            if (!$v) {
                //throw new Exception("$key 配置不存在！");
                return  null;
            }
            return $v["is_json"] && $v["value"] ? json_decode($v["value"], true) : $v["value"];
    }

    public static function setValue($key,$value,$model=''){
        $setting = config('auth')[$model];
        //先找数据库里面是否有
        $row=Db::table($setting['table'])->where(["key"=>$key,'model'=>$model])->find();
        if($row){
            Db::table($setting['table'])->where(["key"=>$key,'model'=>$model])->update([
                "value"=>is_array($value)||is_object($value)?json_encode($value):$value
            ]);
        }else{
            Db::table($setting['table'])->insert([
                "value"=>is_array($value)||is_object($value)?json_encode($value):$value,
                "key"=>$key,
                "model"=>$model,
            ]);
        }
    }

    /**
     * 获取部门的名称
     * @param $deptId
     * @return string
     */
    public static function getDeptFullName($deptId,$deptName=''){
        $row =  Db::table("department")
            ->where("dept_id",$deptId)
            ->find();
        if(!$row){
            return  "";
        }
        if($row['DEPT_PARENT']!=0){
            $deptName = AuthModel::getDeptFullName($row['DEPT_PARENT'],$row['DEPT_NAME']). "\\".$deptName;
        }else{
            $deptName = "\\".$row['DEPT_NAME']."\\".$deptName;
        }
        $deptName = "\\".trim($deptName,"\\");
        return $deptName;
    }
}