<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>专家信息认证</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/company.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .el-cascader { width:100%;}
        .el-form-item__content .el-input-group { vertical-align: middle;}
        .avatar-uploader .el-upload {
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        .avatar-uploader .el-upload:hover {
            border-color: #409EFF;
        }
        .avatar-uploader-icon {
            font-size: 28px;
            color: #8c939d;
            width: 150px;
            height: 150px;
            line-height: 150px;
            text-align: center;
        }
        .avatar {
            width: 150px;
            height: 150px;
            display: block;
        }
        .el-divider--horizontal{ margin:5px 0;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">
        <el-container>
            <el-form ref="form" :model="data" class="tableSearch" label-position="right" label-width="170px" :rules="rules">
                <el-row :gutter="20">
                    <el-col><el-button style="background-color: #436CE6;color:#ffffff;" size="small" disabled>专家基本信息</el-button></el-col>
                    <el-col :span="8">
                        <el-form-item label="照片" prop="head">
                            <el-upload
                                    class="avatar-uploader"
                                    action="upload"
                                    :show-file-list="false"
                                    :on-success="(response,file,fileList)=>uploadSuccess(response,file,fileList,'head')"
                                    :before-upload="uploadBefore">
                                <img v-if="data.headUrl" :src="data.headUrl" class="avatar">
                                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                            </el-upload>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="姓名" prop="name">
                            <el-input v-model="data.name" size="mini"></el-input>
                        </el-form-item>
                        <el-form-item label="手机号" prop="mobile">
                            <el-input v-model="data.mobile" size="mini"></el-input>
                        </el-form-item>
                        <el-form-item label="性别" prop="sex">
                            <el-radio v-model="data.sex" label="男">男</el-radio>
                            <el-radio v-model="data.sex" label="女">女</el-radio>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="所属评审单位" prop="org_id">
                            <el-select v-model="data.org_id" size="mini" placeholder="请选择">
                                <el-option key="0" label="请选择" value="0">
                                <el-option v-for="v in config.orgs" :key="v.id" :label="v.name" :value="v.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="邮箱" prop="email">
                            <el-input v-model="data.email" size="mini"></el-input>
                        </el-form-item>
                        <el-form-item label="出生日期" prop="birthday">
                            <el-date-picker
                                    v-model="data.birthday"
                                    size="mini"
                                    type="date"
                                    placeholder="选择日期"
                                    format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col></el-col>
                    <el-col :span="8">
                        <el-form-item label="民族" prop="nation">
                            <el-input v-model="data.nation" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="QQ" prop="qq">
                            <el-input v-model="data.qq" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="现住址" prop="address">
                            <el-input v-model="data.address" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="学校" prop="school">
                            <el-input v-model="data.school" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="专业" prop="speciality">
                            <el-input v-model="data.speciality" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="学历" prop="education">
                            <el-input v-model="data.education" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="现工作单位" prop="employer">
                            <el-input v-model="data.employer" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="职务" prop="position">
                            <el-input v-model="data.position" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="参加工作时间" prop="work_date">
                            <el-date-picker
                                    v-model="data.work_date"
                                    size="mini"
                                    type="date"
                                    placeholder="选择日期"
                                    format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="从事安全生产工作时间" prop="position_date">
                            <el-date-picker
                                    v-model="data.position_date"
                                    size="mini"
                                    type="date"
                                    placeholder="选择日期"
                                    format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="专业技术职称" prop="professional">
                            <el-input v-model="data.professional" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="职称证书编号" prop="professional_number">
                            <el-input v-model="data.professional_number" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="安全评价资格师等级" prop="secure">
                            <el-input v-model="data.secure" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="安全评价师证书编号" prop="secure_number">
                            <el-input v-model="data.secure_number" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="注册安全工程师证书编号" prop="reg_secure_number">
                            <el-input v-model="data.reg_secure_number" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="其他证书编号" prop="other_number">
                            <el-input v-model="data.other_number" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="擅长专业" prop="major">
                            <el-input v-model="data.major" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="聘用日期" prop="employ_date">
                            <el-date-picker
                                    v-model="data.employ_date"
                                    size="mini"
                                    type="date"
                                    placeholder="选择日期"
                                    format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="受聘情况" prop="offer_info">
                            <el-input type="textarea" row="3" v-model="data.offer_info" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="个人学习及工作简历" prop="resume">
                            <el-input type="textarea" row="3" v-model="data.resume" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" style="text-align: center;">
                        <el-button type="primary" @click="submit()">提交审核</el-button>
                    </el-col>
                </el-row>
            </el-form>
        </el-container>
    </div>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                title: '',
                loading: false,
                data: {
                    date:'',
                    license_start:'',
                    license_end:'',
                    enterprise_size:'',
                },
                rules: {
                    name: [{ required: true, message: '请填写姓名', trigger: 'blur' }],
                    org_id: [{ required: true, message: '请选择评审单位', trigger: 'change' }],
                    mobile: [{ required: true, message: '请填写手机号', trigger: 'blur' }],
                    email: [{ required: true, message: '请填写邮箱', trigger: 'blur' }],
                    sex: [{ required: true, message: '请选择性别', trigger: 'change' }],
                    birthday: [{ required: true, message: '请选择出生日期', trigger: 'change' }],
                    nation: [{ required: true, message: '请填写民族', trigger: 'blur' }],
                    qq: [{ required: true, message: '请填写QQ', trigger: 'blur' }],
                    address: [{ required: true, message: '请填写现住址', trigger: 'blur' }],
                    school: [{ required: true, message: '请填写毕业院校', trigger: 'blur' }],
                    speciality: [{ required: true, message: '请填写专业', trigger: 'blur' }],
                    education: [{ required: true, message: '请填写学历', trigger: 'blur' }],
                    work_date: [{ required: true, message: '请选择参加工作时间', trigger: 'change' }],
                    position_date: [{ required: true, message: '请选择从事安全生产工作时间', trigger: 'change' }],
                },
                config:[],
                height: document.documentElement.clientHeight,
            };
        },
        components: {
        },
        methods: {
            handleChange(value) {
                var data = [];
                for (var i=0;i<3;i++){
                    data.push(value[i]);
                }
                this.data.region = data;
            },
            uploadBefore(file) {
                const isJPG = file.type === 'image/jpeg';
                const isPNG = file.type === 'image/png';
                if(!isJPG&&!isPNG){
                    this.$message.error('请上传jpg图片');
                }
                return isJPG||isPNG;
            },
            uploadSuccess(res, file,fileList,field) {
                var files = [];
                console.log(res)
                if(res.code==0){
                    for(var i in fileList){
                        files.push(fileList[i].response??fileList[i]);
                    }
                    this.data[field] = res.data.code;
                    this.data[field+'Url'] = res.data.url;
                }else{
                    this.$message.error(res.msg);
                }
            },
            submit: function () {
                var _this = this;
                var param = _this.data;
                this.$refs.form.validate(function (valid) {
                    if(valid){
                        _this.loading = true;
                        axios.post("expertSave", param).then(function (res) {
                            _this.loading = false;
                            _this.$message({
                                message: res.data.msg,
                                type: res.data.type
                            });
                            if (res.data.code == 0) {
                                _this.visible = false;
                                location.href = 'info';
                            }
                        }).catch(function (error) {
                            _this.loading = false;
                            console.log("出现错误:",error);
                        });
                    }
                });
            },
            getInfo:function(){
                var _this = this;
                _this.loading = true;
                axios.post('getExpertInfo', {}).then(function (res) {
                    _this.loading = false;
                    if (res.data.code == 0) {
                        _this.data = res.data.data;
                    }
                }).catch(function (error) {
                    _this.loading = false;
                    console.log("出现错误:",error);
                });
            },
            getConfig:function(){
                var _this = this;
                axios.post('getConfig', {}).then(function (res) {
                    if (res.data.code == 0) {
                        _this.config = res.data.data;
                    }
                }).catch(function (error) {
                    console.log("出现错误:",error);
                });
            },
            goBack() {
                window.history.back();
            },
        },
        mounted() {
            this.getInfo();
            this.getConfig();
        }
    })
</script>


</body>
</html>