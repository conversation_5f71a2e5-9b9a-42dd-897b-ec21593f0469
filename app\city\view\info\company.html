<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{$title}</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/area.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">
        <el-breadcrumb separator-class="el-icon-arrow-right">
            <el-breadcrumb-item>
                <header>{$title}</header>
            </el-breadcrumb-item>
        </el-breadcrumb>
        <el-form :inline="true" v-model="searchForm" class="form-inline" @submit.native.prevent>
            <el-form-item>
                <el-input v-model="searchForm.name" size="mini" placeholder="名称"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="getResult()" size="mini">查询</el-button>
                <el-link href="javascript:window.location.reload();" type="primary" size="mini">清空条件</el-link>
            </el-form-item>
            <!--            <el-form-item style="float: right">-->
            <!--                <el-button type="success" size="mini" @click="add">添加</el-button>-->
            <!--            </el-form-item>-->
        </el-form>

        <el-table border
                  v-loading="loading"
                  :data="tableData"
                  style="width: 100%;margin-bottom: 20px;"
                  :height="height"
                  size="mini">
            <el-table-column
                    prop="id"
                    label="ID"
                    show-overflow-tooltip
                    width="80">
            </el-table-column>
            <el-table-column
                    prop="name"
                    label="名称"
                    show-overflow-tooltip
                    min-width="150">
            </el-table-column>


            <el-table-column
                    label="操作"
                    width="100">
                <template slot-scope="scope">
                    <el-button @click="details(scope.row)" size="mini" type="text" v-if="scope.row.status!=1">详情</el-button>
                    <el-button @click="details(scope.row, 1)" size="mini" type="text" v-if="scope.row.status==1">审核</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!--分页条total, sizes, prev, pager, next, jumper-->
        <div class="block">
            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-sizes="[10, 50, 100, 500, 1000]"
                    :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="roletotal">
            </el-pagination>
        </div>

    </div>
    <edit ref="edit" @ok="getResult()"></edit>
    <info ref="info" @ok="getResult()"></info>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',

        data() {
            return {
                form_name: '',
                formOptions: [],
                deptData: [],
                userData: [],
                tableData: [],
                dialogFormVisible: false,
                visible: false,
                loading: true,
                roletotal: 0,
                currentPage: 1,
                searchForm: {},
                pageSize: 10,
                form: {},
                height: document.documentElement.clientHeight - 215,
                formRules: {
                    password: [
                        {required: true, message: '请输入密码', trigger: 'blur'},
                        {
                            pattern: /^(?![\d]+$)(?![a-zA-Z]+$)(?![^\da-zA-Z]+$)([^\u4e00-\u9fa5\s]){8,20}$/,
                            message: '请输入8-20位大/小写字母+数字+特殊字符',
                            trigger: 'blur'
                        },
                    ],
                },
                url: {
                    'get_list': 'get_company',
                    'pwd': 'update_password',
                    'del': 'delete'
                },
                config: {}
            };
        },
        components: {
            'edit': 'url:/general/toppingsoft/app/area/view/company/vue/add_p.vue',
            'info': 'url:/general/toppingsoft/app/area/view/company/vue/info.vue',
        },
        methods: {
            // 日期格式化
            dateFormatter(row, column) {
                let datetime = row[column.property];
                if (datetime) {
                    var date = new Date(parseInt(datetime) * 1000);

                    var year = date.getFullYear().toString().padStart(4, "0");
                    var mon = (date.getMonth() + 1).toString().padStart(2, "0");
                    var day = date.getDate().toString().padStart(2, "0");

                    var hours = date.getHours().toString().padStart(2, "0");
                    var minu = date.getMinutes().toString().padStart(2, "0");
                    var sec = date.getSeconds().toString().padStart(2, "0");

                    return year + '-' + mon + '-' + day + ' ' + hours + ':' + minu;
                }
                return ''
            },
            handleSizeChange: function (val) {
                this.pageSize = val;
                this.getResult();
                console.log('每页 ${val} 条');
            },

            handleCurrentChange: function (val) {
                this.currentPage = val;
                this.getResult();
                console.log('当前页: ${val}');
            },
            edit(row) {
                this.$refs.edit.title = "编辑用户信息";
                console.log(this.config);
                this.$refs.edit.open(row, this.config);
            },
            editPwd(row) {
                row.password = '';
                this.form = row,
                    this.dialogFormVisible = true;
            },
            deleteList(row) {
                var _this = this;
                this.$confirm('将删除该用户, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    var url = _this.url.del;
                    axios.post(url, {id: row.id}).then(function (res) {
                        if (res.data.code == -200) {
                            _this.$message({
                                message: res.data,
                                type: "error"
                            });
                        } else {
                            _this.$message({
                                message: res.data.msg,
                                type: "success"
                            });
                            _this.getResult();
                        }


                    }).catch(function (error) {
                        console.log(error);
                    });
                }).catch(function (error) {
                    console.log(error);
                });
            },
            getResult() {
                var _this = this;
                var param = _this.searchForm;
                param.page = _this.currentPage;
                param.limit = _this.pageSize;
                param.our = 1;
                var url = this.url.get_list;
                axios.post(url, param).then(function (res) {
                    if (res.data.code == 0) {
                        _this.tableData = res.data.data.data;
                        _this.currentPage = res.data.data.current_page;
                        // _this.pageSize = res.data.data.per_page;
                        _this.roletotal = res.data.data.total;
                        _this.$nextTick(() => {
                            // 这里的代码会在DOM更新完成后执行
                            _this.loading = false;
                        });
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            addSubmit: function () {
                this.$refs.form.validate(valid => {
                    var _this = this;
                    if (valid) {
                        this.$confirm("确认提交吗？", "提示", {}).then(() => {
                            _this.addLoading = true;
                            var param = _this.form;
                            var url = _this.url.pwd;
                            axios.post(url, param).then(function (res) {
                                _this.addLoading = false;
                                if (res.data.code == -200) {
                                    _this.$message({
                                        message: res.data.msg,
                                        type: "error"
                                    });
                                } else {
                                    _this.$message({
                                        message: res.data.msg,
                                        type: "success"
                                    });
                                    _this.dialogFormVisible = false;
                                    _this.getResult();
                                }

                            }).catch(function (error) {
                                console.log(error);
                            });

                        });
                    }
                });
            },
            details(row, check) {
                this.thisrow = row;
                this.$refs.info.open(row, check);
            },

        },
        mounted() {
            //获取列表
            this.getResult();
        }
    })
</script>


</body>
</html>