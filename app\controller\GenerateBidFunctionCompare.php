<?php
namespace app\controller;

use PHPExcel;
use PHPExcel_IOFactory;
use think\facade\App;
use think\facade\Request;
use think\facade\Response;

class GenerateBidFunctionCompare
{
    // 读取CSV文件
    protected function readCsv($csvFile)
    {
        $rows = [];
        if (($handle = fopen($csvFile, 'r')) !== false) {
            while (($data = fgetcsv($handle)) !== false) {
                $rows[] = $data;
            }
            fclose($handle);
        }
        return $rows;
    }

    // 扫描按钮功能
    protected function scanButtonFunctions($appDir)
    {
        $buttonList = [];
        $appMap = [
            'app/city' => '市应急管理局',
            'app/area' => '区县',
            'app/company' => '企业',
            'app/org' => '评审机构',
            'app/expert' => '评审专家端'
        ];

        $rii = new \RecursiveIteratorIterator(new \RecursiveDirectoryIterator($appDir));
        foreach ($rii as $file) {
            if ($file->isDir()) continue;
            $ext = strtolower($file->getExtension());
            if (!in_array($ext, ['html','vue'])) continue;
            
            $filePath = str_replace('\\','/',substr($file->getPathname(),strlen(getcwd())+1));
            $moduleTitle = '';
            
            // 提取HTML标题
            $lines = file($file->getPathname());
            foreach ($lines as $line) {
                if (preg_match('/<title>(.*?)<\/title>/i', $line, $titleMatch)) {
                    $moduleTitle = trim(strip_tags($titleMatch[1]));
                    break;
                }
            }
            
            // 匹配按钮
            foreach ($lines as $lineNum => $line) {
                if (preg_match_all('/<el-button[^>]*>(.*?)<\/el-button>/u', $line, $matches)) {
                    foreach ($matches[1] as $btnText) {
                        $btnText = trim(strip_tags($btnText));
                        if ($btnText) {
                            // 确定所属端
                            $appName = '';
                            foreach ($appMap as $path => $name) {
                                if (strpos($filePath, $path) === 0) {
                                    $appName = $name;
                                    break;
                                }
                            }
                            
                            $buttonList[] = [
                                'file' => $filePath,
                                'line' => $lineNum+1,
                                'button' => $btnText,
                                'app' => $appName,
                                'module' => $moduleTitle
                            ];
                        }
                    }
                }
            }
        }
        return $buttonList;
    }

    // 生成Excel
    protected function generateExcel($csvRows, $buttonList, $outputFile)
    {
        $excel = new PHPExcel();
        $sheet = $excel->setActiveSheetIndex(0);
        $sheet->setCellValue('A1', '招标文件功能点');
        $sheet->setCellValue('B1', '所属端');
        $sheet->setCellValue('C1', '模块名称');
        $sheet->setCellValue('D1', '实际功能点');
        $sheet->setCellValue('E1', '文件路径');
        $sheet->setCellValue('F1', '行号');
        $rowNum = 2;
        foreach ($csvRows as $row) {
            $func = isset($row[2]) ? trim($row[2]) : (isset($row[0]) ? trim($row[0]) : '');
            if (!$func) continue;
            $matchedBtn = null;
            $maxSim = 0;
            foreach ($buttonList as $btn) {
                // 优先全包含匹配
                if (mb_strpos($func, $btn['button']) !== false || mb_strpos($btn['button'], $func) !== false) {
                    $matchedBtn = $btn;
                    $maxSim = 100;
                    break;
                }
                // 简单模糊匹配（最长公共子串长度/功能点长度）
                similar_text($func, $btn['button'], $percent);
                if ($percent > $maxSim) {
                    $maxSim = $percent;
                    $matchedBtn = $btn;
                }
            }
            if ($maxSim >= 60 && $matchedBtn) { // 60%相似度阈值
                $sheet->setCellValue('A'.$rowNum, $func);
                $sheet->setCellValue('B'.$rowNum, $matchedBtn['app']);
                $sheet->setCellValue('C'.$rowNum, $matchedBtn['module']);
                $sheet->setCellValue('D'.$rowNum, $matchedBtn['button']);
                $sheet->setCellValue('E'.$rowNum, $matchedBtn['file']);
                $sheet->setCellValue('F'.$rowNum, $matchedBtn['line']);
            } else {
                $sheet->setCellValue('A'.$rowNum, $func);
                $sheet->setCellValue('B'.$rowNum, '');
                $sheet->setCellValue('C'.$rowNum, '');
                $sheet->setCellValue('D'.$rowNum, '未找到对应按钮');
                $sheet->setCellValue('E'.$rowNum, '');
                $sheet->setCellValue('F'.$rowNum, '');
            }
            $rowNum++;
        }
        $writer = PHPExcel_IOFactory::createWriter($excel, 'Excel2007');
        $writer->save($outputFile);
    }

    // 控制器入口方法
    public function index()
    {
        require_once App::getRootPath() . 'vendor/autoload.php';
        $csvFile = App::getRootPath() . 'docs/招标文件功能表.csv';
        $appDir = App::getRootPath() . 'app';
        $outputFile = App::getRootPath() . 'docs/招标文件功能对照表.xlsx';
        $csvRows = $this->readCsv($csvFile);
        // 调试输出CSV内容
        file_put_contents(App::getRootPath() . 'docs/csv_debug.txt', print_r($csvRows, true));
        $buttonList = $this->scanButtonFunctions($appDir);
        // 调试输出按钮扫描内容
        file_put_contents(App::getRootPath() . 'docs/button_debug.txt', print_r($buttonList, true));
        $this->generateExcel($csvRows, $buttonList, $outputFile);
        echo "生成完成，文件已保存到 /docs/招标文件功能对照表.xlsx";
        exit;
    }
}