<?php

namespace app\validate;

use think\Validate;

class ExpertVerify extends Validate
{
    protected $rule = [
        'id' => 'require|number',
        'org_id' => 'require',
        'name' => 'require',
        'mobile' =>'require',
        'email' =>'require',
        'sex' => 'require',
        'birthday' => 'require',
        'nation' => 'require',
        'qq' => 'require',
        'address' => 'require',
        'school' => 'require',
        'speciality' => 'require',
        'education' => 'require',
        'employer' => 'require',
        'position' => 'require',
        'work_date' => 'require',
        'position_date' => 'require',
    ];

    protected $message = [
        'id' => '参数错误',
        'org_id' => '请选择评审单位',
        'name' => '请填写姓名',
        'mobile' =>'请填写手机号',
        'email' =>'请填写邮箱',
        'sex' => '请选择性别',
        'birthday' => '请选择出生日期',
        'nation' => '请选择民族',
        'qq' => '请填写QQ',
        'address' => '请填写现住址',
        'school' => '请填写毕业院校',
        'speciality' => '请填写专业',
        'education' => '请填写学历',
        'work_date' => '请选择参加工作时间',
        'position_date' => '请选择从事安全生产工作时间',
    ];

    protected $scene = [
        'save' => ['name','org_id','mobile','email','sex','birthday','nation','qq','address','school','speciality','education','position_date'],
    ];

    public function userCheck($scene,$data){
        $validate = new self();
        $re = $validate->scene($scene)->check($data);
        if(!$re){
            result('',1001,$validate->getError());
//            if(request()->isAjax()){
//                result('',1001,$validate->getError());
//            }else{
//                return $validate->getError();
//            }
        }
    }

}