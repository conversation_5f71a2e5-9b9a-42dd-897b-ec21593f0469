<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{$title}</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .el-badge__content.is-fixed{right:20px;z-index:9;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">
        <el-breadcrumb separator-class="el-icon-arrow-right">
            <el-breadcrumb-item><header>{$title}</header></el-breadcrumb-item>
        </el-breadcrumb>
        <el-form :inline="true" :model="searchFrom" class="search_form form-inline" @submit.native.prevent>
            {volist name="fields.search" id="item"}
            {if $item.type=='radio'}
            <el-form-item label="{$item.title}">
                <el-radio-group @change="statusChange" size="mini" v-model="searchFrom.{$item.field}">
                    <el-radio-button label="">全部</el-radio-button>
                    {volist name="global[$item.field]" id="value" key="k"}
                    <el-radio-button :label="{$value.value}">{$value.label}</el-radio-button>
                    {/volist}
                </el-radio-group>
            </el-form-item>
            {elseif $item.type=='select'}
            <el-form-item label="{$item.title}">
                <el-select size="mini" clearable v-model="searchFrom.{$item.field}" >
                    {volist name="global[$item.field]" id="value" key="k"}
                    <el-option key="{$value.value}" label="{$value.label}" value="{$value.value}">{$value.label}</el-option>
                    {/volist}
                </el-select>
            </el-form-item>
            {elseif $item.type=='personnel'}
            <el-form-item label="{$item.title}">
                {volist name="item.bind" id="value" key="k"}
                <el-input v-show="'{$k}'=='2'" style="width: 150px;" v-model="searchFrom.{$value}" @focus="choiceUser({$item.bind|json_encode},'searchFrom')" size="mini" placeholder="{$item.title}"></el-input>
                {/volist}
            </el-form-item>
            {elseif $item.type=='input'}
            <el-form-item label="{$item.title}">
                <el-input v-model="searchFrom.{$item.field}" size="mini" placeholder="{$item.title}"></el-input>
            </el-form-item>
            {/if}
            {/volist}
            <el-form-item v-if="'{$fields.search|count}'>0">
                <el-button type="primary" @click="getData()" size="mini">查询</el-button>
                <el-button @click="reset()" size="mini">重置</el-button>
            </el-form-item>
            <el-form-item style="float: right" v-if="isAdmin">
                <el-button v-if="'{$fields.add|count}'>0" type="success" size="mini" @click="add">添加</el-button>
                <el-button v-if="'{$fields.export|count}'>0" :loading="loading" type="primary"  size="mini" @click="export1">导出</el-button>
                <el-button v-if="'{$fields.import|count}'>0" :loading="loading" type="primary"  size="mini" @click="import1">导入</el-button>
            </el-form-item>
        </el-form>
        <el-table border
                  v-loading="loading"
                  :data="data"
                  style="width: 100%;margin-bottom: 20px;"
                  ref="qtable"
                  :height="height"
                  size="small">
            <!--<el-table-column
                    type="selection"
                    fixed="left"
                    :selectable="selected"
                    width="55">
            </el-table-column>-->
            <el-table-column
                    type="index"
                    label="序号"
                    align="center"
                    show-overflow-tooltip
                    width="60">
            </el-table-column>
            {volist name="fields.list" id="item"}
            <el-table-column
                    prop="{$global[$item.field]?'mb_':''}{$item.field}"
                    label="{$item.title}"
                    align="center"
                    show-overflow-tooltip
                    min-width="{$item.width>0??150}">
            </el-table-column>
            {/volist}
            <el-table-column
                    label="操作"
                    fixed="right"
                    width="250">
                <template slot-scope="scope">
                    <!--                    <el-button @click="info(scope.row)" size="mini" type="primary" >详情</el-button>-->
                    <el-button v-if="isAdmin&&'{$fields.edit|count}'>0" @click="edit(scope.row)" size="mini" type="warning" >编辑</el-button>
                    <el-button v-if="isAdmin" @click="deleteList(scope.row)" size="mini" type="danger" >删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!--分页条total, sizes, prev, pager, next, jumper-->
        <div class="block">
            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="page"
                    :page-sizes="[10, 20, 50, 100, 500, 1000]"
                    :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total">
            </el-pagination>
        </div>
        <!--新增弹窗-->
        <el-dialog :title="title" width="90%" :visible.sync="dialogFormVisible" :close-on-click-modal="false">
            <el-form label-position="right" :model="form" :rules="formRules" ref="form" class="oa-form" label-width="140px">
                <el-row :gutter="20">
                    {volist name="fields.add" id="item"}
                    {if $item.show}
                    {if $item.type=='input'}
                    <el-col :span="20" style="width: {$item.width??'50%'}">
                        <el-form-item label="{$item.title}" prop="{$item.field}">
                            <el-input size="mini" v-model="form.{$item.field}" ></el-input>
                        </el-form-item>
                    </el-col>
                    {elseif $item.type=='select'}
                    <el-col :span="20" style="width: {$item.width??'50%'}">
                        <el-form-item label="{$item.title}" prop="{$item.field}">
                            <el-select size="mini" clearable v-model="form.{$item.field}" >
                                {volist name="global[$item.field]" id="value" key="k"}
                                <el-option key="{$value.value}" label="{$value.label}" value="{$value.value}">{$value.label}</el-option>
                                {/volist}
                            </el-select>
                        </el-form-item>
                    </el-col>
                    {elseif $item.type=='radio'}
                    <el-col :span="20" style="width: {$item.width??'50%'}">
                        <el-form-item label="{$item.title}" prop="{$item.field}">
                            <el-radio-group size="mini" v-model="form.{$item.field}">
                                {volist name="global[$item.field]" id="value" key="k"}
                                <el-radio :label="{$value.value}">{$value.label}</el-radio>
                                {/volist}
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                    {elseif $item.type=='checkbox'}
                    <el-col :span="20" style="width: {$item.width??'50%'}">
                        <el-form-item label="{$item.title}" prop="{$item.field}">
                            <el-checkbox-group size="mini" v-model="form.{$item.field}">
                                {volist name="global[$item.field]" id="value" key="k"}
                                <el-checkbox :label="{$value.value}">{$value.label}</el-checkbox>
                                {/volist}
                            </el-checkbox-group>
                        </el-form-item>
                    </el-col>
                    {elseif $item.type=='date'}
                    <el-col :span="20" style="width: {$item.width??'50%'}">
                        <el-form-item label="{$item.title}" prop="{$item.field}">
                            {volist name="item.bind" id="value" key="k"}
                            {if $k>1}
                            -
                            {/if}
                            <el-date-picker style="width:150px;" v-model="form.{$value}" type="date" placeholder="选择日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd" size="mini"></el-date-picker>
                            {/volist}
                        </el-form-item>
                    </el-col>
                    {elseif $item.type=='personnel'}
                    <el-col :span="20" style="width: {$item.width??'50%'}">
                        <el-form-item label="{$item.title}" prop="{$item.field}">
                            {volist name="item.bind" id="value" key="k"}
                            <el-input v-show="'{$k}'=='2'" style="width: 175px;" v-model="form.{$value}" @focus="choiceUser({$item.bind|json_encode},'form')" size="mini" placeholder="{$item.title}"></el-input>
                            {/volist}
                        </el-form-item>
                    </el-col>
                    {elseif $item.type=='dept'}
                    <el-col :span="20" style="width: {$item.width??'50%'}">
                        <el-form-item label="{$item.title}" prop="{$item.field}">
                            {volist name="item.bind" id="value" key="k"}
                            <el-input v-show="'{$k}'=='2'" style="width: 175px;" v-model="form.{$value}" @focus="choiceDept({$item.bind|json_encode},'form')" size="mini" placeholder="{$item.title}"></el-input>
                            {/volist}
                        </el-form-item>
                    </el-col>
                    {elseif $item.type=='textarea'}
                    <el-col :span="20" style="width: {$item.width??'100%'}">
                        <el-form-item label="{$item.title}" prop="{$item.field}">
                            <el-input size="mini" type="textarea" row="3" v-model="form.{$item.field}" ></el-input>
                        </el-form-item>
                    </el-col>
                    {elseif $item.type=='files'}
                    <el-col :span="20" style="width: {$item.width??'50%'}">
                        <el-form-item label="{$item.title}" prop="{$item.field}">
                            <el-upload
                                    class="upload-demo"
                                    action="/module/upload/upload.php"
                                    :file-list="form.{$item.field}"
                                    :limit="{$item.limit??10}"
                                    :on-success="(response,file,fileList)=>uploadSuccess(response,file,fileList,'{$item.field}')"
                                    :before-upload="uploadBefore"
                                    :on-preview="handlePictureCardPreview"
                                    :on-remove="(file,fileList)=>handleRemove(file,fileList,'{$item.field}')">
                                <el-button size="small" type="primary">点击上传</el-button>
                                <!--                                <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>-->
                            </el-upload>
                        </el-form-item>
                    </el-col>
                    {elseif $item.type=='images'}
                    <el-col :span="20" style="width: {$item.width??'50%'}">
                        <el-form-item label="{$item.title}" prop="{$item.field}">
                            <el-upload
                                    action="/module/upload/upload.php"
                                    list-type="picture-card"
                                    :file-list="form.{$item.field}"
                                    :limit="{$item.limit??10}"
                                    :on-success="(response,file,fileList)=>uploadSuccess(response,file,fileList,'{$item.field}')"
                                    :before-upload="uploadBefore"
                                    :on-preview="handlePictureCardPreview"
                                    :on-remove="(file,fileList)=>handleRemove(file,fileList,'{$item.field}')">
                                <i class="el-icon-plus"></i>
                            </el-upload>
                        </el-form-item>
                    </el-col>
                    {/if}
                    {/if}
                    {/volist}
                </el-row>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogFormVisible = false" size="small">取 消</el-button>
                <el-button type="primary" @click="addSubmit" size="small">保 存</el-button>
            </div>
        </el-dialog>
        <el-dialog :visible.sync="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt="">
        </el-dialog>
        <import1 ref="import1" @refresh="getData()"></import1>
        <deptchoose ref="deptChoose" @ok="deptFun" :deptData="deptData"></deptchoose>
        <personnelchoose ref="personnelChoose" @ok="userFun" :userData="userData"></personnelchoose>
    </div>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.all.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                isAdmin:'{$isAdmin}'=='1'?true:false,
                title:'新增',
                searchFrom: {
            {volist name="fields.search" id="item"}
            {$item.field}: '{$item.default}',
                {/volist}
        },
            count:{
                check:0,
            },
            now: {},
            data: [],
                filelist: [],
                page: 1,
                pageSize: 20,
                total: 0,
                deptData: [],
                userData: [],
                dialogFormVisible: false,
                dialogImageUrl:'',
                dialogVisible:false,
                visible: false,
                loading: true,
                form:{},
            formRules: {},
            height: document.documentElement.clientHeight - document.getElementsByClassName('search_form')[0].offsetHeight - 190,
        };
        },
            components: {
                'deptchoose':  'url:/general/toppingsoft/public/vue/deptChoose.vue',
                    'personnelchoose':'url:/general/toppingsoft/public/vue/personnelChoose.vue',
                    'import1':'url:/general/toppingsoft/public/vue/import.vue',
            },
            mounted:function(){
                var that = this;
                window.addEventListener('resize',function(){
                    clearTimeout(that.resizeFlag);
                    that.resizeFlag =setTimeout(function(){
                        that.height = document.documentElement.clientHeight - document.getElementsByClassName('search_form')[0].offsetHeight - 190;
                    },300)
                })
            },
            methods: {
                selected:function(row,index){
                    return true;
                    // if(row.status==1&&row.remaining>0){
                    //     return true;
                    // }else{
                    //     return false;
                    // }
                },
                statusChange:function(){
                    this.searchFrom.page = 1;
                    this.getData();
                },
                handleSizeChange: function(val) {
                    this.pageSize = val;
                    this.getData();
                },
                handleCurrentChange: function(val) {
                    this.page = val;
                    this.getData();
                },
                add:function(){
                    this.form = {
                    {volist name="fields.add" id="item"}
                    {$item.field}:'{$item.default??""}',
                        {/volist}
                }
                    this.formRules = {
                    {volist name="fields.add" id="item"}
                    {if $item.require==1}
                    {$item.field}: [{required: true, message: "{$item.title}不能为空", trigger: "blur"}],
                        {/if}
                    {/volist}
                    }
                    this.title = '新增{$title}';
                    this.dialogFormVisible = true;
                },
                    edit:function(row){
                        this.form = row;
                        this.formRules = {
                        {volist name="fields.edit" id="item"}
                        {if $item.require==1}
                        {$item.field}: [{required: true, message: "{$item.title}不能为空", trigger: "blur"}],
                            {/if}
                        {/volist}
                        }
                        this.title = '修改{$title}';
                        this.dialogFormVisible = true;
                    },
                        info:function(row){
                            this.$refs.info.title = row.name;
                            this.$refs.info.open(row.id,this.searchFrom.type);
                        },
                        deleteList:function(row){
                            var _this = this;
                            _this.$confirm("确认删除？", "提示", {}).then(() => {
                                _this.addLoading = true;
                                var param = {};
                                param.id = row.id;
                                axios.post('del?model={$model}',param).then(function (res) {
                                    _this.addLoading = false;
                                    if( res.data.code == 0 ) {
                                        _this.$message({
                                            message: res.data.msg,
                                            type: "success"
                                        });
                                        _this.dialogFormVisible = false;
                                        _this.getData();
                                    }else{
                                        _this.$message({
                                            message: res.data.msg,
                                            type: "error"
                                        });
                                    }
                                }).catch(function (error) {
                                    console.log(error);
                                });
                            });
                        },
                        //数据初始化
                        reset:function(){
                            this.searchFrom = {is_fixed:this.searchFrom.is_fixed};
                            this.page = 1;
                            this.pageSize = 20;
                            this.getData();
                        },
                        //数据加载
                        getData:function() {
                            var _this = this;
                            var param = _this.searchFrom;
                            param._ajax = 1;
                            param.page = _this.page;
                            param.limit = _this.pageSize;
                            var url = "list";
                            axios.post('list?model={$model}',param).then(function (res) {
                                if( res.data.code == 0 ){
                                    _this.data = res.data.data.data;
                                    _this.searchFrom.page = res.data.data.current_page;
                                    _this.searchFrom.pageSize = res.data.data.per_page;
                                    _this.total = res.data.data.total;
                                    _this.count.check = res.data.data.check;
                                }else{
                                    _this.$message({
                                        message: res.data.msg,
                                        type: "error"
                                    });
                                }
                                _this.loading = false;
                            }).catch(function (error) {
                                console.log(error);
                                _this.loading = false;
                            });
                        },
                        addSubmit: function () {
                            var _this = this;
                            this.$refs.form.validate(valid => {
                                if (valid) {
                                    _this.addLoading = true;
                                    var param = _this.form;
                                    axios.post('save?model={$model}',param).then(function (res) {
                                        _this.addLoading = false;
                                        if( res.data.code == 0 ) {
                                            _this.$message({
                                                message: res.data.msg,
                                                type: "success"
                                            });
                                            _this.dialogFormVisible = false;
                                            _this.getData();
                                        }else{
                                            _this.$message({
                                                message: res.data.msg,
                                                type: "error"
                                            });
                                        }
                                    }).catch(function (error) {
                                        console.log(error);
                                    });
                                }
                            });
                        },
                        uploadBefore(file) {
                            /*const isJPG = file.type === 'image/jpeg';
                            const isPNG = file.type === 'image/png';
                            if(!isJPG&&!isPNG){
                                this.$message.error('请上传jpg图片');
                            }
                            return isJPG||isPNG;*/
                        },
                        uploadSuccess(res, file,fileList,field) {
                            var files = [];
                            console.log(fileList)
                            for(var i in fileList){
                                files.push(fileList[i].response??fileList[i]);
                            }
                            this.form[field] = files;
                        },
                        handleRemove(file, fileList,field) {
                            var files = [];
                            for(var i in fileList){
                                files.push(fileList[i].response);
                            }
                            this.form[field] = files;
                        },
                        handlePictureCardPreview(file) {
                            this.dialogImageUrl = file.url;
                            this.dialogVisible = true;
                        },
                        /**
                         * 部门选择
                         * */
                        choiceDept:function(fields,type,isRadio) {
                            this.now = {type:type,fields:fields};
                            this.$refs.deptChoose.visible = true;
                            this.$refs.deptChoose.isRadio = isRadio; // 是否单选  true 单选  false 多选
                            this.$refs.deptChoose.checkedKeys=this[type][fields[0]].toString().split(",");
                            this.$refs.deptChoose.initialize();
                        },
                        /**
                         * 人员选择
                         * */
                        choiceUser:function(fields,type,isRadio=true){
                            this.now = {type:type,fields:fields};
                            this.$refs.personnelChoose.visible = true;
                            this.$refs.personnelChoose.isRadio = isRadio; // 是否单选  true 单选  false 多选
                            this.$refs.personnelChoose.initialize(this[type][fields[0]].toString().split(","));
                        },
                        /**
                         * 部门选择回调数据
                         * */
                        deptFun:function(data)
                        {
                            var _k = []
                            var _v = [];
                            for(var i in data){
                                _k.push(data[i]['dept_id']);
                                _v.push(data[i]['dept_name']);
                            }
                            this[_this.now.type][this.now.fields[0]] = _k.join(",");
                            this[_this.now.type][this.now.fields[1]] = _v.join(",");
                        },
                        /**
                         * 人员选择回调数据
                         * */
                        userFun:function(data) {
                            var user_id = '';
                            var user_name = '';
                            for (var i in data){
                                user_id += i==0?data[i].uuid:","+data[i].uuid;
                                user_name += i==0?data[i].name:","+data[i].name;
                            }
                            this[this.now.type][this.now.fields[0]] = user_id;
                            this[this.now.type][this.now.fields[1]] = user_name;
                        },
                        upShow:function(row){
                            var _this = this;
                            var url = "upStatus";
                            axios.post(url,{id:row.id}).then(function (res) {
                                if( res.data.code == 0) {
                                    row.is_show = res.data.data.status;
                                }else{
                                    _this.$message({
                                        message: res.data.msg,
                                        type: "error"
                                    });
                                    row.is_show = !row.is_show;
                                }
                            }).catch(function (error) {
                                console.log(error);
                            });
                        },
                        import1: function () {
                            this.$refs.import1.templateUrl = 'importTemplate?model={$model}';
                            this.$refs.import1.submitUrl = 'import?model={$model}';
                            this.$refs.import1.title = '{$title}导入';
                            this.$refs.import1.open();
                        },
                        export1: function () {
                            let where = "";
                            let type = '';
                            //获得where
                            where = 'type='+type;
                            for (let index in this.searchFrom) {
                                if (this.searchFrom[index] !== '' && this.searchFrom[index] !== [] && this.searchFrom[index] !== {}) {
                                    let str = "";
                                    if(index=='type'){
                                        for(let i in this.searchFrom.type){
                                            type += this.searchFrom.type[i]+',';
                                        }
                                        str += 'type='+type;
                                    }else{
                                        str += index+'='+this.searchFrom[index];
                                    }
                                    where += "&" + str;
                                }
                            }
                            let url = "list?model={$model}&excel=1&" + where;
                            //window.open(url);
                            location.href = url;
                        },
                    },
                        mounted() {
                            //获取列表
                            this.getData();
                        }
                    })
</script>


</body>
</html>