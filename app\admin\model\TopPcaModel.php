<?php
declare (strict_types=1);

namespace app\admin\model;

use think\Model;

/**
 * @mixin \think\Model
 */
class TopPcaModel extends Model
{
	//

	public $table = 'top_pca';

	public $order = 'level,sort asc';

	public function getPcaList($level = 1)
	{
		$data = $this->where([['enabled', '=', 1], ['level', '<=', $level]])->order($this->order)->select()->toArray();
		foreach ($data as $k => $v) {
			$data[$k]['label'] = '(' . $v['code'] . ')' . $v['name'];
			if ($v['level'] < $level) $data[$k]['disabled'] = true;
		}
		return $this->treeData($data);
	}

	public function treeData($datas, $root = 0, $pk = 'id', $pid = 'pid', $child = 'children')
	{

		$tree = array();
		if (is_array($datas)) {
			$refer = array();
			foreach ($datas as $key => $data) {
				$refer[$data[$pk]] = &$datas[$key];
			}
			foreach ($datas as $key => $data) {
				$parentId = $data[$pid];
				if ($root == $parentId) {
					$tree[] = &$datas[$key];
				} else {
					if (isset($refer[$parentId])) {
						$parent = &$refer[$parentId];
						$parent[$child][$data[$pk]] = &$datas[$key];
						$parent[$child] = array_values($parent[$child]);
					}
				}
			}
		}
		return array_merge_recursive($tree);
	}

	public function getNames($id){
		if (is_string($id) && strpos($id, ',') !== false){
			$id = explode(',', $id);
		}
		$data = $this->where('id', 'in', $id)->order($this->order)->select()->toArray();
		$data_name = [];
		foreach ($data as $k=>$v){
			$data_name[] = '('.$v['code'].')'.$v['name'];
		}
		return implode(',', $data_name);
	}
}
