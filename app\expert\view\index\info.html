<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>企业信息认证</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/company.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .my-info { line-height:40px;}
        .my-info label { width:160px; display: inline-block;text-align:right;}
        .success .el-descriptions__header {
            border-left:5px solid #67c23a;
            color:#67c23a;
            line-height: 40px;
            padding-left:20px;
            background: -ms-linear-gradient(left, #f0f9eb, #fff); /* IE 10+ */
            background: -moz-linear-gradient(left, #f0f9eb, #fff); /* Firefox */
            background: -webkit-linear-gradient(left, #f0f9eb, #fff); /* Chrome, Safari, Opera */
            background: linear-gradient(to right, #f0f9eb, #fff); /* 标准语法 */
        }
        .primary .el-descriptions__header {
            margin-top: 20px;
            border-left:5px solid #436CE6;
            color:#436CE6;
            line-height: 40px;
            padding-left:20px;
            background: -ms-linear-gradient(left, #F4F7FE, #fff); /* IE 10+ */
            background: -moz-linear-gradient(left, #F4F7FE, #fff); /* Firefox */
            background: -webkit-linear-gradient(left, #F4F7FE, #fff); /* Chrome, Safari, Opera */
            background: linear-gradient(to right, #F4F7FE, #fff); /* 标准语法 */
        }
        .success .el-descriptions-item__label.is-bordered-label { background-color: #f0f9eb; width:140px;}
        .primary .el-descriptions-item__label.is-bordered-label { background-color: #F4F7FE; width:140px;}
        .el-descriptions .is-bordered td { width:250px;}
    </style>
</head>
<body style="background-color:#ffffff;">
<div id="app" v-cloak>
    <div class="centainer">
        <el-descriptions class="{$class}" title="{$title}" :column="3" border>
            <template slot="extra">
<!--                <el-button type="primary" size="small">详细信息</el-button>-->
                <el-button v-if="'{$expert.status}'==7" type="primary" size="small" @click="auth">信息更新</el-button>
            </template>
            <el-descriptions-item label="照片">
                <el-image
                        style="width: 100px; height: 100px"
                        src="{$expert.headUrl}"
                        :preview-src-list="['{$expert.headUrl}']">
                </el-image>
            </el-descriptions-item>
            <el-descriptions-item label="姓名">
                {$expert.name}
            </el-descriptions-item>
            <el-descriptions-item label="手机号">
                {$expert.mobile}
            </el-descriptions-item>
            <el-descriptions-item label="性别">
                {$expert.sex}
            </el-descriptions-item>
            <el-descriptions-item label="所属评审单位">
                {$expert.org_name}
            </el-descriptions-item>
            <el-descriptions-item label="邮箱">
                {$expert.email}
            </el-descriptions-item>
            <el-descriptions-item label="出生日期">
                {$expert.birthday}
            </el-descriptions-item>
            <el-descriptions-item label="民族">
                {$expert.nation}
            </el-descriptions-item>
            <el-descriptions-item label="QQ">
                {$expert.qq}
            </el-descriptions-item>
            <el-descriptions-item label="现住址">
                {$expert.address}
            </el-descriptions-item>
            <el-descriptions-item label="学校">
                {$expert.school}
            </el-descriptions-item>
            <el-descriptions-item label="专业">
                {$expert.speciality}
            </el-descriptions-item>
            <el-descriptions-item label="学历">
                {$expert.education}
            </el-descriptions-item>
            <el-descriptions-item label="现工作单位">
                {$expert.employer}
            </el-descriptions-item>
            <el-descriptions-item label="职务">
                {$expert.position}
            </el-descriptions-item>
            <el-descriptions-item label="参加工作时间">
                {$expert.work_date}
            </el-descriptions-item>
            <el-descriptions-item label="从事安全生产工作时间">
                {$expert.position_date}
            </el-descriptions-item>
            <el-descriptions-item label="专业技术职称">
                {$expert.professional}
            </el-descriptions-item>
            <el-descriptions-item label="职称证书编号">
                {$expert.professional_number}
            </el-descriptions-item>
            <el-descriptions-item label="安全评价资格师等级">
                {$expert.secure}
            </el-descriptions-item>
            <el-descriptions-item label="安全评价师证书编号">
                {$expert.secure_number}
            </el-descriptions-item>
            <el-descriptions-item label="注册安全工程师证书编号">
                {$expert.reg_secure_number}
            </el-descriptions-item>
            <el-descriptions-item label="其他证书编号">
                {$expert.other_number}
            </el-descriptions-item>
            <el-descriptions-item label="擅长专业">
                {$expert.major}
            </el-descriptions-item>
            <el-descriptions-item label="聘用日期">
                {$expert.employ_date}
            </el-descriptions-item>
            <el-descriptions-item label="受聘情况" :span="3">
                {$expert.offer_info}
            </el-descriptions-item>
            <el-descriptions-item label="个人学习及工作简历" :span="3">
                {$expert.resume}
            </el-descriptions-item>
        </el-descriptions>
    </div>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                title: '',
                igrameSrc: '',
                loading: false,
                data: {
                    date:'',
                    license_start:'',
                    license_end:'',
                    enterprise_size:'',
                },
                rules: {
                },
                config:[],
                height: document.documentElement.clientHeight,
            };
        },
        components: {
        },
        methods: {
            auth: function(){
                var _this = this;
                _this.$confirm("确认信息变更？", "提示", {}).then(() => {
                    location.href = 'auth';
                });
            }
        },
        mounted() {
        }
    })
</script>


</body>
</html>