<?php

namespace app\api\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use app\company\model\GradingModel;
use app\model\FileModel;
use app\model\SettingModel;
use hg\apidoc\annotation as Apidoc;
use think\App;
use think\facade\Cache;
use think\facade\Db;

/**
 * @Apidoc\Title("区县应急局端接口")
 * @Apidoc\Group("Index")
 * @Apidoc\Sort(7)
 */
class Area extends Base {


    public function __construct(App $app)
    {
        parent::__construct($app);
        $token = $this->request->param('token');
        $user = Cache::get('token_'.$token);
        if($user['user_type']==='area'){
            $this->user = $user['userinfo'];
        }else{
            result('',8001,'暂无权限');
        }
    }



    /**
     * NotHeaders
     * @Apidoc\Title("企业信息列表")
     * @Apidoc\Desc("企业信息列表")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Param("token", type="string",require=true, default="5f1052124fdb69cc11384c8619fb0e51",desc="登陆接口获取")
     * @Apidoc\Param("search", type="string",require=true, default="",desc="关键字查询（企业名称/统一社会信用代码）")
     * @Apidoc\Returned("id", type="int", desc="消息id")
     * @Apidoc\Returned("name", type="string", desc="企业名称")
     * @Apidoc\Returned("legal", type="string", desc="法定代表人")
     * @Apidoc\Returned("industry", type="string", desc="行业/专业")
     * @Apidoc\Returned("is_ca", type="string", desc="是否获得证书")
     * @Apidoc\Returned("mb_operate_address", type="string", desc="生产经营地点")
     * @Apidoc\Returned("postal_code", type="string", desc="统一社会信用代码")
     * @Apidoc\Returned("license_date", type="string", desc="信用代码有效期")
     * @Apidoc\Returned("reg_money", type="string", desc="注册资本")
     * @Apidoc\Returned("manager", type="string", desc="安全管理联系人")
     * @Apidoc\Returned("personnel", type="string", desc="员工总数")
     * @Apidoc\Returned("area", type="string", desc="营业场所面积")
     * @Apidoc\Returned("personnel_full", type="string", desc="专职安全管理人数")
     * @Apidoc\Returned("personnel_part", type="string", desc="兼职安全管理人数")
     * @Apidoc\Returned("personnel_special", type="string", desc="特种作业人数")
     * @Apidoc\Returned("mb_economy_sector", type="string", desc="经济类型")
     * @Apidoc\Returned("enterprise_size", type="string", desc="企业规模")
     * @Apidoc\Returned("date", type="string", desc="成立日期")
     */
    public function companyList($search='',$limit=10) {
        $user = $this->user;
        $where = [
            ['area_id','=',$user['id']],
            ['dept_id','=',$user['dept_id']],
            ['name|postal_code','like',"%{$search}%"],
        ];
        $res = Db::table('top_company_info')->where($where)->order('id desc');
        $res = $res->paginate($limit)->each(function ($item, $key) {
            $item = self::codeToText($item);
            return $item;
        });
        result($res);
    }


    /**
     * NotHeaders
     * @Apidoc\Title("企业信息详情")
     * @Apidoc\Desc("企业信息详情")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Param("token", type="string",require=true, default="5f1052124fdb69cc11384c8619fb0e51",desc="登陆接口获取")
     * @Apidoc\Param("id", type="int",require=true, default="1",desc="企业id")
     * @Apidoc\Returned("id", type="int", desc="消息id")
     * @Apidoc\Returned("name", type="string", desc="企业名称")
     * @Apidoc\Returned("legal", type="string", desc="法定代表人")
     * @Apidoc\Returned("industry", type="string", desc="行业/专业")
     * @Apidoc\Returned("is_ca", type="string", desc="是否获得证书")
     * @Apidoc\Returned("mb_operate_address", type="string", desc="生产经营地点")
     * @Apidoc\Returned("postal_code", type="string", desc="统一社会信用代码")
     * @Apidoc\Returned("license_date", type="string", desc="信用代码有效期")
     * @Apidoc\Returned("reg_money", type="string", desc="注册资本")
     * @Apidoc\Returned("manager", type="string", desc="安全管理联系人")
     * @Apidoc\Returned("personnel", type="string", desc="员工总数")
     * @Apidoc\Returned("area", type="string", desc="营业场所面积")
     * @Apidoc\Returned("personnel_full", type="string", desc="专职安全管理人数")
     * @Apidoc\Returned("personnel_part", type="string", desc="兼职安全管理人数")
     * @Apidoc\Returned("personnel_special", type="string", desc="特种作业人数")
     * @Apidoc\Returned("mb_economy_sector", type="string", desc="经济类型")
     * @Apidoc\Returned("enterprise_size", type="string", desc="企业规模")
     * @Apidoc\Returned("date", type="string", desc="成立日期")
     */
    public function companyInfo($id=0) {
        $user = $this->user;
        $where = [
            ['id','=',$id],
            ['area_id','=',$user['id']],
            ['dept_id','=',$user['dept_id']],
        ];
        $re = Db::table('top_company_info')->where($where)->find();
        $re = self::codeToText($re);
        $data = [
            'name' => $re['name'],
            'legal' => $re['legal'],
            'industry' => $re['industry'].'/'.$re['specialty'],
            'is_ca' => $re['ca_status']==1?'是':'否',
            'mb_operate_address' => $re['mb_operate_address'],
            'postal_code' => $re['postal_code'],
            'license_date' => $re['license_date'],
            'reg_money' => $re['reg_money'],
            'manager' => $re['manager'],
            'personnel' => $re['personnel'],
            'area' => $re['area'],
            'personnel_full' => $re['personnel_full'],
            'personnel_part' => $re['personnel_part'],
            'personnel_special' => $re['personnel_special'],
            'mb_economy_sector' => $re['mb_economy_sector'],
            'enterprise_size' => $re['enterprise_size'],
            'date' => $re['date'],
        ];
        result($data);
    }

    /**
     * NotHeaders
     * @Apidoc\Title("消息通知列表")
     * @Apidoc\Desc("消息通知列表")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Param("token", type="string",require=false, default="",desc="登陆接口获取")
     * @Apidoc\Param("page", type="int", default="1",desc="页码")
     * @Apidoc\Param("limit", type="int", default="10",desc="每页数据量")
     * @Apidoc\Returned("data", type="list", desc="消息通知",children={
     * @Apidoc\Returned("id", type="int", desc="消息id"),
     * @Apidoc\Returned("sms_type", type="string", desc="消息类型"),
     * @Apidoc\Returned("sms_content", type="string", desc="消息内容"),
     * @Apidoc\Returned("sms_url", type="string", desc="消息链接"),
     * @Apidoc\Returned("sms_time", type="string", desc="消息时间"),
     *     })
     * @Apidoc\Returned("total", type="int", desc="数据总数")
     * @Apidoc\Returned("per_page", type="int", desc="每页数量")
     * @Apidoc\Returned("current_page", type="int", desc="当前页码")
     * @Apidoc\Returned("last_page", type="int", desc="最后一页页码")
     */
    public function message($limit=10) {
        $user = $this->user;
        if(in_array($user['user_type'],['company','city','area','org','expert'])) {
            $data = Db::table('top_message')
                ->where(['user_type' => $user['user_type'], 'user_id' => $user['user_id'], 'is_read' => 0])
                ->field('id,sms_type,sms_content,sms_url,sms_time')->order('sms_time desc')
                ->paginate($limit)->each(function ($item, $key) {
                    $item['sms_time'] = date('Y-m-d H:i',strtotime($item['sms_time']));
                    return $item;
                });
        }
        result($data);
    }

    /**
     * NotHeaders
     * @Apidoc\Title("消息通知详情")
     * @Apidoc\Desc("消息通知详情")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Param("token", type="string",require=true, default="5f1052124fdb69cc11384c8619fb0e51",desc="登陆接口获取")
     * @Apidoc\Param("id", type="int",require=true, default="",desc="消息id")
     * @Apidoc\Returned("id", type="int", desc="消息id")
     * @Apidoc\Returned("sms_type", type="string", desc="消息类型")
     * @Apidoc\Returned("sms_content", type="string", desc="消息内容")
     * @Apidoc\Returned("sms_url", type="string", desc="消息链接")
     * @Apidoc\Returned("sms_time", type="string", desc="消息时间")
     */
    public function messageInfo($id=0) {
        $user = $this->user;
        if(in_array($user['user_type'],['company','city','area','org','expert'])) {
            $data = Db::table('top_message')
                ->where(['user_type' => $user['user_type'], 'user_id' => $user['user_id'], 'id' => $id])
                ->field('id,sms_type,sms_content,sms_url,sms_time')->order('sms_time desc')
                ->find();
            $data['sms_time'] = date('Y-m-d H:i',strtotime($data['sms_time']));
        }
        result($data);
    }

    /**
     * NotHeaders
     * @Apidoc\Title("定级申请列表")
     * @Apidoc\Desc("定级申请列表")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("token", type="string",require=true, default="5f1052124fdb69cc11384c8619fb0e51",desc="登陆接口获取")
     * @Apidoc\Param("page", type="int", default="1",desc="页码")
     * @Apidoc\Param("limit", type="int", default="10",desc="每页数据量")
     * @Apidoc\Returned("grads", type="data", desc="定级申请列表",children={
     * @Apidoc\Returned("id", type="int", desc="申请表id"),
     * @Apidoc\Returned("company_name", type="string", desc="企业名称"),
     * @Apidoc\Returned("type", type="string", desc="申请类型"),
     * @Apidoc\Returned("date", type="string", desc="申请日期"),
     * @Apidoc\Returned("status", type="string", desc="状态{0：未提交，1：审批中，2：整改中，3：整改审批中，5：未通过，7：已通过，9：放弃评审}"),
     *     })
     * @Apidoc\Returned("total", type="int", desc="数据总数")
     * @Apidoc\Returned("per_page", type="int", desc="每页数量")
     * @Apidoc\Returned("current_page", type="int", desc="当前页码")
     * @Apidoc\Returned("last_page", type="int", desc="最后一页页码")
     */
    public function gradList($limit=10) {
        $user = $this->user;
        $where = [
            ['area_id','=',$user['id']],
            ['dept_id','=',$user['dept_id']],
        ];
        $data = Db::table('top_grading')
            ->where($where)
            ->field('id,company_name,type,date,status')->order('apply_time desc')
            ->paginate($limit)->each(function ($item, $key) {
                $item['sms_time'] = date('Y-m-d H:i',strtotime($item['sms_time']));
                return $item;
            });
        result($data);
    }

    /**
     * NotHeaders
     * @Apidoc\Title("定级申请详情")
     * @Apidoc\Desc("定级申请详情")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("token", type="string",require=true, default="5f1052124fdb69cc11384c8619fb0e51",desc="登陆接口获取")
     * @Apidoc\Param("id", type="int",require=true, default="",desc="申请单id")
     * @Apidoc\Returned("id", type="int", desc="id")
     * @Apidoc\Returned("comapny_name", type="string", desc="企业名称")
     * @Apidoc\Returned("industry", type="string", desc="行业")
     * @Apidoc\Returned("specialty", type="string", desc="专业")
     * @Apidoc\Returned("code", type="string", desc="证书编号")
     * @Apidoc\Returned("level", type="string", desc="证书等级")
     * @Apidoc\Returned("start", type="string", desc="有效期开始时间")
     * @Apidoc\Returned("ends", type="string", desc="有效期结束时间")
     * @Apidoc\Returned("status", type="string", desc="状态")
     */
    public function gradInfo($id='') {
        $user = $this->user;
        $where = [
            ['id','=',$id],
            ['area_id','=',$user['id']],
            ['dept_id','=',$user['dept_id']],
        ];
        $re = Db::table('top_grading')->where($where)->find();
        $re = GradingModel::codeToText($re);
        $re['prcs'] = Db::table('top_grading_approval')->where(['grading_id'=>$re['id']])->order('id')
            ->select()->each(function ($item, $key) {
                $files = empty($item['check_files'])?[]:explode(',',$item['check_files']);
                foreach ($files as $v){
                    $f = FileModel::getFile(0,$v,'');
                    $item['files'][] = [
                        'id' => $f['id'],
                        'name' => $f['name'],
                        'url' => $f['url'],
                    ];
                }
                $item['end_time'] = empty($item['end_time'])?'':date('Y-m-d',strtotime($item['end_time']));
                $item['remark'] = empty($item['remark'])?$item['check_content']:$item['remark'];
                return $item;
            })->toArray();
        $re['steps'] = [];
        $re['stepsActive'] = 1;
        if(in_array($re['status'],[5,7,9])){
            foreach ($re['prcs'] as $v){
                $re['steps'][] = [
                    'label' => $v['prcs_name'],
                    'status' => 'success',
                ];
            }
            $re['steps'][count($re['steps'])-1]['status'] = in_array($re['status'],[5,9])?'error':'success';
            $re['stepsActive'] = count($re['steps']);
        }else{
            $i = 0;
            foreach (config('global.grading_prcs') as $k=>$v){
                if((!in_array($k,[5,6,7])||in_array($re['reform_status'],[1,2]))&&!in_array($k,[10])){
                    $re['steps'][] = [
                        'label' => $v['title'],
                        'status' => '',
                    ];
                }
                if($re['prcs_id']==$k){
                    $re['stepsActive'] = $i;
                }
                $i++;
            }
        }
//        $re['files'] = config('global.grading_files');
        result($re);
    }


    //参数格式化
    public static function codeToText($info){
        if(empty($info)){
            return $info;
        }
        $pcas = SettingModel::getPcasAll();
        $info['reg_address'] = empty($info['reg_address'])?[]:explode(',',$info['reg_address']);
        $info['mb_reg_address'] = '';
        foreach ($info['reg_address'] as $k=>$v){
            $info['mb_reg_address'] .= $pcas[$v];
        }
        $info['mb_reg_address'] .= $info['reg_address_info'];
        $info['operate_address'] = empty($info['operate_address'])?[]:explode(',',$info['operate_address']);
        $info['mb_operate_address'] = '';
        foreach ($info['operate_address'] as $k=>$v){
            $info['mb_operate_address'] .= $pcas[$v];
        }
        $info['mb_operate_address'] .= $info['operate_address_info'];
        $info['region'] = empty($info['region'])?[]:explode(',',$info['region']);
        $info['mb_region'] = '';
        foreach ($info['region'] as $k=>$v){
            $info['mb_region'] .= $pcas[$v];
        }
        $info['economy_sector'] = explode(',',$info['economy_sector']);
        $info['mb_economy_sector'] = implode('/',$info['economy_sector']);
        $info['economy_type'] = explode(',',$info['economy_type']);
        $info['mb_economy_type'] = implode('/',$info['economy_type']);
        $info['license_date'] = $info['license_start'].'至'.$info['license_end'];
        $info['licenseUrl'] = empty($info['license'])?'':FileModel::getFile(0,$info['license']);
        $info['aocUrl'] = empty($info['aoc'])?'':FileModel::getFile(0,$info['aoc']);
        foreach ($info as $k=>$v){
            $info[$k] = $v==='0000-00-00'?'':$v;
            if(is_int($v)){
                $info[$k] = (string)$v;
            }
            if($v===null){
                $info[$k] = '';
            }
        }
        return $info;
    }


}
