<?php

namespace app\company\model;

use app\model\SettingModel;
use think\facade\Db;
use think\Model;
use Medoo\Mdb;
use app\model\FileModel;

class GradingModel extends Model
{
    public static function gradingSave($param,$id=0)
    {
        $company = Db::table('top_company_info')->where([['id','=',$_SESSION['company']['id']],['status','=','1']])->find();
        if(empty($company)||empty($_SESSION['company']['id'])){
            result('',1003,'请先认证企业信息');
        }
        if($param['type']=='初次评审'){
            if(strtotime($company['st_date'])>time()){
//            result('',1003,'请于'.$company['st_date'].'后提交申请');//正式上线打开
            }
        }else if($param['type']=='复评申请'){
            if(strtotime($company['ca_date'].' +33month')>time()){
//            result('',1003,'请于'.date('Y-m-d',strtotime($company['ca_date'].' +33month')).'后提交申请');//正式上线打开
            }
        }
        $company = CompanyModel::codeToText($company);
        $data = [
            'company_id' => $company['id'],
            'company_name' => $company['name'],
            'enterprise_size' => $company['enterprise_size'],
            'address' => $company['mb_reg_address'],
            'company_code' => $company['license_number'],
            'legal' => $company['legal'],
            'legal_mobile' => $company['legal_mobile'],
            'legal_email' => $company['legal_email'],
            'manager' => $company['manager'],
            'manager_mobile' => $company['manager_mobile'],
            'manager_email' => $company['manager_email'],
            'industry' => $company['industry'],
            'specialty' => $company['specialty'],
            'area_id' => $company['area_id'],
            'city_id' => $company['city_id'],
            'dept_id' => $company['dept_id'],
            'level' => $param['level'],
            'type' => $param['type'],
            'nature' => $param['nature'],
            'advisory' => $param['advisory'],
            'content' => $param['content'],
            'date' => date('Y-m-d'),
            'status' => $param['status']==1?1:0,
            'prcs_id' => $param['status']==1?2:1,
            'apply_user_id' => $_SESSION['company']['user_id'],
            'apply_user_name' => $_SESSION['company']['user_name'],
            'apply_time' => date('Y-m-d H:i:s'),
        ];
        $data['prcs_name'] = config('global.grading_prcs')[$data['prcs_id']]['title'];
        if($data['status']==1){
            $re = Db::table('top_grading')->where([['company_id','=',$_SESSION['company']['id']],['status','=','1']])->find();
            if($re){
                result('',1003,'重复提交');
            }
        }
        foreach (config('global.grading_files') as $v){
            $codes = [];
//            dd($param[$v['field']]);
            foreach ($param[$v['field']] as $v1){
                $codes[] = $v1['code'];
                FileModel::saveFile($v1['code'],'company/grading/'.date('Ym'));
            }
            $data[$v['field']] = implode(',',$codes);
        }
        if($id){
            $re = Db::table('top_grading')->where(['id'=>$id])->find();
            if(empty($re)){
                result('',1003,'数据不存在或已删除');
            }
            if($re['status']>0){
                result('',1003,'已提交的申请不可修改');
            }
//            dd($data);
            $re = Db::table('top_grading')->where(['id'=>$id])->update($data);
            $approal = Db::table('top_grading_approval')->where(['grading_id'=>$id,'prcs_id'=>1])->order('id desc')->find();
            $apply_data = [
                'status' => $data['status']==1?7:1,
                'status_name' => $data['status']==1?'已提交':'未提交',
            ];
            if($data['status']==1){
                $apply_data['end_user_id'] = $_SESSION['company']['user_id'];
                $apply_data['end_user_name'] = $_SESSION['company']['user_name'];
                $apply_data['end_time'] = date('Y-m-d H:i:s');
            }
            Db::table('top_grading_approval')->where(['id'=>$approal['id']])->update($apply_data);
        }else{
            $id = Db::table('top_grading')->insertGetId($data);
            $id = Db::table('top_grading')->where($data)->field('id')->order('id desc')->find()['id'];
            $apply_data = [
                'grading_id' => $id,
                'prcs_id' => 1,
                'prcs_name' => '企业提交',
                'status' => $data['status']==1?7:1,
                'status_name' => $data['status']==1?'已提交':'未提交',
                'create_user_id' => $_SESSION['company']['user_id'],
                'create_user_name' => $_SESSION['company']['user_name'],
                'create_time' => date('Y-m-d H:i:s'),
                'receive_user_id' => $_SESSION['company']['user_id'],
                'receive_user_name' => $_SESSION['company']['user_name'],
                'receive_time' => date('Y-m-d H:i:s'),
            ];
            if($data['status']==1){
                $apply_data['end_user_id'] = $_SESSION['company']['user_id'];
                $apply_data['end_user_name'] = $_SESSION['company']['user_name'];
                $apply_data['end_time'] = date('Y-m-d H:i:s');
            }
            Db::table('top_grading_approval')->insertGetId($apply_data);
        }
        if($data['status']==1){
            if(in_array($data['level'],['一级','二级'])){
                $apply_data = [
                    'grading_id' => $id,
                    'prcs_id' => 3,
                    'prcs_name' => config('global.grading_prcs')[3]['title'],
                    'status' => 1,
                    'params' => 'c:'.$company['city_id'],
                    'status_name' => '审核中',
                    'create_user_id' => $_SESSION['company']['user_id'],
                    'create_user_name' => $_SESSION['company']['user_name'],
                    'create_time' => date('Y-m-d H:i:s'),
                ];
                Db::table('top_grading_approval')->insertGetId($apply_data);
            }else{
                $apply_data = [
                    'grading_id' => $id,
                    'prcs_id' => 2,
                    'prcs_name' => config('global.grading_prcs')[2]['title'],
                    'status' => 1,
                    'params' => 'a:'.$company['area_id'],
                    'status_name' => '审核中',
                    'create_user_id' => $_SESSION['company']['user_id'],
                    'create_user_name' => $_SESSION['company']['user_name'],
                    'create_time' => date('Y-m-d H:i:s'),
                ];
                Db::table('top_grading_approval')->insertGetId($apply_data);
            }
        }
        return $id;
    }


    //参数格式化
    public static function codeToText($info){
        if(empty($info)){
            return $info;
        }
        foreach (config('global.grading_files') as $v){
            if(isset($info[$v['field']])){
                $codes = explode(',',$info[$v['field']]);
                $info[$v['field']] = [];
                foreach ($codes as $v1){
                    if(!empty($v1)){
                        $f = FileModel::getFile(0,$v1,'');
                        $info[$v['field']][] = [
                            'id' => $f['id'],
                            'code' => $f['code'],
                            'name' => $f['name'],
                            'url' => $f['url'],
                            'ext' => $f['ext'],
                        ];
                    }
                }
            }
        }
        foreach ($info as $k=>$v){
            $info[$k] = $v==='0000-00-00'?'':$v;
            if(is_int($v)){
                $info[$k] = (string)$v;
            }
            if($v===null){
                $info[$k] = '';
            }
        }
        return $info;
    }


}