<?php
/**
 * 批量加密数据库字段脚本
 * 
 * 用途：批量加密指定表的name和mobile字段
 * 表：top_area_user, top_city_user, top_org_user, top_commpany_user, top_expert
 * 
 * 使用方法：
 * php script/batch_encrypt_fields.php
 * 
 * 注意：运行前请备份数据库！
 */

// 设置脚本执行时间限制
set_time_limit(0);
ini_set('memory_limit', '512M');

// 引入ThinkPHP框架
require_once __DIR__ . '/../vendor/autoload.php';

// 启动应用
$app = new \think\App();
$app->initialize();

// 引入common.php文件以使用hsmCacheEncrypt函数
require_once __DIR__ . '/../app/common.php';

use think\facade\Db;
use think\facade\Config;

class BatchEncryptFields
{
    // 需要加密的表和字段配置
    private $tables = [
        'top_area_user' => ['name', 'mobile'],
        'top_city_user' => ['name', 'mobile'],
        'top_org_user' => ['name', 'mobile'],
        'top_commpany_user' => ['name', 'mobile'],
        'top_expert' => ['name', 'mobile']
    ];
    
    // 批处理大小
    private $batchSize = 100;
    
    // 统计信息
    private $stats = [
        'total_processed' => 0,
        'total_encrypted' => 0,
        'total_skipped' => 0,
        'errors' => []
    ];
    
    public function __construct()
    {
        echo "=== 批量字段加密工具 ===\n";
        echo "开始时间: " . date('Y-m-d H:i:s') . "\n";
        echo "内存限制: " . ini_get('memory_limit') . "\n";
        echo "批处理大小: {$this->batchSize}\n";
        echo "========================\n\n";

        // 创建日志目录
        $this->createLogDirectory();
    }

    /**
     * 创建日志目录
     */
    private function createLogDirectory()
    {
        $logDir = __DIR__ . '/../runtime/log/encrypt';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
    }

    /**
     * 记录日志
     */
    private function log($message, $level = 'INFO')
    {
        $logFile = __DIR__ . '/../runtime/log/encrypt/batch_encrypt_' . date('Y-m-d') . '.log';
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] [{$level}] {$message}\n";
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
        echo $logMessage;
    }
    
    /**
     * 执行批量加密
     */
    public function execute()
    {
        try {
            // 检查加密功能是否开启
            if (!$this->checkEncryptionEnabled()) {
                $this->log("错误：加密功能未开启，请检查配置 app.is_hsm_encrypt", 'ERROR');
                return false;
            }

            // 执行前确认
            if (!$this->confirmExecution()) {
                $this->log("用户取消执行", 'INFO');
                return false;
            }

            foreach ($this->tables as $tableName => $fields) {
                $this->log("开始处理表: {$tableName}");
                $this->processTable($tableName, $fields);
                $this->log("完成处理表: {$tableName}");
            }

            $this->printSummary();
            return true;

        } catch (Exception $e) {
            $this->log("执行过程中发生错误: " . $e->getMessage(), 'ERROR');
            $this->log("错误文件: " . $e->getFile(), 'ERROR');
            $this->log("错误行号: " . $e->getLine(), 'ERROR');
            return false;
        }
    }

    /**
     * 执行前确认
     */
    private function confirmExecution()
    {
        echo "\n警告：此操作将批量加密数据库中的敏感字段！\n";
        echo "请确保已经备份数据库！\n";
        echo "要处理的表：" . implode(', ', array_keys($this->tables)) . "\n";
        echo "是否继续执行？(y/N): ";

        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        fclose($handle);

        return trim(strtolower($line)) === 'y';
    }
    
    /**
     * 检查加密功能是否开启
     */
    private function checkEncryptionEnabled()
    {
        return config('app.is_hsm_encrypt') === true;
    }
    
    /**
     * 处理单个表
     */
    private function processTable($tableName, $fields)
    {
        try {
            // 检查表是否存在
            if (!$this->tableExists($tableName)) {
                $this->log("警告：表 {$tableName} 不存在，跳过处理", 'WARN');
                return;
            }

            // 检查字段是否存在
            $existingFields = $this->getExistingFields($tableName, $fields);
            if (empty($existingFields)) {
                $this->log("警告：表 {$tableName} 中没有找到需要加密的字段，跳过处理", 'WARN');
                return;
            }

            // 获取总记录数
            $totalCount = Db::table($tableName)->count();
            $this->log("表 {$tableName} 总记录数: {$totalCount}");

            if ($totalCount == 0) {
                $this->log("表 {$tableName} 无数据，跳过处理", 'WARN');
                return;
            }

            // 分批处理
            $processed = 0;
            $offset = 0;

            while ($offset < $totalCount) {
                $records = Db::table($tableName)
                    ->limit($this->batchSize)
                    ->page(($offset / $this->batchSize) + 1, $this->batchSize)
                    ->select()
                    ->toArray();

                if (empty($records)) {
                    break;
                }

                $this->processBatch($tableName, $records, $existingFields);

                $processed += count($records);
                $offset += $this->batchSize;

                // 显示进度
                $progress = round(($processed / $totalCount) * 100, 2);
                $this->log("表 {$tableName} 进度: {$processed}/{$totalCount} ({$progress}%)");

                // 释放内存
                unset($records);

                // 短暂休息，避免过度占用资源
                usleep(10000); // 10毫秒
            }

        } catch (Exception $e) {
            $error = "处理表 {$tableName} 时发生错误: " . $e->getMessage();
            $this->log($error, 'ERROR');
            $this->stats['errors'][] = $error;
        }
    }
    
    /**
     * 检查表是否存在
     */
    private function tableExists($tableName)
    {
        try {
            $result = Db::query("SELECT TABLE_NAME FROM USER_TABLES WHERE TABLE_NAME = ?", [strtoupper($tableName)]);
            return !empty($result);
        } catch (Exception $e) {
            echo "检查表存在性时发生错误: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * 获取表中存在的字段
     */
    private function getExistingFields($tableName, $fields)
    {
        try {
            $existingFields = [];
            $columns = Db::query("SELECT COLUMN_NAME FROM USER_TAB_COLUMNS WHERE TABLE_NAME = ?", [strtoupper($tableName)]);
            
            $columnNames = array_column($columns, 'COLUMN_NAME');
            $columnNames = array_map('strtolower', $columnNames);
            
            foreach ($fields as $field) {
                if (in_array(strtolower($field), $columnNames)) {
                    $existingFields[] = $field;
                } else {
                    echo "警告：表 {$tableName} 中不存在字段 {$field}\n";
                }
            }
            
            return $existingFields;
        } catch (Exception $e) {
            echo "获取表字段时发生错误: " . $e->getMessage() . "\n";
            return [];
        }
    }
    
    /**
     * 处理一批记录
     */
    private function processBatch($tableName, $records, $fields)
    {
        foreach ($records as $record) {
            try {
                $this->processRecord($tableName, $record, $fields);
            } catch (Exception $e) {
                $error = "处理记录 ID {$record['id']} 时发生错误: " . $e->getMessage();
                echo $error . "\n";
                $this->stats['errors'][] = $error;
            }
        }
    }
    
    /**
     * 处理单条记录
     */
    private function processRecord($tableName, $record, $fields)
    {
        $updateData = [];
        $hasChanges = false;
        
        foreach ($fields as $field) {
            if (!isset($record[$field])) {
                continue;
            }
            
            $originalValue = $record[$field];
            
            // 跳过空值
            if (empty($originalValue)) {
                continue;
            }
            
            // 调用加密函数
            $encryptedValue = hsmCacheEncrypt($originalValue);
            
            // 检查是否加密成功且值有变化
            if ($encryptedValue !== false && $encryptedValue !== $originalValue) {
                $updateData[$field] = $encryptedValue;
                $hasChanges = true;
                $this->stats['total_encrypted']++;
            } else {
                $this->stats['total_skipped']++;
            }
        }
        
        // 如果有变化，更新记录
        if ($hasChanges) {
            Db::table($tableName)->where('id', $record['id'])->update($updateData);
        }
        
        $this->stats['total_processed']++;
    }
    
    /**
     * 打印执行摘要
     */
    private function printSummary()
    {
        echo "\n=== 执行摘要 ===\n";
        echo "结束时间: " . date('Y-m-d H:i:s') . "\n";
        echo "总处理记录数: " . $this->stats['total_processed'] . "\n";
        echo "成功加密字段数: " . $this->stats['total_encrypted'] . "\n";
        echo "跳过字段数: " . $this->stats['total_skipped'] . "\n";
        echo "错误数量: " . count($this->stats['errors']) . "\n";
        
        if (!empty($this->stats['errors'])) {
            echo "\n错误详情:\n";
            foreach ($this->stats['errors'] as $error) {
                echo "- {$error}\n";
            }
        }
        
        echo "================\n";
    }
}

// 执行脚本
if (php_sapi_name() === 'cli') {
    echo "开始执行批量加密脚本...\n\n";
    
    $encryptor = new BatchEncryptFields();
    $success = $encryptor->execute();
    
    if ($success) {
        echo "\n脚本执行完成！\n";
        exit(0);
    } else {
        echo "\n脚本执行失败！\n";
        exit(1);
    }
} else {
    echo "此脚本只能在命令行模式下运行！\n";
    exit(1);
}
