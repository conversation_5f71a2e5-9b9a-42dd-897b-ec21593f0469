<?php

namespace app\demo\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use hg\apidoc\annotation as Apidoc;
use app\BaseController;
use app\demo\model\DemoModel;
use think\Request;
use think\facade\Db;

/**
 * 标题也可以这样直接写
 * @Apidoc\Title("基础示例")
 * @Apidoc\Group("Index")
 * @Apidoc\Sort(9)
 */
class Index extends BaseController {

    /**
     * @Apidoc\Title("基础的注释方法")
     * @Apidoc\Desc("最基础的接口注释写法")
     * @Apidoc\Method("GET")
     * //@Apidoc\Author("HG-CODE")
     * //@Apidoc\Tag("测试")
     * @Apidoc\Param("username", type="abc",require=true, desc="用户名")
     * @Apidoc\Param("password", type="string",require=true, desc="密码")
     * @Apidoc\Param("phone", type="string",require=true, desc="手机号")
     * @Apidoc\Param("sex", type="int",default="1",desc="性别" )
     * @Apidoc\Returned("id", type="int", desc="用户id")
     */
    public function index() {
        ini_set('max_execution_time', 600);
        //$b = '[{"children":[],"diji":"","quHuaDaiMa":"330000","quhao":"NULL","shengji":"浙江省（浙）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"340000","quhao":"NULL","shengji":"安徽省（皖）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"350000","quhao":"NULL","shengji":"福建省（闽）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"360000","quhao":"","shengji":"江西省（赣）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"370000","quhao":"NULL","shengji":"山东省（鲁）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"410000","quhao":"","shengji":"河南省（豫）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"420000","quhao":"NULL","shengji":"湖北省（鄂）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"430000","quhao":"NULL","shengji":"湖南省（湘）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"440000","quhao":"","shengji":"广东省（粤）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"450000","quhao":"","shengji":"广西壮族自治区（桂）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"460000","quhao":"","shengji":"海南省（琼）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"500000","quhao":"NULL","shengji":"重庆市（渝）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"510000","quhao":"NULL","shengji":"四川省（川、蜀）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"520000","quhao":"NULL","shengji":"贵州省（黔、贵）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"530000","quhao":"NULL","shengji":"云南省（滇、云）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"540000","quhao":"","shengji":"西藏自治区（藏）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"610000","quhao":"NULL","shengji":"陕西省（陕、秦）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"620000","quhao":"NULL","shengji":"甘肃省（甘、陇）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"630000","quhao":"","shengji":"青海省（青）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"640000","quhao":"NULL","shengji":"宁夏回族自治区（宁）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"650000","quhao":"","shengji":"新疆维吾尔自治区（新）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"810000","quhao":"00852","shengji":"香港特别行政区（港）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"820000","quhao":"00853","shengji":"澳门特别行政区（澳）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"710000","quhao":"","shengji":"台湾省（台）","xianji":""}]';
        $b = '[{"children":[],"diji":"","quHuaDaiMa":"420000","quhao":"NULL","shengji":"湖北省（鄂）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"430000","quhao":"NULL","shengji":"湖南省（湘）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"440000","quhao":"","shengji":"广东省（粤）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"450000","quhao":"","shengji":"广西壮族自治区（桂）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"460000","quhao":"","shengji":"海南省（琼）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"500000","quhao":"NULL","shengji":"重庆市（渝）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"510000","quhao":"NULL","shengji":"四川省（川、蜀）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"520000","quhao":"NULL","shengji":"贵州省（黔、贵）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"530000","quhao":"NULL","shengji":"云南省（滇、云）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"540000","quhao":"","shengji":"西藏自治区（藏）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"610000","quhao":"NULL","shengji":"陕西省（陕、秦）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"620000","quhao":"NULL","shengji":"甘肃省（甘、陇）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"630000","quhao":"","shengji":"青海省（青）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"640000","quhao":"NULL","shengji":"宁夏回族自治区（宁）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"650000","quhao":"","shengji":"新疆维吾尔自治区（新）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"810000","quhao":"00852","shengji":"香港特别行政区（港）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"820000","quhao":"00853","shengji":"澳门特别行政区（澳）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"710000","quhao":"","shengji":"台湾省（台）","xianji":""}]';
        //$b = '[{"children":[],"diji":"","quHuaDaiMa":"110000","quhao":"NULL","shengji":"北京市（京）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"120000","quhao":"NULL","shengji":"天津市（津）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"130000","quhao":"","shengji":"河北省（冀）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"140000","quhao":"NULL","shengji":"山西省（晋）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"150000","quhao":"NULL","shengji":"内蒙古自治区（内蒙古）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"210000","quhao":"NULL","shengji":"辽宁省（辽）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"220000","quhao":"NULL","shengji":"吉林省（吉）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"230000","quhao":"NULL","shengji":"黑龙江省（黑）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"310000","quhao":"NULL","shengji":"上海市（沪）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"320000","quhao":"","shengji":"江苏省（苏）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"330000","quhao":"NULL","shengji":"浙江省（浙）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"340000","quhao":"NULL","shengji":"安徽省（皖）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"350000","quhao":"NULL","shengji":"福建省（闽）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"360000","quhao":"","shengji":"江西省（赣）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"370000","quhao":"NULL","shengji":"山东省（鲁）","xianji":""},{"children":[],"diji":"","quHuaDaiMa":"410000","quhao":"","shengji":"河南省（豫）","xianji":""}]';
        $shen = json_decode($b, true);
        foreach ($shen as $v) {
            $v['id'] = Db::connect('test')->table('top_pcas')->insertGetId(['code'=>$v['quHuaDaiMa'],'name'=>$v['shengji'],'pid'=>0,'level'=>1]);
            $parm = ['shengji' => $v['shengji']];
            $city = '[]';
            $city = postUrl('http://xzqh.mca.gov.cn/selectJson', $parm);
            $city = json_decode($city, true);
            foreach ($city as $v1) {
                $v1['id'] = Db::connect('test')->table('top_pcas')->insertGetId(['code'=>$v1['quHuaDaiMa'],'name'=>$v1['diji'],'pid'=>$v['id'],'level'=>2]);
                $parm = ['shengji' => $v['shengji'],'diji'=>$v1['diji']];
                $area = '[]';
                $area = postUrl('http://xzqh.mca.gov.cn/selectJson', $parm);
                $area = json_decode($area, true);
                foreach ($area as $v2) {
                    $v1['id'] = Db::connect('test')->table('top_pcas')->insertGetId(['code'=>$v1['quHuaDaiMa'],'name'=>$v2['xianji'],'pid'=>$v1['id'],'level'=>3]);
                }
            }
        }
        //postUrl('http://xzqh.mca.gov.cn/selectJson',);
//        $aa = Db::connect('test')->table('top_pca')->select();
//        print_r($aa);die;
        //Db::connect('test')->table('top_pca')->limit(100)->insertAll($pca_data);
        exit('succ');
        return json_decode($b, true);
    }

    /**
     * @Apidoc\Title("基础的注释方法2")
     * @Apidoc\Desc("最基础的接口注释写法2")
     * @Apidoc\Method("GET")
     * //@Apidoc\Author("HG-CODE")
     * //@Apidoc\Tag("测试")
     * @Apidoc\Param("username", type="abc",require=true, desc="用户名")
     * @Apidoc\Param("password", type="string",require=true, desc="密码")
     * @Apidoc\Param("phone", type="string",require=true, desc="手机号")
     * @Apidoc\Param("sex", type="int",default="1",desc="性别" )
     * @Apidoc\Returned("id", type="int", desc="用户id")
     */
    public function getDataList(Request $requset) {
        $m = new DemoModel();
        $data = $m->getDataList($requset);
        return $data;
    }

    /**
     * @Apidoc\Title("基础的注释方法2")
     * @Apidoc\Desc("最基础的接口注释写法2")
     * @Apidoc\Method("GET")
     * //@Apidoc\Author("HG-CODE")
     * //@Apidoc\Tag("测试")
     * @Apidoc\Param("username", type="abc",require=true, desc="用户名")
     * @Apidoc\Param("password", type="string",require=true, desc="密码")
     * @Apidoc\Param("phone", type="string",require=true, desc="手机号")
     * @Apidoc\Param("sex", type="int",default="1",desc="性别" )
     * @Apidoc\Returned("id", type="int", desc="用户id")
     */
}
