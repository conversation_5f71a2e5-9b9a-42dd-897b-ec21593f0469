<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>初次申请</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/company.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .el-form-item { margin-bottom:0;}
        .el-form--label-top .el-form-item__label { padding:0;line-height:30px;}
        .avatar-uploader .el-upload {
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        .avatar-uploader .el-upload:hover {
            border-color: #409EFF;
        }
        .avatar-uploader-icon {
            font-size: 28px;
            color: #8c939d;
            width: 150px;
            height: 150px;
            line-height: 150px;
            text-align: center;
        }
        .avatar {
            width: 150px;
            height: 150px;
            display: block;
        }
        .el-upload__tip {line-height:20px;color:#999;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer" style="padding:20px;">
        <el-form ref="form" :model="data" class="tableSearch" label-position="top" label-width="150px" :rules="rules">
            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item label="企业名称" prop="company_name">
                        <el-input v-model="data.company_name" size="mini" disabled></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="16">
                    <el-form-item label="注册地址" prop="address">
                        <el-input v-model="data.address" size="mini" disabled></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="统一社会信用代码" prop="company_code">
                        <el-input v-model="data.company_code" size="mini" disabled></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="法定代表人" prop="legal">
                        <el-input v-model="data.legal" size="mini" disabled></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="电话" prop="legal_mobile">
                        <el-input v-model="data.legal_mobile" size="mini" disabled></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="联系人" prop="manager">
                        <el-input v-model="data.manager" size="mini" disabled></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="电话" prop="manager_mobile">
                        <el-input v-model="data.manager_mobile" size="mini" disabled></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="电子邮箱" prop="manager_email">
                        <el-input v-model="data.manager_email" size="mini" disabled></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="申请等级" prop="level">
                        <el-radio v-model="data.level" label="三级">三级</el-radio>
                        <el-radio v-model="data.level" label="小型">小型</el-radio>
                        <el-radio v-model="data.level" label="微型">微型</el-radio>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="申请类型" prop="type">
                        {{data.type}}
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="创建性质" prop="nature">
                        <el-radio v-model="data.nature" label="0">自主创建</el-radio>
                        <el-radio v-model="data.nature" label="1">第三方机构指导</el-radio>
                        <el-input v-show="data.nature==1" v-model="data.advisory" size="mini" placeholder="机构名称"></el-input>
                    </el-form-item>
                </el-col>
                {volist name="files" id="item" key="key"}
                {if $key<16}
                <el-col >
                    <el-form-item label="{$key}、{$item.title}" prop="{$item.field}">
                        <el-upload
                                class="upload-demo"
                                action="upload"
                                :on-success="(response,file,fileList)=>uploadSuccess(response,file,fileList,'{$item.field}')"
                                :before-upload="uploadBefore"
                                :on-preview="preview"
                                :on-remove="(file,fileList)=>handleRemove(file,fileList,'{$item.field}')"
                                multiple
                                :limit="3"
                                :file-list="data.{$item.field}">
                            <el-button size="small" type="primary">点击上传</el-button>
                            <div slot="tip" class="el-upload__tip">只能上传jpg/png/pdf文件，且不超过2M</div>
                        </el-upload>
                        <!--<el-upload
                                class="avatar-uploader"
                                action="upload"
                                :show-file-list="true"
                                :on-success="(response,file,fileList)=>uploadSuccess(response,file,fileList,'{$item.field}')"
                                :before-upload="uploadBefore">
                            <img v-if="data.{$item.field}Url" :src="data.{$item.field}Url" class="avatar">
                            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                        </el-upload>-->
                    </el-form-item>
                </el-col>
                {/if}
                {/volist}
                <el-col :span="24" style="text-align: center;">
                    <el-button type="info" @click="submit(0)">保存</el-button>
                    <el-button type="primary" @click="submit(1)" v-loading.fullscreen.lock="loading">保存并提交</el-button>
                    <el-button @click="location.href = 'index'">返回</el-button>
                </el-col>
            </el-row>
        </el-form>
        <preview ref="preview"></preview>
    </div>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.all.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        components:{
            'preview': 'url:/general/toppingsoft/public/vue/preview.vue',
        },
        data() {
            return {
                title: '新增',
                data: {},
                rules: {
                    level: [{ required: true, message: '请选择申请等级', trigger: 'change' }],
                    type: [{ required: true, message: '请选择申请类型', trigger: 'change' }],
                    nature: [{ required: true, message: '请选择创建性质', trigger: 'change' }],
                },
                visible: false,
                loading: false,
                height: document.documentElement.clientHeight - 155,
            };
        },
        methods: {
            uploadBefore(file) {
                console.log(file)
                const isJPG = file.type === 'image/jpeg';
                const isPNG = file.type === 'image/png';
                const isPDF = file.type === 'application/pdf';
                if(!isJPG&&!isPNG&&!isPDF){
                    this.$message.error('请上传jpg/png/pdf文件');
                }
                return isJPG||isPNG||isPDF;
            },
            uploadSuccess(res, file,fileList,field) {
                var files = [];
                for(var i in fileList){
                    files.push(fileList[i].response.data??fileList[i]);
                }
                this.data[field] = files;
            },
            handleRemove(file, fileList,field) {
                var files = [];
                for(var i in fileList){
                    files.push(fileList[i].response.data??fileList[i]);
                }
                this.data[field] = files;
            },
            preview: function (file) {
                file = file.response?file.response.data:file;
                this.$refs.preview.open(file.url,file.name);
            },
            submit: function (status) {
                var _this = this;
                var param = _this.data;
                param.status = status;
                this.$refs.form.validate(function (valid) {
                    if(valid){
                        _this.loading = true;
                        axios.post("gradingSave", param).then(function (res) {
                            _this.loading = false;
                            _this.$message({
                                message: res.data.msg,
                                type: res.data.type
                            });
                            if (res.data.code == 0) {
                                location.href = 'index';
                            }
                        }).catch(function (error) {
                            console.log("出现错误:",error);
                        });
                    }
                });
            },
            getInfo:function(){
                var _this = this;
                _this.loading = true;
                var param = {
                    id:'{$id}',
                    _ajax:1,
                };
                axios.post('', param).then(function (res) {
                    _this.loading = false;
                    if (res.data.code == 0) {
                        _this.data = res.data.data;
                    }
                }).catch(function (error) {
                    _this.loading = false;
                    console.log("出现错误:",error);
                });
            },
        },
        mounted() {
            this.getInfo();
        }
    })
</script>


</body>
</html>