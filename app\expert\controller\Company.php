<?php

namespace app\expert\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use app\model\SettingModel;
use hg\apidoc\annotation as Apidoc;
use think\facade\View;
use app\model\ExcelModel;
use think\Request;
use think\facade\Db;
use app\model\FileModel;
use app\model\CompanyModel;

/**
 * @Apidoc\Title("企业管理")
 * @Apidoc\Group("Index")
 * @Apidoc\Sort(1)
 */
class Company extends Base
{
    public function index($id = 0) {
        $company = Db::table('top_company_info')->field("name,review_id")->where(['id' => $id])->find();
        $element_id = Db::table('top_company_review_element')->where(['main_id'=>$company['review_id']])->field('id')->order('sort')->find()['id'];

        View::assign('title','首页');
        View::assign('id',$id);
        View::assign('title',$company['name']);
        View::assign('element_id',$element_id);
        return view();
    }

    public function companyInfo($id = 0)
    {
        \app\model\CompanyModel::getCompanyInfo($id);
        exit();
    }

    public function getReview($element_id = 0)
    {
        if (request()->isAjax()) {
            $request = $this->request->post();
            \app\model\CompanyModel::getAjaxReview($element_id, $request);
            exit();

        } else {
            View::assign('element_id', $element_id);
            View::assign('title', '首页');
            return view();
        }
    }


}
