<?php

namespace app\api\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use app\model\FileModel;
use app\model\SettingModel;
use think\facade\Filesystem;
use hg\apidoc\annotation as Apidoc;
use think\App;
use think\facade\Cache;
use think\facade\Db;

/**
 * @Apidoc\Title("附件相关接口")
 * @Apidoc\Group("Index")
 * @Apidoc\Sort(10)
 */
class File extends Base {

	public function __construct(App $app)
    {
        parent::__construct($app);
        $token = $this->request->param('token');
        $user = Cache::get('token_'.$token);
        if(!empty($user)){
            $this->user = $user['userinfo'];
        }else{
            result('',8001,'暂无权限');
        }
    }

	/**
    * NotHeaders
     * @Apidoc\Title("附件上传")
     * @Apidoc\Desc("附件上传")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("token", type="string",require=true, default="",desc="token")
     * @Apidoc\Param("file", type="Object",require=true, default="",desc="附件")
     * @Apidoc\Returned("code", type="int", desc="状态码")
     * @Apidoc\Returned("msg", type="string", desc="状态信息")
     * @Apidoc\Returned("id", type="string", desc="附件id")
     * @Apidoc\Returned("name", type="string", desc="附件名称")
     * @Apidoc\Returned("url", type="string", desc="附件全路径URL")
     **/
    public function upload() {
		$file = \request()->file('file');
		try {
            Db::startTrans();
            $type = $file->getOriginalMime();
            $name = $file->getOriginalName();
            $size = $file->getSize();
            $ext = $file->extension();
            $model = empty($model)?request()->controller().'_'.request()->action():$model;
            $path = 'tmp/'.date('Ymd');
            $fileName = md5(time().rand(100,999));
            // 上传到本地服务器
            $savepath = Filesystem::disk('oa')->putFileAs($path, $file,$fileName.'.'.$ext);
            $data = [
                'model' => $model,
                'code' => $fileName,
                'filename' => $name,
                'filetype' => $type,
                'fileext' => $ext,
                'filesize' => round($size/1024,2).'k',
                'filepath' => $savepath,
                'create_user' => $user_id,
                'create_user_name' => $user_name,
                'create_time' => date('Y-m-d H:i:s'),
            ];
            $id = Db::table('top_files')->insertGetId($data);
            $id = Db::table('top_files')->where($data)->field('id')->find()['id'];//兼容达梦
            Db::commit();
            result(['id'=>$id,'name'=>$name,'code'=>$fileName,'url'=>config("app.http_host").'/general/toppingsoft/index.php/file/info?code='.$fileName]);
        } catch (Exception $exception) {
            Db::rollback();
			result('',8001,'出错了:'.$exception->getMessage());
        }
    }

}