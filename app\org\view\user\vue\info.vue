<style>
.my-label { width: 200px;}
.my-content { width: 450px;}
.margin-bottom { margin-bottom: 15px;}
.form-header { background-color: #E9F2F3; line-height: 25px; margin-bottom: 15px; padding: 5px 10px;}
.el-dialog__body { padding: 15px 20px;}
.el-tabs__content { overflow: auto;}
</style>
<template>
  <el-dialog :title="title" :close-on-click-modal="false"  modal="false" :visible.sync="visible" width="95%" top="10px" @close="refresh()" append-to-body="true" label-position="top">
    <el-tabs type="border-card">
      <el-tab-pane label="基本信息" :style="{height:height+'px'}">
        <el-descriptions class="margin-top" title="基本信息" :column="3" border label-class-name="my-label" content-class-name="my-content">
          <el-descriptions-item label="照片">
            <el-image
                style="width: 100px; height: 100px"
                :src="data.headUrl"
                :preview-src-list="[data.headUrl]">
            </el-image>
          </el-descriptions-item>
          <el-descriptions-item label="姓名">
            {{data.name}}
          </el-descriptions-item>
          <el-descriptions-item label="手机号">
            {{data.mobile}}
          </el-descriptions-item>
          <el-descriptions-item label="性别">
            {{data.sex}}
          </el-descriptions-item>
          <el-descriptions-item label="所属评审单位">
            {{data.org_name}}
          </el-descriptions-item>
          <el-descriptions-item label="邮箱">
            {{data.email}}
          </el-descriptions-item>
          <el-descriptions-item label="出生日期">
            {{data.birthday}}
          </el-descriptions-item>
          <el-descriptions-item label="民族">
            {{data.nation}}
          </el-descriptions-item>
          <el-descriptions-item label="QQ">
            {{data.qq}}
          </el-descriptions-item>
          <el-descriptions-item label="现住址">
            {{data.address}}
          </el-descriptions-item>
          <el-descriptions-item label="学校">
            {{data.school}}
          </el-descriptions-item>
          <el-descriptions-item label="专业">
            {{data.speciality}}
          </el-descriptions-item>
          <el-descriptions-item label="学历">
            {{data.education}}
          </el-descriptions-item>
          <el-descriptions-item label="现工作单位">
            {{data.employer}}
          </el-descriptions-item>
          <el-descriptions-item label="职务">
            {{data.position}}
          </el-descriptions-item>
          <el-descriptions-item label="参加工作时间">
            {{data.work_date}}
          </el-descriptions-item>
          <el-descriptions-item label="从事安全生产工作时间">
            {{data.position_date}}
          </el-descriptions-item>
          <el-descriptions-item label="专业技术职称">
            {{data.professional}}
          </el-descriptions-item>
          <el-descriptions-item label="职称证书编号">
            {{data.professional_number}}
          </el-descriptions-item>
          <el-descriptions-item label="安全评价资格师等级">
            {{data.secure}}
          </el-descriptions-item>
          <el-descriptions-item label="安全评价师证书编号">
            {{data.secure_number}}
          </el-descriptions-item>
          <el-descriptions-item label="注册安全工程师证书编号">
            {{data.reg_secure_number}}
          </el-descriptions-item>
          <el-descriptions-item label="其他证书编号">
            {{data.other_number}}
          </el-descriptions-item>
          <el-descriptions-item label="擅长专业">
            {{data.major}}
          </el-descriptions-item>
          <el-descriptions-item label="聘用日期">
            {{data.employ_date}}
          </el-descriptions-item>
          <el-descriptions-item label="状态" :span="3">
            <el-tag v-if="data.status==0" type="danger">禁用</el-tag>
            <el-tag v-if="data.status==1" type="success">正常</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="受聘情况" :span="3">
            {{data.offer_info}}
          </el-descriptions-item>
          <el-descriptions-item label="个人学习及工作简历" :span="3">
            {{data.resume}}
          </el-descriptions-item>
        </el-descriptions>
      </el-tab-pane>
      <el-tab-pane label="参与评审记录" :style="{height:height+'px'}">

      </el-tab-pane>
    </el-tabs>
    <!--编辑联系人-->
  </el-dialog>
</template>
<script>
module.exports = {
  name: "add",
  // 模板导入区
  components: {
  },
  data:function() {
    return {
      id:0,
      isAdmin:false,
      visible: false,
      dialogFormVisible: false,
      title: '详情',
      loading: false,
      noMore: false,
      user_id: 0,
      data: {
      },
      is_see:0,
      ca:[],
      details:[],
      cards:{},
      type:{},
      pcas: [],
      restaurants:[],
      restaurants2:[],
      height: document.documentElement.clientHeight - 250,
    }
  },
  computed: {
    disabled () {
      return this.loading || this.noMore
    }
  },
  mounted: function(){
    //this.getConfig();
  },
  created:function(){
  },
  methods: {
    /**
     * 打开弹窗调用方法
     * */
    open: function (row) {
      var _this =this;
      console.log(row)
      _this.id = row.id;
      _this.visible = true;
      _this.noMore = false;
      _this.getInfo(row.id);
      _this.getCa(row.id);
    },
    closeDialog: function () {
      this.visible = false;
    },
    getCa:function(id){
      var _this = this;
      if(!_this.noMore){
        _this.loading = true;
        axios.post('caList', {id:id}).then(function (res) {
          if (res.data.code == 0) {
            _this.ca = res.data.data;
          }
          _this.loading = false;
        }).catch(function (error) {
          _this.loading = false;
          console.log("出现错误:",error);
        });
      }
    },
    getInfo:function(id){
      var _this = this;
      if(id){
        axios.post("expertInfo", {
          id:id
        }).then(function (res) {
          if (res.data.code == 0) {
            _this.data=res.data.data;
          }else {
            _this.$message({
              message: res.data.msg,
              type: res.data.type
            });
          }
        }).catch(function (error) {
          console.log("出现错误:",error);
        });
      }
    },
    addContacts: function(){
      this.form = {};
      this.dialogFormVisible = true;
    },
    editContacts: function(row){
      this.form = row;
      this.dialogFormVisible = true;
    },
  }
}
</script>


