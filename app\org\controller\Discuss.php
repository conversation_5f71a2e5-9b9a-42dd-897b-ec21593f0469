<?php

namespace app\org\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use GatewayClient\Gateway;
use hg\apidoc\annotation as Apidoc;
use think\facade\Cache;
use think\facade\Queue;
use think\facade\View;
use think\Request;
use think\facade\Db;
use app\model\FileModel;

/**
 * @Apidoc\Title("日程管理")
 * @Apidoc\Group("Index")
 * @Apidoc\Sort(1)
 */
class Discuss extends Base {

    /**
     * @Apidoc\Title("我的评审任务")
     * @Apidoc\Desc("我的评审任务")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("日程")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function index($limit=20,$id=0) {
        if (request()->isAjax()) {
            $where = [];
            $where[] = ['b.user_id','=',$_SESSION['org']['id']];
            $where[] = ['b.user_type','=','org'];
            $field = "a.*,b.readsum";
            $res = Db::table('top_discuss_group')->alias('a')
                ->leftJoin('top_discuss_group_user b','a.id = b.group_id')
                ->where($where)->order('a.last_time desc')
                ->field($field)
                ->paginate($limit)->each(function ($item, $key) {
                    $item['readsum'] = empty($item['readsum'])?'':$item['readsum'];
                    $item['last_time'] = get_time(strtotime($item['last_time']));
                    return $item;
                });
            result($res);
        } else {
            View::assign('id', $id);
            View::assign('user', $_SESSION['org']);
            return view();
        }
    }

    public function groupinfo($id,$limit=20){
        $re = Db::table('top_discuss_group')->where(['id'=>$id])->find();
        $tasks = Db::table('top_org_tasks')->where(['id'=>$re['tasks_id']])->find();
        $org = Db::table('top_org')->where(['id'=>$tasks['org_id']])->find();
        $re['org_name'] = $org['name'];
        $re['company_name'] = $tasks['company_name'];
        $re['address'] = $tasks['address'];
        $re['review_name'] = $tasks['review_name'];
        $experts = Db::table('top_discuss_group_user')->where(['group_id'=>$id,'user_type'=>'expert'])->column('user_name');
        $re['experts'] = implode('、',$experts);
        $re['content'] = Db::table('top_discuss_group_content')->where(['group_id'=>$id])->order('time desc')
            ->paginate($limit)->each(function ($item, $key) {
                $item['time'] = get_time(strtotime($item['time']));
                return $item;
            });
        Db::table('top_discuss_group_user')->where(['group_id'=>$id,'user_type'=>'org','user_id'=>$_SESSION['org']['id']])->update(['readsum'=>0]);
        result($re);
    }

    public function send($id,$content='',$image=''){
        $re = Db::table('top_discuss_group')->alias('a')
            ->leftJoin('top_discuss_group_user b','a.id=b.group_id')
            ->where(['a.id'=>$id,'b.user_id'=>$_SESSION['org']['id'],'b.user_type'=>'org'])
            ->field('b.id,a.status,a.title,a.date')->find();
        if(empty($re)){
            result('',1002,'群聊不存在');
        }
        if($re['status']!=1){
            result('',1002,'群聊已结束');
        }
        Db::startTrans();
        try {
            if(!empty($content)){
                $data_content = [
                    'group_id' => $id,
                    'user_id' => $_SESSION['org']['id'],
                    'user_name' => $_SESSION['org']['org_name'],
                    'user_type' => 'org',
                    'content' => $content,
                    'time' => date('Y-m-d H:i:s'),
                ];
                Db::table('top_discuss_group_content')->insert($data_content);
                $last_content = $content;
            }else if(!empty($image)){
                $data_content = [
                    'group_id' => $id,
                    'user_id' => $_SESSION['org']['id'],
                    'user_name' => $_SESSION['org']['org_name'],
                    'user_type' => 'org',
                    'image' => $image,
                    'time' => date('Y-m-d H:i:s'),
                ];
                Db::table('top_discuss_group_content')->insert($data_content);
                $last_content = "[图片]";
            }else{
                result('',1003,'内容为空');
            }
            Db::table('top_discuss_group')->where(['id'=>$id])->update(['last_content'=>$last_content,'last_time'=>date('Y-m-d H:i:s')]);
            Db::table('top_discuss_group_user')->where(['group_id'=>$id])->where('id','<>',$re['id'])->inc('readsum')->save();
            Db::commit();
            // 推送任务到消息队列
            $data_content['title'] = $re['title'];
            $data_content['date'] = $re['date'];
            $data_content['time'] = get_time(strtotime($data_content['time']));
            $jobData = ['group_id' => $id, 'content' => $data_content];
            Queue::push('app\job\Stock', $jobData, 'scoketJobQueue');
            result($data_content);
        } catch (\Exception $e) {
            Db::rollback();
            result('',7001,$e->getMessage());
        }
    }

    public function auth(){
        $key = md5('org'.$_SESSION['org']['id'].time());
        $group = Db::table('top_discuss_group')->alias('a')
            ->leftJoin('top_discuss_group_user b','a.id = b.group_id')
            ->where(['b.user_id'=>$_SESSION['org']['id'],'user_type'=>'org'])->field('a.id')->select()->toArray();
        $user = [
            'type' => 'org',
            'user_id' => $_SESSION['org']['id'],
            'user_name' => $_SESSION['org']['org_name'],
            'group' => $group,
        ];
        Cache::set($key,$user);
        result($key);
    }

    public function upload($model='org') {
        $file = request()->file('file');
        $result = FileModel::upload($file,$model);
        FileModel::saveFile($result['code'],'org/task/'.date('Ym'));
        result($result);
    }


}
