<?php

namespace app\api\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use app\company\model\GradingModel;
use app\model\FileModel;
use app\model\SettingModel;
use hg\apidoc\annotation as Apidoc;
use think\App;
use think\facade\Cache;
use think\facade\Db;
use think\facade\View;

/**
 * @Apidoc\Title("视频")
 * @Apidoc\Group("Video")
 * @Apidoc\Sort(7)
 */
class Video extends Base {

    public function info($number=160302,$width=500,$height=500){
        View::assign('width',$width);
        View::assign('height',$height);
        View::assign('number',$number);
        return view();
    }

}
