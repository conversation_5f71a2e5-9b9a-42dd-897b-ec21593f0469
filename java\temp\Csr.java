//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

import java.io.IOException;
import java.nio.ByteBuffer;
import kl.hsm.client.sdk.HsmSdk;
import org.apache.thrift.TException;

public class Csr {
    public Csr() {
    }

    public static void main(String[] args) throws TException, IOException {
        HsmSdk hsmSdk = new HsmSdk();
        Long session = hsmSdk.openSession();
        hsmSdk.getPrivateKeyAccessRight(session, 101, ByteBuffer.wrap("pass".getBytes()));
        String cn = args[0];
        String ouArea = args[1];
        hsmSdk.getCsr(101, session, cn, ouArea);
    }
}
