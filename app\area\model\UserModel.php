<?php


namespace app\area\model;


use app\model\FileModel;
use think\Model;
use think\App;
use think\facade\Db;
use app\model\SettingModel;

//权限配置模块
class UserModel extends Model
{

    public static function expertSave($param,$id=0)
    {
        $data = [
            'head' => $param['head'],
            'sex' => $param['sex'],
            'birthday' => $param['birthday'],
            'nation' => $param['nation'],
            'qq' => $param['qq'],
            'address' => $param['address'],
            'school' => $param['school'],
            'speciality' => $param['speciality'],
            'education' => $param['education'],
            'employer' => $param['employer'],
            'position' => $param['position'],
            'work_date' => $param['work_date'],
            'position_date' => $param['position_date'],
            'professional' => $param['professional'],
            'professional_number' => $param['professional_number'],
            'secure' => $param['secure'],
            'secure_number' => $param['secure_number'],
            'reg_secure_number' => $param['reg_secure_number'],
            'other_number' => $param['other_number'],
            'major' => $param['major'],
            'employ_date' => $param['employ_date'],
            'offer_info' => $param['offer_info'],
            'resume' => $param['resume'],
        ];
        $salt = create_nonce_str(4);
        $password = '';
        $user = [
            'username' => $param['mobile'],
            'name' => $param['name'],
            'password' => crypt($password, $salt),
            'email' => $param['email'],
            'mobile' => $param['mobile'],
            'org_id' => $param['org_id'],
            'status' => 1,
            'reg_time' => date('Y-m-d H:i:s'),
            'reg_ip' => get_ip(),
            'salt' => $salt,
        ];
        $r = Db::table('top_expert')->where([['id','<>',$id],['mobile','=',$user['mobile']]])->find();
        if($r){
            result('',1003,'手机号已注册');
        }
        FileModel::saveFile($data['head'],'expert/'.date('Ym'));
        if($id){
            $u = Db::table('top_expert')->where(['id'=>$id])->find();
            if(empty($u)){
                result('',1003,'数据不存在或已删除');
            }
//            dd($data);
            $re = Db::table('top_expert')->where(['id'=>$id])->update($user);
        }else{
            $id = Db::table('top_expert')->insertGetId($user);
            $u = Db::table('top_expert')->where($user)->order('id desc')->find();
        }
        $data['expert_id'] = $u['id'];
        $re = Db::table('top_expert_info')->where(['expert_id'=>$data['expert_id']])->find();
        if(empty($re)){
            Db::table('top_expert_info')->insertGetId($data);
        }else{
            Db::table('top_expert_info')->where(['id'=>$re['id']])->update($data);
        }
        return $id;
    }

    //导入
    public static function import($data,$dataHeader){
        $fields = [
            'name' => '姓名',
            'org_name' => '所属评审单位',
            'mobile' => '手机号',
            'email' => '邮箱',
            'sex' => '性别',
            'birthday' => '出生日期',
            'nation' => '民族',
            'qq' => 'QQ',
            'address' => '现住址',
            'school' => '学校',
            'speciality' => '专业',
            'education' => '学历',
            'employer' => '现工作单位',
            'position' => '职务',
            'work_date' => '参加工作时间',
            'position_date' => '从事安全生产工作时间',
            'professional' => '专业技术职称',
            'professional_number' => '职称证书编号',
            'secure' => '安全评价资格师等级',
            'secure_number' => '安全评价师证书编号',
            'reg_secure_number' => '注册安全工程师证书编号',
            'other_number' => '其他证书编号',
            'major' => '擅长专业',
            'employ_date' => '聘用日期',
            'offer_info' => '受聘情况',
            'resume' => '个人学习及工作简历',
            'status' => '状态',
        ];
        foreach ($dataHeader as $k=>$v){
            foreach ($fields as $k1=>$v1){
                if($v1==$v){
                    $tit[$k] = $k1;
                }
            }
        }
        foreach ($data as $k=>$v){
            $tmp = [];
            foreach($v as $k1=>$v1){
                if(in_array($tit[$k1],['date'])) {
                    $v1 = str_replace('.', '-', $v1);
                    if (strlen($v1) > 6) {
                        $tmp[$tit[$k1]] = date('Y-m-d', strtotime($v1));
                    } else {
                        $tmp[$tit[$k1]] = gmdate('Y-m-d', \PHPExcel_Shared_Date::ExcelToPHP($v1));
                    }
                }else{
                    $tmp[$tit[$k1]] = str_replace(' ','',$v1);
                }
            }
            $dataList[] = $tmp;
        }
        foreach ($dataList as $v) {
            if(!empty($v['name'])){
                $datauser[] = $v;
            }
        }
        foreach ($datauser as $k=>$v){
            $re = self::importSave($v);
            if($re['code']==0) {
                $result['success'][] = ['row' => $k + 2, 'name' => $v['name'], 'msg' => '保存成功'];
            }else if($re['code']==-1){
                $result['repeat'][] = ['row' => $k + 2, 'name' => $v['name'], 'msg' => '重复'];
            }else{
                $result['fail'][] = ['row'=>$k+2,'name'=>$v['name'],'msg'=>$re['msg']];
            }
        }
        return $result;
    }


    public static function importSave($param){
        $data = [
            'head' => $param['head'],
            'sex' => $param['sex'],
            'birthday' => $param['birthday'],
            'nation' => $param['nation'],
            'qq' => $param['qq'],
            'address' => $param['address'],
            'school' => $param['school'],
            'speciality' => $param['speciality'],
            'education' => $param['education'],
            'employer' => $param['employer'],
            'position' => $param['position'],
            'work_date' => $param['work_date'],
            'position_date' => $param['position_date'],
            'professional' => $param['professional'],
            'professional_number' => $param['professional_number'],
            'secure' => $param['secure'],
            'secure_number' => $param['secure_number'],
            'reg_secure_number' => $param['reg_secure_number'],
            'other_number' => $param['other_number'],
            'major' => $param['major'],
            'employ_date' => $param['employ_date'],
            'offer_info' => $param['offer_info'],
            'resume' => $param['resume'],
        ];
        $salt = create_nonce_str(4);
        $org = Db::table('top_org')->where(['name'=>$param['org_name']])->find();
        $password = '';
        $user = [
            'username' => $param['mobile'],
            'name' => $param['name'],
            'password' => crypt($password, $salt),
            'email' => $param['email'],
            'mobile' => $param['mobile'],
            'org_id' => empty($org)?0:$org['id'],
            'status' => $param['status']=='正常'||$param['status']=='1'?'1':'0',
            'reg_time' => date('Y-m-d H:i:s'),
            'reg_ip' => get_ip(),
            'salt' => $salt,
        ];
        $u = Db::table('top_expert')->where([['mobile','=',$user['mobile']]])->find();
        if(empty($u)){
            $id = Db::table('top_expert')->insertGetId($user);
            $u = Db::table('top_expert')->where($user)->order('id desc')->find();
        }
        $data['expert_id'] = $u['id'];
        $re = Db::table('top_expert_info')->where(['expert_id'=>$data['expert_id']])->find();
        if(empty($re)){
            Db::table('top_expert_info')->insertGetId($data);
            return ['data'=>['id'=>$id],'code'=>0,'msg'=>'保存成功'];
        }else{
            return ['','code'=>-1,'msg'=>'重复'];
        }
    }


    //参数格式化
    public static function codeToText($info, $depType = 'v1')
    {
        if (empty($info)) {
            return $info;
        }

        // 1. 获取部门映射
        $departmentMap = self::getDepartmentMap($depType);
        $info['dept_name'] = self::mapValueToLabel($departmentMap, $info['department'] ?? null);

        // 2. 获取角色映射
        $roleMap = config('global.role', []);
        $role = $info['role'] ?? $info['ROLE'] ?? null;
        $info['role_name'] = self::mapValueToLabel($roleMap, $role);

        // 3. 角色统一处理
        $info['role'] = is_numeric($role) ? $role : ($info['role'] ?? $info['ROLE'] ?? '');

        // 4. 管理员字段处理
        $info['manager'] = empty($info['manager']) ? [] : explode(',', trim($info['manager'], ','));

        // 5. 状态转换
        $info['status_name'] = ($info['status'] ?? 0) == 1 ? '正常' : '禁用';

        // 6. 头像处理
        $info['headUrl'] = empty($info['head']) ? '' : FileModel::getFile(0, $info['head']);

        // 7. 字段清理（null、空日期、保留数字类型）
        foreach ($info as $k => $v) {
            if ($v === null || $v === 'null') {
                $info[$k] = '';
            } elseif ($v === '0000-00-00') {
                $info[$k] = '';
            } elseif (is_int($v)) {
                // 保留整数类型
            } elseif (is_string($v)) {
                // 保留原始字符串
            } elseif (is_array($v)) {
                // 保留原始字符串
            } else {
                $info[$k] = (string)$v;
            }
        }

        return $info;
    }

    public static function getDepartmentMap($depType = 'v1')
    {
        $depTypeArr = [
            //version,不同时期的维护，保留具体版本叫法
            'v1' => [
                ['label' => '基础科', 'value' => 1],
                ['label' => '危化科', 'value' => 2],
                ['label' => '综合协调科', 'value' => 3],
            ],
        ];

        return $depTypeArr[$depType] ?? [];
    }

    private static function mapValueToLabel($map, $value)
    {
        if (empty($map) || $value === null) {
            return '';
        }

        foreach ($map as $item) {
            if ((string)$item['value'] === (string)$value) {
                return $item['label'];
            }
        }

        return '';
    }

}