<template>
    <el-dialog :title="title" :close-on-click-modal="false" modal="false" :visible.sync="visible" width="90%"
               @close="refresh()" append-to-body="true" label-position="top">
      <h2 style="text-align: center;font-weight: 700;">{{data.title}}</h2>
      <div style="line-height: 40px;"><span  style="text-align: left;"></span><span style="float: right;">日期：{{data.date}}</span></div>
      <p style="text-indent:40px;line-height: 30px;margin: 30px 0;text-align: left;">{{data.summary}}</p>
      <div v-html="data.content"></div>
    </el-dialog>
</template>
<script>
    module.exports = {
        name: "info",
        data() {
            return {
                id:0,
                pdfUrl:'',
                visible: false,
                fileVisible: false,
                title: '通知公告详情',
                loading: false,
                user_id: 0,
                data: {
                  title:'',
                  date:'',
                  content:'',
                  files:[],
                },
                height: document.documentElement.clientHeight - 310,
            }
        },
		    mounted(){
            var that = this;
            window.addEventListener('resize',function(){
                clearTimeout(that.resizeFlag);
                that.resizeFlag =setTimeout(function(){
                    that.height = document.documentElement.clientHeight -50
                },300)
            })
        },
        methods: {
            /**
             * 打开弹窗调用方法
             * */
            open: function (id) {
                this.id = id;
                this.visible = true;
                let vm =this;
                //this.clear1();
                axios.post('policyInfo', {
                    id:id,
                }).then(function (res) {
                    if (res.data.code == 0) {
                      if(res.data.data.files.length>0){
                        vm.pdfUrl = res.data.data.files[0].url;
                        vm.fileVisible = true;
                      }else{
                        vm.fileVisible = false;
                      }
                        vm.data=res.data.data;
                    }else {
                        vm.$message({
                            message: res.data.msg,
                            type: res.data.type
                        });
                    }
                }).catch(function (error) {
                    console.log("出现错误:",error);
                });
            },
            refresh: function () {
              this.visible = false;
            }
        }
    }
</script>


