<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>试题管理</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }

        .el-table .danger-row {
            background: #fbc4c4;
        }

        .el-table .warning-row {
            background: oldlace;
        }

        .el-table .success-row {
            background: #f0f9eb;
        }

        .el-badge__content.is-fixed {
            right: 20px;
            z-index: 9;
        }
    </style>
</head>
<body>
<div id="app" v-cloak>

    <el-tabs type="border-card" v-model="activeName">
        <el-tab-pane name="first" label="基础库">
            <div class="centainer">
                <el-form :inline="true" :model="searchFromBase" class="form-inline" @submit.native.prevent>
                    <el-form-item label="试题名称">
                        <el-input v-model="searchFromBase.test_title" size="mini" placeholder="试题名称"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="getBaseTestInfoList()" size="mini">查询</el-button>
                        <el-button @click="resetBase()" size="mini">重置</el-button>
                    </el-form-item>
                    <el-form-item style="float: right">
                        <el-button :loading="loading" type="success" size="mini" @click="testInfoAddClick">添加
                        </el-button>
                        <el-button :loading="loading" type="primary" size="mini" @click="import1">导入</el-button>
                    </el-form-item>
                </el-form>
                <el-table border
                          v-loading="loading"
                          :data="baseData.data"
                          style="width: 100%;margin-bottom: 20px;"
                          ref="qtable"
                          :height="height"
                          size="small">
                    <el-table-column
                            type="index"
                            label="序号"
                            align="center"
                            width="50">
                    </el-table-column>
                    <el-table-column
                            prop="test_title"
                            label="试题标题"
                            align="center"
                            show-overflow-tooltip
                            min-width="100">
                        <!--                <template slot-scope="scope">-->
                        <!--                    <el-link @click="info(scope.row)" type="primary" v-html="scope.row.name"></el-link>-->
                        <!--                </template>-->
                    </el-table-column>
                    <el-table-column
                            prop="type_name"
                            label="类型"
                            align="center"
                            show-overflow-tooltip
                            width="80">
                        <!--                <template slot-scope="scope">-->
                        <!--                    <el-link @click="info(scope.row)" type="primary" v-html="scope.row.name"></el-link>-->
                        <!--                </template>-->
                    </el-table-column>
                    <el-table-column
                            prop="test_option"
                            label="试题选项"
                            align="center"
                            show-overflow-tooltip
                            min-width="100">
                    </el-table-column>
                    <el-table-column
                            prop="test_answer"
                            label="答案"
                            align="center"
                            show-overflow-tooltip
                            width="50">
                    </el-table-column>
                    <el-table-column
                            prop="test_analysis"
                            label="试题解析"
                            align="center"
                            show-overflow-tooltip
                            width="180">
                    </el-table-column>
                    <el-table-column
                            prop="create_user_name"
                            label="创建人"
                            align="center"
                            show-overflow-tooltip
                            width="100">
                    </el-table-column>
                    <el-table-column
                            prop="created_at"
                            label="创建时间"
                            align="center"
                            show-overflow-tooltip
                            width="140">
                    </el-table-column>
                    <el-table-column
                            prop="updated_at"
                            label="更新时间"
                            align="center"
                            show-overflow-tooltip
                            width="140">
                    </el-table-column>

                    <el-table-column fixed="right"
                                     label="操作"
                                     align="center"
                                     width="120">
                        <template slot-scope="scope">
                            <el-dropdown split-button size="small" type="success"
                                         @click="testInfoChangeClick(scope.row.id)">
                                编辑
                                <el-dropdown-menu slot="dropdown">
                                    <!--                                    <el-dropdown-item @click.native="testInfoChangeClick(scope.row.id)" type="warning">-->
                                    <!--                                        编辑-->
                                    <!--                                    </el-dropdown-item>-->
                                    <el-dropdown-item @click.native="deleteOne(scope.row.id)" type="danger">删除
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                        </template>
                    </el-table-column>
                </el-table>
                <!--分页条total, sizes, prev, pager, next, jumper-->
                <div class="block">
                    <el-pagination
                            @size-change="handleSizeBaseChange"
                            @current-change="handleCurrentBaseChange"
                            :current-page="baseData.page"
                            :page-sizes="[10, 20, 50, 100, 500, 1000]"
                            :page-size="baseData.limit"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="baseData.total">
                    </el-pagination>
                </div>
            </div>
        </el-tab-pane>
        <el-tab-pane name="second" label="专业库">
            <div class="centainer">
                <el-form :inline="true" :model="searchFromSpeciality" class="form-inline"
                         @submit.native.prevent>
                    <el-form-item label="评审标准">
                        <el-select v-model="searchFromSpeciality.current_standard_id"  size="mini"
                                   @change="handleStandardChange" 
                                   placeholder="请选择评审标准" 
                                   clearable 
                                   style="width: 200px;">
                            <el-option label="全部" value=""></el-option>
                            <el-option
                                v-for="item in standardList"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="试题名称">
                        <el-input v-model="searchFromSpeciality.test_title" size="mini" placeholder="试题名称"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="getSpecialityTestInfoList()" size="mini">查询
                        </el-button>
                        <el-button @click="resetSpeciality()" size="mini">重置</el-button>
                    </el-form-item>
                    <el-form-item style="float: right">
                        <el-button :loading="loading" type="success" size="mini" @click="testInfoAddClick">添加
                        </el-button>
                        <el-button :loading="loading" type="primary" size="mini" @click="import1">导入</el-button>
                    </el-form-item>
                </el-form>
                <el-table border
                          v-loading="loading"
                          :data="specialityData.data"
                          style="width: 100%;margin-bottom: 20px;"
                          ref="qtable"
                          show-summary
                          :height="height"
                          size="small">
                            <el-table-column
                                    type="index"
                                    label="序号"
                                    align="center"
                                    width="50">
                            </el-table-column>
                            <el-table-column
                                    prop="test_title"
                                    label="试题标题"
                                    align="center"
                                    show-overflow-tooltip
                                    min-width="100">

                            </el-table-column>
                            <el-table-column
                                    prop="type_name"
                                    label="类型"
                                    align="center"
                                    show-overflow-tooltip
                                    width="80">

                            </el-table-column>
                            <el-table-column
                                    prop="standard_name"
                                    label="评审标准"
                                    align="center"
                                    show-overflow-tooltip
                                    width="120">
                            </el-table-column>
                            <el-table-column
                                    prop="test_option"
                                    label="试题选项"
                                    align="center"
                                    show-overflow-tooltip
                                    min-width="100">
                            </el-table-column>
                            <el-table-column
                                    prop="test_answer"
                                    label="答案"
                                    align="center"
                                    show-overflow-tooltip
                                    width="50">
                            </el-table-column>
                            <el-table-column
                                    prop="test_analysis"
                                    label="试题解析"
                                    align="center"
                                    show-overflow-tooltip
                                    width="180">
                            </el-table-column>
                            <el-table-column
                                    prop="create_user_name"
                                    label="创建人"
                                    align="center"
                                    show-overflow-tooltip
                                    width="100">
                            </el-table-column>
                            <el-table-column
                                    prop="created_at"
                                    label="创建时间"
                                    align="center"
                                    show-overflow-tooltip
                                    width="140">
                            </el-table-column>
                            <el-table-column
                                    prop="updated_at"
                                    label="更新时间"
                                    align="center"
                                    show-overflow-tooltip
                                    width="140">
                            </el-table-column>

                            <el-table-column fixed="right"
                                             label="操作"
                                             align="center"
                                             width="120">
                                <template slot-scope="scope">
                                    <el-dropdown split-button size="small" type="success"
                                                 @click="testInfoChangeClick(scope.row.id)">
                                        编辑
                                        <el-dropdown-menu slot="dropdown">
                                            <el-dropdown-item @click.native="deleteOne(scope.row.id)" type="danger">删除
                                            </el-dropdown-item>
                                        </el-dropdown-menu>
                                    </el-dropdown>
                                </template>
                            </el-table-column>
                </el-table>
                <!--分页条total, sizes, prev, pager, next, jumper-->
                <div class="block">
                    <el-pagination
                            @size-change="handleSizeSpecialityChange"
                            @current-change="handleCurrentSpecialityChange"
                            :current-page="specialityData.page"
                            :page-sizes="[10, 20, 50, 100, 500, 1000]"
                            :page-size="specialityData.Limit"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="specialityData.total">
                    </el-pagination>
                </div>
            </div>
        </el-tab-pane>
    </el-tabs>


    <import1 ref="import1" @refresh="refreshList()"></import1>
    <test-info ref="testInfo" @ok="refreshList()"></test-info>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                title: '新增',
                activeName: 'first',
                loading: true,
                height: document.documentElement.clientHeight - 255,

                searchFromBase: {
                    test_title: '',
                },
                baseData: {
                    data: [],
                    total: 0,
                    limit: 10,
                    page: 1,
                },

                searchFromSpeciality: {
                    test_title: '',
                    current_standard_id: '',
                },
                specialityData: {
                    data: [],
                    total: 0,
                    limit: 10,
                    page: 1,
                },
                standardList: [],
            };
        },
        components: {
            'import1': 'url:/general/toppingsoft/app/exam/view/question/modules/import.vue',
            'test-info': 'url:/general/toppingsoft/app/exam/view/question/modules/testInfo.vue?v=2',
        },
        created() {
            let _this = this;
            //窗口改变时
            window.onresize = function () {

            };
        },

        methods: {
            handleStandardChange(value) {
                this.getSpecialityTestInfoList();
            },
            refreshList() {
                this.getBaseTestInfoList();
                this.getSpecialityTestInfoList();
            },

            testInfoAddClick() {
                if (this.activeName == 'second') {
                    if (this.searchFromSpeciality.current_standard_id) {
                        this.$refs.testInfo.addOne(this.searchFromSpeciality.current_standard_id, 'second');
                    } else {
                        this.$alert("请先选择对应的评审标准再新增！", '系统提示', {confirmButtonText: '知道了'});
                    }
                } else {
                    this.$refs.testInfo.addOne("", 'first');
                }
            },


            testInfoChangeClick(test_id) {
                this.$refs.testInfo.changeOne(test_id);
            },


            deleteOne(test_id) {
                var _this = this;
                _this.$confirm("确认删除？", "提示", {}).then(() => {
                    _this.addLoading = true;
                    axios.post("deleteOne", {
                        test_id: test_id
                    }).then(function (res) {
                        _this.addLoading = false;
                        if (res.data.code == 1000) {
                            _this.refreshList();
                        }
                        _this.$message({
                            message: res.data.msg,
                            type: res.data.type
                        });
                    }).catch(function (error) {
                        console.log(error);
                    });
                });
            },

            //数据初始化


            import1: function () {
                if (this.activeName == 'second') {
                    if (this.searchFromSpeciality.current_standard_id) {
                        this.$refs.import1.open(this.searchFromSpeciality.current_standard_id, 'second');
                    } else {
                        this.$alert("请先选择对应的评审标准再导入！", '系统提示', {confirmButtonText: '知道了'});
                    }
                } else {
                    this.$refs.import1.open("", 'first');
                }

            },


            getBaseTestInfoList() {
                var _this = this;
                axios.post("getBaseTestInfoList", {
                    searchData: this.searchFromBase,
                    page: _this.baseData.page,
                    limit: _this.baseData.limit,
                }).then(function (res) {
                    _this.loading = false;
                    if (res.data.code == 1000) {
                        _this.baseData.data = res.data.data.data;
                        _this.baseData.total = res.data.data.total;
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            getSpecialityTestInfoList() {
                var _this = this;
                axios.post("getSpecialityTestInfoList", {
                    searchData: this.searchFromSpeciality,
                    page: _this.specialityData.page,
                    limit: _this.specialityData.limit,
                }).then(function (res) {
                    _this.loading = false;
                    if (res.data.code == 1000) {
                        _this.specialityData.data = res.data.data.data;
                        _this.specialityData.total = res.data.data.total;
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            getStandardNameList() {
                var _this = this;
                axios.post("getStandardNameList", {
                }).then(function (res) {
                    _this.loading = false;
                    if (res.data.code == 1000) {
                        _this.standardList = res.data.data;
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            handleSizeBaseChange: function (val) {
                this.baseData.limit = val;
                this.getBaseTestInfoList();
            },
            handleCurrentBaseChange: function (val) {
                this.baseData.page = val;
                this.getBaseTestInfoList();
            },
            resetBase() {
                this.searchFromBase = {
                    test_title: '',
                };
                this.baseData = {
                    data: [],
                    total: 0,
                    limit: 10,
                    page: 1,
                };


                this.getBaseTestInfoList();
            },
            handleSizeSpecialityChange: function (val) {
                this.specialityData.limit = val;
                this.getSpecialityTestInfoList();
            },
            handleCurrentSpecialityChange: function (val) {
                this.specialityData.page = val;
                this.getSpecialityTestInfoList();
            },
            resetSpeciality() {
                this.searchFromSpeciality = {
                    test_title: '',
                    current_standard_id: '',
                };
                this.specialityData = {
                    data: [],
                    total: 0,
                    limit: 10,
                    page: 1,
                };
                this.getSpecialityTestInfoList();
            },
        },
        mounted() {
            //获取列表
            this.getBaseTestInfoList();
            this.getSpecialityTestInfoList();
            this.getStandardNameList();

        }
    })
</script>


</body>
</html>