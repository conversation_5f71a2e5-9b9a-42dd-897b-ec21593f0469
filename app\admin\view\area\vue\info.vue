<style>
.my-label { width: 200px;}
.my-content { width: 450px;}
.margin-bottom { margin-bottom: 15px;}
.form-header { background-color: #E9F2F3; line-height: 25px; margin-bottom: 15px; padding: 5px 10px;}
.el-dialog__body { padding: 15px 20px;}
.el-tabs__content { overflow: auto;}
</style>
<template>
  <el-dialog :title="title" :close-on-click-modal="false"  modal="false" :visible.sync="visible" width="95%" top="10px" @close="refresh()" append-to-body="true" label-position="top">
    <el-tabs type="border-card">
      <el-tab-pane label="基本信息" :style="{height:height+'px'}">
        <el-descriptions class="margin-top" title="基本信息" :column="3" border label-class-name="my-label" content-class-name="my-content">
          <el-descriptions-item label="企业名称">
            {{data.name}}
          </el-descriptions-item>
          <el-descriptions-item label="注册地址">
            {{data.mb_reg_address}}
          </el-descriptions-item>
          <el-descriptions-item label="生产经营地点">
            {{data.mb_operate_address}}
          </el-descriptions-item>
          <el-descriptions-item label="所属行政区">
            {{data.mb_region}}
          </el-descriptions-item>
          <el-descriptions-item label="营业执照">
            <el-image
                style="width: 100px; height: 100px"
                :src="data.licenseUrl"
                :preview-src-list="[data.licenseUrl]">
            </el-image>
          </el-descriptions-item>
          <el-descriptions-item label="安全行政许可资料">
            <el-image
                style="width: 100px; height: 100px"
                :src="data.aocUrl"
                :preview-src-list="[data.aocUrl]">
            </el-image>
          </el-descriptions-item>
          <el-descriptions-item label="法定代表人">
            {{data.legal}}
          </el-descriptions-item>
          <el-descriptions-item label="法人联系电话">
            {{data.legal_mobile}}
          </el-descriptions-item>
          <el-descriptions-item label="法人邮箱">
            {{data.legal_email}}
          </el-descriptions-item>
          <el-descriptions-item label="企业传真">
            {{data.fax}}
          </el-descriptions-item>
          <el-descriptions-item label="邮政编码">
            {{data.postal_code}}
          </el-descriptions-item>
          <el-descriptions-item label="国民经济行业">
            {{data.mb_economy_sector}}
          </el-descriptions-item>
          <el-descriptions-item label="行业/专业">
            {{data.industry}}/{{data.specialty}}
          </el-descriptions-item>
          <el-descriptions-item label="统一社会信用代码">
            {{data.license_number}}
          </el-descriptions-item>
          <el-descriptions-item label="信用代码有效期">
            {{data.license_date}}
          </el-descriptions-item>
          <el-descriptions-item label="经济类型">
            {{data.mb_economy_type}}
          </el-descriptions-item>
          <el-descriptions-item label="企业规模">
            {{data.enterprise_size}}
          </el-descriptions-item>
          <el-descriptions-item label="注册资本">
            {{data.reg_money}}万元
          </el-descriptions-item>
          <el-descriptions-item label="安全管理联系人">
            {{data.manager}}
          </el-descriptions-item>
          <el-descriptions-item label="联系电话">
            {{data.manager_mobile}}
          </el-descriptions-item>
          <el-descriptions-item label="邮箱">
            {{data.manager_email}}
          </el-descriptions-item>
          <el-descriptions-item label="成立日期">
            {{data.date}}
          </el-descriptions-item>
          <el-descriptions-item label="固定资产">
            {{data.fixed_asset}}
          </el-descriptions-item>
          <el-descriptions-item label="年营业收入">
            {{data.revenue}}万元
          </el-descriptions-item>
          <el-descriptions-item label="员工总数">
            {{data.personnel}}
          </el-descriptions-item>
          <el-descriptions-item label="营业场所面积">
            {{data.area}}m³
          </el-descriptions-item>
          <el-descriptions-item label="专职安全管理人数">
            {{data.personnel_full}}
          </el-descriptions-item>
          <el-descriptions-item label="兼职安全管理人数">
            {{data.personnel_part}}
          </el-descriptions-item>
          <el-descriptions-item label="特种作业人数">
            {{data.personnel_special}}
          </el-descriptions-item>
          <el-descriptions-item label="所属集团名称">
            {{data.group_name}}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag v-if="data.status==0" type="danger">禁用</el-tag>
            <el-tag v-if="data.status==1" type="success">正常</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="经营范围" :span="3">
            {{data.business}}
          </el-descriptions-item>
        </el-descriptions>
      </el-tab-pane>
      <el-tab-pane label="证书添加记录" :style="{height:height+'px'}">
        <el-table border
                  v-loading="loading"
                  :data="ca"
                  style="width: 100%;margin-bottom: 20px;"
                  ref="qtable"
                  :height="height"
                  size="small">
          <el-table-column
              prop="standard_name"
              label="企业安全生产标准化评定标准"
              align="center"
              show-overflow-tooltip
              min-width="200">
          </el-table-column>
          <el-table-column
              prop="level"
              label="企业安全生产标准化级别"
              align="center"
              min-width="100">
          </el-table-column>
          <el-table-column
              prop="standard"
              label="现场评审报告"
              align="center"
              min-width="100">
            <template slot-scope="scope">
              <el-image
                  style="width: 100px; height: 100px"
                  :src="scope.row.standardUrl"
                  :preview-src-list="[scope.row.standardUrl]">
              </el-image>
            </template>
          </el-table-column>
          <el-table-column
              prop="image"
              label="企业安全生产标准化证书"
              align="center"
              min-width="100">
            <template slot-scope="scope">
              <el-image
                  style="width: 100px; height: 100px"
                  :src="scope.row.imageUrl"
                  :preview-src-list="[scope.row.imageUrl]">
              </el-image>
            </template>
          </el-table-column>
          <el-table-column
              prop="code"
              label="企业安全生产标准化证书编号"
              align="center"
              min-width="100">
          </el-table-column>
          <el-table-column
              prop="date"
              label="证书有效期"
              align="center"
              min-width="100">
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="创标申请记录" :style="{height:height+'px'}">
      </el-tab-pane>
      <el-tab-pane label="定级申请记录" :style="{height:height+'px'}">
      </el-tab-pane>
      <el-tab-pane label="证书记录" :style="{height:height+'px'}">
      </el-tab-pane>
    </el-tabs>
    <!--编辑联系人-->
  </el-dialog>
</template>
<script>
module.exports = {
  name: "add",
  // 模板导入区
  components: {
  },
  data:function() {
    return {
      id:0,
      isAdmin:false,
      visible: false,
      dialogFormVisible: false,
      title: '详情',
      loading: false,
      noMore: false,
      user_id: 0,
      data: {
      },
      is_see:0,
      ca:[],
      details:[],
      cards:{},
      type:{},
      pcas: [],
      restaurants:[],
      restaurants2:[],
      height: document.documentElement.clientHeight - 250,
    }
  },
  computed: {
    disabled () {
      return this.loading || this.noMore
    }
  },
  mounted: function(){
    //this.getConfig();
  },
  created:function(){
  },
  methods: {
    /**
     * 打开弹窗调用方法
     * */
    open: function (row) {
      var _this =this;
      console.log(row)
      _this.id = row.id;
      _this.visible = true;
      _this.noMore = false;
      _this.getInfo(row.id);
      _this.getCa(row.id);
    },
    closeDialog: function () {
      this.visible = false;
    },
    getCa:function(id){
      var _this = this;
      if(!_this.noMore){
        _this.loading = true;
        axios.post('caList', {id:id}).then(function (res) {
          if (res.data.code == 0) {
            _this.ca = res.data.data;
          }
          _this.loading = false;
        }).catch(function (error) {
          _this.loading = false;
          console.log("出现错误:",error);
        });
      }
    },
    getInfo:function(id){
      var _this = this;
      if(id){
        axios.post("companyInfo", {
          id:id
        }).then(function (res) {
          if (res.data.code == 0) {
            _this.data=res.data.data;
          }else {
            _this.$message({
              message: res.data.msg,
              type: res.data.type
            });
          }
        }).catch(function (error) {
          console.log("出现错误:",error);
        });
      }
    },
    addContacts: function(){
      this.form = {};
      this.dialogFormVisible = true;
    },
    editContacts: function(row){
      this.form = row;
      this.dialogFormVisible = true;
    },
  }
}
</script>


