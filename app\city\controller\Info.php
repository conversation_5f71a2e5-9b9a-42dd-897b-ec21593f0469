<?php
declare (strict_types=1);

namespace app\city\controller;

use think\Request;
use think\facade\Db;
use app\admin\model\TopCityUserModel;
use app\model\FileModel;
use app\admin\model\TopCityModel;
use app\admin\model\TopPcaModel;
use app\admin\model\CompanyModel;
use think\facade\View;

class Info extends Base
{
	/**
	 * 显示资源列表
	 *
	 * @return \think\Response
	 */
	public function index()
	{
		//
		$loginUser = $this->userInfo;
		return view('', ['title' => '人员管理', 'loginUser' => $loginUser]);
	}

	/**
	 * 请求列表
	 */
	public function getList()
	{
		$param = $this->request->param();
		$city_id = $this->userInfo->city_id;
		$param['city_id'] = $city_id;
		$model = new TopCityUserModel();
		$data = $model->getList($param, $param['page'], $param['limit']);
		$cityModel = new TopCityModel();
		result(array('list' => $data, 'city' => $cityModel->getList([], 0, 0, true), 'department' => $model->department_arr, 'role' => $model->role_arr));
	}


	public function save_data()
	{
		$param = $this->request->param();
		$model = new TopCityUserModel();
		$id = intval($param['id']);
		if ($id) $model = $model::find($id);
		$data = $model->newSave($model, $param, $id);
		result(null, 0, $data);
	}

	/**
	 * 修改密码
	 */
	public function update_password()
	{
		$param = $this->request->param();
		$id = intval($param['id']);
		$model = TopCityUserModel::find($id);
		$data = $model->updatePassword($model, $param['password']);
		if ($data !== '数据处理成功') $code = -200;
		result(null, $code ? $code : 0, $data);
	}

	public function check_user()
	{
		$param = $this->request->param();
		$id = intval($param['id']);
		$model = TopCityUserModel::find($id);
		$data = $model->checkUser($model, $param['status']);
		if ($data !== '数据处理成功') $code = -200;
		result(null, $code ? $code : 0, $data);
	}

	/**
	 * 显示创建资源表单页.
	 *
	 * @return \think\Response
	 */
	public function create()
	{
		//
	}

	/**
	 * 保存新建的资源
	 *
	 * @param \think\Request $request
	 * @return \think\Response
	 */
	public function save(Request $request)
	{
		//
	}

	/**
	 * 显示指定的资源
	 *
	 * @param int $id
	 * @return \think\Response
	 */
	public function read($id)
	{
		//
	}

	/**
	 * 显示编辑资源表单页.
	 *
	 * @param int $id
	 * @return \think\Response
	 */
	public function edit($id)
	{
		//
	}

	/**
	 * 保存更新的资源
	 *
	 * @param \think\Request $request
	 * @param int $id
	 * @return \think\Response
	 */
	public function update(Request $request, $id)
	{
		//
	}

	/**
	 * 删除
	 */
	public function delete()
	{
		$param = $this->request->param();
		$id = intval($param['id']);
		$model = TopCityUserModel::find($id);
		if ($model) {
			$data = $model->delData($model);
			if ($data != '数据处理成功') $code = -200;
		} else {
			$code = -200;
			$data = '未查询到数据';
		}
		result(null, $code ? $code : 0, $data);
	}


	public function review()
	{

		return view('', ['title' => '运行资料']);
	}

	public function c_review()
	{
		$param = $this->request->param();
		$id = $param['company_id'];
		return view('', ['title' => '运行资料', 'company_id' => $id]);
	}

	public function get_company()
	{
		$param = $this->request->param();
		$model = new CompanyModel();
		$data = $model->getList($param, $param['page'], $param['limit']);
		result($data);
	}

	public function review_info()
	{

		$param = $this->request->param();
		$company_id = $param['company_id'];
		$element_id = $param['element_id'];
		return view('', ['title' => '运行资料查看', 'company_id' => $company_id, 'element_id' => $element_id]);
	}

	public function get_review_list()
	{
		$param = $this->request->param();
		$company_id = $param['id'];
		$where = [];
		$company = Db::table('top_company_info')->where(['id' => $company_id])->find();
		$where[] = ['main_id', '=', $company['review_id']];
//            $where[] = ['company_id','=',$company['id']];
		$where[] = ['pid', '=', 0];
//            echo Db::table('cay_company_review_content_list')->where(['element_id'=>1])->fetchSql()->count('id');die;
		$result['elementTitle'] = Db::table('top_company_review_element')->where($where)->field('id,name,main_id')->order('sort')->select()->each(function ($item) {
			$num = Db::table('top_company_review_content_list')->where([['element_ids', 'like', "%,{$item['id']},%"], ['is_sub', '=', '0']])->count('id');
			$item['num'] = empty($num) ? '' : $num;
			return $item;
		});
		result($result);
	}

	public function get_review_info()
	{
		$param = $this->request->param();
		$company_id = $param['company_id'];
		$element_id = $param['element_id'];
		$company = Db::table('top_company_info')->where(['id' => $company_id])->find();
		$where = [
			['a.main_id', '=', $company['review_id']],
			['a.is_del', '=', 0],
		];
		$tmp = [];
		if (!empty($element_id)) {
			$element_id = is_array($element_id) ? $element_id[count($element_id) - 1] : $element_id;
			$where[] = ['a.element_ids', 'like', "%,$element_id,%"];
		}
		$list = Db::table('top_company_review_content_list')->where([['element_ids', 'like', "%,$element_id,%"]])->order('id')->select()->toArray();

		foreach ($list as $k => $v) {
			$files = explode(',', $v['sub_files']);
			$v['sub_files'] = $files;
			$v['mb_sub_files'] = [];
			$v['edit'] = false;
			foreach ($files as $v1) {
				if (!empty($v1)) {
					$v['mb_sub_files'][] = FileModel::getFile(0, $v1, '');
				}
			}
			$tmp[$v['content_id']][] = $v;
		}

		$result['content'] = Db::table('top_company_review_content')->alias('a')
			->where($where)->order('a.sort')->field('a.id,a.ask,a.standards,a.score,a.cycle,a.method')
			->select()->each(function ($item) use ($tmp) {
				$item['list'] = $tmp[$item['id']];
				return $item;
			})->toArray();
		$element = Db::table('top_company_review_element')
			->where(['main_id' => $company['review_id']])
			->field('id,name,pid')->select()->toArray();
		$result['element'] = get_tree_children($element);
		result($result);
	}
}
