<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>企业信息查询</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .el-table .danger-row {
            background: #fbc4c4;
        }
        .el-table .warning-row {
            background: oldlace;
        }
        .el-table .success-row {
            background: #f0f9eb;
        }
        .el-badge__content.is-fixed { right: 20px;z-index: 9;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">
        <el-form :inline="true" :model="searchFrom" class="form-inline" @submit.native.prevent>
            <el-form-item>
                <el-input v-model="searchFrom.title" size="mini" placeholder="企业名称/法人"></el-input>
            </el-form-item>
            <el-form-item>
                <el-input v-model="searchFrom.mb_operate_address" size="mini" placeholder="生产经营地址"></el-input>
            </el-form-item>
            <el-form-item>
                <!--<el-input v-model="searchFrom.industry" size="mini" placeholder="行业/专业"></el-input>-->
                <el-select size="mini" v-model="searchFrom.industry" placeholder="所属行业">
                    <el-option label="所属行业" value=""></el-option>
                    {volist name="industry" id="item"}
                    <el-option label="{$item.name}" value="{$item.name}"></el-option>
                    {/volist}
                </el-select>
            </el-form-item>

            <el-form-item>
                <el-input v-model="searchFrom.enterprise_size" size="mini" placeholder="企业规模"></el-input>
            </el-form-item>
            <el-form-item>
                <el-select size="mini" v-model="searchFrom.area" placeholder="所属区县">
                    <el-option label="所属区县" value=""></el-option>
                    {volist name="area" id="item"}
                    <el-option label="{$item.name}" value="{$item.id}"></el-option>
                    {/volist}
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-select size="mini" v-model="searchFrom.mb_status" placeholder="企业状态">
                    <el-option label="企业状态" value=""></el-option>
                    <el-option label="未创标" value="info"></el-option>
                    <el-option label="试运行中" value="primary"></el-option>
                    <el-option label="持续运行中" value="success"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-select size="mini" v-model="searchFrom.ca_status" placeholder="证书状态">
                    <el-option label="证书状态" value=""></el-option>
                    <el-option label="未取得" value="warning"></el-option>
                    <el-option label="生效中" value="success"></el-option>
                    <el-option label="已过期" value="danger"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="getData()" size="mini">查询</el-button>
                <el-button @click="reset()" size="mini">重置</el-button>
            </el-form-item>
            <el-form-item style="float: right">
<!--                <el-button :loading="loading" type="success" size="mini" @click="add">添加</el-button>-->
                <el-button :loading="loading" type="primary" size="mini" @click="export1">导出</el-button>
<!--                <el-button :loading="loading" type="primary"  size="mini" @click="import1">导入</el-button>-->
            </el-form-item>
        </el-form>
        <el-table border
                  v-loading="loading"
                  :data="data"
                  style="width: 100%;margin-bottom: 20px;"
                  ref="qtable"
                  :height="height"
                  size="small"
                  :row-class-name="tableRowClassName">
            <el-table-column
                    type="index"
                    label="序号"
                    align="center"
                    width="50">
            </el-table-column>
            <el-table-column
                    prop="mb_region"
                    label="所属区县"
                    align="center"
                    show-overflow-tooltip
                    min-width="100">
            </el-table-column>
            <el-table-column
                    prop="name"
                    label="企业名称"
                    align="center"
                    show-overflow-tooltip
                    min-width="200">
                <template slot-scope="scope">
                    <!--<el-link target="_blank" :href="'/general/toppingsoft/index.php/city/company/info?id='+scope.row.id" type="primary" v-html="scope.row.name"></el-link>-->
                    <el-link @click="info(scope.row)" type="primary" v-html="scope.row.name"></el-link>
                </template>
            </el-table-column>
            <el-table-column
                    prop="mb_operate_address"
                    label="生产经营地址"
                    align="center"
                    show-overflow-tooltip
                    min-width="100">
            </el-table-column>
            <el-table-column
                    prop="mb_status"
                    label="企业状态"
                    align="center"
                    show-overflow-tooltip
                    width="120">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.stand_status==0" type="info">未创标</el-tag>
                    <el-tag v-if="scope.row.stand_status==1&&scope.row.ca_status==0" type="primary">试运行中</el-tag>
                    <el-tag v-if="scope.row.stand_status==1&&scope.row.ca_status==1" type="success">持续运行中</el-tag>
                </template>
            </el-table-column>
            <el-table-column
                    prop="ca_status"
                    label="是否取得证书"
                    align="center"
                    show-overflow-tooltip
                    width="100">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.ca_status==0" type="warning">未取得</el-tag>
                    <el-tag v-if="scope.row.ca_status==1" type="success">生效中</el-tag>
                    <el-tag v-if="scope.row.ca_status==2" type="danger">已过期</el-tag>
                </template>
            </el-table-column>
            <el-table-column
                    prop="industry"
                    label="行业/专业"
                    align="center"
                    show-overflow-tooltip
                    min-width="100">
                <template slot-scope="scope">
                    {{scope.row.industry}}/{{scope.row.specialty}}
                </template>
            </el-table-column>
            <el-table-column
                    prop="enterprise_size"
                    label="企业规模"
                    align="center"
                    show-overflow-tooltip
                    min-width="100">
            </el-table-column>
        </el-table>
        <!--分页条total, sizes, prev, pager, next, jumper-->
        <div class="block">
            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="page"
                    :page-sizes="[10, 20, 50, 100, 500, 1000]"
                    :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total">
            </el-pagination>
        </div>
    </div>
    <import1 ref="import1" @refresh="getData()"></import1>
    <info ref="info" @ok="getData()"></info>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.all.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                title: '新增',
                searchFrom: {
                    status: 1,
                    title: '',
                },
                thisrow: {},
                thisuser: '',
                data: [],
                filelist: [],
                page: 1,
                pageSize: 20,
                total: 0,
                deptData: [],
                userData: [],
                dialogFormVisible: false,
                visible: false,
                loading: true,
                form: {},
                formRules: {
                    form_name: [{required: true, message: "请输入表单名称", trigger: "blur"}],
                },
                ueObj: null,
                height: document.documentElement.clientHeight - 155,
            };
        },
        components: {
            'info': 'url:/general/toppingsoft/app/city/view/company/vue/info.vue?v=1',
            'import1': 'url:/general/toppingsoft/public/vue/import.vue',
        },
        watch: {
            $route() {
                //切换标签时
                this.handleRefreshTable();
            }
        },
        created() {
            let _this = this;
            //窗口改变时
            window.onresize = function () {
                _this.handleRefreshTable();
            };
        },
        updated() {
            //数据改变时
            this.handleRefreshTable();
        },
        methods: {
            tableRowClassName({row, rowIndex}) {
                if (row.back) {
                    return row.back;
                }
                return '';
            },
            handleClick(tab, event) {
                this.searchFrom = {
                    our: '',
                    status: '',
                    level: '',
                    type: '',
                    company: '',
                    charge_user_name: '',
                };
                this.getData();
            },
            statusChange() {
                this.page = 1;
                this.getData();
            },
            handleSizeChange: function (val) {
                this.pageSize = val;
                this.getData();
                console.log('每页 ${val} 条');
            },
            handleCurrentChange: function (val) {
                this.page = val;
                this.getData();
                console.log('当前页: ${val}');
            },
            info(row) {
                this.$refs.info.open(row);
            },
            del(row) {
                var _this = this;
                _this.$confirm("确认删除？", "提示", {}).then(() => {
                    _this.addLoading = true;
                    var param = {};
                    var url = "del";
                    param.id = row.id;
                    axios.post(url, param).then(function (res) {
                        _this.addLoading = false;
                        if (res.data.code == 0) {
                            _this.$message({
                                message: res.data.msg,
                                type: "success"
                            });
                            _this.$refs.info.visible = false;
                            _this.getData();
                        } else {
                            _this.$message({
                                message: res.data.msg,
                                type: "error"
                            });
                        }
                    }).catch(function (error) {
                        console.log(error);
                    });
                });
            },
            //数据初始化
            reset() {
                this.searchFrom.title ='';
                this.searchFrom.mb_operate_address ='';
                this.searchFrom.enterprise_size ='';
                this.searchFrom.area ='';
                this.searchFrom.industry ='';
                this.searchFrom.mb_status ='';
                this.searchFrom.ca_status ='';
                this.page = 1;
                this.pageSize = 20;
                this.getData();
            },
            // 请求行业数据
            /*getIndustryList() {
              this.loading = true;
              axios.get('industry_list')
                .then(res => {
                  this.searchFrom.industry = res.data.data;  // 假设接口返回的是数组
                })
                .catch(err => {
                  console.error('获取行业数据失败:', err);
                })
                .finally(() => {
                  this.loading = false;
                });
            },*/
            //数据加载
            getData() {
                var _this = this;
                var param = _this.searchFrom;
                param._ajax = 1;
                param.page = _this.page;
                param.limit = _this.pageSize;
                param.our = 1;
                axios.post('', param).then(function (res) {
                    if (res.data.code == 0) {
                        _this.data = res.data.data.data;
                        _this.page = res.data.data.current_page;
                        _this.pageSize = res.data.data.per_page;
                        _this.total = res.data.data.total;
                        _this.$nextTick(() => {
                            // 这里的代码会在DOM更新完成后执行
                            _this.loading = false;
                        });
                        _this.handleRefreshTable();
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            import1: function () {
                this.$refs.import1.templateUrl = 'importTemplate';
                this.$refs.import1.submitUrl = 'import';
                this.$refs.import1.title = '企业信息导入';
                this.$refs.import1.open();
            },
            export1: function () {
                let _this = this;
                let where = "";
                let type = '';
                //获得where
                where = '';
                for (let index in this.searchFrom) {
                    if (this.searchFrom[index] !== '' && this.searchFrom[index] !== [] && this.searchFrom[index] !== {}) {
                        let str = "";
                        if (index == 'type') {
                            for (let i in this.searchFrom.type) {
                                type += this.searchFrom.type[i] + ',';
                            }
                            str += 'type=' + type;
                        } else {
                            str += index + '=' + this.searchFrom[index];
                        }
                        where += "&" + str;
                    }
                }
                let url = "index?excel=1&" + where;
                window.open(url);
                // location.href = url;
            },
            handleRefreshTable: function () {
                this.$nextTick(function () {
                    var refList = this.$refs;
                    if (refList) {
                        for (var i of Object.keys(refList)) {
                            if (refList[i] && refList[i].doLayout) {
                                refList[i].doLayout();
                            }
                        }
                    }
                });
            },
        },
        mounted() {
            //this.getIndustryList();
            //获取列表
            this.getData();

        }
    })
</script>


</body>
</html>