<?php

namespace app\company\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use hg\apidoc\annotation as Apidoc;
use think\facade\View;
use think\facade\Db;
use app\model\FileModel;

/**
 * @Apidoc\Title("自评管理")
 * @Apidoc\Group("Index")
 * @Apidoc\Sort(1)
 */
class Ca extends Base {

    /**
     * @Apidoc\Title("自评列表")
     * @Apidoc\Desc("自评列表")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function index($limit = 20) {
        if (request()->isAjax()) {
            $status = $this->request->param('status','','trim');
            $title = $this->request->param('title','','trim');
            $where = [
                ['a.company_id','=',$_SESSION['company']['id']],
            ];
            if(!empty($title)){
                $where[] = ['a.company_name|a.code','like',"%{$title}%"];
            }
            $fields = "a.*,b.title,b.date,type,b.status pstatus,b.notify,b.notify_date";
            $res = Db::table('top_certificate')->alias('a')
                ->leftJoin('top_publicity b','a.publicity_id = b.id')
                ->where($where)->field($fields)->order('a.start desc');
            $res = $res->paginate($limit)->each(function ($item, $key) {
                if($item['status']==7){
                    $item['status'] = strtotime($item['ends'])>strtotime(date('Y-m-d'))?7:8;
                }
                if($item['pstatus']>=1){
                    $days = floor(abs(strtotime(date('Y-m-d'))-strtotime($item['date']))/86400);
                    $item['publicity_status'] = $item['status']==1&&empty($item['notify'])?'已公示（'.$days.'天）':'已公示';
                }else{
                    $item['publicity_status'] = '未公示';
                }
                if(!empty($item['notify_date'])){
                    $days = floor(abs(strtotime(date('Y-m-d'))-strtotime($item['notify_date']))/86400);
                    $item['notify_status'] = $item['status']==1&&!empty($item['notify_date'])?'已公告'.'（'.$days.'天）':'已公告';
                }else{
                    $item['notify_status'] = '未公告';
                }
                return $item;
            });
            result($res);
        } else {
            View::assign('title', '首页');
            return view();
        }
    }

    public function apply($id=0) {
        /*$re = SettingModel::setReport($_SESSION['company']['id']);
        dd($re);*/
        if (request()->isAjax()) {
            if($id){
                $re = Db::table('top_company_evaluate')->where(['id'=>$id,'company_id'=>$_SESSION['company']['id']])->find();
            }
//            dd($re['personnels']);
            $company = Db::table('top_company_info')->where(['id'=>$_SESSION['company']['id']])->find();
            $company = CompanyModel::codeToText($company);
            $result = [
                'company_name' => $company['name'],
                'residence' => $company['mb_operate_address'],
                'type' => $re['type']??'',
                'government' => $re['government']??'',
                'legal' => $company['legal'],
                'legal_mobile' => $company['legal_mobile'],
                'legal_fax' => $company['fax'],
                'contacts' => $re['contacts']??$company['manager'],
                'contacts_tel' => $re['contacts_tel']??$company['manager_mobile'],
                'contacts_fax' => $re['contacts_fax']??$company['fax'],
                'contacts_mobile' => $re['contacts_mobile']??$company['manager_mobile'],
                'contacts_email' => $re['contacts_email']??$company['manager_email'],
                'old_level' => $re['old_level']??'',
                'group_name' => $re['group_name']??$company['group_name'],
                'personnels' => empty($re['personnels'])?[['name'=>'','deptname'=>'','mobile'=>'','remark'=>''],['name'=>'','deptname'=>'','mobile'=>'','remark'=>''],['name'=>'','deptname'=>'','mobile'=>'','remark'=>''],['name'=>'','deptname'=>'','mobile'=>'','remark'=>''],['name'=>'','deptname'=>'','mobile'=>'','remark'=>''],['name'=>'','deptname'=>'','mobile'=>'','remark'=>'']]:json_decode($re['personnels'],true),
            ];
            result($result);
        } else {
            View::assign('id', $id);
            View::assign('title', '首页');
            return view();
        }
    }

    public function score($id=0) {
        if (request()->isAjax()) {
            $company = Db::table('top_company_info')->where(['id'=>$_SESSION['company']['id']])->find();
            $where = [
                ['a.main_id', '=', $company['review_id']],
                ['a.is_del', '=', 0],
            ];
            if(!empty($element_id)){
                $element_id = is_array($element_id)?$element_id[count($element_id)-1]:$element_id;
                $where[] = ['a.element_ids', 'like', "%,$element_id,%"];
            }
            $list = Db::table('top_company_review_content_list')->where([['review_id','=',$company['review_id']]])->order('id')->select()->toArray();
            $tmp = [];
            foreach ($list as $k=>$v){
                $files = explode(',',$v['sub_files']);
                $v['sub_files'] = $files;
                $v['mb_sub_files'] = [];
                $v['edit'] = false;
                foreach ($files as $v1){
                    if(!empty($v1)){
                        $v['mb_sub_files'][] = FileModel::getFile(0,$v1,'');
                    }
                }
                $tmp[$v['content_id']][] = $v;
            }
            $result['content'] = Db::table('top_company_review_content')->alias('a')
                ->leftJoin('top_company_review_content_score b',"a.id=b.content_id and b.evaluate_id = {$id}")
                ->where($where)
                ->order('a.sort')->field('a.id,a.ask,a.standards,a.score scores,a.cycle,a.method,a.element_id,a.element_ids,b.score,b.reform,b.miss,b.resion,b.summary')
                ->select()->each(function ($item) use ($tmp){
                    $item['list'] = $tmp[$item['id']];
                    return $item;
                })->toArray();
            result($result);
        } else {
            View::assign('id', $id);
            View::assign('title', '首页');
            return view();
        }
    }


    public function summarize($id=0) {
        if (request()->isAjax()) {
            $result = Db::table('top_company_evaluate')->where(['id'=>$id,'company_id'=>$_SESSION['company']['id']])->find();
            $result['score'] = empty($result['score'])?[]:json_decode($result['score'],true);
            result($result);
        } else {
            View::assign('id', $id);
            View::assign('title', '首页');
            return view();
        }
    }

    public function seSave($id=0) {
        $request = $this->request->post();
        $id = SeModel::seSave($request,$id);
        if($id>0){
            result(['id'=>$id],0,'保存成功');
        }else{
            result('',7001,$id);
        }
    }

    public function scoreSave($id=0) {
        $request = $this->request->post();
        $id = SeModel::scoreSave($request,$id);
        if($id>0){
            result(['id'=>$id],0,'保存成功');
        }else{
            result('',7001,$id);
        }
    }

    public function endSave($id=0) {
        $request = $this->request->post();
        Verify::userCheck('end',$request);
        $id = SeModel::endSave($request,$id);
        if($id>0){
            result(['id'=>$id],0,'保存成功');
        }else{
            result('',7001,$id);
        }
    }

    public function down($id=0) {
        $result = Db::table('top_company_evaluate')->where([['id','=',$id],['company_id','=',$_SESSION['company']['id']]])->find();
        if(empty($result)||$result['status']!=7){
            result('',1003,'自评未完成');
        }
        $score = json_decode($result['score'],true);
//        dd($score);
        $template = new TemplateProcessor(root_path() . 'public/word/se.docx');
        $template->setValue("company_name", $result['company_name']);
        $template->setValue("industry", $result['industry']);
        $template->setValue("specialty", $result['specialty']);
        $template->setValue("score", $score['total']['score']);
        $template->setValue("level", $result['level']);
        $template->setValue("year", date('Y',strtotime($result['date'])));
        $template->setValue("month", date('m',strtotime($result['date'])));
        $template->setValue("day", date('d',strtotime($result['date'])));
        $template->setValue("residence", $result['residence']);
        $template->setValue("type", $result['type']);
        $template->setValue("government", $result['government']);
        $template->setValue("legal", $result['legal']);
        $template->setValue("legal_mobile", $result['legal_mobile']);
        $template->setValue("legal_fax", $result['legal_fax']);
        $template->setValue("contacts", $result['contacts']);
        $template->setValue("contacts_tel", $result['contacts_tel']);
        $template->setValue("contacts_fax", $result['contacts_fax']);
        $template->setValue("contacts_mobile", $result['contacts_mobile']);
        $template->setValue("contacts_email", $result['contacts_email']);
        $template->setValue("group_name", $result['group_name']);
        $template->setValue("overview", $result['overview']);
        $template->setValue("accident", $result['accident']);
        $template->setValue("question", $result['question']);
        $template->setValue("conclusion", $result['conclusion']);
        $i = 1;
        foreach (json_decode($result['personnels'],true) as $v){
            $template->setValue("name".$i, $v['name']);
            $template->setValue("deptname".$i, $v['deptname']);
            $template->setValue("mobile".$i, $v['mobile']);
            $template->setValue("remark".$i, $v['remark']);
            $i++;
        }
        $template->cloneRow("sort", count($score['score']));
        foreach ($score['score'] as $k => $v) {
            $template->setValue("sort#" . ($k + 1), $k+1);
            $template->setValue("name#" . ($k + 1), $v['name']);
            $template->setValue("sumscore#" . ($k + 1), $v['sumscore']);
            $template->setValue("score#" . ($k + 1), $v['score']);
            $template->setValue("deduct#" . ($k + 1), $v['deduct']);
            $template->setValue("miss#" . ($k + 1), $v['miss']);
            $template->setValue("summary#" . ($k + 1), $v['summary']);
        }
        //导出文件备份
        $copyPath = root_path() . "public/storage/tmp/" . time() . rand(1000, 9999) . '.docx';
        $pdfpath = root_path() . "public/storage/tmp/" . time() . rand(1000, 9999) . '.pdf';
        $template->saveAs($copyPath);
        /*header("Content-type: application/vnd.ms-word");
        header("Content-Disposition:attachment;filename=" . $result['company_name'] .date('Y',strtotime($result['date'])). '年度自评报告.docx');
        header('Cache-Control: max-age=0');*/
        FileModel::wordToPdf($copyPath,$pdfpath);
        header("Content-type: application/pdf");
        header("Content-Disposition:attachment;filename=" . $result['company_name'] .date('Y',strtotime($result['date'])). '年度自评报告.pdf');
        header('Cache-Control: max-age=0');
        readfile($pdfpath);
        unlink($copyPath);
        unlink($pdfpath);
        exit;
    }

    public function getConfig() {
        $result['standard'] = Db::table('top_standard_name')->where(['is_del'=>0])->field('id,name')->select()->toArray();
        result($result);
    }

    public function getInfo($id=0) {
        $company = Db::table('top_company_info')->where(['id'=>$_SESSION['company']['id']])->find();
        $re = Db::table('top_standard_apply')->where(['company_id'=>$_SESSION['company']['id']])->find();
        if(!empty($re)){
            $re = StandardModel::codeToText($re);
            $re['industry'] = $company['industry'].'/'.$company['specialty'];
        }else{
            $re = [
                'company_name' => $company['name'],
                'industry' => $company['industry'].'/'.$company['specialty'],
                'standard_id' => '',
                'level' => '',
                'is_advisory' => '',
                'advisory' => '',
            ];
        }
        result($re);
    }



}
