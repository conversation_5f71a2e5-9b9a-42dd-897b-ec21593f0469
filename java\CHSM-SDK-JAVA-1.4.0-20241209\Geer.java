import kl.hsm.client.sdk.HsmClient;
import kl.hsm.client.model.SessionKey;
import kl.hsm.client.sdk.HsmClient;
import kl.hsm.client.sdk.HsmSdk;
import kl.hsm.common.KeyType;
import kl.hsm.server.svc.base.*;
import org.apache.thrift.TException;
import org.bouncycastle.util.encoders.Hex;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

public class Geer {
    public static void main(String[] args) {
        if (args.length < 2) {
            System.out.println("Usage: java Geer <operation> <input>");
            System.out.println("Operations: encrypt, decrypt, hmac, sessionid, sessionkey");
            return;
        }

        String operation = args[0];
        String input = args[1];

        HsmClient hsmClient = new HsmClient();
        try {
            switch (operation) {
                case "encrypt":
                    String encryptedData = encryptData(hsmClient, input);
                    System.out.println("Encrypted Data: " + encryptedData);
                    break;
                case "decrypt":
                    String decryptedData = decryptData(hsmClient, input);
                    System.out.println("Decrypted Data: " + decryptedData);
                    break;
                case "hmac":
                    String hmac = generateHmac(hsmClient, input);
                    System.out.println("HMAC: " + hmac);
                    break;
                case "sessionid":
                    String sessionId = getSessionId(hsmClient);
                    System.out.println("Session ID: " + sessionId);
                    break;
                case "sessionkey":
                    String sessionKey = generateSessionKey(hsmClient);
                    System.out.println("Session Key: " + sessionKey);
                    break;
                default:
                    System.out.println("Unknown operation: " + operation);
            }
        } catch (Exception e) {
            System.out.println("Error: " + e.getMessage());
        }
    }

    public static String encryptData(HsmClient hsmClient, String data) throws Exception {
        byte[] dataBytes = data.getBytes("UTF-8");

        // 假设需要指定算法类型、对称参数和初始化向量
        Algo algo = Algo.valueOf("AES"); // 使用 Algo 类中的正确常量
        SymParam param = new SymParam(); // 示例：创建对称参数对象
        byte[] iv = new byte[16]; // 示例：初始化向量，长度为 16 字节

        // 调用 HsmClient.encrypt 方法进行加密
        byte[] encryptedBytes = hsmClient.encrypt(algo, dataBytes, param, iv);

        // 将加密后的字节数组转换为 Base64 编码的字符串
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    public static String decryptData(HsmClient hsmClient, String encryptedData) throws Exception {
        byte[] encryptedBytes = Base64.getDecoder().decode(encryptedData);
        byte[] decryptedBytes = hsmClient.decrypt(encryptedBytes);
        return new String(decryptedBytes, "UTF-8");
    }

    public static String generateHmac(HsmClient hsmClient, String data) throws Exception {
        byte[] dataBytes = data.getBytes("UTF-8");
        byte[] hmacBytes = hsmClient.generateHmac(dataBytes);
        return Base64.getEncoder().encodeToString(hmacBytes);
    }

    public static String getSessionId(HsmClient hsmClient) {
        return hsmClient.getSessionId();
    }

    public static String generateSessionKey(HsmClient hsmClient) throws Exception {
        byte[] sessionKeyBytes = hsmClient.generateSessionKey(16);
        return Base64.getEncoder().encodeToString(sessionKeyBytes);
    }
}