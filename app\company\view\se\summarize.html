<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>自评总结</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/company.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .mytable {border-collapse:collapse;width: 100%;}
        .mytable thead tr td {border: 1px solid #333;font-weight: 700; padding: 10px;background: #f5f7fa;text-align: center;}
        .mytable tbody tr th {border: 1px solid #333;font-weight: 700; padding: 10px;text-align: center;font-size: 16px;}
        .mytable tbody tr td {border: 1px solid #333; padding: 10px;text-align: center;font-size: 16px;}
        .mytable .active td{ background: #f2f2f2;}
        .mytable tbody tr td {font-family: 宋体;text-align: left;}
        .mytable tbody tr td p{line-height: 30px;}
        .el-divider.el-divider--horizontal { margin:10px 0;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">
        <el-page-header @back="goBack" content="自评总结"></el-page-header>
        <el-divider style="margin:10px 0;"></el-divider>
        <el-form label-position="top" label-width="80px" :model="data">
            <el-form-item label="1.企业概况。">
                <template slot="label">
                    1.企业概况。
                    <el-popover
                            placement="top-start"
                            title="温馨提示"
                            width="200"
                            trigger="hover"
                            content="主要包括经营范围、主营业务、企业规模（含职工人数）、机构设置、在行业中所处地位、安全生产工作特点等。">
                        <i slot="reference" class="el-icon-question"></i>
                    </el-popover>
                </template>
                <el-input style="overflow:hidden;" type="textarea" :autosize="{minRows: 2}" v-model="data.overview" placeholder="请填写不少于5个字的企业概况"></el-input>
            </el-form-item>
            <el-form-item label="2.企业生产安全事故情况（本自评年度内）。">
                <template slot="label">
                    2.企业生产安全事故情况（本自评年度内）。
                    <el-popover
                            placement="top-start"
                            title="温馨提示"
                            width="200"
                            trigger="hover"
                            content="填写企业生产安全事故情况需如实记录事故时间、地点、原因、伤亡情况及处理措施。">
                        <i slot="reference" class="el-icon-question"></i>
                    </el-popover>
                </template>
                <el-input style="overflow:hidden;" type="textarea" :autosize="{minRows: 2}" v-model="data.accident"></el-input>
            </el-form-item>
            <el-form-item label="3.企业安全生产标准化工作存在的问题和取得的成效。">
                <el-input style="overflow:hidden;" type="textarea" :autosize="{minRows: 2}" v-model="data.question"></el-input>
            </el-form-item>
            <el-form-item label="4.自评打分表（得分情况、扣分情况）及整改完成情况。">
                <el-table border
                          :data="data.score.score"
                          style="width: 100%;margin-bottom: 20px;"
                          ref="qtable"
                          show-summary
                          size="small"
                          :row-class-name="tableRowClassName">
                    <el-table-column
                            type="index"
                            label="序号"
                            align="center"
                            width="50">
                    </el-table-column>
                    <el-table-column
                            prop="name"
                            label="自评类目"
                            align="center"
                            show-overflow-tooltip
                            width="150">
                    </el-table-column>
                    <el-table-column
                            prop="sumscore"
                            label="标准分"
                            align="center"
                            width="120">
                    </el-table-column>
                    <el-table-column
                            prop="score"
                            label="得分"
                            align="center"
                            width="120">
                    </el-table-column>
                    <el-table-column
                            prop="deduct"
                            label="扣分"
                            align="center"
                            width="120">
                    </el-table-column>
                    <el-table-column
                            prop="miss"
                            label="缺项分"
                            align="center"
                            width="120">
                    </el-table-column>
                    <el-table-column
                            prop="summary"
                            label="扣分说明"
                            align="center"
                            show-overflow-tooltip
                            min-width="120">
                    </el-table-column>
                </el-table>
                <el-form-item label="整改完成情况">
                    <el-input style="overflow:hidden;" type="textarea" :autosize="{minRows: 2}" v-model="data.reform"></el-input>
                </el-form-item>
            </el-form-item>
            <el-form-item label="5.自评结论。">
                <el-input style="overflow:hidden;" type="textarea" :autosize="{minRows: 2}" v-model="data.conclusion"></el-input>
            </el-form-item>
            <!--<el-form-item label="6.企业主要负责人承诺书（申请定级的企业提交）。">
                <el-input v-model="data.letter"></el-input>
            </el-form-item>-->
        </el-form>
        <div style="text-align: center;margin: 20px auto;">
            <el-button type="primary" @click="submit(0)" v-loading.fullscreen.lock="loading">暂存</el-button>
            <el-button type="primary" @click="submit(1)">保存并提交自评报告</el-button>
            <el-button @click="location.href = 'index'">返回</el-button>
        </div>
    </div>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.all.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                title: '新增',
                id: '{$id}',
                data: {
                    score:{score:[]},
                },
                visible: false,
                loading: true,
                height: document.documentElement.clientHeight - 155,
            };
        },
        methods: {
            tableRowClassName({row, rowIndex}) {
                if (row.back) {
                    return row.back;
                }
                return '';
            },
            //数据加载
            getData() {
                var _this = this;
                var param = {};
                param.id = _this.id;
                param._ajax = 1;
                axios.post('summarize', param).then(function (res) {
                    if (res.data.code == 0) {
                        _this.data = res.data.data;
                        _this.$nextTick(() => {
                            // 这里的代码会在DOM更新完成后执行
                            _this.loading = false;
                        });
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            submit: function (status) {
                var _this = this;
                var param = _this.data;
                param.status = status;
                _this.loading = true;
                if(status==1){
                    _this.$confirm("确定提交自评报告，提交后不可修改？", "提示", {}).then(() => {
                        _this.loading = true;
                        axios.post("endSave?id="+_this.id, param).then(function (res) {
                            _this.loading = false;
                            _this.$message({
                                message: res.data.msg,
                                type: res.data.type
                            });
                            if (res.data.code == 0) {
                                location.href = 'index';
                            }
                        }).catch(function (error) {
                            _this.loading = false;
                            console.log("出现错误:",error);
                        });
                    });
                }else{
                    axios.post("endSave?id="+_this.id, param).then(function (res) {
                        _this.loading = false;
                        _this.$message({
                            message: res.data.msg,
                            type: res.data.type
                        });
                        if (res.data.code == 0) {
                            location.href = 'index';
                        }
                    }).catch(function (error) {
                        _this.loading = false;
                        console.log("出现错误:",error);
                    });
                }
            },
            goBack() {
                location.href = 'index';
            },
        },
        mounted() {
            //获取列表
            this.getData();

        }
    })
</script>


</body>
</html>