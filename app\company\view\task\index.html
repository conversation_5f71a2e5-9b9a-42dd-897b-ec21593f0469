<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>评审任务</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/company.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .my-content p{ line-height:20px; margin: 10px 0;}
        .company-link {
            color: #409EFF;
            text-decoration: none;
            border: none;
            background: none;
            padding: 0;
            margin: 0;
            font-size: inherit;
            cursor: pointer;
        }
        .company-link:hover {
            color: #66b1ff;
            text-decoration: underline;
        }
        .company-link:focus {
            outline: none;
        }
        
        /* 满意度调查弹框样式 */
        .satisfaction-dialog .el-dialog__header {
            background-color: #f5f7fa;
            border-bottom: 1px solid #e4e7ed;
        }
        
        .satisfaction-dialog .el-dialog__title {
            color: #409EFF;
            font-weight: 600;
            font-size: 18px;
        }
        
        .satisfaction-dialog .el-form-item__label {
            color: #606266;
            font-weight: 500;
            line-height: 1.6;
        }
        
        .satisfaction-dialog .el-input__inner,
        .satisfaction-dialog .el-textarea__inner {
            border-radius: 4px;
        }
        
        .satisfaction-dialog .el-radio-group {
            margin-top: 8px;
        }
        
        .satisfaction-dialog .el-radio {
            margin-right: 20px;
        }
        
        /* 廉政问卷弹框样式 */
        .integrity-dialog .el-dialog__header {
            background-color: #f5f7fa;
            border-bottom: 1px solid #e4e7ed;
        }
        
        .integrity-dialog .el-dialog__title {
            color: #409EFF;
            font-weight: 600;
            font-size: 18px;
        }
        
        .integrity-dialog .el-alert {
            margin-top: 15px;
        }
        
        .integrity-dialog .el-upload__tip {
            margin-top: 10px;
            color: #999;
            font-size: 12px;
        }
        
        .integrity-dialog .el-link {
            font-weight: 500;
        }
        
        .integrity-dialog .el-upload-dragger {
            width: 100%;
            height: 120px;
        }
        
        /* PDF预览弹框样式 */
        .pdf-preview-dialog .el-dialog__body {
            padding: 0;
        }
        .pdf-preview-dialog .el-dialog__header {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            background-color: #f5f7fa;
        }
        .pdf-preview-dialog .el-dialog__title {
            color: #409EFF;
            font-weight: 600;
            font-size: 18px;
        }
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">
        <el-form :inline="true" :model="searchFrom" class="form-inline" @submit.native.prevent>
            <el-form-item>
                <el-radio-group @change="statusChange" size="mini" v-model="searchFrom.status">
                    <el-radio-button label="">全部</el-radio-button>
                    <el-radio-button label="1">今日评审</el-radio-button>
                </el-radio-group>
            </el-form-item>
            <el-form-item>
                <el-input v-model="searchFrom.title" size="mini" placeholder="企业名称"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="getData()" size="mini">查询</el-button>
                <el-button @click="reset()" size="mini">重置</el-button>
            </el-form-item>
        </el-form>
        <el-table border
                  v-loading="loading"
                  :data="data"
                  style="width: 100%;margin-bottom: 20px;"
                  ref="qtable"
                  :height="height"
                  size="small">
            <el-table-column
                    type="index"
                    label="序号"
                    align="center"
                    width="50">
            </el-table-column>
            <el-table-column
                    prop="company_name"
                    label="企业名称"
                    align="center"
                    show-overflow-tooltip
                    min-width="200">
                <template slot-scope="scope">
                    <el-button type="text" @click="showCompanyInfo(scope.row)" class="company-link" v-html="scope.row.company_name"></el-button>
                </template>
            </el-table-column>
            <el-table-column
                    prop="level"
                    label="申请等级"
                    align="center"
                    width="100">
            </el-table-column>
            <el-table-column
                    prop="date"
                    label="评审日期"
                    align="center"
                    width="100">
            </el-table-column>
            <el-table-column
                    prop="mb_status"
                    label="任务状态"
                    align="center"
                    show-overflow-tooltip
                    width="120">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.status==1" type="info">待派发</el-tag>
                    <el-tag v-if="scope.row.status==7" type="primary">审核通过</el-tag>
                    <el-tag v-if="scope.row.status==5" type="danger">已驳回</el-tag>
                    <el-tag v-if="scope.row.status==8" type="">评审中</el-tag>
                    <el-tag v-if="scope.row.status==9" type="">评审结束</el-tag>
                </template>
            </el-table-column>
            <el-table-column
                    label="满意度调查"
                    align="center"
                    width="120">
                <template slot-scope="scope">
                    <el-button 
                        v-if="scope.row.satisfaction_survey_status == 1" 
                        size="small" 
                        type="success" 
                        @click="viewSatisfactionSurvey(scope.row)">
                        查看
                    </el-button>
                    <el-button 
                        v-else 
                        size="small" 
                        type="primary" 
                        @click="fillSatisfactionSurvey(scope.row)">
                        填报
                    </el-button>
                </template>
            </el-table-column>
            <el-table-column
                    label="廉政问卷"
                    align="center"
                    width="120">
                <template slot-scope="scope">
                    <el-button 
                        v-if="scope.row.integrity_survey_status == 1"
                        size="small" 
                        type="success" 
                        @click="viewIntegritySurvey(scope.row)">
                        查看
                    </el-button>
                    <el-button 
                        v-else 
                        size="small" 
                        type="primary" 
                        @click="fillIntegritySurvey(scope.row)">
                        填报
                    </el-button>
                </template>
            </el-table-column>
<!--            <el-table-column-->
<!--                    label="操作"-->
<!--                    align="center"-->
<!--                    width="150">-->
<!--                <template slot-scope="scope">-->
<!--                    <el-button size="small" @click="info(scope.row)">详情</el-button>-->
<!--                </template>-->
<!--            </el-table-column>-->
        </el-table>
    </div>
    <elementref ref="elementref" @ok="getData()"></elementref>
    
    <!-- 企业信息弹出框 -->
    <el-dialog
        title="企业信息"
        :visible.sync="companyDialogVisible"
        width="90%"
        :close-on-click-modal="false"
        :before-close="handleCloseCompanyDialog">
        <div v-loading="companyLoading" element-loading-text="正在加载企业信息...">
            <iframe 
                v-if="companyInfoUrl" 
                :src="companyInfoUrl" 
                style="width: 100%; height: 600px; border: none;"
                @load="handleIframeLoad"
                @error="handleIframeError"
                ref="companyIframe">
            </iframe>
            <div v-if="companyLoadError" style="text-align: center; padding: 50px; color: #999;">
                <p>加载失败，请稍后重试</p>
                <el-button type="primary" @click="reloadCompanyInfo">重新加载</el-button>
            </div>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="companyDialogVisible = false">关闭</el-button>
            <el-button type="primary" @click="openInNewWindow">在新窗口打开</el-button>
        </span>
    </el-dialog>

    <!-- 满意度调查弹出框 -->
    <el-dialog
        title="成都市企业安全生产标准化评审验收工作问卷调查表"
        :visible.sync="satisfactionDialogVisible"
        width="800px"
        :close-on-click-modal="false"
        :before-close="handleCloseSatisfactionDialog"
        custom-class="satisfaction-dialog">
        <div v-if="!satisfactionSuccess">
            <el-form label-position="top" :model="satisfactionForm" :rules="satisfactionRules" ref="satisfactionRuleForm">
                <el-form-item label="1.被评审企业名称" prop="company_name">
                    <el-input v-model="satisfactionForm.company_name"></el-input>
                </el-form-item>
                <el-form-item label="2.填表时间" prop="field2">
                    <el-input v-model="satisfactionForm.field2"></el-input>
                </el-form-item>
                <el-form-item label="3、填表人及联系电话（请留下联系方式，便于我们工作改进）。" prop="field3">
                    <el-input v-model="satisfactionForm.field3"></el-input>
                </el-form-item>
                <el-form-item label="4、现场评审人员是否存在违法廉洁纪律行为，如收受红包礼金、土特产、有价证券、帮忙联系办理私事等行为。" prop="field4">
                    <el-input v-model="satisfactionForm.field4"></el-input>
                </el-form-item>
                <el-form-item label="5、您对本次评审成绩是否满意？（满意/不满意）" prop="field5">
                    <el-radio-group v-model="satisfactionForm.field5">
                        <el-radio label="满意">满意</el-radio>
                        <el-radio label="不满意">不满意</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="6、您对本次评审人员的评审工作是否满意？（满意/不满意）。" prop="field6">
                    <el-radio-group v-model="satisfactionForm.field6">
                        <el-radio label="满意">满意</el-radio>
                        <el-radio label="不满意">不满意</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="7、您的意见与建议。" prop="field7">
                    <el-input type="textarea" :rows="4" v-model="satisfactionForm.field7"></el-input>
                </el-form-item>
            </el-form>
        </div>
        <div v-else>
            <el-result icon="success" title="提交成功" sub-title="感谢您的参与">
            </el-result>
        </div>
        <span slot="footer" class="dialog-footer" v-if="!satisfactionSuccess">
            <el-button @click="handleCloseSatisfactionDialog(() => { satisfactionDialogVisible = false; })">取消</el-button>
            <el-button type="primary" @click="submitSatisfactionSurvey" :loading="satisfactionSubmitting">提交</el-button>
        </span>
        <span slot="footer" class="dialog-footer" v-else>
            <el-button type="primary" @click="satisfactionDialogVisible = false">确定</el-button>
        </span>
    </el-dialog>

    <!-- 廉政问卷弹出框 -->
    <el-dialog
        title="廉政问卷"
        :visible.sync="integrityDialogVisible"
        width="600px"
        :close-on-click-modal="false"
        :before-close="handleCloseIntegrityDialog"
        custom-class="integrity-dialog">
        <div v-if="!integritySuccess">
            <div style="padding: 20px;">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <div style="text-align: center;">
                            <el-link 
                                href="/general/toppingsoft/public/template/廉政问卷模板.doc" 
                                target="_blank"
                                style="color:#409EFF;font-size:16px;margin-bottom:20px;display:block;">
                                <i class="el-icon-download"></i> 廉政问卷模板下载
                            </el-link>
                            <el-alert
                                title="请先下载模板，填写完成后上传"
                                type="info"
                                :closable="false"
                                show-icon>
                            </el-alert>
                        </div>
                    </el-col>
                    <el-col :span="12">
                        <div style="text-align: center;">
                            <p style="margin-bottom: 15px;font-weight: 600;color: #606266;">上传填写好的廉政问卷</p>
                            <el-upload
                                action="/general/toppingsoft/index.php/company/task/uploadIntegrity/model/org"
                                :file-list="integrityFileList"
                                :limit="1"
                                :on-success="handleIntegrityUploadSuccess"
                                :before-upload="handleIntegrityUploadBefore"
                                :on-remove="handleIntegrityRemove"
                                :on-preview="handleIntegrityPreview"
                                accept=".doc,.docx,.pdf">
                                <el-button size="small" type="primary">
                                    <i class="el-icon-upload"></i> 选择文件
                                </el-button>
                                <div slot="tip" class="el-upload__tip">
                                    只能上传doc/docx/pdf文件，且不超过10MB
                                </div>
                            </el-upload>
                        </div>
                    </el-col>
                </el-row>
            </div>
        </div>
        <div v-else>
            <el-result icon="success" title="上传成功" sub-title="廉政问卷已成功提交">
            </el-result>
        </div>
        <span slot="footer" class="dialog-footer" v-if="!integritySuccess">
            <el-button @click="integrityDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="submitIntegrity" :disabled="integrityFileList.length === 0">确定提交</el-button>
        </span>
        <span slot="footer" class="dialog-footer" v-else>
            <el-button type="primary" @click="integrityDialogVisible = false">确定</el-button>
        </span>
    </el-dialog>

    <!-- 查看满意度调查弹出框 -->
    <el-dialog
        title="查看满意度调查"
        :visible.sync="viewSatisfactionDialogVisible"
        width="800px"
        :close-on-click-modal="false">
        <div v-loading="viewSatisfactionLoading">
            <div style="padding: 20px;">
                <el-form label-width="200px">
                    <el-form-item label="被评审企业名称：">
                        <el-input v-model="viewSatisfactionData.company_name" readonly></el-input>
                    </el-form-item>
                    <el-form-item label="填表时间：">
                        <el-input v-model="viewSatisfactionData.field2" readonly></el-input>
                    </el-form-item>
                    <el-form-item label="填表人及联系电话：">
                        <el-input v-model="viewSatisfactionData.field3" readonly></el-input>
                    </el-form-item>
                    <el-form-item label="现场评审人员是否存在违法廉洁纪律行为：">
                        <el-input v-model="viewSatisfactionData.field4" readonly type="textarea" :rows="3"></el-input>
                    </el-form-item>
                    <el-form-item label="您对本次评审成绩是否满意：">
                        <el-input v-model="viewSatisfactionData.field5" readonly></el-input>
                    </el-form-item>
                    <el-form-item label="您对本次评审人员的评审工作是否满意：">
                        <el-input v-model="viewSatisfactionData.field6" readonly></el-input>
                    </el-form-item>
                    <el-form-item label="您的意见与建议：">
                        <el-input v-model="viewSatisfactionData.field7" readonly type="textarea" :rows="3"></el-input>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="viewSatisfactionDialogVisible = false">确定</el-button>
        </span>
         </el-dialog>

    <!-- 查看廉政问卷文件弹出框 -->
    <el-dialog
        title="查看廉政问卷"
        :visible.sync="viewIntegrityDialogVisible"
        width="500px"
        :close-on-click-modal="false">
        <div v-loading="viewIntegrityLoading">
            <div style="padding: 20px; text-align: center;">
                <div style="margin-bottom: 20px;">
                    <i class="el-icon-document" style="font-size: 64px; color: #409EFF;"></i>
                </div>
                <div style="margin-bottom: 15px;">
                    <strong>文件名：</strong>{{ integrityFileInfo.file_name }}
                </div>
                                 <div style="margin-bottom: 15px;">
                     <strong>文件类型：</strong>{{ integrityFileInfo.file_ext ? integrityFileInfo.file_ext.toUpperCase() : '' }}
                 </div>
                <div style="margin-bottom: 20px;">
                    <strong>文件大小：</strong>{{ integrityFileInfo.file_size }}
                </div>
                <el-alert
                    title="该文件不支持在线预览，请下载后查看"
                    type="info"
                    :closable="false"
                    show-icon>
                </el-alert>
            </div>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="viewIntegrityDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="downloadFile">
                <i class="el-icon-download"></i> 下载文件
            </el-button>
        </span>
    </el-dialog>

    <!-- PDF预览弹出框 -->
    <el-dialog 
        :title="pdfPreviewTitle" 
        :visible.sync="pdfPreviewVisible" 
        width="90%" 
        top="10px" 
        append-to-body 
        :before-close="closePdfPreview"
        custom-class="pdf-preview-dialog">
        <iframe 
            :src="pdfPreviewUrl" 
            width="100%" 
            :height="pdfPreviewHeight" 
            border="0" 
            frameborder="0" 
            framespacing="0" 
            marginheight="0" 
            marginwidth="0"
            style="border: none;">
        </iframe>
    </el-dialog>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                title: '新增',
                searchFrom: {
                    status: 1,
                    title: '',
                },
                data: [],
                visible: false,
                loading: false,
                height: document.documentElement.clientHeight - 155,
                companyDialogVisible: false,
                companyLoading: true,
                companyInfoUrl: '',
                companyLoadError: false,
                // 满意度调查相关数据
                satisfactionDialogVisible: false,
                satisfactionSuccess: false,
                satisfactionSubmitting: false,
                satisfactionForm: {
                    task_id: '',
                    company_id: '',
                    company_name: '',
                    field2: '',
                    field3: '',
                    field4: '',
                    field5: '',
                    field6: '',
                    field7: '',
                },
                // 查看满意度调查相关数据
                viewSatisfactionDialogVisible: false,
                viewSatisfactionLoading: false,
                viewSatisfactionData: {
                    company_name: '',
                    field2: '',
                    field3: '',
                    field4: '',
                    field5: '',
                    field6: '',
                    field7: '',
                    time: ''
                },
                satisfactionRules: {
                    company_name: [
                        { required: true, message: '请输入企业名称', trigger: 'blur' },
                    ],
                    field2: [
                        { required: true, message: '请输入填表时间', trigger: 'blur' }
                    ],
                    field4: [
                        { required: true, message: '请输入相关内容', trigger: 'blur' }
                    ],
                    field5: [
                        { required: true, message: '请选择是否满意', trigger: 'blur' }
                    ],
                    field6: [
                        { required: true, message: '请选择是否满意', trigger: 'blur' }
                    ],
                    field7: [
                        { required: true, message: '请输入意见与建议', trigger: 'blur' }
                    ],
                },
                // 廉政问卷相关数据
                integrityDialogVisible: false,
                integritySuccess: false,
                integrityFileList: [],
                currentTaskId: '',
                // 查看廉政问卷相关数据
                viewIntegrityDialogVisible: false,
                viewIntegrityLoading: false,
                integrityFileInfo: {
                    file_name: '',
                    file_ext: '',
                    file_url: '',
                    is_pdf: false,
                    file_size: ''
                },
                // PDF预览相关数据
                pdfPreviewVisible: false,
                pdfPreviewUrl: '',
                pdfPreviewTitle: '',
                pdfPreviewHeight: document.documentElement.clientHeight - 180,
            };
        },
        components: {
            'elementref': 'url:/general/toppingsoft/app/company/view/index/vue/info.vue?v=1',
        },
        watch: {
            $route() {
                //切换标签时
                this.handleRefreshTable();
            }
        },
        created() {
            let _this = this;
            //窗口改变时
            window.onresize = function () {
                _this.handleRefreshTable();
                _this.pdfPreviewHeight = document.documentElement.clientHeight - 180;
            };
        },
        updated() {
            //数据改变时
            this.handleRefreshTable();
        },
        methods: {
            selectHandle(row,index) {
                console.log(row)
                return row.status==1||row.status==0;
            },
            statusChange() {
                this.page = 1;
                this.getData();
            },
            //数据初始化
            reset() {
                this.searchFrom.title='';
                this.page = 1;
                this.pageSize = 20;
                this.getData();
            },
            //数据加载
            getData() {
                var _this = this;
                var param = _this.searchFrom;
                param._ajax = 1;
                _this.loading = true;
                axios.post('', param).then(function (res) {
                    if (res.data.code == 0) {
                        _this.data = res.data.data.data;
                        _this.$nextTick(() => {
                            // 这里的代码会在DOM更新完成后执行
                            _this.loading = false;
                        });
                        _this.handleRefreshTable();
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            check(row,status){
                var _this = this;
                var param = {};
                param.id = row.id;
                param.status = status;
                if(status==7){
                    _this.$confirm('确定接收评审任务，接收后请按时到达现场参与评审！', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        _this.loading = true;
                        axios.post('check', param).then(function (res) {
                            _this.loading = false;
                            if (res.data.code == 0) {
                                _this.$message({
                                    message: res.data.msg,
                                    type: "success"
                                });
                                _this.getData();
                            } else {
                                _this.$message({
                                    message: res.data.msg,
                                    type: "error"
                                });
                            }
                        }).catch(function (error) {
                            console.log(error);
                        });
                    });
                }else{
                    this.$prompt('请填写请假原因', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        inputPattern: /^\S+$/,
                        inputErrorMessage: '请填写请假原因'
                    }).then(({value}) => {
                        _this.loading = true;
                        param.reason = value;
                        axios.post('check', param).then(function (res) {
                            _this.loading = false;
                            if (res.data.code == 0) {
                                _this.$message({
                                    message: res.data.msg,
                                    type: "success"
                                });
                                _this.getData();
                            } else {
                                _this.$message({
                                    message: res.data.msg,
                                    type: "error"
                                });
                            }
                        }).catch(function (error) {
                            console.log(error);
                        });
                    }).catch(function (error){
                        console.log(error);
                    });
                }
            },
            elementSet(row) {
                this.$refs.elementref.title="要素分配";
                this.$refs.elementref.open(row);
            },
            review(row){
                location.href = 'review?id='+row.id;
            },
            info(row){
                location.href = 'info?id='+row.id;
            },
            retract(row){
                var _this = this;
                this.$prompt('放弃评审半年内不可申请评审，确认请在下方输入框输入“放弃评审”', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    inputPattern: /^放弃评审$/,
                    inputErrorMessage: '输入有误'
                }).then(({value}) => {
                    _this.loading = true;
                    var param = {};
                    param.id = row.id;
                    axios.post('retract', param).then(function (res) {
                        _this.loading = false;
                        if (res.data.code == 0) {
                            _this.$message({
                                message: res.data.msg,
                                type: "success"
                            });
                            _this.getData();
                        } else {
                            _this.$message({
                                message: res.data.msg,
                                type: "error"
                            });
                        }
                    }).catch(function (error) {
                        console.log(error);
                    });
                }).catch(function (error){
                    console.log(error);
                });
            },
            handleRefreshTable: function () {
                this.$nextTick(function () {
                    var refList = this.$refs;
                    if (refList) {
                        for (var i of Object.keys(refList)) {
                            if (refList[i] && refList[i].doLayout) {
                                refList[i].doLayout();
                            }
                        }
                    }
                });
            },
            showCompanyInfo(row) {
                var tmp = row;
                tmp.id = row.company_id;
                this.$refs.elementref.open(tmp);
            },
            handleCloseCompanyDialog() {
                this.companyDialogVisible = false;
                this.companyInfoUrl = '';
                this.companyLoadError = false;
            },
            openInNewWindow() {
                window.open(this.companyInfoUrl, '_blank');
            },
            handleIframeLoad() {
                this.companyLoading = false;
                this.companyLoadError = false;
            },
            handleIframeError() {
                this.companyLoading = false;
                this.companyLoadError = true;
            },
            reloadCompanyInfo() {
                this.companyLoading = true;
                this.companyLoadError = false;
                // 重新设置iframe的src来重新加载
                this.$nextTick(() => {
                    if (this.$refs.companyIframe) {
                        this.$refs.companyIframe.src = this.companyInfoUrl;
                    }
                });
            },
            // 查看满意度调查
            viewSatisfactionSurvey(row) {
                var _this = this;
                _this.viewSatisfactionLoading = true;
                _this.viewSatisfactionDialogVisible = true;
                
                // 调用后端接口获取满意度调查数据
                axios.post('/general/toppingsoft/index.php/company/task/viewSatisfactionSurvey', {
                    task_id: row.id
                }).then(function (res) {
                    _this.viewSatisfactionLoading = false;
                    if (res.data.code == 0) {
                        _this.viewSatisfactionData = res.data.data;
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        _this.viewSatisfactionDialogVisible = false;
                    }
                }).catch(function (error) {
                    _this.viewSatisfactionLoading = false;
                    _this.$message({
                        message: "加载失败，请重试",
                        type: "error"
                    });
                    _this.viewSatisfactionDialogVisible = false;
                    console.log("出现错误:", error);
                });
            },
            // 填报满意度调查
            fillSatisfactionSurvey(row) {
                // 重置表单数据
                this.resetSatisfactionForm();
                // 设置任务和企业信息
                this.satisfactionForm.task_id = row.id;
                this.satisfactionForm.company_id = row.company_id;
                this.satisfactionForm.company_name = row.company_name;
                // 设置当前日期
                this.satisfactionForm.field2 = this.getCurrentDate();
                // 显示弹框
                this.satisfactionDialogVisible = true;
            },
            // 重置满意度调查表单
            resetSatisfactionForm() {
                this.satisfactionForm = {
                    task_id: '',
                    company_id: '',
                    company_name: '',
                    field2: '',
                    field3: '',
                    field4: '',
                    field5: '',
                    field6: '',
                    field7: '',
                };
                this.satisfactionSuccess = false;
                this.satisfactionSubmitting = false;
                // 清除验证
                this.$nextTick(() => {
                    if (this.$refs.satisfactionRuleForm) {
                        this.$refs.satisfactionRuleForm.clearValidate();
                    }
                });
            },
            // 获取当前日期
            getCurrentDate() {
                const now = new Date();
                const year = now.getFullYear();
                const month = String(now.getMonth() + 1).padStart(2, '0');
                const day = String(now.getDate()).padStart(2, '0');
                return `${year}-${month}-${day}`;
            },
            // 关闭满意度调查弹框
            handleCloseSatisfactionDialog(done) {
                // 如果已经提交成功，直接关闭
                if (this.satisfactionSuccess) {
                    done();
                    this.resetSatisfactionForm();
                    return;
                }
                
                // 检查是否有填写内容
                const hasContent = this.satisfactionForm.field2 || 
                                 this.satisfactionForm.field3 || 
                                 this.satisfactionForm.field4 || 
                                 this.satisfactionForm.field5 || 
                                 this.satisfactionForm.field6 || 
                                 this.satisfactionForm.field7;
                
                if (hasContent) {
                    this.$confirm('关闭后将丢失已填写的内容，确认关闭吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        done();
                        this.resetSatisfactionForm();
                    }).catch(() => {
                        // 用户取消关闭
                    });
                } else {
                    done();
                    this.resetSatisfactionForm();
                }
            },
            // 提交满意度调查
            submitSatisfactionSurvey() {
                var _this = this;
                _this.$refs['satisfactionRuleForm'].validate((valid) => {
                    if (valid) {
                        _this.satisfactionSubmitting = true;
                        axios.post('/general/toppingsoft/index.php/exam/mobile/mydSave', _this.satisfactionForm).then(function (res) {
                            _this.satisfactionSubmitting = false;
                            if (res.data.code == 0) {
                                _this.satisfactionSuccess = true;
                                // 刷新列表数据
                                _this.getData();
                            } else {
                                _this.$message({
                                    message: res.data.msg,
                                    type: "error"
                                });
                            }
                        }).catch(function (error) {
                            _this.satisfactionSubmitting = false;
                            _this.$message({
                                message: "提交失败，请重试",
                                type: "error"
                            });
                            console.log("出现错误:", error);
                        });
                    } else {
                        return false;
                    }
                });
            },
            // 查看廉政问卷
            viewIntegritySurvey(row) {
                var _this = this;
                _this.viewIntegrityLoading = true;
                _this.viewIntegrityDialogVisible = true;
                
                // 调用后端接口获取廉政问卷文件信息
                axios.post('/general/toppingsoft/index.php/company/task/viewIntegritySurvey', {
                    task_id: row.id
                }).then(function (res) {
                    _this.viewIntegrityLoading = false;
                    if (res.data.code == 0) {
                        _this.integrityFileInfo = res.data.data;
                        
                        // 如果是PDF文件，直接预览
                        if (_this.integrityFileInfo.is_pdf) {
                            _this.viewIntegrityDialogVisible = false;
                            _this.previewPdf(_this.integrityFileInfo.file_url, _this.integrityFileInfo.file_name);
                        }
                        // 如果不是PDF文件，显示下载选项
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        _this.viewIntegrityDialogVisible = false;
                    }
                }).catch(function (error) {
                    _this.viewIntegrityLoading = false;
                    _this.$message({
                        message: "加载失败，请重试",
                        type: "error"
                    });
                    _this.viewIntegrityDialogVisible = false;
                    console.log("出现错误:", error);
                });
            },
            // PDF预览功能
            previewPdf(url, title) {
                this.pdfPreviewUrl = url;
                this.pdfPreviewTitle = title || '廉政问卷预览';
                this.pdfPreviewVisible = true;
            },
            // 下载文件
            downloadFile() {
                if (this.integrityFileInfo.file_url) {
                    // 创建一个隐藏的下载链接
                    const link = document.createElement('a');
                    link.href = this.integrityFileInfo.file_url;
                    link.download = this.integrityFileInfo.file_name;
                    link.target = '_blank';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    
                    this.viewIntegrityDialogVisible = false;
                    this.$message.success('开始下载文件');
                }
            },
            // 关闭PDF预览
            closePdfPreview() {
                this.pdfPreviewVisible = false;
                this.pdfPreviewUrl = '';
                this.pdfPreviewTitle = '';
            },
            // 填报廉政问卷
            fillIntegritySurvey(row) {
                // 重置状态
                this.integritySuccess = false;
                this.integrityFileList = [];
                this.currentTaskId = row.id;
                // 显示弹框
                this.integrityDialogVisible = true;
            },
            // 关闭廉政问卷弹框
            handleCloseIntegrityDialog(done) {
                // 如果已经提交成功，直接关闭
                if (this.integritySuccess) {
                    done();
                    return;
                }
                
                // 如果有上传的文件，提示确认
                if (this.integrityFileList.length > 0) {
                    this.$confirm('关闭后将丢失已上传的文件，确认关闭吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.integrityFileList = [];
                        done();
                    }).catch(() => {
                        // 用户取消关闭
                    });
                } else {
                    done();
                }
            },
            // 文件上传前的处理
            handleIntegrityUploadBefore(file) {
                const isDoc = file.type === 'application/msword' || 
                            file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
                            file.type === 'application/pdf';
                const isLt10M = file.size / 1024 / 1024 < 10;

                if (!isDoc) {
                    this.$message.error('只能上传 DOC/DOCX/PDF 格式的文件!');
                    return false;
                }
                if (!isLt10M) {
                    this.$message.error('上传文件大小不能超过 10MB!');
                    return false;
                }
                return true;
            },
            // 文件上传成功处理
            handleIntegrityUploadSuccess(response, file, fileList) {
                if (response.code === 0) {
                    this.$message.success('文件上传成功！');
                    // 保存文件信息到file对象中
                    file.fileInfo = response.data;
                    // 更新文件列表
                    this.integrityFileList = fileList;
                } else {
                    this.$message.error(response.msg || '上传失败');
                    // 从列表中移除失败的文件
                    const index = fileList.indexOf(file);
                    if (index > -1) {
                        fileList.splice(index, 1);
                    }
                }
            },
            // 文件移除处理
            handleIntegrityRemove(file, fileList) {
                this.integrityFileList = fileList;
            },
            // 文件预览
            handleIntegrityPreview(file) {
                if (file.url) {
                    window.open(file.url);
                } else {
                    this.$message.info('文件预览功能暂不支持该文件类型');
                }
            },
            // 提交廉政问卷
            submitIntegrity() {
                if (this.integrityFileList.length === 0) {
                    this.$message.warning('请先上传廉政问卷文件！');
                    return;
                }
                
                this.$confirm('确认提交廉政问卷吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    // 调用提交接口
                    const param = {
                        task_id: this.currentTaskId,
                        file_info: this.integrityFileList[0].fileInfo || {}
                    };
                    
                    axios.post('/general/toppingsoft/index.php/company/task/submitIntegrity', param)
                        .then((res) => {
                            if (res.data.code === 0) {
                                this.integritySuccess = true;
                                this.$message.success('廉政问卷提交成功！');
                                // 刷新列表数据
                                this.getData();
                            } else {
                                this.$message.error(res.data.msg || '提交失败');
                            }
                        }).catch((error) => {
                            this.$message.error('提交失败，请重试');
                            console.log('提交错误:', error);
                        });
                }).catch(() => {
                    // 用户取消提交
                });
            }
        },
        mounted() {
            //获取列表
            this.getData();

        }
    })
</script>


</body>
</html>