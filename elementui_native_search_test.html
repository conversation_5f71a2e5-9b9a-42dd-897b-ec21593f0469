<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Element UI 原生搜索测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .test-section { margin-bottom: 30px; padding: 20px; background: #f9f9f9; border-radius: 6px; }
        .debug-info { background: #e8f4fd; padding: 10px; border-radius: 4px; margin-top: 10px; font-size: 12px; }
    </style>
</head>
<body>
    <div id="app" class="test-container">
        <h1>Element UI 原生搜索功能测试</h1>
        
        <div class="test-section">
            <h3>使用 filterable + 搜索标签</h3>
            <p>这是最简单有效的方法，利用 Element UI 的原生搜索能力</p>
            
            <el-select 
                v-model="selectedTable" 
                placeholder="请选择数据表（支持搜索表名和备注）" 
                style="width: 400px;"
                filterable
                clearable>
                <el-option
                    v-for="table in tableList"
                    :key="table.table_name"
                    :label="getTableSearchLabel(table)"
                    :value="table.table_name">
                    <span style="float: left; font-weight: bold;">{{ table.table_name }}</span>
                    <span style="float: right; color: #8492a6; font-size: 12px" v-if="table.table_comment">{{ table.table_comment }}</span>
                </el-option>
            </el-select>
            
            <div class="debug-info">
                <strong>工作原理：</strong><br>
                1. 使用 <code>filterable</code> 属性启用 Element UI 原生搜索<br>
                2. 通过 <code>:label="getTableSearchLabel(table)"</code> 设置搜索标签<br>
                3. 搜索标签包含表名和备注，Element UI 会在这个字符串中进行模糊搜索<br>
                4. 用户输入关键词时，Element UI 自动过滤匹配的选项
            </div>
        </div>

        <div class="test-section">
            <h3>测试数据</h3>
            <p>当前选中: <strong>{{ selectedTable || '未选择' }}</strong></p>
            
            <div class="debug-info">
                <strong>测试说明：</strong><br>
                • 输入 "user" 应该显示用户相关的表<br>
                • 输入 "信息" 应该显示备注中包含"信息"的表<br>
                • 输入 "product" 应该显示产品相关的表<br>
                • 输入 "配置" 应该显示配置相关的表
            </div>
        </div>

        <div class="test-section">
            <h3>搜索标签测试</h3>
            <el-button @click="showSearchLabels" type="primary">显示所有搜索标签</el-button>
            
            <div v-if="searchLabels.length > 0" class="debug-info">
                <strong>搜索标签列表：</strong><br>
                <div v-for="(label, index) in searchLabels" :key="index" style="margin: 5px 0;">
                    {{ index + 1 }}. "{{ label }}"
                </div>
            </div>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    selectedTable: '',
                    searchLabels: [],
                    tableList: [
                        { 
                            table_name: 'user_info', 
                            display_name: 'user_info (用户信息表)', 
                            table_comment: '用户信息表' 
                        },
                        { 
                            table_name: 'user_role', 
                            display_name: 'user_role (用户角色表)', 
                            table_comment: '用户角色表' 
                        },
                        { 
                            table_name: 'product_info', 
                            display_name: 'product_info (产品信息表)', 
                            table_comment: '产品信息表' 
                        },
                        { 
                            table_name: 'order_detail', 
                            display_name: 'order_detail (订单详情表)', 
                            table_comment: '订单详情表' 
                        },
                        { 
                            table_name: 'system_config', 
                            display_name: 'system_config (系统配置表)', 
                            table_comment: '系统配置表' 
                        },
                        { 
                            table_name: 'log_operation', 
                            display_name: 'log_operation (操作日志表)', 
                            table_comment: '系统操作日志记录表' 
                        },
                        { 
                            table_name: 'config_params', 
                            display_name: 'config_params (配置参数表)', 
                            table_comment: '系统配置参数表' 
                        }
                    ]
                }
            },
            methods: {
                // 获取表的搜索标签（包含表名和备注，用于Element UI的默认过滤）
                getTableSearchLabel(table) {
                    // 将表名和备注组合成一个字符串，Element UI会在这个字符串中搜索
                    const tableName = table.table_name || '';
                    const tableComment = table.table_comment || '';
                    const displayName = table.display_name || '';
                    
                    // 返回包含表名和备注的字符串，用空格分隔，Element UI会在这个字符串中进行模糊搜索
                    return `${tableName} ${tableComment} ${displayName}`;
                },

                showSearchLabels() {
                    this.searchLabels = this.tableList.map(table => this.getTableSearchLabel(table));
                }
            }
        });
    </script>
</body>
</html>
