<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>评审任务</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/expert.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .el-badge__content.is-fixed {top:3px;}
        .my-content p{ line-height:20px; margin: 10px 0;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">
        <el-form :inline="true" :model="searchFrom" class="form-inline" @submit.native.prevent>
        </el-form>
        <el-table border
                  v-loading="loading"
                  :data="data"
                  style="width: 100%;margin-bottom: 20px;"
                  ref="qtable"
                  :height="height"
                  size="small">
            <el-table-column
                    type="index"
                    label="序号"
                    align="center"
                    width="50">
            </el-table-column>
            <el-table-column
                    prop="company_name"
                    label="企业名称"
                    align="center"
                    show-overflow-tooltip
                    min-width="200">
                <template slot-scope="scope">
                    <!--<el-link :href="'/general/toppingsoft/index.php/expert/company/info?id='+scope.row.company_id" target="_blank" type="primary" v-html="scope.row.company_name"></el-link>-->
                    <el-link @click="company_info(scope.row)" type="primary" v-html="scope.row.company_name"></el-link>
                </template>
            </el-table-column>
            <el-table-column
                    prop="level"
                    label="申请等级"
                    align="center"
                    width="100">
            </el-table-column>
            <el-table-column
                    prop="date"
                    label="评审日期"
                    align="center"
                    width="100">
            </el-table-column>
            <el-table-column
                    prop="position"
                    label="职务"
                    align="center"
                    width="100">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.position_id==2" type="info">组员</el-tag>
                    <el-tag v-if="scope.row.position_id==1" type="primary">组长</el-tag>
                </template>
            </el-table-column>
            <el-table-column
                    prop="mb_status"
                    label="计划状态"
                    align="center"
                    show-overflow-tooltip
                    width="120">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.status==1" type="info">待审核</el-tag>
                    <el-tag v-if="scope.row.status==7" type="primary">审核通过</el-tag>
                    <el-tag v-if="scope.row.status==5" type="danger">已驳回</el-tag>
                    <el-tag v-if="scope.row.status==8" type="">评审中</el-tag>
                    <el-tag v-if="scope.row.status==9" type="">评审结束</el-tag>
                </template>
            </el-table-column>
            <el-table-column
                    prop="mb_status"
                    label="接收状态"
                    align="center"
                    show-overflow-tooltip
                    width="120">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.estatus==1" type="info">待接收</el-tag>
                    <el-tag v-if="scope.row.estatus==7" type="primary">已接收</el-tag>
                    <el-tag v-if="scope.row.estatus==6" type="danger">已请假</el-tag>
                    <el-tag v-if="scope.row.estatus==5" type="danger">拒绝接收</el-tag>
                </template>
            </el-table-column>
            <el-table-column
                    prop="element_name"
                    label="在线讨论"
                    align="center"
                    show-overflow-tooltip
                    min-width="120">
                <template slot-scope="scope">
                    <el-badge :is-dot="scope.row.discuss.readsum>0"  class="item" v-if="scope.row.discuss.id>0">
                        <el-button size="small" @click="discuss(scope.row)">查看</el-button>
                    </el-badge>
                    <el-button v-if="scope.row.position_id==1&&scope.row.discuss.id<=0" type="primary" size="small" @click="discussAdd(scope.row)">创建群聊</el-button>
                </template>
            </el-table-column>
            <el-table-column
                    prop="experts"
                    label="评审专家"
                    align="center"
                    min-width="250">
                <template slot-scope="scope">
                    <span v-if="scope.row.isZhuzhang">
                        <span v-for="expert in scope.row.experts" style="color:#409EFF" >
                            <a href="javascript:void(0);" @click="showExpertDetail(expert.expert_id)" style="color:#089BAB">{{expert.expert_name}}</a>,
                        </span>
                    </span>
                    <span v-else>
                        <span v-for="expert in scope.row.experts">
                            {{expert.expert_name}},
                        </span>
                    </span>
                </template>
            </el-table-column>
            <el-table-column
                    label="操作"
                    align="center"
                    width="300">
                <template slot-scope="scope">
                    <!--
                    <el-button v-if="scope.row.estatus==1" size="small" type="primary" @click="check(scope.row,7)">接收</el-button>
                    <el-button v-if="scope.row.estatus==1 || scope.row.status<7" size="small" type="danger" @click="showLeaveRequestDialog(scope.row)">请假</el-button>
                    <el-button v-if="(scope.row.status==7||scope.row.status==8)&&scope.row.estatus==7" size="small" @click="info(scope.row,5)">详情</el-button>
                    -->
                    <el-button v-if="scope.row.position_id==1&&scope.row.status<9" size="small" type="primary" @click="elementSet(scope.row,5)">要素分配</el-button>
                    <el-button v-if="(scope.row.status==7||scope.row.status==8)&&scope.row.estatus==7" size="small" type="primary" @click="review(scope.row,5)">评审</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
    <el-dialog title="请假申请" :visible.sync="dialogVisible" width="40%">
        <el-select v-model="selectedReason" placeholder="请选择请假原因" style="width:100%;margin-bottom:15px">
            <el-option v-for="item in reason" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-input
            type="textarea"
            :rows="3"
            placeholder="请输入补充说明"
            v-model="expertLeaveRemark">
        </el-input>
        <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="submitLeaveRequest">确 定</el-button>
        </span>
    </el-dialog>
    <!---专家个人信息开始--->
    
    <el-dialog title="专家信息" :visible.sync="expertDialogVisible" width="50%">
        <el-descriptions v-if="expertDetail" :column="2" border>
            <el-descriptions-item label="姓名">{{ expertDetail.name }}</el-descriptions-item>
            <el-descriptions-item label="性别">{{ expertDetail.gender == '1' ? '男' : (expertDetail.gender == '2' ? '女' : '-') }}</el-descriptions-item>
            <el-descriptions-item label="年龄">{{ expertDetail.age }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{ expertDetail.phone }}</el-descriptions-item>
            <el-descriptions-item label="学历">{{ expertDetail.education }}</el-descriptions-item>
            <el-descriptions-item label="学位">{{ expertDetail.academicDegree }}</el-descriptions-item>
            <el-descriptions-item label="专业">{{ expertDetail.specializedSubject }}</el-descriptions-item>
            <el-descriptions-item label="工作单位">{{ expertDetail.companyName }}</el-descriptions-item>
            <el-descriptions-item label="职称">{{ expertDetail.professionalTitle }}</el-descriptions-item>
            <el-descriptions-item label="工作年限">{{ expertDetail.workingYears }}</el-descriptions-item>
            <el-descriptions-item label="银行卡号">{{ expertDetail.bankCardNumber }}</el-descriptions-item>
            <el-descriptions-item label="开户行">{{ expertDetail.bankOfDeposit }}</el-descriptions-item>
            <el-descriptions-item label="请假原因">{{ expertDetail.secsInsertReason }}</el-descriptions-item>
            <el-descriptions-item label="专家状态">{{ expertDetail.secsExpertStatus }}</el-descriptions-item>
        </el-descriptions>
        <el-divider>回避单位</el-divider>
        <el-table v-if="expertDetail && expertDetail.expertAvoidList && expertDetail.expertAvoidList.length" :data="expertDetail.expertAvoidList" size="mini" border>
            <el-table-column prop="avoidedCompanyName" label="单位名称"></el-table-column>
            <el-table-column prop="avoidedCompanyCreditCode" label="信用代码"></el-table-column>
        </el-table>
        <span slot="footer" class="dialog-footer">
            <el-button @click="expertDialogVisible = false">关闭</el-button>
        </span>
    </el-dialog>
    <!---专家个人信息结束--->
    <elementref ref="elementref" @ok="getData()"></elementref>

    <company_info ref="company_info" @ok="getData()"></company_info>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                title: '新增',
                searchFrom: {},
                data: [],
                visible: false,
                loading: false,
                reason:[],
                dialogVisible: false,
                selectedReason: '',
                expertLeaveRemark: '',
                selectRow:[],
                height: document.documentElement.clientHeight - 155,
                expertDialogVisible: false,
                expertDetail: null,
            };
        },
        components: {
            'elementref': 'url:/general/toppingsoft/app/expert/view/task/vue/element.vue?v=1',
            'company_info': 'url:/general/toppingsoft/app/expert/view/company/vue/info.vue?v=1',
        },
        watch: {
            $route() {
                //切换标签时
                this.handleRefreshTable();
            }
        },
        created() {
            let _this = this;
            //窗口改变时
            window.onresize = function () {
                _this.handleRefreshTable();
            };
        },
        updated() {
            //数据改变时
            this.handleRefreshTable();
        },
        methods: {
            //显示专家信息
            showExpertDetail(expertId){
                var _this = this;
                _this.loading = true;
                axios.post('getExpertInfo', {id: expertId}).then(function(res) {
                    _this.loading = false;
                    if(res.data.code === 0){
                        _this.expertDetail = res.data.data;
                        _this.expertDialogVisible = true;
                    }else{
                        _this.$message({ message: res.data.msg, type: "error" });
                    }
                }).catch(function(error){
                    _this.loading = false;
                    _this.$message({ message: "获取专家信息失败", type: "error" });
                });
            },
            //显示请假原因对话框
            showLeaveRequestDialog(row) {
                this.selectedRow = row;
                this.dialogVisible = true;
                this.selectedReason = '';
            },
            //提交请假申请
            submitLeaveRequest() {
                var row = this.selectedRow;
                this.dialogVisible = false;
                var _this = this;
                var param = {task_id: row.id,task_expert_id:row.task_expert_id, reason: this.selectedReason, expertLeaveRemark: this.expertLeaveRemark};
                _this.loading = true;
                axios.post('submitLeave', param).then(function(res) {
                    _this.loading = false;
                    if (res.data.code == 0) {
                        _this.$message({ message: res.data.msg, type: "success" });
                        _this.getData();
                    } else {
                        _this.$message({ message: res.data.msg, type: "error" });
                    }
                }).catch(function(error) {
                    console.log(error);
                });
            },
            //获取专家系统的请假原因
            getReason() {
                var _this = this;
                axios.post('getReason', {}).then(function (res) {
                    if (res.data.code == 0) {
                        _this.reason = res.data.data.list; 
                    }  
                }) 
            },
            //数据加载
            getData() {
                var _this = this;
                var param = _this.searchFrom;
                param._ajax = 1;
                _this.loading = true;
                axios.post('', param).then(function (res) {
                    if (res.data.code == 0) {
                        _this.data = res.data.data.data;
                        _this.$nextTick(() => {
                            // 这里的代码会在DOM更新完成后执行
                            _this.loading = false;
                        });
                        _this.handleRefreshTable();
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            check(row,status){
                var _this = this;
                var param = {};
                param.id = row.id;
                param.status = status;
                if(status==7){
                    _this.$confirm('确定接收评审任务，接收后请按时到达现场参与评审！', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        _this.loading = true;
                        axios.post('check', param).then(function (res) {
                            _this.loading = false;
                            if (res.data.code == 0) {
                                _this.$message({
                                    message: res.data.msg,
                                    type: "success"
                                });
                                _this.getData();
                            } else {
                                _this.$message({
                                    message: res.data.msg,
                                    type: "error"
                                });
                            }
                        }).catch(function (error) {
                            console.log(error);
                        });
                    });
                }
            },
            elementSet(row) {
                this.$refs.elementref.title="要素分配";
                this.$refs.elementref.open(row);
            },
            discuss(row){
                location.href = '/general/toppingsoft/index.php/expert/discuss/index?id='+row.discuss.id;
            },
            review(row){
                location.href = 'review?id='+row.id;
            },
            info(row){
                location.href = 'info?id='+row.id;
            },
            company_info (row) {
                var tmp = row;
                tmp.id = row.company_id;
                this.$refs.company_info.open(tmp);
            },
            discussAdd(row){
                var _this = this;
                var param = {
                    id: row.id,
                };
                _this.loading = true;
                axios.post('discussAdd', param).then(function (res) {
                    _this.loading = false;
                    if (res.data.code == 0) {
                        _this.$message({
                            message: res.data.msg,
                            type: "success"
                        });
                        _this.getData();
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            handleRefreshTable: function () {
                this.$nextTick(function () {
                    var refList = this.$refs;
                    if (refList) {
                        for (var i of Object.keys(refList)) {
                            if (refList[i] && refList[i].doLayout) {
                                refList[i].doLayout();
                            }
                        }
                    }
                });
            },
        },
        mounted() {
            //获取列表
            this.getData();
            this.getReason();

        }
    })
</script>


</body>
</html>