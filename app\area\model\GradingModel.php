<?php


namespace app\area\model;


use app\model\FileModel;
use think\Model;
use think\App;
use think\facade\Db;
use app\model\SettingModel;

//权限配置模块
class GradingModel extends Model
{


    //参数格式化
    public static function codeToText($info){
        if(empty($info)){
            return $info;
        }
        foreach (config('global.grading_files') as $v){
            if(isset($info[$v['field']])){
                $codes = explode(',',$info[$v['field']]);
                $info[$v['field']] = [];
                foreach ($codes as $v1){
                    if(!empty($v1)){
                        $f = FileModel::getFile(0,$v1,'');
                        $info[$v['field']][] = [
                            'id' => $f['id'],
                            'code' => $f['code'],
                            'name' => $f['name'],
                            'url' => $f['url'],
                            'ext' => $f['ext'],
                        ];
                    }
                }
            }
        }
        foreach ($info as $k=>$v){
            $info[$k] = $v==='0000-00-00'?'':$v;
            if(is_int($v)){
                $info[$k] = (string)$v;
            }
            if($v===null){
                $info[$k] = '';
            }
        }
        return $info;
    }
}