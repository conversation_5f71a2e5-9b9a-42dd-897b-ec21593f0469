<?php

namespace app\admin\model;

use app\model\FileModel;
use app\model\UserModel;
use PhpOffice\PhpWord\Exception\Exception;
use think\facade\Db;
use think\Model;
use Medoo\Mdb;

class PolicyModel extends Model
{
    /*
     * 保存政策法规
     */
    public static function savePolicy($param,$id = 0){
        $re = Db::table('top_notify')->where(['id'=>$id])->field('id')->find();
        $data = [
            'type' => $param['type'],//类型
            'title' => $param['title'],//标题
            'date' => $param['date'],//发布日期
            'summary' => $param['summary'],//简介
            'is_show' => $param['is_show']?1:0,//是否展示
            'content' => $param['content'],//详细内容
        ];
        if(empty($re)){
            $data['create_user_id'] = $_SESSION['LOGIN_USER_ID'];
            $data['create_user_name'] = $_SESSION['LOGIN_USER_NAME'];
            $data['create_time'] = time();
            $id = Db::table('top_notify')->insert($data);
        }else{
            Db::table('top_notify')->where(['id'=>$re['id']])->update($data);
        }
        return $id;
    }

    /*
     * 保存政策法规
     */
    public static function savePolicyType($param,$id = 0){
        $re = Db::table('top_notify_type')->where(['id'=>$id])->field('id')->find();
        $data = [
            'name' => $param['name'],//类型
            'sort' => $param['sort'],//标题
        ];
        if(empty($re)){
            $id = Db::table('top_notify_type')->insertGetId($data);
        }else{
            Db::table('top_notify_type')->where(['id'=>$re['id']])->update($data);
        }
        return $id;
    }



    //状态变更
    public static function upStatus($table,$field,$id){
        $table = 'top_'.$table;
        $field = 'is_'.$field;
        $re = Db::table($table)->where(['id'=>$id])->field($field)->find();
        if(empty($re)){
            return false;
        }
        $value = $re[$field]==1?0:1;
        $up = Db::table($table)->where(['id'=>$id])->update([$field=>$value]);
        return $up?$value:false;
    }

    public static function codeToText($info){
        if(empty($info)){
            return $info;
        }
        if(isset($info['is_show'])){
            $info['is_show'] = $info['is_show']==1?true:false;
        }
        if(isset($info['is_send'])){
            $info['is_send'] = $info['is_send']==1?true:false;
        }
        if(isset($info['dept_name'])&&empty($info['dept_name'])){
            $info['dept_name'] = '全部';
        }
        if(isset($info['user_type'])){
            $info['user_type'] = explode(',',$info['user_type']);
            foreach ($info['user_type'] as $v){
                $user_type[] = config('global.UserInfo.user_type')[$v];
            }
            $info['mb_user_type'] = trim(implode(',',$user_type),',');
        }
        if(isset($info['files'])){
            $ids = empty(trim($info['files'],','))?'0':trim($info['files'],',');
            $files = Db::table('top_files')->where('id','in',$ids)->field('id,filename name,filepath url')->select()->toArray();
            $info['files'] = $files;
        }
        foreach ($info as $k=>$v){
            $info[$k] = $v==='0000-00-00'?'':$v;
            if(is_int($v)){
                $info[$k] = (string)$v;
            }
            if($v===null){
                $info[$k] = '';
            }
        }
        return $info;
    }




}