<?php

namespace app\controller;

use app\controller\ReportGenerator;
use think\facade\Filesystem;

class GenerateReports
{
    public static function generateAllReports()
    {
        // 创建docs目录
        $docsPath = Filesystem::disk('public')->path('docs');
        if (!is_dir($docsPath)) {
            mkdir($docsPath, 0755, true);
        }

        // 生成2024年9月至2025年4月的月报
        for ($year = 2024; $year <= 2025; $year++) {
            $startMonth = ($year == 2024) ? 9 : 1;
            $endMonth = ($year == 2025) ? 4 : 12;
            
            for ($month = $startMonth; $month <= $endMonth; $month++) {
                $monthStr = str_pad($month, 2, '0', STR_PAD_LEFT);
                $yearMonth = "{$year}-{$monthStr}";
                
                // 生成月报
                ReportGenerator::generateMonthlyReport($yearMonth);
                
                // 生成周报（假设每月4周）
                for ($week = 1; $week <= 4; $week++) {
                    ReportGenerator::generateWeeklyReport($yearMonth, $week);
                }
            }
        }
        
        return '所有报告已生成在public/docs目录下';
    }
}