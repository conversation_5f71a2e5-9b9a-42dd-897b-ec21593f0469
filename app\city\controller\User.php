<?php
declare (strict_types = 1);

namespace app\city\controller;

use think\facade\Db;
use think\facade\View;
use think\Request;
use app\city\model\UserModel;

class User extends Base
{

    // 需要加密处理的字段配置
    private $smFields = ['name','mobile'];

    // 所有可搜索的字段配置
    private $searchableFields = ['username', 'name', 'email', 'mobile'];
    /**
     * @Apidoc\Title("人员管理列表")
     * @Apidoc\Desc("人员管理列表")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function index($limit = 20,$excel = 0) {
        if (request()->isAjax()){
            $name = $this->request->param('name','','trim');
            $where = [
                ['department','=',$_SESSION['city']['dept_id']],
                ['city_id','=',$_SESSION['city']['id']],
            ];
            // 使用动态搜索条件构建方法
            $searchConditions = $this->buildDynamicSearchConditions($name);
            $where1 = $searchConditions['encrypted'];
            $where2 = $searchConditions['plain'];
            $field = "id,username,name,email,mobile,status,reg_time,department,role,check_status";
            $res = Db::table('top_city_user')->where($where)->where($where)
                ->where(function($res) use ($where1,$where2){
                     if (!empty($where1)) {
                         $res->whereOr($where1);
                     }
                     if (!empty($where2)) {
                         $res->whereOr($where2);
                     }
                })
            ->field($field)->order('role');
            if($excel==1){
                $res = $res->select()->each(function ($item, $key) {
                    $item = UserModel::codeToText($item);
                    $item = $this->getDecryptData($item);
                    return $item;
                })->toArray();
                $title = [
                    ['title'=>'姓名','field'=>'name','width'=>'15'],
                    ['title'=>'手机号','field'=>'mobile','width'=>'20','type'=>'string'],
                    ['title'=>'邮箱','field'=>'email','width'=>'20'],
                    ['title'=>'邮箱','field'=>'email','width'=>'20'],
                ];
                ExcelModel::exportExcel($title, $res, '人员信息导出',true);
            }
            $res = $res->paginate($limit)->each(function ($item, $key) {
                $item = UserModel::codeToText($item);
                $item = $this->getDecryptData($item);               
                return $item;
            });
            result($res);
        }else{
            View::assign('title','首页');
            return view();
        }
    }


    public function getInfo($id=0) {
        $fields = "id,username,name,email,mobile,status,reg_time,department,role";
        $re = Db::table('top_city_user')->where(['id'=>$id])->field($fields)->find();
        if(!empty($re)){
            $re = UserModel::codeToText($re);
            $re = $this->getDecryptData($re);
        }else{
            $re = [
                'name' => '',
                'mobile' => '',
                'email' => '',
                'password' => '',
                'dept_name' => $_SESSION['city']['dept_name'],
                'role' => 0,
                'status' => 1,
            ];
        }
        result($re);
    }

    public function userSave($id=0) {
        $param = $this->request->post();
        if(empty($param['mobile'])){
            result('',1002,'手机号不能为空');
        }
        if(empty($param['name'])){
            result('',1002,'姓名不能为空');
        }
        if(empty($param['username'])){
            result('',1002,'用户名不能为空');
        }
        $data = [
            'username' => $param['username'],
            'email' => $param['email'],
            'mobile' => $param['mobile'],
            'status' => $param['status'],
            'name' => $param['name'],
            'role' => $param['role'],
        ];
        $data = $this->getEncryptData($data);
        $salt = create_nonce_str(4);
        if(empty($id)){
            $data['salt'] = $salt;
            $data['password'] = crypt($param['password'], $salt);
            $data['reg_time'] = date('Y-m-d H:i:s');
            $data['reg_ip'] = get_ip();
            $data['city_id'] = $_SESSION['city']['id'];
            $data['department'] = $_SESSION['city']['dept_id'];
            Db::table('top_city_user')->insert($data);
        }else{
            if(!empty($param['password'])){
                $data['salt'] = $salt;
                $data['password'] = crypt($param['password'], $salt);
            }
            $re = Db::table('top_city_user')->where(['id'=>$id])->find();
            if(empty($re)){
                result('',1002,'编辑信息不存在');
            }
            Db::table('top_city_user')->where(['id'=>$id])->update($data);
        }
        result(['id'=>$id]);
    }

       //严重数据的完整性校验
    public function verifyIntegrity($id) {
        $re = Db::table('top_city_user')->where(['id'=>$id])->find();
        $hashSource = '';
        foreach($this->smFields as $_feild){
            if(!empty($re[$_feild])){
                 $hashSource .= $re[$_feild];
            }
        }
        $out =(int) hsmVerify($hashSource,$re['check_hash']);
        result($out);
    }    


    //加密数据的机密性和完整性
    public function getEncryptData($data) {
        //统一加密
        $hashSource = "";
        foreach($this->smFields as $_feild){
            if(!empty($data[$_feild])){
                $data[$_feild] = hsmCacheEncrypt($data[$_feild]);
                $hashSource .=$data[$_feild];
            }
        }
        
        $data['check_status'] =1;
        $data['check_hash'] = hsmHmac($hashSource);
        return $data; 
    }


    /**
     * 构建动态搜索条件
     * @param string $searchValue 搜索值
     * @return array 返回加密字段和非加密字段的搜索条件
     */
    private function buildDynamicSearchConditions($searchValue) {
        $encryptedConditions = [];
        $plainConditions = [];

        if (!empty($searchValue)) {
            // 处理加密字段搜索
            if (!empty($this->smFields)) {
                $encryptedValue = hsmCacheEncrypt($searchValue);
                $encryptedFieldsStr = implode('|', $this->smFields);
                $encryptedConditions[] = [$encryptedFieldsStr, 'like', "%{$encryptedValue}%"];
            }

            // 处理非加密字段搜索
            $nonEncryptedFields = array_diff($this->searchableFields, $this->smFields);
            if (!empty($nonEncryptedFields)) {
                $nonEncryptedFieldsStr = implode('|', $nonEncryptedFields);
                $plainConditions[] = [$nonEncryptedFieldsStr, 'like', "%{$searchValue}%"];
            }
        }

        return [
            'encrypted' => $encryptedConditions,
            'plain' => $plainConditions
        ];
    }

    //解密数据
    public function getDecryptData($data) {
        //使用类属性中定义的加密字段
        foreach($this->smFields as $_feild){
            if(!empty($data[$_feild])){
                $data[$_feild] = hsmCacheDecrypt($data[$_feild]);
            }
        }
        return $data;
    }
}
