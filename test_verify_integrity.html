<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证完整性功能测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
</head>
<body>
    <div id="app">
        <el-container style="padding: 20px;">
            <el-header>
                <h1>验证完整性功能测试</h1>
            </el-header>
            
            <el-main>
                <el-card>
                    <div slot="header">
                        <span>测试验证完整性API</span>
                    </div>
                    
                    <el-form :model="testForm" label-width="120px">
                        <el-form-item label="表名:">
                            <el-input v-model="testForm.table_name" placeholder="请输入表名"></el-input>
                        </el-form-item>
                        
                        <el-form-item label="字段列表:">
                            <el-input 
                                v-model="testForm.fields_input" 
                                placeholder="请输入字段名，用逗号分隔，如: name,email,phone"
                                @blur="updateFields">
                            </el-input>
                            <div style="margin-top: 5px; color: #909399; font-size: 12px;">
                                字段数组: {{ testForm.fields }}
                            </div>
                        </el-form-item>
                        
                        <el-form-item label="WHERE条件:">
                            <el-input 
                                v-model="testForm.where_condition" 
                                placeholder="可选，如: id > 0 AND status = 1">
                            </el-input>
                        </el-form-item>
                        
                        <el-form-item>
                            <el-button type="primary" @click="testVerifyIntegrity" :loading="loading">
                                测试验证完整性
                            </el-button>
                            <el-button @click="resetForm">重置</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
                
                <el-card v-if="result" style="margin-top: 20px;">
                    <div slot="header">
                        <span>测试结果</span>
                    </div>
                    
                    <el-alert
                        :title="result.msg"
                        :type="result.code === 200 ? 'success' : 'error'"
                        show-icon
                        style="margin-bottom: 15px;">
                    </el-alert>
                    
                    <div v-if="result.code === 200 && result.data">
                        <el-row :gutter="20">
                            <el-col :span="6">
                                <el-statistic title="成功处理" :value="result.data.success_count"></el-statistic>
                            </el-col>
                            <el-col :span="6">
                                <el-statistic title="失败记录" :value="result.data.error_count"></el-statistic>
                            </el-col>
                            <el-col :span="6">
                                <el-statistic title="总记录数" :value="result.data.total_count"></el-statistic>
                            </el-col>
                            <el-col :span="6">
                                <el-statistic title="处理进度" :value="getProgressPercentage()" suffix="%"></el-statistic>
                            </el-col>
                        </el-row>
                        
                        <el-divider></el-divider>
                        
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-statistic 
                                    title="完整性正常" 
                                    :value="result.data.valid_count"
                                    :value-style="{color: '#67C23A'}">
                                </el-statistic>
                            </el-col>
                            <el-col :span="12">
                                <el-statistic 
                                    title="完整性异常" 
                                    :value="result.data.invalid_count"
                                    :value-style="{color: '#F56C6C'}">
                                </el-statistic>
                            </el-col>
                        </el-row>
                        
                        <div v-if="result.data.errors && result.data.errors.length > 0" style="margin-top: 20px;">
                            <el-collapse>
                                <el-collapse-item title="查看错误详情" name="1">
                                    <el-alert
                                        v-for="(error, index) in result.data.errors"
                                        :key="index"
                                        :title="error"
                                        type="error"
                                        :closable="false"
                                        style="margin-bottom: 5px;">
                                    </el-alert>
                                </el-collapse-item>
                            </el-collapse>
                        </div>
                    </div>
                    
                    <div style="margin-top: 15px;">
                        <el-button size="small" @click="copyResult">复制结果</el-button>
                        <el-button size="small" @click="result = null">清除结果</el-button>
                    </div>
                </el-card>
                
                <el-card style="margin-top: 20px;">
                    <div slot="header">
                        <span>使用说明</span>
                    </div>
                    
                    <el-steps direction="vertical" :active="4">
                        <el-step title="输入表名" description="输入要验证完整性的数据库表名"></el-step>
                        <el-step title="指定字段" description="输入要验证的字段名，用逗号分隔"></el-step>
                        <el-step title="设置条件" description="可选：设置WHERE条件来筛选要验证的记录"></el-step>
                        <el-step title="执行验证" description="点击按钮开始验证数据完整性"></el-step>
                    </el-steps>
                    
                    <el-divider></el-divider>
                    
                    <h4>验证逻辑说明：</h4>
                    <ul>
                        <li><strong>加密数据验证：</strong>尝试解密，检查解密是否成功</li>
                        <li><strong>明文数据验证：</strong>检查数据格式和长度是否合理</li>
                        <li><strong>完整性状态：</strong>实时更新每条记录的check_status字段</li>
                        <li><strong>错误处理：</strong>记录并报告验证过程中的所有错误</li>
                    </ul>
                </el-card>
            </el-main>
        </el-container>
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    loading: false,
                    result: null,
                    testForm: {
                        table_name: 'users',
                        fields_input: 'name,email,phone',
                        fields: ['name', 'email', 'phone'],
                        where_condition: ''
                    }
                }
            },
            methods: {
                updateFields() {
                    if (this.testForm.fields_input) {
                        this.testForm.fields = this.testForm.fields_input
                            .split(',')
                            .map(field => field.trim())
                            .filter(field => field.length > 0);
                    } else {
                        this.testForm.fields = [];
                    }
                },
                
                testVerifyIntegrity() {
                    if (!this.testForm.table_name || this.testForm.fields.length === 0) {
                        this.$message.error('请输入表名和字段列表');
                        return;
                    }
                    
                    this.loading = true;
                    this.result = null;
                    
                    // 模拟API调用
                    axios.post('/admin/field_encryption/verifyIntegrity', {
                        table_name: this.testForm.table_name,
                        fields: this.testForm.fields,
                        where_condition: this.testForm.where_condition
                    }).then(res => {
                        this.loading = false;
                        this.result = res.data;
                        
                        if (res.data.code === 200) {
                            this.$message.success('验证完整性操作完成');
                        } else {
                            this.$message.error(res.data.msg || '验证失败');
                        }
                    }).catch(err => {
                        this.loading = false;
                        this.result = {
                            code: 500,
                            msg: '网络错误或服务器异常: ' + (err.message || '未知错误'),
                            data: null
                        };
                        this.$message.error('请求失败');
                    });
                },
                
                resetForm() {
                    this.testForm = {
                        table_name: 'users',
                        fields_input: 'name,email,phone',
                        fields: ['name', 'email', 'phone'],
                        where_condition: ''
                    };
                    this.result = null;
                },
                
                getProgressPercentage() {
                    if (!this.result || !this.result.data) return 0;
                    const total = this.result.data.total_count;
                    const processed = this.result.data.success_count + this.result.data.error_count;
                    return total > 0 ? Math.round((processed / total) * 100) : 0;
                },
                
                copyResult() {
                    const text = JSON.stringify(this.result, null, 2);
                    navigator.clipboard.writeText(text).then(() => {
                        this.$message.success('结果已复制到剪贴板');
                    }).catch(() => {
                        this.$message.error('复制失败');
                    });
                }
            },
            
            mounted() {
                this.updateFields();
            }
        });
    </script>
</body>
</html>
