<?php
/**
 * This file is part of workerman.
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the MIT-LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link http://www.workerman.net/
 * @license http://www.opensource.org/licenses/mit-license.php MIT License
 */

namespace app\gateway\controller;

/**
 * 用于检测业务代码死循环或者长时间阻塞等问题
 * 如果发现业务卡死，可以将下面declare打开（去掉//注释），并执行php start.php reload
 * 然后观察一段时间workerman.log看是否有process_timeout异常
 */
//declare(ticks=1);

use app\expert\controller\Discuss;
use \GatewayWorker\Lib\Gateway;
use think\facade\Cache;
use think\facade\Db;
use Workerman\Crontab\Crontab;

/**
 * 主逻辑
 * 主要是处理 onConnect onMessage onClose 三个方法
 * onConnect 和 onClose 如果不需要可以不用实现并删除
 */
class Events
{
    // 进程启动时设置个定时器。Events中支持onWorkerStart需要Gateway版本>=2.0.4
    public static function onWorkerStart()
    {
//        30 21 * * *    //每晚的21:30
//        45 4 1,10,22 * *   //每月1、10、22日的4 : 45
//        10 1 * * 6,0   //每周六、周日的1 : 10
//        0,30 18-23 * * *  //每天18 : 00至23 : 00之间每隔30分钟
//        0 23 * * 6    //每星期六的11 : 00 pm
//        * */1 * * *    //每一小时
//        * 23-7/1 * * *    //晚上11点到早上7点之间，每隔一小时
//        0 11 4 * mon-wed //每月的4号与每周一到周三的11点
//        0 4 1 jan *      //一月一号的4点
//        */30 * * * *     //每半小时
        // 每分钟的第1秒执行.
        new Crontab('*/5 * * * * *', function(){
            //echo date('Y-m-d H:i:s')."\n";
        });
        // 每天的7点50执行，注意这里省略了秒位.
        /*new Crontab('50 7 * * *', function(){
            echo date('Y-m-d H:i:s')."\n";
        });*/
    }
    /**
     * 当客户端连接时触发
     * 如果业务不需此回调可以删除onConnect
     *
     * @param int $client_id 连接id
     */
    public static function onConnect($client_id)
    {
        // 向当前client_id发送数据
        // Gateway::sendToClient($client_id, "Hello $client_id\r\n");
        // 向所有人发送
//        Gateway::sendToAll("$client_id login\r\n");
    }

    /**
     * 当客户端发来消息时触发
     * @param int $client_id 连接id
     * @param mixed $message 具体消息
     */
    public static function onMessage($client_id, $message)
    {
        parse_str($message, $param);
        switch ($param['type']){
            case 'auth':
                $user = Cache::get($param['key']);
                Cache::delete($param['key']);
                if($user['type']=='expert'){
                    Gateway::bindUid($client_id,'expert'.$user['user_id']);
                    foreach ($user['group'] as $v){
                        Gateway::joinGroup($client_id,$v['id']);
                    }
                }else if($user['type']=='company'){
                    Gateway::bindUid($client_id,'company'.$user['user_id']);
                    foreach ($user['group'] as $v){
                        Gateway::joinGroup($client_id,$v['id']);
                    }
                }else if($user['type']=='org'){
                    Gateway::bindUid($client_id,'org'.$user['user_id']);
                    foreach ($user['group'] as $v){
                        Gateway::joinGroup($client_id,$v['id']);
                    }
                }else{
                    $message = [
                        'type' => 'close',
                    ];
                    Gateway::sendToClient($client_id,json_encode($message));
                    Gateway::closeClient($client_id);
                }
                break;
            default:
                break;
        }
    }

    /**
     * 当用户断开连接时触发
     * @param int $client_id 连接id
     */
    public static function onClose($client_id)
    {
        // 向所有人发送
        //GateWay::sendToAll("$client_id logout\r\n");
    }



}
