<style>
    .avatar-uploader .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
    .avatar-uploader .el-upload:hover {
        border-color: #409EFF;
    }
    .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 150px;
        height: 150px;
        line-height: 150px;
        text-align: center;
    }
    .avatar {
        width: 150px;
        height: 150px;
        display: block;
    }
    .my-label {
        width: 200px;
    }

    .my-content {
        width: 450px;
    }

    .margin-bottom {
        margin-bottom: 15px;
    }

    .form-header {
        background-color: #E9F2F3;
        line-height: 25px;
        margin-bottom: 15px;
        padding: 5px 10px;
    }

    .el-dialog__body {
        padding: 15px 20px;
    }

    .el-tabs__content {
        overflow: auto;
    }
</style>
<template>
    <el-dialog :title="title" :close-on-click-modal="false" modal="false" :visible.sync="visible" width="95%" top="10px"
               @close="refresh()" append-to-body="true" label-position="top">
        <!--<el-tabs type="border-card">

            <el-tab-pane label="企业信息" :style="{height:height+'px'}">-->
                <el-descriptions class="margin-top" :column="3" border label-class-name="my-label"
                                 content-class-name="my-content">
                    <el-descriptions-item label="企业名称">
                        {{data.company_name}}
                    </el-descriptions-item>
                    <el-descriptions-item label="注册地址">
                        {{data.company.mb_reg_address}}
                    </el-descriptions-item>
                    <el-descriptions-item label="生产经营地点">
                        {{data.company.mb_operate_address}}
                    </el-descriptions-item>
                    <el-descriptions-item label="所属行政区">
                        {{data.company.mb_region}}
                    </el-descriptions-item>
                    <el-descriptions-item label="营业执照">
                        <el-image
                                style="width: 100px; height: 100px"
                                :src="data.company.licenseUrl"
                                :preview-src-list="[data.company.licenseUrl]">
                        </el-image>
                    </el-descriptions-item>
                    <el-descriptions-item label="安全行政许可资料">
                        <el-image
                                style="width: 100px; height: 100px"
                                :src="data.company.aocUrl"
                                :preview-src-list="[data.company.aocUrl]">
                        </el-image>
                    </el-descriptions-item>
                    <el-descriptions-item label="法定代表人">
                        {{data.company.legal}}
                    </el-descriptions-item>
                    <el-descriptions-item label="法人联系电话">
                        {{data.company.legal_mobile}}
                    </el-descriptions-item>
                    <el-descriptions-item label="法人邮箱">
                        {{data.company.legal_email}}
                    </el-descriptions-item>
                    <el-descriptions-item label="企业传真">
                        {{data.company.fax}}
                    </el-descriptions-item>
                    <el-descriptions-item label="邮政编码">
                        {{data.company.postal_code}}
                    </el-descriptions-item>
                    <el-descriptions-item label="国民经济行业">
                        {{data.company.economy_sector}}
                    </el-descriptions-item>
                    <el-descriptions-item label="行业/专业">
                        {{data.company.industry}}/{{data.specialty}}
                    </el-descriptions-item>
                    <el-descriptions-item label="统一社会信用代码">
                        {{data.company.license_number}}
                    </el-descriptions-item>
                    <el-descriptions-item label="信用代码有效期">
                        {{data.company.license_start}}~{{data.company.license_end}}
                    </el-descriptions-item>
                    <el-descriptions-item label="经济类型">
                        {{data.company.economy_type}}
                    </el-descriptions-item>
                    <el-descriptions-item label="企业规模">
                        {{data.company.enterprise_size}}
                    </el-descriptions-item>
                    <el-descriptions-item label="注册资本">
                        {{data.company.reg_money}}万元
                    </el-descriptions-item>
                    <el-descriptions-item label="安全管理联系人">
                        {{data.company.manager}}
                    </el-descriptions-item>
                    <el-descriptions-item label="联系电话">
                        {{data.company.manager_mobile}}
                    </el-descriptions-item>
                    <el-descriptions-item label="邮箱">
                        {{data.company.manager_email}}
                    </el-descriptions-item>
                    <el-descriptions-item label="成立日期">
                        {{data.company.date}}
                    </el-descriptions-item>
                    <el-descriptions-item label="固定资产">
                        {{data.company.fixed_asset}}
                    </el-descriptions-item>
                    <el-descriptions-item label="年营业收入">
                        {{data.company.revenue}}万元
                    </el-descriptions-item>
                    <el-descriptions-item label="员工总数">
                        {{data.company.personnel}}
                    </el-descriptions-item>
                    <el-descriptions-item label="营业场所面积">
                        {{data.company.area}}m³
                    </el-descriptions-item>
                    <el-descriptions-item label="专职安全管理人数">
                        {{data.company.personnel_full}}
                    </el-descriptions-item>
                    <el-descriptions-item label="兼职安全管理人数">
                        {{data.company.personnel_part}}
                    </el-descriptions-item>
                    <el-descriptions-item label="特种作业人数">
                        {{data.company.personnel_special}}
                    </el-descriptions-item>
                    <el-descriptions-item label="所属集团名称">
                        {{data.company.group_name}}
                    </el-descriptions-item>
                    <el-descriptions-item label="经营范围" :span="3">
                        {{data.company.business}}
                    </el-descriptions-item>
                </el-descriptions>
           <!-- </el-tab-pane>

        </el-tabs>-->

        <!--编辑联系人-->
    </el-dialog>
</template>
<script>
    module.exports = {
        name: "add",
        // 模板导入区
        components: {},
        data: function () {
            return {
                id: 0,
                isAdmin: false,
                visible: false,
                dialogFormVisible: false,
                title: '企业信息',
                form: {
                    reason: '',
                    files: [],
                },
                loading: false,
                check: true,
                noMore: false,
                user_id: 0,
                data: {},
                is_see: 0,
                ca: [],
                details: [],
                cards: {},
                type: {},
                pcas: [],
                restaurants: [],
                restaurants2: [],
                companyData: {},
                standardData: {},
                height: document.documentElement.clientHeight - 250,
                formRules: {
                    reason: [
                        {required: true, message: '请输入驳回原因', trigger: 'blur'}
                    ],
                },
                url: {
                    'sub': 'do_check',
                    'company_info': 'get_company'
                },
            }
        },
        computed: {
            disabled() {
                return this.loading || this.noMore
            }
        },
        mounted: function () {
            //this.getConfig();
        },
        created: function () {
        },
        methods: {
            /**
             * 打开弹窗调用方法
             * */
            open: function (row, check) {
                var _this = this;
                _this.data = row;
                _this.id = row.id;
                _this.visible = true;
                _this.noMore = false;
                console.log(row.company_id);
                _this.getInfo(row.gradingApproval.id, row.company_id);
                _this.check = check;
            },
            closeDialog: function () {
                this.visible = false;
            },
            handleRemove(file, row){
                var files = this.form.files;
                var newFiles = [];
                for (var i=0; i<files.length; i++){
                    if (files[i].code != file.response.data.code){
                        newFiles.push(files[i])
                    }
                }
                this.form.fiels = newFiles;
            },
            getInfo: function (id, company_id) {
                var _this = this;
                if (id) {
                    var url = _this.url.company_info;
                    axios.post(url, {company_id: company_id, id: id}).then(function (res) {
                        if (res.data.code == 0) {
                            _this.companyData = res.data.data;
                        } else {
                            _this.$message({
                                message: res.data.msg,
                                type: res.data.type
                            });
                        }
                    }).catch(function (error) {
                        console.log("出现错误:", error);
                    });
                }
            },
            uploadBefore(file) {
                // const isJPG = file.type === 'image/jpeg';
                // const isPNG = file.type === 'image/png';
                // if(!isJPG&&!isPNG){
                //     this.$message.error('请上传jpg图片');
                // }
                // return isJPG||isPNG;
                return true;
            },
            uploadSuccess(res, file,fileList,field) {
                var files = [];
                console.log(res)
                for(var i in fileList){
                    files.push(fileList[i].response??fileList[i]);
                }
                this.form[field].push({code: res.data.code, url: res.data.url, filename:res.data.filename})
            },
            submit(status) {
                var _this = this;
                this.$confirm("确认提交审核状态吗？", "提示", {}).then(() => {
                    if (status == 5 && _this.form.reason == '') {
                        _this.$message({
                            message: '请输入驳回原因',
                            type: "error"
                        });
                        return false;
                    }
                    _this.addLoading = true;
                    var param = {id: _this.data.id, status: status, reason: _this.form.reason};
                    var url = _this.url.sub;
                    axios.post(url, param).then(function (res) {
                        _this.addLoading = false;
                        if (res.data.code == -200) {
                            _this.$message({
                                message: res.data.msg,
                                type: "error"
                            });
                        } else {
                            _this.$message({
                                message: res.data.msg,
                                type: "success"
                            });
                            _this.visible = false;
                        }

                    }).catch(function (error) {
                        console.log(error);
                    });

                });
            },
            addContacts: function () {
                this.form = {};
                this.dialogFormVisible = true;
            },
            refresh: function () {
                this.$emit("refresh");
            },
            editContacts: function (row) {
                this.form = row;
                this.dialogFormVisible = true;
            },
        }
    }
</script>


