<?php

namespace app\api\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use hg\apidoc\annotation as Apidoc;
use think\facade\Cache;
use think\facade\Db;
use think\facade\View;
use app\api\model\SecsModel;


/**
 * @Apidoc\Title("首页")
 * @Apidoc\Group("Index")
 * @Apidoc\Sort(2)
 */
class Index extends Base {

    /**
     * NotHeaders
     * @Apidoc\Title("首页")
     * @Apidoc\Desc("首页")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Param("token", type="string",require=false, default="",desc="登陆接口获取")
     * @Apidoc\Returned("message", type="object", desc="消息通知",children={
     * @Apidoc\Returned("id", type="int", desc="消息id"),
     * @Apidoc\Returned("sms_type", type="string", desc="消息类型"),
     * @Apidoc\Returned("sms_content", type="string", desc="消息内容"),
     * @Apidoc\Returned("sms_url", type="string", desc="消息链接"),
     * @Apidoc\Returned("sms_time", type="string", desc="消息时间"),
     *     })
     * @Apidoc\Returned("notify", type="object", desc="公示公告",children={
     * @Apidoc\Returned("id", type="int", desc="公示公告id"),
     * @Apidoc\Returned("title", type="string", desc="公示公告标题"),
     * @Apidoc\Returned("date", type="string", desc="公示公告日期"),
     *     })
     * @Apidoc\Returned("total", type="object", desc="评审任务总数（主管部门用户）",children={
     * @Apidoc\Returned("company_sum", type="int", desc="企业总计"),
     * @Apidoc\Returned("company_end", type="int", desc="已完成定标定级"),
     * @Apidoc\Returned("company_not", type="int", desc="未进行定标定级"),
     * @Apidoc\Returned("end", type="int", desc="累计完成数量"),
     * @Apidoc\Returned("ing", type="int", desc="进行中的数量"),
     * @Apidoc\Returned("not", type="int", desc="待签收数量"),
     *     })
     * @Apidoc\Returned("company_info", type="object", desc="企业信息（企业用户）",children={
     * @Apidoc\Returned("id", type="int", desc="企业id"),
     * @Apidoc\Returned("name", type="string", desc="企业名称"),
     * @Apidoc\Returned("legal", type="string", desc="法人"),
     * @Apidoc\Returned("industry", type="string", desc="行业/专业"),
     * @Apidoc\Returned("ca_status", type="string", desc="是否获得证书"),
     * @Apidoc\Returned("operate_address_info", type="string", desc="生产经营地点"),
     *     })
     * @Apidoc\Returned("cards", type="object", desc="我的证书（企业用户）",children={
     * @Apidoc\Returned("id", type="int", desc="证书id"),
     * @Apidoc\Returned("company_name", type="string", desc="企业名称"),
     * @Apidoc\Returned("code", type="string", desc="证书编号"),
     * @Apidoc\Returned("start", type="string", desc="开始日期"),
     * @Apidoc\Returned("ends", type="string", desc="结束日期"),
     *     })
     * @Apidoc\Returned("grads", type="list", desc="定级申请列表（企业用户）",children={
     * @Apidoc\Returned("id", type="int", desc="申请表id"),
     * @Apidoc\Returned("company_name", type="string", desc="企业名称"),
     * @Apidoc\Returned("type", type="string", desc="申请类型"),
     * @Apidoc\Returned("date", type="string", desc="申请日期"),
     * @Apidoc\Returned("status", type="string", desc="状态{0：未提交，1：审批中，2：整改中，3：整改审批中，5：未通过，7：已通过，9：放弃评审}"),
     *     })
     * @Apidoc\Returned("tasks", type="list", desc="评审列表（主管部门用户）",children={
     * @Apidoc\Returned("id", type="int", desc="评审id"),
     * @Apidoc\Returned("company_name", type="string", desc="企业名称"),
     * @Apidoc\Returned("date", type="string", desc="评审日期"),
     * @Apidoc\Returned("status", type="string", desc="状态{7:未开始，8：评审中，9：已结束}"),
     *     })
     */
    public function index($token='') {
        $user = Cache::get('token_'.$token);
        if(in_array($user['user_type'],['company','city','area','org','expert'])) {
            $data['message'] = Db::table('top_message')
                ->where(['user_type' => $user['user_type'], 'user_id' => $user['userinfo']['user_id'], 'is_read' => 0])
                ->field('id,sms_type,sms_content,sms_url,sms_time')->order('sms_time desc')->find();
        }
        $data['notify'] = Db::table('top_notify')->alias('a')
            ->leftJoin('top_notify_type t','a.type = t.id')
            ->where(['a.is_del'=>0,'a.is_show'=>1])
            ->order('date desc,create_time desc')
            ->field('a.*,t.name mb_type')->order('date desc')->find();
        $data['notify']['url'] = '/api/index/notifyInfo?id='.$data['notify']['id'];
        if(in_array($user['user_type'],['city'])){
            $data['total']['company_sum'] = Db::table('top_company_info')->where(['city_id'=>$user['userinfo']['id']])->count();
            $data['total']['company_not'] = Db::table('top_company_info')->where(['city_id'=>$user['userinfo']['id'],'ca_status'=>0])->where('status','in','7,8')->count();
            $data['total']['company_end'] = Db::table('top_company_info')->where(['city_id'=>$user['userinfo']['id'],'ca_status'=>1])->count();
            $data['grads'] = Db::table('top_grading')->where(['city_id'=>$user['userinfo']['id']])->field('id,company_name,type,date,status')->order('apply_time desc')->limit(0,5)->select();
            $data['total']['end'] = Db::table('top_org_tasks')->where('status','in','9')->count();
            $data['total']['ing'] = Db::table('top_org_tasks')->where('status','in','7,8')->count();
            $data['total']['not'] = Db::table('top_org_tasks')->where('status','<','7')->count();
            $data['tasks'] = Db::table('top_org_tasks')->where('status','>=','7')->field('id,company_name,date,status')->order('date desc')->limit(0,5)->select();
        }
        if(in_array($user['user_type'],['area'])){
            $data['total']['company_sum'] = Db::table('top_company_info')->where(['area_id'=>$user['userinfo']['id']])->count();
            $data['total']['company_not'] = Db::table('top_company_info')->where(['area_id'=>$user['userinfo']['id'],'ca_status'=>0])->where('status','in','7,8')->count();
            $data['total']['company_end'] = Db::table('top_company_info')->where(['area_id'=>$user['userinfo']['id'],'ca_status'=>1])->count();
            $data['grads'] = Db::table('top_grading')->where(['area_id'=>$user['userinfo']['id']])->field('id,company_name,type,date,status')->order('apply_time desc')->limit(0,5)->select();
        }
        /**
        * 0:待接收
        * 1:已接收
        * 2:已推送专家
        * 3:待确认组长
        * 5:已驳回
        * 7:审核通过
        * 8:评审中
        * 9:评审结束
         **/ 
        if(in_array($user['user_type'],['org','expert'])){
            $data['total']['end'] = Db::table('top_org_tasks')->where('status','in','9')->count(); //已结束
            $data['total']['ing'] = Db::table('top_org_tasks')->where('status','in','7,8')->count(); //进行中
            $data['total']['wait'] = Db::table('top_org_tasks')->where('status','=','0')->count();//待签收
            $data['tasks'] = Db::table('top_org_tasks')->where('status','>=','7')->field('id,company_name,date,status')->order('date desc')->limit(0,5)->select();
        }

        if(in_array($user['user_type'],['expert'])){
            //获取专家系统专家请假次数
            $res = SecsModel::getSecsInterfaceData('secsExpert/getApply4LeaveCountByExpertIds',["expertIds"=>[$user['userinfo']['expert_id']]],'post');
            if($res['code']==0){
                $infos = $res['data']['data'][0];
                $data['total']['leave_count'] = $infos['apply4LeaveCount'];
            }
            //获取企业系统专家的相关信息
            $res = SecsModel::getSecsInterfaceData('secsTask/getCountByExpertId',["expertId"=>$user['userinfo']['expert_id']],'post');
            if($res['code']==0){
                $infos = $res['data']['data'];
                $data['total']['apply4LeaveCount'] = $infos['apply4LeaveCount'];
                $data['total']['reviewedTaskCount'] = $infos['reviewedTaskCount'];
                $data['total']['totalTaskCount'] = $infos['totalTaskCount'];
                $data['total']['ongoingTaskCount'] = $infos['ongoingTaskCount'];
                $data['total']['waitFeedbackTaskCount'] = $infos['waitFeedbackTaskCount'];
            }
        }
        if(in_array($user['user_type'],['company'])){
            $company_info = Db::table('top_company_info')->where(['user_id'=>$user['userinfo']['user_id']])->field('id,name,legal,industry,specialty,ca_status,operate_address_info')->find();
            $data['company_info'] = [
                'id' => $company_info['id'],
                'name' => $company_info['name'],
                'legal' => $company_info['legal'],
                'industry' => $company_info['industry'].'/'.$company_info['specialty'],
                'ca_status' => $company_info['ca_status']==1?'是':'否',
                'operate_address_info' => $company_info['operate_address_info'],
            ];
            $data['cards'] = Db::table('top_certificate')->where(['company_id'=>$user['userinfo']['id'],'status'=>'7'])->field('id,company_name,code,start,ends')->order('start desc')->find();
            $data['grads'] = Db::table('top_grading')->where(['company_id'=>$user['userinfo']['id']])->field('id,company_name,type,date,status')->order('apply_time desc')->limit(0,5)->select()->toArray();
        }
        result($data);
    }

    /**
     * NotHeaders
     * @Apidoc\Title("公示公告列表")
     * @Apidoc\Desc("公示公告列表")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Param("token", type="string",require=false, default="",desc="登陆接口获取")
     * @Apidoc\Param("limit", type="int",require=false, default="10",desc="每页展示数量")
     * @Apidoc\Param("page", type="int",require=false, default="1",desc="页码")
     * @Apidoc\Returned("data", type="list", desc="公示公告列表",children={
     * @Apidoc\Returned("id", type="int", desc="id"),
     * @Apidoc\Returned("mb_type", type="string", desc="公告类型"),
     * @Apidoc\Returned("title", type="string", desc="标题"),
     * @Apidoc\Returned("date", type="string", desc="公告日期"),
     * @Apidoc\Returned("url", type="string", desc="公告链接"),
     *     })
     * @Apidoc\Returned("total", type="int", desc="数据总数")
     * @Apidoc\Returned("per_page", type="int", desc="每页数量")
     * @Apidoc\Returned("current_page", type="int", desc="当前页码")
     * @Apidoc\Returned("last_page", type="int", desc="最后一页页码")
     */
    public function notify($limit=10) {
        $data = Db::table('top_notify')->alias('a')
            ->leftJoin('top_notify_type t','a.type = t.id')
            ->where(['a.is_del'=>0,'a.is_show'=>1])
            ->order('date desc,create_time desc')
            ->field('a.*,t.name mb_type')->order('date desc')
            ->paginate($limit)->each(function ($item, $key) {
                $item['url'] = '/api/index/notifyInfo?id='.$item['id'];
                return $item;
            });
        result($data);
    }

    /**
     * NotHeaders
     * @Apidoc\Title("公示公告详情")
     * @Apidoc\Desc("公示公告详情")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("limit", type="int",require=false, default="10",desc="每页展示数量")
     * @Apidoc\Param("page", type="int",require=false, default="1",desc="页码")
     * @Apidoc\Returned("data", type="list", desc="公示公告列表",children={
     * @Apidoc\Returned("id", type="int", desc="id"),
     * @Apidoc\Returned("title", type="string", desc="标题"),
     * @Apidoc\Returned("date", type="string", desc="公示日期"),
     *     })
     * @Apidoc\Returned("total", type="int", desc="数据总数")
     * @Apidoc\Returned("per_page", type="int", desc="每页数量")
     * @Apidoc\Returned("current_page", type="int", desc="当前页码")
     * @Apidoc\Returned("last_page", type="int", desc="最后一页页码")
     */
    public function notifyInfo($id=0) {
        $data = Db::table('top_notify')
            ->where(['is_show'=>1,'is_del'=>0])
            ->where(['id'=>$id])
            ->field('id,title,date,content')->order('date desc')
            ->find();
        $data['content'] = htmlspecialchars_decode($data['CONTENT']);
//        dd($data);
        View::assign('data',$data);
        return view();
//        result($data);
    }

    /**
     * NotHeaders
     * @Apidoc\Title("证书查询")
     * @Apidoc\Desc("证书查询")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("name", type="string",require=true, default="",desc="企业名称/证书编号")
     * @Apidoc\Returned("data", type="list", desc="证书列表",children={
     * @Apidoc\Returned("id", type="int", desc="id"),
     * @Apidoc\Returned("comapny_name", type="string", desc="企业名称"),
     * @Apidoc\Returned("industry", type="string", desc="行业"),
     * @Apidoc\Returned("specialty", type="string", desc="专业"),
     * @Apidoc\Returned("code", type="string", desc="证书编号"),
     * @Apidoc\Returned("level", type="string", desc="证书等级"),
     * @Apidoc\Returned("start", type="string", desc="有效期开始时间"),
     * @Apidoc\Returned("ends", type="string", desc="有效期结束时间"),
     * @Apidoc\Returned("status", type="string", desc="状态"),
     *     })
     */
    public function cardList($name='') {
        if(empty($name)){
            result('',1002,'请填写企业名称/证书编号');
        }
        $data['data'] = Db::table('top_certificate')
            ->where('company_name|code','=',$name)
            ->field('id,company_name,industry,specialty,code,level,start,ends,status')->order('start desc')
            ->select();
        result($data);
    }

    /**
     * NotHeaders
     * @Apidoc\Title("证书详情")
     * @Apidoc\Desc("证书详情")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("name", type="string",require=true, default="",desc="证书编号")
     * @Apidoc\Returned("id", type="int", desc="id")
     * @Apidoc\Returned("comapny_name", type="string", desc="企业名称")
     * @Apidoc\Returned("industry", type="string", desc="行业")
     * @Apidoc\Returned("specialty", type="string", desc="专业")
     * @Apidoc\Returned("code", type="string", desc="证书编号")
     * @Apidoc\Returned("level", type="string", desc="证书等级")
     * @Apidoc\Returned("start", type="string", desc="有效期开始时间")
     * @Apidoc\Returned("ends", type="string", desc="有效期结束时间")
     * @Apidoc\Returned("status", type="string", desc="状态")
     */
    public function cardInfo($name='') {
        if(empty($name)){
            result('',1002,'请选择证书');
        }
        $data = Db::table('top_certificate')
            ->where('code','=',$name)
            ->field('id,company_name,industry,specialty,code,level,start,ends,status')
            ->find();
        result($data);
    }

}
