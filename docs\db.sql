CREATE TABLE "SYSDBA"."top_aaa"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "a" VARCHAR2(800) DEFAULT '',
    "v" VARCHAR2(800) DEFAULT '',
    "run_id" VARCHAR2(800) DEFAULT '',
    "c" VARCHAR2(800) DEFAULT '',
    "u" VARCHAR2(800) DEFAULT '',
    "images" VARCHAR2(800) DEFAULT '',
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_aaa" IS '12';
COMMENT ON COLUMN "SYSDBA"."top_aaa"."c" IS '阿达';
COMMENT ON COLUMN "SYSDBA"."top_aaa"."images" IS '图片';
COMMENT ON COLUMN "SYSDBA"."top_aaa"."run_id" IS 'run_id';
COMMENT ON COLUMN "SYSDBA"."top_aaa"."u" IS 'u';
COMMENT ON COLUMN "SYSDBA"."top_aaa"."v" IS '阿萨德';


CREATE TABLE "SYSDBA"."top_aaaa"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "run_id" VARCHAR2(800) DEFAULT '',
    "v" VARCHAR2(800) DEFAULT '',
    "c" VARCHAR2(800) DEFAULT '',
    "main_id" VARCHAR2(800) DEFAULT '',
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_aaaa" IS '12';
COMMENT ON COLUMN "SYSDBA"."top_aaaa"."c" IS 'c';
COMMENT ON COLUMN "SYSDBA"."top_aaaa"."main_id" IS 'main_id';
COMMENT ON COLUMN "SYSDBA"."top_aaaa"."run_id" IS 'run_id';
COMMENT ON COLUMN "SYSDBA"."top_aaaa"."v" IS 'v';


CREATE TABLE "SYSDBA"."top_advisory"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "name" VARCHAR2(400) DEFAULT '',
    "address" VARCHAR2(800) DEFAULT '',
    "legal" VARCHAR2(200) DEFAULT '',
    "level" VARCHAR2(200) DEFAULT '',
    "reg_money" DECIMAL(22,6) DEFAULT 0,
    "business" VARCHAR2(2000) DEFAULT '',
    "license_code" VARCHAR2(200) DEFAULT '',
    "license_start" DATE DEFAULT '',
    "license_end" DATE DEFAULT '',
    "personnel" SMALLINT DEFAULT 0,
    "personnel_senior" SMALLINT DEFAULT 0,
    "personnel_medium" SMALLINT DEFAULT 0,
    "company_name" VARCHAR2(200) DEFAULT '',
    "license" VARCHAR2(800) DEFAULT '',
    "is_block" TINYINT DEFAULT 0,
    "create_user_id" VARCHAR2(200) DEFAULT '',
    "create_user_name" VARCHAR2(200) DEFAULT '',
    "create_time" DATETIME(6) DEFAULT '',
    "contacts" VARCHAR2(200) DEFAULT '',
    "tel" VARCHAR2(200) DEFAULT '',
    "email" VARCHAR2(800),
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_advisory" IS '咨询机构信息表';
COMMENT ON COLUMN "SYSDBA"."top_advisory"."address" IS '注册地址';
COMMENT ON COLUMN "SYSDBA"."top_advisory"."business" IS '经营范围';
COMMENT ON COLUMN "SYSDBA"."top_advisory"."company_name" IS '所属集团公司';
COMMENT ON COLUMN "SYSDBA"."top_advisory"."contacts" IS '联系人';
COMMENT ON COLUMN "SYSDBA"."top_advisory"."email" IS '邮箱';
COMMENT ON COLUMN "SYSDBA"."top_advisory"."is_block" IS '是否黑名单';
COMMENT ON COLUMN "SYSDBA"."top_advisory"."legal" IS '法人';
COMMENT ON COLUMN "SYSDBA"."top_advisory"."level" IS '资质种类、等级';
COMMENT ON COLUMN "SYSDBA"."top_advisory"."license" IS '营业执照';
COMMENT ON COLUMN "SYSDBA"."top_advisory"."license_code" IS '统一社会信用代码';
COMMENT ON COLUMN "SYSDBA"."top_advisory"."license_end" IS '信用代码有效期';
COMMENT ON COLUMN "SYSDBA"."top_advisory"."license_start" IS '信用代码有效期';
COMMENT ON COLUMN "SYSDBA"."top_advisory"."name" IS '机构名称';
COMMENT ON COLUMN "SYSDBA"."top_advisory"."personnel" IS '从业人数';
COMMENT ON COLUMN "SYSDBA"."top_advisory"."personnel_medium" IS '中级职称人数';
COMMENT ON COLUMN "SYSDBA"."top_advisory"."personnel_senior" IS '高级职称人数';
COMMENT ON COLUMN "SYSDBA"."top_advisory"."reg_money" IS '注册资金（万元）';
COMMENT ON COLUMN "SYSDBA"."top_advisory"."tel" IS '电话';


CREATE TABLE "SYSDBA"."top_area"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "name" VARCHAR2(200) DEFAULT '',
    "pcas" TEXT DEFAULT '',
    "status" TINYINT DEFAULT 0,
    "contacts" VARCHAR2(200) DEFAULT '',
    "tel" VARCHAR2(200) DEFAULT '',
    "address" VARCHAR2(400) DEFAULT '',
    "del_flag" TINYINT DEFAULT 0,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_area" IS '区县应急局表';
COMMENT ON COLUMN "SYSDBA"."top_area"."address" IS '地址';
COMMENT ON COLUMN "SYSDBA"."top_area"."contacts" IS '联系人';
COMMENT ON COLUMN "SYSDBA"."top_area"."del_flag" IS '删除标记,1删除';
COMMENT ON COLUMN "SYSDBA"."top_area"."name" IS '应急局名称';
COMMENT ON COLUMN "SYSDBA"."top_area"."pcas" IS '管辖区域id集';
COMMENT ON COLUMN "SYSDBA"."top_area"."status" IS '状态';
COMMENT ON COLUMN "SYSDBA"."top_area"."tel" IS '联系电话';


CREATE TABLE "SYSDBA"."top_area_user"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "username" VARCHAR2(200) DEFAULT '',
    "password" VARCHAR2(200) DEFAULT '',
    "email" VARCHAR2(400) DEFAULT '',
    "mobile" VARCHAR2(44) DEFAULT '',
    "status" INT DEFAULT 0,
    "reg_time" DATETIME(6) DEFAULT '',
    "reg_ip" VARCHAR2(128) DEFAULT '',
    "area_id" INT DEFAULT 0,
    "department" INT DEFAULT 0,
    "name" VARCHAR2(200) DEFAULT '',
    "salt" VARCHAR2(200) DEFAULT '',
    "role" INT DEFAULT 0,
    "openid" VARCHAR2(200) DEFAULT '',
    "unionid" VARCHAR2(200) DEFAULT '',
    "manager" VARCHAR2(200) DEFAULT '',
    NOT CLUSTER PRIMARY KEY("id"),
    UNIQUE("username")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON COLUMN "SYSDBA"."top_area_user"."area_id" IS '应急局id';
COMMENT ON COLUMN "SYSDBA"."top_area_user"."department" IS '科室 1：基础科 2：危化科';
COMMENT ON COLUMN "SYSDBA"."top_area_user"."email" IS '邮箱';
COMMENT ON COLUMN "SYSDBA"."top_area_user"."manager" IS '管辖范围';
COMMENT ON COLUMN "SYSDBA"."top_area_user"."mobile" IS '手机号';
COMMENT ON COLUMN "SYSDBA"."top_area_user"."name" IS '姓名';
COMMENT ON COLUMN "SYSDBA"."top_area_user"."password" IS '密码';
COMMENT ON COLUMN "SYSDBA"."top_area_user"."reg_ip" IS '注册ip';
COMMENT ON COLUMN "SYSDBA"."top_area_user"."reg_time" IS '注册时间';
COMMENT ON COLUMN "SYSDBA"."top_area_user"."role" IS '角色 0：普通角色 1：管理员 2：超级管理员';
COMMENT ON COLUMN "SYSDBA"."top_area_user"."salt" IS '密码盐';
COMMENT ON COLUMN "SYSDBA"."top_area_user"."status" IS '状态';
COMMENT ON COLUMN "SYSDBA"."top_area_user"."username" IS '用户名';


CREATE OR REPLACE  INDEX "SYSDBA"."area_username" ON "SYSDBA"."top_area_user"("username" ASC,"mobile" ASC,"openid" ASC,"unionid" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "SYSDBA"."top_auth"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "key" VARCHAR2(200) DEFAULT '',
    "value" TEXT DEFAULT '',
    "is_json" INT DEFAULT 0,
    "model" VARCHAR2(200) DEFAULT '',
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_auth" IS '列表组件权限模块权限配置表';
COMMENT ON COLUMN "SYSDBA"."top_auth"."is_json" IS '是否json';
COMMENT ON COLUMN "SYSDBA"."top_auth"."key" IS '权限唯一标识';
COMMENT ON COLUMN "SYSDBA"."top_auth"."model" IS '权限所属模块';
COMMENT ON COLUMN "SYSDBA"."top_auth"."value" IS '权限值';


CREATE TABLE "SYSDBA"."top_auth_config"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "key" VARCHAR2(800) DEFAULT '',
    "value" VARCHAR2(12000) DEFAULT '',
    "is_json" TINYINT DEFAULT 0,
    "model" VARCHAR2(200) DEFAULT '',
    "tables" VARCHAR2(200) DEFAULT '',
    "title" VARCHAR2(200) DEFAULT '',
    "is_depts" INT DEFAULT 0,
    "manager" VARCHAR2(200) DEFAULT '',
    "admin_priv" VARCHAR2(1000) DEFAULT '',
    "config_fields" TEXT DEFAULT '',
    "create_user_id" VARCHAR2(200) DEFAULT '',
    "create_user_name" VARCHAR2(200) DEFAULT '',
    "create_time" VARCHAR2(200) DEFAULT '',
    "is_del" INT DEFAULT 0,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_auth_config" IS '权限配置表';
COMMENT ON COLUMN "SYSDBA"."top_auth_config"."admin_priv" IS '一键设置部门权限相关角色';
COMMENT ON COLUMN "SYSDBA"."top_auth_config"."config_fields" IS '全局权限配置';
COMMENT ON COLUMN "SYSDBA"."top_auth_config"."create_time" IS '添加时间';
COMMENT ON COLUMN "SYSDBA"."top_auth_config"."create_user_id" IS '添加人';
COMMENT ON COLUMN "SYSDBA"."top_auth_config"."create_user_name" IS '添加人';
COMMENT ON COLUMN "SYSDBA"."top_auth_config"."is_del" IS '是否删除';
COMMENT ON COLUMN "SYSDBA"."top_auth_config"."is_depts" IS '是否分部门权限';
COMMENT ON COLUMN "SYSDBA"."top_auth_config"."manager" IS '模块管理权限';
COMMENT ON COLUMN "SYSDBA"."top_auth_config"."tables" IS '权限表';
COMMENT ON COLUMN "SYSDBA"."top_auth_config"."title" IS '权限模块名称';


CREATE TABLE "SYSDBA"."top_caexcel"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "year" VARCHAR2(50) DEFAULT '',
    "pca" VARCHAR2(500) DEFAULT '',
    "name" VARCHAR2(500) DEFAULT '',
    "level" VARCHAR2(500) DEFAULT '',
    "hangye" VARCHAR2(50) DEFAULT '',
    "zhuanye" VARCHAR2(50) DEFAULT '',
    "start" VARCHAR2(100) DEFAULT '',
    "ends" VARCHAR2(100) DEFAULT '',
    "code" VARCHAR2(500) DEFAULT '',
    "number" VARCHAR2(50) DEFAULT '',
    "fzdw" VARCHAR2(500) DEFAULT '',
    "yzdw" VARCHAR2(500) DEFAULT '',
    "gswh" VARCHAR2(500) DEFAULT '',
    "zzrq" VARCHAR2(200) DEFAULT '',
    "leibie" VARCHAR2(50) DEFAULT '',
    "company_id" INT DEFAULT 0,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "SYSDBA"."top_cay_exam_config"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "base" INT,
    "speciality" INT,
    "radio" INT,
    "judgment" INT,
    "one_radio_score" INT,
    "one_judgment_score" INT,
    UNIQUE("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_cay_exam_config" IS '考试基本配置';


CREATE TABLE "SYSDBA"."top_cay_exam_paper"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "task_id" INT,
    "total_score" INT,
    "position_name" VARCHAR(400),
    "industry_id" VARCHAR(400),
    "user_name" VARCHAR(400),
    "sign" VARCHAR(400),
    "start_date" VARCHAR(400),
    "end_date" VARCHAR(400),
    "uid" VARCHAR(400)) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_cay_exam_paper" IS '试卷表';


CREATE TABLE "SYSDBA"."top_cay_exam_paper_list"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "test_info_id" INT,
    "my_score" INT,
    "score" INT,
    "my_answer" CHAR(40),
    "answer" CHAR(40),
    "paper_id" INT,
    UNIQUE("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_cay_exam_paper_list" IS '试卷列表信息';


CREATE TABLE "SYSDBA"."top_cay_exam_test_info"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "test_title" TEXT,
    "test_option" TEXT,
    "test_answer" VARCHAR(400),
    "type_id" INT,
    "create_user_name" CHAR(200),
    "create_user_id" CHAR(200),
    "created_at" CHAR(200),
    "updated_at" CHAR(200),
    "deleted_at" CHAR(200),
    "test_analysis" TEXT,
    "industry_ids" VARCHAR(1020),
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_cay_exam_test_info" IS '试题信息';
COMMENT ON COLUMN "SYSDBA"."top_cay_exam_test_info"."create_user_name" IS '创建人名字';
COMMENT ON COLUMN "SYSDBA"."top_cay_exam_test_info"."created_at" IS '创建时间';
COMMENT ON COLUMN "SYSDBA"."top_cay_exam_test_info"."test_answer" IS '试题答案';
COMMENT ON COLUMN "SYSDBA"."top_cay_exam_test_info"."test_option" IS '试题选项';
COMMENT ON COLUMN "SYSDBA"."top_cay_exam_test_info"."test_title" IS '试题标题';
COMMENT ON COLUMN "SYSDBA"."top_cay_exam_test_info"."type_id" IS '1单选 2判断';
COMMENT ON COLUMN "SYSDBA"."top_cay_exam_test_info"."updated_at" IS '更新时间';


CREATE TABLE "SYSDBA"."top_cay_exam_test_type"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "type_name" CHAR(200),
    UNIQUE("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_cay_exam_test_type" IS '试题类型';


CREATE TABLE "SYSDBA"."top_certificate"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "company_id" INT DEFAULT 0,
    "company_name" VARCHAR2(200) DEFAULT '',
    "industry" VARCHAR2(200) DEFAULT '',
    "specialty" VARCHAR2(200) DEFAULT '',
    "code" VARCHAR2(200) DEFAULT '',
    "level" VARCHAR2(200) DEFAULT '',
    "start" VARCHAR2(200) DEFAULT '',
    "ends" VARCHAR2(200) DEFAULT '',
    "status" TINYINT DEFAULT 0,
    "publicity_status" TINYINT DEFAULT 0,
    "publicity_time" DATE DEFAULT '',
    "notify_status" TINYINT DEFAULT 0,
    "notify_time" DATE DEFAULT '',
    "notify_summary" VARCHAR2(800) DEFAULT '',
    "image" VARCHAR2(800) DEFAULT '',
    "grading_id" INT DEFAULT 0,
    "check_date" DATE DEFAULT '',
    "publicity_id" INT DEFAULT 0,
    "city_id" INT DEFAULT 0,
    "dept_id" INT DEFAULT 0,
    "number" VARCHAR2(200) DEFAULT '',
    "printing_number" VARCHAR2(200) DEFAULT '',
    "area_id" INT DEFAULT 0,
    "area" VARCHAR2(500) DEFAULT '',
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_certificate" IS '企业证书信息表';
COMMENT ON COLUMN "SYSDBA"."top_certificate"."area_id" IS '区县id';
COMMENT ON COLUMN "SYSDBA"."top_certificate"."check_date" IS '审核通过日期';
COMMENT ON COLUMN "SYSDBA"."top_certificate"."code" IS '证书编号';
COMMENT ON COLUMN "SYSDBA"."top_certificate"."company_id" IS '企业id';
COMMENT ON COLUMN "SYSDBA"."top_certificate"."company_name" IS '企业名称';
COMMENT ON COLUMN "SYSDBA"."top_certificate"."ends" IS '有效期';
COMMENT ON COLUMN "SYSDBA"."top_certificate"."grading_id" IS '定级申请id';
COMMENT ON COLUMN "SYSDBA"."top_certificate"."image" IS '证书照片';
COMMENT ON COLUMN "SYSDBA"."top_certificate"."industry" IS '行业';
COMMENT ON COLUMN "SYSDBA"."top_certificate"."level" IS '证书等级';
COMMENT ON COLUMN "SYSDBA"."top_certificate"."notify_status" IS '公告状态';
COMMENT ON COLUMN "SYSDBA"."top_certificate"."notify_summary" IS '公告说明';
COMMENT ON COLUMN "SYSDBA"."top_certificate"."notify_time" IS '公告日期';
COMMENT ON COLUMN "SYSDBA"."top_certificate"."number" IS '自动排号1';
COMMENT ON COLUMN "SYSDBA"."top_certificate"."printing_number" IS '自动排号2';
COMMENT ON COLUMN "SYSDBA"."top_certificate"."publicity_id" IS '公示公告id';
COMMENT ON COLUMN "SYSDBA"."top_certificate"."publicity_status" IS '公示状态';
COMMENT ON COLUMN "SYSDBA"."top_certificate"."publicity_time" IS '公示日期';
COMMENT ON COLUMN "SYSDBA"."top_certificate"."specialty" IS '专业';
COMMENT ON COLUMN "SYSDBA"."top_certificate"."start" IS '制证日期';
COMMENT ON COLUMN "SYSDBA"."top_certificate"."status" IS '证书状态';


CREATE TABLE "SYSDBA"."top_city"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "name" VARCHAR2(200) DEFAULT '',
    "pcas" VARCHAR2(800) DEFAULT '',
    "status" TINYINT DEFAULT 0,
    "contacts" VARCHAR2(200) DEFAULT '',
    "tel" VARCHAR2(200) DEFAULT '',
    "address" VARCHAR2(200) DEFAULT '',
    "del_flag" TINYINT DEFAULT 0,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_city" IS '市应急局表';
COMMENT ON COLUMN "SYSDBA"."top_city"."address" IS '地址';
COMMENT ON COLUMN "SYSDBA"."top_city"."contacts" IS '联系人';
COMMENT ON COLUMN "SYSDBA"."top_city"."del_flag" IS '删除标记1删除';
COMMENT ON COLUMN "SYSDBA"."top_city"."name" IS '应急局名称';
COMMENT ON COLUMN "SYSDBA"."top_city"."pcas" IS '管辖范围';
COMMENT ON COLUMN "SYSDBA"."top_city"."status" IS '状态';
COMMENT ON COLUMN "SYSDBA"."top_city"."tel" IS '联系电话';


CREATE TABLE "SYSDBA"."top_city_user"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "username" VARCHAR2(200) DEFAULT '',
    "password" VARCHAR2(200) DEFAULT '',
    "salt" VARCHAR2(200) DEFAULT '',
    "name" VARCHAR2(200) DEFAULT '',
    "email" VARCHAR2(200) DEFAULT '',
    "mobile" VARCHAR2(200) DEFAULT '',
    "status" INT DEFAULT 0,
    "reg_time" DATETIME(6) DEFAULT '',
    "reg_ip" VARCHAR2(200) DEFAULT '',
    "city_id" INT DEFAULT 0,
    "department" TINYINT DEFAULT 0,
    "role" INT DEFAULT 0,
    "openid" VARCHAR2(200) DEFAULT '',
    "unionid" VARCHAR2(200) DEFAULT '',
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_city_user" IS '市应急局用户表';
COMMENT ON COLUMN "SYSDBA"."top_city_user"."city_id" IS '市应急局id';
COMMENT ON COLUMN "SYSDBA"."top_city_user"."department" IS '科室 1：基础科 2：危化科';
COMMENT ON COLUMN "SYSDBA"."top_city_user"."email" IS '邮箱';
COMMENT ON COLUMN "SYSDBA"."top_city_user"."mobile" IS '手机号';
COMMENT ON COLUMN "SYSDBA"."top_city_user"."name" IS '姓名';
COMMENT ON COLUMN "SYSDBA"."top_city_user"."password" IS '密码';
COMMENT ON COLUMN "SYSDBA"."top_city_user"."reg_ip" IS '注册ip';
COMMENT ON COLUMN "SYSDBA"."top_city_user"."reg_time" IS '注册时间';
COMMENT ON COLUMN "SYSDBA"."top_city_user"."role" IS '角色 0：普通角色 1：管理员 2：超级管理员';
COMMENT ON COLUMN "SYSDBA"."top_city_user"."salt" IS '密码盐';
COMMENT ON COLUMN "SYSDBA"."top_city_user"."status" IS '状态';
COMMENT ON COLUMN "SYSDBA"."top_city_user"."username" IS '用户名';


CREATE TABLE "SYSDBA"."top_company"
(
    "id" BIGINT IDENTITY(1, 1) NOT NULL,
    "areacode" VARCHAR(80) DEFAULT '' NOT NULL,
    "clrq" VARCHAR(80) DEFAULT '' NOT NULL,
    "djjgzwmc" VARCHAR(400) DEFAULT '' NOT NULL,
    "djjg" VARCHAR(200) DEFAULT '' NOT NULL,
    "djzt" VARCHAR(80) DEFAULT '' NOT NULL,
    "djztzwmc" VARCHAR(200) DEFAULT '' NOT NULL,
    "fddbr" VARCHAR(200) DEFAULT '' NOT NULL,
    "hyml" VARCHAR(200) DEFAULT '' NOT NULL,
    "hydm" VARCHAR(80) DEFAULT '' NOT NULL,
    "jyqxz" VARCHAR(80) DEFAULT '' NOT NULL,
    "jyfw" TEXT DEFAULT '' NOT NULL,
    "jyqxs" VARCHAR(80) DEFAULT '' NOT NULL,
    "scztlx" VARCHAR(80) DEFAULT '' NOT NULL,
    "sszb" DECIMAL(20,2) DEFAULT 0 NOT NULL,
    "scztlxzwmc" VARCHAR(200) DEFAULT '' NOT NULL,
    "tyshxydm" VARCHAR(200) DEFAULT '' NOT NULL,
    "xzqh" VARCHAR(200) DEFAULT '' NOT NULL,
    "zhrc_sjly" VARCHAR(200) DEFAULT '' NOT NULL,
    "ztmc" VARCHAR(800) DEFAULT '' NOT NULL,
    "zsszxzqh" VARCHAR(200) DEFAULT '' NOT NULL,
    "zczbbzzwmc" VARCHAR(200) DEFAULT '' NOT NULL,
    "ztsfdm" VARCHAR(200) DEFAULT '' NOT NULL,
    "zhrc_wyzj" VARCHAR(200) DEFAULT '' NOT NULL,
    "ztid" VARCHAR(200) DEFAULT '' NOT NULL,
    "zch" VARCHAR(200) DEFAULT '' NOT NULL,
    "zs" TEXT DEFAULT '' NOT NULL,
    "zhrc_rksj" BIGINT DEFAULT 0 NOT NULL,
    "zczb" DECIMAL(20,2) DEFAULT 0 NOT NULL,
    "create_time" BIGINT DEFAULT 0 NOT NULL,
    "update_time" BIGINT DEFAULT 0 NOT NULL,
    NOT CLUSTER PRIMARY KEY("id"),
    CHECK("create_time" >= 0)
    ,CHECK("zczb" >= 0)
    ,CHECK("sszb" >= 0)
    ,CHECK("zhrc_rksj" >= 0)
    ,CHECK("update_time" >= 0)) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_company" IS '专家回避单位信息表';
COMMENT ON COLUMN "SYSDBA"."top_company"."areacode" IS '行政区划代码';
COMMENT ON COLUMN "SYSDBA"."top_company"."clrq" IS '成立日期';
COMMENT ON COLUMN "SYSDBA"."top_company"."create_time" IS '创建时间';
COMMENT ON COLUMN "SYSDBA"."top_company"."djjg" IS '登记机关';
COMMENT ON COLUMN "SYSDBA"."top_company"."djjgzwmc" IS '登记机关(中文名称)';
COMMENT ON COLUMN "SYSDBA"."top_company"."djzt" IS '登记状态';
COMMENT ON COLUMN "SYSDBA"."top_company"."djztzwmc" IS '登记状态(中文名称)';
COMMENT ON COLUMN "SYSDBA"."top_company"."fddbr" IS '法定代表人(负责人)';
COMMENT ON COLUMN "SYSDBA"."top_company"."hydm" IS '行业代码';
COMMENT ON COLUMN "SYSDBA"."top_company"."hyml" IS '行业门类';
COMMENT ON COLUMN "SYSDBA"."top_company"."id" IS '自增主键id';
COMMENT ON COLUMN "SYSDBA"."top_company"."jyfw" IS '经营范围';
COMMENT ON COLUMN "SYSDBA"."top_company"."jyqxs" IS '经营(驻在)期限自';
COMMENT ON COLUMN "SYSDBA"."top_company"."jyqxz" IS '经营(驻在)期限至';
COMMENT ON COLUMN "SYSDBA"."top_company"."scztlx" IS '市场主体类型';
COMMENT ON COLUMN "SYSDBA"."top_company"."scztlxzwmc" IS '市场主体类型(中文名称)';
COMMENT ON COLUMN "SYSDBA"."top_company"."sszb" IS '实收资本';
COMMENT ON COLUMN "SYSDBA"."top_company"."tyshxydm" IS '统一社会信用代码';
COMMENT ON COLUMN "SYSDBA"."top_company"."update_time" IS '更新时间';
COMMENT ON COLUMN "SYSDBA"."top_company"."xzqh" IS '行政区划';
COMMENT ON COLUMN "SYSDBA"."top_company"."zch" IS '注册号';
COMMENT ON COLUMN "SYSDBA"."top_company"."zczb" IS '注册资本(金)(地区)';
COMMENT ON COLUMN "SYSDBA"."top_company"."zczbbzzwmc" IS '注册资本(金)币种(中文名称)';
COMMENT ON COLUMN "SYSDBA"."top_company"."zhrc_rksj" IS '智慧蓉城入库时间';
COMMENT ON COLUMN "SYSDBA"."top_company"."zhrc_sjly" IS '智慧蓉城数据来源';
COMMENT ON COLUMN "SYSDBA"."top_company"."zhrc_wyzj" IS '智慧蓉城唯一主键';
COMMENT ON COLUMN "SYSDBA"."top_company"."zs" IS '住所(地区)';
COMMENT ON COLUMN "SYSDBA"."top_company"."zsszxzqh" IS '住所所在行政区划';
COMMENT ON COLUMN "SYSDBA"."top_company"."ztid" IS '主体id';
COMMENT ON COLUMN "SYSDBA"."top_company"."ztmc" IS '主体名称';
COMMENT ON COLUMN "SYSDBA"."top_company"."ztsfdm" IS '主体身份代码';


CREATE OR REPLACE  INDEX "SYSDBA"."idx_tyshxydm" ON "SYSDBA"."top_company"("tyshxydm" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;
CREATE OR REPLACE  INDEX "SYSDBA"."idx_zch" ON "SYSDBA"."top_company"("zch" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;
CREATE OR REPLACE  INDEX "SYSDBA"."idx_ztid" ON "SYSDBA"."top_company"("ztid" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "SYSDBA"."top_company_ca"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "company_id" INT DEFAULT 0,
    "standard_id" INT DEFAULT 0,
    "level" VARCHAR2(200) DEFAULT '',
    "standard" VARCHAR2(200) DEFAULT '',
    "image" VARCHAR2(200) DEFAULT '',
    "code" VARCHAR2(200) DEFAULT '',
    "start" VARCHAR2(200) DEFAULT '',
    "ends" VARCHAR2(200) DEFAULT '',
    "create_user_id" VARCHAR2(200) DEFAULT '',
    "create_user_name" VARCHAR2(200) DEFAULT '',
    "create_time" DATETIME(6) DEFAULT '',
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_company_ca" IS '证书添加记录表';


CREATE TABLE "SYSDBA"."top_company_evaluate"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "company_id" INT DEFAULT 0,
    "company_name" VARCHAR2(200) DEFAULT '',
    "industry" VARCHAR2(200) DEFAULT '',
    "specialty" VARCHAR2(200) DEFAULT '',
    "date" DATE DEFAULT '',
    "is_publicity" TINYINT DEFAULT 0,
    "is_standard" TINYINT DEFAULT 0,
    "apply_level" VARCHAR2(200) DEFAULT '',
    "residence" VARCHAR2(800) DEFAULT '',
    "business_type" VARCHAR2(200) DEFAULT '',
    "government" VARCHAR2(400) DEFAULT '',
    "legal" VARCHAR2(200) DEFAULT '',
    "legal_mobile" VARCHAR2(200) DEFAULT '',
    "legal_fax" VARCHAR2(200) DEFAULT '',
    "contacts" VARCHAR2(200) DEFAULT '',
    "contacts_tel" VARCHAR2(200) DEFAULT '',
    "contacts_fax" VARCHAR2(200) DEFAULT '',
    "contacts_mobile" VARCHAR2(200) DEFAULT '',
    "contacts_email" VARCHAR2(800) DEFAULT '',
    "old_level" VARCHAR2(200) DEFAULT '',
    "group_name" VARCHAR2(200) DEFAULT '',
    "overview" VARCHAR2(4000) DEFAULT '',
    "accident" VARCHAR2(4000) DEFAULT '',
    "question" VARCHAR2(20000) DEFAULT '',
    "reform" VARCHAR2(20000) DEFAULT '',
    "conclusion" VARCHAR2(2000) DEFAULT '',
    "letter" VARCHAR2(2000) DEFAULT '',
    "create_user_id" INT DEFAULT 0,
    "create_user_name" VARCHAR2(200) DEFAULT '',
    "create_time" DATETIME(6) DEFAULT '',
    "status" TINYINT DEFAULT 0,
    "personnels" VARCHAR2(12000) DEFAULT '',
    "score" VARCHAR2(12000) DEFAULT '',
    "year" VARCHAR2(20) DEFAULT '',
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_company_evaluate" IS '企业自评表';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."accident" IS '安全事故情况';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."apply_level" IS '申请等级';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."business_type" IS '类型';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."company_id" IS '企业id';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."company_name" IS '企业名称';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."conclusion" IS '自评结论';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."contacts" IS '联系人';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."contacts_email" IS '联系人邮箱';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."contacts_fax" IS '联系人传真';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."contacts_mobile" IS '联系人手机号';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."contacts_tel" IS '联系人电话';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."date" IS '自评日期';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."government" IS '安全管理机构';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."group_name" IS '集团名称';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."industry" IS '行业';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."is_publicity" IS '是否在内部公示';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."is_standard" IS '是否申请定级';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."legal" IS '法人';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."legal_fax" IS '法人传真';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."legal_mobile" IS '法人电话';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."letter" IS '负责人承诺书';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."old_level" IS '曾经取得的标准化等级';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."overview" IS '企业概况';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."personnels" IS '评审人员';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."question" IS '问题和成效';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."reform" IS '整改情况';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."residence" IS '住所';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."score" IS '打分结果';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."specialty" IS '专业';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."status" IS '状态';
COMMENT ON COLUMN "SYSDBA"."top_company_evaluate"."year" IS '年度';


CREATE TABLE "SYSDBA"."top_company_info"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "user_id" INT DEFAULT 0,
    "name" VARCHAR2(400) DEFAULT '',
    "reg_address" VARCHAR2(400) DEFAULT '',
    "reg_address_info" VARCHAR2(400) DEFAULT '',
    "operate_address" VARCHAR2(400) DEFAULT '',
    "operate_address_info" VARCHAR2(400) DEFAULT '',
    "legal" VARCHAR2(200) DEFAULT '',
    "legal_mobile" VARCHAR2(200) DEFAULT '',
    "legal_email" VARCHAR2(400) DEFAULT '',
    "fax" VARCHAR2(200) DEFAULT '',
    "postal_code" VARCHAR2(200) DEFAULT '',
    "economy_sector" VARCHAR2(200) DEFAULT '',
    "industry" VARCHAR2(200) DEFAULT '',
    "specialty" VARCHAR2(200) DEFAULT '',
    "license_number" VARCHAR2(200) DEFAULT '',
    "license_start" DATE DEFAULT '',
    "license_end" DATE DEFAULT '',
    "economy_type" VARCHAR2(200) DEFAULT '',
    "enterprise_size" VARCHAR2(200) DEFAULT '',
    "reg_money" DECIMAL(22,0) DEFAULT 0,
    "manager" VARCHAR2(200) DEFAULT '',
    "manager_mobile" VARCHAR2(200) DEFAULT '',
    "manager_email" VARCHAR2(200) DEFAULT '',
    "revenue" DECIMAL(22,0) DEFAULT 0,
    "area" DECIMAL(22,0) DEFAULT 0,
    "personnel" SMALLINT DEFAULT 0,
    "personnel_full" SMALLINT DEFAULT 0,
    "personnel_part" SMALLINT DEFAULT 0,
    "personnel_special" SMALLINT DEFAULT 0,
    "group_name" VARCHAR2(400) DEFAULT '',
    "business" VARCHAR2(12000) DEFAULT '',
    "license" VARCHAR2(800) DEFAULT '',
    "aoc" VARCHAR2(800) DEFAULT '',
    "status" TINYINT DEFAULT 0,
    "date" DATE DEFAULT '',
    "standard_id" INT DEFAULT 0,
    "standard_name" VARCHAR2(400) DEFAULT '',
    "standard_level" VARCHAR2(200) DEFAULT '',
    "stand_status" TINYINT DEFAULT 0,
    "stand_date" DATE DEFAULT '',
    "ca_status" TINYINT DEFAULT 0,
    "ca_date" DATE DEFAULT '',
    "review_id" INT DEFAULT 0,
    "region" VARCHAR2(200) DEFAULT '',
    "fixed_asset" VARCHAR2(200) DEFAULT '',
    "st_date" DATE DEFAULT '',
    "check_user_id" INT DEFAULT 0,
    "check_user_name" VARCHAR2(200) DEFAULT '',
    "check_time" DATETIME(6) DEFAULT '',
    "business_type" VARCHAR2(200) DEFAULT '',
    "area_id" INT DEFAULT 0,
    "city_id" INT DEFAULT 0,
    "dept_id" INT DEFAULT 0,
    "industrial_park" VARCHAR2(200),
    "is_dust_explosion" VARCHAR2(40) DEFAULT '否',
    "is_ammonia_cold" VARCHAR2(40) DEFAULT '否',
    "is_hot_melting" VARCHAR2(40) DEFAULT '否',
    "is_light_industry" VARCHAR2(40) DEFAULT '否',
    "sector" VARCHAR2(200),
    "ammonia_use" VARCHAR2(200),
    "ammonia_usage" INT,
    "ammonia_storage_method" VARCHAR2(200),
    "ammonia_storage_capacity" INT,
    "gas_alarm_number" VARCHAR2(200),
    "limited_space_type" VARCHAR2(200),
    "blast_furnace_number" INT,
    "nonferrous_furnace_number" INT,
    "ferroalloy_furnace_number" INT,
    "soaring_furnace_number" INT,
    "phone" VARCHAR2(200),
    "mb_industry" VARCHAR2(200),
    "jd" DECIMAL(12,8),
    "wd" DECIMAL(12,8),
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_company_info" IS '企业详细信息表';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."ammonia_storage_capacity" IS '液氨储存量';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."ammonia_storage_method" IS '液氨储存方式';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."ammonia_usage" IS '液氨使用量';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."ammonia_use" IS '液氨的用途';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."aoc" IS '安全行政许可资料';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."area" IS '营业面积';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."area_id" IS '所属区县id';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."blast_furnace_number" IS '高炉数量';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."business" IS '经营范围';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."business_type" IS '经营类型';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."ca_date" IS '证书日期';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."ca_status" IS '证书状态';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."check_time" IS '审核时间';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."check_user_id" IS '审核人id';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."check_user_name" IS '审核人姓名';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."city_id" IS '所属市级id';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."date" IS '成立日期';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."dept_id" IS '所属部门';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."economy_sector" IS '国民经济行业';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."economy_type" IS '经济类型';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."enterprise_size" IS '企业规模';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."fax" IS '企业传真';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."ferroalloy_furnace_number" IS '铁合金矿热炉数量';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."fixed_asset" IS '固定资产';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."gas_alarm_number" IS '安装有气体泄露报警装置数量';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."group_name" IS '所属集团公司';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."industrial_park" IS '产业园区';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."industry" IS '行业';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."is_ammonia_cold" IS '是否涉氨制冷';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."is_dust_explosion" IS '是否粉尘涉爆';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."is_hot_melting" IS '是否涉高温熔融金属';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."is_light_industry" IS '是否涉轻工行业有限空间';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."jd" IS '经度';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."legal" IS '法人';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."legal_email" IS '法人邮箱';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."legal_mobile" IS '法人手机号';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."license" IS '营业执照';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."license_end" IS '信用代码结束日期';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."license_number" IS '统一社会信用代码';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."license_start" IS '信用代码开始日期';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."limited_space_type" IS '有限空间类型';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."manager" IS '安全管理人员';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."manager_email" IS '安全管理人员邮箱';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."manager_mobile" IS '安全管理人员联系方式';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."mb_industry" IS '行业/专业';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."name" IS '企业名称';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."nonferrous_furnace_number" IS '有色金属冶炼炉数量';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."operate_address" IS '生产经营地址';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."operate_address_info" IS '生产经营地址';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."personnel" IS '员工总数';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."personnel_full" IS '专职人数';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."personnel_part" IS '兼职人数';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."personnel_special" IS '特种作业人数';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."phone" IS '座机电话号码';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."postal_code" IS '邮政编码';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."reg_address" IS '注册地址';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."reg_address_info" IS '注册地址';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."reg_money" IS '注册资本';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."region" IS '所属行政区';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."revenue" IS '年营业收入';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."review_id" IS '现行评审标准id';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."sector" IS '所属行业';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."soaring_furnace_number" IS '冲天炉数量';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."specialty" IS '专业';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."st_date" IS '可申请定级日期';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."stand_date" IS '创标申请完成日期';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."stand_status" IS '创标申请状态';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."standard_id" IS '评审标准id';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."standard_level" IS '评审标准等级';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."standard_name" IS '评审标准名称';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."status" IS '状态';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."user_id" IS '用户id';
COMMENT ON COLUMN "SYSDBA"."top_company_info"."wd" IS '维度';


CREATE TABLE "SYSDBA"."top_company_info_apply"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "user_id" INT DEFAULT 0,
    "name" VARCHAR2(400) DEFAULT '',
    "reg_address" VARCHAR2(400) DEFAULT '',
    "reg_address_info" VARCHAR2(400) DEFAULT '',
    "operate_address" VARCHAR2(400) DEFAULT '',
    "operate_address_info" VARCHAR2(400) DEFAULT '',
    "legal" VARCHAR2(200) DEFAULT '',
    "legal_mobile" VARCHAR2(200) DEFAULT '',
    "legal_email" VARCHAR2(400) DEFAULT '',
    "fax" VARCHAR2(200) DEFAULT '',
    "postal_code" VARCHAR2(200) DEFAULT '',
    "economy_sector" VARCHAR2(200),
    "industry" VARCHAR2(200) DEFAULT '',
    "specialty" VARCHAR2(200) DEFAULT '',
    "license_number" VARCHAR2(200) DEFAULT '',
    "license_start" DATE DEFAULT '',
    "license_end" DATE DEFAULT '',
    "economy_type" VARCHAR2(200) DEFAULT '',
    "enterprise_size" VARCHAR2(200) DEFAULT '',
    "reg_money" DECIMAL(22,6),
    "manager" VARCHAR2(200) DEFAULT '',
    "manager_mobile" VARCHAR2(200) DEFAULT '',
    "manager_email" VARCHAR2(200) DEFAULT '',
    "revenue" DECIMAL(22,6) DEFAULT 0,
    "area" DECIMAL(22,6) DEFAULT 0,
    "personnel" SMALLINT DEFAULT 0,
    "personnel_full" SMALLINT DEFAULT 0,
    "personnel_part" SMALLINT DEFAULT 0,
    "personnel_special" SMALLINT DEFAULT 0,
    "group_name" VARCHAR2(400) DEFAULT '',
    "business" VARCHAR2(12000) DEFAULT '',
    "license" VARCHAR2(800) DEFAULT '',
    "aoc" VARCHAR2(800) DEFAULT '',
    "status" TINYINT DEFAULT 0,
    "date" DATE DEFAULT '',
    "region" VARCHAR2(200) DEFAULT '',
    "fixed_asset" VARCHAR2(200) DEFAULT '',
    "apply_time" DATETIME(6) DEFAULT '',
    "check_user_id" INT DEFAULT 0,
    "check_user_name" VARCHAR2(200) DEFAULT '',
    "check_time" DATETIME(6) DEFAULT '',
    "reason" VARCHAR(1600),
    "area_id" INT DEFAULT 0,
    "city_id" INT DEFAULT 0,
    "dept_id" INT DEFAULT 1,
    "business_type" VARCHAR2(800) DEFAULT '',
    "industrial_park" VARCHAR2(200),
    "is_dust_explosion" VARCHAR2(40) DEFAULT '否',
    "is_ammonia_cold" VARCHAR2(40) DEFAULT '否',
    "is_hot_melting" VARCHAR2(40) DEFAULT '否',
    "is_light_industry" VARCHAR2(40) DEFAULT '否',
    "sector" VARCHAR2(200),
    "ammonia_use" VARCHAR2(200),
    "ammonia_usage" INT,
    "ammonia_storage_method" VARCHAR2(200),
    "ammonia_storage_capacity" INT,
    "gas_alarm_number" VARCHAR2(200),
    "limited_space_type" VARCHAR2(200),
    "blast_furnace_number" INT,
    "nonferrous_furnace_number" INT,
    "ferroalloy_furnace_number" INT,
    "soaring_furnace_number" INT,
    "phone" VARCHAR2(200),
    "mb_industry" VARCHAR2(200),
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_company_info_apply" IS '企业资料认证申请表';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."ammonia_storage_capacity" IS '液氨储存量';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."ammonia_storage_method" IS '液氨储存方式';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."ammonia_usage" IS '液氨使用量';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."ammonia_use" IS '液氨的用途';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."aoc" IS '安全行政许可资料';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."apply_time" IS '申请时间';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."area" IS '营业面积';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."area_id" IS '所属区县id';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."blast_furnace_number" IS '高炉数量';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."business" IS '经营范围';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."business_type" IS '经营类型';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."check_time" IS '审核时间';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."check_user_id" IS '审核人';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."city_id" IS '所属市级id';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."date" IS '成立日期';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."dept_id" IS '所属部门id';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."economy_sector" IS '国民经济行业';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."economy_type" IS '经济类型';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."enterprise_size" IS '企业规模';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."fax" IS '企业传真';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."ferroalloy_furnace_number" IS '铁合金矿热炉数量';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."fixed_asset" IS '固定资产';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."gas_alarm_number" IS '安装有气体泄露报警装置数量';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."group_name" IS '所属集团公司';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."industrial_park" IS '产业园区';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."industry" IS '行业';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."is_ammonia_cold" IS '是否涉氨制冷';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."is_dust_explosion" IS '是否粉尘涉爆';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."is_hot_melting" IS '是否涉高温熔融金属';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."is_light_industry" IS '是否涉轻工行业有限空间';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."legal" IS '法人';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."legal_email" IS '法人邮箱';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."legal_mobile" IS '法人手机号';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."license" IS '营业执照';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."license_end" IS '信用代码结束日期';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."license_number" IS '统一社会信用代码';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."license_start" IS '信用代码开始日期';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."limited_space_type" IS '有限空间类型';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."manager" IS '安全管理人员';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."manager_email" IS '安全管理人员邮箱';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."manager_mobile" IS '安全管理人员联系方式';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."mb_industry" IS '行业/专业';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."name" IS '企业名称';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."nonferrous_furnace_number" IS '有色金属冶炼炉数量';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."operate_address" IS '生产经营地址';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."operate_address_info" IS '生产经营地址';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."personnel" IS '员工总数';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."personnel_full" IS '专职人数';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."personnel_part" IS '兼职人数';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."personnel_special" IS '特种作业人数';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."phone" IS '座机电话号码';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."postal_code" IS '邮政编码';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."reg_address" IS '注册地址';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."reg_address_info" IS '注册地址';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."reg_money" IS '注册资本';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."region" IS '所属行政区';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."revenue" IS '年营业收入';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."sector" IS '所属行业';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."soaring_furnace_number" IS '冲天炉数量';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."specialty" IS '专业';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."status" IS '状态';
COMMENT ON COLUMN "SYSDBA"."top_company_info_apply"."user_id" IS '用户id';


CREATE TABLE "SYSDBA"."top_company_param"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "name" VARCHAR2(80) NOT NULL,
    "param_value" VARCHAR2(200) NOT NULL,
    "company_id" INT NOT NULL,
    "category" VARCHAR2(80),
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_company_param" IS '粉尘分类及数量';
COMMENT ON COLUMN "SYSDBA"."top_company_param"."category" IS '分类';
COMMENT ON COLUMN "SYSDBA"."top_company_param"."company_id" IS '关联主键';
COMMENT ON COLUMN "SYSDBA"."top_company_param"."id" IS 'id';
COMMENT ON COLUMN "SYSDBA"."top_company_param"."name" IS '名称';
COMMENT ON COLUMN "SYSDBA"."top_company_param"."param_value" IS '值';


CREATE TABLE "SYSDBA"."top_company_param_apply"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "name" VARCHAR2(200) NOT NULL,
    "param_value" VARCHAR2(200) NOT NULL,
    "company_id" INT NOT NULL,
    "category" VARCHAR2(80),
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_company_param_apply" IS '公司参数待审核表';
COMMENT ON COLUMN "SYSDBA"."top_company_param_apply"."category" IS '分类';
COMMENT ON COLUMN "SYSDBA"."top_company_param_apply"."company_id" IS '关联主键';
COMMENT ON COLUMN "SYSDBA"."top_company_param_apply"."name" IS '名称';
COMMENT ON COLUMN "SYSDBA"."top_company_param_apply"."param_value" IS '值';


CREATE TABLE "SYSDBA"."top_company_reform"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "grading_id" INT,
    "reform" VARCHAR2(400),
    "reform_date" DATE,
    "reform_before_info" VARCHAR2(4000),
    "reform_before_files" VARCHAR2(4000),
    "reform_affter_info" VARCHAR2(4000),
    "reform_affter_files" VARCHAR2(4000),
    "remark" VARCHAR2(4000),
    "update_user_id" INT,
    "update_user_name" VARCHAR2(200),
    "update_time" DATETIME(6),
    "task_id" INT,
    "company_id" INT,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_company_reform" IS '企业整改详情';
COMMENT ON COLUMN "SYSDBA"."top_company_reform"."company_id" IS '企业id';
COMMENT ON COLUMN "SYSDBA"."top_company_reform"."grading_id" IS '流程id';
COMMENT ON COLUMN "SYSDBA"."top_company_reform"."reform" IS '整改项';
COMMENT ON COLUMN "SYSDBA"."top_company_reform"."reform_affter_files" IS '整改后情况图片';
COMMENT ON COLUMN "SYSDBA"."top_company_reform"."reform_affter_info" IS '整改后情况';
COMMENT ON COLUMN "SYSDBA"."top_company_reform"."reform_before_files" IS '整改前情况图片';
COMMENT ON COLUMN "SYSDBA"."top_company_reform"."reform_before_info" IS '整改前情况';
COMMENT ON COLUMN "SYSDBA"."top_company_reform"."reform_date" IS '整改日期';
COMMENT ON COLUMN "SYSDBA"."top_company_reform"."remark" IS '备注';
COMMENT ON COLUMN "SYSDBA"."top_company_reform"."task_id" IS '任务id';
COMMENT ON COLUMN "SYSDBA"."top_company_reform"."update_time" IS '最后更新时间';
COMMENT ON COLUMN "SYSDBA"."top_company_reform"."update_user_id" IS '更新人id';
COMMENT ON COLUMN "SYSDBA"."top_company_reform"."update_user_name" IS '更新人';


CREATE TABLE "SYSDBA"."top_company_review"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "company_id" INT DEFAULT 0,
    "accessment_id" INT DEFAULT 0,
    "accessment_name" VARCHAR2(200) DEFAULT '',
    "date" VARCHAR2(200) DEFAULT '',
    "element" VARCHAR2(800) DEFAULT '',
    "create_time" VARCHAR2(200) DEFAULT '',
    "is_del" TINYINT DEFAULT 0,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_company_review" IS '企业现行评审标准主表';
COMMENT ON COLUMN "SYSDBA"."top_company_review"."accessment_id" IS '评审标准id';
COMMENT ON COLUMN "SYSDBA"."top_company_review"."accessment_name" IS '评审标准名称';
COMMENT ON COLUMN "SYSDBA"."top_company_review"."company_id" IS '企业id';
COMMENT ON COLUMN "SYSDBA"."top_company_review"."create_time" IS '添加时间';
COMMENT ON COLUMN "SYSDBA"."top_company_review"."date" IS '启用日期';
COMMENT ON COLUMN "SYSDBA"."top_company_review"."element" IS '要素名称';


CREATE TABLE "SYSDBA"."top_company_review_content"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "main_id" INT DEFAULT 0,
    "content" VARCHAR2(24000) DEFAULT '',
    "score" DECIMAL(10,2) DEFAULT 0,
    "cycle" VARCHAR2(200) DEFAULT '',
    "ask" VARCHAR2(24000) DEFAULT '',
    "standards" TEXT DEFAULT '',
    "method" VARCHAR2(24000) DEFAULT '',
    "sort" SMALLINT DEFAULT 0,
    "weight" DECIMAL(22,2) DEFAULT 0,
    "files" VARCHAR2(16000) DEFAULT '',
    "element_id" INT DEFAULT 0,
    "element_ids" VARCHAR2(2000) DEFAULT '',
    "is_del" TINYINT DEFAULT 0,
    "file_remark" VARCHAR2(24000),
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_company_review_content" IS '企业评审标准内容表';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content"."ask" IS '基本规范要求';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content"."content" IS '指标名称';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content"."cycle" IS '填报周期';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content"."element_id" IS '要素id';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content"."element_ids" IS '要素id集合';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content"."file_remark" IS '自评/评审描述';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content"."files" IS '材料模板';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content"."main_id" IS '评审标准id';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content"."method" IS '评分方式';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content"."score" IS '指标分值';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content"."sort" IS '排序号';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content"."standards" IS '达标标准';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content"."weight" IS '权重';


CREATE OR REPLACE  INDEX "SYSDBA"."contents_id" ON "SYSDBA"."top_company_review_content"("main_id" ASC,"element_id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "SYSDBA"."top_company_review_content_list"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "review_id" INT DEFAULT 0,
    "element_id" INT DEFAULT 0,
    "element_ids" VARCHAR2(2000) DEFAULT '',
    "content_id" INT DEFAULT 0,
    "time" VARCHAR2(200) DEFAULT '',
    "sub_files" VARCHAR2(20000) DEFAULT '',
    "hazard" VARCHAR2(200) DEFAULT '',
    "is_sub" TINYINT DEFAULT 0,
    "is_expire" TINYINT DEFAULT 0,
    "company_id" INT DEFAULT 0,
    "sub_time" VARCHAR2(200) DEFAULT '',
    "sub_user_id" INT DEFAULT 0,
    "sub_user_name" VARCHAR2(200) DEFAULT '',
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_company_review_content_list" IS '企业评审标准材料上报表';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content_list"."company_id" IS '企业id';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content_list"."content_id" IS '上报内容id';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content_list"."element_id" IS '要素id';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content_list"."element_ids" IS '要素id集合';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content_list"."hazard" IS '隐患排查';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content_list"."is_expire" IS '是否过期';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content_list"."is_sub" IS '是否上报';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content_list"."review_id" IS '评审表id';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content_list"."sub_files" IS '上报材料';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content_list"."sub_time" IS '上报时间';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content_list"."sub_user_id" IS '上报人';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content_list"."sub_user_name" IS '上报人';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content_list"."time" IS '时间';


CREATE OR REPLACE  INDEX "SYSDBA"."list_id" ON "SYSDBA"."top_company_review_content_list"("review_id" ASC,"element_id" ASC,"content_id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "SYSDBA"."top_company_review_content_score"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "evaluate_id" INT DEFAULT 0,
    "element_id" INT DEFAULT 0,
    "element_ids" VARCHAR2(4000) DEFAULT '',
    "content_id" INT DEFAULT 0,
    "score" DECIMAL(10,2) DEFAULT 0,
    "reform" VARCHAR2(12000) DEFAULT '',
    "miss" DECIMAL(10,2) DEFAULT 0,
    "resion" VARCHAR2(2000) DEFAULT '',
    "summary" VARCHAR2(2000) DEFAULT '',
    "sumscore" DECIMAL(10,2) DEFAULT 0,
    "deduct" VARCHAR2(12) DEFAULT '',
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_company_review_content_score" IS '企业自评要素打分表';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content_score"."content_id" IS '评审内容id';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content_score"."element_id" IS '评审要素id';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content_score"."element_ids" IS '评审要素id集合';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content_score"."evaluate_id" IS '自评主表id';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content_score"."miss" IS '缺项分值';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content_score"."reform" IS '所需整改内容';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content_score"."resion" IS '原因';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content_score"."score" IS '自评打分';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content_score"."summary" IS '扣分说明';
COMMENT ON COLUMN "SYSDBA"."top_company_review_content_score"."sumscore" IS '总分';


CREATE TABLE "SYSDBA"."top_company_review_element"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "main_id" INT DEFAULT 0,
    "name" VARCHAR2(400) DEFAULT '',
    "sum_score" DECIMAL(22,2) DEFAULT 0,
    "score" DECIMAL(22,2) DEFAULT 0,
    "mark" SMALLINT DEFAULT 0,
    "sort" SMALLINT DEFAULT 0,
    "weight" DECIMAL(22,6) DEFAULT 0,
    "pid" INT DEFAULT 0,
    "pids" VARCHAR2(2000) DEFAULT '',
    "is_del" TINYINT DEFAULT 0,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_company_review_element" IS '企业评审标准要素表';
COMMENT ON COLUMN "SYSDBA"."top_company_review_element"."main_id" IS '评审表id';
COMMENT ON COLUMN "SYSDBA"."top_company_review_element"."mark" IS '合格线';
COMMENT ON COLUMN "SYSDBA"."top_company_review_element"."name" IS '要素名称';
COMMENT ON COLUMN "SYSDBA"."top_company_review_element"."pid" IS '父级id';
COMMENT ON COLUMN "SYSDBA"."top_company_review_element"."pids" IS '父级id集合';
COMMENT ON COLUMN "SYSDBA"."top_company_review_element"."score" IS '分数';
COMMENT ON COLUMN "SYSDBA"."top_company_review_element"."sort" IS '排序号';
COMMENT ON COLUMN "SYSDBA"."top_company_review_element"."sum_score" IS '总分';
COMMENT ON COLUMN "SYSDBA"."top_company_review_element"."weight" IS '权重';


CREATE OR REPLACE  INDEX "SYSDBA"."element_ids" ON "SYSDBA"."top_company_review_element"("main_id" ASC,"pid" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "SYSDBA"."top_company_user"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "username" VARCHAR2(200) DEFAULT '',
    "password" VARCHAR2(200) DEFAULT '',
    "email" VARCHAR2(400) DEFAULT '',
    "mobile" VARCHAR2(44) DEFAULT '',
    "status" TINYINT DEFAULT 0,
    "reg_time" DATETIME(6) DEFAULT '',
    "reg_ip" VARCHAR2(128) DEFAULT '',
    "salt" VARCHAR2(200) DEFAULT '',
    "company_id" INT DEFAULT 0,
    "unionid" VARCHAR2(200) DEFAULT '',
    "openid_chat" VARCHAR2(200) DEFAULT '',
    "openid_dev" VARCHAR2(200) DEFAULT '',
    "openid" VARCHAR2(200) DEFAULT '',
    "name" VARCHAR2(200) DEFAULT '',
    NOT CLUSTER PRIMARY KEY("id"),
    UNIQUE("username")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_company_user" IS '企业用户表';
COMMENT ON COLUMN "SYSDBA"."top_company_user"."email" IS '邮箱';
COMMENT ON COLUMN "SYSDBA"."top_company_user"."mobile" IS '手机号';
COMMENT ON COLUMN "SYSDBA"."top_company_user"."password" IS '密码';
COMMENT ON COLUMN "SYSDBA"."top_company_user"."reg_ip" IS '注册ip';
COMMENT ON COLUMN "SYSDBA"."top_company_user"."reg_time" IS '注册时间';
COMMENT ON COLUMN "SYSDBA"."top_company_user"."salt" IS '密码盐';
COMMENT ON COLUMN "SYSDBA"."top_company_user"."status" IS '状态 1：正常  0：禁用';
COMMENT ON COLUMN "SYSDBA"."top_company_user"."username" IS '用户名';


CREATE OR REPLACE  INDEX "SYSDBA"."company_username" ON "SYSDBA"."top_company_user"("username" ASC,"mobile" ASC,"openid" ASC,"unionid" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "SYSDBA"."top_department"
(
    "dept_id" INT IDENTITY(3, 1) NOT NULL,
    "dept_code" VARCHAR2(200) DEFAULT '',
    "dept_name" VARCHAR2(200) DEFAULT '',
    "dept_tel" VARCHAR2(200) DEFAULT '',
    "dept_fax" VARCHAR2(200) DEFAULT '',
    "dept_address" VARCHAR2(200) DEFAULT '',
    "pid" INT DEFAULT 0,
    NOT CLUSTER PRIMARY KEY("dept_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_department" IS '部门表';


CREATE TABLE "SYSDBA"."top_discuss_group"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "tasks_id" INT DEFAULT 0,
    "title" VARCHAR2(400) DEFAULT '',
    "status" TINYINT DEFAULT 0,
    "date" DATE,
    "last_content" VARCHAR2(200) DEFAULT '',
    "last_time" DATETIME(6) DEFAULT '',
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "SYSDBA"."top_discuss_group_content"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "group_id" INT DEFAULT 0,
    "user_id" INT DEFAULT 0,
    "user_type" VARCHAR2(200) DEFAULT '',
    "content" VARCHAR2(2000) DEFAULT '',
    "image" VARCHAR2(2000) DEFAULT '',
    "time" DATETIME(6) DEFAULT '',
    "user_name" VARCHAR2(200) DEFAULT '',
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "SYSDBA"."top_discuss_group_user"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "user_id" INT,
    "user_type" VARCHAR2(200),
    "readsum" SMALLINT,
    "group_id" INT DEFAULT 0,
    "user_name" VARCHAR2(200) DEFAULT '',
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "SYSDBA"."top_economy_sector"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "code" VARCHAR2(200) DEFAULT '',
    "name" VARCHAR2(200) DEFAULT '',
    "pid" INT DEFAULT 0,
    "sort" SMALLINT DEFAULT 0,
    "level" TINYINT DEFAULT 1,
    "enabled" INT DEFAULT 1,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_economy_sector" IS '国民经济行业配置表';
COMMENT ON COLUMN "SYSDBA"."top_economy_sector"."code" IS '配置编号';
COMMENT ON COLUMN "SYSDBA"."top_economy_sector"."enabled" IS '是否启用';
COMMENT ON COLUMN "SYSDBA"."top_economy_sector"."level" IS '层级';
COMMENT ON COLUMN "SYSDBA"."top_economy_sector"."name" IS '名称';
COMMENT ON COLUMN "SYSDBA"."top_economy_sector"."pid" IS '上级id';
COMMENT ON COLUMN "SYSDBA"."top_economy_sector"."sort" IS '排序号';


CREATE TABLE "SYSDBA"."top_economy_type"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "code" VARCHAR2(200) DEFAULT '',
    "name" VARCHAR2(200) DEFAULT '',
    "pid" INT DEFAULT 0,
    "sort" SMALLINT DEFAULT 0,
    "level" TINYINT DEFAULT 1,
    "enabled" INT DEFAULT 1,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_economy_type" IS '经济类型配置表';
COMMENT ON COLUMN "SYSDBA"."top_economy_type"."code" IS '编号';
COMMENT ON COLUMN "SYSDBA"."top_economy_type"."enabled" IS '是否启用';
COMMENT ON COLUMN "SYSDBA"."top_economy_type"."level" IS '层级';
COMMENT ON COLUMN "SYSDBA"."top_economy_type"."name" IS '名称';
COMMENT ON COLUMN "SYSDBA"."top_economy_type"."pid" IS '上级id';
COMMENT ON COLUMN "SYSDBA"."top_economy_type"."sort" IS '排序号';


CREATE TABLE "SYSDBA"."top_evidence_main"
(
    "id" BIGINT IDENTITY(1, 1) NOT NULL,
    "task_id" VARCHAR(400) DEFAULT '' NOT NULL,
    "hidden_type" VARCHAR(800) DEFAULT '' NOT NULL,
    "hidden_pos" VARCHAR(800) DEFAULT '' NOT NULL,
    "hidden_cont" TEXT DEFAULT '' NOT NULL,
    "create_time" BIGINT DEFAULT 0 NOT NULL,
    "update_time" BIGINT DEFAULT 0 NOT NULL,
    NOT CLUSTER PRIMARY KEY("id"),
    CHECK(update_time >= 0)
    ,CHECK(create_time >= 0)) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_evidence_main" IS '现场取证主表，存储隐患关联任务及核心信息';
COMMENT ON COLUMN "SYSDBA"."top_evidence_main"."create_time" IS '创建时间';
COMMENT ON COLUMN "SYSDBA"."top_evidence_main"."hidden_cont" IS '隐患内容';
COMMENT ON COLUMN "SYSDBA"."top_evidence_main"."hidden_pos" IS '隐患点位';
COMMENT ON COLUMN "SYSDBA"."top_evidence_main"."hidden_type" IS '隐患类型';
COMMENT ON COLUMN "SYSDBA"."top_evidence_main"."id" IS '自增主键ID';
COMMENT ON COLUMN "SYSDBA"."top_evidence_main"."task_id" IS '任务ID，标识该隐患所属评审任务';
COMMENT ON COLUMN "SYSDBA"."top_evidence_main"."update_time" IS '更新时间';


CREATE OR REPLACE  INDEX "SYSDBA"."idx_task_id" ON "SYSDBA"."top_evidence_main"("task_id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "SYSDBA"."top_evidence_photo"
(
    "photo_id" BIGINT IDENTITY(1, 1) NOT NULL,
    "main_id" BIGINT DEFAULT 0 NOT NULL,
    "photo_path" VARCHAR(2000) DEFAULT '' NOT NULL,
    NOT CLUSTER PRIMARY KEY("photo_id"),
    FOREIGN KEY("main_id") REFERENCES "SYSDBA"."top_evidence_main"("id") WITH INDEX ) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_evidence_photo" IS '现场取证照片子表，存储隐患对应多张照片路径';
COMMENT ON COLUMN "SYSDBA"."top_evidence_photo"."main_id" IS '关联主表ID，指向 top_evidence_main 表的 id';
COMMENT ON COLUMN "SYSDBA"."top_evidence_photo"."photo_id" IS '照片自增主键ID';
COMMENT ON COLUMN "SYSDBA"."top_evidence_photo"."photo_path" IS '取证照片存储路径，存储图片文件路径或URL';


CREATE OR REPLACE  INDEX "SYSDBA"."idx_main_id" ON "SYSDBA"."top_evidence_photo"("main_id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "SYSDBA"."top_expert"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "username" VARCHAR2(200) DEFAULT '',
    "password" VARCHAR2(200) DEFAULT '',
    "salt" VARCHAR2(200) DEFAULT '',
    "name" VARCHAR2(200) DEFAULT '',
    "email" VARCHAR2(800) DEFAULT '',
    "mobile" VARCHAR2(200) DEFAULT '',
    "status" TINYINT DEFAULT 0,
    "reg_time" DATETIME(6) DEFAULT '',
    "reg_ip" VARCHAR2(200) DEFAULT '',
    "org_id" INT DEFAULT 0,
    "openid" VARCHAR2(200) DEFAULT '',
    "unionid" VARCHAR2(200) DEFAULT '',
    "expert_id" INT,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_expert" IS '评审专家表';
COMMENT ON COLUMN "SYSDBA"."top_expert"."email" IS '邮箱';
COMMENT ON COLUMN "SYSDBA"."top_expert"."expert_id" IS '专家系统的专家id';
COMMENT ON COLUMN "SYSDBA"."top_expert"."mobile" IS '手机号';
COMMENT ON COLUMN "SYSDBA"."top_expert"."name" IS '姓名';
COMMENT ON COLUMN "SYSDBA"."top_expert"."org_id" IS '评审单位id';
COMMENT ON COLUMN "SYSDBA"."top_expert"."password" IS '密码';
COMMENT ON COLUMN "SYSDBA"."top_expert"."reg_ip" IS '注册ip';
COMMENT ON COLUMN "SYSDBA"."top_expert"."reg_time" IS '注册时间';
COMMENT ON COLUMN "SYSDBA"."top_expert"."salt" IS '密码盐';
COMMENT ON COLUMN "SYSDBA"."top_expert"."status" IS '状态';
COMMENT ON COLUMN "SYSDBA"."top_expert"."username" IS '用户名';


CREATE OR REPLACE  INDEX "SYSDBA"."expert_username" ON "SYSDBA"."top_expert"("username" ASC,"mobile" ASC,"openid" ASC,"unionid" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;
CREATE OR REPLACE  INDEX "SYSDBA"."username" ON "SYSDBA"."top_expert"("username" ASC,"openid" ASC,"unionid" ASC,"mobile" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "SYSDBA"."top_expert_avoid"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "expert_id" INT DEFAULT 0,
    "company_id" INT DEFAULT 0,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_expert_avoid" IS '专家回避企业表';
COMMENT ON COLUMN "SYSDBA"."top_expert_avoid"."company_id" IS '回避企业id';
COMMENT ON COLUMN "SYSDBA"."top_expert_avoid"."expert_id" IS '专家id';


CREATE TABLE "SYSDBA"."top_expert_idle"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "expert_id" INT DEFAULT 0,
    "date" DATE DEFAULT '',
    "status" TINYINT DEFAULT 1,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_expert_idle" IS '评审专家报名表';
COMMENT ON COLUMN "SYSDBA"."top_expert_idle"."date" IS '报名日期';
COMMENT ON COLUMN "SYSDBA"."top_expert_idle"."expert_id" IS '专家id';
COMMENT ON COLUMN "SYSDBA"."top_expert_idle"."status" IS '状态';


CREATE TABLE "SYSDBA"."top_expert_info"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "expert_id" INT,
    "nation" VARCHAR2(200) DEFAULT '',
    "sex" VARCHAR2(200) DEFAULT '',
    "birthday" VARCHAR2(200) DEFAULT '',
    "qq" VARCHAR2(200) DEFAULT '',
    "address" VARCHAR2(400),
    "head" VARCHAR2(800) DEFAULT '',
    "school" VARCHAR2(200) DEFAULT '',
    "speciality" VARCHAR2(200),
    "education" VARCHAR2(200) DEFAULT '',
    "employer" VARCHAR2(200) DEFAULT '',
    "position" VARCHAR2(200) DEFAULT '',
    "work_date" VARCHAR2(200),
    "position_date" VARCHAR2(200) DEFAULT '',
    "professional" VARCHAR2(200) DEFAULT '',
    "professional_number" VARCHAR2(200) DEFAULT '',
    "secure" VARCHAR2(200) DEFAULT '',
    "secure_number" VARCHAR2(200) DEFAULT '',
    "reg_secure_number" VARCHAR2(200) DEFAULT '',
    "other_number" VARCHAR2(2000) DEFAULT '',
    "major" VARCHAR2(12000) DEFAULT '',
    "resume" VARCHAR2(12000) DEFAULT '',
    "employ_date" VARCHAR2(200) DEFAULT '',
    "offer_info" VARCHAR2(12000) DEFAULT '',
    "status" TINYINT DEFAULT 0,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_expert_info" IS '评审专家详细信息表';
COMMENT ON COLUMN "SYSDBA"."top_expert_info"."address" IS '现住址';
COMMENT ON COLUMN "SYSDBA"."top_expert_info"."birthday" IS '出生日期';
COMMENT ON COLUMN "SYSDBA"."top_expert_info"."education" IS '学历';
COMMENT ON COLUMN "SYSDBA"."top_expert_info"."employ_date" IS '聘用日期';
COMMENT ON COLUMN "SYSDBA"."top_expert_info"."employer" IS '现工作单位';
COMMENT ON COLUMN "SYSDBA"."top_expert_info"."expert_id" IS '专家id';
COMMENT ON COLUMN "SYSDBA"."top_expert_info"."head" IS '头像';
COMMENT ON COLUMN "SYSDBA"."top_expert_info"."major" IS '擅长专业';
COMMENT ON COLUMN "SYSDBA"."top_expert_info"."nation" IS '民族';
COMMENT ON COLUMN "SYSDBA"."top_expert_info"."offer_info" IS '受聘情况';
COMMENT ON COLUMN "SYSDBA"."top_expert_info"."other_number" IS '其他证书编号';
COMMENT ON COLUMN "SYSDBA"."top_expert_info"."position" IS '职务';
COMMENT ON COLUMN "SYSDBA"."top_expert_info"."position_date" IS '从事安全生产工作时间';
COMMENT ON COLUMN "SYSDBA"."top_expert_info"."professional" IS '专业技术职称';
COMMENT ON COLUMN "SYSDBA"."top_expert_info"."professional_number" IS '职称证书编号';
COMMENT ON COLUMN "SYSDBA"."top_expert_info"."qq" IS 'QQ';
COMMENT ON COLUMN "SYSDBA"."top_expert_info"."reg_secure_number" IS '注册安全工程师证书编号';
COMMENT ON COLUMN "SYSDBA"."top_expert_info"."resume" IS '个人学习及工作简历';
COMMENT ON COLUMN "SYSDBA"."top_expert_info"."school" IS '学校';
COMMENT ON COLUMN "SYSDBA"."top_expert_info"."secure" IS '安全评价师资格等级';
COMMENT ON COLUMN "SYSDBA"."top_expert_info"."secure_number" IS '安全评价师证书编号';
COMMENT ON COLUMN "SYSDBA"."top_expert_info"."sex" IS '性别';
COMMENT ON COLUMN "SYSDBA"."top_expert_info"."speciality" IS '专业';
COMMENT ON COLUMN "SYSDBA"."top_expert_info"."status" IS '审核状态';
COMMENT ON COLUMN "SYSDBA"."top_expert_info"."work_date" IS '参加工作时间';


CREATE TABLE "SYSDBA"."top_files"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "model" VARCHAR2(200) DEFAULT '',
    "filename" VARCHAR2(800) DEFAULT '',
    "filetype" VARCHAR2(200) DEFAULT '',
    "fileext" VARCHAR2(200) DEFAULT '',
    "filesize" VARCHAR2(200) DEFAULT '',
    "filepath" VARCHAR2(800) DEFAULT '',
    "create_user" VARCHAR2(200) DEFAULT '',
    "create_user_name" VARCHAR2(200) DEFAULT '',
    "create_time" DATETIME(6) DEFAULT '',
    "code" VARCHAR2(128) DEFAULT '',
    "formal" TINYINT DEFAULT 0,
    NOT CLUSTER PRIMARY KEY("id"),
    UNIQUE("code")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_files" IS '附件上传表';
COMMENT ON COLUMN "SYSDBA"."top_files"."code" IS '文件唯一标识';
COMMENT ON COLUMN "SYSDBA"."top_files"."formal" IS '是否正式文件';


CREATE TABLE "SYSDBA"."top_folder"
(
    "folder_id" INT IDENTITY(31, 1) NOT NULL,
    "folder_name" VARCHAR2(200) DEFAULT '',
    "sort" SMALLINT DEFAULT 0,
    "create_user_id" INT DEFAULT 0,
    "create_user_name" VARCHAR2(200) DEFAULT '',
    "create_time" DATETIME(6) DEFAULT '',
    "pid" INT DEFAULT 0,
    "is_file" TINYINT DEFAULT 0,
    NOT CLUSTER PRIMARY KEY("folder_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_folder" IS '文件夹表';


CREATE TABLE "SYSDBA"."top_folder_files"
(
    "id" INT IDENTITY(16, 1) NOT NULL,
    "folder_id" INT DEFAULT 0,
    "title" VARCHAR2(200) DEFAULT '',
    "date" DATE DEFAULT '',
    "code" VARCHAR2(200) DEFAULT '',
    "dept_name" VARCHAR2(200) DEFAULT '',
    "status" TINYINT DEFAULT 0,
    "version" VARCHAR2(200) DEFAULT '',
    "version_summary" VARCHAR2(200) DEFAULT '',
    "describe" TEXT DEFAULT '',
    "sort" SMALLINT DEFAULT 0,
    "files" VARCHAR2(800) DEFAULT '',
    "edit_files" VARCHAR2(800) DEFAULT '',
    "type" VARCHAR2(20) DEFAULT '',
    "create_user_id" INT DEFAULT 0,
    "create_user_name" VARCHAR2(200) DEFAULT '',
    "create_time" DATETIME(6) DEFAULT '',
    "del" TINYINT DEFAULT 0,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_folder_files" IS '文件夹附件管理';
COMMENT ON COLUMN "SYSDBA"."top_folder_files"."code" IS '制度编号';
COMMENT ON COLUMN "SYSDBA"."top_folder_files"."date" IS '发布日期';
COMMENT ON COLUMN "SYSDBA"."top_folder_files"."dept_name" IS '编制单位';
COMMENT ON COLUMN "SYSDBA"."top_folder_files"."describe" IS '文档简介';
COMMENT ON COLUMN "SYSDBA"."top_folder_files"."edit_files" IS '可编辑附件';
COMMENT ON COLUMN "SYSDBA"."top_folder_files"."files" IS '附件';
COMMENT ON COLUMN "SYSDBA"."top_folder_files"."folder_id" IS '目录id';
COMMENT ON COLUMN "SYSDBA"."top_folder_files"."status" IS '是否发布';
COMMENT ON COLUMN "SYSDBA"."top_folder_files"."title" IS '文件名称';
COMMENT ON COLUMN "SYSDBA"."top_folder_files"."version" IS '版本号';
COMMENT ON COLUMN "SYSDBA"."top_folder_files"."version_summary" IS '版本说明';


CREATE TABLE "SYSDBA"."top_folder_role"
(
    "folder_id" INT NOT NULL,
    "role_type" VARCHAR2(200) DEFAULT '',
    "pos_type" VARCHAR2(200) DEFAULT '',
    "pos_id" INT DEFAULT 0) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_folder_role" IS '文件夹权限表';


CREATE TABLE "SYSDBA"."top_grading"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "company_id" INT DEFAULT 0,
    "company_name" VARCHAR2(200) DEFAULT '',
    "address" VARCHAR2(400) DEFAULT '',
    "company_code" VARCHAR2(200) DEFAULT '',
    "legal" VARCHAR2(200) DEFAULT '',
    "legal_mobile" VARCHAR2(200) DEFAULT '',
    "legal_email" VARCHAR2(200) DEFAULT '',
    "manager" VARCHAR2(200) DEFAULT '',
    "manager_mobile" VARCHAR2(200) DEFAULT '',
    "manager_email" VARCHAR2(200) DEFAULT '',
    "type" VARCHAR2(200) DEFAULT '',
    "nature" VARCHAR2(200) DEFAULT '',
    "content" VARCHAR2(2000) DEFAULT '',
    "date" DATE DEFAULT '',
    "status" TINYINT DEFAULT 0,
    "apply_user_id" INT DEFAULT 0,
    "apply_user_name" VARCHAR2(200) DEFAULT '',
    "apply_time" DATETIME(6) DEFAULT '',
    "reform_status" TINYINT DEFAULT 0,
    "file1" VARCHAR2(2000) DEFAULT '',
    "file2" VARCHAR2(2000) DEFAULT '',
    "file3" VARCHAR2(2000) DEFAULT '',
    "file4" VARCHAR2(2000) DEFAULT '',
    "file5" VARCHAR2(2000) DEFAULT '',
    "file6" VARCHAR2(2000) DEFAULT '',
    "file7" VARCHAR2(2000) DEFAULT '',
    "file8" VARCHAR2(2000) DEFAULT '',
    "file9" VARCHAR2(2000) DEFAULT '',
    "file10" VARCHAR2(2000) DEFAULT '',
    "file11" VARCHAR2(2000) DEFAULT '',
    "file12" VARCHAR2(2000) DEFAULT '',
    "file13" VARCHAR2(2000) DEFAULT '',
    "file14" VARCHAR2(2000) DEFAULT '',
    "file15" VARCHAR2(2000) DEFAULT '',
    "file16" VARCHAR2(2000) DEFAULT '',
    "file17" VARCHAR2(2000) DEFAULT '',
    "file18" VARCHAR2(2000) DEFAULT '',
    "file19" VARCHAR2(2000) DEFAULT '',
    "file20" VARCHAR2(2000) DEFAULT '',
    "advisory" VARCHAR2(200) DEFAULT '',
    "level" VARCHAR2(200) DEFAULT '',
    "prcs_id" INT DEFAULT 0,
    "prcs_name" VARCHAR2(200) DEFAULT '',
    "industry" VARCHAR2(200) DEFAULT '',
    "specialty" VARCHAR2(200) DEFAULT '',
    "area_id" INT DEFAULT 0,
    "city_id" INT DEFAULT 0,
    "dept_id" INT DEFAULT 0,
    "enterprise_size" VARCHAR2(200) DEFAULT '',
    "legal_tel" VARCHAR2(50),
    "contacts_tel" CHAR(10),
    "contacts_fax" CHAR(10),
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_grading" IS '定级申请主表';
COMMENT ON COLUMN "SYSDBA"."top_grading"."address" IS '地址';
COMMENT ON COLUMN "SYSDBA"."top_grading"."advisory" IS '第三方机构名称';
COMMENT ON COLUMN "SYSDBA"."top_grading"."apply_time" IS '申请时间';
COMMENT ON COLUMN "SYSDBA"."top_grading"."apply_user_id" IS '申请人';
COMMENT ON COLUMN "SYSDBA"."top_grading"."apply_user_name" IS '申请人';
COMMENT ON COLUMN "SYSDBA"."top_grading"."area_id" IS '区县应急局id';
COMMENT ON COLUMN "SYSDBA"."top_grading"."city_id" IS '市应急局id';
COMMENT ON COLUMN "SYSDBA"."top_grading"."company_code" IS '统一社会信用代码';
COMMENT ON COLUMN "SYSDBA"."top_grading"."company_id" IS '企业id';
COMMENT ON COLUMN "SYSDBA"."top_grading"."company_name" IS '企业名称';
COMMENT ON COLUMN "SYSDBA"."top_grading"."contacts_fax" IS '联系人传真';
COMMENT ON COLUMN "SYSDBA"."top_grading"."contacts_tel" IS '联系人电话';
COMMENT ON COLUMN "SYSDBA"."top_grading"."content" IS '申请意见';
COMMENT ON COLUMN "SYSDBA"."top_grading"."date" IS '申请日期';
COMMENT ON COLUMN "SYSDBA"."top_grading"."dept_id" IS '部门id';
COMMENT ON COLUMN "SYSDBA"."top_grading"."enterprise_size" IS '企业规模';
COMMENT ON COLUMN "SYSDBA"."top_grading"."industry" IS '行业';
COMMENT ON COLUMN "SYSDBA"."top_grading"."legal" IS '法人';
COMMENT ON COLUMN "SYSDBA"."top_grading"."legal_email" IS '法人邮箱';
COMMENT ON COLUMN "SYSDBA"."top_grading"."legal_mobile" IS '法人手机号';
COMMENT ON COLUMN "SYSDBA"."top_grading"."legal_tel" IS '负责人电话';
COMMENT ON COLUMN "SYSDBA"."top_grading"."level" IS '申请等级';
COMMENT ON COLUMN "SYSDBA"."top_grading"."manager" IS '联系人';
COMMENT ON COLUMN "SYSDBA"."top_grading"."manager_email" IS '联系人邮箱';
COMMENT ON COLUMN "SYSDBA"."top_grading"."manager_mobile" IS '联系人手机号';
COMMENT ON COLUMN "SYSDBA"."top_grading"."nature" IS '创建性质';
COMMENT ON COLUMN "SYSDBA"."top_grading"."prcs_id" IS '当前步骤id';
COMMENT ON COLUMN "SYSDBA"."top_grading"."prcs_name" IS '当前步骤名称';
COMMENT ON COLUMN "SYSDBA"."top_grading"."specialty" IS '专业';
COMMENT ON COLUMN "SYSDBA"."top_grading"."status" IS '状态';
COMMENT ON COLUMN "SYSDBA"."top_grading"."type" IS '申请类型';


CREATE TABLE "SYSDBA"."top_grading_approval"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "grading_id" INT DEFAULT 0,
    "prcs_id" INT DEFAULT 0,
    "prcs_name" VARCHAR2(200) DEFAULT '',
    "status" TINYINT DEFAULT 0,
    "create_user_id" INT DEFAULT 0,
    "create_user_name" VARCHAR2(200) DEFAULT '',
    "create_time" DATETIME(6) DEFAULT '',
    "receive_user_id" INT DEFAULT 0,
    "receive_user_name" VARCHAR2(200) DEFAULT '',
    "receive_time" DATETIME(6) DEFAULT '',
    "end_user_id" INT DEFAULT 0,
    "end_user_name" VARCHAR2(200) DEFAULT '',
    "end_time" DATETIME(6) DEFAULT '',
    "params" VARCHAR2(2000) DEFAULT '',
    "check_content" VARCHAR2(2000) DEFAULT '',
    "check_files" VARCHAR2(8000) DEFAULT '',
    "remark" VARCHAR2(800) DEFAULT '',
    "status_name" VARCHAR2(200) DEFAULT '',
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_grading_approval" IS '定级申请审核记录表';
COMMENT ON COLUMN "SYSDBA"."top_grading_approval"."check_content" IS '审批意见';
COMMENT ON COLUMN "SYSDBA"."top_grading_approval"."check_files" IS '审批附件';
COMMENT ON COLUMN "SYSDBA"."top_grading_approval"."create_time" IS '转交时间';
COMMENT ON COLUMN "SYSDBA"."top_grading_approval"."create_user_id" IS '转交人';
COMMENT ON COLUMN "SYSDBA"."top_grading_approval"."end_time" IS '办结时间';
COMMENT ON COLUMN "SYSDBA"."top_grading_approval"."end_user_id" IS '办结人';
COMMENT ON COLUMN "SYSDBA"."top_grading_approval"."grading_id" IS '流程id';
COMMENT ON COLUMN "SYSDBA"."top_grading_approval"."params" IS '步骤参数';
COMMENT ON COLUMN "SYSDBA"."top_grading_approval"."prcs_id" IS '步骤id';
COMMENT ON COLUMN "SYSDBA"."top_grading_approval"."prcs_name" IS '步骤名称';
COMMENT ON COLUMN "SYSDBA"."top_grading_approval"."receive_time" IS '接收时间';
COMMENT ON COLUMN "SYSDBA"."top_grading_approval"."receive_user_id" IS '接收人';
COMMENT ON COLUMN "SYSDBA"."top_grading_approval"."remark" IS '备注';
COMMENT ON COLUMN "SYSDBA"."top_grading_approval"."status" IS '状态 1未接收  2已接收  5驳回  7审批通过';
COMMENT ON COLUMN "SYSDBA"."top_grading_approval"."status_name" IS '状态名称';


CREATE TABLE "SYSDBA"."top_grading_record"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "company_id" INT DEFAULT 0,
    "company_name" VARCHAR2(200) DEFAULT '',
    "address" VARCHAR2(400) DEFAULT '',
    "company_code" VARCHAR2(200) DEFAULT '',
    "legal" VARCHAR2(200) DEFAULT '',
    "legal_mobile" VARCHAR2(200) DEFAULT '',
    "legal_email" VARCHAR2(200) DEFAULT '',
    "manager" VARCHAR2(200) DEFAULT '',
    "manager_mobile" VARCHAR2(200) DEFAULT '',
    "manager_email" VARCHAR2(200) DEFAULT '',
    "type" VARCHAR2(200) DEFAULT '',
    "content" VARCHAR2(2000) DEFAULT '',
    "date" DATE DEFAULT '',
    "status" TINYINT DEFAULT 0,
    "apply_user_id" INT DEFAULT 0,
    "apply_user_name" VARCHAR2(200) DEFAULT '',
    "apply_time" DATETIME(6) DEFAULT '',
    "file" VARCHAR2(2000) DEFAULT '',
    "level" VARCHAR2(200) DEFAULT '',
    "industry" VARCHAR2(200) DEFAULT '',
    "specialty" VARCHAR2(200) DEFAULT '',
    "area_id" INT DEFAULT 0,
    "city_id" INT DEFAULT 0,
    "dept_id" INT DEFAULT 0,
    "enterprise_size" VARCHAR2(200) DEFAULT '',
    "check_user_id" INT DEFAULT 0,
    "check_user_name" VARCHAR2(200) DEFAULT '',
    "check_time" VARCHAR2(200) DEFAULT '',
    "check_remark" VARCHAR2(8000) DEFAULT '',
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_grading_record" IS '一、二级备案记录表';
COMMENT ON COLUMN "SYSDBA"."top_grading_record"."address" IS '地址';
COMMENT ON COLUMN "SYSDBA"."top_grading_record"."apply_time" IS '申请时间';
COMMENT ON COLUMN "SYSDBA"."top_grading_record"."apply_user_id" IS '申请人';
COMMENT ON COLUMN "SYSDBA"."top_grading_record"."apply_user_name" IS '申请人';
COMMENT ON COLUMN "SYSDBA"."top_grading_record"."area_id" IS '区县应急局id';
COMMENT ON COLUMN "SYSDBA"."top_grading_record"."city_id" IS '市应急局id';
COMMENT ON COLUMN "SYSDBA"."top_grading_record"."company_code" IS '统一社会信用代码';
COMMENT ON COLUMN "SYSDBA"."top_grading_record"."company_id" IS '企业id';
COMMENT ON COLUMN "SYSDBA"."top_grading_record"."company_name" IS '企业名称';
COMMENT ON COLUMN "SYSDBA"."top_grading_record"."content" IS '申请意见';
COMMENT ON COLUMN "SYSDBA"."top_grading_record"."date" IS '申请日期';
COMMENT ON COLUMN "SYSDBA"."top_grading_record"."dept_id" IS '部门id';
COMMENT ON COLUMN "SYSDBA"."top_grading_record"."enterprise_size" IS '企业规模';
COMMENT ON COLUMN "SYSDBA"."top_grading_record"."industry" IS '行业';
COMMENT ON COLUMN "SYSDBA"."top_grading_record"."legal" IS '法人';
COMMENT ON COLUMN "SYSDBA"."top_grading_record"."legal_email" IS '法人邮箱';
COMMENT ON COLUMN "SYSDBA"."top_grading_record"."legal_mobile" IS '法人手机号';
COMMENT ON COLUMN "SYSDBA"."top_grading_record"."level" IS '申请等级';
COMMENT ON COLUMN "SYSDBA"."top_grading_record"."manager" IS '联系人';
COMMENT ON COLUMN "SYSDBA"."top_grading_record"."manager_email" IS '联系人邮箱';
COMMENT ON COLUMN "SYSDBA"."top_grading_record"."manager_mobile" IS '联系人手机号';
COMMENT ON COLUMN "SYSDBA"."top_grading_record"."specialty" IS '专业';
COMMENT ON COLUMN "SYSDBA"."top_grading_record"."status" IS '状态';
COMMENT ON COLUMN "SYSDBA"."top_grading_record"."type" IS '申请类型';


CREATE TABLE "SYSDBA"."top_grading_reform"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "grading_id" INT DEFAULT 0,
    "company_id" INT DEFAULT 0,
    "tasks_id" INT DEFAULT 0,
    "status" TINYINT DEFAULT 0,
    "term" DATE DEFAULT '',
    "create_user_id" INT DEFAULT 0,
    "create_user_name" VARCHAR2(200) DEFAULT '',
    "create_time" DATETIME(6) DEFAULT '',
    "files" VARCHAR2(2000) DEFAULT '',
    "remark" VARCHAR2(2000) DEFAULT '',
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_grading_reform" IS '评审整改任务表';
COMMENT ON COLUMN "SYSDBA"."top_grading_reform"."company_id" IS '企业id';
COMMENT ON COLUMN "SYSDBA"."top_grading_reform"."grading_id" IS '定级申请表id';
COMMENT ON COLUMN "SYSDBA"."top_grading_reform"."status" IS '整改状态';
COMMENT ON COLUMN "SYSDBA"."top_grading_reform"."tasks_id" IS '评审任务id';
COMMENT ON COLUMN "SYSDBA"."top_grading_reform"."term" IS '整改期限';


CREATE TABLE "SYSDBA"."top_grading_reform_detail"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "reform_id" INT DEFAULT 0,
    "content_id" INT DEFAULT 0,
    "date" DATE DEFAULT '',
    "summary" VARCHAR2(8000) DEFAULT '',
    "files" VARCHAR2(8000) DEFAULT '',
    "content" VARCHAR2(8000) DEFAULT '',
    "photo" VARCHAR2(8000) DEFAULT '',
    "remark" VARCHAR2(2000) DEFAULT '',
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_grading_reform_detail" IS '整改项目明细表';
COMMENT ON COLUMN "SYSDBA"."top_grading_reform_detail"."content" IS '整改后内容';
COMMENT ON COLUMN "SYSDBA"."top_grading_reform_detail"."content_id" IS '内容id';
COMMENT ON COLUMN "SYSDBA"."top_grading_reform_detail"."date" IS '整改时间';
COMMENT ON COLUMN "SYSDBA"."top_grading_reform_detail"."files" IS '整改前图片';
COMMENT ON COLUMN "SYSDBA"."top_grading_reform_detail"."photo" IS '整改后图片';
COMMENT ON COLUMN "SYSDBA"."top_grading_reform_detail"."reform_id" IS '整改表id';
COMMENT ON COLUMN "SYSDBA"."top_grading_reform_detail"."remark" IS '备注';
COMMENT ON COLUMN "SYSDBA"."top_grading_reform_detail"."summary" IS '整改前情况';


CREATE TABLE "SYSDBA"."top_industrial_park"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "code" VARCHAR2(200) DEFAULT '',
    "name" VARCHAR2(200) DEFAULT '',
    "pid" INT DEFAULT 0,
    "sort" SMALLINT DEFAULT 0,
    "level" TINYINT DEFAULT 1,
    "enabled" INT DEFAULT 1,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "SYSDBA"."top_industry"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "code" VARCHAR2(200) DEFAULT '',
    "name" VARCHAR2(200) DEFAULT '',
    "pid" INT DEFAULT 0,
    "sort" SMALLINT DEFAULT 0,
    "enabled" INT DEFAULT 1,
    "level" TINYINT DEFAULT 1,
    "department" INT DEFAULT 0,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_industry" IS '行业配置表';
COMMENT ON COLUMN "SYSDBA"."top_industry"."code" IS '行业编码';
COMMENT ON COLUMN "SYSDBA"."top_industry"."department" IS '科室';
COMMENT ON COLUMN "SYSDBA"."top_industry"."enabled" IS '是否启用';
COMMENT ON COLUMN "SYSDBA"."top_industry"."level" IS '层级';
COMMENT ON COLUMN "SYSDBA"."top_industry"."name" IS '行业名称';
COMMENT ON COLUMN "SYSDBA"."top_industry"."pid" IS '上级id';
COMMENT ON COLUMN "SYSDBA"."top_industry"."sort" IS '排序号';


CREATE TABLE "SYSDBA"."top_lists_config"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "type" INT DEFAULT 0,
    "model" VARCHAR2(200) DEFAULT '',
    "title" VARCHAR2(200) DEFAULT '',
    "tables" VARCHAR2(200) DEFAULT '',
    "orders" VARCHAR2(200) DEFAULT '',
    "manager" VARCHAR2(800) DEFAULT '',
    "global" TEXT DEFAULT '',
    "fields" TEXT DEFAULT '',
    "create_user_id" VARCHAR2(200) DEFAULT '',
    "create_user_name" VARCHAR2(200) DEFAULT '',
    "create_time" VARCHAR2(200) DEFAULT '',
    "is_del" INT DEFAULT 0,
    "tabs_type" VARCHAR2(200) DEFAULT '',
    "tabs_position" VARCHAR2(200) DEFAULT '',
    "children" VARCHAR2(1020) DEFAULT '',
    "dept_field" VARCHAR2(200) DEFAULT '',
    "auth" TEXT DEFAULT '',
    "user_field" VARCHAR2(200) DEFAULT '',
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_lists_config" IS '列表组件配置表';
COMMENT ON COLUMN "SYSDBA"."top_lists_config"."auth" IS '列表权限配置';
COMMENT ON COLUMN "SYSDBA"."top_lists_config"."user_field" IS '用户权限字段';


CREATE TABLE "SYSDBA"."top_log"
(
    "id" INT IDENTITY(21, 1) NOT NULL,
    "user_id" INT DEFAULT 0,
    "user_name" VARCHAR2(200) DEFAULT '',
    "time" DATETIME(6) DEFAULT '',
    "type" SMALLINT DEFAULT 0,
    "remark" VARCHAR2(800) DEFAULT '',
    "ip" VARCHAR2(80) DEFAULT '',
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "SYSDBA"."top_login_app"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "name" VARCHAR2(200) DEFAULT '',
    "appid" VARCHAR2(200) DEFAULT '',
    "appkey" VARCHAR2(200) DEFAULT '',
    "redirect_uri" VARCHAR2(200) DEFAULT '',
    "create_user_id" VARCHAR2(200) DEFAULT '',
    "create_user_name" VARCHAR2(200) DEFAULT '',
    "create_time" VARCHAR2(200) DEFAULT '',
    "tp_appid" VARCHAR2(200) DEFAULT '',
    "tp_appkey" VARCHAR2(200) DEFAULT '',
    "domain" VARCHAR2(800) DEFAULT '',
    "param" VARCHAR2(1020) DEFAULT '',
    "del" INT DEFAULT 0,
    "domains" VARCHAR2(800) DEFAULT '',
    "is_del" TINYINT DEFAULT 0,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_login_app" IS '开放平台配置表';
COMMENT ON COLUMN "SYSDBA"."top_login_app"."appid" IS '自动生成appid';
COMMENT ON COLUMN "SYSDBA"."top_login_app"."appkey" IS '自动生成appkey';
COMMENT ON COLUMN "SYSDBA"."top_login_app"."create_time" IS '添加时间';
COMMENT ON COLUMN "SYSDBA"."top_login_app"."create_user_id" IS '添加人';
COMMENT ON COLUMN "SYSDBA"."top_login_app"."create_user_name" IS '添加人';
COMMENT ON COLUMN "SYSDBA"."top_login_app"."domains" IS '域名地址';
COMMENT ON COLUMN "SYSDBA"."top_login_app"."name" IS '系统名称';
COMMENT ON COLUMN "SYSDBA"."top_login_app"."redirect_uri" IS '绑定跳转链接地址';
COMMENT ON COLUMN "SYSDBA"."top_login_app"."tp_appid" IS '对接appid';
COMMENT ON COLUMN "SYSDBA"."top_login_app"."tp_appkey" IS '对接appkey';


CREATE TABLE "SYSDBA"."top_login_app_user"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "login_app_id" VARCHAR2(200) DEFAULT '',
    "user_id" VARCHAR2(200) DEFAULT '',
    "byname" VARCHAR2(200) DEFAULT '',
    "bind_byname" VARCHAR2(200) DEFAULT '',
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_login_app_user" IS '开放平台用户绑定关系表';
COMMENT ON COLUMN "SYSDBA"."top_login_app_user"."bind_byname" IS '关联第三方登录名';
COMMENT ON COLUMN "SYSDBA"."top_login_app_user"."byname" IS '登录名';
COMMENT ON COLUMN "SYSDBA"."top_login_app_user"."login_app_id" IS '系统id';
COMMENT ON COLUMN "SYSDBA"."top_login_app_user"."user_id" IS '用户id';


CREATE TABLE "SYSDBA"."top_menu"
(
    "menu_id" INT IDENTITY(15, 1) NOT NULL,
    "menu_code" VARCHAR2(200) DEFAULT '' NOT NULL,
    "menu_name" VARCHAR2(400) DEFAULT '' NOT NULL,
    "menu_url" VARCHAR2(2000) DEFAULT '' NOT NULL,
    "image" VARCHAR2(400) DEFAULT '' NOT NULL,
    "is_sys" INT DEFAULT 0 NOT NULL,
    "icon" VARCHAR2(200) DEFAULT '' NOT NULL,
    "icon_color" VARCHAR2(60) DEFAULT '' NOT NULL,
    "pid" INT DEFAULT 0 NOT NULL,
    NOT CLUSTER PRIMARY KEY("menu_id"),
    CHECK("is_sys" >= 0)) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_menu" IS '系统菜单表';
COMMENT ON COLUMN "SYSDBA"."top_menu"."icon" IS '菜单图标';
COMMENT ON COLUMN "SYSDBA"."top_menu"."icon_color" IS '图标颜色';
COMMENT ON COLUMN "SYSDBA"."top_menu"."image" IS '图片名';
COMMENT ON COLUMN "SYSDBA"."top_menu"."is_sys" IS '系统内置菜单(0-否,1-是)';
COMMENT ON COLUMN "SYSDBA"."top_menu"."menu_code" IS '菜单项代码';
COMMENT ON COLUMN "SYSDBA"."top_menu"."menu_id" IS '菜单ID';
COMMENT ON COLUMN "SYSDBA"."top_menu"."menu_name" IS '菜单名称';
COMMENT ON COLUMN "SYSDBA"."top_menu"."menu_url" IS '菜单路径';
COMMENT ON COLUMN "SYSDBA"."top_menu"."pid" IS '上级菜单id';


CREATE TABLE "SYSDBA"."top_message"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "user_type" VARCHAR2(200) DEFAULT '',
    "user_id" INT DEFAULT 0,
    "user_name" VARCHAR2(200) DEFAULT '',
    "sms_type" VARCHAR2(200) DEFAULT '',
    "sms_content" VARCHAR2(800) DEFAULT '',
    "sms_url" VARCHAR2(1200) DEFAULT '',
    "sms_time" DATETIME(6) DEFAULT '',
    "is_read" TINYINT DEFAULT 0,
    "read_time" DATETIME(6) DEFAULT '',
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "SYSDBA"."top_notify"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "type" INT DEFAULT 0,
    "title" VARCHAR2(800),
    "date" VARCHAR2(200) DEFAULT '',
    "summary" VARCHAR2(2000) DEFAULT '',
    "content" TEXT DEFAULT '',
    "files" VARCHAR2(8000) DEFAULT '',
    "create_user_id" VARCHAR2(200) DEFAULT '',
    "create_user_name" VARCHAR2(200) DEFAULT '',
    "create_time" VARCHAR2(200) DEFAULT '',
    "is_show" TINYINT DEFAULT 0,
    "is_del" TINYINT DEFAULT 0,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_notify" IS '通知公告列表';


CREATE TABLE "SYSDBA"."top_notify_type"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "name" VARCHAR2(400) DEFAULT '',
    "sort" INT DEFAULT 0,
    "is_del" TINYINT DEFAULT 0,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_notify_type" IS '通知公告类型表';


CREATE TABLE "SYSDBA"."top_org"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "name" VARCHAR2(200) DEFAULT '',
    "address" VARCHAR2(400) DEFAULT '',
    "contract" VARCHAR2(200) DEFAULT '',
    "tel" VARCHAR2(200) DEFAULT '',
    "is_del" TINYINT DEFAULT 0,
    "status" TINYINT DEFAULT 1,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_org" IS '评审单位表';
COMMENT ON COLUMN "SYSDBA"."top_org"."address" IS '地址';
COMMENT ON COLUMN "SYSDBA"."top_org"."contract" IS '联系人';
COMMENT ON COLUMN "SYSDBA"."top_org"."name" IS '单位名称';
COMMENT ON COLUMN "SYSDBA"."top_org"."tel" IS '联系电话';


CREATE TABLE "SYSDBA"."top_org_tasks"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "grading_id" INT DEFAULT 0,
    "company_id" INT DEFAULT 0,
    "org_id" INT DEFAULT 0,
    "status" TINYINT DEFAULT 0,
    "remark" VARCHAR2(2000) DEFAULT '',
    "date" DATE DEFAULT '',
    "receive_user_id" INT DEFAULT 0,
    "receive_user_name" VARCHAR(32752) DEFAULT '',
    "receive_time" DATETIME(6) DEFAULT '',
    "company_name" VARCHAR2(200) DEFAULT '',
    "review_id" INT DEFAULT 0,
    "review_name" VARCHAR2(400) DEFAULT '',
    "baogao" TEXT DEFAULT '',
    "personnel" INT DEFAULT 0,
    "exam_num" INT DEFAULT 0,
    "exam_sign" VARCHAR2(200) DEFAULT '',
    "address" VARCHAR2(800) DEFAULT '',
    "code" VARCHAR2(200) DEFAULT '',
    "reform" VARCHAR2(12000),
    "city_id" INT DEFAULT 0,
    "dept_id" INT DEFAULT 0,
    "result_status" TINYINT DEFAULT 0,
    "video_id" VARCHAR2(50),
    "score" DECIMAL(22,2),
    "industry" VARCHAR(50),
    "video_id1" VARCHAR2(50),
    "video_id2" VARCHAR2(50),
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_org_tasks" IS '评审任务表';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks"."address" IS '企业地址';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks"."code" IS '任务单号';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks"."company_id" IS '企业id';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks"."company_name" IS '企业名称';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks"."date" IS '计划评审日期';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks"."exam_num" IS '笔试人数';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks"."exam_sign" IS '笔试签名';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks"."grading_id" IS '评审申请单id';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks"."org_id" IS '评审单位id';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks"."reform" IS '现场评审不符合项(必须整改项)';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks"."remark" IS '备注';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks"."review_id" IS '评审标准id';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks"."review_name" IS '评审标准名称';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks"."status" IS '评审任务状态';


CREATE TABLE "SYSDBA"."top_org_tasks_appeal"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "task_id" INT,
    "company_id" INT,
    "remark" TEXT,
    "status" INT,
    "reply" TEXT,
    "create_time" DATETIME(6),
    "company_user_id" INT,
    "reply_user_id" INT,
    "grading_id" INT DEFAULT 0,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_org_tasks_appeal" IS '评审企业申述表';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_appeal"."company_id" IS '企业ID';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_appeal"."company_user_id" IS '申述人';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_appeal"."create_time" IS '申请时间';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_appeal"."grading_id" IS '定级申请id';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_appeal"."remark" IS '申述内容';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_appeal"."reply" IS '回复内容';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_appeal"."reply_user_id" IS '回复人';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_appeal"."status" IS '0 待审核 ，1 已处理';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_appeal"."task_id" IS '任务表';


CREATE TABLE "SYSDBA"."top_org_tasks_element"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "tasks_id" INT DEFAULT 0,
    "element_id" INT DEFAULT 0,
    "element_name" VARCHAR2(200) DEFAULT '',
    "expert_id" INT DEFAULT 0,
    "expert_name" VARCHAR2(200) DEFAULT '',
    "total" SMALLINT DEFAULT 0,
    "score" SMALLINT DEFAULT 0,
    "mark" SMALLINT DEFAULT 0,
    "status" TINYINT DEFAULT 0,
    "miss" SMALLINT DEFAULT 0,
    "weight" DECIMAL(22,6),
    "rate" SMALLINT DEFAULT 0,
    "deduct" SMALLINT DEFAULT 0,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_org_tasks_element" IS '评审要素分配表';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_element"."deduct" IS '扣分';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_element"."element_id" IS '要素id';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_element"."element_name" IS '要素名称';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_element"."expert_id" IS '专家id';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_element"."expert_name" IS '专家姓名';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_element"."mark" IS '合格线';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_element"."miss" IS '缺项分值';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_element"."rate" IS '得分率';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_element"."score" IS '得分';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_element"."status" IS '状态';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_element"."tasks_id" IS '评审任务id';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_element"."total" IS '总分';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_element"."weight" IS '权重';


CREATE TABLE "SYSDBA"."top_org_tasks_experts"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "tasks_id" INT DEFAULT 0,
    "expert_id" INT DEFAULT 0,
    "expert_name" VARCHAR2(200) DEFAULT '',
    "mobile" VARCHAR2(200) DEFAULT '',
    "position" VARCHAR2(200) DEFAULT '',
    "sepc" VARCHAR2(200) DEFAULT '',
    "status" TINYINT DEFAULT 0,
    "pbwc_id" INT DEFAULT 0,
    "pbwc_name" VARCHAR2(200) DEFAULT '',
    "element_id" VARCHAR2(2000) DEFAULT '',
    "element_name" VARCHAR2(12000) DEFAULT '',
    "position_id" TINYINT DEFAULT 0,
    "reason" VARCHAR2(200) DEFAULT '',
    "is_cay_expert" TINYINT DEFAULT 0,
    "is_cancel" TINYINT,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_org_tasks_experts" IS '评审任务专家表';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_experts"."element_id" IS '分配要素id(多个“,”隔开)';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_experts"."element_name" IS '要素名称(多个“、”隔开)';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_experts"."expert_id" IS '专家id';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_experts"."expert_name" IS '专家姓名';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_experts"."is_cancel" IS '是否已经取消';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_experts"."is_cay_expert" IS '是否城安院专家';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_experts"."mobile" IS '手机号';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_experts"."pbwc_id" IS '执法仪id';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_experts"."pbwc_name" IS '执法仪名称';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_experts"."position" IS '职务';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_experts"."position_id" IS '职务id  1：组长 2：组员';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_experts"."reason" IS '未接收原因';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_experts"."sepc" IS '从事专业';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_experts"."status" IS '状态 1待接收 5拒绝接收 7已接受';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_experts"."tasks_id" IS '评审任务表id';


CREATE TABLE "SYSDBA"."top_org_tasks_myd"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "tasks_id" INT DEFAULT 0,
    "company_id" INT DEFAULT 0,
    "company_name" VARCHAR2(200) DEFAULT '',
    "field2" VARCHAR2(200) DEFAULT '',
    "field3" VARCHAR2(200) DEFAULT '',
    "field4" VARCHAR2(200) DEFAULT '',
    "field5" VARCHAR2(200) DEFAULT '',
    "field6" VARCHAR2(200) DEFAULT '',
    "field7" VARCHAR2(800) DEFAULT '',
    "time" DATETIME(6) DEFAULT '',
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_org_tasks_myd" IS '满意度调查表';


CREATE TABLE "SYSDBA"."top_org_tasks_plan"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "tasks_id" INT DEFAULT 0,
    "date" DATE DEFAULT '',
    "number" INT DEFAULT 0,
    "number1" INT DEFAULT 0,
    "results" VARCHAR2(8000) DEFAULT '',
    "time" DATETIME(6) DEFAULT '',
    "number_city" INT,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_org_tasks_plan" IS '评审任务需求派发';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_plan"."number_city" IS '所需市级以上专家数量';


CREATE TABLE "SYSDBA"."top_org_tasks_score"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "tasks_id" INT DEFAULT 0,
    "element_id" INT DEFAULT 0,
    "element_ids" VARCHAR2(4000) DEFAULT '',
    "content_id" INT DEFAULT 0,
    "score" DECIMAL(10,2) DEFAULT '',
    "reform" VARCHAR2(12000) DEFAULT '',
    "miss" VARCHAR2(20) DEFAULT '',
    "resion" VARCHAR2(2000) DEFAULT '',
    "summary" VARCHAR2(2000) DEFAULT '',
    "sumscore" DECIMAL(10,2) DEFAULT 0,
    "files" VARCHAR2(8000) DEFAULT '',
    "is_reform" TINYINT DEFAULT 0,
    "deduct" VARCHAR2(20) DEFAULT '',
    "deduct_reason" VARCHAR2(4000),
    "miss_reason" VARCHAR2(4000),
    "deduct_material" VARCHAR2(8000),
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_org_tasks_score" IS '评审任务专家打分表';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_score"."content_id" IS '评审内容id';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_score"."deduct" IS '扣分分值';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_score"."deduct_material" IS '扣分材料说明';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_score"."deduct_reason" IS '扣分原因';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_score"."element_id" IS '评审要素id';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_score"."element_ids" IS '评审要素id集合';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_score"."is_reform" IS '是否必须整改';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_score"."miss" IS '缺项分值';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_score"."miss_reason" IS '缺项原因';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_score"."reform" IS '所需整改内容';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_score"."resion" IS '原因';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_score"."score" IS '专家打分';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_score"."summary" IS '扣分说明';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_score"."sumscore" IS '总分';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_score"."tasks_id" IS '评审任务表id';


CREATE OR REPLACE  INDEX "SYSDBA"."score_id" ON "SYSDBA"."top_org_tasks_score"("tasks_id" ASC,"element_id" ASC,"content_id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "SYSDBA"."top_org_tasks_works"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "tasks_id" INT DEFAULT 0,
    "stage" INT DEFAULT 0,
    "type" VARCHAR2(200) DEFAULT '',
    "title" VARCHAR2(800) DEFAULT '',
    "content" VARCHAR2(2000) DEFAULT '',
    "files" VARCHAR2(12000) DEFAULT '',
    "remark" VARCHAR2(800) DEFAULT '',
    "status" TINYINT DEFAULT 0,
    "end_time" VARCHAR2(200) DEFAULT '',
    "user_name" VARCHAR2(200) DEFAULT '',
    "sort" INT DEFAULT 0,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_org_tasks_works" IS '评审任务工作明细表';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_works"."content" IS '工作内容';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_works"."end_time" IS '完成时间';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_works"."files" IS '附件';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_works"."remark" IS '备注';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_works"."sort" IS '排序';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_works"."stage" IS '阶段';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_works"."status" IS '状态';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_works"."tasks_id" IS '评审任务id';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_works"."title" IS '标题';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_works"."type" IS '类型';
COMMENT ON COLUMN "SYSDBA"."top_org_tasks_works"."user_name" IS '操作人';


CREATE TABLE "SYSDBA"."top_org_user"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "org_id" INT DEFAULT 0,
    "username" VARCHAR2(200) DEFAULT '',
    "password" VARCHAR2(200) DEFAULT '',
    "salt" VARCHAR2(200) DEFAULT '',
    "name" VARCHAR2(200) DEFAULT '',
    "email" VARCHAR2(200) DEFAULT '',
    "mobile" VARCHAR2(200) DEFAULT '',
    "status" TINYINT DEFAULT 0,
    "reg_time" DATETIME(6) DEFAULT '',
    "reg_ip" VARCHAR2(200) DEFAULT '',
    "role" INT DEFAULT 0,
    "openid" VARCHAR2(200) DEFAULT '',
    "unionid" VARCHAR2(200) DEFAULT '',
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_org_user" IS '评审单位人员表';
COMMENT ON COLUMN "SYSDBA"."top_org_user"."email" IS '邮箱';
COMMENT ON COLUMN "SYSDBA"."top_org_user"."mobile" IS '手机号';
COMMENT ON COLUMN "SYSDBA"."top_org_user"."name" IS '姓名';
COMMENT ON COLUMN "SYSDBA"."top_org_user"."org_id" IS '单位id';
COMMENT ON COLUMN "SYSDBA"."top_org_user"."password" IS '密码';
COMMENT ON COLUMN "SYSDBA"."top_org_user"."reg_ip" IS '注册ip';
COMMENT ON COLUMN "SYSDBA"."top_org_user"."reg_time" IS '注册时间';
COMMENT ON COLUMN "SYSDBA"."top_org_user"."role" IS '角色 0：普通角色 1：管理员 2：超级管理员';
COMMENT ON COLUMN "SYSDBA"."top_org_user"."salt" IS '密码盐';
COMMENT ON COLUMN "SYSDBA"."top_org_user"."status" IS '状态';
COMMENT ON COLUMN "SYSDBA"."top_org_user"."username" IS '用户名';


CREATE OR REPLACE  INDEX "SYSDBA"."org_username" ON "SYSDBA"."top_org_user"("username" ASC,"mobile" ASC,"openid" ASC,"unionid" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "SYSDBA"."top_pca"
(
    "id" INT NOT NULL,
    "code" VARCHAR2(40) DEFAULT '',
    "name" VARCHAR2(200) DEFAULT '',
    "level" TINYINT DEFAULT 0,
    "pid" INT DEFAULT 0,
    "sort" SMALLINT DEFAULT 0,
    "enabled" INT DEFAULT 1,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_pca" IS '行政区划配置表';
COMMENT ON COLUMN "SYSDBA"."top_pca"."code" IS '编号';
COMMENT ON COLUMN "SYSDBA"."top_pca"."enabled" IS '启用';
COMMENT ON COLUMN "SYSDBA"."top_pca"."level" IS '层级';
COMMENT ON COLUMN "SYSDBA"."top_pca"."name" IS '名称';
COMMENT ON COLUMN "SYSDBA"."top_pca"."pid" IS '父级id';
COMMENT ON COLUMN "SYSDBA"."top_pca"."sort" IS '排序';


CREATE TABLE "SYSDBA"."top_pca1"
(
    "id" INT NOT NULL,
    "code" VARCHAR2(40) DEFAULT '',
    "name" VARCHAR2(200) DEFAULT '',
    "level" TINYINT DEFAULT 0,
    "pid" INT DEFAULT 0,
    "sort" SMALLINT DEFAULT 0,
    "enabled" INT DEFAULT 1,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "SYSDBA"."top_pca_bak"
(
    "id" INT NOT NULL,
    "code" VARCHAR2(40) DEFAULT '',
    "name" VARCHAR2(200) DEFAULT '',
    "level" TINYINT DEFAULT 0,
    "pid" INT DEFAULT 0,
    "sort" SMALLINT DEFAULT 0,
    "enabled" INT DEFAULT 1,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_pca_bak" IS '行政区划配置表';
COMMENT ON COLUMN "SYSDBA"."top_pca_bak"."code" IS '编号';
COMMENT ON COLUMN "SYSDBA"."top_pca_bak"."enabled" IS '启用';
COMMENT ON COLUMN "SYSDBA"."top_pca_bak"."level" IS '层级';
COMMENT ON COLUMN "SYSDBA"."top_pca_bak"."name" IS '名称';
COMMENT ON COLUMN "SYSDBA"."top_pca_bak"."pid" IS '父级id';
COMMENT ON COLUMN "SYSDBA"."top_pca_bak"."sort" IS '排序';


CREATE TABLE "SYSDBA"."top_publicity"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "title" VARCHAR2(200) DEFAULT '',
    "date" DATE DEFAULT '',
    "image" TEXT DEFAULT '',
    "user_id" INT DEFAULT 0,
    "user_name" VARCHAR2(200) DEFAULT '',
    "time" DATETIME(6) DEFAULT '',
    "type" TINYINT DEFAULT 0,
    "notify" VARCHAR2(200) DEFAULT '',
    "notify_date" DATE DEFAULT '',
    "status" TINYINT DEFAULT 0,
    "no_id" INT DEFAULT 0,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_publicity" IS '公示表';
COMMENT ON COLUMN "SYSDBA"."top_publicity"."date" IS '公示日期';
COMMENT ON COLUMN "SYSDBA"."top_publicity"."image" IS '公示名单图片';
COMMENT ON COLUMN "SYSDBA"."top_publicity"."notify" IS '公告内容';
COMMENT ON COLUMN "SYSDBA"."top_publicity"."notify_date" IS '公告日期';
COMMENT ON COLUMN "SYSDBA"."top_publicity"."status" IS '状态 1：正常  5：撤回';
COMMENT ON COLUMN "SYSDBA"."top_publicity"."time" IS '操作时间';
COMMENT ON COLUMN "SYSDBA"."top_publicity"."title" IS '公示标题';
COMMENT ON COLUMN "SYSDBA"."top_publicity"."type" IS '公示类型 1：公示  2：撤销公示';
COMMENT ON COLUMN "SYSDBA"."top_publicity"."user_id" IS '操作人';
COMMENT ON COLUMN "SYSDBA"."top_publicity"."user_name" IS '操作人';


CREATE TABLE "SYSDBA"."top_role"
(
    "role_id" INT IDENTITY(3, 1) NOT NULL,
    "role_code" VARCHAR2(200) DEFAULT '',
    "role_name" VARCHAR2(200) DEFAULT '',
    "menu_ids" TEXT DEFAULT '',
    NOT CLUSTER PRIMARY KEY("role_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_role" IS '角色表';


CREATE TABLE "SYSDBA"."top_sms"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "type" VARCHAR2(200) DEFAULT '',
    "phone" VARCHAR2(200) DEFAULT '',
    "sms" VARCHAR2(200) DEFAULT '',
    "content" VARCHAR2(1200) DEFAULT '',
    "status" TINYINT DEFAULT 0,
    "time" VARCHAR2(200) DEFAULT '',
    "taskid" VARCHAR2(200) DEFAULT '',
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_sms" IS '短信验证码';
COMMENT ON COLUMN "SYSDBA"."top_sms"."content" IS '发送内容';
COMMENT ON COLUMN "SYSDBA"."top_sms"."phone" IS '手机号';
COMMENT ON COLUMN "SYSDBA"."top_sms"."sms" IS '验证码';
COMMENT ON COLUMN "SYSDBA"."top_sms"."status" IS '状态';
COMMENT ON COLUMN "SYSDBA"."top_sms"."time" IS '时间';
COMMENT ON COLUMN "SYSDBA"."top_sms"."type" IS '类型';


CREATE TABLE "SYSDBA"."top_special_work"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "work_item" VARCHAR2(1200),
    "work_time_start" DATE,
    "work_place" VARCHAR2(800),
    "work_risk" VARCHAR2(4000),
    "examin_user" VARCHAR2(200),
    "work_permit" VARCHAR2(4000),
    "company_id" INT,
    "work_time_end" DATE,
    "create_time" DATETIME(0),
    "update_time" DATETIME(0),
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_special_work" IS '特殊作业许可表';
COMMENT ON COLUMN "SYSDBA"."top_special_work"."company_id" IS '企业id';
COMMENT ON COLUMN "SYSDBA"."top_special_work"."create_time" IS '创建时间';
COMMENT ON COLUMN "SYSDBA"."top_special_work"."examin_user" IS '审批人';
COMMENT ON COLUMN "SYSDBA"."top_special_work"."update_time" IS '更新时间';
COMMENT ON COLUMN "SYSDBA"."top_special_work"."work_item" IS '作业项目';
COMMENT ON COLUMN "SYSDBA"."top_special_work"."work_permit" IS '上传资料（作业许可证）';
COMMENT ON COLUMN "SYSDBA"."top_special_work"."work_place" IS '作业地点';
COMMENT ON COLUMN "SYSDBA"."top_special_work"."work_risk" IS '作业风险';
COMMENT ON COLUMN "SYSDBA"."top_special_work"."work_time_end" IS '作业时间结束时间';
COMMENT ON COLUMN "SYSDBA"."top_special_work"."work_time_start" IS '作业时间（选择时间段）';


CREATE TABLE "SYSDBA"."top_standard_apply"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "company_id" INT DEFAULT 0,
    "company_name" VARCHAR2(200) DEFAULT '',
    "industry" VARCHAR2(200) DEFAULT '',
    "specialty" VARCHAR2(200) DEFAULT '',
    "standard_id" INT DEFAULT 0,
    "standard_name" VARCHAR2(200) DEFAULT '',
    "level" VARCHAR2(200) DEFAULT '',
    "status" TINYINT DEFAULT 0,
    "apply_user_id" INT DEFAULT 0,
    "apply_user_name" VARCHAR2(200) DEFAULT '',
    "apply_time" DATETIME(6) DEFAULT '',
    "is_advisory" TINYINT DEFAULT 0,
    "advisory" VARCHAR2(200) DEFAULT '',
    "area_id" INT DEFAULT 0,
    "dept_id" INT DEFAULT 0,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_standard_apply" IS '创标申请表';
COMMENT ON COLUMN "SYSDBA"."top_standard_apply"."advisory" IS '机构名称';
COMMENT ON COLUMN "SYSDBA"."top_standard_apply"."apply_time" IS '申请时间';
COMMENT ON COLUMN "SYSDBA"."top_standard_apply"."apply_user_id" IS '申请人';
COMMENT ON COLUMN "SYSDBA"."top_standard_apply"."area_id" IS '所属区县id';
COMMENT ON COLUMN "SYSDBA"."top_standard_apply"."company_id" IS '企业id';
COMMENT ON COLUMN "SYSDBA"."top_standard_apply"."company_name" IS '企业名称';
COMMENT ON COLUMN "SYSDBA"."top_standard_apply"."dept_id" IS '所属部门id';
COMMENT ON COLUMN "SYSDBA"."top_standard_apply"."industry" IS '行业';
COMMENT ON COLUMN "SYSDBA"."top_standard_apply"."is_advisory" IS '是否咨询服务机构参与';
COMMENT ON COLUMN "SYSDBA"."top_standard_apply"."level" IS '级别';
COMMENT ON COLUMN "SYSDBA"."top_standard_apply"."specialty" IS '专业';
COMMENT ON COLUMN "SYSDBA"."top_standard_apply"."standard_id" IS '评审标准id';
COMMENT ON COLUMN "SYSDBA"."top_standard_apply"."standard_name" IS '评审标准名称';
COMMENT ON COLUMN "SYSDBA"."top_standard_apply"."status" IS '状态';


CREATE TABLE "SYSDBA"."top_standard_apply_approval"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "apply_id" INT DEFAULT 0,
    "remark" VARCHAR2(2000) DEFAULT '',
    "status" TINYINT DEFAULT 0,
    "check_user_id" INT DEFAULT 0,
    "check_user_name" VARCHAR2(200) DEFAULT '',
    "check_time" DATETIME(6) DEFAULT '',
    "files" VARCHAR2(8000) DEFAULT '',
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_standard_apply_approval" IS '创标申请审批记录表';
COMMENT ON COLUMN "SYSDBA"."top_standard_apply_approval"."apply_id" IS '申请单id';
COMMENT ON COLUMN "SYSDBA"."top_standard_apply_approval"."check_time" IS '审核时间';
COMMENT ON COLUMN "SYSDBA"."top_standard_apply_approval"."check_user_id" IS '审核人';
COMMENT ON COLUMN "SYSDBA"."top_standard_apply_approval"."files" IS '说明附件';
COMMENT ON COLUMN "SYSDBA"."top_standard_apply_approval"."remark" IS '审核说明';
COMMENT ON COLUMN "SYSDBA"."top_standard_apply_approval"."status" IS '状态';


CREATE TABLE "SYSDBA"."top_standard_content"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "main_id" INT DEFAULT 0,
    "content" VARCHAR2(24000) DEFAULT '',
    "score" DECIMAL(10,2) DEFAULT 0,
    "cycle" VARCHAR2(20) DEFAULT '',
    "ask" VARCHAR2(24000) DEFAULT '',
    "standards" TEXT DEFAULT '',
    "method" VARCHAR2(24000) DEFAULT '',
    "files" VARCHAR2(8000) DEFAULT '',
    "element_id" INT DEFAULT 0,
    "element_ids" VARCHAR2(2000) DEFAULT '',
    "is_del" TINYINT DEFAULT 0,
    "sort" SMALLINT DEFAULT 0,
    "file_remark" VARCHAR(24000),
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_standard_content" IS '评审标准内容表';
COMMENT ON COLUMN "SYSDBA"."top_standard_content"."ask" IS '基本规范要求';
COMMENT ON COLUMN "SYSDBA"."top_standard_content"."content" IS '指标名称';
COMMENT ON COLUMN "SYSDBA"."top_standard_content"."cycle" IS '填报周期';
COMMENT ON COLUMN "SYSDBA"."top_standard_content"."element_id" IS '要素id';
COMMENT ON COLUMN "SYSDBA"."top_standard_content"."element_ids" IS '要素id集合';
COMMENT ON COLUMN "SYSDBA"."top_standard_content"."file_remark" IS '自评/评审描述';
COMMENT ON COLUMN "SYSDBA"."top_standard_content"."files" IS '模板附件';
COMMENT ON COLUMN "SYSDBA"."top_standard_content"."main_id" IS '评审标准id';
COMMENT ON COLUMN "SYSDBA"."top_standard_content"."method" IS '评分方式';
COMMENT ON COLUMN "SYSDBA"."top_standard_content"."score" IS '指标分值';
COMMENT ON COLUMN "SYSDBA"."top_standard_content"."standards" IS '达标标准';


CREATE TABLE "SYSDBA"."top_standard_element"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "main_id" INT DEFAULT 0,
    "name" VARCHAR2(200) DEFAULT '',
    "score" DECIMAL(22,2) DEFAULT 0,
    "mark" SMALLINT DEFAULT 0,
    "sort" SMALLINT,
    "pid" INT DEFAULT 0,
    "pids" VARCHAR2(2000) DEFAULT '',
    "weight" DECIMAL(22,6) DEFAULT 1,
    "create_user_id" VARCHAR2(200) DEFAULT '',
    "create_user_name" VARCHAR2(200) DEFAULT '',
    "create_time" DATETIME(6) DEFAULT '',
    "is_del" TINYINT DEFAULT 0,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_standard_element" IS '评审标准要素表';
COMMENT ON COLUMN "SYSDBA"."top_standard_element"."main_id" IS '标准表id';
COMMENT ON COLUMN "SYSDBA"."top_standard_element"."mark" IS '合格线';
COMMENT ON COLUMN "SYSDBA"."top_standard_element"."name" IS '要素名称';
COMMENT ON COLUMN "SYSDBA"."top_standard_element"."pid" IS '上级要素id';
COMMENT ON COLUMN "SYSDBA"."top_standard_element"."pids" IS '上级要素id集合';
COMMENT ON COLUMN "SYSDBA"."top_standard_element"."score" IS '分数';
COMMENT ON COLUMN "SYSDBA"."top_standard_element"."sort" IS '排序号';
COMMENT ON COLUMN "SYSDBA"."top_standard_element"."weight" IS '权重';


CREATE TABLE "SYSDBA"."top_standard_name"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "name" VARCHAR2(200) DEFAULT '',
    "element" VARCHAR2(200) DEFAULT '',
    "sort" SMALLINT DEFAULT 0,
    "create_user_id" VARCHAR2(200) DEFAULT '',
    "create_user_name" VARCHAR2(200) DEFAULT '',
    "create_time" DATETIME(6) DEFAULT '',
    "is_del" TINYINT DEFAULT 0,
    "update_time" DATETIME(0),
    "update_user_id" VARCHAR2(200),
    "update_user_name" VARCHAR2(200),
    "status" TINYINT DEFAULT 0,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_standard_name" IS '评审标准主表';
COMMENT ON COLUMN "SYSDBA"."top_standard_name"."element" IS '要素名称';
COMMENT ON COLUMN "SYSDBA"."top_standard_name"."name" IS '名称';


CREATE TABLE "SYSDBA"."top_sys_log"
(
    "LOG_ID" INT IDENTITY(885, 1) NOT NULL,
    "USER_ID" VARCHAR(20) DEFAULT '',
    "TIME" TIMESTAMP(0) DEFAULT '1000-01-01 00:00:00' NOT NULL,
    "IP" VARCHAR(20) DEFAULT '' NOT NULL,
    "TYPE" INT DEFAULT 1 NOT NULL,
    "REMARK" CLOB DEFAULT '',
    "PARAM_JSON" CLOB DEFAULT '',
    NOT CLUSTER PRIMARY KEY("LOG_ID"),
    CHECK("TYPE" >= 0)) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_sys_log" IS '系统日志';
COMMENT ON COLUMN "SYSDBA"."top_sys_log"."IP" IS 'IP地址';
COMMENT ON COLUMN "SYSDBA"."top_sys_log"."LOG_ID" IS '唯一自增ID';
COMMENT ON COLUMN "SYSDBA"."top_sys_log"."PARAM_JSON" IS '日志内容';
COMMENT ON COLUMN "SYSDBA"."top_sys_log"."REMARK" IS '日志内容';
COMMENT ON COLUMN "SYSDBA"."top_sys_log"."TIME" IS '记录时间';
COMMENT ON COLUMN "SYSDBA"."top_sys_log"."TYPE" IS '日志类型(详见系统代码)';
COMMENT ON COLUMN "SYSDBA"."top_sys_log"."USER_ID" IS '用户USER_ID';


CREATE OR REPLACE  INDEX "SYSDBA"."INDEXtop_sys_log" ON "SYSDBA"."top_sys_log"("TIME" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "SYSDBA"."top_training_certificate"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "name" VARCHAR2(200),
    "job" VARCHAR2(200),
    "grant_date" DATE,
    "validity_start" DATE,
    "year_review_time" DATE,
    "year_review_info" VARCHAR2(4000),
    "file" VARCHAR2(4000),
    "company_id" INT,
    "validity_end" DATE,
    "create_time" DATETIME(0),
    "update_time" DATETIME(0),
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_training_certificate" IS '培训合格证书管理表';
COMMENT ON COLUMN "SYSDBA"."top_training_certificate"."company_id" IS '企业id';
COMMENT ON COLUMN "SYSDBA"."top_training_certificate"."create_time" IS '创建时间';
COMMENT ON COLUMN "SYSDBA"."top_training_certificate"."file" IS '证书扫描件上传';
COMMENT ON COLUMN "SYSDBA"."top_training_certificate"."grant_date" IS '证书发放时间';
COMMENT ON COLUMN "SYSDBA"."top_training_certificate"."job" IS '职务';
COMMENT ON COLUMN "SYSDBA"."top_training_certificate"."name" IS '姓名';
COMMENT ON COLUMN "SYSDBA"."top_training_certificate"."update_time" IS '更新时间';
COMMENT ON COLUMN "SYSDBA"."top_training_certificate"."validity_end" IS '有限期截止时间';
COMMENT ON COLUMN "SYSDBA"."top_training_certificate"."validity_start" IS '有效期开始时间';
COMMENT ON COLUMN "SYSDBA"."top_training_certificate"."year_review_info" IS '年审记录';
COMMENT ON COLUMN "SYSDBA"."top_training_certificate"."year_review_time" IS '年审时间';


CREATE TABLE "SYSDBA"."top_trigger_config"
(
    "id" INT IDENTITY(1, 1) NOT NULL,
    "title" VARCHAR2(200) DEFAULT '',
    "flow_id" INT DEFAULT 0,
    "tables" VARCHAR2(200) DEFAULT '',
    "controller" VARCHAR2(200) DEFAULT '',
    "fields" TEXT DEFAULT '',
    "list" TEXT DEFAULT '',
    "create_user_id" VARCHAR2(200) DEFAULT '',
    "create_user_name" VARCHAR2(200) DEFAULT '',
    "create_time" VARCHAR2(200) DEFAULT '',
    "is_del" INT DEFAULT 0,
    "lists" TEXT DEFAULT '',
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_trigger_config" IS '流程触发器配置表';
COMMENT ON COLUMN "SYSDBA"."top_trigger_config"."controller" IS '转存方法';
COMMENT ON COLUMN "SYSDBA"."top_trigger_config"."fields" IS '转存主表字段映射';
COMMENT ON COLUMN "SYSDBA"."top_trigger_config"."flow_id" IS '流程id';
COMMENT ON COLUMN "SYSDBA"."top_trigger_config"."lists" IS '列表字段映射';
COMMENT ON COLUMN "SYSDBA"."top_trigger_config"."tables" IS '表名';
COMMENT ON COLUMN "SYSDBA"."top_trigger_config"."title" IS '名称';


CREATE TABLE "SYSDBA"."top_user"
(
    "user_id" INT IDENTITY(3, 1) NOT NULL,
    "username" VARCHAR2(200) DEFAULT '',
    "password" VARCHAR2(200) DEFAULT '',
    "salt" VARCHAR2(200) DEFAULT '',
    "name" VARCHAR2(200) DEFAULT '',
    "sex" VARCHAR2(20) DEFAULT '',
    "dept_id" INT DEFAULT 0,
    "dept_name" VARCHAR2(200) DEFAULT '',
    "role_id" INT DEFAULT 0,
    "role_name" VARCHAR2(200) DEFAULT '',
    "sort" SMALLINT DEFAULT 0,
    NOT CLUSTER PRIMARY KEY("user_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_user" IS '用户表';


CREATE TABLE "SYSDBA"."top_user_role"
(
    "user_id" INT DEFAULT 0 NOT NULL,
    "role_id" INT DEFAULT 0 NOT NULL,
    NOT CLUSTER PRIMARY KEY("user_id", "role_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "SYSDBA"."top_user_role" IS '用户角色表';


