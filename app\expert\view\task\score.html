<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>评审打分</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/company.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .el-descriptions .is-bordered td { width:250px;}
        .item { width: 30px;height:30px;line-height:30px;margin: 10px 0;border-radius:40px;background-color: #f0f0f0;text-align:center;cursor: pointer;}
        .item.checked { background-color: #1989FA;color:#fff;}
        .item.success { background-color: #67c23a;color:#fff;}
        .el-divider.el-divider--horizontal { margin:10px 0;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">
        <el-page-header @back="goBack" content="评审打分"></el-page-header>
        <el-divider style="margin:10px 0;"></el-divider>
        <el-row :gutter="20">
            <el-col :span="5" :style="{overflow: 'auto',height:height+'px'}">
                <!--<el-tree
                        :data="data"
                        :props="{label:'name'}"
                        @node-click="handleNodeClick">
                    <div :class="data.scores-data.score-data.miss==0||data.summary?'success':''" style="width:100%" slot-scope="{node,data}">
                        <el-tooltip v-if="data.is_content" style="width:90%;display: inline-block;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;" :content="data.name" placement="top">
                            <span><i v-if="data.scores-data.score-data.miss==0||data.summary" style="color:#67c23a" class="el-icon-success"></i> {{data.name}}</span>
                        </el-tooltip>
                        <div v-if="!data.is_content">{{data.name}}</div>
                    </div>
                </el-tree>-->
                <el-col v-for="review in data">
                    <p>{{review.name}}</p>
                <el-col v-for="(item,key) in review.children" style="width:50px;">
                    <!--                    <el-tooltip effect="dark" placement="top">-->
                    <!--                        <div slot="content" v-html="item.ask"></div>-->
                    <div :class="form==item?'item checked':((item.deduct==='0' && item.miss==='0')||(item.deduct>0&&item.deduct_reason!='')||(item.miss>0&&item.miss_reason!='')?'item success':'item')" @click="change(item)">{{key+1}}</div>
                    <!--                    </el-tooltip>-->
                </el-col>
                </el-col>
            </el-col>
            <el-col :span="19" :style="{overflow: 'auto',height:height+'px'}">
                <el-form v-model="form" label-width="150px">
                    <el-form-item label="基本规范要求">
                        <div v-html="form.ask"></div>
                    </el-form-item>
                    <el-form-item label="企业达标标准">
                        <div v-html="form.standards"></div>
                    </el-form-item>
                    <el-form-item label="评分方式">
                        <div style="white-space:pre-line" v-html="form.method"></div>
                    </el-form-item>
                    <el-form-item label="上报记录">
                        <el-table border
                                  :data="form.list"
                                  style="width: 100%;margin-bottom: 20px;"
                                  size="small">
                            <el-table-column
                                    type="index"
                                    label="序号"
                                    align="center"
                                    width="50">
                            </el-table-column>
                            <el-table-column
                                    prop="time"
                                    label="上报时间"
                                    align="center"
                                    width="100">
                            </el-table-column>
                            <el-table-column
                                    prop="sub_files"
                                    label="上报材料"
                                    align="center"
                                    show-overflow-tooltip
                                    min-width="100">
                                <template slot-scope="scope">
                                    <el-upload
                                            v-if="scope.row.edit"
                                            action="upload"
                                            :file-list="scope.row.mb_sub_files"
                                            limit="10"
                                            :on-success="(response,file,fileList)=>uploadSuccess(response,file,fileList,scope.row)"
                                            :before-upload="uploadBefore"
                                            :on-remove="(file,fileList)=>handleRemove(file,fileList,scope.row)">
                                        <el-button size="small" type="primary">点击上传</el-button>
                                        <!--                                <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>-->
                                    </el-upload>
                                    <div v-if="!scope.row.edit" v-for="v in scope.row.mb_sub_files">
                                        <el-image v-if="v.ext=='jpg'||v.ext=='png'||v.ext=='JPG'||v.ext=='PNG'" style="width:60px;height: 60px;border: 1px solid #999;"
                                                  :title="v.name"
                                                  :src="v.url"
                                                  :preview-src-list="[v.url]"></el-image>
                                        <el-link style="color: #2c89ff;" v-else :href="v.url" target="_blank">{{v.name}}</el-link>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column
                                    prop="hazard"
                                    label="运行检查"
                                    align="center"
                                    show-overflow-tooltip
                                    min-width="100">
                                <template slot-scope="scope">
                                    <el-input v-if="scope.row.edit" type="textarea" v-model="scope.row.hazard"></el-input>
                                    <div v-if="!scope.row.edit">{{scope.row.hazard}}</div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-form-item>
                    <el-form-item label="标准分值">
                        <div>{{form.scores}}</div>
                    </el-form-item>
                    <!--<el-form-item label="得分">
                        <el-input v-model="form.score" :min="0" :max="form.scores-form.miss" label="" @blur="changescore"></el-input>
                    </el-form-item>-->
                    <el-form-item label="缺项分值">
                        <el-input v-model="form.miss" @blur="changemiss"></el-input>
                    </el-form-item>
                    <el-form-item label="缺项说明">
                        <el-input style="overflow:hidden;" type="textarea" :autosize="{minRows: 2}" v-model="form.miss_reason" placeholder="缺项说明，最多500个汉字。"></el-input>
                    </el-form-item>
                    <el-form-item label="扣分分值">
                        <el-input v-model="form.deduct"  @blur="changededuct"></el-input>
                    </el-form-item>
                    <el-form-item label="扣分说明">
                        <el-input style="overflow:hidden;" type="textarea" :autosize="{minRows: 2}" v-model="form.deduct_reason" placeholder="扣分说明，最多500个汉字。"></el-input>
                    </el-form-item>
                    <!--<el-form-item v-show="form.scores-form.score-form.miss>0" label="扣分说明">
                        <el-input style="overflow:hidden;" type="textarea" :autosize="{minRows: 2}" v-model="form.summary"></el-input>
                        <el-upload
                                action="upload"
                                list-type="picture-card"
                                :file-list="form.files"
                                :limit="10"
                                :on-success="(response,file,fileList)=>uploadSuccess(response,file,fileList)"
                                :before-upload="uploadBefore"
                                :on-preview="handlePictureCardPreview"
                                :on-remove="(file,fileList)=>handleRemove(file,fileList)">
                            <i class="el-icon-plus"></i>
                        </el-upload>
                    </el-form-item>-->

                    <el-form-item v-if="form.have_reform" label="扣分材料说明">
                        <el-button type="primary" size="small" @click="uploadReform">上传整改图片</el-button>
                    </el-form-item>

                    <!--扣分材料说明-图片-->
                    <el-form-item v-if="form.have_reform" label="">
                        <div style="width: 100%;min-height: 200px;max-height: 400px;background-color: #f8f8f8;overflow-y: auto">
                            <image-box ref="imageBox" :image-list="form.deduct_material"></image-box>
                        </div>
                    </el-form-item>

                    <template v-for="(item,key) in form.reform">
                        <el-form-item v-if="form.have_reform && key==0" label="必须整改项">
                            <el-input v-model="form.reform[key]" placeholder="整改内容" style="width: 500px"></el-input>
                            <el-link @click="addReformLine" type="success" icon="el-icon-circle-plus-outline">增加必须整改项</el-link>
                        </el-form-item>
                        <el-form-item v-else-if="form.have_reform && key>0" label="">
                            <el-input v-model="form.reform[key]" placeholder="整改内容" style="width: 500px"></el-input>
                            <el-link @click="delReformLine(form.reform[key])" type="danger" icon="el-icon-remove-outline">删除</el-link>
                        </el-form-item>
                    </template>


                    <el-form-item>
<!--                        <el-button type="primary" size="small" @click="next">下一个</el-button>-->
                    </el-form-item>
                </el-form>
            </el-col>
            <el-col style="border-top: 1px solid #eee;">
                <div style="text-align: center;margin: 20px auto;">
                    <el-button @click="submit(0)" v-loading.fullscreen.lock="loading">保存</el-button>
                    <el-button type="primary"  @click="submit(1)">提交</el-button>
                </div>
            </el-col>
        </el-row>
        <el-dialog :visible.sync="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt="">
        </el-dialog>
    </div>

    <correct-files ref="correctFiles" @ok="updateImage"></correct-files>

</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.all.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                imageList: [],
                title: '新增',
                id: '{$id}',
                index: 1,
                form: {
                },
                data: {
                },
                param: [],
                visible: false,
                loading: true,
                dialogImageUrl:'',
                dialogVisible:false,
                height: document.documentElement.clientHeight - 165,
            };
        },
        components: {
            'imageBox': 'url:../../../public/vue/imageBox.vue?v=1',
            'correctFiles': 'url:../../../public/vue/correctFiles.vue?v=1',
        },
        methods: {
            addReformLine(){
                this.form.reform.push('')
            },
            delReformLine(currentData){
                this.form.reform.splice(this.form.reform.indexOf(currentData),1);
            },
            updateImage(fileList){
                for (let i = 0; i < fileList.length; i++)
                {
                    const targetJSON = JSON.stringify(fileList[i]);
                    var have = this.form.deduct_material.some(subArr =>
                        JSON.stringify(subArr) === targetJSON
                    );

                    if(!have )
                    {
                        this.form.deduct_material.push(fileList[i]);
                    }
                }
            },
            uploadReform(){
                this.$refs.correctFiles.task_id = this.id;
                this.$refs.correctFiles.open();
            },
            handleNodeClick(data) {
                if(data.is_content){
                    this.form = data;
                }
                console.log(data);
            },
            changescore(e) {
                var max = this.isNumber(this.form.miss)?this.form.scores-this.form.miss:this.form.scores;
                if(!this.isNumber(this.form.score)||this.form.score>max||this.form.score<0){
                    this.form.score = '';
                    return true;
                }
            },
            // changededuct(e) {
            //     var max = this.isNumber(this.form.miss)?this.form.scores-this.form.miss:this.form.scores;
            //     if(!this.isNumber(this.form.deduct)||this.form.deduct>max||this.form.deduct<0){
            //         this.form.deduct = '';
            //         return true;
            //     }
            // },
            changemiss(e) {
                var max = this.form.scores;
                if( this.form.miss == '' )
                {
                    return false;
                }

                if( !this.isNumber(this.form.miss) || Number(this.form.miss)>max || Number(this.form.miss)<0 ){
                    this.$message({
                        message: "缺项分值不能大于标准分值，且不能小于0",
                        type: "error"
                    });
                    this.form.miss = '';
                    return false;
                }
                else if( Number(this.form.miss)<0 ){
                    this.$message({
                        message: "缺项分值不能小于0",
                        type: "error"
                    });
                    this.form.miss = '';
                    return false;
                }
                else if( (Number(this.form.miss) + Number(this.form.deduct))>max )
                {
                    this.$message({
                        message: "缺项分值+扣分分值，不能大于标准分值",
                        type: "error"
                    });
                    this.form.miss = '';
                    return false;
                }
            },
            changededuct(e) {
                var max = Number(this.form.scores);

                if( this.form.deduct == '' )
                {
                    return false;
                }

                if( !this.isNumber(this.form.deduct) || Number(this.form.deduct)>max || Number(this.form.deduct)<0 ){
                    this.$message({
                        message: "扣分分不能大于标准分值，且不能小于0",
                        type: "error"
                    });
                    this.form.deduct = '';
                    return false;
                }
                else if( Number(this.form.deduct)<0 ){
                    this.$message({
                        message: "扣分分不能小于0",
                        type: "error"
                    });
                    this.form.deduct = '';
                    return false;
                }
                else if( (Number(this.form.miss) + Number(this.form.deduct))>max )
                {
                    this.$message({
                        message: "缺项分值+扣分分值，不能大于标准分值",
                        type: "error"
                    });
                    this.form.deduct = '';
                    return false;
                }
            },
            isNumber(value) {
                return /^[0-9]+.?[0-9]*$/.test(value);
                // return typeof value === 'number' && isFinite(value)
            },
            uploadBefore(file) {
                const isJPG = file.type === 'image/jpeg';
                const isPNG = file.type === 'image/png';
                if(!isJPG&&!isPNG){
                    this.$message.error('请上传jpg图片');
                }
                return isJPG||isPNG;
            },
            uploadSuccess(res, file,fileList,id) {
                var files = [];
                for(var i in fileList){
                    files.push(fileList[i].response.data??fileList[i]);
                }
                // console.log(fileList);
                this.form.files = files;
            },
            handleRemove(file, fileList,id) {
                var files = [];
                for(var i in fileList){
                    files.push(fileList[i].response.data??fileList[i]);
                }
                // console.log(fileList);
                this.form.files = files;
            },
            handlePictureCardPreview(file) {
                this.dialogImageUrl = file.url;
                this.dialogVisible = true;
            },
            //数据加载
            getData() {
                var _this = this;
                var param = {};
                param.id = _this.id;
                param._ajax = 1;
                axios.post('score', param).then(function (res) {
                    if (res.data.code == 0) {
                        _this.data = res.data.data.content;
                        _this.form = res.data.data.content[0]['children'][0];
                        _this.$nextTick(() => {
                            // 这里的代码会在DOM更新完成后执行
                            _this.loading = false;
                        });
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            getchildren(data){
                for(var i in data){
                    if(data[i]['is_content']){
                        row = {
                            id:data[i].id,
                            scores:data[i].scores,
                            score:data[i].score,
                            miss:data[i].miss,
                            is_reform:data[i].is_reform,
                            reform:data[i].reform,
                            summary:data[i].summary,
                            resion:data[i].resion,
                            element_id:data[i].element_id,
                            element_ids:data[i].element_ids,
                            files:data[i].files,
                        };
                        this.param.push(data[i]);
                    }else{
                        this.getchildren(data[i]['children']);
                    }
                }
                return true;
            },
            submit: function (status) {
                var _this = this;
                this.param = [];
                this.getchildren(this.data);

                var param = {
                    id:_this.id,
                    status:status,
                    data:this.param,
                };
                /*var row = {};
                for(var i in _this.data){
                    row = {
                        id:_this.data[i].id,
                        scores:_this.data[i].scores,
                        score:_this.data[i].score,
                        miss:_this.data[i].miss,
                        reform:_this.data[i].reform,
                        summary:_this.data[i].summary,
                        resion:_this.data[i].resion,
                        element_id:_this.data[i].element_id,
                        element_ids:_this.data[i].element_ids,
                    };
                    param.data.push(row);
                }*/
                if(status==1){
                    _this.$confirm("确认完成打分，确认后将自动计算生成最终得分，生成后不可再修改打分？", "提示", {}).then(() => {
                        _this.loading = true;
                        axios.post("scoreSave?id="+_this.id, param).then(function (res) {
                            _this.loading = false;
                            _this.$message({
                                message: res.data.msg,
                                type: res.data.type
                            });
                            if (res.data.code == 0) {
                                location.href = 'index';
                            }
                        }).catch(function (error) {
                            _this.loading = false;
                            console.log("出现错误:",error);
                        });
                    });
                }else{
                    _this.loading = true;
                    axios.post("scoreSave?id="+_this.id, param).then(function (res) {
                        _this.loading = false;
                        _this.$message({
                            message: res.data.msg,
                            type: res.data.type
                        });
                    }).catch(function (error) {
                        _this.loading = false;
                        console.log("出现错误:",error);
                    });
                }
            },
            next() {
                if(this.index<this.data.length){
                    this.change(this.index);
                }else{
                    this.$message({
                        message: '已经是最后一个了',
                        type: 'error'
                    });
                }
            },
            change(item) {
                // this.index = key+1;
                this.form = item;
            },
            goBack() {
                location.href = 'index';
            },
        },
        mounted() {
            //获取列表
            this.getData();

        }
    })
</script>


</body>
</html>