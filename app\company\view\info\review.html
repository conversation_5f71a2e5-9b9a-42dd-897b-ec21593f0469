<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>运行资料</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/company.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .el-table .danger-row {
            background: #fbc4c4;
        }
        .el-table .warning-row {
            background: oldlace;
        }
        .el-table .success-row {
            background: #f0f9eb;
        }
        .el-badge__content.is-fixed { right: 70px;top:60px;height:30px;line-height:30px;border-radius:30px;width:18px;z-index: 9;}
        .success .el-descriptions__header {
            border-left:5px solid #67c23a;
            color:#67c23a;
            line-height: 40px;
            padding-left:20px;
            background: -ms-linear-gradient(left, #f0f9eb, #fff); /* IE 10+ */
            background: -moz-linear-gradient(left, #f0f9eb, #fff); /* Firefox */
            background: -webkit-linear-gradient(left, #f0f9eb, #fff); /* Chrome, Safari, Opera */
            background: linear-gradient(to right, #f0f9eb, #fff); /* 标准语法 */
        }
        .primary .el-descriptions__header {
            margin-top: 20px;
            border-left:5px solid #436CE6;
            color:#436CE6;
            line-height: 40px;
            padding-left:20px;
            background: -ms-linear-gradient(left, #F4F7FE, #fff); /* IE 10+ */
            background: -moz-linear-gradient(left, #F4F7FE, #fff); /* Firefox */
            background: -webkit-linear-gradient(left, #F4F7FE, #fff); /* Chrome, Safari, Opera */
            background: linear-gradient(to right, #F4F7FE, #fff); /* 标准语法 */
        }
        .success .el-descriptions-item__label.is-bordered-label { background-color: #f0f9eb; width:140px;}
        .primary .el-descriptions-item__label.is-bordered-label { background-color: #F4F7FE; width:140px;}
        .el-descriptions .is-bordered td { width:250px;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">
        <el-descriptions class="success" title="待上报材料" :column="2" border>
        </el-descriptions>
        <el-row :gutter="40">
            <el-col style="width:380px;" v-for="(item,key) in data.elementTitle">
                <el-badge :value="item.num" :max="99" class="item">
                    <div style="overflow:hidden;width: 260px;margin:20px; padding:0 20px;height: 80px;line-height: 80px;border: 1px solid #D1D1D1;border-radius:10px;cursor: pointer;" @click="reviewAdd(item)">
                        <el-row>
                            <el-col style="width:50px;">
                                <el-image style="width: 36px; height: 36px;margin-top:20px;" :src="'__PUBLIC__/static/images/company/e'+(key+1)+'.png'" fit="fill"></el-image>
                            </el-col>
                            <el-col style="width:210px;">
                                {{item.name}}
                            </el-col>
                        </el-row>

                    </div>
                </el-badge>
            </el-col>
			<hr>
			<el-col style="width:380px;" v-for="(item,key) in data.elementOtherTitle">
                <el-badge :value="item.num" :max="99" class="item">
                    <div style="overflow:hidden;width: 260px;margin:20px; padding:0 20px;height: 80px;line-height: 80px;border: 1px solid #D1D1D1;border-radius:10px;cursor: pointer;" @click="reviewSpecial(item)">
                        <el-row>
                            <el-col style="width:50px;">
                                <el-image style="width: 36px; height: 36px;margin-top:20px;" :src="'__PUBLIC__/static/images/company/e'+(key+1)+'.png'" fit="fill"></el-image>
                            </el-col>
                            <el-col style="width:210px;">
                                {{item.name}}
                            </el-col>
                        </el-row>
                    </div>
                </el-badge>
            </el-col>
        </el-row>
        <el-descriptions class="primary" title="已上报运行资料" :column="2" border>
        </el-descriptions>
        <el-row :gutter="40">
            <el-col style="width:380px;" v-for="(item,key) in data.elementTitle">
                <div style="overflow:hidden;width: 260px;margin:20px; padding:0 20px;height: 80px;line-height: 80px;border: 1px solid #D1D1D1;border-radius:10px;cursor: pointer;" @click="reviewInfo(item)">
                    <el-row>
                        <el-col style="width:50px;">
                            <el-image style="width: 36px; height: 36px;margin-top:20px;" :src="'__PUBLIC__/static/images/company/e'+(key+1)+'.png'" fit="fill"></el-image>
                        </el-col>
                        <el-col style="width:210px;">
                            {{item.name}}
                        </el-col>
                    </el-row>

                </div>
            </el-col>
        </el-row>
    </div>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.all.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                title: '新增',
                searchFrom: {},
                data: [],
                visible: false,
                loading: true,
                height: document.documentElement.clientHeight - 155,
            };
        },
        watch: {
            $route() {
                //切换标签时
                this.handleRefreshTable();
            }
        },
        created() {
            let _this = this;
            //窗口改变时
            window.onresize = function () {
                _this.handleRefreshTable();
            };
        },
        updated() {
            //数据改变时
            this.handleRefreshTable();
        },
        methods: {
            //数据加载
            getData() {
                var _this = this;
                var param = _this.searchFrom;
                param._ajax = 1;
                param.our = 1;
                axios.post('', param).then(function (res) {
                    if (res.data.code == 0) {
                        _this.data = res.data.data;
                        _this.$nextTick(() => {
                            // 这里的代码会在DOM更新完成后执行
                            _this.loading = false;
                        });
                        _this.handleRefreshTable();
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            reviewSpecial(data){
                if(data.id == -2)
                {
                    location.href = 'trainingCertificate';
                }
                else if(data.id == -3)
                {
                    location.href = 'specialWork';
                }
                else{
                    location.href = 'reviewEdit?element_id='+data.id;
                }
            },
            reviewAdd(data){
                location.href = 'reviewEdit?element_id='+data.id;
            },
            reviewInfo(data){
                location.href = 'reviewInfo?element_id='+data.id;
            },
            handleRefreshTable: function () {
                this.$nextTick(function () {
                    var refList = this.$refs;
                    if (refList) {
                        for (var i of Object.keys(refList)) {
                            if (refList[i] && refList[i].doLayout) {
                                refList[i].doLayout();
                            }
                        }
                    }
                });
            },
        },
        mounted() {
            //获取列表
            this.getData();

        }
    })
</script>


</body>
</html>