<?php

namespace app\demo\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use hg\apidoc\annotation as Apidoc;
use app\BaseController;
use app\model\UserModel;

/**
 * 标题也可以这样直接写
 * @Apidoc\Title("用户信息示例参考")
 * @Apidoc\Group("Demo")
 * @Apidoc\Sort(8)
 */
class User extends BaseController {

    public function test() {
        $result = UserModel::getDepts(0,1,'now');
        result($result);
    }
    /**
     * @Apidoc\Title("获取部门信息")
     */
    public function getDepts() {
        $result = UserModel::getDepts(0,1,'now');
        result($result);
    }

    /**
     * @Apidoc\Title("获取用户信息列表")
     */
    public function getUserInfoList() {
        $where = [];
        $result = UserModel::getUserInfoList($where,20,'all');
        result($result);
    }

    /**
     * @Apidoc\Title("获取家庭成员列表")
     */
    public function getUserinfoFamily() {
        $result = UserModel::getUserinfoFamily('admin');
        result($result);
    }

    /**
     * @Apidoc\Title("获取部门下人员信息")
     */
    public function getDeptUser() {
        $result = UserModel::getDeptUser(5,'now');
        result($result);
    }

    /**
     * @Apidoc\Title("获取用户详细信息")
     */
    public function getUserInfo() {
        $result = UserModel::getUserInfo('admin',true);
        result($result);
    }

    /**
     * @Apidoc\Title("获取用户姓名")
     */
    public function getUserName() {
        $result = UserModel::getUserName('admin');
        result($result);
    }


}
