<?php

namespace app\validate;

use think\Validate;

class SeVerify extends Validate
{
    protected $rule = [
        'id' => 'require|number',
        'overview' => 'require|min:5',
        'accident' =>'require',
        'question' =>'require',
        'conclusion' =>'require|min:5',
    ];

    protected $message = [
        'id' => '参数错误',
        'overview' => '请填写企业概况',
        'accident' =>'请填写企业生产安全事故情况',
        'question' =>'请填写企业安全生产标准化工作存在的问题和取得的成效',
        'conclusion' =>'请填写自评结论（最少5个字）',
    ];

    protected $scene = [
        'end' => ['overview','accident','question','conclusion'],
    ];

    public function userCheck($scene,$data){
        $validate = new self();
        $re = $validate->scene($scene)->check($data);
        if(!$re){
            result('',1001,$validate->getError());
//            if(request()->isAjax()){
//                result('',1001,$validate->getError());
//            }else{
//                return $validate->getError();
//            }
        }
    }

}