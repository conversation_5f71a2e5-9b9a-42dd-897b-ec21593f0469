<?php

namespace app\admin\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use app\admin\model\CompanyModel;
use app\admin\model\ExpertModel;
use app\BaseController;
use app\model\SettingModel;
use app\model\ExcelModel;
use hg\apidoc\annotation as Apidoc;
use think\facade\View;
use think\Request;
use think\facade\Db;
use app\model\FileModel;

/**
 * @Apidoc\Title("专家管理")
 * @Apidoc\Group("Index")
 * @Apidoc\Sort(1)
 */
class Expert extends BaseController {

    /**
     * @Apidoc\Title("专家管理列表")
     * @Apidoc\Desc("专家管理列表")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function index($limit = 20,$excel = 0) {
        if (request()->isAjax()){
            $param = $this->request->param();
            $where = [];

            if( isset($param['name']) && !empty($param['name']) )
            {
                $where[] = ['a.name', 'like', "%{$param['name']}%"];
            }

            $fields = "a.id,a.name,a.email,a.mobile,a.status,a.org_id,b.nation,b.sex,b.birthday,b.qq,b.address,b.head,b.school,b.speciality,b.education,b.employer,b.position,b.work_date,b.position_date,b.professional,b.professional_number,b.secure,b.secure_number,b.reg_secure_number,b.other_number,b.major,b.resume,b.employ_date,b.offer_info,o.name org_name";
            $res = Db::table('top_expert')->alias('a')
                ->leftJoin('top_expert_info b','a.id = b.expert_id')
                ->leftJoin('top_org o','o.id = a.org_id')
                ->where($where)->field($fields)->order('a.id');
            if($excel==1){
                $res = $res->select()->each(function ($item, $key) {
                    $item = ExpertModel::codeToText($item);
                    return $item;
                })->toArray();
                $title = [
                    ['title'=>'姓名','field'=>'name','width'=>'15'],
                    ['title'=>'手机号','field'=>'mobile','width'=>'20','type'=>'string'],
                    ['title'=>'邮箱','field'=>'email','width'=>'20'],
                    ['title'=>'所属评审单位','field'=>'org_name','width'=>'25'],
                    ['title'=>'民族','field'=>'nation','width'=>'10'],
                    ['title'=>'性别','field'=>'sex','width'=>'10'],
                    ['title'=>'出生日期','field'=>'birthday','width'=>'20'],
                    ['title'=>'QQ','field'=>'qq','width'=>'20'],
                    ['title'=>'现住址','field'=>'address','width'=>'20'],
                    ['title'=>'学校','field'=>'school','width'=>'20'],
                    ['title'=>'专业','field'=>'speciality','width'=>'20'],
                    ['title'=>'学历','field'=>'education','width'=>'20'],
                    ['title'=>'现工作单位','field'=>'employer','width'=>'20'],
                    ['title'=>'职务','field'=>'position','width'=>'20'],
                    ['title'=>'参加工作时间','field'=>'work_date','width'=>'20'],
                    ['title'=>'从事安全生产工作时间','field'=>'position_date','width'=>'20'],
                    ['title'=>'专业技术职称','field'=>'professional','width'=>'20'],
                    ['title'=>'职称证书编号','field'=>'professional_number','width'=>'20'],
                    ['title'=>'安全评价师资格等级','field'=>'secure','width'=>'20'],
                    ['title'=>'安全评价师证书编号','field'=>'secure_number','width'=>'20'],
                    ['title'=>'注册安全工程师证书编号','field'=>'reg_secure_number','width'=>'20'],
                    ['title'=>'其他证书编号','field'=>'other_number','width'=>'20'],
                    ['title'=>'擅长专业','field'=>'major','width'=>'20'],
                    ['title'=>'个人学习及工作简历','field'=>'resume','width'=>'20'],
                    ['title'=>'聘用日期','field'=>'employ_date','width'=>'20'],
                    ['title'=>'受聘情况','field'=>'offer_info','width'=>'20'],
                ];
                ExcelModel::exportExcel($title, $res, '专家基本信息导出',true);
            }
            $res = $res->paginate($limit)->each(function ($item, $key) {
                $item = ExpertModel::codeToText($item);
                return $item;
            });
            result($res);
        }else{
            View::assign('title','首页');
            return view();
        }
    }

    /**
     * @param $id
     * @return void
     * @sysLog 修改专家信息
     */
    public function expertSave($id=0) {
        $request = $this->request->post();
        if(empty($request['mobile'])){
            result('',1002,'手机号不能为空');
        }
        if(empty($request['name'])){
            result('',1002,'姓名不能为空');
        }
        $id = ExpertModel::expertSave($request,$id);
        result(['id'=>$id]);
    }

    /**
     * @param $model
     * @return void
     * @sysLog 上传附件
     */
    public function upload($model='') {
        $file = request()->file('file');
        $result = FileModel::upload($file,$model);
        result($result);
    }

    public function getConfig() {
        $result['orgs'] = Db::table('top_org')->where(['is_del'=>0])->field('id,name')->select()->toArray();
        foreach ($result['orgs'] as $k=>$v){
            $result['orgs'][$k]['id'] = (string)$v['id'];
        }
        result($result);
    }

    public function getExpertInfo($id=0) {
        $fields = "a.id,a.name,a.email,a.mobile,a.status,a.org_id,b.nation,b.sex,b.birthday,b.qq,b.address,b.head,b.school,b.speciality,b.education,b.employer,b.position,b.work_date,b.position_date,b.professional,b.professional_number,b.secure,b.secure_number,b.reg_secure_number,b.other_number,b.major,b.resume,b.employ_date,b.offer_info";
        $re = Db::table('top_expert')->alias('a')
            ->leftJoin('top_expert_info b','a.id = b.expert_id')
            ->where(['a.id'=>$id])->field($fields)->find();
        if(!empty($re)){
            $re = ExpertModel::codeToText($re);
            $re['industry'] = [$re['industry'],$re['specialty']];
        }else{
            $re = [
                'name' => '',
                'head' =>'',
                'headUrl' => '',
                'mobile' => '',
                'sex' => '',
                'org_id' => '',
                'email' => '',
                'birthday' => '',
                'nation' => '',
                'qq' => '',
                'address' => '',
                'school' => '',
                'speciality' => '',
                'education' => '',
                'employer' => '',
                'position' => '',
                'work_date' => '',
                'position_date' => '',
                'professional' => '',
                'professional_number' => '',
                'secure' => '',
                'secure_number' => '',
                'reg_secure_number' => '',
                'other_number' => '',
                'major' => '',
                'employ_date' => '',
                'offer_info' => '',
                'resume' => '',
                'status' => '1',
            ];
        }
        result($re);
    }


    public function expertInfo($id=0) {
        $fields = "a.id,a.name,a.email,a.mobile,a.status,a.org_id,b.nation,b.sex,b.birthday,b.qq,b.address,b.head,b.school,b.speciality,b.education,b.employer,b.position,b.work_date,b.position_date,b.professional,b.professional_number,b.secure,b.secure_number,b.reg_secure_number,b.other_number,b.major,b.resume,b.employ_date,b.offer_info,o.name org_name";
        $re = Db::table('top_expert')->alias('a')
            ->leftJoin('top_expert_info b','a.id = b.expert_id')
            ->leftJoin('top_org o','o.id = a.org_id')
            ->where(['a.id'=>$id])->field($fields)->find();
        if(!empty($re)){
            $re = ExpertModel::codeToText($re);
            $re['industry'] = [$re['industry'],$re['specialty']];
        }else{
        }
        result($re);
    }


    //导入模板
    public function importTemplate()
    {
        $fields = [
            'f0' => '姓名',
            'f1' => '所属评审单位',
            'f2' => '手机号',
            'f4' => '邮箱',
            'f5' => '性别',
            'f6' => '出生日期',
            'f7' => '民族',
            'f8' => 'QQ',
            'f9' => '现住址',
            'f10' => '学校',
            'f11' => '专业',
            'f12' => '学历',
            'f14' => '现工作单位',
            'f15' => '职务',
            'f16' => '参加工作时间',
            'f18' => '从事安全生产工作时间',
            'f19' => '专业技术职称',
            'f20' => '职称证书编号',
            'f21' => '安全评价资格师等级',
            'f22' => '安全评价师证书编号',
            'f23' => '注册安全工程师证书编号',
            'f24' => '其他证书编号',
            'f25' => '擅长专业',
            'f26' => '聘用日期',
            'f27' => '受聘情况',
            'f28' => '个人学习及工作简历',
            'f31' => '状态',
        ];
        foreach ($fields as $k=>$v){
            $title[] = ['title'=>$v,'field'=>$k,'width'=>'20','type'=>'string'];
        }
        $data[] = [
            'f0' => '李四',
            'f1' => '成都市城市安全管理研究院',
            'f2' => '18292837483',
            'f4' => '<EMAIL>',
            'f5' => '男',
            'f6' => '2001-03-23',
            'f7' => '汉族',
            'f8' => '5432435',
            'f9' => '成都市锦江区',
            'f10' => '北京大学',
            'f11' => '专业',
            'f12' => '硕士',
            'f14' => '某某咨询公司',
            'f15' => '职务',
            'f16' => '2020-11-22',
            'f18' => '2020-11-22',
            'f19' => '专业技术职称',
            'f20' => '职称证书编号',
            'f21' => '安全评价资格师等级',
            'f22' => '安全评价师证书编号',
            'f23' => '注册安全工程师证书编号',
            'f24' => '其他证书编号',
            'f25' => '擅长专业',
            'f26' => '2020-11-22',
            'f27' => '受聘情况',
            'f28' => '个人学习及工作简历',
            'f31' => '正常',
        ];
        ExcelModel::exportExcel($title, $data, '专家信息导入模板');
    }


    //数据导入

    /**
     * @return void
     * @throws \think\Exception
     * @sysLog  数据导入
     */
    public function import()
    {
        $data = import("input");
        $dataHeader = importHeader("input");
        $result = ExpertModel::import($data,$dataHeader);
        $msg = count($result['success']).'条数据保存成功，'.count($result['repeat']).'条数据重复导入，'.count($result['fail']).'条数据处理失败。';
        result($result,0,$msg);
    }

}
