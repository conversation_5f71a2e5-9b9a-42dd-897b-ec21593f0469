<style>
.my-autocomplete li{line-height: normal;padding: 7px;}
.my-autocomplete li .name{text-overflow: ellipsis;overflow: hidden;}
.my-autocomplete li .addr{font-size: 12px;color: #b4b4b4;}
.my-autocomplete li .highlighted{color: #ddd;}
.el-cascader { width:100%;}
.el-form-item__content .el-input-group { vertical-align: middle;}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 150px;
  height: 150px;
  line-height: 150px;
  text-align: center;
}
.avatar {
  width: 150px;
  height: 150px;
  display: block;
}
</style>
<template>
  <el-dialog :title="title" :close-on-click-modal="false"  modal="false" :visible.sync="visible" width="600px" @close="refresh()" append-to-body="true" label-position="top">
    <el-form ref="form" :model="data" class="tableSearch" label-position="right" label-width="150px" :rules="rules">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="企业名称" prop="company_name">
            {{data.company_name}}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="行业/专业" prop="industry">
            {{data.industry}}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="评审标准" prop="standard_id">
            <el-select v-model="data.standard_id" size="mini" placeholder="请选择">
              <el-option v-for="item in config.standard" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="标准等级" prop="level">
            <el-select v-model="data.level" size="mini" placeholder="请选择">
              <el-option key="三级" label="三级" value="三级"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="是否机构参与" prop="is_advisory">
            <el-radio-group v-model="data.is_advisory">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
            <el-input v-if="data.is_advisory==1" v-model="data.advisory" size="mini" placeholder="机构名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24" style="text-align: center;">
          <el-button type="primary" @click="submit()">提交审核</el-button>
          <el-button @click="visible = false">关闭</el-button>
        </el-col>
      </el-row>
    </el-form>
  </el-dialog>
</template>
<script>
module.exports = {
  name: "add",
  // 模板导入区
  components: {
  },
  data:function() {
    return {
      id:0,
      visible: false,
      title: '新增',
      loading: false,
      data: {
      },
      rules: {
      },
      config:[],
      imageUrl:'',
    }
  },
  mounted: function(){
    // this.getConfig();
  },
  created:function(){
  },
  methods: {
    /**
     * 打开弹窗调用方法
     * */
    open: function (row) {
      console.log(row)
      row = row?row:{id:0};
      var _this =this;
      _this.visible = true;
      _this.getConfig();
      _this.getInfo(row.id);
    },
    submit: function () {
      var _this = this;
      var param = _this.data;
      this.$refs.form.validate(function (valid) {
        if(valid){
          axios.post("standardSave", param).then(function (res) {
            _this.$message({
              message: res.data.msg,
              type: res.data.type
            });
            if (res.data.code == 0) {
              _this.visible = false;
              _this.$emit("ok");
            }
          }).catch(function (error) {
            console.log("出现错误:",error);
          });
        }
      });
    },
    getConfig:function(){
      var _this = this;
      axios.post('getConfig', {}).then(function (res) {
        if (res.data.code == 0) {
          _this.config = res.data.data;
        }
      }).catch(function (error) {
        console.log("出现错误:",error);
      });
    },
    getInfo:function(id){
      var _this = this;
      _this.loading = true;
      axios.post('getStandardInfo', {id:id}).then(function (res) {
        _this.loading = false;
        if (res.data.code == 0) {
          _this.data = res.data.data;
        }
      }).catch(function (error) {
        _this.loading = false;
        console.log("出现错误:",error);
      });
    },
    refresh: function () {
      this.$emit("refresh");
    },
    changeweekBegin(data){
      console.log(data);
    },
  }
}
</script>