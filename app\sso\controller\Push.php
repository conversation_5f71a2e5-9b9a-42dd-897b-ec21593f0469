<?php

namespace app\sso\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use hg\apidoc\annotation as Apidoc;
use app\Request;
use think\App;
use think\facade\Db;

/**
 * @Apidoc\Title("数据推送")
 * @Apidoc\Group("User")
 * @Apidoc\Sort(3)
 */
class Push
{
    private $appid ="";
    private $appkey ="";

    public function __construct()
    {
        $secsConfig = getSecsConfig();
        $this->appid = $secsConfig['appid'];
        $this->appkey = $secsConfig['appkey'];
    }


    /**
     * NotHeaders
     * @Apidoc\Title("专家分配结果接收")
     * @Apidoc\Desc("专家分配结果通知/专家变更结果通知")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("日程")
     * @Apidoc\Param("taskcode", type="string", require=true,desc="评审任务单号"),
     * @Apidoc\Param("experts", type="list", require=true,desc="分配专家列表",children={
     * @Apidoc\Param("expert_id", type="string", require=true,desc="专家唯一id"),
     * @Apidoc\Param("expert_name", type="string", require=true,desc="专家姓名"),
     * @Apidoc\Param("mobile", type="string", require=true,desc="专家手机号"),
     * @Apidoc\Param("email", type="string", desc="专家邮箱"),
     * @Apidoc\Param("secsIsCayExpert", type="bool", desc="是否是城安院专家"),
     * }),
     * @Apidoc\Param("time", type="string", require=true,desc="请求时间：2025-01-02 09:12:23"),
     * @Apidoc\Param("sign", type="string", require=true,desc="签名，签名规则：MD5(taskcode+time+秘钥key)"),
     */
    public function tasksExpert($id=0) {
        $this->logRequest('tasksExpert');
        $params = \request()->param();
		if(empty($params['taskcode']) && empty($params['time'])){
			 result('',8001,'taskcode 或 time 为空');
		}
        $sign = md5($params['taskcode'].$params['time'].$this->appkey);
        if($sign!==$params['sign']){
            result('',8001,'签名错误');
        }
        
        //根据taskcode查询任务信息
        $task = Db::table('top_org_tasks')->where('code',$params['taskcode'])->find();

        if(!$task){
            result('',8001,'任务不存在');
        }
        //数据写入任务专家表
        Db::startTrans();
        try {
            if($params['experts']){
                //判断传入的专家是否在专家表中
                $expertIds = Db::table('top_expert')
                    ->where('expert_id','in',array_column($params['experts'],'expert_id'))
                    ->column('expert_id');
                //如果有需要插入库的专家，则插入库
                if(count($expertIds)!=count($params['experts'])){
                    foreach ($params['experts'] as $v){
                        if(!in_array($v['expert_id'],$expertIds)){
                            //写专家表
                            $insertData = [
                                'openid'=>'',
                                'unionid'=>'',
                                'mobile'=>$v['mobile'],
                                'name'=>$v['expert_name'],
                                'reg_ip'=>get_ip(),
                                'reg_time'=>date('Y-m-d H:i:s'),
                                'expert_id'=>$v['expert_id'],
                                'username'=>$v['mobile'],
                                'password'=>'66Hky1XQL1oTg',
                                'salt'=>'66cbd97f70c51',
                                'status'=>'1',
                                'org_id'=>'1',
                            ];
                            Db::table('top_expert')->insert($insertData);

                        }
                    }
                }

                //比对是否有新的专家，插入新的专家，并删除本次传入外的专家
                $expertIds = [];
                //获取安标系统和专家系统的对应关系
                $expertIdDicts = Db::table('top_expert')->column('id','expert_id');
                foreach($params['experts'] as $v){
                    $expertIds[] = $expertIdDicts[$v['expert_id']];
                }
                //计算出新增的专家
                $newExpertIds = array_diff($expertIds,Db::table('top_org_tasks_experts')->where('tasks_id',$task['id'])->column('expert_id'));
                //删除本次传入外的专家
                $res = Db::table('top_org_tasks_experts')
                    ->where('tasks_id',$task['id'])
                    ->where('expert_id','not in',$expertIds)
                    ->delete();
                $data = [];
                foreach($params['experts'] as $v){
                    //如果不在新的专家列表中则跳过
                    if(!in_array($expertIdDicts[$v['expert_id']],$newExpertIds)){
                        continue;
                    }
                    $data[] = [
                        'tasks_id' => $task['id'],
                        'expert_id' => $expertIdDicts[$v['expert_id']],
                        'expert_name' => $v['expert_name'],
                        'mobile' => $v['mobile'],
                        'email' => isset($v['email']) ? $v['email'] : '',
                        'status' => 7,
                        'is_cay_expert' => $v['secsIsCayExpert'] ? 1 : 0,
                    ];
                }
                $res = Db::table('top_org_tasks_experts')->strict(false)->insertAll($data);
                if(!$res){
                     result('',8001,"写入数据失败");
                }
            }
            /** 
            更新任务状态
            0: 待接收
            1: 已接收
			2: 已推送
            3: 待确认
            5: 已驳回
            7: 审核通过
            8: 评审中
            9: 评审结束
            **/
            $res = Db::table('top_org_tasks')->where('code',$params['taskcode'])->update(['status'=>3]);
            Db::commit();
			$this->logRequest('tasksExpert',$res);
            result($res);
        } catch (\Exception $e) {
            Db::rollback();
			$this->logRequest('tasksExpert',$e->getMessage());
            result('',8001,$e->getMessage());
        }
    }

    /**
     * NotHeaders
     * @Apidoc\Title("专家请假的通知接口")
     * @Apidoc\Desc("取消派遣/请假的通知接口")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("日程")
     * @Apidoc\Param("taskcode", type="string", require=true,desc="任务单号"),
     * @Apidoc\Param("experts", type="list", require=true,desc="专家列表",children={
     * @Apidoc\Param("expert_id", type="string", require=true,desc="专家唯一id"),
     * @Apidoc\Param("expert_name", type="string", require=true,desc="专家姓名"),
     * @Apidoc\Param("secsIsCayExpert", type="bool", require=true,desc="是否是城安院专家"),
     * }),
     * @Apidoc\Param("time", type="string", require=true,desc="请求时间：2025-01-02 09:12:23"),
     * @Apidoc\Param("sign", type="string", require=true,desc="签名，签名规则：MD5(taskcode+time+秘钥key)"),
     */
    public function expertCancel($id=0) {
		$this->logRequest('expertCancel');
        $params = \request()->param();
		if(empty($params['taskcode']) && empty($params['time'])){
			 result('',8001,'taskcode 或 time 为空');
		}
        $sign = md5($params['taskcode'].$params['time'].$this->appkey);
        if($sign!==$params['sign']){
            result('',8001,'签名错误');
        }
        //根据taskcode查询任务信息
        $task = Db::table('top_org_tasks')->where('code',$params['taskcode'])->find();
        if(!$task){
            result('',8001,'任务不存在');
        }

        //获取专家的id
        $expertIds = [];
        foreach($params['experts'] as $v){
            $expertIds[] = $v['expert_id'];
        }
        //更新专家任务表的是否取消
        Db::startTrans();
        try {
            $res = Db::table('top_org_tasks_experts')
                ->where('tasks_id',$task['id'])
                ->where('expert_id','in',$expertIds)
                ->update(['is_cancel'=>1]);
            $res = Db::table('top_org_tasks')->where('code',$params['taskcode'])->update(['status'=>1]);
            Db::commit();
			$this->logRequest('expertCancel',$res);
            result($res);    
        } catch (\Exception $e) {
            Db::rollback();
			$this->logRequest('expertCancel',$e->getMessage());
            result('',8001,$e->getMessage());
        }
    }


    public function logRequest($methodName, $response = null)
    {
        if(empty($response)){
			$request = \request();
			$logContent = sprintf(
				"[%s] %s\n请求来源: %s\n请求参数: %s\n请求头: %s\n",
				date('Y-m-d H:i:s'),
				$methodName,
				$request->ip(),
				json_encode($request->param(), JSON_UNESCAPED_UNICODE),
				json_encode($request->header(), JSON_UNESCAPED_UNICODE)
			);
		}else{
			$logContent = sprintf(
				"[%s] %s\n返回结果: %s\n\n",
				date('Y-m-d H:i:s'),
				$methodName,
				json_encode($response, JSON_UNESCAPED_UNICODE)
			);
		}

        $logFile = config('app.secs_log_path')."pull.log";
        file_put_contents($logFile, $logContent, FILE_APPEND);
    }


}
