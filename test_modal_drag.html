<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模态框拖拽测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            min-height: 100vh;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .btn {
            padding: 10px 20px;
            margin: 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        .btn-danger { background-color: #dc3545; color: white; }

        /* 模态框样式 - 修复后的版本 */
        .modal { 
            display: none; 
            position: fixed; 
            z-index: 1000; 
            left: 0; 
            top: 0; 
            width: 100%; 
            height: 100%; 
            background-color: rgba(0,0,0,0.5); 
        }
        
        .modal-content { 
            background-color: white; 
            margin: 2% auto; 
            padding: 20px; 
            border-radius: 8px; 
            width: 600px; 
            max-width: 90%; 
            max-height: 90vh; 
            position: relative; 
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #e9ecef;
            margin-bottom: 20px;
            cursor: move;
            user-select: none;
        }

        .modal-header h3 {
            margin: 0;
            color: #2c3e50;
            font-size: 18px;
            pointer-events: none;
        }

        .modal-header .close {
            font-size: 24px;
            font-weight: bold;
            color: #aaa;
            cursor: pointer;
            border: none;
            background: none;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s;
        }

        .modal-header .close:hover {
            color: #000;
            background-color: #f8f9fa;
        }

        .form-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); 
            gap: 15px; 
            max-height: 60vh; 
            overflow-y: auto; 
            padding-right: 10px;
        }

        .form-row { margin-bottom: 15px; }
        .form-row label { 
            display: block; 
            margin-bottom: 5px; 
            font-weight: bold; 
            color: #2c3e50; 
        }
        .form-row input { 
            width: 100%; 
            padding: 8px; 
            border: 1px solid #ddd; 
            border-radius: 4px; 
            font-size: 14px;
        }

        .modal-footer {
            position: sticky;
            bottom: 0;
            background: white;
            padding: 15px 0 0 0;
            margin-top: 20px;
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .drag-hint {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-size: 12px;
            color: #1976d2;
        }

        .position-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            z-index: 2000;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>模态框拖拽功能测试</h1>
        <p>这个页面用于测试修复后的模态框拖拽效果。</p>
        
        <h2>测试说明</h2>
        <ul>
            <li>✅ 点击模态框头部可以拖拽移动</li>
            <li>✅ 第一次点击不会突然跳动</li>
            <li>✅ 拖拽时有边界限制，不会完全移出屏幕</li>
            <li>✅ 双击头部可以重置到屏幕中心</li>
            <li>✅ 关闭按钮不会触发拖拽</li>
        </ul>
        
        <button class="btn btn-primary" onclick="showDragTestModal(1)">测试模态框 1</button>
        <button class="btn btn-success" onclick="showDragTestModal(2)">测试模态框 2</button>
        <button class="btn btn-warning" onclick="showDragTestModal(3)">测试模态框 3</button>
        
        <h2>修复内容</h2>
        <ul>
            <li>✅ 修复了第一次点击时窗口突然移动的问题</li>
            <li>✅ 正确获取模态框的初始位置</li>
            <li>✅ 改进了拖拽边界限制逻辑</li>
            <li>✅ 添加了双击重置位置功能</li>
            <li>✅ 优化了拖拽时的视觉反馈</li>
        </ul>
    </div>

    <div class="position-info" id="positionInfo" style="display: none;">
        位置信息:<br>
        X: <span id="posX">0</span>px<br>
        Y: <span id="posY">0</span>px
    </div>

    <script>
        let modalCounter = 0;

        function showDragTestModal(num) {
            modalCounter++;
            const modalId = `dragTestModal_${modalCounter}`;
            const headerId = `dragTestHeader_${modalCounter}`;
            
            // 创建测试模态框
            let modalHtml = `<div class="modal" id="${modalId}" style="display: block;">`;
            modalHtml += '<div class="modal-content">';
            modalHtml += `<div class="modal-header" id="${headerId}">`;
            modalHtml += `<h3>拖拽测试模态框 ${num}</h3>`;
            modalHtml += `<span class="close" onclick="closeModal('${modalId}')">&times;</span>`;
            modalHtml += '</div>';
            
            modalHtml += '<div class="drag-hint">';
            modalHtml += '💡 提示：拖拽头部可以移动窗口，双击头部重置到中心位置';
            modalHtml += '</div>';
            
            modalHtml += '<form>';
            modalHtml += '<div class="form-grid">';
            
            // 生成一些测试字段
            for (let i = 1; i <= 8; i++) {
                modalHtml += `<div class="form-row">
                    <label>测试字段 ${i}</label>
                    <input type="text" value="测试数据 ${i}">
                </div>`;
            }
            
            modalHtml += '</div>';
            
            modalHtml += '<div class="modal-footer">';
            modalHtml += `<button type="button" class="btn btn-danger" onclick="closeModal('${modalId}')">关闭</button>`;
            modalHtml += '<button type="submit" class="btn btn-primary">保存</button>';
            modalHtml += '</div>';
            
            modalHtml += '</form></div></div>';
            
            // 添加模态框到页面
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            
            // 启用拖拽功能
            makeDraggable(modalId, headerId);
            
            // 显示位置信息
            document.getElementById('positionInfo').style.display = 'block';
            updatePositionInfo(modalId);
            
            // 绑定表单提交事件
            document.querySelector(`#${modalId} form`).addEventListener('submit', function(e) {
                e.preventDefault();
                alert('表单提交成功！（这只是测试）');
            });
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.remove();
            }
            
            // 如果没有其他模态框，隐藏位置信息
            if (!document.querySelector('.modal')) {
                document.getElementById('positionInfo').style.display = 'none';
            }
        }

        function updatePositionInfo(modalId) {
            const modal = document.getElementById(modalId);
            if (!modal) return;
            
            const rect = modal.querySelector('.modal-content').getBoundingClientRect();
            document.getElementById('posX').textContent = Math.round(rect.left);
            document.getElementById('posY').textContent = Math.round(rect.top);
        }

        // 修复后的拖拽功能
        function makeDraggable(modalId, headerId) {
            const modal = document.getElementById(modalId);
            const header = document.getElementById(headerId);
            const modalContent = modal.querySelector('.modal-content');

            if (!modal || !header) return;

            let isDragging = false;
            let startX = 0;
            let startY = 0;
            let modalStartX = 0;
            let modalStartY = 0;

            // 设置初始样式
            header.style.cursor = 'move';
            header.style.userSelect = 'none';

            header.addEventListener('mousedown', dragStart);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', dragEnd);

            function dragStart(e) {
                // 不拖动关闭按钮
                if (e.target.classList.contains('close')) return;
                
                // 只在头部区域开始拖拽
                if (e.target === header || header.contains(e.target)) {
                    isDragging = true;
                    
                    // 记录鼠标起始位置
                    startX = e.clientX;
                    startY = e.clientY;
                    
                    // 获取模态框当前位置
                    const modalRect = modalContent.getBoundingClientRect();
                    modalStartX = modalRect.left;
                    modalStartY = modalRect.top;
                    
                    // 设置拖拽状态样式
                    header.style.cursor = 'grabbing';
                    modalContent.style.position = 'fixed';
                    modalContent.style.margin = '0';
                    modalContent.style.left = modalStartX + 'px';
                    modalContent.style.top = modalStartY + 'px';
                    
                    // 防止文本选择
                    e.preventDefault();
                }
            }

            function drag(e) {
                if (!isDragging) return;
                
                e.preventDefault();
                
                // 计算鼠标移动距离
                const deltaX = e.clientX - startX;
                const deltaY = e.clientY - startY;
                
                // 计算新位置
                let newX = modalStartX + deltaX;
                let newY = modalStartY + deltaY;
                
                // 限制拖动范围，确保模态框不会完全移出视窗
                const modalWidth = modalContent.offsetWidth;
                const modalHeight = modalContent.offsetHeight;
                const windowWidth = window.innerWidth;
                const windowHeight = window.innerHeight;
                
                // 至少保留50px在视窗内
                const minVisible = 50;
                newX = Math.max(-modalWidth + minVisible, Math.min(newX, windowWidth - minVisible));
                newY = Math.max(0, Math.min(newY, windowHeight - minVisible));
                
                // 应用新位置
                modalContent.style.left = newX + 'px';
                modalContent.style.top = newY + 'px';
                
                // 更新位置信息
                updatePositionInfo(modalId);
            }

            function dragEnd(e) {
                if (isDragging) {
                    isDragging = false;
                    header.style.cursor = 'move';
                }
            }

            // 双击头部重置位置到中心
            header.addEventListener('dblclick', function(e) {
                if (e.target.classList.contains('close')) return;
                
                const windowWidth = window.innerWidth;
                const windowHeight = window.innerHeight;
                const modalWidth = modalContent.offsetWidth;
                const modalHeight = modalContent.offsetHeight;
                
                const centerX = (windowWidth - modalWidth) / 2;
                const centerY = (windowHeight - modalHeight) / 2;
                
                modalContent.style.position = 'fixed';
                modalContent.style.margin = '0';
                modalContent.style.left = centerX + 'px';
                modalContent.style.top = centerY + 'px';
                modalContent.style.transition = 'all 0.3s ease';
                
                // 移除过渡效果
                setTimeout(() => {
                    modalContent.style.transition = '';
                    updatePositionInfo(modalId);
                }, 300);
            });
        }

        // 点击模态框外部关闭
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal')) {
                e.target.remove();
                if (!document.querySelector('.modal')) {
                    document.getElementById('positionInfo').style.display = 'none';
                }
            }
        });

        // ESC键关闭所有模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const modals = document.querySelectorAll('.modal');
                modals.forEach(modal => modal.remove());
                document.getElementById('positionInfo').style.display = 'none';
            }
        });
    </script>
</body>
</html>
