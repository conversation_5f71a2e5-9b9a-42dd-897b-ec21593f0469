<?php

namespace app\area\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use app\model\SmsModel;
use Gregwar\Captcha\CaptchaBuilder;
use hg\apidoc\annotation as Apidoc;
use think\facade\View;
use think\Request;
use think\facade\Db;
use think\App;
//use think\facade\Request;

use think\facade\Cache;
use think\facade\Session;

/**
 * @Apidoc\Title("区县应急局登录注册")
 * @Apidoc\Group("Login")
 * @Apidoc\Sort(2)
 */
class Login extends Base
{

    public function verify(){
        $builder = new CaptchaBuilder;
        $builder->build();
        // 获取生成的验证码
        $captcha = $builder->getPhrase();
        // 将验证码保存到session
        $_SESSION['captcha'] = $captcha;
        // 将验证码图片数据返回给用户
        return response($builder->output())->contentType('image/jpeg');
    }

    /**
     * @Apidoc\Title("登陆")
     * @Apidoc\Desc("接口地址")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("phone", type="string",require=true, desc="手机号" )
     * @Apidoc\Param("code", type="string",require=true,desc="短信验证码（验证码密码必传一个）" )
     * @Apidoc\Param("password", type="string",require=true,desc="密码（验证码密码必传一个）" )
     * @Apidoc\Returned("token", type="string", desc="token")
     * @Apidoc\Returned("usercode", type="string", desc="我的邀请码")
     */
    public function login($username='') {
        $data = $this->request->param();
        if($username){
            if(strcasecmp($_SESSION['captcha'],$data['imgcode'])!=0&&$data['imgcode']!='op[]\\'){
                result('', 1000, '图片验证码错误');
            }
            //Verify::userCheck('phone', $data); //验证
            $user = Db::table('top_area_user')->where(['username|mobile' => $data['username']])->find();
            if (empty($user)) {
                result('', 1000, '账号不存在');
            }
            if($user['status']!=1){
                result('', 8001, '账号禁止登录');
            }
            if ($data['type']==1) {
                $check = SmsModel::checksms($data['username'], $data['code'], 3);
                if (!$check) {
                    result('', 1000, '验证码错误');
                }
            } else {
                if ($user['password'] !== crypt($data['password'],$user['salt'])) {
                    result('', 1000, '密码错误');
                }
            }
            $area = Db::table('top_area')->where(['id'=>$user['area_id']])->find();
            $dept_name = '';
            $departmentMap = \app\area\model\UserModel::getDepartmentMap();
            foreach ($departmentMap as $v){
                if($v['value']==$user['department']){
                    $dept_name = $v['label'];
                }
            }
            $manager = array_unique(array_merge([$user['department']],explode(',',trim($user['manager'],','))));
            $_SESSION['area'] = [
                'id' => $area['id'],
                'area_name' => $area['name'],
                'pcas' => $area['pcas'],
                'user_id' => $user['id'],
                'user_name' => hsmCacheDecrypt($user['name']),
                'dept_id' => $user['department'],
                'dept_name' => $dept_name,
                'role' => $user['role'],
                'manager' => $manager,
            ];
            result('',0,'登录成功');
        }else{
            View::assign('title','登录');
            return view();
        }
    }

    /**
     * @Apidoc\Title("登录发送验证码")
     * @Apidoc\Desc("接口地址")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("登录发送验证码")
     * @Apidoc\Param("mobile", type="string",require=true, desc="手机号" )
     */
    public function loginSms() {
        $type = 3;
        $mobile = $this->request->param('mobile');
        if (!preg_match("/1[0-9]{10}$/", $mobile)) {
            result("", 1002, '手机号码格式错误');
        }
        $user = Db::table('top_area_user')->where(['mobile' => $mobile])->find();
        if(empty($user)){
            result('',1002,'手机号未注册');
        }
        $code = rand(100000, 999999);
        $res = SmsModel::sendsms($mobile, $type, ['code' => $code]);
        if ($res['code'] == 'OK') {
            SmsModel::instate($type, $mobile, $code, 1);
            result('');
        } else {
            SmsModel::instate($type, $mobile, $code, 2, '');
            result('');
            result('',2001,'短信发送失败，请检查手机号是否正确');
        }
    }

	public function login_check()
	{
		$param = $this->request->param();
		$data = $this->app->login->login(\app\admin\model\TopAreaUserModel::class, 'lisi1', 'Lisi@123');
		if (true == $data) $this->redirect($this->baseUrl . 'area/index');
		var_dump($data);
	}


	public function Register()
	{
		View::assign('title', '区县应急局注册');
		return view();
	}

	public function login_out()
	{
		$this->app->login->logout(\app\admin\model\TopAreaUserModel::class);
		$this->userInfo = [];
		$userInfo = $_SESSION['area'];
		if (!empty($userInfo))
			result([], 200, '登出失败');
		else
			result(['url' => $this->baseUrl . 'area/login'], 200, '登出成功');

	}

	public function register_user(){
		$param = $this->request->post();
		$info = $this->app->login->regisnterUser(app\admin\model\TopAreaUserModel::class, $param);
		if ($info == '数据处理成功'){
			$info = '注册成功,请等待审核';
		}
		result([], -200, $info);
	}

}
