<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>现场评审</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/company.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .my-title { font-weight: 700;line-height: 30px; margin-top: 20px;}
        .my-content { text-indent: 30px;line-height: 40px;}
        .my-files { margin-left: 30px;}
        .my-files label { float: left;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <el-row :gutter="20">
        <el-col>
            <el-descriptions class="margin-top" title="现场评审" column="3" border>
                <template slot="extra">
                    <el-button type="primary" size="small" v-if="'{$task.status}'==7" @click="start">开始评审</el-button>
                    <el-button type="success" size="small" v-if="'{$task.status}'==8" @click="end('success')">结束评审</el-button>
                    <el-button type="success" size="small" v-if="'{$task.status}'==8" @click="yaosu">要素评审</el-button>
                    <el-button type="danger" size="small" v-if="'{$task.status}'==8" @click="end('error')">终止评审</el-button>
                </template>
                <el-descriptions-item :labelStyle="{'width':'100px'}">
                    <template slot="label">
                        <i class="el-icon-office-building"></i>
                        单位名称
                    </template>
                    {$grading.company_name}
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        <i class="el-icon-office-building"></i>
                        行业/专业
                    </template>
                    {$grading.industry}/{$grading.specialty}
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        <i class="el-icon-office-building"></i>
                        申请等级
                    </template>
                    {$grading.level}
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        <i class="el-icon-user"></i>
                        法定代表人
                    </template>
                    {$grading.legal}
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        <i class="el-icon-mobile-phone"></i>
                        电话
                    </template>
                    {$grading.legal_mobile}
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        <i class="el-icon-mobile-phone"></i>
                        手机
                    </template>
                    {$grading.legal_email}
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        <i class="el-icon-user"></i>
                        安全管理联系人
                    </template>
                    {$grading.manager}
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        <i class="el-icon-mobile-phone"></i>
                        电话
                    </template>
                    {$grading.manager_mobile}
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        <i class="el-icon-mobile-phone"></i>
                        手机
                    </template>
                    {$grading.manager_email}
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        <i class="el-icon-mobile-phone"></i>
                        状态
                    </template>
                    <el-tag v-if="'{$task.status}'==5" type="danger">终止评审</el-tag>
                    <el-tag v-if="'{$task.status}'==7" type="success">等待现场评审</el-tag>
                    <el-tag v-if="'{$task.status}'==8" type="warning">现场评审中</el-tag>
                    <el-tag v-if="'{$task.status}'==9" type="info">结束评审</el-tag>
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        <i class="el-icon-location-outline"></i>
                        单位地址
                    </template>
                    {$grading.address}
                </el-descriptions-item>
            </el-descriptions>
        </el-col>
        <el-col v-if="'{$task.status}'==8">
            <el-steps :active="active" finish-status="" simple style="margin-top: 20px">
                <el-step style="cursor: pointer;" title="首次会议" @click.native="on_click(0)"></el-step>
                <el-step style="cursor: pointer;" title="现场评审" @click.native="on_click(1)"></el-step>
                <el-step style="cursor: pointer;" title="末次会议" @click.native="on_click(2)"></el-step>
            </el-steps>
        </el-col>
        <el-col v-for="(item,key) in review_flow" v-if="'{$task.status}'==8" v-show="key == active">
            <template v-if="item.work" v-for="(v,k) in item.work">
                <div class="my-title">{{k+1}}、{{v.title}}
                    <el-button v-if="key==1&&k==2" type="primary" @click="pingshen" size="mini">评审汇总</el-button>
                    <el-button v-if="v.status==0" type="primary" @click="success(v.id)" size="mini">完成</el-button>
                    <el-button v-if="v.status==2" type="info" disabled size="mini">已完成</el-button></div>
                <div class="my-content">{{v.content}}</div>
                <div v-if="v.type=='bishi'">
                    <el-image
                        style="width: 100px; height: 100px;margin-left: 30px;"
                        src="__PUBLIC__/assets/images/qr.png"
                        fit="fill"></el-image>
                </div>
                <div v-if="v.type=='pinfen'">
                    <el-table
                            :data="review_list"
                            border
                    style="width: 541px;">
                        <el-table-column
                                prop="title"
                                label="要素"
                                width="180">
                        </el-table-column>
                        <el-table-column
                                prop="user_name"
                                label="专家"
                                width="180">
                        </el-table-column>
                        <el-table-column
                                prop="status"
                                label="状态"
                                width="180">
                            <template slot-scope="scope">
                                <el-tag v-if="scope.row.status==1" type="danger">未提交</el-tag>
                                <el-tag v-if="scope.row.status==2" type="success">已提交</el-tag>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div v-if="v.files" v-for="v1 in v.files" class="my-files">
                    <label>{{v1.name}}：</label>
                    <el-upload
                            action="/module/upload/upload.php"
                            list-type="picture-card"
                            :file-list="v1.filelists"
                            :limit="10"
                            :on-success="(response,file,fileList)=>uploadSuccess(response,file,fileList,v.id)"
                            :before-upload="uploadBefore"
                            :on-preview="handlePictureCardPreview"
                            :on-remove="(file,fileList)=>handleRemove(file,fileList,v.id)">
                        <i class="el-icon-plus"></i>
                    </el-upload>
                </div>
            </template>
            <div style="text-align: center;">
                <el-button v-show="active<2" @click="next()">下一步</el-button>
            </div>
        </el-col>
    </el-row>
    <el-dialog :visible.sync="dialogVisible">
        <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
    <pingshen ref="pingshen" @ok="index"></pingshen>
    <end ref="end" @ok="index"></end>
</div>
<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>
<script type="text/babel">
    Vue.use(httpVueLoader);
    new Vue({
        el: '#app',
        data: function () {
            return{
                active:0,
                dialogImageUrl:'',
                dialogVisible:false,
                searchData:{
                    status:1,
                },
                review_flow:[],
                review_list:[],
                form: {
                    'company_name':''
                },
                loading: false,
                page_data: {
                    limit: 10,
                    page: 1,
                    total:10,
                },
                order:'',
                sort:'',
                files: [],
                single:true,
                multiple:true,
                data: [],
                exceldata:[],
                dumppage:1,
                ws:{},
                dumpshow:false,
                percentage:0,
                filename:'',
                tableHeight:document.documentElement.clientHeight-250,
                isAdvanced:false,
                is_clear:false,
                tabsActive:1,
                num_v1:0,
                num_v2:0,
                num_v3:0,
                num_v4:0,
            }
        },
        components:{
            'pingshen':'url:/general/secure/app/expert/view/reviewtasks/vue/pingshen.vue?v=1',
            'end':'url:/general/secure/app/expert/view/reviewtasks/vue/end.vue?v=1',
            'info':'url:/general/secure/app/city/view/check/vue/info.vue?v=1',
            'check':'url:/general/secure/app/city/view/check/vue/check.vue?v=1',
        },
        methods: {
            on_click(e){
                console.log(e)
                if(e != "" || e != null){ this.active = e }
            },
            next(){
                this.active++;
            },
            start(){
                this.$confirm('确定评审人员已到齐，开始评审?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.loading = true;
                    axios.post('reviewStart',{id:'{$task.id}'}).then(res => {
                        this.loading = false
                        if(res.data.code === 0){
                            location.reload();
                        }else{
                            this.$message.error(res.data.msg);
                        }
                    })
                }).catch(() => {
                    /*this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });*/
                });
            },
            index(){
                var _this = this;
                _this.loading = true
                axios.post('review',{id:'{$re.id}'}).then(res => {
                    _this.loading = false
                    if(res.data.code === 0){
                        _this.review_flow = res.data.data.review_work;
                        _this.review_list = res.data.data.review_list;
                    }else{
                        _this.$message.error(res.data.msg);
                    }
                })
            },
            uploadBefore(file) {
                const isJPG = file.type === 'image/jpeg';
                const isPNG = file.type === 'image/png';
                if(!isJPG&&!isPNG){
                    this.$message.error('请上传jpg图片');
                }
                return isJPG||isPNG;
            },
            uploadSuccess(res, file,fileList,id) {
                var params = {
                    id:id,
                    filelist:fileList,
                };
                axios.post('updateFiles',params).then(res => {
                    this.loading = false
                    if(res.data.code === 0){
                        //this.index();
                    }else{
                        this.$message.error(res.data.msg);
                    }
                })
            },
            handleRemove(file, fileList,id) {
                var params = {
                    id:id,
                    filelist:fileList,
                };
                axios.post('updateFiles',params).then(res => {
                    this.loading = false
                    if(res.data.code === 0){
                        //this.index();
                    }else{
                        this.$message.error(res.data.msg);
                    }
                })
                // console.log(fileList);
                this.filelist = fileList;
            },
            handlePictureCardPreview(file) {
                this.dialogImageUrl = file.url;
                this.dialogVisible = true;
            },
            pingshen() {
                this.$refs.pingshen.open('{$task.id}');
            },
            end(t) {
                var _this = this;
                if(t=='success'){
                    var a = true;
                    _this.review_flow.forEach(function(i,index){
                        i.work.forEach(function(item,index){
                            if(item.status!=2){
                                a = false;
                            }
                        })
                    });
                    if(!a){
                        _this.$message.error('有未完成工作');
                        return false;
                    }
                    _this.$confirm('确定评审完成?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        _this.loading = true;
                        var param = {
                            id:'{$task.id}',
                            status:1,
                        };
                        axios.post('end',param).then(res => {
                            _this.loading = false
                            if(res.data.code === 0){
                                location.reload();
                            }else{
                                _this.$message.error(res.data.msg);
                            }
                        })
                    }).catch(() => {
                    });
                }else{
                    this.$refs.end.open('{$task.id}');
                }
            },
            yaosu() {
                openTab2('', 'reviewyaosu','现场评审','/general/secure/index.php/expert/reviewtasks/elementReview?id={$re.id}');
            },
            success(id) {
                this.$confirm('确定该项工作已完成?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.loading = true;
                    axios.post('worksuccess',{id:id}).then(res => {
                        this.loading = false
                        if(res.data.code === 0){
                            this.index();
                        }else{
                            this.$message.error(res.data.msg);
                        }
                    })
                }).catch(() => {
                });
            },
            /*流程办理*/
            info : function(row) {
                this.$refs.info.open(row);
            },
            /*流程办理*/
            checkData : function(row) {
                this.$refs.check.open(row);
            },
            selection(selection) {
                this.ids = selection.map(item => item.id)
                this.single = selection.length !== 1
                this.multiple = !selection.length
            },
            handleRowClick(row, rowIndex,event){
                if(event.target.className !== 'el-input__inner'){
                    this.$refs.multipleTable.toggleRowSelection(row)
                }
            },
            rowClass ({ row, rowIndex }) {
                for(let i=0;i<this.ids.length;i++) {
                    if (row.id === this.ids[i]) {
                        return 'rowLight'
                    }
                }
            },
            sortChange(val){
                if(val.order === 'descending'){
                    this.order= 'desc'
                }
                if(val.order === 'ascending'){
                    this.order= 'asc'
                }
                this.sort = val.prop
                this.index()
            },
            fn(method){
                this[method](this.ids)
            },
            onSubmit() {
                this.page_data.page = 1;
                this.index();
            },
            searchFun(){
                this.page_data.page = 1;
                this.index();
            },
            advancedToggle(data){
                this.isAdvanced=data;
            },
            clearSearch : function() {
                this.form.company_name = '';
                this.index();
            },
            selectTab(tab){
                this.searchData.status = tab
                this.tabsActive = tab
                this.form.company_name = '';
                this.index()
            },
            resetSearch:  function(){
                var _this = this;
                _this.searchData.manage_address = '';
                _this.searchData.create_time = '';
            },
            //计算table高度(动态设置table高度)
            getTableHeight() {
                let tableH = 250; //距离页面下方的高度
                let tempHeight = window.innerHeight - tableH;
                if (tempHeight <= 250) {
                    this.tableHeight =250;
                }
                this.tableHeight = tempHeight;
            },
        },
        mounted(){
            //挂载window.onresize事件(动态设置table高度)
            let _this = this;
            window.onresize = () => {
                if (_this.resizeFlag) {
                    clearTimeout(_this.resizeFlag);
                }
                _this.resizeFlag = setTimeout(() => {
                    _this.getTableHeight();
                    _this.resizeFlag = null;
                }, 1000);
            };
            _this.index()
        },
    })
</script>
</body>
</html>