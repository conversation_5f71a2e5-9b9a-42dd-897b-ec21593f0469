<?php

namespace app\model;

use think\Model;
use think\facade\Db;

class SmsModel extends Model {

    //发送短信验证码
    public static function sendsms($phone, $type, $data) {
        $types = config('shop.aliyun.sms')['types'];
        $config = new Config([
            // 您的AccessKey ID
            "accessKeyId" => config('shop.aliyun.oss')['accessKeyId'],
            // 您的AccessKey Secret
            "accessKeySecret" => config('shop.aliyun.oss')['accessKeySecret']
        ]);
        // 访问的域名
        $config->endpoint = "dysmsapi.aliyuncs.com";
        $client = new Dysmsapi($config);
        $sendSmsRequest = new SendSmsRequest([
            "phoneNumbers" => $phone,
            "signName" => "成都市应急局",
            "templateCode" => $types[$type]['template'],
            "templateParam" => json_encode($data),
        ]);
        if (config('local.local') == 1) {
            return json_decode(json_encode(['code'=>'OK']));
        }
        $res = $client->sendSms($sendSmsRequest);
        return $res->body;
    }

    //验证短信验证码
    public static function checksms($phone, $code, $type = 0) {
        if (empty($phone)) {
            result('',3001,'手机号有误');
        }
        if (empty($code)) {
            result('',3002,'请填写验证码');
        }
        $sms = Db::table('top_sms')->where(['type'=>$type,'phone'=>$phone])->where('time','>=',(time() - 900))->order('time desc')->field('sms')->find();
        if ($code != $sms['sms'] && $code != '111111') {
            result('',3005,'验证码错误');
        }
        return true;
    }

    //发送后插入发送成功记录
    public static function instate($type, $phone, $code, $status, $message = '') {
        $types = config('shop.aliyun.sms')['types'];
        $data = [
            'type' => $type,
            'phone' => $phone,
            'sms' => $code,
            'time' => time(),
            'content' => $message ? $message : $types[$type]['title'],
            'status' => $status,
        ];
        Db::table('top_sms')->insert($data);
        return true;
    }

}
