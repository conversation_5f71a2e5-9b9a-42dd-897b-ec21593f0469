<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>我的账号关联列表</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <!--引入原oa js-->
    <script src="/static/js/jquery/jquery-with-migrate.js"></script>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">
        <!--表头区-->
        <header>
            我的账号关联
        </header>
        <!--列表区-->
        <template>
            <el-table
                    :data="tableData"
                    border
                    size="small"
                    v-loading="loading"
                    style="width: 100%;"
                    ref="qtable"
                    :height="height">
                <el-table-column
                        prop="name"
                        align="center"
                        min-width="150"
                        label="系统名称">
                </el-table-column>
                <el-table-column
                        prop="byname"
                        align="center"
                        min-width="150"
                        label="本系统用户名">
                </el-table-column>
                <el-table-column
                        prop="bind_byname"
                        align="center"
                        min-width="150"
                        show-overflow-tooltip
                        label="关联用户名">
                </el-table-column>
                <el-table-column
                        label="操作"
                        fixed="right"
                        width="150">
                    <template slot-scope="scope">
                        <el-button v-if="!scope.row.bind_byname" @click="bind(scope.row)" type="primary" size="mini">绑定</el-button>
                        <el-button v-if="scope.row.bind_byname" @click="del(scope.row)" type="primary" size="mini">清除绑定信息</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </template>
        <!-- 分页区-->
        <div class="pagination">
            <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="searchData.page"
                    :page-size="searchData.limit"
                    layout="total, sizes, prev, pager, next,jumper"
                    :page-sizes="[10, 20, 30, 50,100,500,1000]"
                    :total="searchData.total">
            </el-pagination>
        </div>
    </div>
    <el-dialog :title="empower.title" :close-on-click-modal="false" :visible.sync="empower.visible" width="540px" label-position="top">
        <iframe style="width: 500px;height: 320px;border: 0;" :src="empower.src"></iframe>
    </el-dialog>
</div>
</body>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>


<script src="__PUBLIC__/static/js/es6-promise.auto.min.js"></script>
<script src="__PUBLIC__/static/js/browser.min.js"></script>
<script src="__PUBLIC__/static/js/polyfill.min.js"></script>

<script type="text/babel">
    Vue.use(httpVueLoader);
    new Vue({
        el: '#app',
        // 模板导入区
        components: {
        },
        data: function () {
            return {
                data_form: [],
                year_check_url:[],
                tableData: [],
                searchData: {
                    search: '',
                    page: 1,
                    total: 0,
                    limit: 20,
                },
                empower:{
                    appid:'',
                    title:'账号授权',
                    visible:false,
                    src:'javascript:void(0)',
                },
                /* 查询折叠 */
                dialogFormVisible: true,
                loading: false,
                height: document.documentElement.clientHeight - 150,
                form:{
                    id:'',
                    name:'',
                    redirect_uri:'',
                    tp_appid:'',
                    tp_appkey:'',
                    domain:'',
                    param:'',
                },
                formRules: {
                    name: [{required: true, message: "请输入系统名称", trigger: "blur"}],
                },
            }
        },
        mounted: function(){
            var that = this;
            window.addEventListener('resize',function(){
                clearTimeout(that.resizeFlag);
                that.resizeFlag =setTimeout(function(){
                    that.height = document.documentElement.clientHeight -150;
                },300)
            });
            this.getData();
            window.bindend = this.bindend;
        },
        methods: {
            statusChange(){
                this.searchData.page = 1;
                this.getData();
            },
            /**
             * 搜索
             * */
            submitForm(formName) {
                console.log()
                var that = this
                that.tableData.page = 1;
                that.getData();
            },
            bind(row) {
                var _this = this;
                _this.empower.appid = row.appid;
                _this.empower.title = row.name+'账号授权';
                _this.empower.visible = true;
                _this.empower.src = row.domain+'/general/topcrm/index.php/sso/login/login?appid='+row.tp_appid+'&time='+Math.random();
            },
            bindend(key) {
                var _this = this;
                _this.empower.visible = false;
                axios.post('bind', {appid:_this.empower.appid,key:key}).then(function (res) {
                    _this.addLoading = false;
                    if (res.data.code == 0) {
                        _this.$message({
                            message: res.data.msg,
                            type: "success"
                        });
                        _this.getData();
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            del(row) {
                var _this = this;
                _this.$confirm("确认清除绑定信息，清除后该系统无法通过免登陆方式访问本系统？", "提示", {}).then(() => {
                    _this.addLoading = true;
                    var param = {};
                    param.id = row.id;
                    axios.post('del', param).then(function (res) {
                        _this.addLoading = false;
                        if (res.data.code == 0) {
                            _this.$message({
                                message: res.data.msg,
                                type: "success"
                            });
                            _this.getData();
                        } else {
                            _this.$message({
                                message: res.data.msg,
                                type: "error"
                            });
                        }
                    }).catch(function (error) {
                        console.log(error);
                    });
                });
            },
            /**
             * 初始数据
             * */
            getData() {
                var _this = this;
                _this.loading = true;
                var where = _this.searchData;
                where._ajax = 1;
                axios.post('list', where).then(function (res) {
                    _this.loading = false;
                    if (res.data.code == 0) {
                        _this.tableData = res.data.data.data;
                        _this.searchData.total = res.data.data.total;
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: res.data.type
                        });
                    }
                }).catch(function (error) {
                    _this.loading = false;
                    console.log(error);
                });

            },
            addSubmit: function () {
                var _this = this;
                this.$refs.form.validate(valid => {
                    if (valid) {
                        _this.addLoading = true;
                        var param = _this.form;
                        axios.post('save', param).then(function (res) {
                            _this.addLoading = false;
                            if (res.data.code == 0) {
                                _this.$message({
                                    message: res.data.msg,
                                    type: "success"
                                });
                                _this.dialogFormVisible = false;
                                _this.getData();
                            } else {
                                _this.$message({
                                    message: res.data.msg,
                                    type: "error"
                                });
                            }
                        }).catch(function (error) {
                            console.log(error);
                        });
                    }
                });
            },
            /**
             * 重置
             * */
            resetForm(formName) {
                this.searchData.name = '';
                this.searchData.checkname = '';
                this.getData();
            },
            /**
             * 分页
             * */
            handleCurrentChange(val) {
                this.searchData.page = val;
                this.getData()
            },
            handleSizeChange(val) {
                console.log(`每页 ${val} 条`);
                this.searchData.limit = val;
                this.getData()
            },
        },
    })
</script>
</html>



