<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>首页</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/company.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
<!--    <script src="__PUBLIC__/static/js/common.js"></script>-->
    <style>
        .centainer { padding: 20px;}
        .el-card__header { padding: 10px 20px;}
        .el-badge__content.is-fixed { right: 70px;top:60px;height:30px;line-height:30px;border-radius:30px;width:18px;z-index: 9;}
        .success .el-descriptions__header {
            border-left:5px solid #67c23a;
            color:#67c23a;
            line-height: 40px;
            padding-left:20px;
            margin-bottom: 0;
            margin-top: 20px;
            background: -ms-linear-gradient(left, #f0f9eb, #fff); /* IE 10+ */
            background: -moz-linear-gradient(left, #f0f9eb, #fff); /* Firefox */
            background: -webkit-linear-gradient(left, #f0f9eb, #fff); /* Chrome, Safari, Opera */
            background: linear-gradient(to right, #f0f9eb, #fff); /* 标准语法 */
        }
        .primary .el-descriptions__header {
            margin-top: 20px;
            border-left:5px solid #436CE6;
            color:#436CE6;
            line-height: 40px;
            padding-left:20px;
            background: -ms-linear-gradient(left, #F4F7FE, #fff); /* IE 10+ */
            background: -moz-linear-gradient(left, #F4F7FE, #fff); /* Firefox */
            background: -webkit-linear-gradient(left, #F4F7FE, #fff); /* Chrome, Safari, Opera */
            background: linear-gradient(to right, #F4F7FE, #fff); /* 标准语法 */
        }
        .mytitle{ font-size: 20px;line-height: 40px;font-weight: 700;}
        .mylink {max-width: 100%;}
        .mylink .el-link--inner {width: 100%;overflow: hidden;text-overflow: ellipsis;}
        .el-table th {background-color: #F6FAFE;}
        .el-tree-node.is-current{color: #1d39c4;}
        .el-tree-node{color: #606266;}
        .el-form-item--mini.el-form-item, .el-form-item--small.el-form-item{margin: 15px 0;}
        .announcement-wrapper {overflow: hidden;}
        .announcement-wrapper .announcement-scrollbar .announcement-content {border-bottom:1px dashed #f5f5f5;padding: 10px 0px; cursor: pointer;}
    </style>
</head>
<body style="padding: 0;">
<div id="app" v-cloak>
    <div class="centainer">
        <el-row :gutter="20">
            <el-col :span="24">
                <el-descriptions title="{$company.name}">
                    <el-descriptions-item label="统一社会信用代码">{$company.license_number}</el-descriptions-item>
                    <el-descriptions-item label="法人">{$company.legal}</el-descriptions-item>
                    <el-descriptions-item label="所属行政区">{$company.mb_region}</el-descriptions-item>
                    <!--<el-descriptions-item label="运行状态">
                        <el-tag size="small" type="info" v-show="'{$company.stand_status}'==0">未创标</el-tag>
                        <el-tag size="small" v-show="'{$company.stand_status}'==1&&'{$company.ca_status}'==0">试运行中</el-tag>
                        <el-tag size="small" v-show="'{$company.ca_status}'==1">持续运行中</el-tag>
                    </el-descriptions-item>-->
                    <el-descriptions-item label="运行标准" :span="2">{$company.standard_name}</el-descriptions-item>
                    <el-descriptions-item label="证书状态">
                        <el-tag size="small" type="info" v-show="'{$company.ca_status}'==0">未获得</el-tag>
                        <el-tag size="small" v-show="'{$company.ca_status}'==1">三级</el-tag>
                    </el-descriptions-item>
                </el-descriptions>
            </el-col>
            <el-col :span="12" style="">
                <el-descriptions class="primary" title="通知公告">
                </el-descriptions>
                <div class="announcement-wrapper">
                    <el-scrollbar class="announcement-scrollbar">
                        {volist name="result.notify" id="item"}
                        <div class="announcement-content" @click="notifyInfo('{$item.id}')">
                            <span style="display: inline-block;float: left;margin-right: 10px;color: #AF3230;">●</span>
                            <span style="display: inline-block;width: 250px;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;">
                                <span>【{$item.mb_type}】</span>
                                <span>{$item.title}</span>
                            </span>
                            <span style="display: inline-block;width: 120px; float: right;">{$item.date}</span>
                        </div>
                        {/volist}
                    </el-scrollbar>
                </div>
            </el-col>
            <el-col :span="12" style="">
                <el-descriptions class="primary" title="消息提醒">
                </el-descriptions>
                <div class="announcement-wrapper">
                    <el-scrollbar class="announcement-scrollbar">
                        {volist name="result.message" id="item"}
                        <div class="announcement-content" @click="messageInfo('{$item.id}')">
                            <span style="display: inline-block;float: left;margin-right: 10px;color: #AF3230;">●</span>
                            <span style="display: inline-block;width: 250px;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;">{$item.sms_content}</span>
                            <span style="display: inline-block;width: 120px; float: right;">{$item.sms_time}</span>
                        </div>
                        {/volist}
                    </el-scrollbar>
                </div>
            </el-col>
            <el-col>
                <el-descriptions class="success" title="待上报材料" :column="2" border>
                </el-descriptions>
                <el-row :gutter="40">
                    <el-col style="width:380px;" v-for="(item,key) in data.elementTitle">
                        <el-badge :value="item.num" :max="99" class="item">
                            <div style="overflow:hidden;width: 260px;margin:20px; padding:0 20px;height: 80px;line-height: 80px;border: 1px solid #D1D1D1;border-radius:10px;cursor: pointer;" @click="reviewAdd(item)">
                                <el-row>
                                    <el-col style="width:50px;">
                                        <el-image style="width: 36px; height: 36px;margin-top:20px;" :src="'__PUBLIC__/static/images/company/e'+(key+1)+'.png'" fit="fill"></el-image>
                                    </el-col>
                                    <el-col style="width:210px;">
                                        {{item.name}}
                                    </el-col>
                                </el-row>

                            </div>
                        </el-badge>
                    </el-col>
                </el-row>
            </el-col>
        </el-row>
    </div>
    <notify ref="notify"></notify>
    <message ref="message"></message>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data:function() {
            return {
                title: '',
                loading: false,
                data: [],
                config:[],
                height: document.documentElement.clientHeight,
            };
        },
        components: {
            'notify': 'url:/general/toppingsoft/public/vue/notifyInfo.vue?v=1',
            'message': 'url:/general/toppingsoft/public/vue/messageInfo.vue?v=1',
        },
        methods: {
            notifyInfo:function(id){
                this.$refs.notify.open(id);
            },
            messageInfo:function(id){
                this.$refs.message.open(id);
            },
            reviewAdd:function(data){
                location.href = '/general/toppingsoft/index.php/company/info/reviewEdit?element_id='+data.id;
            },
            getData:function(){
                var _this = this;
                axios.post('review', {}).then(function (res) {
                    if (res.data.code == 0) {
                        _this.data = res.data.data;
                        _this.$nextTick(function(){
                            // 这里的代码会在DOM更新完成后执行
                            _this.loading = false;
                        });
                        _this.handleRefreshTable();
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                }).catch(function (error) {
                    console.log("出现错误:",error);
                });
            },
        },
        mounted:function() {
            this.getData();
        }
    })
</script>


</body>
</html>