<?php
declare (strict_types = 1);

namespace app\api\controller;

use think\App;
use think\facade\Cache;
use think\facade\Db;
use \liliuwei\think\Jump;

session_start();

/**
 * 控制器基础类
 */
abstract class Base
{
    use Jump;


    protected $baseUrl = '/general/toppingsoft/index.php/';

    /**
     * Request实例
     * @var \think\Request
     */
    protected $request;

    /**
     * 应用实例
     * @var \think\App
     */
    protected $app;


    /**
     * 是否批量验证
     * @var bool
     */
    protected $batchValidate = false;

    /**
     * 控制器中间件
     * @var array
     */
    protected $middleware = [];


    protected $userInfo;

    //空方法
    public function __call($method, $args)
    {
        $data = request()->param();
        return view($method, ['data' => $data]);
    }

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        // 关闭未定义对象报错
        error_reporting(E_ERROR | E_PARSE);
        $this->app     = $app;
        $this->request = $this->app->request;
        $file = '/opt/tdoa/webroot/general/toppingsoft/public/api.txt';
        file_put_contents($file,date('【Y-m-d H:i:s】').'start'."\n",FILE_APPEND);
        file_put_contents($file,$this->request->controller().'/'.$this->request->action()."\n",FILE_APPEND);
        file_put_contents($file,json_encode($this->request->param())."\n",FILE_APPEND);
    }

    // 初始化
    protected function isLogin()
    {
        $token = $this->request->param('token');
        $user = Cache::get('token_'.$token);
        if(empty($user)){
            $controller = $this->request->controller();
            if($controller != 'Login'){
                result('',9001,'请先登陆');
            }
        }
    }

}
