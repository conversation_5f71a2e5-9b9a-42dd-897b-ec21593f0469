<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字段注释显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .btn {
            padding: 10px 20px;
            margin: 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }

        /* 模态框样式 - 包含字段注释功能 */
        .modal { 
            display: none; 
            position: fixed; 
            z-index: 1000; 
            left: 0; 
            top: 0; 
            width: 100%; 
            height: 100%; 
            background-color: rgba(0,0,0,0.5); 
        }
        
        .modal-content { 
            background-color: white; 
            margin: 2% auto; 
            padding: 20px; 
            border-radius: 8px; 
            width: 800px; 
            max-width: 90%; 
            max-height: 90vh; 
            position: relative; 
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 15px;
            border-bottom: 1px solid #e9ecef;
            margin-bottom: 20px;
        }

        .modal-header h3 {
            margin: 0;
            color: #2c3e50;
            font-size: 18px;
        }

        .modal-header .close {
            font-size: 24px;
            font-weight: bold;
            color: #aaa;
            cursor: pointer;
            border: none;
            background: none;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s;
        }

        .modal-header .close:hover {
            color: #000;
            background-color: #f8f9fa;
        }

        .form-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); 
            gap: 15px; 
            max-height: 60vh; 
            overflow-y: auto; 
            padding-right: 10px;
        }

        .form-row { margin-bottom: 15px; }
        .form-row label { 
            display: block; 
            margin-bottom: 5px; 
            font-weight: bold; 
            color: #2c3e50; 
            position: relative;
        }

        /* 字段注释样式 */
        .form-row label small {
            color: #999;
            font-size: 10px;
            font-weight: normal;
            font-style: normal;
            margin-top: 2px;
            margin-bottom: 0;
        }

        /* 字段标签悬停效果 */
        .form-row label[title]:hover {
            color: #007bff;
            cursor: help;
        }

        .form-row input { 
            width: 100%; 
            padding: 8px; 
            border: 1px solid #ddd; 
            border-radius: 4px; 
            font-size: 14px;
        }

        /* 输入框悬停显示完整信息 */
        .form-row input[title]:hover {
            border-color: #007bff;
        }

        .form-row input:focus { 
            outline: none; 
            border-color: #3498db; 
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .form-row input[readonly] {
            background-color: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
        }

        .modal-footer {
            position: sticky;
            bottom: 0;
            background: white;
            padding: 15px 0 0 0;
            margin-top: 20px;
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .demo-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            background: #f8f9fa;
        }

        .demo-section h3 {
            margin-top: 0;
            color: #495057;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 5px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-list li::before {
            content: "✅ ";
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>字段注释显示功能测试</h1>
        <p>这个页面用于测试编辑数据时字段注释的显示效果。</p>
        
        <div class="demo-section">
            <h3>功能特性</h3>
            <ul class="feature-list">
                <li>显示字段的中文注释作为主标签</li>
                <li>在注释下方显示原始字段名</li>
                <li>鼠标悬停显示完整的字段信息</li>
                <li>必填字段用红色星号标识</li>
                <li>只读字段有特殊样式标识</li>
                <li>支持编辑、批量编辑、新增数据等场景</li>
            </ul>
        </div>
        
        <button class="btn btn-primary" onclick="showEditDemo()">编辑数据演示</button>
        <button class="btn btn-success" onclick="showBatchEditDemo()">批量编辑演示</button>
        <button class="btn btn-warning" onclick="showAddDataDemo()">新增数据演示</button>
        
        <div class="demo-section">
            <h3>显示效果说明</h3>
            <p><strong>标签显示逻辑：</strong></p>
            <ul>
                <li>如果字段有注释：显示注释作为主标签，下方显示字段名</li>
                <li>如果字段无注释：直接显示字段名</li>
                <li>鼠标悬停：显示"字段名: 注释 - 数据类型(长度)"</li>
                <li>输入框也有相同的悬停提示</li>
            </ul>
        </div>
    </div>

    <script>
        // 模拟字段数据
        const mockFieldsWithComments = {
            'USER_ID': '',
            'USER_NAME': '用户姓名',
            'USER_EMAIL': '电子邮箱',
            'USER_PHONE': '联系电话',
            'USER_ADDRESS': '用户地址',
            'CREATE_TIME': '创建时间',
            'UPDATE_TIME': '更新时间',
            'USER_STATUS': '用户状态',
            'USER_TYPE': '用户类型',
            'LAST_LOGIN': '最后登录时间'
        };

        const mockRowData = {
            'USER_ID': '10001',
            'USER_NAME': '张三',
            'USER_EMAIL': '<EMAIL>',
            'USER_PHONE': '13800138001',
            'USER_ADDRESS': '北京市朝阳区某某街道123号',
            'CREATE_TIME': '2023-01-15 10:30:00',
            'UPDATE_TIME': '2023-07-20 14:25:00',
            'USER_STATUS': '1',
            'USER_TYPE': 'VIP',
            'LAST_LOGIN': '2023-07-21 09:15:00'
        };

        function showEditDemo() {
            const columns = Object.keys(mockFieldsWithComments);
            createEditModal(mockRowData, columns, mockFieldsWithComments);
        }

        function showBatchEditDemo() {
            const columns = Object.keys(mockFieldsWithComments);
            createBatchEditModal(columns, mockFieldsWithComments, [1, 2, 3]);
        }

        function showAddDataDemo() {
            const tableStructure = Object.keys(mockFieldsWithComments).map(column => ({
                COLUMN_NAME: column,
                DATA_TYPE: column.includes('TIME') ? 'DATETIME' : column.includes('ID') ? 'INT' : 'VARCHAR',
                DATA_LENGTH: column.includes('ADDRESS') ? 200 : column.includes('ID') ? null : 50,
                NULLABLE: column.includes('ID') ? 'N' : 'Y',
                COMMENTS: mockFieldsWithComments[column],
                DATA_DEFAULT: column === 'USER_STATUS' ? '1' : null
            }));
            createAddDataModal(tableStructure);
        }

        // 创建编辑模态框
        function createEditModal(rowData, columns, columnComments) {
            let formHtml = '<div class="modal" id="editModal" style="display: block;">';
            formHtml += '<div class="modal-content">';
            formHtml += '<div class="modal-header">';
            formHtml += '<h3>编辑数据 - 字段注释演示</h3>';
            formHtml += '<span class="close" onclick="closeModal(\'editModal\')">&times;</span>';
            formHtml += '</div>';
            formHtml += '<form>';

            formHtml += '<div class="form-grid">';
            columns.forEach(column => {
                const value = rowData[column] || '';
                const isReadonly = column.includes('ID') || column.includes('_ID');
                const comment = columnComments[column];
                
                let labelText = column;
                let titleText = column;
                if (comment && comment.trim()) {
                    labelText = comment;
                    titleText = `${column}: ${comment}`;
                }
                
                formHtml += `<div class="form-row">
                    <label title="${titleText}">
                        ${labelText}
                        ${comment && comment.trim() ? `<br><small>${column}</small>` : ''}
                        ${isReadonly ? ' <span style="color: #999; font-size: 11px;">(只读)</span>' : ''}
                    </label>
                    <input type="text" name="${column}" value="${value}" ${isReadonly ? 'readonly' : ''} title="${titleText}">
                </div>`;
            });
            formHtml += '</div>';

            formHtml += '<div class="modal-footer">';
            formHtml += '<button type="button" class="btn btn-danger" onclick="closeModal(\'editModal\')">取消</button>';
            formHtml += '<button type="submit" class="btn btn-primary">保存</button>';
            formHtml += '</div>';
            formHtml += '</form></div></div>';

            document.body.insertAdjacentHTML('beforeend', formHtml);
        }

        // 创建批量编辑模态框
        function createBatchEditModal(columns, columnComments, selectedRows) {
            let formHtml = '<div class="modal" id="batchEditModal" style="display: block;">';
            formHtml += '<div class="modal-content">';
            formHtml += '<div class="modal-header">';
            formHtml += '<h3>批量编辑数据 - 字段注释演示</h3>';
            formHtml += '<span class="close" onclick="closeModal(\'batchEditModal\')">&times;</span>';
            formHtml += '</div>';
            formHtml += `<p style="margin-bottom: 15px; padding: 10px; background: #e3f2fd; border-radius: 4px; color: #1976d2;">将对选中的 ${selectedRows.length} 行数据进行批量修改</p>`;
            formHtml += '<form>';

            formHtml += '<div class="form-grid">';
            columns.forEach(column => {
                if (!column.includes('ID') && !column.includes('_ID')) {
                    const comment = columnComments[column];
                    
                    let labelText = column;
                    let titleText = column;
                    if (comment && comment.trim()) {
                        labelText = comment;
                        titleText = `${column}: ${comment}`;
                    }
                    
                    formHtml += `<div class="form-row">
                        <label title="${titleText}">
                            <input type="checkbox" name="update_${column}" style="margin-right: 8px;"> 
                            ${labelText}
                            ${comment && comment.trim() ? `<br><small style="margin-left: 20px;">${column}</small>` : ''}
                        </label>
                        <input type="text" name="${column}" placeholder="新值" disabled title="${titleText}">
                    </div>`;
                }
            });
            formHtml += '</div>';

            formHtml += '<div class="modal-footer">';
            formHtml += '<button type="button" class="btn btn-danger" onclick="closeModal(\'batchEditModal\')">取消</button>';
            formHtml += '<button type="submit" class="btn btn-primary">批量更新</button>';
            formHtml += '</div>';
            formHtml += '</form></div></div>';

            document.body.insertAdjacentHTML('beforeend', formHtml);

            // 绑定复选框事件
            document.querySelectorAll('#batchEditModal input[type="checkbox"]').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const fieldName = this.name.replace('update_', '');
                    const input = document.querySelector(`#batchEditModal input[name="${fieldName}"]`);
                    if (input) {
                        input.disabled = !this.checked;
                        if (!this.checked) {
                            input.value = '';
                        }
                    }
                });
            });
        }

        // 创建新增数据模态框
        function createAddDataModal(tableStructure) {
            let formHtml = '<div class="modal" id="addDataModal" style="display: block;">';
            formHtml += '<div class="modal-content">';
            formHtml += '<div class="modal-header">';
            formHtml += '<h3>新增数据 - 字段注释演示</h3>';
            formHtml += '<span class="close" onclick="closeModal(\'addDataModal\')">&times;</span>';
            formHtml += '</div>';
            formHtml += '<form>';

            formHtml += '<div class="form-grid">';
            tableStructure.forEach(column => {
                const required = column.NULLABLE === 'N' ? 'required' : '';
                const placeholder = column.DATA_DEFAULT ? `默认值: ${column.DATA_DEFAULT}` : '';
                const dataType = column.DATA_TYPE;
                const maxLength = column.DATA_LENGTH ? ` (最大长度: ${column.DATA_LENGTH})` : '';
                const isRequired = column.NULLABLE === 'N';
                const comment = column.COMMENTS || '';

                let labelText = column.COLUMN_NAME;
                let titleText = `${column.COLUMN_NAME} - ${dataType}${maxLength}`;
                if (comment && comment.trim()) {
                    labelText = comment;
                    titleText = `${column.COLUMN_NAME}: ${comment} - ${dataType}${maxLength}`;
                }

                formHtml += `<div class="form-row">
                    <label title="${titleText}">
                        ${labelText}${isRequired ? ' <span style="color: red;">*</span>' : ''}
                        ${comment && comment.trim() ? `<br><small>${column.COLUMN_NAME}</small>` : ''}
                    </label>
                    <small style="color: #666; font-size: 11px; margin-bottom: 5px; display: block;">${dataType}${maxLength}</small>
                    <input type="text" name="${column.COLUMN_NAME}" placeholder="${placeholder}" ${required} title="${titleText}">
                </div>`;
            });
            formHtml += '</div>';

            formHtml += '<div class="modal-footer">';
            formHtml += '<button type="button" class="btn btn-danger" onclick="closeModal(\'addDataModal\')">取消</button>';
            formHtml += '<button type="submit" class="btn btn-primary">保存</button>';
            formHtml += '</div>';
            formHtml += '</form></div></div>';

            document.body.insertAdjacentHTML('beforeend', formHtml);
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.remove();
            }
        }

        // 点击模态框外部关闭
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal')) {
                e.target.remove();
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const modals = document.querySelectorAll('.modal');
                modals.forEach(modal => modal.remove());
            }
        });
    </script>
</body>
</html>
