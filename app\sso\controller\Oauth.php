<?php

namespace app\sso\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use hg\apidoc\annotation as Apidoc;
use app\Request;
use think\App;
use think\facade\Db;
use think\facade\Cache;

/**
 * @Apidoc\Title("单点登录")
 * @Apidoc\Group("Oauth")
 * @Apidoc\Sort(3)
 */
class Oauth
{

    /**
     * NotHeaders
     * @Apidoc\Title("重定向获取code")
     * @Apidoc\Method("")
     * @Apidoc\Param("appid", type="string",require=true, desc="开放平台appid")
     * @Apidoc\Param("redirect_uri", type="string",require=true, desc="授权后重定向的回调链接地址， 请使用 urlEncode 对链接进行处理")
     * @Apidoc\Param("state", type="string",require=true, desc="重定向后会带上state参数")
     * @Apidoc\Desc("重定向：授权成功后跳转至redirect_uri/?code=CODE&state=STATE")
     */
    function authorize(Request $request) {
        $appid = $request->param('appid');
        $redirect_uri = $request->param('redirect_uri');
        $state = $request->param('state');
        if(empty($_SESSION['LOGIN_USER_ID'])){
            $host = $_SERVER['HTTP_HOST'];
            $port = $_SERVER['SERVER_PORT'];
            $url = $port==443?'https://':'http://';
            $url .= $host;
        }else{
            $re = Db::name('login_app')->where(['appid'=>$appid,'del'=>0])->find();
            if(empty($re)){
                exit('无效的appid');
                result('',1001,'无效的appid');
            }
            $code = md5($re['appid'].time().rand(1000,9999));
            $data = Db::table('td_user')->alias('a')
                ->leftJoin(['department'=>'b'],'a.DEPT_ID = b.DEPT_ID')
                ->where(['USER_ID'=>$_SESSION['LOGIN_USER_ID']])
                ->field('a.BYNAME username,a.USER_NAME name,a.SEX sex,a.MOBIL_NO mobile,b.DEPT_NAME department')->find();
            $data['appid'] = $appid;
            $data['sex'] = $data['sex']==1?'女':'男';
            Cache::tag('sso_'.$appid)->set('sso_'.$code,$data,300);
            $url = urldecode($redirect_uri).'?code='.$code.'&state='.$state;
        }
        return redirect($url);
    }


    /**
     * NotHeaders
     * @Apidoc\Title("获取accessToken")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("appid", type="string",require=true, desc="开放平台appid（应用id）")
     * @Apidoc\Param("appkey", type="string",require=true, desc="开放平台appkey（应用秘钥）")
     * @Apidoc\Param("code", type="string",require=true, desc="重定向跳转回来获取的code参数")
     * @Apidoc\Returned("accessToken", type="string",desc="accessToken")
     * @Apidoc\Returned("expires_in", type="string",desc="过期时间（秒）")
     * @Apidoc\Returned("username", type="string",desc="用户登录名（唯一）")
     */
    function accessToken(Request $request){
        $appid = $request->param('appid');
        $appkey = $request->param('appkey');
        $code = $request->param('code');
        $data = Cache::get('sso_'.$code);
        $re = Db::name('login_app')->where(['appid'=>$appid,'del'=>0])->find();
        if(empty($re)){
            result('',8001,'无效的appid');
        }
        if($re['appkey']!==$appkey){
            result('',8002,'身份验证失败');
        }
        if(empty($data)||$data['appid']!==$appid){
            result('',8003,'无效的code');
        }
        $accessToken = self::create_nonce_str(64);
        unset($data['appid']);
        Cache::tag('sso_'.$appid)->set('sso_'.$accessToken,$data,7200);
        Cache::delete('sso_'.$code);
        $data = [['accessToken'=>$accessToken,'expires_in'=>7200,'username'=>$data['username']]];
        result($data,0,'获取成功');
    }

    /**
     * NotHeaders
     * @Apidoc\Title("获取用户信息")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("appid", type="string",require=true, desc="开放平台appid（应用id）")
     * @Apidoc\Param("accessToken", type="string",require=true, desc="获取accessToken接口获取到的accessToken")
     * @Apidoc\Returned("appid", type="string",desc="appid")
     * @Apidoc\Returned("username", type="string",desc="用户登录名（唯一）")
     * @Apidoc\Returned("name", type="string",desc="用户姓名")
     * @Apidoc\Returned("sex", type="string",desc="性别")
     * @Apidoc\Returned("mobile", type="string",desc="手机号")
     * @Apidoc\Returned("department", type="string",desc="部门")
     */
    function userinfo(Request $request){
        $appid = $request->param('appid');
        $accessToken = $request->param('accessToken');
        $data = Cache::get('sso_'.$accessToken);
        $re = Db::name('login_app')->where(['appid'=>$appid,'del'=>0])->find();
        if(empty($re)){
            result('',1011,'无效的appid');
        }
        if(empty($re)||$re['appid']!==$appid){
            result('',1013,'无效的accessToken');
        }
        result($data,0,'获取成功');
    }

    function create_nonce_str(int $length = 16) {
        $chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        $str = "";
        for ($i = 0; $i < $length; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }
        return $str;
    }

}
