<?php

namespace app\model;

use think\db\exception\DbException;
use think\facade\Db;
use think\Model;
use Medoo\Mdb;

class CompanyModel extends Model
{

    //企业状态
    public static $standStatusText = [
        'info' => '未创标',
        'primary' => '试运行中',
        'success' => '持续运行中',
    ];

    //证书状态
    public static $caStatusText = [
        'warning' => '未取得',
        'success' => '已取得',
        'danger' => '已过期',
    ];

    public static function companyInfo($id = 0)
    {
        $re = Db::table('top_company_info')->where(['id' => $id])->find();
        if (empty($re)) {
            return [];
        }
        return CompanyModel::codeToText($re);
    }

    //参数格式化
    public static function codeToText($info)
    {
        if (empty($info)) {
            return $info;
        }
        $pcas = SettingModel::getPcasAll();
        $info['reg_address'] = empty($info['reg_address']) ? [] : explode(',', $info['reg_address']);
        $info['mb_reg_address'] = '';
        foreach ($info['reg_address'] as $k => $v) {
            $info['mb_reg_address'] .= $pcas[$v];
        }
        $info['mb_reg_address'] .= $info['reg_address_info'];
        $info['operate_address'] = empty($info['operate_address']) ? [] : explode(',', $info['operate_address']);
        $info['mb_operate_address'] = '';
        foreach ($info['operate_address'] as $k => $v) {
            $info['mb_operate_address'] .= $pcas[$v];
        }
        $info['mb_operate_address'] .= $info['operate_address_info'];
        $info['region'] = empty($info['region']) ? [] : explode(',', $info['region']);
        $info['mb_region'] = '';
        foreach ($info['region'] as $k => $v) {
            $info['mb_region'] .= $pcas[$v];
        }
        $info['economy_sector'] = explode(',', $info['economy_sector']);
        $info['mb_economy_sector'] = implode('/', $info['economy_sector']);
        $info['economy_type'] = explode(',', $info['economy_type']);
        $info['mb_economy_type'] = implode('/', $info['economy_type']);
        $info['license_date'] = $info['license_start'] . '至' . $info['license_end'];
        $info['licenseUrl'] = empty($info['license']) ? '' : FileModel::getFile(0, $info['license']);
        $info['aocUrl'] = empty($info['aoc']) ? '' : FileModel::getFile(0, $info['aoc']);
        foreach ($info as $k => $v) {
            $info[$k] = $v === '0000-00-00' ? '' : $v;
            if (is_int($v)) {
                $info[$k] = (string)$v;
            }
            if ($v === null) {
                $info[$k] = '';
            }
        }
        //非企业端 查看信息无需展示认证中的信息,则直接使用的&$info
        self::getCompanyAuthInfo($info);

        return $info;
    }


    /**
     * 获取企业 获取粉尘 获取是否涉高温熔融金属 获取是否涉有限空间 认证信息top_company_param,认证中信息top_company_param_apply
     */
    public static function getCompanyAuthInfo(&$info, $table = 'top_company_param')
    {
        self::getAuthInfoByCategory($info, 'dust', 'dust_list', $table);
        self::getAuthInfoByCategory($info, 'hot', 'hot_list', $table);
        self::getAuthInfoByCategory($info, 'limited', 'limited_list', $table);
    }

    /**
     * 获取企业认证信息（粉尘、高温熔融金属、有限空间）
     * @param array $info 企业信息数组（引用传值）
     * @param string $category 分类（dust、hot、limited）
     * @param string $key 输出字段名（如 dust_list）
     * @param string $table 输出表名（如 top_company_param）
     * @return void
     */
    private static function getAuthInfoByCategory(&$info, $category, $key, $table = 'top_company_param')
    {
        try {
            $list = Db::table($table)
                ->field("name,param_value")
                ->where(['company_id' => $info['id'], 'category' => $category])
                ->select()
                ->toArray();
            $info[$key] = !empty($list) ? $list : [];
        } catch (DbException $e) {
            $info[$key] = [];
        }
    }

    /**
     * 获取自评报告数据
     * @param $company_id
     * @return array|\think\Collection|Db[]
     */
    public static function getSelfEvaluationList($company_id)
    {
        try {
            return Db::table('top_company_evaluate')->where([
                ['company_id', '=', $company_id],
                ['status', '=', 7]
            ])->order('id desc')->select()->each(function ($item) {
                $item['score'] = json_decode($item['score'], true);
                $item['files'] = !empty($item['files']) ? $item['files'] : [];
                $f = FileModel::getFile(0, $item['files'], '');
                $item['files'] = [
                    'id' => $f['id'],
                    'name' => $f['name'],
                    'url' => $f['url'],
                ];
                return $item;
            });
        } catch (DbException $e) {
            return [];
        }
    }

    public static function getCompanyInfo($id, $where = [])
    {
        if ($where) {
            $re = Db::table('top_company_info')->where($where)->find();
        } else {
            $re = Db::table('top_company_info')->where(['id' => $id])->find();
        }
        if (empty($re)) {
            result('', 1002, '信息查询失败');
        }
        $re = self::codeToText($re);
        $re['calist'] = Db::table('top_certificate')->where(['company_id' => $id])->select()->each(function ($item) {
            $item['date'] = $item['start'] . '至' . $item['ends'];
            return $item;
        });
        $re['specialWorkData'] = [];//特殊作业许可表
        $re['trainingData'] = [];//培训合格证书管理表
        $re['selfEvaluationData'] = [];//自评报告表
        $specialList = Db::table('top_special_work')->where(['company_id' => $id])->select()->toArray();
        //处理图片
        if (!empty(isset($specialList)) && count(isset($specialList))) {
            foreach ($specialList as $k => $item) {
                if (empty($item['work_permit'])) {
                    $specialList[$k]['files'] = [];
                    continue;
                }

                $item['work_permit'] = explode(',', $item['work_permit']);
                foreach ($item['work_permit'] as $v) {
                    $f = FileModel::getFile('', $v, '');
                    $specialList[$k]['files'][] = $f;
                }
            }
        }
        $re['specialWorkData'] = $specialList;


        $trainingList = Db::table('top_training_certificate')->where(['company_id' => $id])->select()->toArray();

        //处理图片
        if (!empty(isset($trainingList)) && count(isset($trainingList))) {
            foreach ($trainingList as $k => $item) {
                if (empty($item['file'])) {
                    $trainingList[$k]['files'] = [];
                    continue;
                }

                $item['file'] = explode(',', $item['file']);
                foreach ($item['file'] as $v) {
                    $f = FileModel::getFile('', $v, '');
                    $trainingList[$k]['files'][] = $f;
                }
            }
        }
        $re['trainingData'] = $trainingList;

        // 获取自评报告数据
        $re['selfEvaluationData'] = \app\model\CompanyModel::getSelfEvaluationList($id);

        /******************获取运行资料*******Start*********/
        $element_id = Db::table('top_company_review_element')->where(['main_id' => $re['review_id']])->field('id')->order('sort')->find()['id'];
        $re['element_id'] = $element_id;
        /******************获取运行资料*******End*********/

        result($re);
    }

    public static function getAjaxReview($element_id, $request)
    {
        $company_id = 0;
        if (isset($request['company_id']) && !empty($request['company_id'])) {
            $company_id = $request['company_id'];
        }

        $company = Db::table('top_company_info')->field("review_id")->where(['id' => $company_id])->find();
        $where = [
            ['a.main_id', '=', $company['review_id']],
            ['a.is_del', '=', 0],
        ];
        if (!empty($element_id)) {
            $element_id = is_array($element_id) ? $element_id[count($element_id) - 1] : $element_id;
            $where[] = ['a.element_ids', 'like', "%,$element_id,%"];
        }
        $list = Db::table('top_company_review_content_list')->where([['element_ids', 'like', "%,$element_id,%"]])->order('id')->select()->toArray();
        $tmp = [];
        foreach ($list as $k => $v) {
            $files = explode(',', $v['sub_files']);
            $v['sub_files'] = $files;
            $v['mb_sub_files'] = [];
            $v['edit'] = false;
            foreach ($files as $v1) {
                if (!empty($v1)) {
                    $v['mb_sub_files'][] = FileModel::getFile(0, $v1, '');
                }
            }
            $tmp[$v['content_id']][] = $v;
        }
        $result['content'] = Db::table('top_company_review_content')->alias('a')
            ->leftJoin('top_company_review_element b', 'a.element_id = b.id')
            ->where($where)
            ->order('b.sort,a.sort')
            ->field('a.id,a.ask,a.standards,a.score,a.cycle,a.method,a.sort,b.sort bsort')
            ->select()->each(function ($item) use ($tmp) {
                $item['list'] = $tmp[$item['id']];
                return $item;
            })->toArray();
        $element = Db::table('top_company_review_element')
            ->where(['main_id' => $company['review_id']])
            ->field('id,name,pid')->order('sort')->select()->toArray();

        $result['element'] = get_tree_children($element);
        result($result);
    }

    /**
     * 获取企业状态文本
     * @param $stand_status
     * @param $ca_status
     * @return string
     * Author: 思密达か宁采臣
     */
    public static function getStatusText($stand_status, $ca_status)
    {
        if ($stand_status == 0) {
            return self::$standStatusText['info'];
        } elseif ($stand_status == 1 && $ca_status == 0) {
            return self::$standStatusText['primary'];
        } elseif ($stand_status == 1 && $ca_status == 1) {
            return self::$standStatusText['success'];
        } else {
            return '';
        }
    }

    /**
     * 获取证书状态文本
     * @param $ca_status
     * @return string
     * Author: 思密达か宁采臣
     */
    public static function getCaStatusText($ca_status)
    {
        if ($ca_status == 0) {
            return self::$caStatusText['warning'];
        } elseif ($ca_status == 1) {
            return self::$caStatusText['success'];
        } else {
            return self::$caStatusText['danger'];
        }
    }
}