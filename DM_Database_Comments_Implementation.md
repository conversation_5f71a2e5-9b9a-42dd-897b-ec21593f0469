# DM 数据库注释功能实现总结

## 概述
参考 `DmQuery.php` 中获取字段备注和表备注的方法，完成了 `DatabaseCompatibilityService.php` 中 DM 数据库获取表注释和字段注释的功能。

## 实现的功能

### 1. 表注释获取
- **方法**: `getTableComment($tableName)`
- **SQL查询**: `SELECT COMMENTS FROM USER_TAB_COMMENTS WHERE TABLE_NAME = ?`
- **参考**: `DmQuery.php` 中的 `getTables()` 函数

### 2. 字段注释获取
- **方法**: `getColumnComments($tableName)`
- **主要SQL**: `SELECT COLUMN_NAME, COMMENTS FROM USER_COL_COMMENTS WHERE TABLE_NAME = ? AND COMMENTS IS NOT NULL`
- **备用SQL**: `SELECT COLUMN_NAME, COMMENTS FROM ALL_COL_COMMENTS WHERE TABLE_NAME = ? AND OWNER = USER AND COMMENTS IS NOT NULL`
- **参考**: `DmQuery.php` 中的 `getColumnComments()` 函数

### 3. 增强的表列表查询
- **更新**: `getTablesSQL()` 方法
- **新SQL**: `SELECT TABLE_NAME, COMMENTS FROM USER_TAB_COMMENTS ORDER BY TABLE_NAME`
- **返回**: 现在包含表注释信息

### 4. 增强的字段列表查询
- **更新**: `getTableFieldsSQL()` 方法
- **新SQL**: 
```sql
SELECT 
    c.COLUMN_NAME,
    c.DATA_TYPE,
    c.DATA_LENGTH,
    c.DATA_PRECISION,
    c.DATA_SCALE,
    c.NULLABLE,
    c.DATA_DEFAULT,
    cc.COMMENTS as COLUMN_COMMENT
FROM USER_TAB_COLUMNS c
LEFT JOIN USER_COL_COMMENTS cc ON c.TABLE_NAME = cc.TABLE_NAME AND c.COLUMN_NAME = cc.COLUMN_NAME
WHERE c.TABLE_NAME = ? 
ORDER BY c.COLUMN_ID
```

## 关键改进

### 1. 表查询增强
- **原来**: 只返回表名
- **现在**: 返回表名和表注释
- **数据结构**: 
```php
[
    'table_name' => '表名',
    'display_name' => '显示名称',
    'table_comment' => '表注释'  // 新增
]
```

### 2. 字段查询增强
- **原来**: 字段注释使用字段名作为默认值
- **现在**: 从 `USER_COL_COMMENTS` 获取真实注释
- **数据结构**:
```php
[
    'field_name' => '字段名',
    'field_type' => '字段类型',
    'field_comment' => '字段注释',  // 现在是真实注释
    'is_nullable' => true/false,
    'default_value' => '默认值'
]
```

### 3. 容错机制
- **双重查询**: 先查 `USER_COL_COMMENTS`，如果为空则查 `ALL_COL_COMMENTS`
- **异常处理**: 查询失败时返回空数组而不是抛出异常
- **大小写处理**: 表名自动转换为大写（DM 数据库要求）

## 参考的 DmQuery.php 方法

### 1. getTables() 函数
```php
function getTables($pdo) {
    try {
        $sql = "SELECT TABLE_NAME, COMMENTS FROM USER_TAB_COMMENTS ORDER BY TABLE_NAME";
        $stmt = $pdo->query($sql);
        return ['success' => true, 'data' => $stmt->fetchAll()];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}
```

### 2. getColumnComments() 函数
```php
function getColumnComments($pdo, $tableName) {
    try {
        // 尝试多种方式获取列注释
        $sql = "SELECT COLUMN_NAME, COMMENTS
                FROM USER_COL_COMMENTS
                WHERE TABLE_NAME = ? AND COMMENTS IS NOT NULL";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([strtoupper($tableName)]);
        $comments = [];
        foreach ($stmt->fetchAll() as $row) {
            if (!empty($row['COMMENTS'])) {
                $comments[$row['COLUMN_NAME']] = $row['COMMENTS'];
            }
        }

        // 如果没有找到注释，尝试从ALL_COL_COMMENTS查询
        if (empty($comments)) {
            try {
                $sql2 = "SELECT COLUMN_NAME, COMMENTS
                        FROM ALL_COL_COMMENTS
                        WHERE TABLE_NAME = ? AND OWNER = USER AND COMMENTS IS NOT NULL";
                $stmt2 = $pdo->prepare($sql2);
                $stmt2->execute([strtoupper($tableName)]);
                foreach ($stmt2->fetchAll() as $row) {
                    if (!empty($row['COMMENTS'])) {
                        $comments[$row['COLUMN_NAME']] = $row['COMMENTS'];
                    }
                }
            } catch (Exception $e2) {
                // 忽略这个错误，继续使用空的comments数组
            }
        }

        return ['success' => true, 'data' => $comments];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}
```

### 3. getTableStructure() 函数
```php
function getTableStructure($pdo, $tableName) {
    try {
        $sql = "SELECT
                    c.COLUMN_NAME,
                    c.DATA_TYPE,
                    c.DATA_LENGTH,
                    c.NULLABLE,
                    c.DATA_DEFAULT,
                    c.COLUMN_ID,
                    cc.COMMENTS as COLUMN_COMMENT
                FROM USER_TAB_COLUMNS c
                LEFT JOIN USER_COL_COMMENTS cc ON c.TABLE_NAME = cc.TABLE_NAME AND c.COLUMN_NAME = cc.COLUMN_NAME
                WHERE c.TABLE_NAME = ?
                ORDER BY c.COLUMN_ID";
        // ...
    }
}
```

## 使用示例

```php
use app\admin\service\DatabaseCompatibilityService;

// 获取所有表（包含注释）
$tables = DatabaseCompatibilityService::getTables();
foreach ($tables as $table) {
    echo "表名: " . $table['table_name'] . "\n";
    echo "注释: " . $table['table_comment'] . "\n";
}

// 获取表字段（包含注释）
$fields = DatabaseCompatibilityService::getTableFields('USER_INFO');
foreach ($fields as $field) {
    echo "字段: " . $field['field_name'] . "\n";
    echo "注释: " . $field['field_comment'] . "\n";
}

// 获取单个表的注释
$tableComment = DatabaseCompatibilityService::getTableComment('USER_INFO');

// 获取表的所有字段注释
$columnComments = DatabaseCompatibilityService::getColumnComments('USER_INFO');
```

## 总结
成功参考 `DmQuery.php` 中的实现方法，完善了 `DatabaseCompatibilityService.php` 中的 DM 数据库注释功能，实现了：
1. 表注释的获取和显示
2. 字段注释的获取和显示
3. 容错机制和备用查询方案
4. 与现有代码的兼容性

所有功能都遵循了 DM 数据库的特性和 `DmQuery.php` 中已验证的查询方法。
