<?php

namespace app\company\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use app\company\model\CompanyModel;
use app\company\model\StandardModel;
use app\validate\StandardVerify as Verify;
use app\model\SettingModel;
use hg\apidoc\annotation as Apidoc;
use mysql_xdevapi\Exception;
use think\facade\View;
use think\Request;
use think\facade\Db;
use app\model\FileModel;

/**
 * @Apidoc\Title("运行资料")
 * @Apidoc\Group("Index")
 * @Apidoc\Sort(1)
 */
class Info extends Base {

    /**
     * @Apidoc\Title("运行资料")
     * @Apidoc\Desc("运行资料")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function review() {
        /*$re = SettingModel::setReport($_SESSION['company']['id']);
        dd($re);*/
        if (request()->isAjax()) {
            $where = [];
            $company = Db::table('top_company_info')->where(['id'=>$_SESSION['company']['id']])->find();
            $where[] = ['main_id', '=', $company['review_id']];
//            $where[] = ['company_id','=',$company['id']];
            $where[] = ['pid', '=', 0];
//            echo Db::table('cay_company_review_content_list')->where(['element_id'=>1])->fetchSql()->count('id');die;
            $result['elementTitle'] = Db::table('top_company_review_element')->where($where)->field('id,name,main_id')->order('sort')->select()->each(function ($item) {
                $num = Db::table('top_company_review_content_list')->where([['element_ids', 'like', "%,{$item['id']},%"], ['is_sub', '=', '0']])->count('id');
                $item['num'] = empty($num) ? '' : $num;
                return $item;
            });
            $result = $this->addOtherFiles($result);
            result($result);
        } else {
            View::assign('title', '首页');
            return view();
        }
    }

    //附加的资料上报隐患整改、教育培训、特殊作业设置
    private  function addOtherFiles($data){
        if(is_array($data)){
            $dataLength = count($data['elementTitle']);
            //$data['elementOtherTitle'][] = ['id'=> -1, 'name'=>($dataLength+1).".隐患整改", "main_id"=>22, "numrow"=>"1", "num"=> 21,'url'=>''];
            $data['elementOtherTitle'][] = ['id'=> -2, 'name'=>"教育培训", "main_id"=>22, "numrow"=>"1", "num"=> '','url'=>''];
            $data['elementOtherTitle'][] = ['id'=> -3, 'name'=>"特殊作业", "main_id"=>22, "numrow"=>"1", "num"=> '','url'=>''];
        }
        return $data;
    }

    public function reviewEdit($element_id=0) {
        if (request()->isAjax()) {
            $company = Db::table('top_company_info')->where(['id'=>$_SESSION['company']['id']])->find();
            $where = [
                ['a.main_id', '=', $company['review_id']],
                ['a.is_del', '=', 0],
                ['b.is_sub', '=', 0],
                ['b.is_expire', '=', 0],
            ];
            if(!empty($element_id)){
                $element_id = is_array($element_id)?$element_id[count($element_id)-1]:$element_id;
                $where[] = ['b.element_ids', 'like', "%,$element_id,%"];
            }
            $result['content'] = Db::table('top_company_review_content')->alias('a')
                ->leftJoin('top_company_review_content_list b', 'a.id = b.content_id')
                ->where($where)->order('a.sort,b.id')->field('b.*,a.content,a.file_remark,a.ask,a.standards,a.score,a.cycle,a.method,a.file_remark')
                ->select()->each(function ($item) {
                    $files = explode(',',$item['sub_files']);
                    $item['sub_files'] = [];
                    foreach ($files as $v){
                        if(!empty($v)){
                            $item['sub_files'][] = FileModel::getFile(0,$v,'');
                        }
//                        $f = FileModel::getFile(0,$v,'');
                    }
                    return $item;
                })->toArray();
            $element = Db::table('top_company_review_element')
                ->where(['main_id'=>$company['review_id']])
                ->field('id,name,pid')->select()->toArray();
            $result['element'] = get_tree_children($element);
            result($result);
        } else {
            View::assign('element_id', $element_id);
            View::assign('title', '首页');
            return view();
        }
    }



    public function reviewInfo($element_id=0) {
        if (request()->isAjax()) {
            $company = Db::table('top_company_info')->where(['id'=>$_SESSION['company']['id']])->find();
            $where = [
                ['a.main_id', '=', $company['review_id']],
                ['a.is_del', '=', 0],
            ];
            if(!empty($element_id)){
                $element_id = is_array($element_id)?$element_id[count($element_id)-1]:$element_id;
                $where[] = ['a.element_ids', 'like', "%,$element_id,%"];
            }
            $list = Db::table('top_company_review_content_list')->where([['element_ids','like',"%,$element_id,%"]])->order('id')->select()->toArray();
            $tmp = [];
            foreach ($list as $k=>$v){
                $files = explode(',',$v['sub_files']);
                $v['sub_files'] = $files;
                $v['mb_sub_files'] = [];
                $v['edit'] = false;
                foreach ($files as $v1){
                    if(!empty($v1)){
                        $v['mb_sub_files'][] = FileModel::getFile(0,$v1,'');
                    }
                }
                $tmp[$v['content_id']][] = $v;
            }
            $result['content'] = Db::table('top_company_review_content')->alias('a')
                ->where($where)->order('a.sort')->field('a.id,a.content,a.file_remark,a.ask,a.standards,a.score,a.cycle,a.method,a.file_remark')
                ->select()->each(function ($item) use ($tmp){
                    $item['list'] = $tmp[$item['id']];
                    return $item;
                })->toArray();
            $element = Db::table('top_company_review_element')
                ->where(['main_id'=>$company['review_id']])
                ->field('id,name,pid')->select()->toArray();
            $result['element'] = get_tree_children($element);
            result($result);
        } else {
            View::assign('element_id', $element_id);
            View::assign('title', '首页');
            return view();
        }
    }


    public function reviewSave($id = 0, $sub_files = [], $hazard = '',$is_sub=0)
    {
        if (empty($hazard)) {
//            result('', 1001, '请填写隐患排查');
        }
        $re = Db::table('top_company_review_content_list')->where(['id' => $id])->find();
        if (empty($re)) {
            result('', 1001, '填报数据有误');
        }
        if ($re['is_sub'] == 1) {
            //result('', 1001, '重复上报');
        }
        $data = [
            'sub_files' => implode(',',$sub_files),
            'hazard' => $hazard,
            'is_sub' => $is_sub,
            'sub_time' => date('Y-m-d H:i:s'),
            'sub_user_id' => $_SESSION['company']['user_id'],
            'sub_user_name' => $_SESSION['company']['user_name'],
        ];
        foreach ($sub_files as $v){
            FileModel::saveFile($v,'review/'.date('Ym'));
        }
        Db::table('top_company_review_content_list')->where(['id' => $id])->update($data);
        result('', 0, '保存成功');
    }

    public function upload($model = 'company')
    {
        $file = request()->file('file');
        $result = FileModel::upload($file, $model);
        result($result);
    }

    //培训合格证书管理表
    public function trainingCertificate()
    {
        if (request()->isAjax()) {
            $trainingList = Db::table('top_training_certificate')->where(['company_id'=>$_SESSION['company']['id']])->select()->toArray();
            foreach ($trainingList as $k=>$item){
                $item['file'] = empty($item['file'])?[]:explode(',',$item['file']);
                foreach ($item['file'] as $v){
                    $f = FileModel::getFile('',$v,'');
                    $trainingList[$k]['files'][] = $f;
                }
                $year_review_info = empty($item['year_review_info'])?[]:explode(',',$item['year_review_info']);
                $trainingList[$k]['year_review_info'] = [];
                foreach ($year_review_info as $v){
                    $f = FileModel::getFile('',$v,'');
                    $trainingList[$k]['year_review_info'][] = $f;
                }
            }
            result($trainingList);
        } else {
            return view();
        }
    }

    //保存培训合格证书
    public function trainingSave()
    {
        try {
            $post = $this->request->post();

            //处理图片
            $file = '';
            if( !empty(isset($post['files'])) && count(isset($post['files'])) )
            {
                foreach ($post['files'] as $item)
                {
                    $file .= $item['code'].',';
                }
                $file = rtrim($file,',');
            }
            $post['file'] = $file;

            $codes = [];
            foreach ($post['year_review_info'] as $v1){
                $codes[] = $v1['code'];
                FileModel::saveFile($v1['code'],'company/info/'.date('Ym'));
            }
            $post['year_review_info'] = implode(',',$codes);
            if( isset($post['id']) && !empty($post['id']) )
            {
                //修改
                $post['update_time'] = date('Y-m-d H:i:s');
                $id = $post['id'];
                unset($post['id']);
                $re = Db::table('top_training_certificate')->strict(false)->where(['id' => $id])->update($post);
            }else{
                //新增
                $post['company_id'] = $_SESSION['company']['id'];
                $post['create_time'] = date('Y-m-d H:i:s');
                $post['update_time'] = date('Y-m-d H:i:s');
                unset($post['id']);
                $re = Db::table('top_training_certificate')->strict(false)->insert($post);
            }

            result('', 0, '保存成功');
        }catch (Exception $e){
            result('', $e->getCode(), $e->getMessage());
        }
    }

    //删除培训合格证书
    public function trainingDelete()
    {
        try {
            $post = $this->request->post();

            if( isset($post['id']) && !empty($post['id']) )
            {
                //删除
                $re = Db::table('top_training_certificate')->where(['id' => $post['id']])->delete();

                if( $re )
                {
                    result('', 0, '删除成功');
                }else{
                    result('', 201, '删除失败');
                }
            }
            result('', 201, '删除失败');
        }catch (Exception $e){
            result('', $e->getCode(), $e->getMessage());
        }
    }

    //作业许可管理表
    public function specialWork()
    {
        if (request()->isAjax()) {
            $trainingList = Db::table('top_special_work')->where(['company_id'=>$_SESSION['company']['id']])->select()->toArray();

            //处理图片
            if( !empty(isset($trainingList)) && count(isset($trainingList)) )
            {
                foreach ($trainingList as $k => $item)
                {
                    if( empty($item['work_permit']) )
                    {
                        $trainingList[$k]['files'] = [];
                        continue;
                    }

                    $item['work_permit'] = explode(',',$item['work_permit']);
                    foreach ($item['work_permit'] as $v){
                        $f = FileModel::getFile('',$v,'');
                        $trainingList[$k]['files'][] = $f;
                    }
                }
            }
            result($trainingList);
        } else {
            return view();
        }
    }

    //保存作业许可
    public function specialWorkSave()
    {
        try {
            $post = $this->request->post();

            //处理图片
            $file = '';
            if( !empty(isset($post['files'])) && count(isset($post['files'])) )
            {
                foreach ($post['files'] as $item)
                {
                    $file .= $item['code'].',';
                }
                $file = rtrim($file,',');
            }
            $post['work_permit'] = $file;

            if( isset($post['id']) && !empty($post['id']) )
            {
                //修改
                $post['update_time'] = date('Y-m-d H:i:s');
                $id = $post['id'];
                unset($post['id']);
                $re = Db::table('top_special_work')->strict(false)->where(['id' => $id])->update($post);
            }else{
                //新增
                $post['company_id'] = $_SESSION['company']['id'];
                $post['create_time'] = date('Y-m-d H:i:s');
                $post['update_time'] = date('Y-m-d H:i:s');
                unset($post['id']);
                $re = Db::table('top_special_work')->strict(false)->insert($post);
            }

            result('', 0, '保存成功');
        }catch (Exception $e){
            result('', $e->getCode(), $e->getMessage());
        }
    }

    //删除作业许可
    public function specialWorkDelete()
    {
        try {
            $post = $this->request->post();

            if( isset($post['id']) && !empty($post['id']) )
            {
                //删除
                $re = Db::table('top_special_work')->where(['id' => $post['id']])->delete();

                if( $re )
                {
                    result('', 0, '删除成功');
                }else{
                    result('', 201, '删除失败');
                }
            }
            result('', 201, '删除失败');
        }catch (Exception $e){
            result('', $e->getCode(), $e->getMessage());
        }
    }

    public function testOrc()
    {
//        $str = '证 号 123456 姓 名 王敬德 性别 男 身份证号 510125199101011234 行业类别 危险化学品 人员类型 从业人员 工作单位 通标标准技术服务（上海）有限 公司四川分公司 发证日期 第一次应复审日期 2025-04-14 A 2026-04-14前 有效期限 第二次应复审日期 2028-04-14 2027-04-14前';
//
//        $user = [
//            'name' => '',
//            'grant_date' => '',
//            'validity_end' => '',
//            'year_review_time' => '',
//        ];
//
//        // 匹配“姓 名”和“性别”之间的内容（含中文及空格）
//        preg_match('/姓\s*名\s*(.*?)\s*性别/', $str, $matches);
//        $user['name'] = $matches[1] ?? '';
//        preg_match('/第一次应复审日期\s*(.*?)\s*A/', $str, $matches);
//        $user['grant_date'] = $matches[1] ?? '';
//        preg_match('/A\s*(.*?)\s*前\s*有效期限/', $str, $matches);
//        $user['year_review_time'] = $matches[1] ?? '';
//        $pattern = '/第二次应复审日期\s+(\d{4}-\d{2}-\d{2})/';
//        preg_match($pattern, $str, $matches);
//        $user['validity_end'] = $matches[1];
//
//        dd($user);
//        exit;


        $tmpFilePath = $_FILES['file']['tmp_name'];
        $binaryData = file_get_contents($tmpFilePath); // 读取二进制文件

        $header = [
            'x-ti-app-id: 4dcdc0910e964189feab1636a51b411b',
            'x-ti-secret-code: af9a65d1ff592bfe29daff97ef0d8092',
            'Content-Type: application/octet-stream', // 明确指定二进制流类型
            'Content-Length: ' . strlen($binaryData)
        ];
        $url = 'https://api.textin.com/ai/service/v1/pdf_to_markdown';


        //初始化
        $curl = curl_init();
        //设置抓取的url
        curl_setopt($curl, CURLOPT_URL, $url);
        //设置头文件的信息作为数据流输出
        curl_setopt($curl, CURLOPT_HEADER, 0);
        //设置获取的信息以文件流的形式返回，而不是直接输出。
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        // 超时设置
        curl_setopt($curl, CURLOPT_TIMEOUT, 2000);

        // 超时设置，以毫秒为单位
        // curl_setopt($curl, CURLOPT_TIMEOUT_MS, 500);

        curl_setopt($curl, CURLOPT_HTTPHEADER, $header);

        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);

        //设置post方式提交
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $binaryData);

        //执行命令
        $data = curl_exec($curl);
        $data = json_decode($data, true);

        // 显示错误信息
        if (curl_error($curl)) {
            curl_close($curl);
            return json("ERROR");
        } else {
            // 打印返回的内容
            curl_close($curl);

            $str = $data['result']['detail'][0]['text'];
            $user = [
                'name' => '',
                'grant_date' => '',
                'validity_end' => '',
                'year_review_time' => '',
            ];

            // 匹配“姓 名”和“性别”之间的内容（含中文及空格）
            preg_match('/姓\s*名\s*(.*?)\s*性别/', $str, $matches);
            $user['name'] = $matches[1] ?? '';
            preg_match('/第一次应复审日期\s*(.*?)\s*A/', $str, $matches);
            $user['grant_date'] = $matches[1] ?? '';
            preg_match('/A\s*(.*?)\s*前\s*有效期限/', $str, $matches);
            $user['year_review_time'] = $matches[1] ?? '';
            $pattern = '/第二次应复审日期\s+(\d{4}-\d{2}-\d{2})/';
            preg_match($pattern, $str, $matches);
            $user['validity_end'] = $matches[1];

            $rdata = [
                'code'  => $data['code'],
                'message'  => $data['message'],
                'result'  => $data['result']['detail'][0]['text'],
                'data'  => $user,
            ];
            return json($rdata);
        }



//        $tmpFilePath = $_FILES['file']['tmp_name'];
//        $imageData = file_get_contents($tmpFilePath);
////        $base64 = base64_encode($imageData);
//
//        $header = [
//            'x-ti-app-id' => '4dcdc0910e964189feab1636a51b411b',
//            'x-ti-secret-code' => 'af9a65d1ff592bfe29daff97ef0d8092',
//            'Content-Type: application/octet-stream', // 明确指定二进制流类型
//            'Content-Length: ' . strlen($imageData)
//        ];
//        $url = 'https://api.textin.com/ai/service/v1/pdf_to_markdown';
//        $res = self::curl_post($url, $imageData,$header);
//        dd($res);
    }

    static function curl_post($url, $binaryData = array(), $headers = array())
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $binaryData); // 直接传递二进制内容
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $response = curl_exec($ch);
        if (curl_errno($ch)) {
//            echo 'Error: ' . curl_error($ch);
            return 'Error';
        }
        curl_close($ch);
        return $response;
    }

}
