<?php
declare (strict_types=1);

namespace app\model;

use think\Model;
use app\admin\model\CompanyModel;

/**
 * @mixin \think\Model
 */
class TopOrgTasks extends Model
{
	//

	public function company()
	{
		return $this->belongsTo(CompanyModel::class, 'company_id', 'id');
	}

	public function grading()
	{
		return $this->belongsTo(TopGrading::class, 'grading_id', 'id');
	}

	public function getList($param, $page = 1, $limit = 10)
	{
		$data = $this->paginate($limit)->each(function ($item, $index) {
			$item = $this->getInfo($item);
			return $item;
		});
		return $data;
	}

	public function getInfo($item)
	{
		$companyData = $item->company ? $item->company->toArray() : '';
		$gradingData = $item->grading ? $item->grading->toArray() : '';
		$item->companyData = $companyData;
		$item->gradingData = $gradingData;
		$item->date = $item->date ? date('Y-m-d', strtotime($item->date)) : '';
		$item->statusStr = $this->getStatus($item->status);
		return $item;
	}


	public function getStatus($status)
	{
		$str = '';
		switch ($status) {
			case 1:
				$str = '未签收';
				break;
			case 2:
				$str = '已签收';
				break;
			case 4:
				$str = '已添加评审计划';
				break;
			case 7:
				$str = '已审核';
				break;
			default:
				break;
		}
		return $str;
	}


	public function receiptTask($model)
	{
		// 添加评审任务
		$t_data = [
			'grading_id' => $model->grading_id,
			'company_id' => $model->grading->company_id,
			'org_id' => $_SESSION['org']['org_id'],
			'status' => 2,
			'receive_user_id' => $_SESSION['org']['user_id'],
			'receive_user_name' => $_SESSION['org']['user_name'],
			'receive_time' => date('Y-m-d H:i:s'),
		];
		$this->save($t_data);
		if ($model->save()) {
			return '数据处理成功';
		} else {
			return '数据处理失败';
		}
	}
}
