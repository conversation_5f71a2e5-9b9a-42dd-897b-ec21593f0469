<?php

namespace app\expert\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use app\company\model\CompanyModel;
use app\controller\BuildWord;
use app\expert\model\ExpertModel;
use app\validate\ExpertVerify as Verify;
use Endroid\QrCode\QrCode;
use hg\apidoc\annotation as Apidoc;
use PhpOffice\PhpWord\TemplateProcessor;
use think\facade\View;
use think\Request;
use think\facade\Db;
use app\model\FileModel;
use app\api\model\SecsModel;

/**
 * @Apidoc\Title("日程管理")
 * @Apidoc\Group("Index")
 * @Apidoc\Sort(1)
 */
class Task extends Base {

    /**
     * @Apidoc\Title("我的评审任务")
     * @Apidoc\Desc("我的评审任务")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("日程")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function index($limit=20) {
        if (request()->isAjax()) {
            $where = [];
            $where[] = ['c.expert_id','=',$_SESSION['expert']['id']];
            $field = "b.id,a.company_id,a.company_name,a.level,b.date,c.position_id,b.status,c.status estatus,c.element_name,c.id as task_expert_id";
            $res = Db::table('top_grading')->alias('a')
                ->leftJoin('top_org_tasks b','a.id = b.grading_id')
                ->leftJoin('top_org_tasks_experts c','b.id = c.tasks_id')
                ->where($where)->order('b.date desc')
                ->field($field)
                ->paginate($limit)->each(function ($item, $key) {
                    $e = Db::table('top_org_tasks_element')->where(['tasks_id'=>$item['id'],'expert_id'=>$_SESSION['expert']['id']])->column('element_name');
                    $item['element_name'] = empty($e)?'未分配':implode('，',$e);
                    $item['discuss'] = Db::table('top_discuss_group')->alias('a')
                        ->leftJoin('top_discuss_group_user b',"a.id = b.group_id and user_type='expert' and user_id = '{$_SESSION['expert']['id']}'")
                        ->where(['a.tasks_id'=>$item['id']])
                        ->field('a.*,b.readsum')->find();
                    $item['discuss']['id'] = $item['discuss']['id']*1;
                    $item['discuss']['readsum'] = $item['discuss']['readsum']<=0?'':$item['discuss']['readsum'];
                    $experts = Db::table('top_org_tasks_experts')->where(['tasks_id'=>$item['id']])->select()->toArray();
                    $item['experts'] =$experts;
                    $item['isZhuzhang'] = false;
                    foreach ( $experts as $expert){
                        if($expert['position_id']==1 && $expert['expert_id']==$_SESSION['expert']['id']){
                            $item['isZhuzhang'] = true;
                        }
                    }
                    return $item;
                });
            result($res);
        } else {
            return view();
        }
    }

    /**
     * @Apidoc\Title("评审任务接收")
     * @Apidoc\Desc("评审任务接收")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("日程")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function check($id=0,$status=0,$reason='') {
        if(!in_array($status,[5,7])){
            result('',1002,'请选择接收状态');
        }

        $re = Db::table('top_org_tasks_experts')->where(['tasks_id'=>$id,'expert_id'=>$_SESSION['expert']['id']])->find();
        if(empty($re)){
            result('',1002,'任务信息不存在');
        }
        if($re['status']!=1){
            result('',1002,'重复操作');
        }
        //专家系统确认接收任务或拒绝任务
        $taskCode = Db::table('top_org_tasks')->where(['id'=>$id])->value('code');

        $res = SecsModel::getSecsInterfaceData('secsTask/expert/update-task-expert-status',
            [
                'taskcode'=>$taskCode,
                'joinTaskStatus'=>$status==5 ? 'refuse' : 'agree',
                'refuseReason'=>$reason,
                'expert_id'=>$_SESSION['expert']['id'],
            ]
            ,'post') ;
        if($res['code']==0){
            Db::table('top_org_tasks_experts')->where(['id'=>$re['id']])->update(['status'=>$status,'reason'=>$reason]);
            result('',0,'操作成功');
        }else{
            result('',$res['code'],$res['msg']);
        }
    }

    public function upload($model='expert') {
        $file = request()->file('file');
        $result = FileModel::upload($file,$model);
        FileModel::saveFile($result['code'],'expert/task/'.date('Ym'));
        result($result);
    }

    /**
     * @Apidoc\Title("评审任务要素分配详情")
     * @Apidoc\Desc("评审任务要素分配详情")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("日程")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function getElementInfo($id=0) {
        $task = Db::table('top_org_tasks')->where(['id'=>$id])->find();
        if(empty($task)){
            result('',2001,'评审任务不存在');
        }
        $expert = Db::table('top_org_tasks_experts')->where(['tasks_id'=>$id,'expert_id'=>$_SESSION['expert']['id']])->find();
        if($expert['position_id']!=1){
            result('',2001,'暂无权限');
        }
        $experts = Db::table('top_org_tasks_experts')->where(['tasks_id'=>$id])->select()->toArray();
        foreach ($experts as $k=>$v){
            $experts[$k]['expert_name'] = $v['status']==7?$v['expert_name']:$v['expert_name'].'（未接收）';
        }
        $grading = Db::table('top_grading')->where(['id'=>$task['grading_id']])->find();
        $elements = Db::table('top_company_review_element')->where(['main_id'=>$task['review_id'],'is_del'=>0,'pid'=>0])->order('sort')->select()->toArray();
        $e = Db::table('top_org_tasks_element')->where(['tasks_id'=>$id])->select()->toArray();
        foreach ($elements as $k=>$v){
            $elements[$k]['expert_id'] = '';
            foreach ($e as $v1){
                if($v['id']==$v1['element_id']){
                    $elements[$k]['expert_id'] = empty($v1['expert_id'])?'':$v1['expert_id'];
                    break;
                }
            }
        }
        $result = [
            'id' => $task['id'],
            'company_name' => $grading['company_name'],
            'industry' => $grading['industry'].'/'.$grading['specialty'],
            'review_name' => $task['review_name'],
            'elements' => $elements,
            'experts' => $experts,
        ];
        result($result);
    }


    /**
     * @Apidoc\Title("评审任务要素分配详情")
     * @Apidoc\Desc("评审任务要素分配详情")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("日程")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function elementSave($id=0) {
        $elements = $this->request->post('elements');
        $expert = Db::table('top_org_tasks_experts')->where(['tasks_id'=>$id,'expert_id'=>$_SESSION['expert']['id']])->find();
        if($expert['position_id']!=1){
            result('',2001,'暂无权限');
        }
        $experts = Db::table('top_org_tasks_experts')->where(['tasks_id'=>$id])->select()->toArray();
        $exp = [];
        foreach ($experts as $v){
            $exp[$v['expert_id']] = $v;
        }
        foreach ($elements as $v){
            $e = Db::table('top_company_review_element')->where(['id'=>$v['id']])->find();
            $r = Db::table('top_org_tasks_element')->where(['tasks_id'=>$id,'element_id'=>$v['id']])->find();
            $data = [
                'tasks_id' => $id,
                'element_id' => $e['id'],
                'element_name' => $e['name'],
                'expert_id' => empty($v['expert_id'])?'':$v['expert_id'],
                'expert_name' => empty($exp[$v['expert_id']])?'':$exp[$v['expert_id']]['expert_name'],
                'total' => $e['sum_score'],
                'mark' => $e['mark'],
                'weight' => $e['weight'],
            ];
            if(empty($r)){
                $data['score'] = 0;
                $data['status'] = 1;
                Db::table('top_org_tasks_element')->insert($data);
            }else{
                Db::table('top_org_tasks_element')->where(['id'=>$r['id']])->update($data);
            }
        }
        result('');
    }


    public function review($id=0){
        if (request()->isAjax()) {
            $re = Db::table('top_org_tasks_experts')->where(['tasks_id'=>$id])->find();
            $task = Db::table('top_org_tasks')->where(['id'=>$id])->find();
            $grading = Db::table('top_grading')->where(['id'=>$task['grading_id']])->find();
            $details = Db::table('top_org_tasks_works')->where(['tasks_id'=>$task['id']])->order('sort')->select()->toArray();
            $detail = [];
            foreach ($details as $v){
                $v['files'] = json_decode($v['files'],true);
                $detail[$v['stage']][] = $v;
            }
            $review_flow = config('global.review_flow');
            foreach ($review_flow as $k=>$v){
                $review_work[] = [
                    'stage'=>$v['stage'],
                    'work' => $detail[$k],
                ];
            }
            $data['review_work'] = $review_work;
            $res = Db::table('top_org_tasks_experts')->where(['tasks_id'=>$id])->select()->toArray();
            $ids = [];
            foreach ($res as $v){
                $eids = explode(',',$v['element_id']);
                foreach ($eids as $v1){
                    if($v1>0){
                        $idarr[$v1] = [
                            'user_name' => $v['expert_name'],
                            'status' => $v['status'],
                        ];
                        $ids[] = $v1;
                    }
                }
            }
            $review_list = Db::table('top_org_tasks_element')->where('tasks_id','=',$id)->select()->toArray();
            $data['review_list'] = $review_list;
            result($data);
        } else {
            $task = Db::table('top_org_tasks')->where(['id'=>$id])->find();
            $re = Db::table('top_org_tasks_experts')->where(['tasks_id'=>$id,'expert_id'=>$_SESSION['expert']['id']])->find();
            if($re['position_id']!=1){
                return redirect('score?id='.$re['id']);
            }
            $grading = Db::table('top_grading')->where(['id'=>$task['grading_id']])->find();
            $company = Db::table('top_company_info')->where(['id'=>$task['company_id']])->find();
            $details = Db::table('top_org_tasks_works')->where(['tasks_id'=>$task['id']])->order('sort')->select()->toArray();
            $detail = [];
            foreach ($details as $v){
                $v['files'] = json_decode($v['files'],true);
                $detail[$v['stage']][] = $v;
            }
            $review_flow = config('flow.review_flow');
            foreach ($review_flow as $k=>$v){
                $review_work[] = [
                    'stage'=>$v['stage'],
                    'work' => $detail[$k],
                ];
            }
            $url = $_SERVER['REQUEST_SCHEME'].'://'.$_SERVER['HTTP_HOST'].'/general/toppingsoft/index.php/exam/mobile/myd?id='.$id;
            // 实例化QrCode对象
            $qrCode = new QrCode($url);
            // 设置二维码的尺寸
            $qrCode->setSize(500);
            // 设置二维码的边距
            $qrCode->setMargin(10);
            // 设置二维码的颜色和背景颜色
            $qrCode->setForegroundColor(['r' => 0, 'g' => 0, 'b' => 0, 'a' => 0]);
            View::assign('mydcodeimg', $qrCode->writeDataUri());
            View::assign('review_flow', $review_work);
            View::assign('task', $task);
            View::assign('re', $re);
            View::assign('grading', $grading);
            View::assign('standard_id', $company['standard_id']);
            return view();
        }
    }


    public function elementReview($id=0){
        if (!$this->request->isPost()) {
            $re = Db::table('top_org_tasks_experts')->where(['id'=>$id])->find();
            $task = Db::table('top_org_tasks')->where(['id'=>$re['tasks_id']])->find();
            $company = Db::table('top_grading')->where(['id'=>$task['grading_id']])->find();
            $details = Db::table('top_org_tasks_works')->where(['tasks_id'=>$task['id']])->order('sort')->select()->toArray();
            $detail = [];
            foreach ($details as $v){
                $v['files'] = json_decode($v['files'],true);
                $detail[$v['stage']][] = $v;
            }
            $review_flow = config('flow.review_flow');
            foreach ($review_flow as $k=>$v){
                $review_work[] = [
                    'stage'=>$v['stage'],
                    'work' => $detail[$k],
                ];
            }
            View::assign('review_flow', $review_work);
            View::assign('task', $task);
            View::assign('re', $re);
            View::assign('company', $company);
            return view();
        } else {
//            result([]);
            $experts = Db::table('top_org_tasks_experts')->where(['id'=>$id])->find();
            $task = Db::table('top_org_tasks')->where(['id'=>$experts['tasks_id']])->find();
            $company = Db::table('top_grading')->where(['id'=>$task['grading_id']])->find();
            $companyinfo = Db::table('top_company_info')->where(['id'=>$company['company_id']])->find();
            $re = Db::table('top_company_review')->where(['id' => $companyinfo['review_id'], 'is_del' => 0])->find();
//            dd($re);
            if ($re) {
                $re['elementTitle'] = empty($re['element']) ? [] : explode(',', $re['element']);
                $where = [
                    ['a.main_id', '=', $re['id']],
                    ['a.is_del', '=', 0],
//                    ['b.element_ids', 'like', "%,$element_id,%"],
                ];
                $content = Db::table('top_company_review_element')->alias('a')
                    ->leftJoin('cay_company_review_content_scoring b', 'a.id = b.content_id')
                    ->where($where)->order('a.sort,b.id')->field('b.*,a.id cid,a.ask,a.standards,a.score scoret,a.method,a.cycle,a.content,a.element_id')->select()->toArray();
//                dd($where);
                foreach ($content as $k=>$v){
                    if(is_null(json_decode($v['method']))){
                        $content[$k]['method'] = $v['method'];
                    }else{
                        $arr = json_decode($v['method'],true);
                        $a = [];
                        foreach ($arr as $v1){
                            $a[] = $v1['key'];
                        }
                        $content[$k]['method'] = implode('<br/>',$a);
                    }
                }
                if (empty($re['elementTitle'])) {
                    $re['content'] = $content;
                } else {
                    $re['content'] = [];
                    foreach ($content as $v) {
                        $v['sub_files'] = empty($v['sub_files']) ? [] : json_decode($v['sub_files'], true);
                        $tmp[$v['element_id']][] = $v;
                    }
                    $element_ids = explode(',',$experts['element_id']);
                    foreach ($element_ids as $v){
                        $element = Db::table('cay_company_review_element')->where([['is_del', '=', 0]])->where("pids like '%,$v,%' or id = $v")->order('sort,id')->select()->toArray();
                        $element = get_tree_children($element);
                        for ($i = 1; $i <= count($re['elementTitle']); $i++) {
                            $re['elementParent' . $i] = self::getTreeElement($element, 1, $i);
                        }
                    }
                    $content = self::getTreeContent($element, $tmp, 1, count($re['elementTitle']))['res'];
                    $re['content'] = self::getTreeContentList($content, [], 0, count($re['elementTitle']));
                }
            } else {
                $re = [];
            }
            result($re);
        }
    }



    public function score($id=0)
    {
        if (request()->isAjax()) {
//            $expert = Db::table('top_org_tasks_experts')->where(['tasks_id'=>$id,'expert_id'=>$_SESSION['expert']['id']])->find();
            $expert = Db::table('top_org_tasks_experts')->where(['id'=>$id])->find();
            $task = Db::table('top_org_tasks')->where(['id'=>$expert['tasks_id']])->find();
            $elements = Db::table('top_org_tasks_element')->where(['tasks_id'=>$expert['tasks_id'],'expert_id'=>$expert['expert_id']])->select()->toArray();
            $company = Db::table('top_company_info')->where(['id'=>$task['company_id']])->find();
            foreach ($elements as $k=>$v){
                $elements[$k]['id'] = $v['element_id'];
                $elements[$k]['name'] = $v['element_name'];
                $elements[$k]['children'] = self::getElements($v['element_id'],$expert['tasks_id'],$company['review_id']);
            }
            $result['content'] = $elements;
            result($result);
        } else {
            View::assign('id', $id);
            View::assign('title', '首页');
            return view();
        }
    }

    public static function getElements($pid,$tasks_id,$review_id){
        $list = Db::table('top_company_review_content_list')->where([['review_id','=',$review_id]])->order('id')->select()->toArray();
        $tmp = [];
        foreach ($list as $k1=>$v1){
            $files = explode(',',$v1['sub_files']);
            $v1['sub_files'] = $files;
            $v1['mb_sub_files'] = [];
            $v1['edit'] = false;
            foreach ($files as $v2){
                if(!empty($v2)){
                    $v1['mb_sub_files'][] = FileModel::getFile(0,$v2,'');
                }
            }
            $tmp[$v1['content_id']][] = $v1;
        }
        $content = Db::table('top_company_review_content')->alias('a')
            ->leftJoin('top_org_tasks_score b',"a.id=b.content_id and b.tasks_id = {$tasks_id}")
            ->where([['a.element_ids','like',','.$pid.',%']])
            ->order('a.sort,a.id')->field('a.id,a.ask,a.standards,a.score scores,a.cycle,a.method,a.element_id,a.element_ids,b.score,b.reform,b.is_reform,b.miss,b.resion,b.summary,b.files,b.deduct,b.deduct_reason,b.miss_reason,b.deduct_material')
            ->select()->each(function ($item) use ($tmp){
                $item['list'] = $tmp[$item['id']];
                $item['is_reform'] = $item['is_reform']==1;
                $item['have_reform'] = mb_strpos($item['method'], '★') !== false?1:0;
                $item['is_content'] = true;
                $item['score'] = is_numeric($item['score'])?$item['score']:'';
                $item['deduct'] = is_numeric($item['deduct'])?$item['deduct']:'';
                $item['miss'] = is_numeric($item['miss'])?$item['miss']:'';
                $item['name'] = $item['ask'];
                $files = empty($item['files'])?[]:explode(',',$item['files']);
                $item['files'] = [];
                foreach ($files as $v){
                    $f = FileModel::getFile('',$v,'');
                    $item['files'][] = [
                        'id' => $f['id'],
                        'code' => $f['code'],
                        'name' => $f['name'],
                        'url' => $f['url'],
                    ];
                }
                $deduct_material = empty($item['deduct_material'])?[]:explode(',',$item['deduct_material']);
                $item['deduct_material'] = [];
                foreach ($deduct_material as $v){
                    $f = FileModel::getFile('',$v,'');
                    $item['deduct_material'][] = [
                        'url' => $f['url'],
                        'code' => $f['code'],
                    ];
                }
                $item['reform'] = empty($item['reform'])?[0=>'']:explode(',',$item['reform']);
                return $item;
            })->toArray();
//        dd($content);
        return $content;
        /*$res = Db::table('top_company_review_element')->where(['pid'=>$pid])->select()->toArray();
        foreach ($res as $k=>$v){
            $children = self::getElements($v['id'],$tasks_id,$review_id);
            if(empty($children)){
                $list = Db::table('top_company_review_content_list')->where([['review_id','=',$review_id]])->order('id')->select()->toArray();
                $tmp = [];
                foreach ($list as $k1=>$v1){
                    $files = explode(',',$v1['sub_files']);
                    $v1['sub_files'] = $files;
                    $v1['mb_sub_files'] = [];
                    $v1['edit'] = false;
                    foreach ($files as $v2){
                        if(!empty($v2)){
                            $v1['mb_sub_files'][] = FileModel::getFile(0,$v2,'');
                        }
                    }
                    $tmp[$v1['content_id']][] = $v1;
                }
                $content = Db::table('top_company_review_content')->alias('a')
                    ->leftJoin('top_org_tasks_score b',"a.id=b.content_id and b.tasks_id = {$tasks_id}")
                    ->where(['a.element_id'=>$v['id']])
                    ->order('a.sort,a.id')->field('a.id,a.ask,a.standards,a.score scores,a.cycle,a.method,a.element_id,a.element_ids,b.score,b.reform,b.is_reform,b.miss,b.resion,b.summary,b.files')
                    ->select()->each(function ($item) use ($tmp){
                        $item['list'] = $tmp[$item['id']];
                        $item['is_reform'] = $item['is_reform']==1;
                        $item['have_reform'] = mb_strpos($item['method'], '★') !== false?1:0;
                        $item['is_content'] = true;
                        $item['score'] = is_numeric($item['score'])?$item['score']:'';
                        $item['deduct'] = is_numeric($item['deduct'])?$item['deduct']:'';
                        $item['miss'] = is_numeric($item['miss'])?$item['miss']:'';
                        $item['name'] = $item['ask'];
                        $files = empty($item['files'])?[]:explode(',',$item['files']);
                        $item['files'] = [];
                        foreach ($files as $v){
                            $f = FileModel::getFile('',$v,'');
                            $item['files'][] = [
                                'id' => $f['id'],
                                'code' => $f['code'],
                                'name' => $f['name'],
                                'url' => $f['url'],
                            ];
                        }
                        return $item;
                    })->toArray();
                $res[$k]['children'] = $content;
            }else{
                $res[$k]['children'] = $children;
            }
        }
        return $res;*/
    }

    public function scoreSave($id=0)
    {
        $param = $this->request->post();
        $expert = Db::table('top_org_tasks_experts')->where(['id'=>$id])->find();
        $elements = Db::table('top_org_tasks_element')->where(['tasks_id'=>$expert['tasks_id'],'expert_id'=>$_SESSION['expert']['id']])->select()->toArray();
        if(empty($elements)){
            result('',1003,'暂无评分权限');
        }
        $eles = [];
        foreach ($elements as $v){
            $v['score'] = $v['miss'] = 0;
            $eles[$v['element_id']] = $v;
        }
        $err = false;
        foreach ($param['data'] as $v)
        {
            $v['scores'] = is_numeric($v['scores']) && !empty($v['scores'])?$v['scores']:0;
            $element_id = explode(',',trim($v['element_ids'],','))[0];
            if($eles[$element_id]){
                $score = Db::table('top_org_tasks_score')->where(['tasks_id'=>$expert['tasks_id'],'content_id'=>$v['id']])->find();
                $files = [];
                foreach ($v['files'] as $v1){
                    $files[] = $v1['code'];
                }
                $scoretmp = 0;
                if(is_numeric($v['deduct'])){
                    $scoretmp = $v['scores']-$v['deduct']-$v['miss'];
                }
                /*if($scoretmp<0||$v['miss']<0||$v['deduct']<0){
                    $scoretmp = 0;
                    $v['miss'] = 0;
                    $v['deduct'] = 0;
                }*/
                if( (($v['deduct']>0 && empty($v['deduct_reason'])) || ($v['miss']>0 && empty($v['miss_reason'])))&&$param['status']==1 )
                {
                    result('',1003,'请完成所有内容打分，缺项项目必须写明缺项说明，扣分项目必须写明扣分说明');
                }

                $data = [
                    'element_id' => $element_id,
                    'element_ids' => $v['element_ids'],
                    'sumscore' => $v['scores'],
                    'score' => $scoretmp,
                    'deduct' => is_numeric($v['deduct'])?$v['deduct']:'',
                    'is_reform' => !empty($v['reform'])?1:0,
                    'miss' => is_numeric($v['miss'])?$v['miss']:'',
                    'resion' => $v['resion']??'',
                    'summary' => $v['summary']??'',
                    'files' => empty($files)?'':implode(',',$files),
                    'reform' => empty($v['reform'])?'':implode(',',$v['reform']),
                    'deduct_reason' => $v['deduct_reason']??'',
                    'miss_reason' => $v['miss_reason']??'',
                ];

                $data['deduct_material'] = '';
                if( !empty($v['deduct_material']) && is_array($v['deduct_material']) )
                {
                    foreach ($v['deduct_material'] as $item)
                    {
                        if( empty($data['deduct_material']) ){
                            $data['deduct_material'] = $item['code'];
                        }else{
                            $data['deduct_material'] .= ','.$item['code'];
                        }
                    }
                }

                if(empty($score)){
                    $data['tasks_id'] = $expert['tasks_id'];
                    $data['content_id'] = $v['id'];
                    Db::table('top_org_tasks_score')->insert($data);
                }else{
                    Db::table('top_org_tasks_score')->where(['id'=>$score['id']])->update($data);
                }

                $eles[$element_id]['score'] += $v['score'];
                $eles[$element_id]['miss'] += $v['miss'];
                $eles[$element_id]['deduct'] += $v['deduct'];
            }
        }

        if($param['status']==1)
        {
            foreach ($eles as $v)
            {
                $v['score'] = is_numeric($v['score']) && !empty($v['score'])?$v['score']:0;
                $v['total'] = is_numeric($v['total']) && !empty($v['total'])?$v['total']:0;
                $v['miss'] = is_numeric($v['miss']) && !empty($v['miss'])?$v['miss']:0;
                $v['deduct'] = is_numeric($v['deduct']) && !empty($v['deduct'])?$v['deduct']:0;

                $rate = (int)(($v['score']/($v['total']-$v['miss']))*100);
                Db::table('top_org_tasks_element')->where(['id'=>$v['id']])->update(['score'=>$v['score'],'miss'=>$v['miss'],'deduct'=>(int)$v['deduct'],'rate'=>$rate,'status'=>'2']);
            }
        }
        if($id>0){
            result(['id'=>$id],0,'保存成功');
        }else{
            result('',7001,$id);
        }
    }

    public function reviewStart($id=0){
        $task = Db::table('top_org_tasks')->where(['id'=>$id])->find();
        $expert = Db::table('top_org_tasks_experts')->where(['tasks_id'=>$id,'position_id'=>1])->find();
        if($expert['expert_id']!=$_SESSION['expert']['id']){
            result('',7001,'无操作权限');
        }
        Db::table('top_org_tasks')->where(['id'=>$id])->update(['status'=>8]);
        Db::table('top_org_tasks_works')->where(['tasks_id'=>$id])->delete();
        $review_flow = config('global.review_flow');
        foreach ($review_flow as $k=>$v){
            $sort = 1;
            foreach ($v['work'] as $v1){
                if($v1['type']=='exam'){
                    $v1['content'] = "企业员工人数为{$task['personnel']}人，根据考试抽查规则，此次考试人数为{$task['exam_num']}人，其中必须包含企业主要负责人、安全管理人员、特种作业人员、关键岗位作业人员";
                }
                $data_list[] = [
                    'tasks_id' => $id,
                    'stage' => $k,
                    'type' => $v1['type'],
                    'title' => $v1['title'],
                    'content' => $v1['content'],
                    'files' => json_encode($v1['files']),
                    'status' => 0,
                    'sort' => $sort++,
                ];
            }
        }
        Db::table('top_org_tasks_works')->insertAll($data_list);
        result();
    }

    public function worksuccess($id=0,$remark=''){
        $data = [
            'status'=>2
        ];
        if(!empty($remark)){
            $data['remark'] = $remark;
        }
        Db::table('top_org_tasks_works')->where(['id'=>$id])->update($data);
        result();
    }

    public function updateFiles($id=0,$filelist=[]){
        $re = Db::table('top_org_tasks_works')->where(['id'=>$id])->find();
        foreach ($filelist as $v){
            if(!empty($v['response'])){
                $files[] = [
                    'id' => $v['response']['data']['id'],
                    'name' => $v['response']['data']['name'],
                    'url' => $v['response']['data']['url'],
                ];
            }else{
                $files[] = $v;
            }
        }
        $file = json_decode($re['files'],true);
        if(!empty($file)){
            $file[0]['filelists'] = empty($files)?[]:$files;
            Db::table('top_org_tasks_works')->where(['id'=>$id])->update(['files'=>json_encode($file)]);
        }
        result();
    }

    //评审任务查询详情
    public function baogaoInfo($id=0,$isJson=true)
    {
        $task = Db::table('top_org_tasks')->where(['id'=>$id])->find();
        $re = Db::table('top_grading')->where(['id'=>$task['grading_id']])->find();
        //获取企业信息表
        $companyInfo = Db::table('top_company_info')
            ->where('id', $task['company_id'])
            ->find();
        $companyInfo = \app\model\CompanyModel::codeToText($companyInfo);
        $re = array_merge($re,$companyInfo);

        $experts = Db::table('top_org_tasks_experts')->where(['tasks_id'=>$task['id']])->order('position_id')->select()->toArray();
        foreach ($experts as $k=>$v)
        {
            $experts[$k]['unit_name'] = '市城安院';
            $experts[$k]['mobile'] = '028-87706080';
        }
        //如果专家数量小于4，则补全
        while (count($experts)<4){
            $experts[] = [
                'unit_name' => '市城安院',
                'mobile' => '028-87706080',
                'expert_name' => '',
                'position_id' => '',
                'unit_id' => '',
                'unit_name' => '',
                'mobile' => '',
                'position_id' => '',
                'unit_id' => '',
                'unit_name' => '',
                'mobile' => '',
            ];
        }
        //拆分成直接变量
        foreach ($experts as $k=>$v){
            foreach ($v as $k1=>$v1){
                $re['experts#'.($k+1)."_".$k1]=$v1;
            }
        }

        $re['task'] = $task;
        $re['experts'] = $experts;

        $re['citys'] = [
            ['name' => '', 'unit' => '', 'mobile' => ''],
            ['name' => '', 'unit' => '', 'mobile' => ''],
            ['name' => '', 'unit' => '', 'mobile' => ''],
        ];

        $re['status'] = '是';
        $re['year'] = date('Y');
        $re['month'] = date('m');
        $re['day'] = date('d');
        $review_list = Db::table('top_org_tasks_element')->where('tasks_id','=',$task['id'])->order('element_name')->select()->toArray();
        // dd($review_list);
        $re['total'] = $re['miss'] = $re['score'] = $re['bishi'] = $mark = $bishiscore = 0;
        $bishi = Db::table('top_cay_exam_paper')->field("total_score")->where(['task_id'=>$id])->field('id,total_score')->select()->toArray();
        foreach ($bishi as $v){
            $bishiscore += $v['total_score'];
        }
        $re['bishi'] = (int)($bishiscore/count($bishi));
        $reform = [''];
        $taskReformList = [];
        $reformList = [];


        //查询专家整改信息
        $taskReformListTemp = Db::table('top_org_tasks_score')->field("reform")->where('tasks_id','=',$task['id'])->select()->toArray();

        if( !empty($taskReformListTemp) && count($taskReformListTemp) )
        {
            foreach ($taskReformListTemp as $v)
            {
                if(!empty($v['reform']))
                {
                    $reformTemp = explode(',',$v['reform']);
                    $taskReformList = array_merge($taskReformList,$reformTemp);
                }
            }
        }

        //查询企业整改信息
        $companyReformList = Db::table('top_company_reform')->field("id,reform")->where('task_id','=',$task['id'])->select()->toArray();

        //判断企业无整改信息，则直接查询专家填报的整改信息，第一次则无企业整改信息
        if( is_array($companyReformList) && count($companyReformList) && count($companyReformList) >= count($taskReformList) )
        {
            $reformList = $companyReformList;

            foreach ($companyReformList as $item)
            {
                $reform[] = $item['reform'];
            }
        }
        else
        {
            if( !empty($taskReformList) && count($taskReformList) )
            {
                foreach ($taskReformList as $v)
                {
                    if(!empty($v))
                    {
                        $reformTemp = [
                            'id'=>'',
                            'reform'=>$v
                        ];

                        $reformList[] = $reformTemp;
                    }
                }
            }
        }
        if(count($reformList) == 0)
        {
            $reformList = ['id'=>'','reform'=>''];
        }

        //不合格的事项
//        $re['bufu'] = [];
        foreach ($review_list as $k=>$v)
        {
            $re['total'] += $v['total']*$v['weight'];
            $re['miss'] += $v['miss']*$v['weight'];
            $re['score'] += $v['score']*$v['weight'];
//            //如果不合格
//            if($v['rate']<$v['mark']){
//                $re['bufu'][] = $v['element_name'];
//            }
            $v['deduct'] = $v['total']-$v['score']-$v['miss'];
            $review_list[$k]['deduct'] = $v['deduct'];
            if(in_array($companyInfo['standard_id'],[62,63,53,34,60,61])){
                if($v['score']/($v['total']-$v['miss'])*100<$v['mark']){
                    $re['status'] = '否';
                }
            }
            //实得分（汇总）：单项得分总和—教育培训得分*（100-笔试平均分）% @todo
            if(mb_strpos($v['element_name'],'教育培训')!==false){
                $re['score'] -= round($v['total']*(100-$re['bishi'])/100,1);
            }
//            $mark += $v['score']/($v['total']-$v['miss'])*100*$v['weight'];
        }

        //如果不合格
        $jielunExt0 = "";
        if(in_array($companyInfo['standard_id'],[62,63,53,34,60,61])){
            $jielunExt1 = "且每个一级要素得分率均不低于60%";
            if(count($re['bufu'])>0){
                $jielunExt0 = "不";
                $jielunExt1 =implode(",",$re['bufu'])."要素得分率低于60%";
            }
        }

        //企业性质 ☑️☐
        // 定义选项数组
        $natureOptions = ['国有', '集体', '民营', '私有', '合资', '独资', '其它'];

        // 使用 generateOptions 函数生成企业性质选项字符串
        $re['nature'] = $this->generateOptions($natureOptions, implode("/",$companyInfo['economy_type']));
        //企业涉及主要风险
        // 定义选项数组
        $riskOptions = [
            '动火作业' => '动火作业',
            '外委外包' => '外委外包',
            '检维修' => '检维修',
            '涉危险化学品' => '涉危险化学品',
            '涉爆粉尘' => '涉爆粉尘',
            '有限空间作业' => '有限空间作业',
            '用电安全' => '用电安全',
            '吊装作业' => '吊装作业',
            '高处作业' => '高处作业',
            '消防安全' => '消防安全',
            '金属冶炼' => '金属冶炼',
        ];

        $selectedRisks = [];
        // 假设这是动态获取的值，表示哪些风险项被选中
        //是否涉氨制冷
        if($companyInfo['is_ammonia_cold']==1){
            $selectedRisks[] = "涉危险化学品";
        }

        //是否粉尘涉爆
        if($companyInfo['is_dust_explosion']==1){
            $selectedRisks[] = "涉爆粉尘";
        }

        //是否涉高温熔融金属
        if($companyInfo['is_hot_melting']==1){
            $selectedRisks[] = "金属冶炼";
        }
        //是否涉轻工行业有限空间
        if($companyInfo['is_light_industry']==1){
            $selectedRisks[] = "有限空间作业";
        }

        // 使用 generateOptions 函数生成企业涉及主要风险选项字符串
        $re['risk'] = $this->generateOptions($riskOptions, implode(",",$selectedRisks));
        $re['status_cn'] = $re['status']=='是'?'☑是 □否':'□是 ☑否';
        //现场评审得分（百分制）：实得分（汇总）/（标准分-缺项分）
        $re['scores'] = round($re['score']/($re['total']-$re['miss'])*100,1);
        $re['review_list'] = $review_list;
        //现场评审情况
        $curDate = date('Y年m月日');
        $re['qingkuang']=<<<EOT
    {$curDate}，成都市城市安全与应急管理研究院受成都市应急管理局委托，成立由专家、技术人员组成的现场评审组，按照《成都市企业三级安全生产标准化基本规范》对{$companyInfo['name']}进行安全生产标准化三级达标评审工作。评审主要采用资料查阅、现场查看、笔试及抽查考核等方法，对该企业安全生产标准化建设工作进行评审。首次会议上听取了企业安全生产标准化建设情况汇报，现场评审阶段评审人员查看了企业生产作业现场并按照相关评分标准逐项评审，末次会议中现场评审组向企业通报了安全生产标准化评审情况，指出了存在的主要问题和整改要求，企业负责人做出了整改承诺。
    {$companyInfo['name']}主要从事{$re['industry_name']}，企业现有人员{$companyInfo['personnel']}人，参加笔试的人员  人，询问人员  人次；企业现有生产设备  台，抽查  台。
EOT;
        //现场评审结论
        $re['jielun']=<<<EOT
    该企业已开展安全生产标准化工作并持续运行半年以上，且按《成都市企业三级安全生产标准化基本规范》进行了自评，自评得分为  分；企业在申请评审之日的前1年内无生产安全死亡事故，并向XXXX应急管理局申请了企业安全生产标准化三级达标评审。
    评审组按照《成都市企业三级安全生产标准化基本规范》的要求，对{$companyInfo['name']}进行了三级安全生产标准化评审，评审得分为{$re['scores']}分，{$jielunExt1}。
    评审结论：依据《成都市企业安全生产标准化建设定级管理办法》和《成都市企业三级安全生产标准化基本规范》的规定，{$companyInfo['name']}安全生产标准化评审得分{$jielunExt0}安全生产标准化三级达标要求，成都市城市安全与应急管理研究院向成都市应急管理局{$jielunExt0}推荐{$companyInfo['name']}为安全生产标准化三级达标企业。
    岗位达标建设情况：该企业设置岗位XX个，其中特种（设备）作业岗位XX个、生产工序岗位XX个、承包（承租）单位作业岗位XX个。达标岗位XX个。
    本次评审仅对当时现场和评审资料负责。
EOT;
        //企业安全生产基本情况概述
        $re['gaishu1'] =<<<EOT
    {$companyInfo['name']}公司成立于{$companyInfo['mb_date']}，注册地位于{$companyInfo['reg_address_info']}，生产地位于{$companyInfo['mb_operate_address']}，自有占地面积{$companyInfo['area']}m²（或租赁厂房面积{$companyInfo['area']}m²），法定代表人为{$companyInfo['legal']}，企业现有员工{$companyInfo['personnel']}人。企业主要经营（生产、销售）包括{$companyInfo['business']}、XXXX年XX月开始投入生产，XXXX年年产值为XXXX万元（或XXXX年年产能为XXXX吨），属于（规上、规下或微小）企业。
    公司主要生产工艺为XXXXXXXXXX。生产过程中涉及（动火（包括电气焊、明火、产生火花的切割等）、有限空间作业、临时用电、吊装、涉危险化学品、粉尘涉爆、检维修、外委外包等）危险作业。
EOT;
        //企业标准化管理体系建设及其运行情况
        $re['gaishu2']=<<<EOT
    {$companyInfo['name']}安全生产标准化建设由XXXXXXX咨询指导，从XXXX年XX月开始创建，XXXX年XX月开始安全生产标准化管理体系运行。在安全生产标准化建设中，企业按照《成都市企业三级安全生产标准化基本规范》的要求，成立了安全生产委员会（或安全生产领导小组），设置了安全生产管理机构，配备了X名专职、X名兼职安全管理人员，落实了各级各部门的安全职责，与X个二级部门签订了安全生产目标责任书，建立了X个安全生产责任制、X个安全生产管理制度、X个安全操作规程。
    XXX（企业部门名称）为企业安全教育培训主管部门。制定了各类人员的教育培训计划，对安全教育培训效果进行了评估和改进。建立了培训教育档案，培训记录比较齐全。XX名主要负责人和XX名专（兼）职安全管理人员已参加培训并取得培训合格证书，XX名人员取得了职业卫生管理人员培训合格证书，XX名作业人员取得特种作业操作证或特种设备作业人员证。
    构建了安全风险分级管控和隐患排查治理双重预防机制，企业开展了风险辨识评估，辨识风险xx条，形成了风险管控清单，在关键岗位进行了风险告知。制定了隐患排查工作方案，按照方案进行了隐患排查工作，确定了隐患等级并登记建档。在安全生产标准化建设期间，共查找出XX项事故隐患，且在成都市隐患排查系统进行了申报，目前已整改事故隐患XX项。
    建立了应急救援管理制度，编制了《生产安全事故应急救援预案》，成立了应急救援领导小组，组建了应急救援队伍，配备了应急救援设备设施和应急救援物资。《生产安全事故应急救援预案》于XXXX年XX月在相关部门备案。
EOT;
        $re['reform'] = $reform;
        $re['reformList'] = $reformList;
        $re['reformTotal'] = count($taskReformList);
        $re['jianyi'] = "建议{$companyInfo['name']}按照《成都市企业三级安全生产标准化基本规范》等法律法规、标准规范的要求，结合本次安全生产标准化的评审意见，制定整改方案，及时整改，并将现场存在问题报成都市隐患排查治理动态监管系统。";
//        $re['jiainyi'] = "建议{$re['company_name']}按照《成都市企业三级安全生产标准化基本规范》等法律法规、标准规范的要求，结合本次安全生产标准化的评审意见，制定整改方案，及时整改，并将现场存在问题报成都市隐患排查治理动态监管系统。";
        //获取扣分信息
        $deduction = Db::table('top_org_tasks_score')
            ->where('tasks_id',$id)
            ->where('deduct','>','0')
            ->select()
            ->toArray();
        //获取内容信息 id=>content
        $contentDicts = Db::table('top_company_review_content')
            ->where('main_id',$task['review_id'])
            ->column('CONTENT','id');

        //获取分类信息 id=>content
        $elementDicts = Db::table('top_company_review_element')
            ->where('main_id',$task['review_id'])
            ->column('name','id');

        foreach ($deduction as $k=>$v){
            $elementArr = explode(',',$v['element_ids']);
            $elementOut = [];
            foreach ($elementArr as $k1=>$v1){
                $elementOut[] =  $elementDicts[$v1] ?? '';
            }
            $elementOut = implode('/',$elementOut);
            $deduction[$k]['subject'] = $elementOut ?? '';
            $deduction[$k]['content'] = $contentDicts[$v['content_id']] ?? '';
        }

        $re['deduction_list'] = $deduction;
        $re['deduction_list_deduct_sum'] = array_sum(array_column($deduction,'deduct'));
        $re['deduction_list_miss_sum'] = array_sum(array_column($deduction,'miss'));

        $last=  !empty($task['baogao']) && ($decoded = json_decode($task['baogao'], true)) !== null ? $decoded : $re;
        // $last = $re;
        //区（市）县应急局人员
        foreach ($last['citys'] as $k=>$v){
            foreach ($v as $k1=>$v1){
                $last['citys#'.($k+1)."_".$k1]=$v1;
            }
        }
        unset($last['baogao']);
        unset($last['task']['baogao']);
        if($isJson){
            result($last);
        }else{
            return $last;
        }
    }

    /**
     * 生成选项字符串
     * 根据提供的选项数组和选中的选项数组，生成一个表示选中状态的字符串
     * 选中的选项前会加上☑符号，未选中的选项前会加上□符号
     *
     * @param array $options 所有可能的选项数组，可以是键值对形式或简单数组形式
     * @param string $selectedString 已选中的选项
     * @return string 表示选项选中状态的字符串
     */
    private function generateOptions(array $options, string $selectedString): string {
        $result = '';
        foreach ($options as $key => $option) {
            $result .= (strpos($option, $selectedString) !== false || strpos($selectedString,$option) !== false ) ? '☑' . $option . ' ' : '□' . $option . ' ';
        }
        // 返回整理后的选项字符串，去除首尾空格
        return trim($result);
    }


    /**
     * 生成PDF报告
     * @return void
     */
    public function buildReportPdf(Request $request)
    {
        $docTemplatePath = root_path()."public/word/report1.docx";
        $id = $request->param('id');
        $docPath = root_path()."public/storage/report_".$id.".docx";
        $outPdfName = "public/storage/report_".$id.".pdf";
        $pdfPath = root_path().$outPdfName;
        $templateData = $this->baogaoInfo($id,false);

        //设置不符合项
        $templateData['bufu'] = '';
        if( !empty($templateData['reform']) )
        {
            $reformTemp = explode(',',$templateData['reform']);
            foreach ($reformTemp as $k => $item)
            {
                if( !empty($templateData['bufu']) )
                {
                    $templateData['bufu'] .= "\n".($k+1).'. '.$item;
                }else{
                    $templateData['bufu'] = "\n".($k+1).'. '.$item;
                }
            }
        }

        // 加载文档模板
        $template = new TemplateProcessor($docTemplatePath);
        //如果docPath 目录不存在则创建目录
        if (!file_exists(dirname($docPath))) {
            mkdir(dirname($docPath), 0755, true);
        }
        // 用递归方法遍历项目数据，填充模板中的值
        $this->_setValues($template, $templateData);
        // 保存填充后的模板为新文档
        $template->saveAs($docPath);
        $result = FileModel::wordToPdf($docPath,$pdfPath);
        result(['url'=>'index.php/expert/task/showPdf?id='.$id]);
    }

    public function showPdf($id){
        $pdfPath = root_path()."public/storage/report_".$id.".pdf";
        if(!file_exists($pdfPath)){
            echo "文件不存在";
            exit;
        }
        $content = file_get_contents($pdfPath);
        // 输出PDF文件
        header('Content-Type: application/pdf');
        header('Content-Length: ' . filesize($pdfPath));
        header('Content-Disposition: inline; filename="现场评审报告.pdf"');
        echo $content;
        exit;
    }

    private function _filterData($str)
    {
        $str = str_replace('<br/>', "[w:br/]", $str);
        $str = str_replace('<br>', "[w:br/]", $str);
        $str = str_replace("\n", "[w:br/]", $str);
        $str = str_replace('>', '＞', $str);
        $str = str_replace('<', '＜', $str);
        $str = str_replace('[w:br/]', '<w:p><w:br></w:br></w:p>', $str);
        return $str;
    }

    private function _setValues($template, $data=[], $path = '')
    {
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                if (strpos($key, 'review_list') !== false || strpos($key, 'deduction_list') !== false) {
                    $template->cloneRow($path .$key . '.index' , count($value));
                    foreach($value  as  $_i=>$row){
                        $_index = $_i + 1;
                        $template->setValue($path .$key . '.index#' . $_index, $_index);
                        foreach($row as $k=>$v){
                            $v = $this->_filterData($v);
                            $template->setValue($path .$key . '.' .$k.  '#' . $_index, $v);
                        }
                    }
                }else{
                    // 如果当前值是数组，则递归调用自身，增加路径以处理嵌套数组。
                    $this->_setValues($template, $value, $path . $key . '.');
                }
            } else {
                // 如果当前值不是数组，直接设置值到模板中，使用当前路径加上键名作为模板的访问路径。
                // 如果包含image，则使用setImageValue方法设置图片。
                $value = $this->_filterData($value);
                if (strpos($key, 'theImage') !== false) {
                    $template->setImageValue($path . $key, $value);
                }else{
                    $template->setValue($path . $key, $value);
                }
            }
        }
    }

    /**
     *
     * @param $id
     * @return void
     * @throws \think\db\exception\DbException
     */

    public function baogaoSave($id=0)
    {
        $formData = $this->request->param('data');

        $reform = '';
        if( !empty($formData['reformList']) && count($formData['reformList']) )
        {
            //查询任务数据
            $task = Db::table('top_org_tasks')->field("id,grading_id,company_id")->where(['id'=>$id])->find();

            $reformTemp = [];
            $updateData = [];//修改的数据
            $updateIds = [];//修改的数据id集合
            $insertData = [];//新增的数据
            foreach ($formData['reformList'] as $item)
            {
                $reformTemp[] = $item['reform'];

                //判断id是否为空，不为空则修改数据，为空则新增数据
                if( !empty($item['id']) )
                {
                    $updateData[] = $item;
                    $updateIds[] = $item['id'];
                }
                else{
                    $tempData = [
                        'task_id' => $task['id'],
                        'grading_id' => $task['grading_id'],
                        'company_id' => $task['company_id'],
                        'reform' => $item['reform'],
                    ];
                    $insertData[] = $tempData;
                }
            }
            $reform = implode(',',$reformTemp);

            //给企业赋值整改项
            //修改
            if( count($updateIds) )
            {
                //删除数据
                Db::table('top_company_reform')->where('id','not in', $updateIds)->delete();

                //修改数据
                foreach ($updateData as $updateDatum)
                {
                    Db::table('top_company_reform')->strict(false)->update($updateDatum);
                }
            }
            else{
                //无修改数据，则全部清空，重新添加
                //清空
                Db::table('top_company_reform')->where(['task_id'=>$id])->delete();
            }

            //新增
            if( count($insertData) )
            {
                Db::table('top_company_reform')->strict(false)->insertAll($insertData);
            }

        }
        else
        {
            //清空
            Db::table('top_company_reform')->where(['task_id'=>$id])->delete();
        }

        $data['reform'] = $reform;
        $formData['reform'] = $reform;
        $data['baogao'] = json_encode($formData);
        $re = Db::table('top_org_tasks')->where(['id'=>$id])->update($data);//更新task数据
        result($re);
    }

    public function baogaodayin($id=0){
        if(request()->isAjax()){
            $re = Db::table('top_org_tasks')->where(['id'=>$id])->find();
            $rData = json_decode($re['baogao'],true);

            $rData['reform'] = explode(',',$re['reform']);
            result($rData);
        }else{
            View::assign('id', $id);
            return view();
        }
    }

    public function end($id=0,$status=0){
        Db::startTrans();
        try {
            if($status==1){
                $re = Db::table('top_org_tasks')->where(['id'=>$id])->find();
                Db::table('top_org_tasks')->where(['id'=>$id])->update(['status'=>9]);
                $reform = Db::table('top_org_tasks_score')->where(['tasks_id'=>$id,'is_reform'=>1])->select()->toArray();
                // dd($reform);
                if($reform){
                    $data = [
                        'grading_id' => $re['grading_id'],
                        'company_id' => $re['company_id'],
                        'tasks_id' => $re['id'],
                        'status' => 1,
                        'term' => date('Y-m-d',strtotime('3month')),
                        'create_user_id' => $_SESSION['expert']['id'],
                        'create_user_name' => $_SESSION['expert']['name'],
                        'create_time' => date('Y-m-d H:i:s'),
                    ];
                    Db::table('top_grading_reform')->insert($data);
                    $reformid = Db::table('top_grading_reform')->where($data)->order('id desc')->field('id')->find()['id'];
                    foreach ($reform as $v){
                        $detail = [
                            'reform_id' => $reformid,
                            'content_id' => $v['content_id'],
                            'summary' => $v['summary'],
                            'files' => $v['files'],
                            'date' => '',
                            'content' => '',
                            'photo' => '',
                            'remark' => '',
                        ];
                        Db::table('top_grading_reform_detail')->insert($detail);
                    }
                    $apply_data = [
                        'grading_id' => $re['grading_id'],
                        'prcs_id' => '5',
                        'prcs_name' => '企业整改',
                        'status' => 1,
                        'status_name' => '待整改',
                        'create_user_id' => $_SESSION['expert']['id'],
                        'create_user_name' => $_SESSION['expert']['name'],
                        'create_time' => date('Y-m-d H:i:s'),
                    ];
                    Db::table('top_grading_approval')->insert($apply_data);
                    Db::table('top_grading')->where(['id'=>$re['grading_id']])->update(['prcs_id'=>5,'prcs_name'=>'企业整改','status'=>2,'reform_status'=>1]);
                }else{
                    $apply_data = [
                        'grading_id' => $re['grading_id'],
                        'prcs_id' => '7',
                        'prcs_name' => '市局终审',
                        'status' => 1,
                        'status_name' => '待接收',
                        'create_user_id' => $_SESSION['expert']['id'],
                        'create_user_name' => $_SESSION['expert']['name'],
                        'create_time' => date('Y-m-d H:i:s'),
                    ];
                    Db::table('top_grading_approval')->insert($apply_data);
                    Db::table('top_grading')->where(['id'=>$re['grading_id']])->update(['prcs_id'=>7,'prcs_name'=>'市局终审']);
                }
            }else if($status==2){
                $param = $this->request->param();
                $re = Db::table('top_org_tasks')->where(['id'=>$id])->find();
                Db::table('top_org_tasks')->where(['id'=>$id])->update(['status'=>5,'reason'=>$param['reason'],'remark'=>$param['remark'],'files'=>json_encode($param['files'])]);
                $data = [
                    'prcs_id' => 0,
                    'prcs_name' => '',
                    'status' => 5,
                ];
                Db::table('top_grading')->where(['id'=>$re['grading_id']])->update($data);
                result();
            }
            Db::table('top_grading_approval')->where(['grading_id'=>$re['grading_id'],'prcs_id'=>4,'status'=>2])->update(['status'=>7,'end_user_id'=>$_SESSION['expert']['id'],'end_user_name'=>$_SESSION['expert']['name'],'end_time'=>date('Y-m-d H:i:s'),'status_name'=>'已完成']);
            Db::commit();
            result();
        } catch (\Exception $e) {
            Db::rollback();
            result('',7001,$e->getMessage());
        }
    }

    public function examupdate($id,$standard_id=''){
        try {
            $sign = md5($id.time().rand(1000,9999));
            Db::table('top_org_tasks')->where(['id'=>$id])->update(['exam_sign'=>$sign]);
            $url = $_SERVER['REQUEST_SCHEME'].'://'.$_SERVER['HTTP_HOST'].'/general/toppingsoft/index.php/exam/mobile/joinText?task_id='.$id.'&sign='.$sign.'&standard_id='.$standard_id;
            // 实例化QrCode对象
            $qrCode = new QrCode($url);
            // 设置二维码的尺寸
            $qrCode->setSize(500);
            // 设置二维码的边距
            $qrCode->setMargin(10);
            // 设置二维码的颜色和背景颜色
            $qrCode->setForegroundColor(['r' => 0, 'g' => 0, 'b' => 0, 'a' => 0]);
            // 输出二维码到浏览器
            result(['img'=>$qrCode->writeDataUri()]);
        } catch (ValidateException $e) {
            // 验证失败的处理逻辑
            result('',7001,$e->getMessage());
        } catch (\Exception $e) {
            // 其他异常的处理逻辑
            result('',7001,$e->getMessage());
        }
    }

    public function examend($id){
        Db::table('top_org_tasks')->where(['id'=>$id])->update(['exam_sign'=>'']);
        result();
    }

    public function discussAdd($id){
        $task = Db::table('top_org_tasks')->where(['id'=>$id])->find();
        $org = Db::table('top_org')->where(['id'=>$task['org_id']])->find();
        $expert = Db::table('top_org_tasks_experts')->where(['tasks_id'=>$id,'position_id'=>1])->find();
        if($expert['expert_id']!=$_SESSION['expert']['id']){
            result('',7001,'无操作权限');
        }
        $group = Db::table('top_discuss_group')->where(['tasks_id'=>$id])->find();
        if(!empty($group)){
            result('',2001,'重复操作');
        }
        $experts = Db::table('top_org_tasks_experts')->where(['tasks_id'=>$id])->select()->toArray();
        $data = [
            'tasks_id' => $id,
            'title' => $task['company_name'],
            'date' => $task['date'],
            'status' => 1,
            'last_time' => date('Y-m-d H:i:s'),
        ];
        Db::startTrans();
        try {
            Db::table('top_discuss_group')->insert($data);
            $id = Db::table('top_discuss_group')->where($data)->find()['id'];
            $data_user = [
                'group_id' => $id,
                'user_type' => 'org',
                'user_id' => $org['id'],
                'user_name' => $org['name'],
                'readsum' => 0,
            ];
            Db::table('top_discuss_group_user')->insert($data_user);
            $data_user = [
                'group_id' => $id,
                'user_type' => 'company',
                'user_id' => $task['company_id'],
                'user_name' => $task['company_name'],
                'readsum' => 0,
            ];
            Db::table('top_discuss_group_user')->insert($data_user);
            $content = '“'.$task['company_name'];
            foreach ($experts as $v){
                $data_user = [
                    'group_id' => $id,
                    'user_type' => 'expert',
                    'user_id' => $v['expert_id'],
                    'user_name' => $v['expert_name'],
                    'readsum' => 0,
                ];
                $content .= '、'.$v['expert_name'];
                Db::table('top_discuss_group_user')->insert($data_user);
            }
            $content .= "”已加入群聊";
            $data_content = [
                'group_id' => $id,
                'user_id' => 0,
                'user_type' => 'system',
                'content' => $content,
                'time' => date('Y-m-d H:i:s'),
            ];
            Db::table('top_discuss_group_content')->insert($data_content);
            Db::commit();
            result();
        } catch (\Exception $e) {
            Db::rollback();
            result('',7001,$e->getMessage());
        }
    }

    public function exam($limit=20,$id=0) {
        $where = [];
        $where[] = ['task_id','=',$id];
        $res = Db::table('top_cay_exam_paper')
            ->where($where)->order('start_date desc')
            ->paginate($limit)->each(function ($item, $key) {
                return $item;
            });
        result($res);
    }

    public function examDel($id=0) {
        $where = ['id'=>$id];
        $exam = Db::table('top_cay_exam_paper')->where($where)->find();
        $re = Db::table('top_org_tasks_experts')->where(['tasks_id'=>$exam['task_id'],'expert_id'=>$_SESSION['expert']['id'],'position_id'=>1])->find();
        if(empty($re)){
            result('',1002,'任务信息不存在');
        }
        $res = Db::table('top_cay_exam_paper')->where($where)->delete();
        result($res);
    }

    //获取现场取证图片
    public function getEvidenceData()
    {
        try{
            $request = $this->request->post();
            $task_id = $request['task_id'];

            if( empty($task_id) )
            {
                result();
            }

            $result = [];
            $dataList = Db::table('top_evidence_main')->alias("main")
                ->field('photo.photo_path')
                ->leftJoin('top_evidence_photo photo','main.id = photo.main_id')
                ->where(['main.task_id'=>$task_id])
                ->select()->toArray();

            if( !empty($dataList) )
            {
                foreach ($dataList as $k => $item)
                {
                    $result[$k]['url'] = "/general/toppingsoft/index.php/file/info?code=".$item['photo_path'];
                    $result[$k]['code'] = $item['photo_path'];
                    $result[$k]['checked'] = false;
                }
            }

            result($result);
        } catch (\Exception $e) {
            return ['code' => 1001, 'msg' => $e->getMessage()];
        }
    }

    //获取专家请假原因
    public function getReason(){
        $params = ['pageNo'=>1,'pageSize'=>100,'dictType'=>'secs_expert_leave_reason'];
        $datas = SecsModel::getSecsInterfaceData('secsExpert/dict-data/page',$params,'get');
        return $datas ;
    }

    //专家请假
    public function submitLeave(){

        $request = $this->request->post();
        $params= [];
        $taskcode = Db::table('top_org_tasks')->where('id',$request['task_id'])->value('code');
        $params['taskcode'] = $taskcode;
        $params['experts'] = [
            'expert_id'=> $_SESSION['expert']['expert_id'],
            'expert_name'=>$_SESSION['expert']['name'],
            'leaveReason'=>$request['reason'],
            'leaveReasonRemark'=>$request['expertLeaveRemark'],
        ];

        $datas = SecsModel::getSecsInterfaceData('secsTask/apply4Leave',$params,'post');
        if($datas['code'] == 0){
            Db::table('top_org_tasks_experts')->where(['id'=>$request['task_expert_id']])->update(['status'=>6,'reason'=>$request['expertLeaveRemark']]);
        }
        return $datas ;
    }

    //获取专家信息
    public function getExpertInfo(){
        $request =$this->request->post();
        $id = $request['id'];
        $expertId = Db::table('top_expert')->where('id',$id)->value('expert_id');
        $params = ['expertId'=>$expertId];
        $datas = SecsModel::getSecsInterfaceData('secsExpert/get-with-extension',$params,'get');
        return $datas ;
    }

    public function companyInfo($id = 0)
    {
        \app\model\CompanyModel::getCompanyInfo($id);
        exit();
    }

    public function getReview($element_id = 0)
    {
        if (request()->isAjax()) {
            $request = $this->request->post();
            \app\model\CompanyModel::getAjaxReview($element_id, $request);
            exit();

        } else {
            View::assign('element_id', $element_id);
            View::assign('title', '首页');
            return view();
        }
    }

}
