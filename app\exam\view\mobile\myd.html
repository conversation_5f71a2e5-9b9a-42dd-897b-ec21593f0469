<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>满意度调查</title>
    <!-- 引入样式 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <link href="__PUBLIC__/plugs/layui/css/layui.css" rel="stylesheet">
    <script src="__PUBLIC__/plugs/layui/layui.js"></script>
</head>
<body  style="background: white">
<div id="app">
    <div class="centainer" style="padding: 20px;">
        <h3 v-show="!success" style="font-size: 1.5rem;text-align: center;padding:20px 10%;color:#409EFF;">成都市企业安全生产标准化评审验收工作问卷调查表</h3>
        <el-form v-show="!success" label-position="top" :model="form" :rules="rules" ref="ruleForm">
            <el-form-item label="1.被评审企业名称" prop="company_name">
                <el-input v-model="form.company_name"></el-input>
            </el-form-item>
            <el-form-item label="2.填表时间" prop="field2">
                <el-input v-model="form.field2"></el-input>
            </el-form-item>
            <el-form-item label="3、填表人及联系电话（请留下 联系方式，便于我们工作改进）。" prop="field3">
                <el-input v-model="form.field3"></el-input>
            </el-form-item>
            <el-form-item label="4、现场评审人员是否存在违法廉洁纪律行为，如收受红包礼金、土特产、有价证券、帮忙联系办理私事等行为。" prop="field4">
                <el-input v-model="form.field4"></el-input>
            </el-form-item>
            <el-form-item label="5、您对本次评审成绩是否满意？（满意/不满意）" prop="field5">
                <el-radio v-model="form.field5" label="满意">满意</el-radio>
                <el-radio v-model="form.field5" label="不满意">不满意</el-radio>
            </el-form-item>
            <el-form-item label="6、您对本次评审人员的评审工作是否满意？（满意/不满意）。" prop="field6">
                <el-radio v-model="form.field6" label="满意">满意</el-radio>
                <el-radio v-model="form.field6" label="不满意">不满意</el-radio>
            </el-form-item>
            <el-form-item label="7、您的意见与建议。" prop="field7">
                <el-input v-model="form.field7"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" style="width: 100%;" @click="submit()">提交</el-button>
            </el-form-item>
        </el-form>
        <el-result v-show="success" icon="success" title="提交成功" sub-title="感谢您的参与">
        </el-result>
    </div>
</div>
</body>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        // 模板导入区
        components: {
        },
        data: function () {
            return {
                success:false,
                form: {
                    task_id:'{$result.id}',
                    company_id:'{$result.company_id}',
                    company_name:'{$result.company_name}',
                    field2:'',
                    field3:'',
                    field4:'',
                    field5:'',
                    field6:'',
                    field7:'',
                },
                rules: {
                    company_name: [
                        { required: true, message: '请输入内容', trigger: 'blur' },
                    ],
                    field2: [
                        { required: true, message: '请输入内容', trigger: 'blur' }
                    ],
                    field4: [
                        { required: true, message: '请输入内容', trigger: 'blur' }
                    ],
                    field5: [
                        { required: true, message: '请输入内容', trigger: 'blur' }
                    ],
                    field6: [
                        { required: true, message: '请输入内容', trigger: 'blur' }
                    ],
                    field7: [
                        { required: true, message: '请输入内容', trigger: 'blur' }
                    ],
                }
            }
        },
        methods: {
            submit: function () {
                var _this = this;
                _this.$refs['ruleForm'].validate((valid) => {
                    if (valid) {
                        const loading = _this.$loading();
                        axios.post('mydSave', _this.form).then(function (res) {
                            loading.close();
                            if (res.data.code == 0) {
                                _this.success = true;
                            } else {
                                _this.$message({
                                    message: res.data.msg,
                                    type: "error"
                                });
                                return false;
                            }
                        }).catch(function (error) {
                            console.log("出现错误:",error);
                        });
                    } else {
                        return false;
                    }
                });
            },
        },
    })
</script>

</html>