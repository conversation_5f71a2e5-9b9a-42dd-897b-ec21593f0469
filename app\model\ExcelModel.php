<?php

namespace app\model;

use think\Model;

class ExcelModel extends Model {

    public static $cellName = array('A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'AA', 'AB', 'AC', 'AD', 'AE', 'AF', 'AG', 'AH', 'AI', 'AJ', 'AK', 'AL', 'AM', 'AN', 'AO', 'AP', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AV', 'AW', 'AX', 'AY', 'AZ', 'BA', 'BB', 'BC', 'BD', 'BE', 'BF', 'BG', 'BH', 'BI', 'BJ', 'BK', 'BL', 'BM', 'BN', 'BO', 'BP', 'BQ', 'BR', 'BS', 'BT', 'BU', 'BV', 'BW', 'BX', 'BY', 'BZ');

    //写在处理的前面（了解表格基本知识，已测试）
//     $objPHPExcel->getActiveSheet()->getDefaultRowDimension()->setRowHeight(20);//所有单元格（行）默认高度
//     $objPHPExcel->getActiveSheet()->getDefaultColumnDimension()->setWidth(20);//所有单元格（列）默认宽度
//     $objPHPExcel->getActiveSheet()->getRowDimension('1')->setRowHeight(30);//设置行高度
//     $objPHPExcel->getActiveSheet()->getColumnDimension('C')->setWidth(30);//设置列宽度
//     $objPHPExcel->getActiveSheet()->getStyle('A1')->getFont()->setSize(18);//设置文字大小
//     $objPHPExcel->getActiveSheet()->getStyle('A1')->getFont()->setBold(true);//设置是否加粗
//     $objPHPExcel->getActiveSheet()->getStyle('A1')->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_WHITE);// 设置文字颜色
//     $objPHPExcel->getActiveSheet()->getStyle('A1')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);//设置文字居左（HORIZONTAL_LEFT，默认值）中（HORIZONTAL_CENTER）右（HORIZONTAL_RIGHT）
//     $objPHPExcel->getActiveSheet()->getStyle('A1')->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);//垂直居中
//     $objPHPExcel->getActiveSheet()->getStyle('A1')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID);//设置填充颜色
//     $objPHPExcel->getActiveSheet()->getStyle('A1')->getFill()->getStartColor()->setARGB('FF7F24');//设置填充颜色

    /**
     * 数据导出（基础版）
     * @param array $title   表头设置
     * @param array $data   导出数据
     * @param string $fileName 文件名
     * @param int $sort 是否添加序号
     * @param string $savePath 保存路径
     * @param $isDown   是否下载  false--保存   true--下载
     * @param $family   字体
     * @return string   返回文件全路径
     * @throws PHPExcel_Exception
     * @throws PHPExcel_Reader_Exception
     */
    public static function exportExcel($title = array(), $data = array(), $fileName = '',$sort = false, $savePath = './', $isDown = true,$family = '微软雅黑') {
        $obj = new \PHPExcel();
        $sheet = $obj->setActiveSheetIndex(0);
        $sheet->setTitle('sheet1');   //设置sheet名称
        $_row = 1;   //设置纵向单元格标识
        $sheet->getDefaultStyle()->getFont()->setName($family);//设置文字默认字体
        $sheet->getDefaultRowDimension()->setRowHeight(20);
        $sheet->getDefaultColumnDimension()->setWidth(15);
        //填写表头
        if ($title) {
            if($sort==1){
                $titletmp[] = ['title' => '序号','type' => '','field' => 'x','width' => '10',];
                foreach ($title as $v){
                    $titletmp[] = $v;
                }
                $title = $titletmp;
            }
            $i = 0;
            $titleRow = ExcelModel::titleRow($title,$_row);//表头总行数
            $titleRow1 = ExcelModel::titleRow1($title,$_row);//表头总行数
            list($i,$_row) = ExcelModel::titleTree($sheet,$title,$i,$_row,$titleRow1);
        }
        for($i = 1;$i<=$_row;$i++){
            $sheet->getRowDimension($i)->setRowHeight(25); //设置表头行高
        }
        //填写数据
        if ($data) {
            if($sort==1){
                $i = 1;
                $datatmp = [];
                foreach ($data as $k=>$v){
                    $v['x'] = $i;
                    $datatmp[] = $v;
                    $i++;
                }
                $data = $datatmp;
            }
            foreach ($data as $k=>$v){
                foreach ($title as $v1){
                    if($v1['type']=='image'){
                        $filepath = $v[$v1['field']];
                        if(is_file($filepath)){
                            $fileinfo = pathinfo($filepath);
                            $ext = $fileinfo['extension'];
                            $filename = create_nonce_str(32).'.'.$ext;//文件名改为英文，中文路径无法写入
                            copy($filepath,public_path().'/storage/tmp/'.$filename); //复制原图片文件到临时文件夹
                            $filepath = public_path().'/storage/tmp/'.$filename;
                            $data[$k][$v1['field']] = $filepath;
                            $unlink[] = $filepath;
                        }else if(is_file(public_path().$filepath)){
                            $filepath = public_path().$filepath;
                            $fileinfo = pathinfo($filepath);
                            $ext = $fileinfo['extension'];
                            $filename = create_nonce_str(32).'.'.$ext;//文件名改为英文，中文路径无法写入
                            copy($filepath,public_path().'/storage/tmp/'.$filename); //复制原图片文件到临时文件夹
                            $filepath = public_path().'/storage/tmp/'.$filename;
                            $data[$k][$v1['field']] = $filepath;
                            $unlink[] = $filepath;
                        }
                    }
                }
            }
            ExcelModel::dataTree($sheet,$title,$data,0,$titleRow1+1);
        }
//        exit();
        //文件名处理
        if (!$fileName) {
            $fileName = uniqid(time(), true);
        }
        $objWrite = \PHPExcel_IOFactory::createWriter($obj, 'Excel2007');
        if ($isDown) {   //网页下载
            ob_get_clean();
            ob_clean();
            header('pragma:public');
            header("Content-Disposition:attachment;filename=$fileName.xlsx");
            $objWrite->save('php://output');
            foreach ($unlink as $v){
                unlink($v);
            }
            exit;
        }
//        $_fileName = iconv("UTF-8", "GBK", $fileName);   //转码
        $_savePath = $savePath . $fileName . '.xlsx';
        $objWrite->save($_savePath);
        foreach ($unlink as $v){
            unlink($v);
        }
        return $savePath . $fileName . '.xlsx';
    }

    /**
     * 数据导出（模板表头版）
     * @param array $title   表头设置
     * @param array $data   导出数据
     * @param string $fileName 文件名
     * @param int $sort 是否添加序号
     * @param string $savePath 保存路径
     * @param $isDown   是否下载  false--保存   true--下载
     * @param $family   字体
     * @return string   返回文件全路径
     * @throws PHPExcel_Exception
     * @throws PHPExcel_Reader_Exception
     */
    public static function exportExcelTemplate($templatepath='',$istitle=0,$_row=1,$title = array(), $data = array(), $fileName = '',$sort = false, $savePath = './', $isDown = true,$family = '微软雅黑') {
        //$obj = new \PHPExcel();
        $obj = \PHPExcel_IOFactory::load($templatepath);
        $sheet = $obj->getActiveSheet();
        //填写表头
        if ($title) {
            if($sort==1){
                $titletmp[] = ['title' => '序号','type' => '','field' => 'x','width' => '10',];
                foreach ($title as $v){
                    $titletmp[] = $v;
                }
                $title = $titletmp;
            }
            $i = 0;
            if($istitle){
                $titleRow = ExcelModel::titleRow($title,$_row);//表头总行数
                $titleRow1 = ExcelModel::titleRow1($title,$_row);//表头总行数
                list($i,$_row) = ExcelModel::titleTree($sheet,$title,$i,$_row,$titleRow1);
            }else{
                $titleRow1 = $_row;
            }
        }
        //填写数据
        if ($data) {
            if($sort==1){
                $i = 1;
                $datatmp = [];
                foreach ($data as $k=>$v){
                    $v['x'] = $i;
                    $datatmp[] = $v;
                    $i++;
                }
                $data = $datatmp;
            }
            foreach ($data as $k=>$v){
                foreach ($title as $v1){
                    if($v1['type']=='image'){
                        $filepath = $v[$v1['field']];
                        if(is_file($filepath)){
                            $fileinfo = pathinfo($filepath);
                            $ext = $fileinfo['extension'];
                            $filename = create_nonce_str(32).'.'.$ext;//文件名改为英文，中文路径无法写入
                            copy($filepath,public_path().'/storage/tmp/'.$filename); //复制原图片文件到临时文件夹
                            $filepath = public_path().'/storage/tmp/'.$filename;
                            $data[$k][$v1['field']] = $filepath;
                            $unlink[] = $filepath;
                        }else if(is_file(public_path().$filepath)){
                            $filepath = public_path().$filepath;
                            $fileinfo = pathinfo($filepath);
                            $ext = $fileinfo['extension'];
                            $filename = create_nonce_str(32).'.'.$ext;//文件名改为英文，中文路径无法写入
                            copy($filepath,public_path().'/storage/tmp/'.$filename); //复制原图片文件到临时文件夹
                            $filepath = public_path().'/storage/tmp/'.$filename;
                            $data[$k][$v1['field']] = $filepath;
                            $unlink[] = $filepath;
                        }
                    }
                }
            }
            ExcelModel::dataTree($sheet,$title,$data,0,$titleRow1+1);
        }
//        exit();
        //文件名处理
        if (!$fileName) {
            $fileName = uniqid(time(), true);
        }
        $objWrite = \PHPExcel_IOFactory::createWriter($obj, 'Excel2007');
        if ($isDown) {   //网页下载
            ob_get_clean();
            ob_clean();
            header('pragma:public');
            header("Content-Disposition:attachment;filename=$fileName.xlsx");
            $objWrite->save('php://output');
            foreach ($unlink as $v){
                unlink($v);
            }
            exit;
        }
//        $_fileName = iconv("UTF-8", "GBK", $fileName);   //转码
        $_savePath = $savePath . $fileName . '.xlsx';
        $objWrite->save($_savePath);
        foreach ($unlink as $v){
            unlink($v);
        }
        return $savePath . $fileName . '.xlsx';
    }

    /**
     * 数据导出多Sheet版
     * @param array $results   sheet数据
     * @param string $fileName 文件名
     * @param int $sort 是否添加序号
     * @param string $savePath 保存路径
     * @param $isDown   是否下载  false--保存   true--下载
     * @param $family   字体
     * @return string   返回文件全路径
     * @throws PHPExcel_Exception
     * @throws PHPExcel_Reader_Exception
     */
    public static function exportExcelSheets($results = array(), $fileName = '',$sort = false, $savePath = './', $isDown = true,$family = '微软雅黑') {
        $obj = new \PHPExcel();
        $sheetnum = 0;
        foreach ($results as $v){
            $title = $v['title'];
            $data = $v['data'];
            $sheetname = empty($v['sheetName'])?'sheet'.($sheetnum+1):$v['sheetName'];
            if($sheetnum>0){
                $obj->createSheet();
            }
            $sheet = $obj->setActiveSheetIndex($sheetnum);
            $sheet->setTitle($sheetname);   //设置sheet名称
            $_row = 1;   //设置纵向单元格标识
            $sheet->getDefaultStyle()->getFont()->setName($family);//设置文字默认字体
            $sheet->getDefaultRowDimension()->setRowHeight(20);
            $sheet->getDefaultColumnDimension()->setWidth(15);
            //填写表头
            if ($title) {
                $titletmp = [];
                if($sort==1){
                    $titletmp[] = ['title' => '序号','type' => '','field' => 'x','width' => '10',];
                    foreach ($title as $v){
                        $titletmp[] = $v;
                    }
                    $title = $titletmp;
                }
                $i = 0;
                $titleRow = ExcelModel::titleRow($title,$_row);//表头总行数
                $titleRow1 = ExcelModel::titleRow1($title,$_row);//表头总行数
                list($i,$_row) = ExcelModel::titleTree($sheet,$title,$i,$_row,$titleRow1);
            }
            for($i = 1;$i<=$_row;$i++){
                $sheet->getRowDimension($i)->setRowHeight(25); //设置表头行高
            }
            //填写数据
            if ($data) {
                if($sort==1){
                    $i = 1;
                    $datatmp = [];
                    foreach ($data as $k=>$v){
                        $v['x'] = $i;
                        $datatmp[] = $v;
                        $i++;
                    }
                    $data = $datatmp;
                }
                foreach ($data as $k=>$v){
                    foreach ($title as $v1){
                        if($v1['type']=='image'){
                            $filepath = $v[$v1['field']];
                            if(is_file($filepath)){
                                $fileinfo = pathinfo($filepath);
                                $ext = $fileinfo['extension'];
                                $filename = create_nonce_str(32).'.'.$ext;//文件名改为英文，中文路径无法写入
                                copy($filepath,public_path().'/storage/tmp/'.$filename); //复制原图片文件到临时文件夹
                                $filepath = public_path().'/storage/tmp/'.$filename;
                                $data[$k][$v1['field']] = $filepath;
                                $unlink[] = $filepath;
                            }else if(is_file(public_path().$filepath)){
                                $filepath = public_path().$filepath;
                                $fileinfo = pathinfo($filepath);
                                $ext = $fileinfo['extension'];
                                $filename = create_nonce_str(32).'.'.$ext;//文件名改为英文，中文路径无法写入
                                copy($filepath,public_path().'/storage/tmp/'.$filename); //复制原图片文件到临时文件夹
                                $filepath = public_path().'/storage/tmp/'.$filename;
                                $data[$k][$v1['field']] = $filepath;
                                $unlink[] = $filepath;
                            }
                        }
                    }
                }
                ExcelModel::dataTree($sheet,$title,$data,0,$titleRow1+1);
            }
            $sheetnum++;
        }
//        exit();
        //文件名处理
        if (!$fileName) {
            $fileName = uniqid(time(), true);
        }
        $objWrite = \PHPExcel_IOFactory::createWriter($obj, 'Excel2007');
        if ($isDown) {   //网页下载
            ob_get_clean();
            ob_clean();
            header('pragma:public');
            header("Content-Disposition:attachment;filename=$fileName.xlsx");
            $objWrite->save('php://output');
            foreach ($unlink as $v){
                unlink($v);
            }
            exit;
        }
//        $_fileName = iconv("UTF-8", "GBK", $fileName);   //转码
        $_savePath = $savePath . $fileName . '.xlsx';
        $objWrite->save($_savePath);
        foreach ($unlink as $v){
            unlink($v);
        }
        return $savePath . $fileName . '.xlsx';
    }

    /**
     * 数据导出单Sheet多表格版
     * @param array $results   sheet数据
     * @param string $fileName 文件名
     * @param int $sort 是否添加序号
     * @param string $savePath 保存路径
     * @param $isDown   是否下载  false--保存   true--下载
     * @param $family   字体
     * @return string   返回文件全路径
     * @throws PHPExcel_Exception
     * @throws PHPExcel_Reader_Exception
     */
    public static function exportExcels($results = array(), $fileName = '',$sort = false, $savePath = './', $isDown = true,$family = '微软雅黑') {
        $obj = new \PHPExcel();
        $sheetnum = 0;
        $_row = 1;   //设置纵向单元格标识
        foreach ($results as $v){
            $title = $v['title'];
            $data = $v['data'];
            $sheet = $obj->setActiveSheetIndex($sheetnum);
            $sheet->setTitle('sheet1');   //设置sheet名称
            $sheet->getDefaultStyle()->getFont()->setName($family);//设置文字默认字体
            $sheet->getDefaultRowDimension()->setRowHeight(20);
            $sheet->getDefaultColumnDimension()->setWidth(15);
            //填写表头
            if ($title) {
                $titletmp = [];
                if($sort==1){
                    $titletmp[] = ['title' => '序号','type' => '','field' => 'x','width' => '10',];
                    foreach ($title as $v){
                        $titletmp[] = $v;
                    }
                    $title = $titletmp;
                }
                $i = 0;
                $titleRow = ExcelModel::titleRow($title,$_row);//表头总行数
                $titleRow1 = ExcelModel::titleRow1($title,$_row);//表头总行数
                list($i,$_row) = ExcelModel::titleTree($sheet,$title,$i,$_row,$titleRow1);
            }
            for($i = 1;$i<=$_row;$i++){
                $sheet->getRowDimension($i)->setRowHeight(25); //设置表头行高
            }
            //填写数据
            if ($data) {
                if($sort==1){
                    $i = 1;
                    $datatmp = [];
                    foreach ($data as $k=>$v){
                        $v['x'] = $i;
                        $datatmp[] = $v;
                        $i++;
                    }
                    $data = $datatmp;
                }
                foreach ($data as $k=>$v){
                    foreach ($title as $v1){
                        if($v1['type']=='image'){
                            $filepath = $v[$v1['field']];
                            if(is_file($filepath)){
                                $fileinfo = pathinfo($filepath);
                                $ext = $fileinfo['extension'];
                                $filename = create_nonce_str(32).'.'.$ext;//文件名改为英文，中文路径无法写入
                                copy($filepath,public_path().'/storage/tmp/'.$filename); //复制原图片文件到临时文件夹
                                $filepath = public_path().'/storage/tmp/'.$filename;
                                $data[$k][$v1['field']] = $filepath;
                                $unlink[] = $filepath;
                            }else if(is_file(public_path().$filepath)){
                                $filepath = public_path().$filepath;
                                $fileinfo = pathinfo($filepath);
                                $ext = $fileinfo['extension'];
                                $filename = create_nonce_str(32).'.'.$ext;//文件名改为英文，中文路径无法写入
                                copy($filepath,public_path().'/storage/tmp/'.$filename); //复制原图片文件到临时文件夹
                                $filepath = public_path().'/storage/tmp/'.$filename;
                                $data[$k][$v1['field']] = $filepath;
                                $unlink[] = $filepath;
                            }
                        }
                    }
                }
                $_row = ExcelModel::dataTree($sheet,$title,$data,0,$titleRow1+1)['_row'];
            }
        }
//        exit();
        //文件名处理
        if (!$fileName) {
            $fileName = uniqid(time(), true);
        }
        $objWrite = \PHPExcel_IOFactory::createWriter($obj, 'Excel2007');
        if ($isDown) {   //网页下载
            ob_get_clean();
            ob_clean();
            header('pragma:public');
            header("Content-Disposition:attachment;filename=$fileName.xlsx");
            $objWrite->save('php://output');
            foreach ($unlink as $v){
                unlink($v);
            }
            exit;
        }
//        $_fileName = iconv("UTF-8", "GBK", $fileName);   //转码
        $_savePath = $savePath . $fileName . '.xlsx';
        $objWrite->save($_savePath);
        foreach ($unlink as $v){
            unlink($v);
        }
        return $savePath . $fileName . '.xlsx';
    }

    public static function titleRow($title,$i = 0){
        $j = $k = $i;
        foreach ($title as $v){
            if(!empty($v['children'])){
                $i = ExcelModel::titleRow($v['children'],$j+1);
                $k = $i>$k?$i:$k;
                //echo $i.'<br/>';
            }
        }
        return $k;
    }

    public static function titleRow1($title,$i = 0){
        $j = $k = $i;
        foreach ($title as $v){
            if(!empty($v['children'])&&!empty($v['title'])){
                $i = ExcelModel::titleRow($v['children'],$j+1);
                $k = $i>$k?$i:$k;
                //echo $i.'<br/>';
            }
        }
        return $k;
    }

    //表头设置
    public static function titleTree($sheet,$title,$i = 0,$_row = 1,$titleRow = 1){
        $cellName = ExcelModel::$cellName;
        $r = $_row;
        $j = $i;
        foreach ($title as $v) {
            if(!empty($v['title'])){
                $sheet->setCellValue($cellName[$i] . $r, $v['title']);
                $sheet->getStyle($cellName[$i] . $r)->getFont()->setBold(true);//加粗
                $sheet->getStyle($cellName[$i] . $r)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);//设置水平居中
                $sheet->getStyle($cellName[$i] . $r)->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);//设置垂直居中
                if($v['width']>0){
                    $sheet->getColumnDimension($cellName[$i])->setWidth($v['width']);
                }
                if($v['children']){
                    list($i1,$_row1) = ExcelModel::titleTree($sheet,$v['children'],$i,$r+1,$titleRow,$sheet);
                    $sheet->mergeCells($cellName[$i] . $r . ':' . $cellName[$i1-1] . $r);
                    $i = $i1;
                    $_row = $_row1>=$_row?$_row1:$_row;
                }else{
                    $i++;
                }
            }else{
                if($v['children']){
                    list($i1,$_row1) = ExcelModel::titleTree($sheet,$v['children'],$i,$r,$titleRow,$sheet);
                    $i = $i1;
                    $_row = $_row1>=$_row?$_row1:$_row;
                }
            }
        }
        foreach($title as $v){
            if(empty($v['children'])){
                //echo $cellName[$j] . $r . ':' . $cellName[$j] . $titleRow.'<br/>';
                if($r<$titleRow){
                    $sheet->mergeCells($cellName[$j] . $r . ':' . $cellName[$j] . $titleRow);
                }
                $j++;
            }else{
                $j += ExcelModel::childrenNum($v['children']);
            }
        }
        return [$i,$_row];
    }

    //计算子元素数量
    public static function childrenNum($data){
        $i = 0;
        foreach($data as $v){
            if($v['children']){
                $i += ExcelModel::childrenNum($v['children']);
            }else{
                $i++;
            }
        }
        return $i;
    }

    //表格数据填充
    /*
     * $title 表头数据
     * $data 数据
     * $x 数据写入开始列数
     * $y 数据写入开始行数
     * $childrenRow 下级层数
     */
    public static function dataTree($sheet,$title,$data,$x = 0,$y = 1,$childrenRow=1){
        $cellName = ExcelModel::$cellName;
        list($i,$data) = ExcelModel::dataMerge($data,1,$childrenRow-1);
        foreach ($data as $v){
            $j = $x;
            foreach($title as $_cell){
                if($_cell['children']){
                    $datachild = [];
                    if(empty($v['children'])){
                        foreach($_cell['children'] as $v1){
                            if(empty($v1['children'])){
                                $datachild[0][$v1['field']] = '';
                            }
                        }
                    }else{
                        $datachild = $v['children'];
                    }
                    $j = ExcelModel::dataTree($sheet,$_cell['children'],$datachild,$j,$y)['j'];
                }else{
//                    print_r($v);
//                    echo $v[$_cell['field']].$_cell['field'].'-'.$j.'-'.$y.'<br/>';
                    ExcelModel::dataWrite($sheet,$v[$_cell['field']],$_cell,$j,$y);
                    if($v['_sum']>1){
                        $sheet->mergeCells($cellName[$j] . $y . ':' . $cellName[$j] . ($y+$v['_sum']-1));
                    }
                }
                $j++;
            }
            $y += $v['_sum'];
        }
        return ['j'=>$j-1,'_row'=>$y];
    }

    //计算下级数量
    public static function dataMerge($data,$level,$sumlevel){
        $j = 0;
        foreach($data as $k=>$v){
            if($sumlevel==$level){
                $i = count($v['children']);
            }else{
                list($i,$children) = ExcelModel::dataMerge($v['children'],$level+1,$sumlevel);
            }
            $i = $i>0?$i:1;
            $data[$k]['_sum'] = $i;
            $j += $i;
            if($children){
                $data[$k]['children'] = $children;
            }
        }
        return [$j,$data];
    }

    //单元格数据写入
    public static function dataWrite($sheet,$data,$title,$x,$y){
        $cellName = ExcelModel::$cellName;
        if($title['height']>0){
            $sheet->getRowDimension($y)->setRowHeight($title['height']); //设置行高度
        }
        $family = empty($title['family'])?'':$title['family']; //字体
        $color = empty($title['color'])?'':$title['color']; //颜色
        $size = empty($title['size'])?'':$title['size']; //字体大小
        $bold = $title['bold']; //加粗
		if(is_array($data)){
			if(!empty($data['color'])){
				$color = $data['color'];
			}
			if(!empty($data['family'])){
				$family = $data['family']; //字体
			}
			if(!empty($data['size'])){
				$size = $data['size']; //字体
			}
            if(isset($data['bold'])){
                $bold = $data['bold']; //加粗
            }
			$data = $data['name'];
		}
        switch ($title['type']){
            case 'image':
                $filepath = '';
                if(is_file($data)){
                    $filepath = $data;
//                    $filepath = "D:\MYOA\attach\approve_center/2303/520171613.jpg";
                }else if(is_file(public_path().$data)){
                    $filepath = public_path().$data;
                }
				if(!empty($filepath)){
					$objDrawing = new \PHPExcel_Worksheet_Drawing();
					$objDrawing->setPath($filepath);
					//$objDrawing->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);
					if($title['imgwidth']>0&&$title['imgheight']>0){
						$objDrawing->setResizeProportional(false);
					}
					if($title['imgheight']>0){
						$objDrawing->setHeight($title['imgheight']);
					}
					if($title['imgwidth']>0){
						$objDrawing->setWidth($title['imgwidth']);
					}
					$objDrawing->setCoordinates($cellName[$x] . $y);
                    $objDrawing->setOffsetX(5);
                    $objDrawing->setOffsetY(5);
					$objDrawing->setWorksheet($sheet);
				}
                break;
            case 'string':
                $sheet->setCellValueExplicit($cellName[$x] . $y, $data, \PHPExcel_Cell_DataType::TYPE_STRING)->getStyle($cellName[$x] . $y)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER)->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER)->setWrapText(true);
                break;
            default:
                $sheet->setCellValue($cellName[$x] . $y, $data)->getStyle($cellName[$x] . $y)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER)->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER)->setWrapText(true);
                break;
        }
        if($bold===true){
            $sheet->getStyle($cellName[$x] . $y)->getFont()->setBold(true);//加粗
        }
        if(!empty($color)){
            $sheet->getStyle($cellName[$x] . $y)->getFont()->getColor()->setARGB($color);//设置文字颜色
        }
        if(!empty($family)){
            $sheet->getStyle($cellName[$x] . $y)->getFont()->setName($family);//设置文字字体
        }
        if(!empty($size)){
            $sheet->getStyle($cellName[$x] . $y)->getFont()->setSize($size);//设置文字大小
        }
    }

}
