# 达梦数据库查询工具使用说明

## 📖 工具概述

DmQuery是一个专为达梦数据库设计的Web管理工具，提供直观的数据库操作界面和丰富的数据管理功能。通过浏览器即可完成数据库的查询、编辑、管理等操作。

## 🚀 核心功能

### 1. SQL查询执行
- **多语句支持**：支持SELECT、INSERT、UPDATE、DELETE等所有SQL语句
- **语法高亮**：提供清晰的SQL编辑器
- **执行统计**：显示执行时间、影响行数等详细信息
- **错误提示**：详细的SQL错误信息和建议

### 2. 数据浏览与编辑
- **表格展示**：清晰的表格形式展示查询结果
- **分页查询**：支持大数据量的分页显示（10-1000条/页）
- **行级编辑**：支持单行数据的增删改操作
- **批量操作**：支持多行数据的批量编辑和删除

### 3. 智能列过滤
- **双模式过滤**：
  - **前端过滤**：实时过滤当前页面数据，响应快速
  - **后端过滤**：修改SQL查询条件，支持大数据量
- **多种过滤语法**：
  ```
  张三          # 包含匹配
  =张三         # 精确匹配
  >100          # 数值大于
  <50           # 数值小于
  张*           # 以"张"开头
  *管理*        # 包含"管理"
  ```
- **过滤状态标识**：已过滤的列会有明显的视觉标识

### 4. 表结构管理
- **完整字段信息**：显示字段名、数据类型、长度、是否允许空值等
- **中文注释显示**：自动获取并显示字段的中文注释说明
- **字段管理**：支持字段的添加、编辑、删除操作
- **数据类型支持**：完整支持达梦数据库的各种数据类型

### 5. 数据导入导出
- **批量导入**：支持CSV格式的数据批量导入
- **SQL模板生成**：自动生成INSERT、UPDATE、DELETE等SQL模板
- **快速操作**：一键生成常用SQL语句

## 📋 详细使用方法

### 基础操作流程

#### 1. 访问工具
1. 在浏览器中打开 `DmQuery.php`
2. 系统自动连接达梦数据库
3. 查看页面顶部的连接状态（绿色=正常，红色=异常）

#### 2. 执行SQL查询
1. 在左上角的SQL编辑器中输入SQL语句
2. 点击"执行SQL"按钮（或按Ctrl+Enter）
3. 在右侧查看执行结果和统计信息

#### 3. 浏览表数据
1. 从左侧"表列表"中点击要查看的表名
2. 系统自动执行SELECT查询并显示数据
3. 使用分页控件浏览大量数据

### 高级功能详解

#### 列过滤功能
1. **启用过滤**：
   - 点击工具栏中的"列过滤"按钮
   - 列标题会显示下拉箭头图标

2. **设置过滤条件**：
   - 点击要过滤的列标题
   - 在弹出的过滤器中：
     - 选择过滤模式（前端过滤/后端查询）
     - 输入过滤条件
     - 点击"应用"按钮

3. **过滤模式选择**：
   - **前端过滤**：适合小数据量，实时响应
   - **后端查询**：适合大数据量，修改SQL重新查询

4. **清除过滤**：
   - 单列清除：在过滤器中点击"清除"
   - 全部清除：点击工具栏"清除过滤"按钮

#### 数据编辑操作
1. **新增数据**：
   - 点击"新增数据"按钮
   - 在弹出表单中填写字段值
   - 点击"保存"提交

2. **编辑现有数据**：
   - 点击数据行末尾的"编辑"按钮
   - 修改字段值
   - 点击"保存"更新

3. **删除数据**：
   - 点击数据行末尾的"删除"按钮
   - 确认删除操作

4. **批量操作**：
   - 点击"批量操作"按钮启用批量模式
   - 勾选要操作的数据行
   - 选择"批量修改"或"批量删除"
   - 确认操作

#### 表结构查看
1. 选择表后，点击"表结构"标签
2. 查看完整的字段信息：
   - 字段名（英文）
   - 注释（中文说明）
   - 数据类型和长度
   - 是否允许空值
   - 默认值
3. 使用"添加字段"、"编辑"、"删除"管理字段

### 快捷操作

#### SQL模板功能
- **INSERT模板**：自动生成插入语句框架
- **UPDATE模板**：自动生成更新语句框架
- **DELETE模板**：自动生成删除语句框架
- **SELECT模板**：自动生成查询语句框架

#### 分页设置
- 页面大小选项：10, 20, 50, 100, 200, 500, 1000条/页
- 分页导航：首页、上一页、下一页、末页
- 页面信息：显示当前页范围和总记录数

## 🎯 界面布局说明

```
┌─────────────────────────────────────────────────────────┐
│ 🔵 达梦数据库查询工具 - 连接状态                         │
├─────────────────┬───────────────────────────────────────┤
│ 左侧操作面板     │ 主工作区域                            │
│                 │                                       │
│ 📋 表列表       │ 📝 SQL编辑器                         │
│ ├─ 表1          │ ┌─────────────────────────────────┐   │
│ ├─ 表2          │ │ SELECT * FROM table_name        │   │
│ └─ 表3          │ │ WHERE condition                 │   │
│                 │ └─────────────────────────────────┘   │
│ 🛠️ 快速操作     │                                       │
│ ├─ INSERT模板   │ 🔘 执行SQL  📄 简单查询              │
│ ├─ UPDATE模板   │                                       │
│ ├─ DELETE模板   │ 📊 结果标签页                        │
│ └─ SELECT模板   │ ├─ 📈 查询结果                       │
│                 │ ├─ 🏗️ 表结构                        │
│ 🔧 测试工具     │ └─ ℹ️ 执行信息                       │
│ ├─ 测试注释     │                                       │
│ ├─ 调试表格     │ 🎛️ 数据操作工具栏                    │
│ └─ 测试表结构   │ 📄 分页控件                          │
└─────────────────┴───────────────────────────────────────┘
```

## 🔧 故障排除

### 常见问题及解决方案

1. **数据库连接失败**
   - ❌ 现象：页面顶部显示红色连接状态
   - ✅ 解决：检查数据库服务、网络连接、用户权限

2. **列过滤不准确**
   - ❌ 现象：过滤的不是点击的列
   - ✅ 解决：点击"调试表格"按钮，查看控制台输出

3. **中文注释不显示**
   - ❌ 现象：表结构中注释列显示"-"
   - ✅ 解决：点击"测试注释"按钮，检查数据库注释设置

4. **查询超时**
   - ❌ 现象：大数据量查询长时间无响应
   - ✅ 解决：使用分页查询，优化SQL语句，添加索引

5. **编辑数据失败**
   - ❌ 现象：保存时提示权限错误
   - ✅ 解决：确认数据库用户具有相应的增删改权限

### 调试工具使用

1. **测试注释**：验证字段注释获取功能
2. **调试表格**：输出详细的表格结构信息到控制台
3. **测试表结构**：验证表结构查询功能

## 📊 性能优化建议

### 查询优化
- 使用分页查询处理大数据量
- 为常用查询字段添加索引
- 避免使用SELECT *，指定具体字段
- 合理使用WHERE条件限制结果集

### 过滤优化
- 小数据量（<1000行）使用前端过滤
- 大数据量使用后端过滤
- 数值比较使用>、<操作符
- 文本搜索合理使用通配符

## 🛡️ 安全注意事项

1. **权限控制**：确保数据库用户权限最小化
2. **数据备份**：重要操作前先备份数据
3. **SQL注入防护**：工具已内置防护，但仍需谨慎操作
4. **网络安全**：在安全的网络环境中使用

## 📞 技术支持

如遇到问题或需要功能改进，请：
1. 查看浏览器控制台的错误信息
2. 使用内置的调试工具收集信息
3. 联系开发团队并提供详细的错误描述

---

**版本信息**：当前版本已修复列过滤索引问题，增强了中文注释支持，优化了用户体验。
