<?php

namespace app\expert\model;

use app\model\SettingModel;
use think\facade\Db;
use think\Model;
use Medoo\Mdb;
use app\model\FileModel;

class ExpertModel extends Model
{

    public static function expertSave($param,$id=0)
    {
        $data = [
            'head' => $param['head'],
            'sex' => $param['sex'],
            'birthday' => $param['birthday'],
            'nation' => $param['nation'],
            'qq' => $param['qq'],
            'address' => $param['address'],
            'school' => $param['school'],
            'speciality' => $param['speciality'],
            'education' => $param['education'],
            'employer' => $param['employer'],
            'position' => $param['position'],
            'work_date' => $param['work_date'],
            'position_date' => $param['position_date'],
            'professional' => $param['professional'],
            'professional_number' => $param['professional_number'],
            'secure' => $param['secure'],
            'secure_number' => $param['secure_number'],
            'reg_secure_number' => $param['reg_secure_number'],
            'other_number' => $param['other_number'],
            'major' => $param['major'],
            'employ_date' => $param['employ_date'],
            'offer_info' => $param['offer_info'],
            'resume' => $param['resume'],
            'status' => 1,
        ];
        FileModel::saveFile($data['head'],'expert/info/'.date('Ym'));
        $data['expert_id'] = $_SESSION['expert']['id'];
        $u = [
            'name' => $param['name'],
            'email' => $param['email'],
            'org_id' => $param['org_id'],
        ];
        Db::table('top_expert')->where(['id'=>$_SESSION['expert']['id']])->update($u);
        $re = Db::table('top_expert_info')->where(['expert_id'=>$data['expert_id']])->find();
        if(empty($re)){
            $id = Db::table('top_expert_info')->insertGetId($data);
        }else{
            $id = Db::table('top_expert_info')->where(['id'=>$re['id']])->update($data);
        }
        return $id;
    }


    //参数格式化
    public static function codeToText($info){
        if(empty($info)){
            return $info;
        }
        $info['headUrl'] = empty($info['head'])?'':FileModel::getFile(0,$info['head']);
        foreach ($info as $k=>$v){
            $info[$k] = $v==='0000-00-00'?'':$v;
            if(is_int($v)){
                $info[$k] = (string)$v;
            }
            if($v===null){
                $info[$k] = '';
            }
        }
        return $info;
    }

}