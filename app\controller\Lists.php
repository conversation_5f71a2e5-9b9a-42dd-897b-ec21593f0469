<?php


namespace app\controller;

use app\BaseController;
use app\model\ExcelModel;
use app\model\AuthModel;
use hg\apidoc\annotation as Apidoc;
use app\model\ListModel;
use think\App;
use think\facade\Db;
use think\facade\View;
use think\helper\Str;

/**
 * @Apidoc\Title("通用列表")
 * @Apidoc\Group("Lists")
 * @Apidoc\Sort(3)
 */
class Lists extends BaseController
{
    public $isAdmin = false;
    public $title = '';
    public $table = '';
    public $fields = [];
    public $setting = [];

    public function __construct(App $app,$model='')
    {
        parent::__construct($app);
        $model = $this->request->param('model');
        $model = Str::snake($model); //驼峰转下划线
        $model = str_replace('_','.',$model);
        $setting = config('config_lists.'.$model);
        if($setting['manager']){
            $is_dept = (bool)$setting['manager']['dept_id'];
            $this->isAdmin = AuthModel::isAdmin($setting['manager']['model'],'lists',$is_dept);
        }else{
            $this->isAdmin = true;
        }
        $this->setting = $setting;
        $this->model = $model;
        $this->title = $setting['title'];
        $this->table = $setting['table'];
        $this->fields = $setting['fields'];
    }

    public function list($limit = 20,$excel = 0){
        ListModel::isDb($this->setting);
        if (request()->isAjax()||$excel==1) {
            if(!$this->isAdmin){
                result('',9001,'暂无权限');
            }else if($this->isAdmin!==true){
                if($this->setting['manager']['dept_id']){
                    $where[] = [$this->setting['manager']['dept_id'],'in',$this->isAdmin];
                }
            }
            $param = $this->request->param();
            foreach ($this->fields['search'] as $v){
                if(!empty($param[$v['field']])||$param[$v['field']]==='0'||$param[$v['field']]===0){
                    $where[] = [$v['field'],'=',$param[$v['field']]];
                }
            }
            $res = Db::table($this->table)->where($where)->order($this->setting['order']);
            if($excel==1){
                $res = $res->select()->toArray();
                foreach ($res as $k=>$v){
                    foreach ($this->setting['global'] as $k1=>$v1){
                        foreach ($v1 as $v2){
                            if($v2['value']==$res[$k][$k1]){
                                $res[$k][$k1] = $v2['label'];
                            }
                        }
//                        $res[$k][$k1] = !empty($v1[$res[$k][$k1]])?$v1[$res[$k][$k1]]:$res[$k][$k1];
                    }
                }
                ExcelModel::exportExcel($this->fields['export'], $res, $this->title.'导出',true);
            }
            $res = $res->paginate($limit)->each(function ($item, $key) {
                foreach ($this->setting['global'] as $k1=>$v1){
                    $item['mb_'.$k1] = $item[$k1];
                    foreach ($v1 as $v2){
                        if($v2['value']==$item[$k1]){
                            $item['mb_'.$k1] = $v2['label'];
                        }
                    }
//                    $item['mb_'.$k1] = !empty($v1[$item[$k1]])?$v1[$item[$k1]]:$item[$k1];
                }
                foreach ($this->setting['fields']['add'] as $k=>$v){
                    if(in_array($v['type'],['images','files'])){
                        $item[$v['field']] = empty($item[$v['field']])?[]:json_decode($item[$v['field']],true);
                    }
                }
                return $item;
            });
            result($res);
        }else{
            View::assign('model',$this->model);
            View::assign('isAdmin',$this->isAdmin);
            View::assign('global',$this->setting['global']);
            View::assign('title',$this->title);
            View::assign('fields',$this->fields);
            return view();
        }
    }

    public function save($id=0){
        if(!$this->isAdmin){
            result('',9001,'暂无权限');
        }
        $request = $this->request->post();
        $id = ListModel::listSave($this->setting,$request,$id);
        result(['id'=>$id]);
    }

    public function del($id=0){
        $id = $this->request->param('id');
        $re = ListModel::del($this->table,$id);
        if($re===false){
            result('',1002,'信息有误');
        }
        result('',0,'删除成功');
    }

    //导入模板
    public function importTemplate()
    {
        foreach ($this->fields['import'] as $k=>$v){
            if($v['show']){
                $title[] = ['title'=>$v['title'],'field'=>$v['field'],'width'=>'20','type'=>'string'];
            }
        }
        ExcelModel::exportExcel($title, [], $this->title.'导入模板');
    }


    //数据导入
    public function import()
    {
        $data = import("input");
        $dataHeader = importHeader("input");
        $result = ListModel::import($this->setting,$data,$dataHeader);
        $msg = count($result['success']).'条数据保存成功，'.count($result['repeat']).'条数据重复导入，'.count($result['fail']).'条数据处理失败。';
        result($result,0,$msg);
        foreach ($dataHeader as $k=>$v){
            foreach ($this->setting['fields'] as $k1=>$v1){
                if($v1==$v){
                    $tit[$k] = $k1;
                }
            }
        }
        foreach ($data as $k=>$v){
            $tmp = [];
            foreach($v as $k1=>$v1){
                if(in_array($tit[$k1],[])){
                    $v1 = str_replace('.','-',$v1);
                    if(date('Y',strtotime($v1))>=1900){
                        $tmp[$tit[$k1]] = date('Y-m-d',strtotime($v1));
                    }else{
                        $tmp[$tit[$k1]] = gmdate('Y-m-d',\PHPExcel_Shared_Date::ExcelToPHP($v1));
                    }
                }else{
                    $tmp[$tit[$k1]] = str_replace(' ','',$v1);
                }
            }
            $dataList[] = $tmp;
        }
        foreach ($dataList as $v) {
            if(!empty($v['code'])&&!empty($v['category'])){
                $datauser[] = $v;
            }
        }
        foreach ($datauser as $k=>$v){
            $re = self::importSave($this->setting,$v);
            if($re['code']==0) {
                $result['success'][] = ['row' => $k + 2, 'user_name' => $v['user_name'], 'msg' => '保存成功'];
            }else if($re['code']==-1){
                $result['repeat'][] = ['row' => $k + 2, 'user_name' => $v['user_name'], 'msg' => '单据重复'];
            }else{
                $result['fail'][] = ['row'=>$k+2,'user_name'=>$v['user_name'],'msg'=>$re['msg']];
            }
        }
        $msg = count($result['success']).'条数据保存成功，'.count($result['repeat']).'条数据重复导入，'.count($result['fail']).'条数据处理失败。';
        result($result,0,$msg);
    }



    public function importSave($setting,$param){
        foreach ($setting['fields'] as $k=>$v){
            $data[$k] = $param[$k];
        }
        $re = Db::table($setting['table'])->where($data)->field('id')->find();
        if($re){
            return ['','code'=>-1,'msg'=>'重复'];
        }else{
            $p_code = substr($data['code'], 0, (strlen($data['code']) - 3));
            $p_model = Db::table('top_property_category')->where('code', $p_code)->find();
            $data['pid'] = empty($p_model)?0:$p_model['id'];
            $id = Db::table('top_property_category')->insertGetId($data);
        }
        return ['data'=>['id'=>$id],'code'=>0,'msg'=>'保存成功'];
    }


}