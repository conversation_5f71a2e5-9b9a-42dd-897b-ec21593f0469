<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>企业信息认证</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/company.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .el-cascader { width:100%;}
        .el-form-item__content .el-input-group { vertical-align: middle;}
        .avatar-uploader .el-upload {
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        .avatar-uploader .el-upload:hover {
            border-color: #409EFF;
        }
        .avatar-uploader-icon {
            font-size: 28px;
            color: #8c939d;
            width: 150px;
            height: 150px;
            line-height: 150px;
            text-align: center;
        }
        .avatar {
            width: 150px;
            height: 150px;
            display: block;
        }
        .el-divider--horizontal{ margin:5px 0;}
        .el-input--mini{
            height: initial;
        }

        .el-form-item {
            margin-bottom: 22px;
            height: 50px;
            line-height: 50px;
        }
        .el-input--mini .el-input__inner {
            height: 40px;
            line-height: 40px;
        }
        .el-form-item__content {
            line-height: 40px;
            position: relative;
            font-size: 14px;
        }

    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">
        <el-page-header @back="goBack" content="企业基本信息提交"></el-page-header>
        <el-container>
            <el-form ref="form" :model="data" class="tableSearch" label-position="right" label-width="150px" :rules="rules">
                <el-row :gutter="24">
<!--                    <el-col><el-button style="background-color: #436CE6;color:#ffffff;" size="small" disabled>企业基本信息</el-button></el-col>-->
                    <el-col><el-divider style="margin:5px 0;"></el-divider></el-col>
                    <el-col style="height:20px;"></el-col>
                    <el-col :span="8">
                        <el-form-item label="企业名称" prop="name">
                            <el-input v-model="data.name" size="mini" @change="getCompanyInfo"></el-input>
                        </el-form-item>
                        <el-form-item label="注册资本" prop="reg_money">
                            <el-input v-model="data.reg_money" type="number" size="mini">
                                <template slot="append">万元</template>
                            </el-input>
                        </el-form-item>
                        <el-form-item label="法定代表人" prop="legal">
                            <el-input v-model="data.legal" size="mini"></el-input>
                        </el-form-item>
                        <!--<el-form-item label="经营类型" prop="name">
                            <el-input v-model="data.business_type" size="mini"></el-input>
                        </el-form-item>-->
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="营业执照" prop="license" style="height: 216px">
                            <el-upload
                                    class="avatar-uploader"
                                    action="upload"
                                    :show-file-list="false"
                                    :on-success="(response,file,fileList)=>uploadSuccess(response,file,fileList,'license')"
                                    :before-upload="uploadBefore">
                                <img v-if="data.licenseUrl" :src="data.licenseUrl" class="avatar">
                                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                            </el-upload>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="安全行政许可资料" prop="aoc" style="height: 216px">
                            <el-upload
                                    class="avatar-uploader"
                                    action="upload"
                                    :show-file-list="false"
                                    :on-success="(response,file,fileList)=>uploadSuccess(response,file,fileList,'aoc')"
                                    :before-upload="uploadBefore">
                                <img v-if="data.aocUrl" :src="data.aocUrl" class="avatar">
                                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                            </el-upload>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="法人联系电话" prop="legal">
                            <el-input v-model="data.legal_mobile" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="注册地址" prop="reg_address">
                            <el-cascader
                                    size="mini"
                                    :props="{value:'code',label:'name'}"
                                    v-model="data.reg_address"
                                    :options="config.pca"></el-cascader>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="" label-width="0" prop="title">
                            <el-input v-model="data.reg_address_info" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="法人邮箱" prop="legal_email">
                            <el-input v-model="data.legal_email" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="生产经营地点" prop="operate_address">
                            <el-cascader
                                    size="mini"
                                    :props="{value:'code',label:'name'}"
                                    v-model="data.operate_address"
                                    :options="config.pca"
                                    @change="handleChange"></el-cascader>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="" label-width="0" prop="title">
                            <el-input v-model="data.operate_address_info" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="座机电话号码" prop="phone">
                            <el-input v-model="data.phone" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <!--<el-col :span="8">
                        <el-form-item label="企业传真" prop="fax">
                            <el-input v-model="data.fax" size="mini"></el-input>
                        </el-form-item>
                    </el-col>-->
                    <el-col :span="8">
                        <el-form-item label="产业园区" prop="industrial_park">
                            <el-cascader
                                    size="mini"
                                    :props="{value:'name',label:'name'}"
                                    v-model="data.industrial_park"
                                    :options="config.industrialPark"></el-cascader>
                        </el-form-item>
                    </el-col>
<!--                    <el-col></el-col>-->
                    <el-col :span="8">
                        <el-form-item label="邮政编码" prop="postal_code">
                            <el-input v-model="data.postal_code" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="经济类型" prop="economy_type">
                            <el-cascader
                                    size="mini"
                                    :props="{value:'name',label:'name'}"
                                    v-model="data.economy_type"
                                    :options="config.economyType"></el-cascader>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="国民经济行业" prop="economy_sector">
                            <el-cascader
                                    size="mini"
                                    :props="{value:'name',label:'name'}"
                                    v-model="data.economy_sector"
                                    :options="config.economySector"></el-cascader>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="行业/专业" prop="industry">
                            <el-cascader
                                    size="mini"
                                    :props="{value:'name',label:'name'}"
                                    v-model="data.industry"
                                    :options="config.industry"></el-cascader>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="统一社会信用代码" prop="license_number">
                            <el-input v-model="data.license_number" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="信用代码有效期" prop="license_start">
                            <el-date-picker
                                    style="width: 125px"
                                    v-model="data.license_start"
                                    :picker-options="startPickerOptions"
                                    @change="handleStartChange"
                                    size="mini"
                                    type="date"
                                    placeholder="选择日期"
                                    format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd">
                            </el-date-picker>
                            ~
                            <el-date-picker
                                    style="width: 125px"
                                    v-model="data.license_end"
                                    :picker-options="endPickerOptions"
                                    @change="handleEndChange"
                                    size="mini"
                                    type="date"
                                    placeholder="选择日期"
                                    format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd">
                            </el-date-picker>
                            <el-button type="" size="mini" @click="data.license_end='2099-12-30'">长期</el-button>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="成立日期" prop="date">
                            <el-date-picker
                                    v-model="data.date"
                                    size="mini"
                                    type="date"
                                    placeholder="选择日期"
                                    format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="企业规模" prop="enterprise_size">
                            <el-select v-model="data.enterprise_size" size="mini" placeholder="请选择">
                                <el-option label="大型" value="大型"></el-option>
                                <el-option label="中型" value="中型"></el-option>
                                <el-option label="小型" value="小型"></el-option>
                                <el-option label="微型" value="微型"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="营业场所面积" prop="area">
                            <el-input v-model="data.area" type="number" size="mini">
                                <template slot="append">m²</template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col></el-col>
                    <el-col :span="8">
                        <el-form-item label="员工总数" prop="personnel">
                            <el-input v-model="data.personnel" size="mini">
                                <template slot="append">人</template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="专职安全管理人数" prop="personnel_full">
                            <el-input v-model="data.personnel_full" type="number" size="mini">
                                <template slot="append">人</template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="兼职安全管理人数" prop="personnel_part">
                            <el-input v-model="data.personnel_part" type="number" size="mini">
                                <template slot="append">人</template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col></el-col>
                    <el-col :span="8">
                        <el-form-item label="特种作业人数" prop="personnel_special">
                            <el-input v-model="data.personnel_special" type="number" size="mini">
                                <template slot="append">人</template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="安全管理联系人" prop="manager">
                            <el-input v-model="data.manager" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="联系电话" prop="manager_mobile">
                            <el-input v-model="data.manager_mobile" maxlength="11" placeholder="请输入座机号码" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col></el-col>
                    <el-col :span="8">
                        <el-form-item label="邮箱" prop="manager_email">
                            <el-input v-model="data.manager_email" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="固定资产" prop="fixed_asset">
                            <el-input v-model="data.fixed_asset" size="mini">
                                <template slot="append">万元</template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="年营业收入" prop="revenue">
                            <el-input v-model="data.revenue" size="mini">
                                <template slot="append">万元</template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col></el-col>
                    <el-col :span="16">
                        <el-form-item label="所属集团名称" prop="group_name">
                            <el-input v-model="data.group_name" size="mini" placeholder="如果是某企业集团的成员单位，请注明企业集团名称"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="经营范围" prop="business">
                            <el-input type="textarea" row="3" v-model="data.business" size="mini"></el-input>
                        </el-form-item>
                    </el-col>

                    <el-col :span="24">
                        <div style="width: 100%;border-bottom: 2px solid #dedede;margin: 10px 0px;"></div>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="是否粉尘涉爆" prop="is_dust_explosion">
                            <el-radio v-model="data.is_dust_explosion" label="否">否</el-radio>
                            <el-radio v-model="data.is_dust_explosion" label="是">是</el-radio>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" v-if="data.is_dust_explosion=='是'">
                        <el-form-item label="所属行业" prop="sector">
                            <el-select v-model="data.sector" size="mini" placeholder="请选择">
                                <el-option label="金属制品加工" value="金属制品加工"></el-option>
                                <el-option label="木制品加工" value="木制品加工"></el-option>
                                <el-option label="农副产品加工" value="农副产品加工"></el-option>
                                <el-option label="纺织品加工" value="纺织品加工"></el-option>
                                <el-option label="橡胶塑料制品加工" value="橡胶塑料制品加工"></el-option>
                                <el-option label="冶金/有色/建材煤粉制备" value="冶金/有色/建材煤粉制备"></el-option>
                                <el-option label="烟草" value="烟草"></el-option>
                                <el-option label="其他" value="其他"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <template v-for="(dust,key) in data.dust_list">
                        <el-col :span="8" v-if="data.is_dust_explosion=='是'">
                            <el-form-item label="粉尘种类" prop="dust_type">
                                <el-select v-model="dust.name" @change="chnageDustCheck(dust)" size="mini" placeholder="请选择">
                                    <el-option label="木粉尘" value="木粉尘"></el-option>
                                    <el-option label="金属粉尘" value="金属粉尘"></el-option>
                                    <el-option label="塑料橡胶粉尘" value="塑料橡胶粉尘"></el-option>
                                    <el-option label="农副产品粉尘" value="农副产品粉尘"></el-option>
                                    <el-option label="纸浆粉尘" value="纸浆粉尘"></el-option>
                                    <el-option label="纺织粉尘" value="纺织粉尘"></el-option>
                                    <el-option label="煤粉" value="煤粉"></el-option>
                                    <el-option label="静电喷涂粉尘" value="静电喷涂粉尘"></el-option>
                                    <el-option label="其他粉尘" value="其他粉尘"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8" v-if="data.is_dust_explosion=='是'">
                            <el-form-item label="涉粉作业人数" prop="dust_work_person">
                                <el-input v-model="dust.param_value" type="number" size="mini">
                                    <template slot="append">人</template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8" v-if="data.is_dust_explosion=='是' && key==0">
                            <el-form-item label-width="0">
                                <el-link @click="addDustLine" type="success" icon="el-icon-circle-plus-outline">增加粉尘分类</el-link>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8" v-if="data.is_dust_explosion=='是' && key>0">
                            <el-form-item label-width="0">
                                <el-link @click="delDustLine(dust)" type="danger" icon="el-icon-remove-outline">删除</el-link>
                            </el-form-item>
<!--                            <el-form-item label-width="0">-->
<!--                                &nbsp;-->
<!--                            </el-form-item>-->
                        </el-col>
                    </template>

                    <el-col :span="24">
                        <div style="width: 100%;border-bottom: 2px solid #dedede;margin: 10px 0px;"></div>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="是否涉氨制冷" prop="is_ammonia_cold">
                            <el-radio v-model="data.is_ammonia_cold" label="否">否</el-radio>
                            <el-radio v-model="data.is_ammonia_cold" label="是">是</el-radio>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" v-if="data.is_ammonia_cold=='是'">
                        <el-form-item label="液氨的用途" prop="ammonia_use">
                            <el-select v-model="data.ammonia_use" size="mini" placeholder="请选择">
                                <el-option label="制气" value="制气"></el-option>
                                <el-option label="调节PH值" value="调节PH值"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" v-if="data.is_ammonia_cold=='是'">
                        <el-form-item label="液氨使用量" prop="ammonia_usage">
                            <el-input v-model="data.ammonia_usage" size="mini">
                                <template slot="append">(单位：t/a)</template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" v-if="data.is_ammonia_cold=='是'">
                        <el-form-item label="液氨储存方式" prop="ammonia_storage_method">
                            <el-select v-model="data.ammonia_storage_method" size="mini" placeholder="请选择">
                                <el-option label="罐装" value="罐装"></el-option>
                                <el-option label="瓶装" value="瓶装"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" v-if="data.is_ammonia_cold=='是'">
                        <el-form-item label="液氨储存量" prop="ammonia_storage_capacity">
                            <el-input v-model="data.ammonia_storage_capacity" size="mini">
                                <template slot="append">(单位：t)</template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" v-if="data.is_ammonia_cold=='是'">
                        <el-form-item label="气体泄露报警装置数" prop="gas_alarm_number">
                            <el-input v-model="data.gas_alarm_number" size="mini"></el-input>
                        </el-form-item>
                    </el-col>


                    <el-col :span="24">
                        <div style="width: 100%;border-bottom: 2px solid #dedede;margin: 10px 0px;"></div>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="是否涉高温熔融金属" prop="is_hot_melting">
                            <el-radio v-model="data.is_hot_melting" label="否">否</el-radio>
                            <el-radio v-model="data.is_hot_melting" label="是">是</el-radio>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="data.is_hot_melting=='是'">
                        <el-form-item label="冶炼炉" prop="">
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="data.is_hot_melting=='是'">
                        <el-form-item label="熔炼炉" prop="">
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="data.is_hot_melting=='是'">
                        <el-form-item label="高炉数量" prop="blast_furnace_number">
                            <el-input v-model="data.blast_furnace_number" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="data.is_hot_melting=='是'">
                        <el-form-item label="熔炼炉" style="width: 60%;float: left;">
                            <el-input v-model="data.hot_list[0].name" disabled size="mini"></el-input>

<!--                            <el-select v-model="data.melting_furnace1" size="mini" placeholder="请选择">-->
<!--                                <el-option label="＜0.5t" value="＜0.5t"></el-option>-->
<!--                                <el-option label="0.5t≤X＜1t" value="0.5t≤X＜1t"></el-option>-->
<!--                                <el-option label="1t≤X＜3t" value="1t≤X＜3t"></el-option>-->
<!--                                <el-option label="3t≤X＜5t" value="3t≤X＜5t"></el-option>-->
<!--                                <el-option label="5t≤X＜10t" value="5t≤X＜10t"></el-option>-->
<!--                                <el-option label="10t≤X＜30t" value="10t≤X＜30t"></el-option>-->
<!--                                <el-option label="≥30t" value="≥30t"></el-option>-->
<!--                            </el-select>-->
                        </el-form-item>
                        <el-form-item label="数量" prop="melting_furnace_number1" style="width: 40%;float: left;">
                            <el-input v-model="data.hot_list[0].param_value" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="data.is_hot_melting=='是'">
                        <el-form-item label="有色金属冶炼炉数量" prop="nonferrous_furnace_number">
                            <el-input v-model="data.nonferrous_furnace_number" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="data.is_hot_melting=='是'">
                        <el-form-item label="熔炼炉" style="width: 60%;float: left;">
                            <el-input v-model="data.hot_list[1].name" disabled size="mini"></el-input>
                        </el-form-item>
                        <el-form-item label="数量" prop="melting_furnace_number2" style="width: 40%;float: left;">
                            <el-input v-model="data.hot_list[1].param_value" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="data.is_hot_melting=='是'">
                        <el-form-item label="铁合金矿热炉数量" prop="ferroalloy_furnace_number">
                            <el-input v-model="data.ferroalloy_furnace_number" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="data.is_hot_melting=='是'">
                        <el-form-item label="熔炼炉" style="width: 60%;float: left;">
                            <el-input v-model="data.hot_list[2].name" disabled size="mini"></el-input>
                        </el-form-item>
                        <el-form-item label="数量" prop="melting_furnace_number3" style="width: 40%;float: left;">
                            <el-input v-model="data.hot_list[2].param_value" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="data.is_hot_melting=='是'">
                        <el-form-item label="冲天炉数量" prop="soaring_furnace_number">
                            <el-input v-model="data.soaring_furnace_number" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="data.is_hot_melting=='是'">
                        <el-form-item label="熔炼炉" style="width: 60%;float: left;">
                            <el-input v-model="data.hot_list[3].name" disabled size="mini"></el-input>
                        </el-form-item>
                        <el-form-item label="数量" prop="melting_furnace_number4" style="width: 40%;float: left;">
                            <el-input v-model="data.hot_list[3].param_value" size="mini"></el-input>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12" v-if="data.is_hot_melting=='是'">
                        &nbsp;
                    </el-col>
                    <el-col :span="12" v-if="data.is_hot_melting=='是'">
                        <el-form-item label="熔炼炉" style="width: 60%;float: left;">
                            <el-input v-model="data.hot_list[4].name" disabled size="mini"></el-input>
                        </el-form-item>
                        <el-form-item label="数量" prop="melting_furnace_number4" style="width: 40%;float: left;">
                            <el-input v-model="data.hot_list[4].param_value" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="data.is_hot_melting=='是'">
                        &nbsp;
                    </el-col>
                    <el-col :span="12" v-if="data.is_hot_melting=='是'">
                        <el-form-item label="熔炼炉" style="width: 60%;float: left;">
                            <el-input v-model="data.hot_list[5].name" disabled size="mini"></el-input>
                        </el-form-item>
                        <el-form-item label="数量" prop="melting_furnace_number4" style="width: 40%;float: left;">
                            <el-input v-model="data.hot_list[5].param_value" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="data.is_hot_melting=='是'">
                        &nbsp;
                    </el-col>
                    <el-col :span="12" v-if="data.is_hot_melting=='是'">
                        <el-form-item label="熔炼炉" style="width: 60%;float: left;">
                            <el-input v-model="data.hot_list[6].name" disabled size="mini"></el-input>
                        </el-form-item>
                        <el-form-item label="数量" prop="melting_furnace_number4" style="width: 40%;float: left;">
                            <el-input v-model="data.hot_list[6].param_value" size="mini"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <div style="width: 100%;border-bottom: 2px solid #dedede;margin: 10px 0px;"></div>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="涉轻工行业有限空间" prop="is_light_industry">
                            <el-radio v-model="data.is_light_industry" label="否">否</el-radio>
                            <el-radio v-model="data.is_light_industry" label="是">是</el-radio>
                        </el-form-item>
                    </el-col>

                    <template v-for="(dust,key) in data.limited_list">
                        <el-col :span="8" v-if="data.is_light_industry=='是'">
                            <el-form-item label="有限空间类型" prop="dust_type">
                                <el-select v-model="dust.name" @change="chnageLimitedCheck(dust)" size="mini" placeholder="请选择">
                                    <el-option label="污水处理池" value="污水处理池"></el-option>
                                    <el-option label="沼气池" value="沼气池"></el-option>
                                    <el-option label="化粪池" value="化粪池"></el-option>
                                    <el-option label="酒糟池" value="酒糟池"></el-option>
                                    <el-option label="发酵池" value="发酵池"></el-option>
                                    <el-option label="纸浆池" value="纸浆池"></el-option>
                                    <el-option label="腌渍池" value="腌渍池"></el-option>
                                    <el-option label="锅炉" value="锅炉"></el-option>
                                    <el-option label="其他" value="其他"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8" v-if="data.is_light_industry=='是'">
                            <el-form-item label="数量" prop="dust_work_person">
                                <el-input v-model="dust.param_value" type="number" size="mini">
                                    <template slot="append">个</template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8" v-if="data.is_light_industry=='是' && key==0">
                            <el-form-item label-width="0">
                                <el-link @click="addLimitedLine" type="success" icon="el-icon-circle-plus-outline">增加</el-link>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8" v-if="data.is_light_industry=='是' && key>0">
                            <el-form-item label-width="0">
                                <el-link @click="delLimitedLine(dust)" type="danger" icon="el-icon-remove-outline">删除</el-link>
                            </el-form-item>
                            <!--                            <el-form-item label-width="0">-->
                            <!--                                &nbsp;-->
                            <!--                            </el-form-item>-->
                        </el-col>
                    </template>
                    <el-col :span="24" style="text-align: center;">
                        <el-button type="primary" size="small" @click="submit()" v-loading.fullscreen.lock="loading">提交审核</el-button>
                    </el-col>
                </el-row>
            </el-form>
        </el-container>
    </div>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            // 正则（支持座机）
            const validateTel = (rule, value, callback) => {
                const phoneReg = /^\d{3,4}-\d{7,8}(-\d{1,4})?$/;
                if (!value) return callback(new Error('座机电话号码不能为空 格如028-88888888'));
                if (!phoneReg.test(value)) return callback(new Error('格式不正确'));
                callback();
            };
            const validateEmail = (rule, value, callback) => {
                const emailReg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                if (!value) callback();
                if (!emailReg.test(value)) return callback(new Error('格式不正确'));
                callback();
            };
            return {
                title: '',
                loading: false,
                data: {
                    date:'',
                    license_start:'',
                    license_end:'',
                    enterprise_size:'',
                },
                startPickerOptions: {
                    disabledDate: (time) => {
                    if (this.data.license_end) {
                        return time.getTime() > new Date(this.data.license_end).getTime()
                    }
                    return false
                    }
                },
                endPickerOptions: {
                    disabledDate: (time) => {
                    if (this.data.license_start) {
                        return time.getTime() < new Date(this.data.license_start).getTime()
                    }
                    return false
                    }
                },
                rules: {
                    name: [{ required: true, message: '请填写企业名称', trigger: 'blur' }],
                    reg_address: [{ required: true, message: '请选择注册地址', trigger: 'change' }],
                    reg_address_info: [{ required: true, message: '请填写注册地址', trigger: 'blur' }],
                    license: [{ required: true, message: '请上传营业执照', trigger: 'change' }],
                    // aoc: [{ required: true, message: '请上传安全行政许可资料', trigger: 'blur' }],
                    operate_address: [{ required: true, message: '请选择生产经营地点', trigger: 'change' }],
                    operate_address_info: [{ required: true, message: '请填写生产经营地点', trigger: 'blur' }],
                    legal: [{ required: true, message: '请填写法定代表人', trigger: 'blur' }],
                    legal_mobile: [{ required: true, message: '请填写法人联系电话', trigger: 'blur' }],
                    // postal_code: [{ required: true, message: '请填写邮政编码', trigger: 'blur' }],
                    economy_sector: [{ required: true, message: '请选择国民经济行业', trigger: 'change' }],
                    industry: [{ required: true, message: '请选择行业/专业', trigger: 'change' }],
                    license_number: [{ required: true, message: '请填写统一社会信用代码', trigger: 'blur' }],
                    // license_start: [{ required: true, message: '请选择信用代码日期', trigger: 'change' }],
                    // license_end: [{ required: true, message: '请选择信用代码日期', trigger: 'change' }],
                    economy_type: [{ required: true, message: '请选择经济类型', trigger: 'change' }],
                    enterprise_size: [{ required: true, message: '请选择企业规模', trigger: 'change' }],
                    reg_money: [{ required: true, message: '请填写注册资本', trigger: 'blur' }],
                    manager: [{ required: true, message: '请填写安全管理联系人', trigger: 'blur' }],
                    manager_mobile: [{ required: true, message: '请填写安全管理联系人联系方式', trigger: 'blur' }],
                    manager_email: [{ required: true, message: '请填写安全管理联系人邮箱', trigger: 'blur' }],
                    date: [{ required: true, message: '请选择成立日期', trigger: 'change' }],
                    // fixed_asset: [{ required: true, message: '请填写固定资产', trigger: 'blur' }],
                    revenue: [{ required: true, message: '请填写年营业收入', trigger: 'blur' }],
                    personnel: [{ required: true, message: '请填写员工总数', trigger: 'blur' }],
                    // area: [{ required: true, message: '请填写营业场所面积', trigger: 'blur' }],
                    personnel_full: [{ required: true, message: '请填写专职安全管理人数', trigger: 'blur' }],
                    personnel_part: [{ required: true, message: '请填写兼职安全管理人数', trigger: 'blur' }],
                    personnel_special: [{ required: true, message: '请填写特种作业人数', trigger: 'blur' }],
                    business: [{ required: true, message: '请填写经营范围', trigger: 'blur' }],
                    /*phone: [
                        { required: true, message: '请输入电话号码', trigger: 'blur' },
                        { validator: validateTel, trigger: ['blur', 'change'] }
                    ],*/
                    /*fax: [
                        { required: true, message: '请输入电话号码', trigger: 'blur' },
                        { validator: validateTel, trigger: ['blur', 'change'] }
                    ],*/
                    /*manager_mobile: [
                        { required: true, message: '请输入座机电话号码', trigger: 'blur' },
                        { validator: validateTel, trigger: ['blur', 'change'] }
                    ],*/
                    /*legal_email: [
                        { validator: validateEmail, trigger: ['blur', 'change'] }
                    ],*/
                },
                config:[],
                height: document.documentElement.clientHeight,
            };
        },
        components: {
        },
        methods: {
            handleStartChange(val) {
                if (this.data.license_end && new Date(val) > new Date(this.data.license_end)) {
                    this.data.license_end = ''
                }
            },
            handleEndChange(val) {
                if (this.data.license_start && new Date(val) < new Date(this.data.license_start)) {
                    this.data.license_start = ''
                }
            },
            getCompanyInfo() {
                if(!this.data.name) return;
                that = this;
                axios.post('getBigDataCompanyInfo', {name: this.data.name})
                    .then(res => {
                        if(res.data.code === 0) {
                            that.data = {...that.data, ...res.data.data};
                        }
                    })
                    .catch(err => console.log(err));
            },
            delDustLine(currentDust){
                this.data.dust_list.splice(this.data.dust_list.indexOf(currentDust),1);
            },
            addDustLine(){
                if( this.data.dust_list.length < 10 ) {
                    this.data.dust_list.push({
                        name:'',
                        value:''
                    })
                } else{
                    this.$message.error('最多添加9条');
                }
            },
            delLimitedLine(currentDust){
                this.data.limited_list.splice(this.data.limited_list.indexOf(currentDust),1);
            },
            addLimitedLine(){
                if( this.data.limited_list.length < 10 )
                {
                    this.data.limited_list.push({
                        name:'',
                        value:''
                    })
                }
                else{
                    this.$message.error('最多添加9条');
                }
            },
            chnageDustCheck(currentDust){
                // 统计当前值出现的次数
                const count = this.data.dust_list.filter(d => d.name === currentDust.name).length;
                if (count > 1) {
                    // 提示重复
                    this.$message.warning('不能重复选择相同的粉尘种类');
                    // 清空当前选项的值
                    currentDust.name = '';
                }
            },
            chnageLimitedCheck(currentDust){
                // 统计当前值出现的次数
                const count = this.data.limited_list.filter(d => d.name === currentDust.name).length;
                if (count > 1) {
                    // 提示重复
                    this.$message.warning('不能重复选择相同的有限空间类型');
                    // 清空当前选项的值
                    currentDust.name = '';
                }
            },
            handleChange(value) {
                var data = [];
                for (var i=0;i<3;i++){
                    data.push(value[i]);
                }
                this.data.region = data;
            },
            uploadBefore(file) {
                const isJPG = file.type === 'image/jpeg';
                const isPNG = file.type === 'image/png';
                if(!isJPG&&!isPNG){
                    this.$message.error('请上传jpg图片');
                }
                return isJPG||isPNG;
            },
            uploadSuccess(res, file,fileList,field) {
                var files = [];
                console.log(res)
                for(var i in fileList){
                    files.push(fileList[i].response??fileList[i]);
                }
                this.data[field] = res.data.code;
                this.data[field+'Url'] = res.data.url;
            },
            submit: function () {
                var _this = this;
                var param = _this.data;
                this.$refs.form.validate(function (valid) {
                    if(valid){
                        _this.loading = true;
                        axios.post("companySave", param).then(function (res) {
                            _this.loading = false;
                            _this.$message({
                                message: res.data.msg,
                                type: res.data.type
                            });
                            if (res.data.code == 0) {
                                _this.visible = false;
                                location.href = 'info';
                            }
                        }).catch(function (error) {
                            _this.loading = false;
                            console.log("出现错误:",error);
                        });
                    }
                });
            },
            getInfo:function(){
                var _this = this;
                _this.loading = true;
                axios.post('getCompanyInfo', {id:'{$id}'}).then(function (res) {
                    _this.loading = false;
                    if (res.data.code == 0) {
                        _this.data = res.data.data;
                    }
                }).catch(function (error) {
                    _this.loading = false;
                    console.log("出现错误:",error);
                });
            },
            getConfig:function(){
                var _this = this;
                axios.post('getConfig', {}).then(function (res) {
                    if (res.data.code == 0) {
                        _this.config = res.data.data;
                    }
                }).catch(function (error) {
                    console.log("出现错误:",error);
                });
            },
            goBack() {
                window.history.back();
            },
        },
        mounted() {
            this.getInfo();
            this.getConfig();
        }
    })
</script>


</body>
</html>