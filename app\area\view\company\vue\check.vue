<style>
.my-label { width: 200px;}
.my-content { width: 450px;}
.margin-bottom { margin-bottom: 15px;}
.form-header { background-color: #E9F2F3; line-height: 25px; margin-bottom: 15px; padding: 5px 10px;}
.el-dialog__body { padding: 15px 20px;}
.el-tabs__content { overflow: auto;}
</style>
<template>
  <el-dialog :title="title" :close-on-click-modal="false"  modal="false" :visible.sync="visible" width="95%" top="10px"  append-to-body="true" label-position="top">
    <el-descriptions class="margin-top" title="基本信息" :column="3" border label-class-name="my-label" content-class-name="my-content">
          <el-descriptions-item label="企业名称">
            {{data.name}}
          </el-descriptions-item>
          <el-descriptions-item label="注册地址">
            {{data.mb_reg_address}}
          </el-descriptions-item>
          <el-descriptions-item label="生产经营地点">
            {{data.mb_operate_address}}
          </el-descriptions-item>
          <el-descriptions-item label="所属行政区">
            {{data.mb_region}}
          </el-descriptions-item>
          <el-descriptions-item label="营业执照">
            <el-image
                style="width: 100px; height: 100px"
                :src="data.licenseUrl"
                :preview-src-list="[data.licenseUrl]">
            </el-image>
          </el-descriptions-item>
          <el-descriptions-item label="安全行政许可资料">
            <el-image
                style="width: 100px; height: 100px"
                :src="data.aocUrl"
                :preview-src-list="[data.aocUrl]">
            </el-image>
          </el-descriptions-item>
          <el-descriptions-item label="法定代表人">
            {{data.legal}}
          </el-descriptions-item>
          <el-descriptions-item label="法人联系电话">
            {{data.legal_mobile}}
          </el-descriptions-item>
          <el-descriptions-item label="法人邮箱">
            {{data.legal_email}}
          </el-descriptions-item>
          <el-descriptions-item label="座机电话号码">
            {{data.phone}}
          </el-descriptions-item>
          <el-descriptions-item label="企业传真">
            {{data.fax}}
          </el-descriptions-item>
          <el-descriptions-item label="产业园区">
            {{data.industrial_park}}
          </el-descriptions-item>
          <el-descriptions-item label="邮政编码">
            {{data.postal_code}}
          </el-descriptions-item>
          <el-descriptions-item label="国民经济行业">
            {{data.mb_economy_sector}}
          </el-descriptions-item>
          <el-descriptions-item label="行业/专业">
            {{data.industry}}/{{data.specialty}}
          </el-descriptions-item>
          <el-descriptions-item label="统一社会信用代码">
            {{data.license_number}}
          </el-descriptions-item>
          <el-descriptions-item label="信用代码有效期">
            {{data.license_date}}
          </el-descriptions-item>
          <el-descriptions-item label="经济类型">
            {{data.mb_economy_type}}
          </el-descriptions-item>
          <el-descriptions-item label="企业规模">
            {{data.enterprise_size}}
          </el-descriptions-item>
          <el-descriptions-item label="注册资本">
            {{data.reg_money}}万元
          </el-descriptions-item>
          <el-descriptions-item label="安全管理联系人">
            {{data.manager}}
          </el-descriptions-item>
          <el-descriptions-item label="联系电话">
            {{data.manager_mobile}}
          </el-descriptions-item>
          <el-descriptions-item label="邮箱">
            {{data.manager_email}}
          </el-descriptions-item>
          <el-descriptions-item label="成立日期">
            {{data.date}}
          </el-descriptions-item>
          <el-descriptions-item label="固定资产">
            {{data.fixed_asset}}
          </el-descriptions-item>
          <el-descriptions-item label="年营业收入">
            {{data.revenue}}万元
          </el-descriptions-item>
          <el-descriptions-item label="员工总数">
            {{data.personnel}}
          </el-descriptions-item>
          <el-descriptions-item label="营业场所面积">
            {{data.area}}m³
          </el-descriptions-item>
          <el-descriptions-item label="专职安全管理人数">
            {{data.personnel_full}}
          </el-descriptions-item>
          <el-descriptions-item label="兼职安全管理人数">
            {{data.personnel_part}}
          </el-descriptions-item>
          <el-descriptions-item label="特种作业人数">
            {{data.personnel_special}}
          </el-descriptions-item>
          <el-descriptions-item label="所属集团名称">
            {{data.group_name}}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag v-if="data.status==5" type="danger">已驳回</el-tag>
            <el-tag v-if="data.status==1" type="">待审核</el-tag>
            <el-tag v-if="data.status==7" type="success">已通过</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="经营范围" :span="3">
            {{data.business}}
          </el-descriptions-item>
          <el-descriptions-item :span="3">
            <template slot="label">
              <el-tag type="info" effect="dark" style="font-size: 15px;display: block;">粉尘涉爆</el-tag>
            </template>
            {{data.is_dust_explosion}}
          </el-descriptions-item>
          <el-descriptions-item label="所属行业" v-if="data.is_dust_explosion=='是'" :span="3">
            {{data.sector}}
          </el-descriptions-item>
      <template v-for="(dust,key) in data.dust_list">
          <el-descriptions-item label="粉尘种类" v-if="data.is_dust_explosion=='是'" :span="1">
            {{dust.name}}
          </el-descriptions-item>
          <el-descriptions-item label="涉粉作业人数" v-if="data.is_dust_explosion=='是'" :span="2">
            {{dust.param_value}}
          </el-descriptions-item>
      </template>
          <el-descriptions-item :span="3">
            <template slot="label">
              <el-tag type="info" effect="dark" style="font-size: 15px;display: block;">涉氨制冷</el-tag>
            </template>
            {{data.is_ammonia_cold}}
          </el-descriptions-item>
          <el-descriptions-item label="液氨的用途" v-if="data.is_ammonia_cold=='是'">
            {{data.ammonia_use}}
          </el-descriptions-item>
          <el-descriptions-item label="液氨使用量" v-if="data.is_ammonia_cold=='是'">
            {{data.ammonia_usage}}t/a
          </el-descriptions-item>
          <el-descriptions-item label="液氨储存方式" v-if="data.is_ammonia_cold=='是'">
            {{data.ammonia_storage_method}}
          </el-descriptions-item>
          <el-descriptions-item label="液氨储存量" v-if="data.is_ammonia_cold=='是'">
            {{data.ammonia_storage_capacity}}t
          </el-descriptions-item>
          <el-descriptions-item label="气体泄露报警装置数" :span="2" v-if="data.is_ammonia_cold=='是'">
            {{data.gas_alarm_number}}
          </el-descriptions-item>
          <el-descriptions-item :span="3">
            <template slot="label">
              <el-tag type="info" effect="dark" style="font-size: 15px;display: block;">涉高温熔融金属</el-tag>
            </template>
            {{data.is_hot_melting}}
          </el-descriptions-item>
          <el-descriptions-item label="高炉数量" :span="1" v-if="data.is_hot_melting=='是'">
            {{data.blast_furnace_number}}
          </el-descriptions-item>
          <el-descriptions-item label="熔炼炉" :span="2" v-if="data.is_hot_melting=='是'">
            <span style="width: 120px;display: inline-block">{{data.hot_list[0].name}}</span> 数量: {{data.hot_list[0].param_value}}
          </el-descriptions-item>
          <el-descriptions-item label="有色金属冶炼炉数量" :span="1" v-if="data.is_hot_melting=='是'">
            {{data.nonferrous_furnace_number}}
          </el-descriptions-item>
          <el-descriptions-item label="熔炼炉" :span="2" v-if="data.is_hot_melting=='是'">
            <span style="width: 120px;display: inline-block">{{data.hot_list[1].name}}</span> 数量: {{data.hot_list[1].param_value}}
          </el-descriptions-item>
          <el-descriptions-item label="铁合金矿热炉数量" :span="1" v-if="data.is_hot_melting=='是'">
            {{data.ferroalloy_furnace_number}}
          </el-descriptions-item>
          <el-descriptions-item label="熔炼炉" :span="2" v-if="data.is_hot_melting=='是'">
            <span style="width: 120px;display: inline-block">{{data.hot_list[2].name}}</span> 数量: {{data.hot_list[2].param_value}}
          </el-descriptions-item>
          <el-descriptions-item label="冲天炉数量" :span="1" v-if="data.is_hot_melting=='是'">
            {{data.soaring_furnace_number}}
          </el-descriptions-item>
          <el-descriptions-item label="熔炼炉" :span="2" v-if="data.is_hot_melting=='是'">
            <span style="width: 120px;display: inline-block">{{data.hot_list[3].name}}</span> 数量: {{data.hot_list[3].param_value}}
          </el-descriptions-item>
          <el-descriptions-item label="" :span="1" v-if="data.is_hot_melting=='是'">
          </el-descriptions-item>
          <el-descriptions-item label="熔炼炉" :span="2" v-if="data.is_hot_melting=='是'">
            <span style="width: 120px;display: inline-block">{{data.hot_list[4].name}}</span> 数量: {{data.hot_list[4].param_value}}
          </el-descriptions-item>
          <el-descriptions-item label="" :span="1" v-if="data.is_hot_melting=='是'">
          </el-descriptions-item>
          <el-descriptions-item label="熔炼炉" :span="2" v-if="data.is_hot_melting=='是'">
            <span style="width: 120px;display: inline-block">{{data.hot_list[5].name}}</span> 数量: {{data.hot_list[5].param_value}}
          </el-descriptions-item>
          <el-descriptions-item label="" :span="1" v-if="data.is_hot_melting=='是'">
          </el-descriptions-item>
          <el-descriptions-item label="熔炼炉" :span="2" v-if="data.is_hot_melting=='是'">
            <span style="width: 120px;display: inline-block">{{data.hot_list[6].name}}</span> 数量: {{data.hot_list[6].param_value}}
          </el-descriptions-item>
          <el-descriptions-item :span="3">
            <template slot="label">
              <el-tag type="info" effect="dark" style="font-size: 15px;display: block;">涉轻工行业有限空间</el-tag>
            </template>
            {{data.is_light_industry}}
          </el-descriptions-item>
      <template v-for="(dust,key) in data.limited_list">
        <el-descriptions-item label="有限空间类型" v-if="data.is_light_industry=='是'" :span="2">
          {{dust.name}}
        </el-descriptions-item>
        <el-descriptions-item label="数量" v-if="data.is_light_industry=='是'" :span="1">
          {{dust.param_value}}
        </el-descriptions-item>
      </template>
          <el-descriptions-item v-if="data.check_user_name" label="审核人" >
            {{data.check_user_name}}
          </el-descriptions-item>
          <el-descriptions-item v-if="data.check_time" label="审核时间">
            {{data.check_time.substr(0,19)}}
          </el-descriptions-item>
          <el-descriptions-item v-if="data.reason" label="驳回原因" >
            {{data.reason}}
          </el-descriptions-item>
        </el-descriptions>
    <div style="padding:20px;text-align:center;">
      <el-button v-if="showBtn" type="primary" @click="submit(7)" size="small">审核通过</el-button>
      <el-button v-if="showBtn" type="danger" @click="submit(5)" size="small">驳回</el-button>
      <el-button @click="visible = false" size="small">关闭</el-button>
    </div>
    <!--编辑联系人-->
  </el-dialog>
</template>
<script>
module.exports = {
  name: "add",
  // 模板导入区
  components: {
  },
  data:function() {
    return {
      id:0,
      isAdmin:false,
      visible: false,
      dialogFormVisible: false,
      title: '详情',
      loading: false,
      noMore: false,
      user_id: 0,
      data: {
      },
      is_see:0,
      ca:[],
      details:[],
      cards:{},
      type:{},
      pcas: [],
      showBtn:true,
      restaurants:[],
      restaurants2:[],
      height: document.documentElement.clientHeight - 250,
    }
  },
  computed: {
    disabled () {
      return this.loading || this.noMore
    }
  },
  mounted: function(){
    //this.getConfig();
  },
  created:function(){
  },
  methods: {
    /**
     * 打开弹窗调用方法
     * */
    open: function (row,showBtn) {
      var _this =this;
      _this.id = row.id;
      _this.visible = true;
      _this.noMore = false;
      _this.showBtn = showBtn;
      _this.getInfo(row.id);
    },
    closeDialog: function () {
      this.visible = false;
    },
    submit:function(status){
      var _this = this;
      if(status==5){
        _this.$prompt('请填写驳回原因', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /^\S+$/,
          inputErrorMessage: '请填写驳回原因'
        }).then(({ value }) => {
          axios.post("companyApplyCheck", {id:_this.id,status:status,reason:value}).then(function (res) {
            _this.$message({
              message: res.data.msg,
              type: res.data.type
            });
            if (res.data.code == 0) {
                _this.visible = false;
              window.parent.getMessage();
                _this.$emit("ok");
            }
          }).catch(function (error) {
            console.log("出现错误:",error);
          });
        }).catch(() => {
        });
      }else if(status==7){
        _this.$confirm('确认审核通过？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        }).then(() => {
          axios.post("companyApplyCheck", {id:_this.id,status:status}).then(function (res) {
            _this.$message({
              message: res.data.msg,
              type: res.data.type
            });
            if (res.data.code == 0) {
              _this.visible = false;
              window.parent.getMessage();
              _this.$emit("ok");
            }
          }).catch(function (error) {
            console.log("出现错误:",error);
          });
        }).catch(() => {
        });
      }
    },
    getInfo:function(id){
      var _this = this;
      if(id){
        axios.post("companyApplyInfo", {
          id:id
        }).then(function (res) {
          if (res.data.code == 0) {
            _this.data=res.data.data;
          }else {
            _this.$message({
              message: res.data.msg,
              type: res.data.type
            });
          }
        }).catch(function (error) {
          console.log("出现错误:",error);
        });
      }
    },
    addContacts: function(){
      this.form = {};
      this.dialogFormVisible = true;
    },
    editContacts: function(row){
      this.form = row;
      this.dialogFormVisible = true;
    },
  }
}
</script>


