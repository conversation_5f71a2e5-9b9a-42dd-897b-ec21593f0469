<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>评审任务</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/company.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .my-content p{ line-height:20px; margin: 10px 0;}
        .el-form-item--mini.el-form-item, .el-form-item--small.el-form-item{margin-bottom: 0;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">
        <el-form :inline="true" :model="searchFrom" class="form-inline" @submit.native.prevent>
            <el-form-item>
                <el-radio-group @change="statusChange" size="mini" v-model="searchFrom.status">
                    <el-radio-button label="">全部</el-radio-button>
                    <el-radio-button label="1">待分配</el-radio-button>
                    <el-radio-button label="7">待评审</el-radio-button>
                    <el-radio-button label="8">评审中</el-radio-button>
                    <el-radio-button label="9">已完成</el-radio-button>
                </el-radio-group>
            </el-form-item>
            <el-form-item>
                <el-input v-model="searchFrom.title" size="mini" placeholder="企业名称"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="getData()" size="mini">查询</el-button>
                <el-button @click="reset()" size="mini">重置</el-button>
                <el-button type="primary" @click="getData()" size="mini">任务分配</el-button>
            </el-form-item>
        </el-form>
        <el-table border
                  v-loading="loading"
                  :data="data"
                  style="width: 100%;margin-bottom: 20px;"
                  ref="qtable"
                  :height="height"
                  size="small">
            <el-table-column
                    type="selection"
                    :selectable="selectHandle"
                    width="55">
            </el-table-column>
            <el-table-column
                    type="index"
                    label="序号"
                    align="center"
                    width="50">
            </el-table-column>
            <el-table-column
                    prop="code"
                    label="编号"
                    align="center"
                    width="150">
            </el-table-column>
            <el-table-column
                    prop="company_name"
                    label="企业名称"
                    align="center"
                    show-overflow-tooltip
                    min-width="200">
                <template slot-scope="scope">
                    <!--<el-link :href="'/general/toppingsoft/index.php/org/company/info?id='+scope.row.company_id" target="_blank" type="primary" v-html="scope.row.company_name"></el-link>-->
                    <el-link @click="company_info(scope.row)" type="primary" v-html="scope.row.company_name"></el-link>
                </template>
            </el-table-column>
            <el-table-column
                    prop="level"
                    label="申请等级"
                    align="center"
                    width="100">
            </el-table-column>
            <el-table-column
                    prop="date"
                    label="评审日期"
                    align="center"
                    width="100">
            </el-table-column>
            <el-table-column
                    prop="mb_status"
                    label="任务状态"
                    align="center"
                    show-overflow-tooltip
                    width="120">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.status==-1" type="">已取消</el-tag>
                    <el-tag v-if="scope.row.status==0" type="info">待接收</el-tag>
                    <el-tag v-if="scope.row.status==1" type="info">已接收</el-tag>
					<el-tag v-if="scope.row.status==2" type="info">已推送</el-tag>
					<el-tag v-if="scope.row.status==3" type="info">待确认</el-tag>
                    <el-tag v-if="scope.row.status==7" type="primary">待评审</el-tag>
                    <el-tag v-if="scope.row.status==5" type="danger">已驳回</el-tag>
                    <el-tag v-if="scope.row.status==8" type="">评审中</el-tag>
                    <el-tag v-if="scope.row.status==9" type="">评审结束</el-tag>
                    <el-tag v-if="scope.row.status==10" type="">任务结束</el-tag>
                </template>
            </el-table-column>
            <el-table-column
                    prop="expert_names"
                    label="评审人员"
                    align="center"
                    show-overflow-tooltip
                    min-width="120">
            </el-table-column>
            <el-table-column
                    label="操作"
                    align="center"
                    width="200">
                <template slot-scope="scope">
                    <el-button v-if="scope.row.status==0" size="small" @click="update(scope.row,1)" type="primary">接收</el-button>
                    <el-button v-if="scope.row.status==1 || scope.row.status==-1 " size="small" @click="expert(scope.row)" type="primary">任务派发</el-button>
                    <el-button v-if="scope.row.status==2 || scope.row.status==7" size="small" @click="cancelTask(scope.row)" type="primary">取消任务</el-button>
					<el-button v-if="scope.row.status==3" size="small" @click="confirmLeader(scope.row)" type="primary">确认组长</el-button>
                    <el-button v-if="scope.row.status>=8" size="small" @click="info(scope.row)">详情</el-button>
                    <el-button v-if="scope.row.status==9" size="small" @click="finishTask(scope.row)">结束任务</el-button>
<!--                    <el-button v-if="scope.row.status==7" size="small" @click="info(scope.row)" type="warning">专家变更</el-button>-->
                </template>
            </el-table-column>
        </el-table>
    </div>
    <elementref ref="elementref" @ok="getData()"></elementref>
	<confirmleader ref="confirmleader" @ok="getData()"></confirmleader>
    <el-dialog title="任务派发" width="500px" :visible.sync="visible">
        <el-form :model="form" size="small">
            <el-descriptions class="margin-top" title="" :column="1" size="small" border :label-style="{width:'120px'}">
                <el-descriptions-item label="企业名称">
                    <el-form-item label="">
                        <el-input v-model="form.company_name" ></el-input>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label="评审日期">
                    <el-form-item label="">
                        <el-date-picker
                                v-model="form.date"
                                style="width: 100%;"
                                type="date"
                                placeholder="选择日期"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd">
                        </el-date-picker>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label="专家数量">
                    <el-form-item label="">
                        <el-input v-model="form.number"><template slot="append">人</template></el-input>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label="市级以上专家数量">
                    <el-form-item label="">
                        <el-input v-model="form.number1"><template slot="append">人</template></el-input>
                    </el-form-item>
                </el-descriptions-item>

                <el-descriptions-item label="指导机构名称">
                    <el-form-item label="">
                        <el-input v-model="form.directOrgName"></el-input>
                    </el-form-item>
                </el-descriptions-item>				
				<el-descriptions-item label="专家类别">
                <el-form-item label="">
                    <el-select v-model="form.expert_type" placeholder="请选择专家类别" style="width:100%">
                        <el-option
                            v-for="item in expertTypes"
                            :key="item.categoryId"
                            :label="item.categoryName"
                            :value="item.categoryId">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-descriptions-item>
			</el-descriptions>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="visible = false">取消</el-button>
            <el-button type="primary" @click="expertSubmit">提交</el-button>
        </div>
    </el-dialog>
    <el-dialog :visible.sync="scoreDialogVisible" title="专家打分" width="600px" @close="handleScoreClose" class="expert-score">
        <el-form :model="scoreForm" label-width="120px">
        <div v-for="(expert, idx) in scoreExperts" :key="expert.id" style="margin-bottom: 20px; border-bottom: 1px solid #f0f0f0; padding-bottom: 10px;">
        <div style="font-weight:bold; margin-bottom: 10px;">{{ expert.name || '专家' + (idx + 1) }}</div>
        <el-row :gutter="10">
            <el-col :span="12">
            <el-form-item label="专业知识">
                <el-rate v-model="scoreForm[idx].knowledge" :max="5" show-score></el-rate>
            </el-form-item>
            </el-col>
            <el-col :span="12">
            <el-form-item label="业务能力">
                <el-rate v-model="scoreForm[idx].ability" :max="5" show-score></el-rate>
            </el-form-item>
            </el-col>
        </el-row>
        <el-row :gutter="10">
            <el-col :span="12">
            <el-form-item label="工作态度">
                <el-rate v-model="scoreForm[idx].attitude" :max="5" show-score></el-rate>
            </el-form-item>
            </el-col>
            <el-col :span="12">
            <el-form-item label="廉政意识">
                <el-rate v-model="scoreForm[idx].integrity" :max="5" show-score></el-rate>
            </el-form-item>
            </el-col>
        </el-row>
        </div>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="handleScoreClose">取消</el-button>
            <el-button type="primary" @click="handleScoreSubmit">打分并确认结束评审</el-button>
        </div>
    </el-dialog>

    <company_info ref="company_info" @ok="getData()"></company_info>
</div>

<style scoped>.expert-score .el-form-item__label{line-height: 20px;}</style>
<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                title: '新增',
                searchFrom: {
                    status: 1,
                    title: '',
                },
                form: {
                    status: 1,
                    title: '',
                    directOrgName:'',
                    expert_type:''
                },
                expertTypes: [],
                data: [],
                visible: false,
                loading: false,
                height: document.documentElement.clientHeight - 155,
                scoreDialogVisible: false,
                scoreForm: [],
                scoreExperts: [],
                scoreTaskId: null,
            };
        },
        components: {
            'elementref': 'url:/general/toppingsoft/app/expert/view/task/vue/element.vue?v=1',
            'confirmleader': 'url:/general/toppingsoft/app/org/view/tasks/vue/confirmLeader.vue?v=1',
            'company_info': 'url:/general/toppingsoft/app/org/view/company/vue/info.vue?v=1',
        },
        watch: {
            $route() {
                //切换标签时
                this.handleRefreshTable();
            }
        },
        created() {
            let _this = this;
            //窗口改变时
            window.onresize = function () {
                _this.handleRefreshTable();
            };
        },
        updated() {
            //数据改变时
            this.handleRefreshTable();
        },
        methods: {
            // 新增获取专家类别方法
            getExpertTypes() {
                var _this = this;
                axios.post('getExpertTypes').then(function(res) {
                    if(res.data.code == 0) {
                        _this.expertTypes = res.data.data;
                    }
                }).catch(function(error) {
                    console.log(error);
                });
            },
            //取消任务
            cancelTask(row){
                var _this = this;
                var param = {};
                param.id = row.id;
                _this.$confirm('确认取消任务？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    _this.loading = true;
                    axios.post('cancelTask', param).then(function (res) {
                        _this.loading = false;
                        if (res.data.code == 0) {
                            _this.$message({
                                message: res.data.msg,
                                type: "success"
                            });
                            _this.getData();
                        } else {
                            _this.$message({
                                message: res.data.msg,
                                type: "error"
                            });
                        }
                    })
                })
            },
            //结束任务,弹出专家打分对话框
            finishTask(row){
                var _this = this;
                _this.scoreTaskId = row.id;
                _this.scoreDialogVisible = true;
                _this.scoreForm = _this.scoreExperts.map(expert => ({
                    id: expert.id,
                    name:expert.name,
                    position:expert.position,
                    knowledge: 5,
                    ability: 5,
                    attitude: 5,
                    integrity: 5
                }));
                // 假设 row.experts 为专家数组，若无则需请求接口获取
                if(row.experts && row.experts.length > 0){
                    _this.scoreExperts = row.experts.map(e => ({id: e.expert_id, name: e.expert_name,position:e.position}));
                    _this.scoreDialogVisible = true;
                    _this.scoreForm = _this.scoreExperts.map(expert => ({
                        id: expert.id,
                        name:expert.name,
                        position:expert.position,
                        knowledge: 5,
                        ability: 5,
                        attitude: 5,
                        integrity: 5
                    }));
                }else{
                    // 若没有专家列表，需请求接口获取
                    _this.loading = true;
                    axios.post('getTaskExperts', {id: row.id}).then(function(res){
                        _this.loading = false;
                        if(res.data.code == 0){
                            _this.scoreExperts = res.data.data.map(e => ({id: e.expert_id, name: e.expert_name,position:e.position}));
                            _this.scoreDialogVisible = true;
                            _this.scoreForm = _this.scoreExperts.map(expert => ({
                                id: expert.id,
                                name:expert.name,
                                position:expert.position,
                                knowledge: 5,
                                ability: 5,
                                attitude: 5,
                                integrity: 5
                            }));
                        }else{
                            _this.$message({message: res.data.msg, type: 'error'});
                        }
                    }).catch(function(error){
                        _this.loading = false;
                        console.log(error);
                    });
                }
            },
            //确认组长
            confirmLeader(row){
                    this.$refs.confirmleader.open(row);	
            },
            // 专家打分提交
            handleScoreSubmit(){
                var _this = this;
                _this.loading = true;
                axios.post('submitExpertScores', {task_id: _this.scoreTaskId, scores: _this.scoreForm}).then(function(res){
                    _this.loading = false;
                    if(res.data.code == 0){
                        _this.$message({message: '打分成功！', type: 'success'});
                        _this.getData();
                        _this.handleScoreClose();
                    }else{
                        _this.$message({message: res.data.msg, type: 'error'});
                    }
                }).catch(function(error){
                    _this.loading = false;
                    console.log(error);
                });
            },
            handleScoreClose() {
                this.scoreDialogVisible = false;
                this.scoreForm = [];
                this.scoreExperts = [];
                this.scoreTaskId = null;
            },
            selectHandle(row,index) {
                return row.status==1||row.status==0;
            },
            statusChange() {
                this.page = 1;
                this.getData();
            },
            reset(){
                this.page = 1;
                this.searchFrom.title="";
                this.getData();
            },
            //数据加载
            getData() {
                var _this = this;
                var param = _this.searchFrom;
                param._ajax = 1;
                _this.loading = true;
                axios.post('', param).then(function (res) {
                    if (res.data.code == 0) {
                        _this.data = res.data.data.data;
                        _this.$nextTick(() => {
                            // 这里的代码会在DOM更新完成后执行
                            _this.loading = false;
                        });
                        _this.handleRefreshTable();
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            update(row,status){
                var _this = this;
                var param = {};
                var msg = '';
                param.id = row.id;
                param.status = status;
                if(status==1){
                    msg = '确认接收任务？';
                }
                _this.$confirm(msg, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    _this.loading = true;
                    axios.post('update', param).then(function (res) {
                        _this.loading = false;
                        if (res.data.code == 0) {
                            _this.$message({
                                message: res.data.msg,
                                type: "success"
                            });
                            _this.getData();
                        } else {
                            _this.$message({
                                message: res.data.msg,
                                type: "error"
                            });
                        }
                    }).catch(function (error) {
                        console.log(error);
                    });
                });
            },
            expertSubmit(){
                var _this = this;
                var param = _this.form;
                _this.loading = true;
                axios.post('expertSubmit', param).then(function (res) {
                    _this.loading = false;
                    if (res.data.code == 0) {
                        _this.$message({
                            message: '推送成功！',
                            type: "success"
                        });
						_this.visible  = false;
                        _this.getData();
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            elementSet(row) {
                this.$refs.elementref.title="要素分配";
                this.$refs.elementref.open(row);
            },
            review(row){
                location.href = 'review?id='+row.id;
            },
            info(row){
                location.href = 'info?id='+row.id;
            },
            company_info (row) {
                var tmp = row;
                tmp.id = row.company_id;
                this.$refs.company_info.open(tmp);
            },
            expert(row){
                var _this = this;
                _this.form = row;
                _this.visible = true;
            },
            handleRefreshTable: function () {
                this.$nextTick(function () {
                    var refList = this.$refs;
                    if (refList) {
                        for (var i of Object.keys(refList)) {
                            if (refList[i] && refList[i].doLayout) {
                                refList[i].doLayout();
                            }
                        }
                    }
                });
            },
        },
        mounted() {
            //获取列表
            this.getData();
            // 获取专家类别
            this.getExpertTypes();

        }
    })
</script>


</body>
</html>
</body>
</html>