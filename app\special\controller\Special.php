<?php

namespace app\special\controller;

use app\Base;
use app\BaseController;
use app\ToExcel;
use think\Exception;
use think\facade\Db;
use think\facade\View;
use think\Request;

//use think\View;

class Special extends BaseController
{

    protected $type = 'file';
    protected static $dc_table = 'dingding_app';

    public function index()
    {

//        $is_admin = false;
        View::assign('isadmin', '1');
        return View::fetch('dataList');
    }


    /**
     * @return \think\response\Json
     * 上传模板文件
     *
     */
    public function uploadFile()
    {
        $request = \think\facade\Request::instance();
        $post = $request->post();

        //返回值

        try {
            Db::startTrans();
            // 获取表单上传文件 例如上传了001.jpg
            $file = request()->file('file');

            //保存到本地
            $savename = \think\facade\Filesystem::disk('public')->putFile('special', $file, 'uniqid');
            $savename = str_replace("\\", "/", $savename);
//            dd($savename);


            $data = ToExcel::getData($file);
            if ($data['code'] != 200) {
                throw new Exception('文件格式错误');
            }
            $data = $data['data'];
            $header = $data[1];


            unset($data[1]);
            $header_out = [];
            foreach ($header as $key => $value) {
                $header_out[] = [
                    'key' => $key,
                    'field_name' => $value,
                    'field' => '',
                    'dc_field' => '',
                    'dc_field_name' => '',
                    'field_type' => 'text',
                    'is_dc' => false,
                    'width' => 100,
                    'list_is_show' => true,
                    'is_search' => true,
                ];
            }


            $specialOption = $this->getTableFieldsAndAlias(self::$dc_table);
            return json([
                'code' => 1000,
                'msg' => '上传成功',
                'data' => [
                    'header' => $header_out,
                    'data' => $data,
                    'specialOption' => $specialOption,
                    'file_path' => $savename,
                ],
            ]);
        } catch (\Exception $e) {
            //如获取到异常信息，对所有表的删、改、写操作，都会回滚至操作前的状态：
            Db::rollback();
            $rdata = [
                'code' => $e->getCode(),
                'msg' => $e->getMessage(),
            ];
            return json($rdata);
        }
    }

    /**
     * @return \think\response\Json
     *
     * 导入专项数据
     */
    public function upSpecialData(Request $request)
    {

        $table_order_id = $request->param('table_order_id', '');
        $dc_special_table_name = Db::table("hgy_special_db_table_order")->where('id', $table_order_id)->value('dc_special_table_name');

        try {
            Db::startTrans();
            // 获取表单上传文件 例如上传了001.jpg
            $file = request()->file('file');
            $data = ToExcel::getData($file);
            if ($data['code'] != 200) {
                throw new Exception('文件格式错误');
            }
            $data = $data['data'];
            $header_count = count($data[1]);
            $c_count = Db::table('hgy_special_db_table_field_config')
                ->where('table_order_id', $table_order_id)
                ->count();


            if ($c_count != $header_count) {
                throw new Exception('导入的模板有误请下载正确的模板');
            }

            unset($data[1]);
            foreach ($data as $value) {
                $insertData = [];
                foreach ($value as $key => $item) {
                    $field = Db::table('hgy_special_db_table_field_config')
                        ->where('table_order_id', $table_order_id)
                        ->where('key', $key)
                        ->value('field');
                    $insertData[$field] = $item;
                }
                Db::table($dc_special_table_name)->insert($insertData);

            }
            Db::commit();
            return json(['code' => 1000, 'msg' => '导入成功',]);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => $e->getCode(), 'msg' => $e->getMessage()]);
        }
    }

    /**
     * @param Request $request
     * @return \think\response\Json
     *
     * 获取一个详情信息
     */

    public function getOneTemplateData(Request $request)
    {

        $template_id = $request->param('template_id', '');

        try {

            $header_out = Db::table("hgy_special_db_table_field_config")
                ->where('table_order_id', $template_id)
                ->select()
                ->each(function ($model) {
                    $model['is_dc'] = $model['is_dc'] == "1" ? true : false;
                    $model['list_is_show'] = $model['list_is_show'] == "1" ? true : false;
                    $model['is_search'] = $model['is_search'] == "1" ? true : false;
                    return $model;

                });
            $specialOption = $this->getTableFieldsAndAlias(self::$dc_table);
            $model = Db::table("hgy_special_db_table_order")->where('id', $template_id)->find();

            $authority = [];
            $authority['manger_user_name'] = $model['manger_user_name'];
            $authority['manger_user_id'] = $model['manger_user_id'];
            $authority['look_user_name'] = $model['look_user_name'];
            $authority['look_user_id'] = $model['look_user_id'];
            return json([
                'code' => 1000,
                'msg' => '上传成功',
                'data' => [
                    'authority' => $authority,
                    'header' => $header_out,
                    'specialOption' => $specialOption,
                    'dc_special_title' => $model['dc_special_title'],
                    'dc_remark' => $model['dc_remark'],
                    'file_path' => $model['file_path'],
                ],
            ]);
            return json(['code' => 1000, 'msg' => '导入成功',]);
        } catch (\Exception $e) {
            return json(['code' => $e->getCode(), 'msg' => $e->getMessage()]);
        }
    }

    /**
     * @return \think\response\Json
     * 创建模板
     */
    public function createOneTemplate(Request $request)
    {
        try {
            $dc_special_title = $request->param('dc_special_title', '');
            $remark = $request->param('dc_remark', '');
            $header = $request->param('header', []);
            $data = $request->param('data', []);
            $file_path = $request->param('file_path', '');
            Db::startTrans();
            $id = Db::table("hgy_special_db_table_order")
                ->insertGetId([
                    'dc_special_title' => $dc_special_title,
                    'dc_remark' => $remark,
                    'file_path' => $file_path,
                    'dc_create_user_id' => $_SESSION['LOGIN_USER_ID'],
                    'dc_create_user_name' => $_SESSION['LOGIN_USER_NAME'],
                    'dc_created_at' => date('Y-m-d H:i:s'),
                    'dc_updated_at' => date('Y-m-d H:i:s'),
                ]);

            $count = Db::table("hgy_special_db_table_order")
                ->where('dc_special_title', $dc_special_title)->count();
            if ($count > 1) {
                throw new Exception($dc_special_title . '模板已经存在');
            }
            $dc_special_table_name = "hgy_special_data_" . $id;
            Db::table("hgy_special_db_table_order")
                ->where('id', $id)
                ->update(['dc_special_table_name' => $dc_special_table_name]);


            //存储
            $index = 1;
            foreach ($header as $item) {
                Db::table("hgy_special_db_table_field_config")
                    ->insert([
                        'table_order_id' => $id,
                        'dc_field' => $item['dc_field'],
                        'dc_field_name' => $item['dc_field_name'],
                        'field' => 'dc_special_' . $index,
                        'field_name' => $item['field_name'],
                        'is_dc' => $item['is_dc'],
                        'key' => $item['key'],
                        'list_is_show' => $item['list_is_show'],
                        'width' => $item['width'],
                        'is_search' => $item['is_search'],
                        'field_type' => $item['field_type'],
                    ]);
                $index++;
            }
            //创建数据表
            $sql = "CREATE TABLE `" . $dc_special_table_name . "` ( `id` int(11) NOT NULL AUTO_INCREMENT,";
            Db::table("hgy_special_db_table_field_config")
                ->where('table_order_id', $id)
                ->select()->each(function ($model) use (&$sql) {
                    $sql = $sql . "`" . $model['field'] . "` TEXT  COMMENT '" . $model['field_name'] . "', ";
                });
            $sql = $sql . " PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

            Db::execute($sql);
            foreach ($data as $value) {
                $insertData = [];
                foreach ($value as $key => $item) {
                    $field = Db::table('hgy_special_db_table_field_config')
                        ->where('table_order_id', $id)
                        ->where('key', $key)
                        ->value('field');
                    $insertData[$field] = $item;
                }
                Db::table($dc_special_table_name)->insert($insertData);

            }

            Db::commit();
            return json(['code' => 1000, 'data' => $id, 'msg' => '模板创建成功', 'type' => 'success']);
        } catch (\Exception $e) {
            Db::rollback();
            $rdata = [
                'code' => 1001,
                'data' => [],
                'is_super_man' => false,
                'msg' => $e->getMessage(),
                'type' => 'error'
            ];
            return json($rdata);
        }
    }

    public function changeOneTemplate(Request $request)
    {
        try {
            $template_id = $request->param('template_id', 0);
            $dc_special_title = $request->param('dc_special_title', '');
            $remark = $request->param('dc_reamrk', '');
            $header = $request->param('header', []);
//            $file_path = $request->param('file_path', '');
            Db::startTrans();
            $id = Db::table("hgy_special_db_table_order")
                ->where('id', $template_id)
                ->update([
                    'dc_special_title' => $dc_special_title,
                    'dc_remark' => $remark,
                    'dc_updated_at' => date('Y-m-d H:i:s'),
                ]);
            $count = Db::table("hgy_special_db_table_order")
                ->where('dc_special_title', $dc_special_title)->count();
            if ($count > 1) {
                throw new Exception($dc_special_title . '模板已经存在');
            }
            $dc_special_table_name = "hgy_special_data_" . $id;
            Db::table("hgy_special_db_table_order")
                ->where('id', $id)
                ->update(['dc_special_table_name' => $dc_special_table_name]);
            //修改
            foreach ($header as $item) {
                Db::table("hgy_special_db_table_field_config")
                    ->where('id', $item['id'])
                    ->update([
                        'dc_field' => $item['dc_field'],
                        'dc_field_name' => $item['dc_field_name'],
                        'field_name' => $item['field_name'],
                        'is_dc' => $item['is_dc'],
                        'key' => $item['key'],
                        'list_is_show' => $item['list_is_show'],
                        'width' => $item['width'],
                        'is_search' => $item['is_search'],
                        'field_type' => $item['field_type'],
                    ]);
            }
            Db::commit();
            return json(['code' => 1000, 'msg' => '模板修改成功', 'type' => 'success']);
        } catch (\Exception $e) {
            Db::rollback();
            $rdata = [
                'code' => 1001,
                'data' => [],
                'is_super_man' => false,
                'msg' => $e->getMessage(),
                'type' => 'error'

            ];
            return json($rdata);
        }
    }

    public function deleteOnemplate(Request $request)
    {
        try {
            $template_id = $request->param('template_id', 0);

            Db::startTrans();
            $id = Db::table("hgy_special_db_table_order")
                ->where('id', $template_id)
                ->update(['dc_deleted_at' => date('Y-m-d H:i:s')]);


            Db::commit();
            return json(['code' => 1000, 'msg' => '模板删除成功', 'type' => 'success']);
        } catch (\Exception $e) {
            Db::rollback();
            $rdata = [
                'code' => 1001,
                'data' => [],
                'is_super_man' => false,
                'msg' => $e->getMessage(),
                'type' => 'error'

            ];
            return json($rdata);
        }
    }

    public function addOneAuthority(Request $request)
    {
        try {
            $template_id = $request->param('template_id', 0);
            $data = $request->param('data', []);


            Db::startTrans();
            $data['dc_updated_at'] = date('Y-m-d H:i:s');
            Db::table("hgy_special_db_table_order")
                ->where('id', $template_id)
                ->update($data);
            Db::commit();
            return json(['code' => 1000, 'msg' => '权限设置成功', 'type' => 'success']);
        } catch (\Exception $e) {
            Db::rollback();
            $rdata = [
                'code' => 1001,
                'data' => [],
                'is_super_man' => false,
                'msg' => $e->getMessage(),
                'type' => 'error'

            ];
            return json($rdata);
        }
    }


    /**
     * @return \think\response\Json
     *
     * 获取专项类型列表
     */
    public function getSpecialTypeList()
    {
        try {
            $data = Db::table("hgy_special_db_table_order")
                ->alias('o')
                ->field(['o.dc_special_title', 'o.id'])
                ->whereOr(function ($query) {
//                    $query->whereOr();
//                    $query->whereOr();
                })
                ->whereNull('dc_deleted_at')
                ->select()
                ->each(function ($item) {
                    $item['disabled'] = false;
                    return $item;
                });
            return json(['data' => $data, 'code' => 1000, 'msg' => '获取成功']);
        } catch (\Exception $e) {
            $rdata = [
                'code' => 1001,
                'data' => [],
                'is_super_man' => false,
                'msg' => $e->getMessage(),
            ];
            return json($rdata);
        }
    }

    /**
     * @return \think\response\Json
     *
     * 获取字段类型
     */
    public function getSpecialFiledTypeList()
    {
        try {
            $data = Db::table("hgy_special_filed_type")
                ->alias('t')
                ->field(['t.field_type', 't.type_name'])
                ->select();
            return json(['data' => $data, 'code' => 1000, 'msg' => '获取成功']);
        } catch (\Exception $e) {
            $rdata = [
                'code' => 1001,
                'data' => [],
                'is_super_man' => false,
                'msg' => $e->getMessage(),
            ];
            return json($rdata);
        }
    }


    public function getSpecialDataList(Request $request)
    {
        try {
            $searchData = $request->param('searchData', '');
            $pageSize = $request->param('limit', 10);
            $key_exists = key_exists('table_order_id', $searchData);
            if (!$key_exists) {
                throw new Exception("请选择专项类型", 1001);
            }

            $table_order_id = $searchData['table_order_id'];
            $dc_special_table_name = Db::table("hgy_special_db_table_order")->where('id', $table_order_id)->value('dc_special_table_name');

            $db = Db::table($dc_special_table_name)
                ->alias('r')
                ->field('r.*');

            foreach ($searchData as $key => $value) {
                if (str_ends_with($key, '_name')) {

                    continue;
                }

                $model = Db::table("hgy_special_db_table_field_config")
                    ->where('table_order_id', $table_order_id)
                    ->where('field', $key)
                    ->find();

                switch ($model['field_type']) {
                    case "text":
                    case "textarea":
                        if (!empty($value)) {

                            $db = $db->where($key, 'like', '%' . $value . '%');
                        }
                        break;
                    case "user":
                        if (!empty($value)) {
                            $db = $db->whereFindInSet($key, $value);
                        }
                        break;
                    case "user":
                        if (!empty($value)) {
                            $db = $db->whereFindInSet($key, $value);
                        }
                        break;
                    case "date":
                        if (!empty($value)) {
                            $db = $db->where($key, 'like', $value . '%');
                        }
                        break;
                }
            }


            $data = $db->paginate($pageSize)
                ->each(function ($out_data) use ($table_order_id) {
                    foreach ($out_data as $key => $item) {
                        $out_data[$key] = $item;
                        if ($key == 'id') {
                            continue;
                        }
                        $field_type = Db::table("hgy_special_db_table_field_config")
                            ->where('field', $key)
                            ->where('table_order_id', $table_order_id)
                            ->value('field_type');
                        if ($field_type == 'user') {
                            $out_data[$key . '_name'] = "";
                            if (!empty($item)) {
                                $user_id_arr = explode(',', $item);
                                $user_name = Db::table("td_user")->whereIn('USER_ID', $user_id_arr)->column('USER_NAME');

                                $out_data[$key . '_name'] = implode(',', $user_name);
                            }
                        } else if ($field_type == 'dept') {
                            $out_data[$key . '_name'] = "";
                            if (!empty($item)) {
                                $dept_id_arr = explode(',', $item);
                                $dept_name = Db::table("department")->where('DEPT_ID', $dept_id_arr)->column('DEPT_NAME');
                                $out_data[$key . '_name'] = implode(',', $dept_name);
                            }
                        }
                    }
                    return $out_data;
                });

            $header = Db::table("hgy_special_db_table_field_config")
                ->where('list_is_show', 1)
                ->where('table_order_id', $table_order_id)
                ->column('field_name,width', 'field');

            $search = Db::table("hgy_special_db_table_field_config")
                ->where('list_is_show', 1)
                ->where('table_order_id', $table_order_id)
                ->column('field_name,width', 'field');

            $header_back = [];
            foreach ($header as $key => $item) {
                if ($key == 'id') {
                    $header_back[] = [
                        'prop' => $key,
                        'label' => $item['field_name'],
                        'width' => $item['width'],
                    ];
                } else {
                    $field_type = Db::table("hgy_special_db_table_field_config")
                        ->where('field', $key)
                        ->where('table_order_id', $table_order_id)
                        ->value('field_type');
                    if ($field_type == 'user' || $field_type == 'dept') {
                        $header_back[] = [
                            'prop' => $key . '_name',
                            'label' => $item['field_name'],
                            'width' => $item['width'],
                        ];
                    } else {
                        $header_back[] = [
                            'prop' => $key,
                            'label' => $item['field_name'],
                            'width' => $item['width'],
                        ];
                    }
                }


            }

            $search_back = [];
            foreach ($search as $key => $item) {
                $search_back[] = [
                    'prop' => $key,
                    'label' => $item['field_name'],
                    'width' => $item['width'],
                ];
            }

            return json(['data' => $data, 'header' => $header_back, 'search' => $search_back, 'code' => 1000, 'msg' => '获取成功']);
        } catch (\Exception $e) {
            $rdata = [
                'code' => $e->getCode(),
                'data' => [],
                'is_super_man' => false,
                'msg' => $e->getMessage(),
            ];
            return json($rdata);
        }
    }

    /**
     * @param Request $request
     * @return \think\response\Json
     * 获取专项数据搜索条件
     */
    public function getSpecialDataSearch(Request $request)
    {
        try {
            $template_id = $request->param('template_id', '');

            $search_back = Db::table("hgy_special_db_table_field_config")
                ->where('is_search', 1)
                ->where('table_order_id', $template_id)
                ->column('field,field_name,width,field_type');


            return json(['search' => $search_back, 'code' => 1000, 'msg' => '获取成功']);
        } catch (\Exception $e) {
            $rdata = [
                'code' => $e->getCode(),
                'data' => [],
                'is_super_man' => false,
                'msg' => $e->getMessage(),
            ];
            return json($rdata);
        }
    }

    /**
     * @param Request $request
     * @return void
     *
     * 获取一条专项数据
     */
    public function getOneSpecialData(Request $request)
    {
        try {
            $table_order_id = $request->param('type_id', '');
            $data_id = $request->param('data_id', 10);
            $dc_special_table_name = Db::table("hgy_special_db_table_order")->where('id', $table_order_id)->value('dc_special_table_name');
            $data = Db::table($dc_special_table_name)
                ->alias('r')
                ->field('r.*')
                ->where('id', $data_id)
                ->find();


            $out_data = [];
            foreach ($data as $key => $item) {
                $out_data[$key] = $item;
                if ($key == 'id') {
                    continue;
                }
                $field_type = Db::table("hgy_special_db_table_field_config")
                    ->where('field', $key)
                    ->where('table_order_id', $table_order_id)
                    ->value('field_type');
                if ($field_type == 'user') {
                    $out_data[$key . '_name'] = "";
                    if (!empty($item)) {
                        $user_id_arr = explode(',', $item);
                        $user_name = Db::table("td_user")->whereIn('USER_ID', $user_id_arr)->column('USER_NAME');
                        $out_data[$key . '_name'] = implode(',', $user_name);
                    }


                } else if ($field_type == 'dept') {
                    $out_data[$key . '_name'] = "";
                    if (!empty($item)) {
                        $dept_id_arr = explode(',', $item);
                        $dept_name = Db::table("department")->where('DEPT_ID', $dept_id_arr)->column('DEPT_NAME');
                        $out_data[$key . '_name'] = implode(',', $dept_name);
                    }
                }


            }


            $title = Db::table("hgy_special_db_table_field_config")
                ->where('list_is_show', 1)
                ->where('table_order_id', $table_order_id)
                ->column('field_name,field_type', 'field');


            return json(['data' => $out_data, 'header' => $title, 'code' => 1000, 'msg' => '获取成功']);
        } catch (\Exception $e) {
            $rdata = [
                'code' => $e->getCode(),
                'data' => [],
                'is_super_man' => false,
                'msg' => $e->getMessage() . $e->getLine(),
            ];
            return json($rdata);
        }
    }

    /**
     * @param Request $request
     * @return \think\response\Json
     * 修改一条专项数据
     */

    public function changeOneSpecialData(Request $request)
    {
        try {
            $table_order_id = $request->param('type_id', '');
            $data_id = $request->param('data_id', 0);
            $data = $request->param('data', []);


            $dc_special_table_name = Db::table("hgy_special_db_table_order")->where('id', $table_order_id)->value('dc_special_table_name');

            $title = Db::table("hgy_special_db_table_field_config")
                ->where('list_is_show', 1)
                ->where('table_order_id', $table_order_id)
                ->column('field_name,field_type', 'field');


            $update_data = [];
            foreach ($data as $key => $item) {
                if ($key == 'id' || str_ends_with($key, "_name")) {
                    continue;
                }
                $update_data[$key] = $item;
            }
            Db::startTrans();
            Db::table($dc_special_table_name)
                ->where('id', $data_id)
                ->update($update_data);
            Db::commit();
            return json(['data' => $data, 'header' => $title, 'code' => 1000, 'type' => 'success', 'msg' => '修改成功']);
        } catch (\Exception $e) {
            Db::rollback();
            $rdata = [
                'code' => $e->getCode(),
                'data' => [],
                'is_super_man' => false,
                'msg' => $e->getMessage(),
                'type' => 'error',
            ];
            return json($rdata);
        }
    }


    /**
     * @param Request $request
     * @return \think\response\Json
     *
     * 删除一条专项数据
     */
    public function deleteOneSpecialData(Request $request)
    {
        try {
            $table_order_id = $request->param('type_id', '');
            $data_id = $request->param('data_id', 0);


            $dc_special_table_name = Db::table("hgy_special_db_table_order")->where('id', $table_order_id)->value('dc_special_table_name');
            Db::startTrans();

            Db::table($dc_special_table_name)
                ->where('id', $data_id)
                ->delete();
            Db::commit();
            return json(['code' => 1000, 'msg' => '删除成功', 'type' => 'success']);
        } catch (\Exception $e) {
            Db::rollback();
            $rdata = [
                'code' => $e->getCode(),
                'data' => [],
                'type' => 'error',
                'is_super_man' => false,
                'msg' => $e->getMessage(),
            ];
            return json($rdata);
        }
    }


    /**
     * @param Request $request
     * @return \think\response\Json
     *
     * 获取待结束流程
     */
    public function getSpecialConfigList(Request $request)
    {
        try {
            $searchData = $request->param('searchConfigData', '');
            $pageSize = $request->param('limit', 10);
            $db = Db::table("hgy_special_db_table_order")
                ->alias('o')
                ->field('o.*');
            foreach ($searchData as $key => $value) {
                switch ($key) {
                    case "dc_special_title":
                        if (!empty($value)) {
                            $db = $db->where("dc_special_title", 'like', '%' . $value . '%');
                        }
                        break;
                    case "dc_remark":
                        if (!empty($value)) {
                            $db = $db->where("dc_remark", 'like', '%' . $value . '%');
                        }
                        break;
                }
            }
            $data = $db->where("dc_create_user_id", $_SESSION['LOGIN_USER_ID'])
                ->whereNull("dc_deleted_at")
                ->order('id', 'desc')
                ->paginate($pageSize)->each(function ($item) {
                    $item['file_path'] = '/general/lcj_toppingsoft/public/storage/' . $item['file_path'];

                    return $item;
                });
            return json(['data' => $data, 'code' => 1000, 'msg' => '获取成功']);
        } catch (\Exception $e) {
            $rdata = [
                'code' => 1001,
                'data' => [],
                'is_super_man' => false,
                'msg' => $e->getMessage(),
            ];
            return json($rdata);
        }
    }


    public function getHadList(Request $request)
    {
        try {

            $flow_ids = Db::table("top_config_order")->where("id", '>', 0)->column('flow_id');
            $searchData = $request->param('searchData', '');
            $pageSize = $request->param('limit', 10);
            $db = Db::table("top_zgj_flow_end_record")
                ->alias('e')
                ->field(['r.*', 'e.created_at', 'e.create_user_name g_user_name'])
                ->leftJoin("bpm_run r", 'r.RUN_ID = e.run_id');

            foreach ($searchData as $key => $value) {
                switch ($key) {
                    case "flow_id":
                        if (empty($value)) {
                            $db->whereIn("FLOW_ID", $flow_ids);
                        } else {
                            $db->where("FLOW_ID", $value);
                        }
                        break;
                }
            }

            $data = $db->where("DEL_FLAG", 0)
                ->order('e.created_at', 'desc')
                ->distinct()
                ->paginate($pageSize)
                ->each(function ($model) {
                    $getRunInfo = getRunInfo($model['RUN_ID']);
                    $model['url'] = "";
                    if ($getRunInfo['code'] == 200) {
                        $model['url'] = $getRunInfo['url'];
                    }
                    return $model;
                });
            return json(['data' => $data, 'code' => 200, 'msg' => '获取成功']);
        } catch (\Exception $e) {
            $rdata = [
                'code' => 1001,
                'data' => [],
                'is_super_man' => false,
                'msg' => $e->getMessage(),
            ];
            return json($rdata);
        }
    }

    public
    function getRemoveList(Request $request)
    {
        try {

            $flow_ids = Db::table("top_config_order")->where("id", '>', 0)->column('flow_id');
            $searchData = $request->param('searchData', '');
            $pageSize = $request->param('limit', 10);
            $db = Db::table("top_zgj_flow_remove_record")
                ->alias('e')
                ->field(['r.*', 'e.created_at', 'e.create_user_name g_user_name'])
                ->leftJoin("bpm_run r", 'r.RUN_ID = e.run_id');

            foreach ($searchData as $key => $value) {
                switch ($key) {
                    case "flow_id":
                        if (empty($value)) {
                            $db->whereIn("FLOW_ID", $flow_ids);
                        } else {
                            $db->where("FLOW_ID", $value);
                        }
                        break;
                }
            }

            $data = $db->where("DEL_FLAG", 0)
                ->order('e.created_at', 'desc')
                ->distinct()
                ->paginate($pageSize)
                ->each(function ($model) {
                    $getRunInfo = getRunInfo($model['RUN_ID']);
                    $model['url'] = "";
                    if ($getRunInfo['code'] == 200) {
                        $model['url'] = $getRunInfo['url'];
                    }
                    return $model;
                });
            return json(['data' => $data, 'code' => 200, 'msg' => '获取成功']);
        } catch (\Exception $e) {
            $rdata = [
                'code' => 1001,
                'data' => [],
                'is_super_man' => false,
                'msg' => $e->getMessage(),
            ];
            return json($rdata);
        }
    }


    /**
     * @return \think\response\Json
     * 获取流程名字
     */
    public
    function getFlowList()
    {
        try {
            $data = Db::table("top_config_order")
                ->alias('o')
                ->leftJoin("bpm_type t", 'o.flow_id = t.FLOW_ID')
                ->where("id", '>', 0)
                ->column('t.FLOW_NAME flow_name ,o.flow_id');
            return json(['code' => 200, 'msg' => '操作成功', 'data' => $data, 'type' => 'success']);
        } catch (\Exception $e) {
            Db::rollback();
            $rdata = [
                'code' => 1001,
                'data' => [],
                'is_super_man' => false,
                'msg' => $e->getMessage(),
            ];
            return json($rdata);
        }
    }


    /**
     * @param Request $request
     * @return \think\response\Json
     *
     * 流程归档
     */
    public
    function endOneFlow(Request $request)
    {
        try {
            Db::startTrans();
            $run_id = $request->param('run_id', '');

//            Db::table('bpm_run')->where('RUN_ID', $run_id)->update(array('end_time' => date('Y-m-d H:i:s')));
            Db::table('top_zgj_flow_end_record')->where('run_id', $run_id)->delete();
            Db::table('top_zgj_flow_end_record')->insert([
                'run_id' => $run_id,
                'create_user_id' => $_SESSION['LOGIN_USER_ID'],
                'create_user_name' => $_SESSION['LOGIN_USER_NAME'],
                'created_at' => date('Y-m-d H:i:s'),
            ]);
            Db::table('top_zgj_flow_end_record_do')->insert([
                'run_id' => $run_id,
                'type' => "1",
                'create_user_id' => $_SESSION['LOGIN_USER_ID'],
                'create_user_name' => $_SESSION['LOGIN_USER_NAME'],
                'created_at' => date('Y-m-d H:i:s'),
            ]);


            Db::commit();
            return json(['code' => 200, 'msg' => '操作成功', 'type' => 'success']);
        } catch (\Exception $e) {
            Db::rollback();
            $rdata = [
                'code' => 1001,
                'data' => [],
                'is_super_man' => false,
                'msg' => $e->getMessage(),
            ];
            return json($rdata);
        }
    }

    /**
     * @param Request $request
     * @return \think\response\Json
     *
     * 取消归档一个流程
     */
    public
    function cancelEndOneFlow(Request $request)
    {
        try {
            Db::startTrans();
            $run_id = $request->param('run_id', '');

            Db::table('bpm_run')->where('RUN_ID', $run_id)->update(array('end_time' => '1000-01-01 00:00:00'));
            Db::table('top_zgj_flow_end_record')->where('run_id', $run_id)->delete();
            Db::table('top_zgj_flow_end_record_do')
                ->insert([
                    'run_id' => $run_id,
                    'type' => "0",
                    'create_user_id' => $_SESSION['LOGIN_USER_ID'],
                    'create_user_name' => $_SESSION['LOGIN_USER_NAME'],
                    'created_at' => date('Y-m-d H:i:s'),
                ]);
            Db::commit();
            return json(['code' => 200, 'msg' => '操作成功', 'type' => 'success']);
        } catch (\Exception $e) {
            Db::rollback();
            $rdata = [
                'code' => 1001,
                'data' => [],
                'is_super_man' => false,
                'msg' => $e->getMessage(),
            ];
            return json($rdata);
        }
    }


    /**
     * @param Request $request
     * @return \think\response\Json
     *
     * 流程归档
     */
    public
    function removeOneFlow(Request $request)
    {
        try {
            Db::startTrans();
            $run_id = $request->param('run_id', '');

            Db::table('top_zgj_flow_remove_record')->where('run_id', $run_id)->delete();
            Db::table('top_zgj_flow_remove_record')
                ->insert([
                    'run_id' => $run_id,
                    'create_user_id' => $_SESSION['LOGIN_USER_ID'],
                    'create_user_name' => $_SESSION['LOGIN_USER_NAME'],
                    'created_at' => date('Y-m-d H:i:s'),
                ]);
            Db::table('top_zgj_flow_remove_record_do')->insert([
                'run_id' => $run_id,
                'type' => "1",
                'create_user_id' => $_SESSION['LOGIN_USER_ID'],
                'create_user_name' => $_SESSION['LOGIN_USER_NAME'],
                'created_at' => date('Y-m-d H:i:s'),
            ]);
            Db::commit();
            return json(['code' => 200, 'msg' => '操作成功', 'type' => 'success']);
        } catch (\Exception $e) {
            Db::rollback();
            $rdata = [
                'code' => 1001,
                'data' => [],
                'is_super_man' => false,
                'msg' => $e->getMessage(),
            ];
            return json($rdata);
        }
    }

    /**
     * @param Request $request
     * @return \think\response\Json
     *
     * 取消归档一个流程
     */
    public
    function cancelRemoveOneFlow(Request $request)
    {
        try {
            Db::startTrans();
            $run_id = $request->param('run_id', '');

            Db::table('bpm_run')->where('RUN_ID', $run_id)->update(array('end_time' => '1000-01-01 00:00:00'));
            Db::table('top_zgj_flow_remove_record')->where('run_id', $run_id)->delete();
            Db::table('top_zgj_flow_remove_record_do')
                ->insert([
                    'run_id' => $run_id,
                    'type' => "0",
                    'create_user_id' => $_SESSION['LOGIN_USER_ID'],
                    'create_user_name' => $_SESSION['LOGIN_USER_NAME'],
                    'created_at' => date('Y-m-d H:i:s'),
                ]);
            Db::commit();
            return json(['code' => 200, 'msg' => '操作成功', 'type' => 'success']);
        } catch (\Exception $e) {
            Db::rollback();
            $rdata = [
                'code' => 1001,
                'data' => [],
                'is_super_man' => false,
                'msg' => $e->getMessage(),
            ];
            return json($rdata);
        }
    }


    /**
     * @param Request $request
     * @return \think\response\Json
     *
     *
     * 获取公文列表
     */
    public
    function getDocumentList(Request $request)
    {
        try {

            $flow_ids = Db::table("top_config_order")->where("id", '>', 0)->column('flow_id');
            $searchData = $request->param('searchData', '');
            $pageSize = $request->param('limit', 10);
            $db = Db::table("top_zgj_document_info")
                ->alias('re')->field('re.*')
                ->leftJoin("bpm_run r", 'r.RUN_ID = re.run_id');
            foreach ($searchData as $key => $value) {
                switch ($key) {

                    default:
                        if (!empty($value)) {
                            $db->where("re . " . $key, 'like', '%' . $value . '%');
                        }

                }
            }

            $data = $db->where("DEL_FLAG", 0)
                ->paginate($pageSize)->each(function ($model) {
                    $getRunInfo = getRunInfo($model['run_id']);
                    $model['url'] = "";
                    if ($getRunInfo['code'] == 200) {
                        $model['url'] = $getRunInfo['url'];
                    }
                    return $model;
                });
            return json(['data' => $data, 'code' => 200, 'msg' => '获取成功']);
        } catch (\Exception $e) {
            $rdata = [
                'code' => 1001,
                'data' => [],
                'is_super_man' => false,
                'msg' => $e->getMessage(),
            ];
            return json($rdata);
        }
    }

    public
    static function check_column($table, $column)
    {
        $res = Db::query('select count(*) from information_schema.columns where table_name = ' . "'" . $table . "' " . 'and column_name =' . "'" . $column . "'");
        if ($res[0]['count(*)'] != 0) {
            return 1;
        } else {
            return 0;
        }
    }

    public
    static function add_column()
    {
        $sql = "ALTER TABLE `sys_rule_data_1_child`
	ADD COLUMN `old_parent_id` INT(11) NULL default '0' COMMENT '历史id' AFTER `module_id`,
	ADD COLUMN `run_id` INT(11) NULL default '0' COMMENT '流水号' AFTER `old_parent_id`,
	ADD COLUMN `deleted_at` VARCHAR(50) NULL default NULL COMMENT '删除时间' AFTER `run_id`;
";

        $res = Db::execute($sql);
        if ($res) {
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * @param Request $request
     * @return \think\response\Json
     *
     * 删除流程
     */
    public
    function deleteOneFlowBack(Request $request)
    {
        try {
            Db::startTrans();
            $run_id = $request->param("run_id", 0);
            if (empty($run_id)) {
                throw new Exception("流程删除失败");
            }

            $model = Db::table("bpm_run")->where("RUN_ID", $run_id)->find();

            $flow_id = $model['FLOW_ID'];
            $order = Db::table("top_config_order")->where("flow_id", $flow_id)->find();
            $rule_id = $order['rule_id'];
            $document_type_field = Db::table("top_config_list")->where("ek_field", 'document_type')->where("flow_id", $flow_id)->value('flow_field');
            $document_code_field = Db::table("top_config_list")->where("ek_field", 'document_code')->where("flow_id", $flow_id)->value('flow_field');
            $document_year_field = Db::table("top_config_list")->where("ek_field", 'document_year')->where("flow_id", $flow_id)->value('flow_field');
            $runModel = Db::table("bpm_data_" . $flow_id)->where('run_id', $run_id)->find();

            $document_type_value = $runModel[$document_type_field];
            $document_code_value = $runModel[$document_code_field];
            $document_code_year = $runModel[$document_year_field];

            $sys_rule_data_model = Db::table("sys_rule_data_" . $rule_id)
                ->where("placeholder", $document_type_value)
                ->where("year_full", $document_code_year)
                ->find();
            if (empty($sys_rule_data_model)) {
                throw new Exception("无编号");
            }
            $table = "sys_rule_data_" . $rule_id . '_child';
            $res = self::check_column($table, 'deleted_at');
            if ($res == 0) {

                self::add_column();
            }

            $res = self::check_column($table, 'run_id');
            if ($res == 0) {
                self::add_column();
            }

            $res = self::check_column($table, 'old_parent_id');
            if ($res == 0) {
                self::add_column();
            }

            $sys_rule_data_child_model = Db::table($table)
                ->where("parent_id", $sys_rule_data_model['id'])
                ->where("number", $document_code_value)
                ->find();
            if (!empty($sys_rule_data_child_model)) {
                Db::table("sys_rule_data_" . $rule_id . '_child')
                    ->where("parent_id", $sys_rule_data_model['id'])
                    ->where("number", $document_code_value)
                    ->update([
                        'deleted_at' => date('Y-m-d H:i:s'),
                        'old_parent_id' => $sys_rule_data_child_model['parent_id'],
                        'parent_id' => 0,
                        'run_id' => $run_id,
                    ]);
            }
            Db::commit();
            return json(['code' => 200, 'msg' => '操作成功', 'type' => 'success']);
        } catch (\Exception $e) {
            Db::rollback();
            $rdata = [
                'code' => 1001,
                'type' => 'error',
                'msg' => $e->getMessage() . $e->getLine(),
            ];
            return json($rdata);
        }
    }


    /**
     * @param Request $request
     * @return \think\response\Json
     *
     * 获取编号情况
     */
    public
    function getCodeInfo(Request $request)
    {
        try {

            $rule_list = Db::table('sys_rule')
                ->alias('r')
                ->field(['r.name rule_name', 'r.id rule_id'])
                ->select()
                ->each(function ($model) {
                    $res = Db::table('sys_rule_data_' . $model['rule_id'])
                        ->field([
                            'number max_code',
                            'year_full',
                            'placeholder',
                            'id',
                        ])
                        ->select()
                        ->each(function ($item) use ($model) {
                            $return_delete_code = [];
                            $check_column = self::check_column('sys_rule_data_' . $model['rule_id'] . '_child', "deleted_at");
                            if ($check_column > 0) {
                                $delete_code = Db::table('sys_rule_data_' . $model['rule_id'] . '_child')
                                    ->where('old_parent_id', $item['id'])
                                    ->whereNotNull('deleted_at')
                                    ->column('number');


                                foreach ($delete_code as $c) {

                                    $count = Db::table('sys_rule_data_' . $model['rule_id'] . '_child')
                                        ->where('parent_id', $item['id'])
                                        ->where('number', $c)
                                        ->whereNull('deleted_at')
                                        ->count();
                                    if ($count == 0) {
                                        $return_delete_code[] = $c;
                                    }
                                }
                            }
                            $return_not_code = [];
                            for ($i = 1; $i <= $item['max_code']; $i++) {
                                if (in_array($i, $return_delete_code)) {
                                    continue;
                                }
                                //判断是否使用
                                $check_column = self::check_column('sys_rule_data_' . $model['rule_id'] . '_child', "deleted_at");
                                if ($check_column == 0) {
                                    $return_not_code[] = $i;
                                    continue;
                                }
                                $count = Db::table('sys_rule_data_' . $model['rule_id'] . '_child')
                                    ->where('parent_id', $item['id'])
                                    ->where('number', $i)
                                    ->whereNull('deleted_at')
                                    ->count();
                                if ($count == 0) {
                                    $return_not_code[] = $i;
                                }
                            }

                            $item['deleted_code'] = $return_delete_code;
                            $item['not_code'] = $return_not_code;
                            return $item;
                        });

                    $model['children'] = $res;
                    $model['hasChildren'] = empty($res);
                    return $model;
                });
            return json(['code' => 1000, 'type' => 'success', 'data' => $rule_list, 'msg' => "创建成功"]);
        } catch (\Exception $exception) {

            Db::rollback();
            return json(['code' => 201, 'type' => 'error', 'msg' => $exception->getLine()]);
        }
    }


    public
    function getUserInfo(Request $request)
    {

        try {
            return json(['data' => '军哥']);
        } catch (\Exception $exception) {
            dd($exception->getMessage());
            $this->loginView();
        }
    }
}