<style>
.my-autocomplete li{line-height: normal;padding: 7px;}
.my-autocomplete li .name{text-overflow: ellipsis;overflow: hidden;}
.my-autocomplete li .addr{font-size: 12px;color: #b4b4b4;}
.my-autocomplete li .highlighted{color: #ddd;}
.el-cascader { width:100%;}
.el-form-item__content .el-input-group { vertical-align: middle;}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 150px;
  height: 150px;
  line-height: 150px;
  text-align: center;
}
.avatar {
  width: 150px;
  height: 150px;
  display: block;
}
</style>
<template>
  <el-dialog :title="title" :close-on-click-modal="false"  modal="false" :visible.sync="visible" width="600px" @close="refresh()" append-to-body="true" label-position="top">
    <el-form ref="form" :model="data" class="tableSearch" label-position="right" label-width="150px" :rules="rules">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="企业名称" prop="company_name">
            {{data.company_name}}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="行业/专业" prop="industry">
            {{data.industry}}/{{data.specialty}}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="整改报告" prop="standard_id">
<!--            <el-upload
                class="upload-demo"
                ref="uploadCom"
                :show-file-list="false"
                action="upload"
                :on-success="uploadSuccess"
                :on-remove="handleRemove"
                :before-upload="uploadBefore"
                multiple
                :limit="5"
                :on-exceed="handleExceed"
                :file-list="data.files"
                accept="image/*, .pdf">
              <el-button icon="el-icon-upload2" @click="uploadFileClick($event)">将文件拖到此处，或<em>点击上传</em></el-button>
              <div slot="tip" class="el-upload_tip">
                支持扩展名：.rar .zip .doc .docx .xls .txt .pdf .jpg .png .jpeg，最多上传5个文件，每个大小不超过50Mb
              </div>
            </el-upload>
            <div class="flex mt20" v-show="data.files.length > 0">
              <div
                  class="items-wrap"
                  ref="contentWrap"
                  :style="wrapHeight <= 70 ? 'height: auto' : 'height: 60px;'"
                  :class="{ noHidden: noHidden }">
                <uploadfileitem
                    v-for="(item, index) in data.files"
                    :key="index"
                    :file="item"
                    @cancel="cancelFile"
                    :showDel="true"
                    class="mr20"></uploadfileitem>
              </div>
              <div
                  class="on-off"
                  v-if="wrapHeight > 70"
                  @click="noHidden = !noHidden">
                {{ noHidden ? "收起" : "更多" }}
              </div>
            </div>-->
            <el-upload
                class="upload-demo"
                action="upload"
                :on-success="uploadSuccess"
                :before-upload="uploadBefore"
                :on-remove="handleRemove"
                :on-preview="Previewf"
                accept=".jpg,.png,.pdf"
                multiple
                :limit="3"
                :file-list="data.files">
              <el-button size="small" type="primary">点击上传</el-button>
              <div slot="tip" class="el-upload__tip">只能上传jpg/png/pdf文件</div>
            </el-upload>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="整改说明" prop="remark">
            <el-input type="textarea" row="3" v-model="data.remark" size="mini"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24" style="text-align: center;">
          <el-button type="primary" @click="submit()">提交审核</el-button>
          <el-button @click="visible = false">关闭</el-button>
        </el-col>
      </el-row>
    </el-form>
  </el-dialog>
</template>
<script>
// import UploadfileItem from "/general/toppingsoft/public/vue/preview.vue?v=1";
module.exports = {
  name: "add",
  // 模板导入区
  components: {
    'uploadfileitem': 'url:/general/toppingsoft/public/vue/uploadfileitem.vue?v=1',
    // UploadfileItem
  },
  data:function() {
    return {
      id:0,
      visible: false,
      title: '企业整改',
      loading: false,
      noHidden: true,
      wrapHeight: 0,
      delDialogitem: 0,
      imgLoad: false,
      centerDialogVisible: false,
      data: {
        files:[],
      },
      rules: {
      },
      config:[],
      imageUrl:'',
    }
  },
  mounted: function(){
    // this.getConfig();
  },
  created:function(){
  },
  methods: {
    /**
     * 打开弹窗调用方法
     * */
    open: function (row) {
      row = row?row:{id:0};
      var _this =this;
      _this.visible = true;
      row.files = row.files??[];
      _this.data = row;
    },
    submit: function () {
      var _this = this;
      var param = _this.data;
      this.$refs.form.validate(function (valid) {
        if(valid){
          axios.post("reformSave", param).then(function (res) {
            _this.$message({
              message: res.data.msg,
              type: res.data.type
            });
            if (res.data.code == 0) {
              _this.visible = false;
              _this.$emit("ok");
            }
          }).catch(function (error) {
            console.log("出现错误:",error);
          });
        }
      });
    },
    cancelFile(file) {
      console.log(file);
    },
    uploadFileClick(event) {
      if (this.formLabelAlign.annex.length === 5) {
        this.$message({
          type: "warning",
          message: "最多只可以添加五条",
        });
        event.stopPropagation();
        return false;
      }
    },
    uploadBefore(file) {
      console.log(file)
      const isJPG = file.type === 'image/jpeg';
      const isPNG = file.type === 'image/png';
      const isPDF = file.type === 'application/pdf';
      if(!isJPG&&!isPNG&&!isPDF){
        this.$message.error('请上传jpg/png/pdf文件');
      }
      return isJPG||isPNG||isPDF;
    },
    uploadSuccess(res, file,fileList) {
      var files = [];
      for(var i in fileList){
        files.push(fileList[i].response.data??fileList[i]);
      }
      this.data.files = files;
    },
    handleRemove(file, fileList) {
      var files = [];
      for(var i in fileList){
        files.push(fileList[i].response.data??fileList[i]);
      }
      this.data.files = files;
    },
    handleExceed(files, fileList) {
      console.log(files);
    },
    Previewf(file) {
      console.log(file);
      if (file) {
        const addTypeArray = file.name.split(".");
        const addType = addTypeArray[addTypeArray.length - 1];
        const url = file.response?file.response.data.url:file.url;
        console.log(addType);
        if (addType === "pdf") {
          let routeData = this.$router.resolve({
            path: "/insurancePdf",
            query: { url: url, showBack: false },
          });
          window.open(routeData.href, "_blank");
        } else if (addType === "txt") {
          window.open(url);
        } else if (["png", "jpg", "jpeg"].includes(addType)) {
          window.open(url);
        } else {
          this.$message({
            message: "该文件类型暂不支持预览",
            type: "warning",
          });
          return false;
        }
      }
    },
    getConfig:function(){
      var _this = this;
      axios.post('getConfig', {}).then(function (res) {
        if (res.data.code == 0) {
          _this.config = res.data.data;
        }
      }).catch(function (error) {
        console.log("出现错误:",error);
      });
    },
    getInfo:function(id){
      var _this = this;
      _this.loading = true;
      axios.post('getStandardInfo', {id:id}).then(function (res) {
        _this.loading = false;
        if (res.data.code == 0) {
          _this.data = res.data.data;
        }
      }).catch(function (error) {
        _this.loading = false;
        console.log("出现错误:",error);
      });
    },
    refresh: function () {
      this.$emit("refresh");
    },
    changeweekBegin(data){
      console.log(data);
    },
  }
}
</script>