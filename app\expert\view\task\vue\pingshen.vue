<style>
.my-autocomplete li{line-height: normal;padding: 7px;}
.my-autocomplete li .name{text-overflow: ellipsis;overflow: hidden;}
.my-autocomplete li .addr{font-size: 12px;color: #b4b4b4;}
.my-autocomplete li .highlighted{color: #ddd;}
.mytable {border-collapse:collapse;width: 100%;}
.mytable thead tr td {border: 1px solid #333;font-weight: 700; padding: 10px;background: #f5f7fa;text-align: center;}
.mytable tbody tr th {border: 1px solid #333;font-weight: 700; padding: 10px;text-align: center;font-size: 16px;height: 20px;}
.mytable tbody tr td {border: 1px solid #333; padding: 10px;text-align: center;font-size: 16px;}
.mytable .active td{ background: #f2f2f2;}
.mytable tbody tr td {font-family: 宋体;text-align: left;height: 20px;}
.mytable tbody tr td p{line-height: 30px;}
.bufuIndex{
  display: inline-block;
  width: 30px;
  height: 50px;
  line-height: 50px;
  text-align: left;
}
</style>
<template>
  <el-dialog :title="title" :close-on-click-modal="false" :visible.sync="visible" width="1000px" top="10px" @close="refresh()" :append-to-body="true" label-position="top">
    <div id="print-content">
      <h3 style="line-height: 300%;text-align: center;font-size: 40px;margin-bottom: 200px;font-weight: bold">成都市企业安全生产标准<br>现场评审报告</h3>
      <div style="width: 500px;margin: 0 auto;overflow:hidden;font-size: 16px;">
        <div style="width: 100%;height: 20px;float: left;"></div>
        <div style="float: left;height: 30px;line-height: 30px;">评审单位</div>
        <div style="float: left;width: 410px;border-bottom: 2px solid #000;height: 30px;line-height: 30px;font-weight: 700;text-align: center;">成都市城市安全管理研究院</div>
        <div style="float: left;width: 100%;height: 20px;"></div>
        <div style="float: left;height: 30px;line-height: 30px;">定级企业（盖章）</div>
        <div style="float: left;width: 345px;border-bottom: 2px solid #000;height: 30px;line-height: 30px;font-weight: 700;text-align: center;">{{data.company_name}}</div>
        <div style="float: left;width: 100%;height: 20px;"></div>
        <div style="float: left;height: 30px;line-height: 30px;">行&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;业</div>
        <div style="float: left;width: 175px;border-bottom: 2px solid #000;height: 30px;line-height: 30px;font-weight: 700;text-align: center;">{{data.industry}}</div>
        <div style="float: left;height: 30px;line-height: 30px;">专&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;业</div>
        <div style="float: left;width: 175px;border-bottom: 2px solid #000;height: 30px;line-height: 30px;font-weight: 700;text-align: center;">{{data.specialty}}</div>
        <div style="float: left;width: 100%;height: 20px;"></div>
        <div style="float: left;height: 30px;line-height: 30px;">评审性质</div>
        <div style="float: left;width: 170px;border-bottom: 2px solid #000;height: 30px;line-height: 30px;font-weight: 700;text-align: center;">{{data.type}}</div>
        <div style="float: left;height: 30px;line-height: 30px;">评审等级</div>
        <div style="float: left;width: 170px;border-bottom: 2px solid #000;height: 30px;line-height: 30px;font-weight: 700;text-align: center;">{{data.level}}</div>
        <div style="float: left;width: 100%;height: 20px;"></div>
        <div style="float: left;height: 30px;line-height: 30px;">评审日期</div>
        <div style="float: left;width: 120px;border-bottom: 2px solid #000;height: 30px;line-height: 30px;font-weight: 700;text-align: center;">{{data.year}}</div>
        <div style="float: left;height: 30px;line-height: 30px;">年</div>
        <div style="float: left;width: 120px;border-bottom: 2px solid #000;height: 30px;line-height: 30px;font-weight: 700;text-align: center;">{{data.month}}</div>
        <div style="float: left;height: 30px;line-height: 30px;">月</div>
        <div style="float: left;width: 120px;border-bottom: 2px solid #000;height: 30px;line-height: 30px;font-weight: 700;text-align: center;">{{data.day}}</div>
        <div style="float: left;height: 30px;line-height: 30px;">日</div>
      </div>
      <h3 style="line-height: 300%;text-align: center;font-size: 20px;margin: 100px 0;font-weight: bold">成都市应急管理局制</h3>
      <table class="mytable" style="margin-top: 20px;">
        <tbody>
        <tr>
          <th colspan="6"><label class="my-label">现场评审的基本情况</label></th>
        </tr>
        <tr>
          <th style="width: 120px;"><label class="my-label">单位名称</label></th>
          <td colspan="5" style="width: 700px;"><div class="my-online">{{data.company_name}}</div></td>
        </tr>
        <tr>
          <th style="width: 120px;"><label class="my-label">单位地址</label></th>
          <td colspan="5" style="width: 700px;"><div class="my-online">{{data.address}}</div></td>
        </tr>
        <tr>
          <th style="width: 120px;"><label class="my-label">主要负责人</label></th>
          <td style="width: 200px;"><div class="my-online">{{data.legal}}</div></td>
          <th style="width: 120px;"><label class="my-label">电话</label></th>
          <td style="width: 200px;"><div class="my-online">{{data.phone}}</div></td>
          <th style="width: 120px;"><label class="my-label">手机</label></th>
          <td style="width: 500px;"><div class="my-online">{{data.legal_mobile}}</div></td>
        </tr>
        <tr>
          <th rowspan="2" style="width: 120px;"><label class="my-label">联&nbsp;&nbsp;&nbsp;系&nbsp;&nbsp;&nbsp;人</label></th>
          <td rowspan="2" style="width: 200px;"><div class="my-online">{{data.manager}}</div></td>
          <th style="width: 120px;"><label class="my-label">电话</label></th>
          <td style="width: 200px;"><div class="my-online">{{data.phone}}</div></td>
          <th style="width: 120px;"><label class="my-label">传真</label></th>
          <td style="width: 200px;"><div class="my-online">{{data.fax}}</div></td>
        </tr>
        <tr>
          <th style="width: 120px;"><label class="my-label">手机</label></th>
          <td style="width: 200px;"><div class="my-online">{{data.manager_mobile}}</div></td>
          <th style="width: 120px;"><label class="my-label">电子邮箱</label></th>
          <td style="width: 200px;"><div class="my-online">{{data.manager_email}}</div></td>
        </tr>
        <tr>
          <th style="width: 120px;"><label class="my-label">企业性质</label></th>
          <td colspan="5" style="width: 700px;"><div class="my-online">{{data.nature}}</div></td>
        </tr>
        <tr>
          <th style="width: 120px;"><label class="my-label">企业涉及主要风险 </label></th>
          <td colspan="5" style="width: 700px;"><div class="my-online">{{data.risk}}</div></td>
        </tr>
        <tr>
          <th colspan="6"><label class="my-label">评审情况 </label></th>
        </tr>
        <tr>
          <th :rowspan="data.experts.length+1" style="width: 120px;"><label class="my-label">现场评审组成员</label></th>
          <th style="width: 120px;"><label class="my-label"></label></th>
          <th style="width: 120px;"><label class="my-label">姓名</label></th>
          <th style="width: 120px;"><label class="my-label">单位/职务/职称</label></th>
          <th style="width: 120px;"><label class="my-label">电话</label></th>
          <th style="width: 120px;"><label class="my-label">备注</label></th>
        </tr>
        <tr>
          <th style="width: 120px;"><label class="my-label">组长</label></th>
          <td style="width: 200px;"><div class="my-online">{{data.experts[0].expert_name}}</div></td>
          <td style="width: 200px;"><div class="my-online">{{data.experts[0].unit_name}}</div></td>
          <td style="width: 200px;"><div class="my-online">{{data.experts[0].mobile}}</div></td>
          <td style="width: 200px;"><div class="my-online"></div></td>
        </tr>
        <tr v-for="(item,k) in data.experts" v-if="k>0">
          <th v-if="k==1" :rowspan="data.experts.length-1" style="width: 120px;"><label class="my-label">成员</label></th>
          <td style="width: 200px;"><div class="my-online">{{item.expert_name}}</div></td>
          <td style="width: 200px;"><div class="my-online">{{item.unit_name}}</div></td>
          <td style="width: 200px;"><div class="my-online">{{item.mobile}}</div></td>
          <td style="width: 200px;"><div class="my-online"></div></td>
        </tr>
        <tr v-for="(item,k) in data.citys">
          <th v-if="k==0" colspan="2" :rowspan="data.citys.length" style="width: 120px;"><label class="my-label">(区)市、县应急局人员</label></th>
          <td style="width: 200px;"><div class="my-online"><el-input v-model="item.name"></el-input></div></td>
          <td style="width: 200px;"><div class="my-online"><el-input v-model="item.unit"></el-input></div></td>
          <td style="width: 200px;"><div class="my-online"><el-input v-model="item.mobile"></el-input></div></td>
          <td style="width: 200px;"><div class="my-online"></div></td>
        </tr>
        <tr>
          <th colspan="6" style="width: 120px;"><label class="my-label">企业标准化运行情况概述</label></th>
        </tr>
        <tr>
          <td colspan="6" style="width: 200px;line-height: 150%">
            <p>1．企业安全生产基本情况概述</p>
            <el-input
                type="textarea"
                style="line-height: 130%"
                :autosize="{minRows: 4}"
                placeholder="请输入内容"
                v-model="data.gaishu1">
            </el-input>
            <p>2.企业标准化管理体系建设及其运行情况</p>
            <el-input
                style="line-height: 130%"
                type="textarea"
                :autosize="{minRows: 4}"
                placeholder="请输入内容"
                v-model="data.gaishu2">
            </el-input>
          </td>
        </tr>
        </tbody>
      </table>
      <table class="mytable">
        <tbody>
        <tr>
          <th colspan="8" style="width: 120px;"><label class="my-label">现场评审评分情况</label></th>
        </tr>
        <tr>
          <th style="width: 120px;"><label class="my-label">序号</label></th>
          <th style="width: 200px;"><label class="my-label">评审要素</label></th>
          <th style="width: 120px;"><label class="my-label">标准分</label></th>
          <th style="width: 120px;"><label class="my-label">得分</label></th>
          <th style="width: 120px;"><label class="my-label">得分率</label></th>
          <th style="width: 120px;"><label class="my-label">扣分</label></th>
          <th style="width: 120px;"><label class="my-label">缺项分</label></th>
          <th style="width: 120px;"><label class="my-label">评审人员</label></th>
        </tr>
        <tr v-for="(item,k) in data.review_list">
          <td><div class="my-online">{{k+1}}</div></td>
          <td><div class="my-online">{{item.element_name}}</div></td>
          <td><div class="my-online">{{item.total}}</div></td>
          <td><div class="my-online">{{item.score}}</div></td>
          <td><div class="my-online">{{item.rate}}%</div></td>
          <td><div class="my-online">{{item.deduct}}</div></td>
          <td><div class="my-online">{{item.miss}}</div></td>
          <td><div class="my-online">{{item.expert_name}}</div></td>
        </tr>
        </tbody>
      </table>
      <table class="mytable">
        <tbody>
        <tr>
          <td colspan="8"><div class="my-online">注：行业标准化评审类目与此表不同的，适用其行业评审类目。</div></td>
        </tr>
        <tr>
          <th><label class="my-label">标准分<br/>（汇总）</label></th>
          <td><div class="my-online">{{data.total}}</div></td>
          <th><label class="my-label">缺项分<br/>（汇总）</label></th>
          <td><div class="my-online">{{data.miss}}</div></td>
          <th><label class="my-label">笔试平均<br/>成绩（分）</label></th>
          <td><div class="my-online">{{data.bishi}}</div></td>
          <th><label class="my-label" title="实得分（汇总）：单项得分总和—教育培训得分*（100-笔试平均分）%">实得分<br/>（汇总）</label></th>
          <td><div class="my-online">{{data.score}}</div></td>
        </tr>
        <tr>
          <th><label class="my-label" title="现场评审得分（百分制）：实得分（汇总）/（标准分-缺项分）">现场评审得分</label></th>
          <td colspan="3"><div class="my-online">{{data.scores}}</div></td>
          <th><label class="my-label">拟定达标分值</label></th>
          <td colspan="3"><div class="my-online">≥60</div></td>
        </tr>
        </tbody>
      </table>
      <table class="mytable" style="border: 2px solid #000;">
        <tbody>
        <tr>
          <th colspan="2"><label class="my-label">现场评审结果</label></th>
        </tr>
        <tr>
          <td colspan="2"><div class="my-online">是否达到拟定等级条件：{{data.status_cn}}</div></td>
        </tr>
        <tr>
          <td colspan="2">
            <p>现场评审组组长签字：</p>
            <p style="margin-bottom: 40px;">成员签字：</p>
          </td>
        </tr>
        <tr>
          <td colspan="2">
            <p>1.现场评审情况：</p>
            <el-input
                type="textarea"
                :autosize="{minRows: 2}"
                placeholder="请输入内容"
                v-model="data.qingkuang">
            </el-input>
            <p>2.现场评审结论：</p>
            <el-input
                type="textarea"
                :autosize="{minRows: 2}"
                placeholder="请输入内容"
                v-model="data.jielun">
            </el-input>
          </td>
        </tr>
        <tr>
          <td colspan="2">
            <p>现场评审不符合项：</p>
<!--            <el-input-->
<!--                type="textarea"-->
<!--                :autosize="{minRows: 2}"-->
<!--                placeholder="请输入内容"-->
<!--                v-model="data.reform">-->
<!--            </el-input>-->

            <template v-for="(item,key) in data.reformList">
              <div v-if="key==0">
                <span class="bufuIndex">{{key+1}}.</span>
                <el-input v-if="key < reformTotal" disabled v-model="data.reformList[key]['reform']" placeholder="整改内容" style="width: 500px"></el-input>
                <el-input v-else v-model="data.reformList[key]['reform']" placeholder="整改内容" style="width: 500px"></el-input>
                <el-link v-if="key == (reformTotal-1)" @click="addReformLine" type="success" icon="el-icon-circle-plus-outline">增加必须整改项</el-link>
              </div>

              <div v-else-if="key>0">
                <span class="bufuIndex">{{key+1}}.</span>
                <el-input v-if="key < reformTotal" disabled v-model="data.reformList[key]['reform']" placeholder="整改内容" style="width: 500px"></el-input>
                <el-input v-else v-model="data.reformList[key]['reform']" placeholder="整改内容" style="width: 500px"></el-input>
                <el-link v-if="key == (reformTotal-1)" @click="addReformLine" type="success" icon="el-icon-circle-plus-outline">增加必须整改项</el-link>
                <el-link v-if="key >= reformTotal" @click="delReformLine(data.reformList[key]['reform'])" type="danger" icon="el-icon-remove-outline">删除</el-link>
              </div>
            </template>

          </td>
        </tr>
        <tr>
          <td colspan="2">
            <p>整改建议和要求：</p>
            <el-input
                type="textarea"
                :autosize="{minRows: 2}"
                placeholder="请输入内容"
                v-model="data.jianyi">
            </el-input>
          </td>
        </tr>
        <tr>
          <td colspan="2">
            <p>申请定级企业意见（整改承诺）：</p>
            <p>主要负责人签字：</p>
            <p>（企业盖章）</p>
            <p style="text-align: right;margin: 20px 0;">{{data.year}}&nbsp;年&nbsp;&nbsp;{{data.month}}&nbsp;月&nbsp;&nbsp;{{data.day}}&nbsp;日</p>
          </td>
        </tr>
        <tr>
          <td colspan="2">
            <p>区（市）县应急管理局意见：</p>
            <p>负责人签字：</p>
            <p> 区（市）县应急管理部门（公章）</p>
            <p style="text-align: right;margin: 20px 0;">{{data.year}}&nbsp;年&nbsp;&nbsp;{{data.month}}&nbsp;月&nbsp;&nbsp;{{data.day}}&nbsp;日</p>
          </td>
        </tr>
        </tbody>
      </table>
    </div>
    <el-button style="margin: 10px auto;" type="primary" @click="saveData" size="mini">保存报告</el-button>
<!--    <el-button style="margin: 10px auto;" type="primary" @click="dayin" size="mini">生成报告并打印</el-button>-->
    <el-button style="margin: 10px auto;" type="primary" @click="downPdf" size="mini">查看PDF</el-button>

    <el-dialog
        :visible.sync="loading"
        width="30%"
        :show-close="false"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        custom-class="loading-dialog"
        :modal="true"
        :append-to-body="true"
        :z-index="9999">
      <div style="text-align: center;">
        <i class="el-icon-loading" style="font-size: 30px;"></i>
        <p style="margin-top: 10px;">处理中，请稍候...</p>
      </div>
    </el-dialog>

  </el-dialog>
</template>
<style>
.bufuIndex{
  display: inline-block;
  width: 30px;
  height: 50px;
  line-height: 50px;
  text-align: left;
}

.loading-dialog {
  background-color: rgba(255,255,255,0.8) !important;
  box-shadow: none !important;
  border: none !important;
}
.loading-dialog .el-dialog__header {
  display: none;
}
.loading-dialog .el-dialog__body {
  padding: 20px !important;
}
</style>
<script>
module.exports = {
  name: "add",
  // 模板导入区
  components: {
  },
  data:function() {
    return {
      reformTotal:0,
      id:0,
      activeName:'a',
      type:1,
      visible: false,
      dialogVisible: false,
      dialogImageUrl: '',
      title: '现场评审报告',
      loading: false,
      user_id: 0,
      data:{

      },
      form:{
        id:'',
        status:'',
        content:'',
        files:[],
      },
      is_see:0,
      userData:[],
      details:[],
      cards:{},
      pcas: [],
      thisuser:'',
      rules: {
        status: [
          { required: true, message: '请选择审批状态', trigger: 'blur' },
        ],
        content: [
          { required: true, message: '请填写审批意见', trigger: 'blur' },
        ],
        files: [
          { required: true, message: '请上传定级申请表审批扫描件', trigger: 'blur' },
        ],
      },
      attachData:[],
    }
  },
  methods: {
    addReformLine(){
      this.data.reformList.push({'id':'','reform':''})
    },
    delReformLine(currentData){
      this.data.reformList.splice(this.data.reformList.indexOf(currentData),1);
    },
    /**
     * 打开弹窗调用方法
     * */
    open: function (id) {
      var _this =this;
      _this.visible = true;
      _this.id = id;
      _this.getInfo(id);
    },
    handleRemove(file, fileList) {
      this.form.files = [];
    },
    uploadBefore(file) {
      const isJPG = file.type === 'image/jpeg';
      if(!isJPG){
        this.$message.error('请上传jpg图片');
      }
      return isJPG;
    },
    uploadSuccess(res, file) {
      let data = {
        id:res.id,
        url:res.url,
        name:res.name,
      };
      if(res.state==='SUCCESS'){
        this.form.files.push(data);
      }
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    handleClick(tab, event) {
      console.log(tab, event);
    },
    closeDialog: function () {
      this.visible = false;
    },
    saveData: function () {
      var _this = this;
      _this.loading = true;
      var param = {
        id:_this.data.task.id,
        data:_this.data,
      };
      axios.post("baogaoSave", param).then(function (res) {
        _this.loading = false;
        if (res.data.code == 0) {
          _this.visible = false;
          _this.$alert(res.data.msg);
        }
      }).catch(function (error) {
        console.log("出现错误:",error);
      });
    },
    dayin: function () {
      var _this = this;
      _this.loading = true;
      var param = {
        id:_this.data.task.id,
      };
      openTab2('', 'baogaodayin','打印评审报告','/general/toppingsoft/index.php/expert/task/baogaodayin?id='+param.id);
    },
    downPdf: function () {
      var _this = this;
      _this.loading = true;
      var param = {
        id:_this.data.task.id,
        data:_this.data,
      };
      axios.post("buildReportPdf", param).then(function (res) {
        if (res.data.code == 0) {
          _this.loading = false;
          _this.visible = false;
          openTab2('', 'buildReportPdf','打印评审报告','/general/toppingsoft/'+res.data.data.url);
        }
      }).catch(function (error) {
        console.log("出现错误:",error);
      });
      },
    getInfo:function(id){
      var _this = this;
      axios.post("baogaoInfo", {
        id:id,
      }).then(function (res) {
        if (res.data.code == 0) {
          _this.data=res.data.data;
          _this.reformTotal = _this.data.reformTotal;
          _this.form.id = res.data.data.id;
        }else {
          _this.$message({
            message: res.data.msg,
            type: res.data.type
          });
        }
      }).catch(function (error) {
        _this.loading = false;
        console.log("出现错误:",error);
      });
    },
    refresh: function () {
      this.$emit("refresh");
    },
    printDiv:function(){
      var printContents = document.getElementById("print-content").innerHTML;
      var originalContents = document.body.innerHTML;
      document.body.innerHTML = printContents;
      window.print();
      document.body.innerHTML = originalContents;
    },

  }
}
</script>


