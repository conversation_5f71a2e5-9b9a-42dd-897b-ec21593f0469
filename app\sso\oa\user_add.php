<?php

function get_user_id($has_user_id_str) {
    $user_id = rand(1, 100000);
    if (find_id($has_user_id_str, $user_id)) {
        $user_id = get_user_id($has_user_id_str);
    }
    return $user_id;
}
$USER_NAME = '';
$PASSWORD = '123456';
$USER_PRIV  = '';
$PRIV_NAME0  = '';
$PRIV_ID = '';
$PRIV_NAME = '';
$DEPT_ID = '';
$DEPT_ID_OTHER = '';
$DEPT_NAME_OTHER = '';
$USER_NO = 10;
$POST_PRIV = 0;
$NOT_LOGIN = 0;
$NOT_MOBILE_LOGIN = 0;
$IM_RANGE = 1;
$PRIV_ID1 = '';
$PRIV_NAME1 = '';
$USER_ID1 = '';
$USER_NAME1 = '';
$DEPT_ID_MSG = '';
$DEPT_NAME_MSG = '';
$EMAIL_CAPACITY = 500;
$FOLDER_CAPACITY = 500;
$WEBMAIL_NUM = '';
$WEBMAIL_CAPACITY = '';
$BIND_IP = '';
$REMARK = '';
$SEX = 0;
$BIRTHDAY = '';
$THEME = 19;
$MOBIL_NO = '';
$EMAIL = '';
$TEL_NO_DEPT = '';
$USER_PRIV_TYPE = 1;

include_once "auth.php";
include_once "inc/session.php";
$PHPSESSID = (isset($_GET["PHPSESSID"]) ? $_GET["PHPSESSID"] : (isset($_POST["PHPSESSID"]) ? $_POST["PHPSESSID"] : ""));

if (preg_match("/^[a-z0-9]{20,32}$/i", $PHPSESSID)) {
    session_id($PHPSESSID);
}

if (stristr($PHP_SELF, "export") || stristr($PHP_SELF, "excel") || stristr($PHP_SELF, "word") || stristr($PHP_SELF, "attach.php") || stristr($PHP_SELF, "download.php") || stristr($PHP_SELF, "down.php")) {
    session_cache_limiter("private, must-revalidate");
}
session_start();
ob_start();
include_once "inc/utility.php";
$SCRIPT_NAME = $_SERVER["SCRIPT_NAME"];
if (0 < MYOA_OFFLINE_TIME_MIN) {
    $LAST_OPERATION_TIME = $_COOKIE["LAST_OPERATION_TIME"];
    if (!stristr($SCRIPT_NAME, "/general/ipanel/") && !stristr($SCRIPT_NAME, "/general/task_center/") && !stristr($SCRIPT_NAME, "/general/mytable/") && !stristr($SCRIPT_NAME, "/general/status_bar/") && !stristr($SCRIPT_NAME, "/general/topbar.php") && !stristr($SCRIPT_NAME, "/inc/")) {
        setcookie("LAST_OPERATION_TIME", time(), 0, "/");
    }
    if (($LAST_OPERATION_TIME != "") && ((MYOA_OFFLINE_TIME_MIN * 60) < (time() - $LAST_OPERATION_TIME))) {
        $sessionid = session_id();
        del_my_online_status($sessionid);
        clear_online_status();
        setcookie("LAST_OPERATION_TIME", "", 1, "/");
        session_unset();
        session_destroy();
    }
}

if (!isset($_SESSION["LOGIN_USER_PRIV"])) {
    $query = "select USER_PRIV from TD_USER where UID='" . $_SESSION["LOGIN_UID"] . "'";
    $cursor = exequery(TD::conn(), $query);

    if ($ROW = mysqli_fetch_array($cursor)) {
        $_SESSION["LOGIN_USER_PRIV"] = $ROW["USER_PRIV"];
    }
}

if (!isset($SESSION_WRITE_CLOSE) || ($SESSION_WRITE_CLOSE != 0)) {
    session_write_close();
}

$USER_FUNC_ID_STR = $_SESSION["LOGIN_FUNC_STR"];

if ($_SESSION["LOGIN_USER_ID"] == "admin") {
    $USER_FUNC_ID_STR .= "32,33,56,";
}

$GZIP_POSTFIX = (MYOA_SUPPORT_GZIP ? ".gz" : "");

if (isset($HTML_PAGE_TITLE)) {
    unset($HTML_PAGE_TITLE);
}

include_once "inc/utility_org.php";
include_once "inc/check_type.php";
include_once "inc/td_core.php";
include_once "inc/utility_user.php";
include_once "inc/utility_secu.php";
include_once "inc/utility_email_audit.php";
//include_once "inc/TSecurity/TSecurity.inc.php";
include_once "inc/utility.php";
include_once "inc/td_config.php";
include_once "inc/common.inc.php";
include_once "inc/utility_all.php";
$guid = get_sys_para("TD_UNIQID,WATERMARK_CLASSIFY");
$watermark_classify = $guid["WATERMARK_CLASSIFY"];
$guid = (isset($guid["TD_UNIQID"]) ? $guid["TD_UNIQID"] : "");
$HTML_RENDER_MODE = (isset($HTML_RENDER_MODE) ? $HTML_RENDER_MODE : "webkit");
@header("X-UA-Compatible: IE=10");
@header("renderer: $HTML_RENDER_MODE");

if (!find_id($watermark_classify, "pc_modules")) {
    $_SESSION["LOGIN_WATERMARK_JSON"] = "[]";
}
$PARA_ARRAY = get_sys_para("SEC_PASS_MIN,SEC_PASS_MAX,SEC_PASS_SAFE,LOGIN_USE_DOMAIN,LOGIN_KEY,SEC_PASS_SAFE_LETTER,WEAK_PASSWORD");
while (list($PARA_NAME, $PARA_VALUE) = each($PARA_ARRAY)) {
    $PARA_NAME = $PARA_VALUE;
}
$has_user_id_str = "";
$query = "SELECT UID,USER_ID FROM td_user";
$cursor = exequery(TD::conn(), $query);
while ($row = mysqli_fetch_array($cursor)) {
    $has_user_id_str .= $row["USER_ID"] . ",";
}
$USER_ID = get_user_id($has_user_id_str);
$BIND_IP = str_replace("\r\n", ",", $BIND_IP);
$BIND_IP = str_replace("\n", ",", $BIND_IP);
$BIND_IP = trim($BIND_IP);
$BYNAME = trim($BYNAME);
$USER_NAME = trim($USER_NAME);
if ($BYNAME == "") {
    exit(json_encode(array('code' => 1001, 'msg' => $BYNAME.'用户名不能为空')));
}
if ($USER_NAME == "") {
    exit(json_encode(array('code' => 1002, 'msg' => '用户姓名不能为空')));
}
$DEPT_ID = intval($DEPT_ID);
if (($_SESSION["MYOA_IS_GROUP"] == "1") && ($_SESSION["LOGIN_USER_PRIV_TYPE"] != "1")) {
    if ($_SESSION["LOGIN_USER_PRIV_TYPE"] == "0") {
        $DEPT_ID_ALL = get_manage_dept_ids($_SESSION["LOGIN_UID"]);
        $DEPT_ID_ALL = GetUidByOther("", "", $DEPT_ID_ALL);
        if (!find_id($DEPT_ID_ALL, $_SESSION["LOGIN_UID"])) {
            //exit(json_encode(array('code' => 1003, 'msg' => iconv('GB2312','UTF-8','您没有修改该部门用户的权限'))));
        }
    }
} else if (!is_dept_priv($DEPT_ID)) {
    //exit(json_encode(array('code' => 1004, 'msg' => iconv('GB2312','UTF-8','您没有建立该部门用户的权限'))));
}
$USER_PRIV = intval($USER_PRIV);
if ($USER_PRIV <= 0) {
    exit(json_encode(array('code' => 1005, 'msg' => '角色无效')));
}
$query = "SELECT * from USER_PRIV where USER_PRIV='" . $_SESSION["LOGIN_USER_PRIV"] . "'";
$cursor = exequery(TD::conn(), $query);
if ($ROW = mysqli_fetch_array($cursor)) {
    $PRIV_NO = $ROW["PRIV_NO"];
}
if (($_SESSION["MYOA_IS_GROUP"] == "1") && ($_SESSION["LOGIN_USER_PRIV_TYPE"] != "1")) {
    $user_priv_str = get_manage_priv_ids($_SESSION["LOGIN_UID"], 1);
    if (!find_id($user_priv_str, $USER_PRIV)) {
        //exit(json_encode(array('code' => 1006, 'msg' => iconv('GB2312','UTF-8','您没有建立该角色用户的权限'))));
    }
} else {
    if ($_SESSION["LOGIN_USER_PRIV_TYPE"] != "1") {
        $query = "SELECT USER_PRIV from USER_PRIV where PRIV_NO>'$PRIV_NO' and USER_PRIV='$USER_PRIV' ";
    } else {
        $query = "SELECT USER_PRIV from USER_PRIV where USER_PRIV='$USER_PRIV'";
    }
    $cursor = exequery(TD::conn(), $query);
    if (mysqli_num_rows($cursor) <= 0) {
        //exit(json_encode(array('code' => 1007, 'msg' => iconv('GB2312','UTF-8','您没有建立该角色用户的权限'))));
    }
}
$BYNAME = str_replace(array(",", "\\\"", "\'", "\"", "'", "\t", "\\", "\\\\"), array("", "", "", "", "", "", "", ""), $BYNAME);
$USER_NAME = str_replace(array(",", "\\\"", "\'", "\"", "'", "\t", "\\", "\\\\"), array("", "", "", "", "", "", "", ""), $USER_NAME);
if ($NOT_LOGIN == 0) {
    login_check("[TDCORE_ADDUSER]", "[TDCORE_ADDUSER]");
}
if ((strstr($BYNAME, "\\") != false) || (strstr($BYNAME, ",") != false)) {
    exit(json_encode(array('code' => 1008, 'msg' => '用户名中含有非法字符')));
}
**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
$PASSWORD_ENCRYPT = trim($PASSWORD);
$ENCRYPT_DATA = pack("H*", $PASSWORD_ENCRYPT);
openssl_private_decrypt($ENCRYPT_DATA, $PASSWORD, $private_key);
if (($BIRTHDAY != "") && !is_date($BIRTHDAY)) {
    exit(json_encode(array('code' => 1009, 'msg' => sprintf(_("生日格式不合法，应形如：%s"), date("Y-m-d", time())))));
}
if (substr($BYNAME, -1) == "\\") {
    $BYNAME = substr($BYNAME, 0, -1);
}
if (substr($USER_NAME, -1) == "\\") {
    $USER_NAME = substr($USER_NAME, 0, -1);
}
$query = "select * from TD_USER where BYNAME='$BYNAME'";
$cursor = exequery(TD::conn(), $query, true);
if ($ROW = mysqli_fetch_array($cursor)) {
    exit(json_encode(array('code' => 1010, 'msg' => sprintf(_("用户名 %s 已存在"), $BYNAME))));
}
if ($USER_NO == "") {
    $USER_NO = 10;
}
if (!is_number($USER_NO)) {
    exit(json_encode(array('code' => 1011, 'msg' => '用户排序号应为数字')));
}
$NOT_VIEW_USER = 0;
$NOT_VIEW_TABLE = 0;
$NOT_MOBILE_LOGIN = 0;
$MOBIL_NO_HIDDEN = "0";
$USEING_KEY = 0;
$USING_FINGER = 0;
$IS_LUNAR = 0;
$EMAIL_CAPACITY = 500;
$FOLDER_CAPACITY = 500;
$WEBMAIL_NUM = 0;
$WEBMAIL_CAPACITY = 0;
$USE_POP3 = 1;
$USE_EMAIL = 1;
$query = "SELECT * from USER_PRIV where USER_PRIV='$USER_PRIV'";
$cursor = exequery(TD::conn(), $query);
if ($ROW = mysqli_fetch_array($cursor)) {
    $FUNC_ID_STR = $ROW["FUNC_ID_STR"];
    $PRIV_NO = $ROW["PRIV_NO"];
    $PRIV_NAME = $ROW["PRIV_NAME"];
}
$SHORTCUT = check_id($FUNC_ID_STR, "1,2,3,42,4,147,8,9,16,130,5,131,132,182,183,24,15,76,", true);
$DEPT_ID_OTHER = check_id($DEPT_ID, $DEPT_ID_OTHER, false);
$PASSWORD = crypt($PASSWORD);
$USER_NAME_INDEX = getChnprefix($USER_NAME);
$USER_MANAGE_ORGS = ($_SESSION["MYOA_IS_GROUP"] == "1" ? $USER_MANAGE_ORGS : "");
$u_priv_str = $USER_PRIV . "," . $PRIV_ID;
$u_dept_str = $DEPT_ID . "," . $DEPT_ID_OTHER;
$u_priv_str = td_trim($u_priv_str);
$u_dept_str = td_trim($u_dept_str);
$u_dept_arr = explode(",", $u_dept_str);
$count = count($u_dept_arr);
$org_str = "";
for ($i = 0; $i < $count; $i++) {
    $org = GetOrgIDByDeptID($u_dept_arr[$i]);
    if ($org != 0) {
        $org_str .= $org . ",";
    } else {
        $org = GetWholeIDByDeptID($u_dept_arr[$i]);
        if ($org != 0) {
            $org_str .= $org . ",";
        }
    }
}
$org_str = td_trim($org_str);
if ($DEPT_ID != 0) {
    $sql = "SELECT * FROM user_priv WHERE FIND_IN_SET(USER_PRIV,'$u_priv_str') and PRIV_DEPT_ID!=0";
    $cursor = exequery(TD::conn(), $sql);
    while ($arr = mysqli_fetch_array($cursor)) {
        $PRIV_DEPT_ID = $arr["PRIV_DEPT_ID"];
        $PRIV_TYPE = $arr["PRIV_TYPE"];
        $IS_GLOBAL = $arr["IS_GLOBAL"];
        if (($PRIV_TYPE == 2) || ($IS_GLOBAL == 2)) {
            if (!find_id($org_str, $PRIV_DEPT_ID)) {
                //exit(json_encode(array('code' => 1012, 'msg' => iconv('GB2312','UTF-8','所选的角色与所选部门不匹配！'))));
            }
        }
        if (($PRIV_TYPE == 0) && ($IS_GLOBAL == 1)) {
            if (!find_id($u_dept_str, $PRIV_DEPT_ID)) {
                //exit(json_encode(array('code' => 1013, 'msg' => iconv('GB2312','UTF-8','所选的角色与所选部门不匹配！'))));
            }
        }
    }
}
if(!isset($TO_ID) || empty($TO_ID))
    $TO_ID = '';

$USER_PRIV_OTHER = $PRIV_ID ? $PRIV_ID : '';
//----------------------------------------------------------
$ARRAY=array('USER_ID'=> "$USER_ID",'BYNAME'=> $BYNAME,'USER_NAME'=> $USER_NAME,'USER_NAME_INDEX'=> $USER_NAME_INDEX,'SEX'=> $SEX,'PASSWORD'=> $PASSWORD,'USER_PRIV'=> $USER_PRIV,'POST_PRIV'=> $POST_PRIV,'POST_DEPT'=> $TO_ID,'IM_RANGE'=> $IM_RANGE,'DEPT_ID'=> $DEPT_ID,'DEPT_ID_OTHER'=> $DEPT_ID_OTHER,'AVATAR'=> $SEX,'CALL_SOUND'=> 1,'SMS_ON'=> $SMS_ON,'USER_PRIV_OTHER'=> $USER_PRIV_OTHER,'USER_NO'=> $USER_NO,'NOT_LOGIN'=> $NOT_LOGIN,'NOT_VIEW_USER'=> $NOT_VIEW_USER,'NOT_VIEW_TABLE'=> $NOT_VIEW_TABLE,'NOT_MOBILE_LOGIN'=> $NOT_MOBILE_LOGIN,'BIRTHDAY'=> $BIRTHDAY,'THEME'=> $THEME,'SHORTCUT'=> $SHORTCUT,'MOBIL_NO'=> $MOBIL_NO,'MOBIL_NO_HIDDEN'=> $MOBIL_NO_HIDDEN,'BIND_IP'=> $BIND_IP,'KEY_SN'=> $KEY_SN,'USEING_KEY'=> $USEING_KEY ,'REMARK'=> $REMARK,'TEL_NO_DEPT'=> $TEL_NO_DEPT,'EMAIL'=> $EMAIL,'USING_FINGER'=> $USING_FINGER,'IS_LUNAR'=> $IS_LUNAR,'USER_PRIV_NO'=> $PRIV_NO,'USER_PRIV_NAME'=> $PRIV_NAME,'USER_PRIV_TYPE' => $USER_PRIV_TYPE,'USER_MANAGE_ORGS' => $USER_MANAGE_ORGS,'KEY_SN' => '','SECURE_KEY_SN' => '','LEAVE_DEPT' => 0,'SMS_ON' => 1,'MY_RSS' => '','PHOTO' => '','LEAVE_TIME' => '1000-01-01 00:00:00','USER_PARA' => '','corp_id' => '','THEME_COLOUR'=>'red','VIEW_DEPT_ID'=>'','VIEW_UID'=>'','VIEW_PRIV_ID'=>'','FAX_NO_DEPT'=>'','ADD_HOME'=>'','POST_NO_HOME'=>'','TEL_NO_HOME'=>'','BP_NO'=>'','OICQ_NO'=>'','ICQ_NO'=>'','MSN'=>'','PORTAL'=>'','BKGROUND'=>'','LAST_VISIT_IP'=>'','MENU_EXPAND'=>'','MY_STATUS'=>'','MANAGE_MODULE_TYPE'=>'','MOBIL_NO_BD'=>'');
$email_audit_flag = check_email_audit(3);
if($email_audit_flag)
{
    $ARRAY["SECRET_LEVEL"] = intval($SECRET_LEVEL);
}

if($_SESSION['MYOA_IS_GROUP']=='1' && $_SESSION['LOGIN_USER_PRIV_TYPE']=='1')
{
    $ARRAY['VIEW_DEPT_ID'] = $VIEW_DEPT_ID;
    //$ARRAY['VIEW_UID'] = $VIEW_UID;
    //$ARRAY['VIEW_PRIV_ID'] = $VIEW_PRIV_ID;
}
$UID = add_user($ARRAY);
if ($UID === -1) {
    exit(json_encode(array('code' => 1014, 'msg' => 'OA用户数已经达到上限100人，请联系管理员注册OA系统！')));
} else if ($UID == 0) {
    exit(json_encode(array('code' => 1015, 'msg' => '新建用户失败，请稍后重试！')));
}
if ($email_audit_flag && (intval($SECRET_LEVEL) != intval($SECRET_LEVEL_OLD))) {
    $log_data = array("src" => intval($SECRET_LEVEL_OLD), "des" => intval($SECRET_LEVEL), "change_user" => $USER_ID);
    addEmailAuditLog(72, $_SESSION["LOGIN_UID"], $log_data);
}
$query = "insert into USER_EXT(UID,USER_ID,USE_POP3,EMAIL_CAPACITY,FOLDER_CAPACITY,WEBMAIL_CAPACITY,WEBMAIL_NUM,DUTY_TYPE,USE_EMAIL) values('$UID','$UID','$USE_POP3','$EMAIL_CAPACITY','$FOLDER_CAPACITY','$WEBMAIL_CAPACITY','$WEBMAIL_NUM','$DUTY_TYPE','$USE_EMAIL')";
exequery(TD::conn(), $query);
include_once "inc/attendance/attend.setting.funcs.php";
$setting = new AttendSetting();
$data_info[] = array("user_id" => $UID, "priv_id" => $USER_PRIV, "to_id" => $DEPT_ID, "type" => 0);
$setting->set_userduty($data_info);
if ($NOT_LOGIN == 0) {
    set_sys_para(array("ORG_UPDATE" => date("Y-m-d H:i:s")));
}

if (($PRIV_ID1 != "") || ($USER_ID1 != "") || ($DEPT_ID_MSG != "")) {
    $query = "select * from MODULE_PRIV where UID='$UID' and MODULE_ID='0'";
    $cursor = exequery(TD::conn(), $query);

    if ($ROW = mysqli_fetch_array($cursor)) {
        $query = "update MODULE_PRIV set PRIV_ID='$PRIV_ID1',USER_ID='$USER_ID1',DEPT_ID='$DEPT_ID_MSG' where UID='$UID' and MODULE_ID='0'";
        exequery(TD::conn(), $query);
    } else {
        $query = "insert into MODULE_PRIV (UID,MODULE_ID,DEPT_PRIV,ROLE_PRIV,PRIV_ID,USER_ID,DEPT_ID) values('$UID','0','1','2','$PRIV_ID1','$USER_ID1','$DEPT_ID_MSG')";
        exequery(TD::conn(), $query);
    }
} else {
    $query = "delete from MODULE_PRIV where UID='$UID' and MODULE_ID='0'";
    exequery(TD::conn(), $query);
}

if (file_exists("fis_acset_update.php")) {
    include_once "fis_acset_update.php";
}


cache_users();
TD::cache()->delete_specify("MY_PRIV");

exit(json_encode(array('code' => 0, 'msg' => '成功', 'data' => array('user_id' => $UID))));

