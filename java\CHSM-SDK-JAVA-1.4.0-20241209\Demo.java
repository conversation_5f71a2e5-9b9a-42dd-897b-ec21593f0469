package org.example;
import kl.hsm.client.model.SessionKey;
import kl.hsm.client.sdk.HsmClient;
import kl.hsm.client.sdk.HsmSdk;
import kl.hsm.common.KeyType;
import kl.hsm.server.svc.base.*;
import org.apache.thrift.TException;
import org.bouncycastle.util.encoders.Hex;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

public class Demo {
    // 加解密使用的到key和iv，实际应该生成一个16字节会话密钥并保存，作为IV使用
    private final static byte[] IV_16 = new byte[]{1, 2, 3, 4, 5, 6, 7, 8, 1, 2, 3, 4, 5, 6, 7, 8};

    // 私钥授权码-管理页面创建非对称密钥时填入
    private static String password = "pass";
    //所有HsmSdk对象共享一个连接池，所以只创建一个即可，支持多线程
    private static HsmClient hsmClient = new HsmClient();
    static {
        try {
            hsmClient.init("172.43.147.100", 10000);
        } catch (SvcException e) {
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) throws Exception {

        try {
            //建立会话获得会话ID

            //获取一个16字节的随机数,作为测试明文数据
            byte[] data = hsmClient.generateRandom(16);

            System.out.println("随机数:" + Hex.toHexString(data));
            byte[] text = "12345678".getBytes(StandardCharsets.UTF_8);
            System.out.println("明文："+ new String(text));

            //组装对称加密参数
            SymParam symParam = new SymParam();
            //工作模式
            symParam.setMode(EncMode.CBC);
            //初始向量
            symParam.setIv(IV_16);
            //设置补码
            symParam.setPadding(Padding.PKCS7Padding);
            //内部密钥加解密
            byte[] bytes = hsmClient.encrypt(1, symParam, text);
            System.out.println("内部对称密钥加密:" + Hex.toHexString(bytes));
            byte[] bytes1 = hsmClient.decrypt(1, symParam, bytes);
            System.out.println("内部对称密钥解密:" + new String(bytes1));

            //完整性保护hmac方法
            byte[] hmac = hsmClient.hmac(2, Algo.SM3, text);
            System.out.println("hmac:" + Hex.toHexString(hmac));



            //建立会话获得会话ID
            Long sessionId = hsmClient.openSession();
            //生成16字节的会话密钥(使用内部加密密钥（索引为1）加密会话密钥)。
            SessionKey sessionKey = hsmClient.generateSessionKey(sessionId, 2, KeyType.SYMMETRIC, 16);

            System.out.println("会话密钥密文:" + Hex.toHexString(sessionKey.getSessionKeyCipher()));

            sdk.closeSession(sessionId);
        } catch (TException e) {
            if (e instanceof SvcException) {
                ((SvcException) e).getErrCode();//错误码
                ((SvcException) e).getErr();//错误信息
            } else {
                // 根据业务自行处理
            }
        }
    }
}