<?php
//declare (strict_types = 1);

namespace app\admin\model;

use think\Model;

/**
 * @mixin \think\Model
 */
class TopCityModel extends Model
{
	//

	public $table = 'top_city';


	public function city_user(){
		return $this->hasOne(TopCityUserModel::class, 'city_id', 'id');
	}

	/**
	 * 保存数据
	 * @param $model
	 * @param $data
	 * @param int $id
	 * @return string
	 */
	public function newSave($model, $data, $id = 0)
	{
		$check = $this->checkName($data['name'], intval($id));
		if ($check) {
			if ($model->allowField(['name', 'status', 'tel', 'address', 'pcas', 'contacts'])->save($data)) {
				return '数据处理成功';
			} else {
				return '数据处理失败';
			}
		}
		return '名称重复';
	}

	/**
	 * 检测名称是否重复
	 * @param $name
	 * @param int $id
	 * @return bool
	 */
	public function checkName($name, $id = 0)
	{
		if ($id) $where[] = ['id', '<>', $id];
		$where[] = ['name', '=', $name];
		$where[] = ['del_flag', '=', 0];
		$data = $this->where($where)->find();
		return !$data;
	}

	/**
	 * 获取列表数据
	 * @param $where
	 * @param int $page
	 * @param int $limit
	 * @param bool $export
	 * @return mixed
	 */
	public function getList($where, $page = 1, $limit = 10, $export = false)
	{
		$whereArr[] = ['del_flag', '=', 0];
		if (isset($where['name']) && $where['name'] != '') {
			$whereArr[] = ['name', 'like', "%" . $where['name'] . "%"];
		}
		$pcaModel = new TopPcaModel();
		if ($export) { // 导出标记
			$data = $this->where($whereArr)->select()->each(function ($item, $index) use ($pcaModel){
				$item->statusStr = $this->getStatusStr($item->status);
				$item->pcas_ids = explode(',', $item->pcas);
				$item->pcas_names = $pcaModel->getNames($item->pcas_ids);
				return $item;
			})->toArray();
			return $data;
		}
		$data = $this->where($whereArr)->paginate($limit)->each(function ($item, $index) use ($pcaModel){
			$item->statusStr = $this->getStatusStr($item->status);
			$item->pcas_ids = explode(',', $item->pcas);
			$item->pcas_names = $pcaModel->getNames($item->pcas_ids);
			return $item;
		});
		return $data;
	}

	public function getInfo()
	{

	}

	/**
	 * 获取状态
	 * @param string $status
	 * @return string
	 */
	public function getStatusStr($status = '')
	{
		$str = '';
		switch ($status) {
			case 0:
				$str = '未使用';
				break;
			case 1:
				$str = '正常';
				break;
			default:
				break;
		}
		return $str;
	}

	/**
	 * 删除
	 * @param $model
	 * @return string
	 */
	public function delData($model)
	{
		$data = array('del_flag' => 1);
		if ($model->save($data)) {
			return '数据处理成功';
		}
		return '数据处理失败';
	}

	/**
	 * 获取表头字段
	 * @return array
	 */
	public function getTitle()
	{
		$title = [
			['title' => '名称', 'field' => 'name', 'width' => '30', 'type' => 'string'],
			['title' => '状态', 'field' => 'statusStr', 'width' => '30', 'type' => 'string'],
			['title' => '联系人', 'field' => 'contacts', 'width' => '30', 'type' => 'string'],
			['title' => '联系电话', 'field' => 'tel', 'width' => '30', 'type' => 'string'],
			['title' => '地址', 'field' => 'address', 'width' => '60', 'type' => 'string'],
			['title' => '所属行政区', 'field' => 'pcas_names', 'width' => '100', 'type' => 'string'],
		];
		return $title;
	}

	/**
	 * 导入数据
	 * @param $data
	 * @param $dataHeader
	 * @return mixed
	 */
	public function importData($data, $dataHeader)
	{
		$fields = $this->getTitle();
		foreach ($dataHeader as $k => $v) {
			foreach ($fields as $k1 => $v1) {
				if ($v1['title'] == $v) {
					$tit[$k] = $v1['field'];
				}
			}
		}
		foreach ($data as $k => $v) {
			$tmp = [];
			foreach ($v as $k1 => $v1) {
				$tmp[$tit[$k1]] = str_replace(' ', '', $v1);
			}
			$dataList[] = $tmp;
		}
		foreach ($dataList as $v) {
			if (!empty($v['name'])) {
				$datauser[] = $v;
			}
		}

		foreach ($datauser as $k => $v) {
			$v['status'] = 1;
			$re = $this->newSave(new self(), $v);
			if ($re == '数据处理成功') {
				$result['success'][] = ['row' => $k + 2, 'name' => $v['name'], 'msg' => '数据处理成功'];
			} else if ($re == '名称重复') {
				$result['repeat'][] = ['row' => $k + 2, 'name' => $v['name'], 'msg' => '重复'];
			} else {
				$result['fail'][] = ['row' => $k + 2, 'name' => $v['name'], 'msg' => $re];
			}
		}
		return $result;
	}
}
