<?php

namespace app\validate;

use think\Validate;

class CompanyVerify extends Validate
{
    protected $rule = [
        'id' => 'require|number',
        'name' => 'require',
        'reg_address' =>'require',
        'reg_address_info' => 'require',
        'license' => 'require',
        'aoc' => 'require',
        'operate_address' => 'require',
        'operate_address_info' => 'require',
        'legal' => 'require',
        'legal_mobile' => 'require',
        'legal_email' => 'require',
        'fax' => 'require',
        'postal_code' => 'require',
        'economy_sector' => 'require',
        'industry' => 'require',
        'license_number' => 'require',
        'license_start' => 'require',
        'license_end' => 'require|date',
        'economy_type' => 'require',
        'enterprise_size' => 'require',
        'reg_money' => 'require',
        'manager' => 'require',
        'manager_mobile' => 'require',
        'manager_email' => 'require',
        'date' => 'require|date',
        'fixed_asset' => 'require',
        'revenue' => 'require',
        'personnel' => 'require|number',
        'area' => 'require|number',
        'personnel_full' => 'require|number',
        'personnel_part' => 'require|number',
        'personnel_special' => 'require|number',
        'business' => 'require',
    ];

    protected $message = [
        'id' => '参数错误',
        'name' => '请填写企业名称',
        'reg_address' =>'请选择注册地址',
        'reg_address_info' => '请填写注册地址',
        'license' => '请上传营业执照',
        'aoc' => '请上传安全行政许可资料',
        'operate_address' => '请选择生产经营地点',
        'operate_address_info' => '请填写生产经营地点',
        'legal' => '请填写法定代表人',
        'legal_mobile' => '请填写法人联系电话',
        'legal_email' => '请填写法人邮箱',
        'fax' => '请填写传真',
        'postal_code' => '请填写邮政编码',
        'economy_sector' => '请选择国民经济行业',
        'industry' => '请选择行业/专业',
        'license_number' => '请填写统一社会信用代码',
        'license_start' => '请选择信用代码日期',
        'license_end' => '请选择信用代码日期',
        'economy_type' => '请选择经济类型',
        'enterprise_size' => '请选择企业规模',
        'reg_money' => '请填写注册资本',
        'manager' => '请填写安全管理联系人',
        'manager_mobile' => '请填写安全管理联系人联系方式',
        'manager_email' => '请填写安全管理联系人邮箱',
        'date' => '请选择成立日期',
        'fixed_asset' => '请填写固定资产',
        'revenue' => '请填写年营业收入',
        'personnel' => '请填写员工总数',
        'area' => '请填写营业场所面积',
        'personnel_full' => '请填写专职安全管理人数',
        'personnel_part' => '请填写兼职安全管理人数',
        'personnel_special' => '请填写特种作业人数',
        'business' => '请填写经营范围',
    ];

    protected $scene = [
        'save' => ['name','reg_address','reg_address_info','license','operate_address','operate_address_info','legal','economy_sector','industry','license_number','license_start','license_end','economy_type','enterprise_size','reg_money','manager','manager_mobile','manager_email','date','revenue','personnel','area','personnel_full','personnel_part','personnel_special','business'],
    ];

    public function userCheck($scene,$data){
        $validate = new self();
        $re = $validate->scene($scene)->check($data);
        if(!$re){
            result('',1001,$validate->getError());
//            if(request()->isAjax()){
//                result('',1001,$validate->getError());
//            }else{
//                return $validate->getError();
//            }
        }
    }

}