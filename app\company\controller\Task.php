<?php
declare (strict_types = 1);

namespace app\company\controller;

use app\model\FileModel;
use Endroid\QrCode\QrCode;
use think\facade\Db;
use think\facade\View;
use Html2image\Assets\Html2img;
use think\facade\request;

class Task extends Base
{

    /**
     * @Apidoc\Title("评审任务")
     * @Apidoc\Desc("评审任务")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("日程")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function index($limit=20) {
        if (request()->isAjax()) {
            $title = $this->request->param('title','','trim');
            $where = [
                ['a.company_id','=',$_SESSION['company']['id']],
            ];
            if(!empty($title)){
                $where[] = ['a.company_name','like',"%{$title}%"];
            }
            $field = "b.id,a.company_id,a.company_name,a.level,b.date,b.status,b.integrity_code";
            $res = Db::table('top_org_tasks')->alias('b')
                ->leftJoin('top_grading a','a.id = b.grading_id')
                ->where($where)->order('b.date desc')
                ->field($field)
                ->paginate($limit)->each(function ($item, $key) {
                    $e = Db::table('top_org_tasks_experts')->where(['tasks_id'=>$item['id']])->column('expert_name');
                    $item['element_name'] = empty($e)?'未分配':implode('，',$e);
                    // 查询满意度调查状态
                    $mydStatus = Db::table('top_org_tasks_myd')->where(['tasks_id'=>$item['id']])->find();
                    $item['satisfaction_survey_status'] = $mydStatus ? 1 : 0;
                    // 查询廉政问卷状态 - 检查任务表中的integrity_code字段
                    $taskIntegrity = isset($item['integrity_code'])?$item['integrity_code']:'';
                    $item['integrity_survey_status'] = !empty($taskIntegrity) ? 1 : 0;
                    return $item;
                });
            result($res);
        } else {
            return view();
        }
    }
    /**
     * 查看廉政问卷
     */
    public function viewIntegritySurvey()
    {
        try {
            $taskId = request()->param('task_id', 0);

            if (!$taskId) {
                result('', 1, '缺少任务ID');
            }

            // 查询任务中的廉政问卷文件编码
            $task = Db::table('top_org_tasks')
                ->where('id', $taskId)
                ->find();

            if (!$task) {
                result('', 1, '任务不存在');
            }

            if (empty($task['integrity_code'])) {
                result('', 1, '未找到廉政问卷文件');
            }

            // 根据文件编码获取文件信息
            $fileRecord = Db::table('top_files')
                ->where('code', $task['integrity_code'])
                ->find();

            if (!$fileRecord) {
                result('', 1, '文件记录不存在');
            }

            // 判断文件类型
            $fileExt = strtolower($fileRecord['fileext']);
            $isPdf = $fileExt === 'pdf';

            // 构建文件访问URL
            $fileUrl = '/general/toppingsoft/index.php/file/info?code=' . $fileRecord['code'];

            $result = [
                'file_name' => $fileRecord['filename'],
                'file_ext' => $fileExt,
                'file_url' => $fileUrl,
                'is_pdf' => $isPdf,
                'file_size' => $fileRecord['filesize']
            ];

            result($result, 0, '查询成功');

        } catch (\Exception $e) {
            result('', 1, '查询过程中发生错误：' . $e->getMessage());
        }
    }

    /**
     * 廉政问卷文件上传
     */
    public function uploadIntegrity($model = 'org')
    {
        $file = request()->file('file');
        $result = FileModel::upload($file, $model);
        result($result);
    }

    /**
     * 查看满意度调查
     */
    public function viewSatisfactionSurvey()
    {
        try {
            $taskId = request()->param('task_id', 0);

            if (!$taskId) {
                result('', 1, '缺少任务ID');
            }

            // 查询满意度调查数据
            $surveyData = Db::table('top_org_tasks_myd')
                ->where('tasks_id', $taskId)
                ->find();

            if (!$surveyData) {
                result('', 1, '未找到满意度调查数据');
            }

            // 格式化数据
            $result = [
                'company_name' => $surveyData['company_name'],
                'field2' => $surveyData['field2'], // 填表时间
                'field3' => $surveyData['field3'], // 填表人及联系电话
                'field4' => $surveyData['field4'], // 现场评审人员是否存在违法廉洁纪律行为
                'field5' => $surveyData['field5'], // 您对本次评审成绩是否满意
                'field6' => $surveyData['field6'], // 您对本次评审人员的评审工作是否满意
                'field7' => $surveyData['field7'], // 您的意见与建议
                'time' => $surveyData['time'] // 提交时间
            ];

            result($result, 0, '查询成功');

        } catch (\Exception $e) {
            result('', 1, '查询过程中发生错误：' . $e->getMessage());
        }
    }

    /**
     * 廉政问卷提交
     */
    public function submitIntegrity()
    {
        try {
            $taskId = request()->param('task_id', 0);
            $fileInfo = request()->param('file_info', []);

            if (!$taskId) {
                result('', 1, '缺少任务ID');
            }

            if (empty($fileInfo) || empty($fileInfo['id'])) {
                result('', 1, '请先上传廉政问卷文件');
            }

            // 检查任务是否存在
            $task = Db::table('top_org_tasks')->where('id', $taskId)->find();
            if (!$task) {
                result('', 1, '任务不存在');
            }

            // 根据文件ID获取文件信息
            $fileRecord = Db::table('top_files')->where('code', $fileInfo['code'])->find();
            if (!$fileRecord) {
                result('', 1, '文件记录不存在');
            }

            // 更新任务表中的廉政问卷路径字段，保存文件的相对路径
            $result = Db::table('top_org_tasks')
                ->where('id', $taskId)
                ->update([
                    'integrity_code' => $fileRecord['code'],
                    'integrity_time' => date('Y-m-d H:i:s')
                ]);

            if ($result !== false) {
                result('', 0, '廉政问卷提交成功');
            } else {
                result('', 1, '提交失败，请重试');
            }

        } catch (\Exception $e) {
            result('', 1, '提交过程中发生错误：' . $e->getMessage());
        }
    }

    public function companyInfo($id = 0)
    {
        \app\model\CompanyModel::getCompanyInfo($id);
        exit();
    }

    public function getReview($element_id = 0)
    {
        if (request()->isAjax()) {
            $request = $this->request->post();
            \app\model\CompanyModel::getAjaxReview($element_id, $request);
            exit();

        } else {
            View::assign('element_id', $element_id);
            View::assign('title', '首页');
            return view();
        }
    }

}
