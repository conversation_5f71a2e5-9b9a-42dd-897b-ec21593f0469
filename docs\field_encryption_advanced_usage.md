# 数据库字段加密功能 - 高级使用说明

## 概述

本文档介绍了数据库字段加密功能的高级特性，包括Redis缓存优化、加密状态判断、安全保护机制等。

## 核心特性

### 1. 高效的加密状态判断

系统使用多层次的加密状态判断机制：

#### 缓存优先策略
```php
// 1. 首先检查Redis缓存
$isEncrypted = $encryptionService->isFieldEncrypted($table, $field, $recordId, $value);

// 2. 如果缓存未命中，使用启发式检测
// 3. 将检测结果缓存到Redis
```

#### 启发式检测规则
- **Base64编码检测**：检查是否为有效的Base64编码
- **长度范围检测**：加密后数据通常在32-1024字节范围内
- **字符集检测**：检查是否符合加密数据的字符特征
- **模式匹配**：检查HSM特定的前缀/后缀
- **排除规则**：排除明显的明文特征（中文、纯数字、邮箱等）

### 2. Redis安全存储

#### 键值对安全存储
```php
// 原始值哈希处理
$originalHash = hash('sha256', $originalValue . config('app.app_key'));

// 加密值二次保护
$protectedEncrypted = $this->protectEncryptedValue($encryptedValue);

// 安全存储到Redis
$this->storeEncryptionMapping($table, $field, $recordId, $originalValue, $encryptedValue);
```

#### 缓存键安全生成
```php
// 使用SHA256哈希生成安全的缓存键
$rawKey = "{$table}:{$field}:{$recordId}";
$hashedKey = hash('sha256', $rawKey);
$cacheKey = "field_encrypt:status:" . $hashedKey;
```

### 3. 配置化管理

所有功能都支持通过配置文件进行自定义：

```php
// config/field_encryption.php
return [
    'redis' => [
        'prefix' => 'field_encrypt:',
        'expire' => 86400,
        'batch_size' => 1000,
    ],
    'detection' => [
        'heuristic' => [
            'min_length' => 32,
            'max_length' => 1024,
            'check_base64' => true,
        ],
        'exclude_patterns' => [
            '/^\d+$/',                    // 纯数字
            '/^[a-zA-Z\s]+$/',           // 纯英文
            '/[\x{4e00}-\x{9fa5}]/u',    // 中文字符
        ],
    ],
    'security' => [
        'cipher' => 'AES-256-CBC',
        'protect_suffix' => 'encrypt_protect',
    ],
];
```

## 使用示例

### 1. 基本加密操作

```javascript
// 前端调用
axios.post('/admin/field_encryption/encryptFields', {
    table_name: 'user_info',
    fields: ['name', 'phone', 'email'],
    where_condition: 'status = 1 AND created_time > "2023-01-01"'
}).then(res => {
    console.log('加密结果:', res.data);
});
```

### 2. 查看字段统计

```javascript
// 获取字段加密统计
axios.get('/admin/field_encryption/getEncryptionStats', {
    params: {
        table_name: 'user_info',
        field_name: 'phone'
    }
}).then(res => {
    const stats = res.data.data;
    console.log(`总记录: ${stats.total}`);
    console.log(`已加密: ${stats.encrypted}`);
    console.log(`加密率: ${stats.encryption_rate}%`);
});
```

### 3. 清除缓存

```javascript
// 清除特定字段的缓存
axios.post('/admin/field_encryption/clearEncryptionCache', {
    table_name: 'user_info',
    field_name: 'phone'
}).then(res => {
    console.log('缓存清除成功');
});
```

## 性能优化

### 1. 批量处理

系统支持批量处理大量数据：

```php
// 配置批量处理大小
'performance' => [
    'batch_size' => 1000,
    'max_concurrent' => 10,
],
```

### 2. 内存管理

```php
// 启用内存限制检查
'performance' => [
    'memory_limit_check' => true,
    'max_memory_usage' => 80, // 最大内存使用率80%
],
```

### 3. 缓存策略

```php
// Redis缓存配置
'redis' => [
    'expire' => 86400,        // 24小时过期
    'prefix' => 'field_encrypt:',
    'batch_size' => 1000,
],
```

## 安全机制

### 1. 数据保护

- **原始值哈希**：原始数据不直接存储，使用SHA256哈希
- **加密值保护**：加密后的数据进行二次AES加密保护
- **缓存键哈希**：所有缓存键都经过哈希处理

### 2. 权限控制

```php
// 白名单配置
'whitelist' => [
    'tables' => ['user_info', 'customer_data'],
    'field_types' => ['varchar', 'text'],
    'forbidden_fields' => ['id', 'password'],
],
```

### 3. 操作验证

```php
// 验证配置
'validation' => [
    'pre_operation' => [
        'check_table_exists' => true,
        'check_field_exists' => true,
        'check_permissions' => true,
    ],
],
```

## 监控和日志

### 1. 操作日志

```php
'logging' => [
    'enabled' => true,
    'level' => 'info',
    'operations' => [
        'encrypt' => true,
        'decrypt' => true,
    ],
],
```

### 2. 性能监控

```php
'monitoring' => [
    'enabled' => true,
    'slow_threshold' => 5,      // 慢操作阈值5秒
    'error_rate_threshold' => 0.1,
],
```

## 故障恢复

### 1. 自动恢复

```php
'recovery' => [
    'auto_recovery' => false,
    'strategy' => 'rollback',   // rollback, retry, skip
    'max_retries' => 3,
],
```

### 2. 备份机制

```php
'backup' => [
    'auto_backup' => false,
    'retention_days' => 7,
    'storage_path' => 'backup/field_encryption/',
],
```

## 最佳实践

### 1. 操作前准备

1. **数据备份**：执行大批量操作前务必备份数据
2. **测试验证**：在测试环境中验证配置和功能
3. **性能评估**：评估操作对系统性能的影响

### 2. 配置优化

1. **缓存设置**：根据数据量调整缓存过期时间
2. **批量大小**：根据服务器性能调整批量处理大小
3. **检测规则**：根据实际加密特征调整启发式规则

### 3. 安全考虑

1. **密钥管理**：确保应用密钥的安全性
2. **网络安全**：使用HTTPS传输敏感数据
3. **访问控制**：限制功能访问权限

### 4. 监控维护

1. **定期检查**：定期检查加密状态和统计信息
2. **缓存清理**：定期清理过期的缓存数据
3. **日志分析**：分析操作日志发现潜在问题

## 常见问题

### Q1: 如何提高加密状态判断的准确性？

A: 可以通过以下方式优化：
- 调整启发式检测规则
- 增加HSM特定的模式匹配
- 完善排除规则

### Q2: Redis缓存占用过多内存怎么办？

A: 可以采取以下措施：
- 减少缓存过期时间
- 增加批量清理机制
- 使用Redis的内存优化配置

### Q3: 大批量操作时性能较慢？

A: 可以通过以下方式优化：
- 调整批量处理大小
- 使用异步处理
- 优化数据库查询

### Q4: 如何确保数据安全？

A: 系统提供多层安全保护：
- 原始数据哈希存储
- 加密数据二次保护
- 缓存键哈希处理
- 访问权限控制

## 技术支持

如有技术问题，请参考：
- 基础使用文档：`docs/field_encryption_usage.md`
- 配置文件：`config/field_encryption.php`
- 测试脚本：`test/field_encryption_test.php`
