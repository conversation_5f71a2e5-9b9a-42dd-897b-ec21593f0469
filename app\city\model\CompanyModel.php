<?php


namespace app\city\model;


use app\model\FileModel;
use think\db\exception\DbException;
use think\Model;
use think\App;
use think\facade\Db;
use app\model\SettingModel;

//权限配置模块
class CompanyModel extends Model
{

    //企业状态
    public static $standStatusText = [
        'info' => '未创标',
        'primary' => '试运行中',
        'success' => '持续运行中',
    ];

    //证书状态
    public static $caStatusText = [
        'warning' => '未取得',
        'success' => '已取得',
        'danger' => '已过期',
    ];

    //参数格式化
    public static function codeToText($info)
    {
        if (empty($info)) {
            return $info;
        }
        $pcas = SettingModel::getPcasAll();
        $info['reg_address'] = empty($info['reg_address']) ? [] : explode(',', $info['reg_address']);
        $info['mb_reg_address'] = '';
        foreach ($info['reg_address'] as $k => $v) {
            $info['mb_reg_address'] .= $pcas[$v];
        }
        $info['mb_reg_address'] .= $info['reg_address_info'];
        $info['operate_address'] = empty($info['operate_address']) ? [] : explode(',', $info['operate_address']);
        $info['mb_operate_address'] = '';
        foreach ($info['operate_address'] as $k => $v) {
            $info['mb_operate_address'] .= $pcas[$v];
        }
        $info['mb_operate_address'] .= $info['operate_address_info'];
        $info['region'] = empty($info['region']) ? [] : explode(',', $info['region']);
        $info['mb_region'] = '';
        foreach ($info['region'] as $k => $v) {
            $info['mb_region'] .= $pcas[$v];
        }
        $info['economy_sector'] = explode(',', $info['economy_sector']);
        $info['mb_economy_sector'] = implode('/', $info['economy_sector']);
        $info['economy_type'] = explode(',', $info['economy_type']);
        $info['mb_economy_type'] = implode('/', $info['economy_type']);
        $info['license_date'] = $info['license_start'] . '至' . $info['license_end'];
        $info['licenseUrl'] = empty($info['license']) ? '' : FileModel::getFile(0, $info['license']);
        $info['aocUrl'] = empty($info['aoc']) ? '' : FileModel::getFile(0, $info['aoc']);
        foreach ($info as $k => $v) {
            $info[$k] = $v === '0000-00-00' ? '' : $v;
            if (is_int($v)) {
                $info[$k] = (string)$v;
            }
            if ($v === null) {
                $info[$k] = '';
            }
        }
        //非企业端 查看信息无需展示认证中的信息,则直接使用的&$info
        \app\model\CompanyModel::getCompanyAuthInfo($info);

        return $info;
    }

    /**
     * 获取表头字段
     * @return array
     */
    public function getTitle()
    {
        $title = [
            ['title' => 'ID', 'field' => 'id', 'width' => '20', 'type' => 'integer'],
            ['title' => '所属区县', 'field' => 'mb_region', 'width' => '80', 'type' => 'string'],
            ['title' => '企业名称', 'field' => 'name', 'width' => '100', 'type' => 'string'],
            ['title' => '生产经营地址', 'field' => 'mb_operate_address', 'width' => '100', 'type' => 'string'],
            ['title' => '企业状态', 'field' => 'mb_status_text', 'width' => '30', 'type' => 'string'],
            ['title' => '是否取得证书', 'field' => 'ca_status_text', 'width' => '30', 'type' => 'string'],
            ['title' => '行业/专业', 'field' => 'industry', 'width' => '100', 'type' => 'string'],
            ['title' => '企业规模', 'field' => 'enterprise_size', 'width' => '20', 'type' => 'string'],
        ];
        return $title;
    }

    /**
     * 获取企业状态文本
     * @param $stand_status
     * @param $ca_status
     * @return string
     * Author: 思密达か宁采臣
     */
    public static function getStatusText($stand_status, $ca_status)
    {
        if ($stand_status == 0) {
            return self::$standStatusText['info'];
        } elseif ($stand_status == 1 && $ca_status == 0) {
            return self::$standStatusText['primary'];
        } elseif ($stand_status == 1 && $ca_status == 1) {
            return self::$standStatusText['success'];
        } else {
            return '';
        }
    }

    /**
     * 获取证书状态文本
     * @param $ca_status
     * @return string
     * Author: 思密达か宁采臣
     */
    public static function getCaStatusText($ca_status)
    {
        if ($ca_status == 0) {
            return self::$caStatusText['warning'];
        } elseif ($ca_status == 1) {
            return self::$caStatusText['success'];
        } else {
            return self::$caStatusText['danger'];
        }
    }

}