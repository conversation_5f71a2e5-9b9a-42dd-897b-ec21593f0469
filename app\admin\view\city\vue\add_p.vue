<style>
.my-autocomplete li {
  line-height: normal;
  padding: 7px;
}

.my-autocomplete li .name {
  text-overflow: ellipsis;
  overflow: hidden;
}

.my-autocomplete li .addr {
  font-size: 12px;
  color: #b4b4b4;
}

.my-autocomplete li .highlighted {
  color: #ddd;
}

.el-cascader {
  width: 100%;
}

.el-form-item__content .el-input-group {
  vertical-align: middle;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 150px;
  height: 150px;
  line-height: 150px;
  text-align: center;
}

.avatar {
  width: 150px;
  height: 150px;
  display: block;
}

.el-date-editor.el-input, .el-date-editor.el-input__inner {
  width: auto;
}
</style>
<template>
  <el-dialog :title="title" :close-on-click-modal="false" modal="false" :visible.sync="visible" width="90%" top="50px"
             @close="refresh()" append-to-body="true" label-position="top">
    <el-form ref="form" :model="data" class="tableSearch" label-position="right" label-width="170px" :rules="rules">
      <el-row :gutter="20">
        <el-col :span="10">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="data.name" size="mini"></el-input>
          </el-form-item>
          <el-form-item label="用户名" prop="username">
            <el-input v-model="data.username" size="mini"></el-input>
          </el-form-item>

        </el-col>
        <el-col :span="10">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="data.email" size="mini"></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input v-model="data.mobile" size="mini"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="所属应急局" prop="area_id">
            <el-select v-model="data.city_id" size="mini" placeholder="请选择">
              <el-option v-for="v in config.city" :key="v.id" :label="v.name" :value="v.id">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="data.status">
              <el-radio :label="1">正常</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="科室" prop="department">
            <el-select v-model="data.department" size="mini" placeholder="请选择">
              <el-option :key="1" label="基础处" :value="1"></el-option>
              <el-option :key="2" label="危化处" :value="2"></el-option>
              <el-option :key="3" label="综合协调处" :value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否管理员" prop="role">
            <el-radio-group v-model="data.role">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>

        </el-col>
        <el-col :span="10">
          <el-form-item label="密码" prop="password" v-if="!data.id">
            <el-input v-model="data.password" size="mini"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24" style="text-align: center;">
          <el-button type="primary" @click="submit()">保存</el-button>
          <el-button @click="visible = false">关闭</el-button>
        </el-col>
      </el-row>
    </el-form>
  </el-dialog>
</template>
<script>
module.exports = {
  name: "add",
  // 模板导入区
  components: {},
  data: function () {
    return {
      id: 0,
      isAdmin: false,
      visible: false,
      title: '新增',
      model: '',
      loading: false,
      user_id: 0,
      date_object: new Date(),
      user_name: '',
      data: {},
      rules: {
        /*username: [
            {required: true, message: '请输入用户名', trigger: 'blur'},
            {pattern: /[^\d]/g, message: '用户名不能全是数字', trigger: 'blur'},
            {pattern: /^[\u4e00-\u9fa5_a-zA-Z0-9]{4,20}$/, message: '请输入4-20位中英文/数字/下划线', trigger: 'blur'},
        ],*/
        name: [
          {required: true, message: '请输入姓名', trigger: 'blur'}
        ],
        mobile: [
          {required: true, message: '请输入电话号码', trigger: 'blur'},
          {pattern: /^((0\d{2,3}-\d{7,8})|(1[345789]\d{9}))$/, message: '请输入正确的电话号码', trigger: 'blur'}
        ],
        /*email: [
          {required: true, message: '请输入邮箱', trigger: 'blur'},
          {
            pattern: /^([a-zA-Z0-9]+[-_\.]?)+@[a-zA-Z0-9]+\.[a-z]+$/,
            message: '请输入正确的邮箱地址',
            trigger: 'blur'
          }
        ],*/
        /*password: [
            {required: true, message: '请输入密码', trigger: 'blur'},
            {
              pattern:/^(?![\d]+$)(?![a-zA-Z]+$)(?![^\da-zA-Z]+$)([^\u4e00-\u9fa5\s]){8,20}$/,
                message: '请输入8-20位大/小写字母+数字+特殊字符',
                trigger: 'blur'
            },
        ],*/
        city_id: [
          {required: true, message: '请选择所属应急局', trigger: 'blur'}
        ]

      },
      config: [],
      contractData: [],
      salesData: [],
      userData: [],
      dels: [],
      dels2: [],
      imageUrl: '',
      url: {
        'save': 'save_data_p',
      }
    }
  },
  mounted: function () {
    // this.getConfig();
  },
  created: function () {
  },
  methods: {
    handleChange(value) {
      var data = [];
      for (var i = 0; i < 3; i++) {
        data.push(value[i]);
      }
      this.data.region = data;
    },
    /**
     * 打开弹窗调用方法
     * */
    open: function (row, config) {
      row = row ? row : {id: 0};
      var _this = this;
      _this.visible = true;
      _this.data = row;
      _this.config = config;
    },
    submit: function () {
      var _this = this;
      var param = _this.data;
      var url = this.url.save;
      this.$refs.form.validate(function (valid) {
        if (valid) {
          axios.post(url, param).then(function (res) {
            _this.$message({
              message: res.data.msg,
              type: res.data.type
            });
            if (res.data.code == 0) {
              _this.visible = false;
              _this.$emit("ok");
            }
          }).catch(function (error) {
            console.log("出现错误:", error);
          });
        }
      });
    },
    refresh: function () {
      this.$emit("refresh");
    },
    changeweekBegin(data) {
      console.log(data);
    },
  }
}
</script>