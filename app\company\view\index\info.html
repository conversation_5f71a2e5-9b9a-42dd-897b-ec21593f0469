<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>企业信息认证</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/company.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .my-info { line-height:40px;}
        .my-info label { width:160px; display: inline-block;text-align:right;}
        .success .el-descriptions__header {
            border-left:5px solid #67c23a;
            color:#67c23a;
            line-height: 40px;
            padding-left:20px;
            background: -ms-linear-gradient(left, #f0f9eb, #fff); /* IE 10+ */
            background: -moz-linear-gradient(left, #f0f9eb, #fff); /* Firefox */
            background: -webkit-linear-gradient(left, #f0f9eb, #fff); /* Chrome, Safari, Opera */
            background: linear-gradient(to right, #f0f9eb, #fff); /* 标准语法 */
        }
        .primary .el-descriptions__header {
            margin-top: 20px;
            border-left:5px solid #436CE6;
            color:#436CE6;
            line-height: 40px;
            padding-left:20px;
            background: -ms-linear-gradient(left, #F4F7FE, #fff); /* IE 10+ */
            background: -moz-linear-gradient(left, #F4F7FE, #fff); /* Firefox */
            background: -webkit-linear-gradient(left, #F4F7FE, #fff); /* Chrome, Safari, Opera */
            background: linear-gradient(to right, #F4F7FE, #fff); /* 标准语法 */
        }
        .success .el-descriptions-item__label.is-bordered-label { background-color: #f0f9eb; width:140px;}
        .primary .el-descriptions-item__label.is-bordered-label { background-color: #F4F7FE; width:140px;}
        .el-descriptions .is-bordered td { width:250px;}
    </style>
</head>
<body style="background-color:#ffffff;">
<div id="app" v-cloak>
    <div class="centainer">
        {if $company}
        <el-descriptions class="success" title="已认证信息" :column="3" border>
            <template slot="extra">
<!--                <el-button type="primary" size="small">详细信息</el-button>-->
                <el-button type="primary" size="small" @click="auth">信息更新</el-button>
            </template>
            <el-descriptions-item label="企业名称">
                {$company.name}
            </el-descriptions-item>
            <el-descriptions-item label="法定代表人">
                {$company.legal}
            </el-descriptions-item>
            <el-descriptions-item label="法人联系电话">
                {$company.legal_mobile}
            </el-descriptions-item>
            <el-descriptions-item label="法人邮箱">
                {$company.legal_email}
            </el-descriptions-item>
            <el-descriptions-item label="座机电话号码">
                {$company.phone}
            </el-descriptions-item>
            <el-descriptions-item label="企业传真">
                {$company.fax}
            </el-descriptions-item>
            <el-descriptions-item label="产业园区">
                {$company.mb_industrial_park}
            </el-descriptions-item>
            <el-descriptions-item label="邮政编码">
                {$company.postal_code}
            </el-descriptions-item>
            <el-descriptions-item label="统一社会信用代码">
                {$company.license_number}
            </el-descriptions-item>
            <el-descriptions-item label="信用代码有效期">
                {$company.license_date}
            </el-descriptions-item>
            <el-descriptions-item label="经济类型">
                {$company.mb_economy_type}
            </el-descriptions-item>
            <el-descriptions-item label="注册资本">
                {$company.reg_money}万元
            </el-descriptions-item>
            <el-descriptions-item label="安全管理联系人">
                {$company.manager}
            </el-descriptions-item>
            <el-descriptions-item label="联系电话">
                {$company.manager_mobile}
            </el-descriptions-item>
            <el-descriptions-item label="邮箱">
                {$company.manager_email}
            </el-descriptions-item>
            <el-descriptions-item label="成立日期">
                {$company.date}
            </el-descriptions-item>
            <el-descriptions-item label="固定资产">
                {$company.fixed_asset}
            </el-descriptions-item>
            <el-descriptions-item label="年营业收入">
                {$company.revenue}万元
            </el-descriptions-item>
            <el-descriptions-item label="员工总数">
                {$company.personnel}
            </el-descriptions-item>
            <el-descriptions-item label="营业场所面积">
                {$company.area}m²
            </el-descriptions-item>
            <el-descriptions-item label="专职安全管理人数">
                {$company.personnel_full}
            </el-descriptions-item>
            <el-descriptions-item label="兼职安全管理人数">
                {$company.personnel_part}
            </el-descriptions-item>
            <el-descriptions-item label="特种作业人数">
                {$company.personnel_special}
            </el-descriptions-item>
            <el-descriptions-item label="行业/专业">
                {$company.industry}/{$company.specialty}
            </el-descriptions-item>
            <el-descriptions-item label="国民经济行业" :span="2">
                {$company.mb_economy_sector}
            </el-descriptions-item>
            <el-descriptions-item label="所属集团名称">
                {$company.group_name}
            </el-descriptions-item>
            <el-descriptions-item label="注册地址" :span="2">
                {$company.mb_reg_address}
            </el-descriptions-item>
            <el-descriptions-item label="企业规模">
                {$company.enterprise_size}
            </el-descriptions-item>
            <el-descriptions-item label="生产经营地点" :span="2">
                {$company.mb_operate_address}
            </el-descriptions-item>
            <el-descriptions-item label="经营类型">
                {$company.business_type}
            </el-descriptions-item>
            <el-descriptions-item label="经营范围" :span="3">
                {$company.business}
            </el-descriptions-item>
            <el-descriptions-item :span="3">
                <template slot="label">
                    <el-tag type="success" effect="dark" style="font-size: 15px;display: block;">粉尘涉爆</el-tag>
                </template>
                {$company.is_dust_explosion}
            </el-descriptions-item>
            <?php
            if($company['is_dust_explosion'] == '是'){
            ?>
            <el-descriptions-item label="所属行业" :span="3">
                {$company.sector}
            </el-descriptions-item>
            <?php
            foreach ($company['dust_list'] as $item) {
            ?>
            <el-descriptions-item label="粉尘种类" :span="1">
                {$item.name}
            </el-descriptions-item>
            <el-descriptions-item label="涉粉作业人数" :span="2">
                {$item.param_value}
            </el-descriptions-item>

            <?php
                }
            }
            ?>
            <el-descriptions-item :span="3">
                <template slot="label">
                    <el-tag type="success" effect="dark" style="font-size: 15px;display: block;">涉氨制冷</el-tag>
                </template>
                {$company.is_ammonia_cold}
            </el-descriptions-item>
            <?php
            if($company['is_ammonia_cold'] == '是'){
            ?>
            <el-descriptions-item label="液氨的用途" >
                {$company.ammonia_use}
            </el-descriptions-item>
            <el-descriptions-item label="液氨使用量" >
                {$company.ammonia_usage}t/a
            </el-descriptions-item>
            <el-descriptions-item label="液氨储存方式" >
                {$company.ammonia_storage_method}
            </el-descriptions-item>
            <el-descriptions-item label="液氨储存量" >
                {$company.ammonia_storage_capacity}t
            </el-descriptions-item>
            <el-descriptions-item label="气体泄露报警装置数" :span="2">
                {$company.gas_alarm_number}
            </el-descriptions-item>
                <?php
            }
            ?>
            <el-descriptions-item :span="3">
                <template slot="label">
                    <el-tag type="success" effect="dark" style="font-size: 15px;display: block;">涉高温熔融金属</el-tag>
                </template>
                {$company.is_hot_melting}
            </el-descriptions-item>
            <?php
            if($company['is_hot_melting'] == '是'){
            ?>
                <el-descriptions-item label="高炉数量" :span="1">
                    {$company.blast_furnace_number}
                </el-descriptions-item>
                <el-descriptions-item label="熔炼炉" :span="2">
                    <span style="width: 120px;display: inline-block">{$company['hot_list'][0]['name']}</span> 数量: {$company['hot_list'][0]['param_value']}
                </el-descriptions-item>
                <el-descriptions-item label="有色金属冶炼炉数量" :span="1">
                    {$company.nonferrous_furnace_number}
                </el-descriptions-item>
                <el-descriptions-item label="熔炼炉" :span="2">
                    <span style="width: 120px;display: inline-block">{$company['hot_list'][1]['name']}</span> 数量: {$company['hot_list'][1]['param_value']}
                </el-descriptions-item>
                <el-descriptions-item label="铁合金矿热炉数量" :span="1">
                    {$company.ferroalloy_furnace_number}
                </el-descriptions-item>
                <el-descriptions-item label="熔炼炉" :span="2">
                    <span style="width: 120px;display: inline-block">{$company['hot_list'][2]['name']}</span> 数量: {$company['hot_list'][2]['param_value']}
                </el-descriptions-item>
                <el-descriptions-item label="冲天炉数量" :span="1">
                    {$company.soaring_furnace_number}
                </el-descriptions-item>
                <el-descriptions-item label="熔炼炉" :span="2">
                    <span style="width: 120px;display: inline-block">{$company['hot_list'][3]['name']}</span> 数量: {$company['hot_list'][3]['param_value']}
                </el-descriptions-item>
                <el-descriptions-item label="" :span="1">
                </el-descriptions-item>
                <el-descriptions-item label="熔炼炉" :span="2">
                    <span style="width: 120px;display: inline-block">{$company['hot_list'][4]['name']}</span> 数量: {$company['hot_list'][4]['param_value']}
                </el-descriptions-item>
                <el-descriptions-item label="" :span="1">
                </el-descriptions-item>
                <el-descriptions-item label="熔炼炉" :span="2">
                    <span style="width: 120px;display: inline-block">{$company['hot_list'][5]['name']}</span> 数量: {$company['hot_list'][5]['param_value']}
                </el-descriptions-item>
                <el-descriptions-item label="" :span="1">
                </el-descriptions-item>
                <el-descriptions-item label="熔炼炉" :span="2">
                    <span style="width: 120px;display: inline-block">{$company['hot_list'][6]['name']}</span> 数量: {$company['hot_list'][6]['param_value']}
                </el-descriptions-item>

            <?php
            }
            ?>
            <el-descriptions-item :span="3">
                <template slot="label">
                    <el-tag type="success" effect="dark" style="font-size: 15px;display: block;">涉轻工行业有限空间</el-tag>
                </template>
                {$company.is_light_industry}
            </el-descriptions-item>
            <?php
            if($company['is_light_industry'] == '是'){
            ?>
            {volist name="company.limited_list" id="item"}
            <el-descriptions-item label="有限空间类型" :span="2">
                {$item.name}
            </el-descriptions-item>
            <el-descriptions-item label="数量" :span="1">
                {$item.param_value}
            </el-descriptions-item>
            {/volist}
                <?php
            }
            ?>

        </el-descriptions>
        {/if}

        {if $company_apply}
        <el-descriptions class="primary"  {if $company_apply.status ==1 } title="认证中信息" {/if}  {if $company_apply.status ==5 } title="驳回中信息(驳回原因:{$company_apply.reason})" {/if} :column="3" border>

        <template slot="extra">
            <!--                <el-button type="primary" size="small">详细信息</el-button>-->
            <el-button type="primary" size="small" @click="auth('{$company_apply.id}')">信息修改</el-button>
        </template>
        <el-descriptions-item label="企业名称">
                {$company_apply.name}
            </el-descriptions-item>
            <el-descriptions-item label="法定代表人">
                {$company_apply.legal}
            </el-descriptions-item>
            <el-descriptions-item label="法人联系电话">
                {$company_apply.legal_mobile}
            </el-descriptions-item>
            <el-descriptions-item label="法人邮箱">
                {$company_apply.legal_email}
            </el-descriptions-item>
            <el-descriptions-item label="座机电话号码">
                {$company_apply.phone}
            </el-descriptions-item>
            <el-descriptions-item label="企业传真">
                {$company_apply.fax}
            </el-descriptions-item>
            <el-descriptions-item label="产业园区">
                {$company_apply.mb_industrial_park}
            </el-descriptions-item>
            <el-descriptions-item label="邮政编码">
                {$company_apply.postal_code}
            </el-descriptions-item>
            <el-descriptions-item label="统一社会信用代码">
                {$company_apply.license_number}
            </el-descriptions-item>
            <el-descriptions-item label="信用代码有效期">
                {$company_apply.license_date}
            </el-descriptions-item>
            <el-descriptions-item label="经济类型">
                {$company_apply.mb_economy_type}
            </el-descriptions-item>
            <el-descriptions-item label="注册资本">
                {$company_apply.reg_money}万元
            </el-descriptions-item>
            <el-descriptions-item label="安全管理联系人">
                {$company_apply.manager}
            </el-descriptions-item>
            <el-descriptions-item label="联系电话">
                {$company_apply.manager_mobile}
            </el-descriptions-item>
            <el-descriptions-item label="邮箱">
                {$company_apply.manager_email}
            </el-descriptions-item>
            <el-descriptions-item label="成立日期">
                {$company_apply.date}
            </el-descriptions-item>
            <el-descriptions-item label="固定资产">
                {$company_apply.fixed_asset}
            </el-descriptions-item>
            <el-descriptions-item label="年营业收入">
                {$company_apply.revenue}万元
            </el-descriptions-item>
            <el-descriptions-item label="员工总数">
                {$company_apply.personnel}
            </el-descriptions-item>
            <el-descriptions-item label="营业场所面积">
                {$company_apply.area}m³
            </el-descriptions-item>
            <el-descriptions-item label="专职安全管理人数">
                {$company_apply.personnel_full}
            </el-descriptions-item>
            <el-descriptions-item label="兼职安全管理人数">
                {$company_apply.personnel_part}
            </el-descriptions-item>
            <el-descriptions-item label="特种作业人数">
                {$company_apply.personnel_special}
            </el-descriptions-item>
            <el-descriptions-item label="行业/专业">
                {$company_apply.industry}/{$company_apply.specialty}
            </el-descriptions-item>
            <el-descriptions-item label="国民经济行业" :span="2">
                {$company_apply.mb_economy_sector}
            </el-descriptions-item>
            <el-descriptions-item label="所属集团名称">
                {$company_apply.group_name}
            </el-descriptions-item>
            <el-descriptions-item label="注册地址" :span="2">
                {$company_apply.mb_reg_address}
            </el-descriptions-item>
            <el-descriptions-item label="企业规模">
                {$company_apply.enterprise_size}
            </el-descriptions-item>
            <el-descriptions-item label="生产经营地点" :span="2">
                {$company_apply.mb_operate_address}
            </el-descriptions-item>
            <el-descriptions-item label="经营类型">
                {$company_apply.business_type}
            </el-descriptions-item>
            <el-descriptions-item label="经营范围" :span="3">
                {$company_apply.business}
            </el-descriptions-item>
            <el-descriptions-item :span="3">
                <template slot="label">
                    <el-tag type="info" effect="dark" style="font-size: 15px;display: block;">粉尘涉爆</el-tag>
                </template>
                {$company_apply.is_dust_explosion}
            </el-descriptions-item>
            <?php
            if($company_apply['is_dust_explosion'] == '是'){
                ?>
                <el-descriptions-item label="所属行业" :span="3">
                    {$company_apply.sector}
                </el-descriptions-item>
                <?php
                foreach ($company_apply['dust_list'] as $item) {
                ?>
                <el-descriptions-item label="粉尘种类" :span="1">
                    {$item.name}
                </el-descriptions-item>
                <el-descriptions-item label="涉粉作业人数" :span="2">
                    {$item.param_value}
                </el-descriptions-item>

                <?php
                }
            }
            ?>
            <el-descriptions-item :span="3">
                <template slot="label">
                    <el-tag type="info" effect="dark" style="font-size: 15px;display: block;">涉氨制冷</el-tag>
                </template>
                {$company_apply.is_ammonia_cold}
            </el-descriptions-item>
            <?php
            if($company_apply['is_ammonia_cold'] == '是'){
                ?>
                <el-descriptions-item label="液氨的用途" >
                    {$company_apply.ammonia_use}
                </el-descriptions-item>
                <el-descriptions-item label="液氨使用量" >
                    {$company_apply.ammonia_usage}t/a
                </el-descriptions-item>
                <el-descriptions-item label="液氨储存方式" >
                    {$company_apply.ammonia_storage_method}
                </el-descriptions-item>
                <el-descriptions-item label="液氨储存量" >
                    {$company_apply.ammonia_storage_capacity}t
                </el-descriptions-item>
                <el-descriptions-item label="气体泄露报警装置数" :span="2">
                    {$company_apply.gas_alarm_number}
                </el-descriptions-item>
                <?php
            }
            ?>
            <el-descriptions-item :span="3">
                <template slot="label">
                    <el-tag type="info" effect="dark" style="font-size: 15px;display: block;">涉高温熔融金属</el-tag>
                </template>
                {$company_apply.is_hot_melting}
            </el-descriptions-item>
            <?php
            if($company_apply['is_hot_melting'] == '是'){
                ?>
                <el-descriptions-item label="高炉数量" :span="1">
                    {$company_apply.blast_furnace_number}
                </el-descriptions-item>
                <el-descriptions-item label="熔炼炉" :span="2">
                    <span style="width: 120px;display: inline-block">{$company_apply['hot_list'][0]['name']}</span> 数量: {$company_apply['hot_list'][0]['param_value']}
                </el-descriptions-item>
                <el-descriptions-item label="有色金属冶炼炉数量" :span="1">
                    {$company_apply.nonferrous_furnace_number}
                </el-descriptions-item>
                <el-descriptions-item label="熔炼炉" :span="2">
                    <span style="width: 120px;display: inline-block">{$company_apply['hot_list'][1]['name']}</span> 数量: {$company_apply['hot_list'][1]['param_value']}
                </el-descriptions-item>
                <el-descriptions-item label="铁合金矿热炉数量" :span="1">
                    {$company_apply.ferroalloy_furnace_number}
                </el-descriptions-item>
                <el-descriptions-item label="熔炼炉" :span="2">
                    <span style="width: 120px;display: inline-block">{$company_apply['hot_list'][2]['name']}</span> 数量: {$company_apply['hot_list'][2]['param_value']}
                </el-descriptions-item>
                <el-descriptions-item label="冲天炉数量" :span="1">
                    {$company_apply.soaring_furnace_number}
                </el-descriptions-item>
                <el-descriptions-item label="熔炼炉" :span="2">
                    <span style="width: 120px;display: inline-block">{$company_apply['hot_list'][3]['name']}</span> 数量: {$company_apply['hot_list'][3]['param_value']}
                </el-descriptions-item>
                <el-descriptions-item label="" :span="1">
                </el-descriptions-item>
                <el-descriptions-item label="熔炼炉" :span="2">
                    <span style="width: 120px;display: inline-block">{$company_apply['hot_list'][4]['name']}</span> 数量: {$company_apply['hot_list'][4]['param_value']}
                </el-descriptions-item>
                <el-descriptions-item label="" :span="1">
                </el-descriptions-item>
                <el-descriptions-item label="熔炼炉" :span="2">
                    <span style="width: 120px;display: inline-block">{$company_apply['hot_list'][5]['name']}</span> 数量: {$company_apply['hot_list'][5]['param_value']}
                </el-descriptions-item>
                <el-descriptions-item label="" :span="1">
                </el-descriptions-item>
                <el-descriptions-item label="熔炼炉" :span="2">
                    <span style="width: 120px;display: inline-block">{$company_apply['hot_list'][6]['name']}</span> 数量: {$company_apply['hot_list'][6]['param_value']}
                </el-descriptions-item>
                <?php
            }
            ?>
            <el-descriptions-item :span="3">
                <template slot="label">
                    <el-tag type="info" effect="dark" style="font-size: 15px;display: block;">涉轻工行业有限空间</el-tag>
                </template>
                {$company_apply.is_light_industry}
            </el-descriptions-item>
            <?php
            if($company_apply['is_light_industry'] == '是'){
                ?>
            {volist name="company_apply.limited_list" id="item"}
                    <el-descriptions-item label="有限空间类型" :span="2">
                        {$item.name}
                    </el-descriptions-item>
                    <el-descriptions-item label="数量" :span="1">
                        {$item.param_value}
                    </el-descriptions-item>
            {/volist}
                <?php
            }
            ?>
        </el-descriptions>
        {/if}
    </div>
</div>
<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                title: '',
                igrameSrc: '',
                loading: false,
                data: {
                    date:'',
                    license_start:'',
                    license_end:'',
                    enterprise_size:'',
                },
                rules: {
                },
                config:[],
                height: document.documentElement.clientHeight,
            };
        },
        components: {
        },
        methods: {
            auth: function(id){
                var _this = this;
                if(id>0){
                    _this.$confirm("确认修改信息？", "提示", {}).then(() => {
                        location.href = 'auth?id='+id;
                    });
                }else{
                    _this.$confirm("确认信息变更？", "提示", {}).then(() => {
                        location.href = 'auth';
                    });
                }
            }
        },
        mounted() {
        }
    })
</script>

</body>
</html>