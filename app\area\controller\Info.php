<?php
declare (strict_types = 1);

namespace app\area\controller;

use hg\apidoc\annotation as Apidoc;
use think\Request;
use app\admin\model\TopAreaUserModel;
use app\admin\model\TopAreaModel;
use app\admin\model\TopPcaModel;

class Info extends Base
{
    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index()
    {
        //
	    $loginUser = $this->userInfo;
	    return view('', ['title' => '人员管理', 'loginUser' => $loginUser]);
    }

	/**
	 * 请求列表
	 */
    public function getList(){
    	$param = $this->request->param();
    	$area_id = $this->userInfo->area_id;
    	$param['area_id'] = $area_id;
    	$model = new TopAreaUserModel();
    	$data = $model->getList($param, $param['page'], $param['limit']);
	    $areaModel = new TopAreaModel();
	    result(array('list' => $data, 'area' => $areaModel->getList([], 0, 0, true), 'department' => $model->department_arr, 'role' => $model->role_arr));
    }


    public function save_data(){
	    $param = $this->request->param();
	    $model = new TopAreaUserModel();
	    $id = intval($param['id']);
	    if ($id) $model = $model::find($id);
	    $data = $model->newSave($model, $param, $id);
	    result(null, 0, $data);
    }

	/**
	 * 修改密码
	 */
	public function update_password(){
		$param = $this->request->param();
		$id = intval($param['id']);
		$model = TopAreaUserModel::find($id);
		$data = $model->updatePassword($model, $param['password']);
		if ($data !== '数据处理成功') $code = -200;
		result(null, $code ? $code : 0, $data);
	}

	public function check_user(){
		$param = $this->request->param();
		$id = intval($param['id']);
		$model = TopAreaUserModel::find($id);
		$data = $model->checkUser($model, $param['status']);
		if ($data !== '数据处理成功') $code = -200;
		result(null, $code ? $code : 0, $data);
	}

    /**
     * 显示创建资源表单页.
     *
     * @return \think\Response
     */
    public function create()
    {
        //
    }

    /**
     * 保存新建的资源
     *
     * @param  \think\Request  $request
     * @return \think\Response
     */
    public function save(Request $request)
    {
        //
    }

    /**
     * 显示指定的资源
     *
     * @param  int  $id
     * @return \think\Response
     */
    public function read($id)
    {
        //
    }

    /**
     * 显示编辑资源表单页.
     *
     * @param  int  $id
     * @return \think\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * 保存更新的资源
     *
     * @param  \think\Request  $request
     * @param  int  $id
     * @return \think\Response
     */
    public function update(Request $request, $id)
    {
        //
    }
    
	/**
	 * 删除
	 */
	public function delete()
	{
		$param = $this->request->param();
		$id = intval($param['id']);
		$model = TopAreaUserModel::find($id);
		if ($model){
			$data = $model->delData($model);
			if ($data != '数据处理成功') $code = -200;
		}else{
			$code = -200;
			$data = '未查询到数据';
		}
		result(null, $code ? $code : 0, $data);
	}
}
