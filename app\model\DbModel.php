<?php

namespace app\model;

use Dompdf\Dompdf;
use think\facade\Cache;
use think\facade\Db;
use think\facade\Filesystem;
use think\Model;

class DbModel extends Model
{
    /*
     * 判断数据表是否存在
     */
    public static function isDb($setting){
        if(empty($setting['fields']['db'])){
            return true;
        }
        $database = config('database.connections')[config('database.default')];
        if($database['type']=='mysql'){
            $res = Db::query("SHOW TABLES LIKE '{$setting['table']}'");
            if(!$res){
                $sql = "CREATE TABLE `{$setting['table']}`  (`id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,";
                foreach ($setting['fields']['db'] as $k=>$v){
                    if(strpos($v['type'],'varchar')!==false){
                        $sql .= "`{$v['field']}` {$v['type']} CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '{$v['title']}',";
                    }else if(strpos($v['type'],'int')!==false){
                        $sql .= "`{$v['field']}` tinyint(1) NOT NULL COMMENT '{$v['title']}',";
                    }else if(strpos($v['type'],'text')!==false){
                        $sql .= "`{$v['field']}` {$v['type']} CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '{$v['title']}',";
                    }else{
                        $sql .= "`{$v['field']}` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '{$v['title']}',";
                    }
                }
                $sql .= "PRIMARY KEY (`id`) USING BTREE) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '{$setting['title']}' ROW_FORMAT = Dynamic;";
//            dd($sql);
                Db::query($sql);
            }else{
                $res = Db::query("show full columns from {$setting['table']}");
                foreach ($res as $k=>$v){
                    $fields[] = $v['Field'];
                }
                $sql = "";
                $f = 'id';
                foreach ($setting['fields']['db'] as $k=>$v){
                    if(!in_array($v['field'],$fields)){
                        if(strpos($v['type'],'varchar')!==false){
                            $sql .= "ADD COLUMN `{$v['field']}` {$v['type']} CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '{$v['title']}' AFTER `$f`,";
                        }else if(strpos($v['type'],'int')!==false){
                            $sql .= "ADD COLUMN `{$v['field']}` tinyint(1) NOT NULL COMMENT '{$v['title']}' AFTER `$f`,";
                        }else if(strpos($v['type'],'text')!==false){
                            $sql .= "ADD COLUMN `{$v['field']}` {$v['type']} CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '{$v['title']}' AFTER `$f`,";
                        }else{
                            $sql .= "ADD COLUMN `{$v['field']}` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '{$v['title']}' AFTER `$f`,";
                        }
                    }
                    $f = $v['field'];
                }
                if(!empty($sql)){
                    $sql = "ALTER TABLE `{$setting['table']}` ".trim($sql,',').';';
                    Db::query($sql);
                }
//            dd($sql);
            }
        }else if($database['type']=='dm'){
            $res = Db::query("select * from dba_tables where table_name='{$setting['table']}'");
            if(!$res){
                $sql = "CREATE TABLE `{$setting['table']}`  (id INT identity(1, 1) not null unique,";
                foreach ($setting['fields']['db'] as $k=>$v){
                    if(strpos($v['type'],'varchar')!==false){
                        $v['type'] = str_replace('varchar(','VARCHAR2(',$v['type']);
                        $sql .= "`{$v['field']}` {$v['type']} default (''),";
                    }else if(strpos($v['type'],'int')!==false){
                        $sql .= "`{$v['field']}` INT default (0),";
                    }else if(strpos($v['type'],'text')!==false){
                        $sql .= "`{$v['field']}` TEXT default (''),";
                    }else{
                        $sql .= "`{$v['field']}` VARCHAR2(500) default (''),";
                    }
                }
                $sql .= "primary key(id))storage(initial 1, next 1, minextents 1, fillfactor 0);";
//                $sql .= "comment on table {$setting['table']} is '{$setting['title']}';";
                $pdo = Db::getPdo();
                $pdo->query($sql);
                $pdo->query("comment on table {$setting['table']} is '{$setting['title']}';");
                foreach ($setting['fields']['db'] as $k=>$v){
                    $pdo->query("comment ON COLUMN `{$setting['table']}`.{$v['field']} IS '{$v['title']}';");
                }
            }else{
                $res = Db::query("SELECT COLUMN_NAME,DATA_TYPE,DATA_LENGTH FROM USER_TAB_COLUMNS WHERE TABLE_NAME = '{$setting['table']}';");
                foreach ($res as $k=>$v){
                    $fields[] = $v['COLUMN_NAME'];
                }
                $sql = "";
                $pdo = Db::getPdo();
                foreach ($setting['fields']['db'] as $k=>$v){
                    if(!in_array($v['field'],$fields)){
                        if(strpos($v['type'],'varchar')!==false){
                            $v['type'] = str_replace('varchar(','VARCHAR2(',$v['type']);
                            $pdo->query("ALTER TABLE `{$setting['table']}` ADD {$v['field']} {$v['type']} default ('');");
                            $pdo->query("comment ON COLUMN `{$setting['table']}`.{$v['field']} IS '{$v['title']}';");
                        }else if(strpos($v['type'],'int')!==false){
                            $pdo->query("ALTER TABLE `{$setting['table']}` ADD {$v['field']} INT default (0);");
                            $pdo->query("comment ON COLUMN `{$setting['table']}`.{$v['field']} IS '{$v['title']}';");
                        }else if(strpos($v['type'],'text')!==false){
                            $pdo->query("ALTER TABLE `{$setting['table']}` ADD {$v['field']} TEXT default ('');");
                            $pdo->query("comment ON COLUMN `{$setting['table']}`.{$v['field']} IS '{$v['title']}';");
                        }else{
                            $pdo->query("ALTER TABLE `{$setting['table']}` ADD {$v['field']} VARCHAR2(500) default ('');");
                            $pdo->query("comment ON COLUMN `{$setting['table']}`.{$v['field']} IS '{$v['title']}';");
                        }
                    }
                }
                $sql = trim($sql,',').';';
//                exit($sql);
                if(!empty($sql)){
//                    $pdo->query($sql);
                }
            }
        }
        return true;
    }

    public static function dbFields($table){
        if(empty($table)){
            return [];
        }
        $database = config('database.connections')[config('database.default')];
        if($database['type']=='mysql'){
            $res = Db::query("show full columns from {$table}");
        }else if($database['type']=='dm'){
            $res = Db::query("SELECT COLUMN_NAME as Field,DATA_TYPE,DATA_LENGTH FROM USER_TAB_COLUMNS WHERE TABLE_NAME = '{$table}';");
            foreach ($res as $k=>$v){
                $res[$k]['Comment'] = '';
            }
        }
        return $res;
    }

    public static function getTables($like){
        $database = config('database.connections')[config('database.default')];
        if($database['type']=='mysql'){
            $res = Db::query("SHOW TABLES LIKE '{$like}';");
        }else if($database['type']=='dm'){
            $res = Db::query("SELECT table_name FROM user_tables where table_name like {$like};");
        }
        return $res;
    }

}