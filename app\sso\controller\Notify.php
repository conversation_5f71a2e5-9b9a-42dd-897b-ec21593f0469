<?php

namespace app\sso\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use hg\apidoc\annotation as Apidoc;
use app\Request;
use think\App;
use think\facade\Db;

/**
 * @Apidoc\Title("通知公告相关接口")
 * @Apidoc\Group("User")
 * @Apidoc\Sort(3)
 */
class Notify extends Base
{

    /**
     * @Apidoc\Title("通知公告发布")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("department", type="string",require=true, desc="部门名称")
     * @Apidoc\Returned("dept_id", type="string",desc="部门id")
     * @Apidoc\Returned("dept_name", type="string",desc="部门名称")
     */
    function notifyAdd(Request $request){
    }

    /**
     * @Apidoc\Title("通知公告列表")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("department", type="string",require=true, desc="部门名称")
     * @Apidoc\Returned("dept_id", type="string",desc="部门id")
     * @Apidoc\Returned("dept_name", type="string",desc="部门名称")
     */
    function notifyList(Request $request){
    }

    /**
     * @Apidoc\Title("通知公告详情")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("department", type="string",require=true, desc="部门名称")
     * @Apidoc\Returned("dept_id", type="string",desc="部门id")
     * @Apidoc\Returned("dept_name", type="string",desc="部门名称")
     */
    function notifyInfo(Request $request){
    }


}
