<?php

namespace app\sso\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use hg\apidoc\annotation as Apidoc;
use app\BaseController;
use think\App;
use think\facade\Db;
use think\facade\View;

/**
 * @Apidoc\Title("单点登录")
 * @Apidoc\Group("Myapi")
 * @Apidoc\Sort(3)
 */
class Myapi extends BaseController
{
    public function __construct(App $app)
    {
        parent::__construct($app);
    }

    /*
     * 我的账号绑定列表
     */
    public function list($limit=20){
//        echo crypt('admin123456', '$1$g01Q42zj$OwRexEQG6z3fXLtO1X90X/');die;
        if (request()->isAjax()) {
            $search = request()->param('search','','trim');
            $where[] = ['del','=',0];
            if(!empty($search)){
                $where[] = ['name','like',"%$search%"];
            }
            $res = Db::name('login_app')->alias('a')
                ->leftJoin('login_app_user b','a.id = b.login_app_id')
                ->where($where)->order('create_time desc')
                ->field('a.appid,a.name,a.domain,a.tp_appid,b.*')
                ->paginate($limit)->each(function ($item, $key) {
                return $item;
            });
            result($res);
        }else{
            View::assign('isAdmin',true);
            return view();
        }
    }

    public function bind($appid='',$key=''){
        $loginApp = Db::name('login_app')->where(['appid'=>$appid])->find();
        if(empty($loginApp)){
            result('',8001,'该系统未授权，请联系管理员授权！');
        }
        $url = $loginApp['domain'].'/general/topcrm/index.php/sso/login/getBindUser?key='.$key;
        $results = file_get_contents($url);
        $results = json_decode($results, true);
        if(empty($results)||$results['code']!=0){
            result('',1001,$results['msg']);
        }
        /*$checksign = md5('appid='.$loginApp['appid'].'&appkey='.$loginApp['appkey'].'&byname='.$byname.'&time='.$time);
        if($sign!==$checksign){
            result('',1001,'签名错误');
        }*/
        $user = Db::table('td_user')->where(['USER_ID'=>$_SESSION['LOGIN_USER_ID']])->find();
        $data = [
            'login_app_id' => $loginApp['id'],
            'user_id' => $user['USER_ID'],
            'byname' => $user['BYNAME'],
            'bind_byname' => $results['data']['username'],
        ];
        $re = Db::name('login_app_user')->where(['user_id'=>$data['user_id']])->find();
        if(empty($re)){
            Db::name('login_app_user')->where(['login_app_id'=>$data['login_app_id'],'bind_byname'=>$data['bind_byname']])->delete();
            Db::name('login_app_user')->insertGetId($data);
        }else{
            Db::name('login_app_user')->where([['login_app_id','=',$data['login_app_id']],['bind_byname','=',$data['bind_byname']],['id','<>',$re['id']]])->delete();
            Db::name('login_app_user')->where(['id'=>$re['id']])->update($data);
        }
        result('');
    }

    //清除关联
    public function del($id=0){
        $del = Db::name('login_app_user')->where(['id'=>$id])->delete();
        result('');
    }

    //登录
    public function tpLogin(){
        $param = self::getTopParentId();
        $loginApp = Db::name('login_app')->where(['param'=>$param])->find();
        $time = time();
        $user = Db::table('td_user')->where(['USER_ID'=>$_SESSION['LOGIN_USER_ID']])->find();
        $url = $loginApp['domain'].'/general/toppingsoft/oa/login.php';
        $sign = md5('appid='.$loginApp['appid'].'&appkey='.$loginApp['appkey'].'&byname='.$user['BYNAME'].'&time='.$time);
        $url .= 'appid='.$loginApp['tp_appid'].'&byname='.$user['BYNAME'].'&time='.$time.'&sign='.$sign;
        return redirect($url);
    }

    //获取顶级部门id
    public function getTopParentId(){
        $re = Db::table('department')->where(['DEPT_ID'=>$_SESSION['LOGIN_DEPT_ID']])->find();
        if($re['DEPT_NO']){
            $re = Db::table('department')->where(['DEPT_NO'=>substr($re['DEPT_NO'],0,3)])->find();
        }
        return $re['DEPT_ID'];
    }

}
