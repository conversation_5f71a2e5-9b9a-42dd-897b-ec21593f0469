<style>
.mytable {border-collapse:collapse;width: 100%;}
.mytable thead tr td {border: 1px solid #333;font-weight: 700; padding: 10px;background: #f5f7fa;text-align: center;}
.mytable tbody tr th {border: 1px solid #333;font-weight: 700; padding: 10px;text-align: center;font-size: 16px;line-height:20px;}
.mytable tbody tr td {border: 1px solid #333; padding: 10px;text-align: center;font-size: 16px;font-family: 宋体;line-height:20px;}
.mytable .active td{ background: #f2f2f2;}
.mytable tbody tr td p{line-height: 30px;}
</style>
<template>
  <el-dialog :title="title" :close-on-click-modal="false"  modal="false" :visible.sync="visible" width="900px" @close="refresh()" append-to-body="true" label-position="top">
    <el-form ref="form" :model="data" class="tableSearch" label-position="right" label-width="170px">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="公示标题" prop="name">
            <el-input v-model="data.title" size="mini"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="公示日期" prop="date">
            <el-date-picker
                v-model="data.date"
                size="mini"
                type="date"
                placeholder="选择日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="公示名单" prop="mobile">
            <div>
              <table class="mytable" id="myCanvas">
                <tr>
                  <th>区（市）县</th>
                  <th>企业名称</th>
                  <th>行业/专业</th>
                </tr>
                <tr v-for="item in data.list">
                  <td>{{item.company_name}}</td>
                  <td>{{item.company_name}}</td>
                  <td>{{item.industry}}/{{item.specialty}}</td>
                </tr>
              </table>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24" style="text-align: center;">
          <el-button type="primary" @click="submit()">保存</el-button>
          <el-button @click="visible = false">关闭</el-button>
        </el-col>
      </el-row>
    </el-form>
  </el-dialog>
</template>
<script>
module.exports = {
  name: "add",
  // 模板导入区
  components: {
  },
  data:function() {
    return {
      visible: false,
      title: '公示',
      user_name:'',
      data: {
        title:'',
        date:'',
        list:[],
      },
    }
  },
  mounted: function(){
    // this.getConfig();
  },
  created:function(){
  },
  methods: {
    handleChange(value) {
      var data = [];
      for (var i=0;i<3;i++){
        data.push(value[i]);
      }
      this.data.region = data;
    },
    /**
     * 打开弹窗调用方法
     * */
    open: function (rows) {
      var _this =this;
      _this.data.list = rows;
      _this.visible = true;
    },
    submit: function () {
      var _this = this;
      var param = _this.data;
      html2canvas(document.getElementById("myCanvas")).then(function(canvas) {
        param.image = canvas.toDataURL();
        axios.post("publicitySave", param).then(function (res) {
          _this.$message({
            message: res.data.msg,
            type: res.data.type
          });
          if (res.data.code == 0) {
            _this.visible = false;
            _this.$emit("ok");
          }
        }).catch(function (error) {
          console.log("出现错误:",error);
        });
      });
    },
    getInfo:function(id){
      var _this = this;
      _this.loading = true;
      axios.post('getInfo', {id:id}).then(function (res) {
        _this.loading = false;
        if (res.data.code == 0) {
          _this.data = res.data.data;
        }
      }).catch(function (error) {
        _this.loading = false;
        console.log("出现错误:",error);
      });
    },
    refresh: function () {
      this.$emit("refresh");
    },
  }
}
</script>