<?php
namespace app\dashboard\controller;

use think\Controller;
use think\facade\Db;
use think\Request;
use think\facade\View;

class Index extends Base
{
    public function main()
    {
        View::assign('title','数据大屏');
        return view();
    }

    public function data()
    {
        // 【总体概览】统计企业总数、通过数、有效数、未通过数等，来源表：top_company_info
        $overview = [
            'total' => Db::table('top_company_info')->count(), // 企业总数
            'passed' => Db::table('top_company_info')->where('status',1)->count(), // 通过企业数，status=1
            'valid' => Db::table('top_company_info')->where('stand_status',1)->count(), // 达标有效企业数，stand_status=1
            'rate' => '93.8%', // 可根据实际通过率计算
            'unpassed' => Db::table('top_company_info')->where('status',0)->count(), // 未通过企业数，status=0
            'passedCount' => Db::table('top_company_info')->where('status',1)->count() // 通过企业数，冗余字段
        ];
        // 【分行业统计】统计前5个行业的企业数量，来源表：top_company_info，字段：industry
        $industryArr = Db::table('top_company_info')->field('industry,count(*) as num')->group('industry')->limit(5)->select()->toArray();
        $industry = [];
        foreach($industryArr as $item){
            $industry[] = $item['num']; // 只取数量部分
        }
        // 【区域名称获取】获取前5个区域名称，来源表：top_area，字段：name
        $district = Db::table('top_area')->field('name')->limit(5)->select()->toArray();
        $districtNames = [];
        foreach($district as $item){
            $districtNames[] = $item['name'];
        }
        // 【区域企业数量统计】统计前5个区域的企业数量，来源表：top_company_info，字段：area_id
        $districtCountArr = Db::table('top_company_info')->field('area_id,count(*) as num')->group('area_id')->limit(5)->select()->toArray();
        $districtCount = [];
        foreach($districtCountArr as $item){
            $districtCount[] = $item['num'];
        }
        // 【区域概览信息】统计区域企业总数、通过数、任务数等，来源表：top_company_info
        $districtInfo = [
            'total' => array_sum($districtCount), // 区域企业总数
            'passed' => Db::table('top_company_info')->where('status',1)->count(), // 区域通过企业数
            'task' => Db::table('top_company_info')->where('stand_status',1)->count(), // 区域评审任务数
            'timely' => '99%' // 评审及时率
        ];
        // 【高分评审企业TOP3】获取评分最高的3个行业，来源表：top_company_evaluate，字段：industry,score
        $top = Db::table('top_company_evaluate')->field('industry,score')->order('score desc')->limit(3)->select();
        if(!$top || count($top)<3){
            $top = [
                ['name'=>'冶金','score'=>99.6],
                ['name'=>'建材','score'=>99.5],
                ['name'=>'机械','score'=>99.4]
            ];
        }
        // 【企业整改信息】获取最新3条企业整改记录，来源表：top_company_review、top_company_info
        $review = Db::table('top_company_review')->alias('r')
            ->join('top_company_info c','r.company_id=c.id','LEFT')
            ->field('c.name as company, c.region as area, r.accessment_name as org, r.date, r.element as group_name')
            ->order('r.date desc')->limit(3)->select();
        if(!$review || count($review)<3){
            $review = [
                ['company'=>'成都金牛机械厂','area'=>'金牛区','org'=>'城安院','date'=>'2024.03.06 13:00:00','group_name'=>'专家组A','progress'=>'企业整改'],
                ['company'=>'成都青羊化工厂','area'=>'青羊区','org'=>'城安院','date'=>'2024.03.06 13:00:00','group_name'=>'专家组B','progress'=>'企业整改'],
                ['company'=>'成都高新电子厂','area'=>'高新区','org'=>'城安院','date'=>'2024.03.06 13:00:00','group_name'=>'专家组C','progress'=>'企业整改']
            ];
        } else {
            foreach($review as &$item){
                $item['progress'] = '企业整改';
            }
            unset($item);
        }
        // 【空间类型分布】获取空间相关参数，来源表：top_company_param，category=space
        $space = Db::table('top_company_param')->where('category','space')->field('param_value as value,name')->limit(3)->select();
        if(!$space || count($space)<3){
            $space = [
                ['value'=>30,'name'=>'污水处理池'],
                ['value'=>20,'name'=>'腌渍池'],
                ['value'=>10,'name'=>'其他']
            ];
        }
        // 【粉尘类型分布】获取粉尘相关参数，来源表：top_company_param，category=dust
        $dustPie = Db::table('top_company_param')->where('category','dust')->field('param_value as value,name')->limit(3)->select();
        if(!$dustPie || count($dustPie)<3){
            $dustPie = [
                ['value'=>40,'name'=>'金属制品'],
                ['value'=>30,'name'=>'木制品'],
                ['value'=>20,'name'=>'其他']
            ];
        }
        // 【粉尘条形图数据】获取粉尘条形图参数，来源表：top_company_param，category=dustBar
        $dustBar = Db::table('top_company_param')->where('category','dustBar')->limit(2)->column('param_value');
        if(!$dustBar || count($dustBar)<2){
            $dustBar = [18,12];
        }
        // 【氨气类型分布】获取氨气相关参数，来源表：top_company_param，category=ammonia
        $ammoniaPie = Db::table('top_company_param')->where('category','ammonia')->field('param_value as value,name')->limit(2)->select();
        if(!$ammoniaPie || count($ammoniaPie)<2){
            $ammoniaPie = [
                ['value'=>21,'name'=>'瓶装'],
                ['value'=>13,'name'=>'罐装']
            ];
        }
        // 【金属条形图1】获取金属相关参数1，来源表：top_company_param，category=metalBar1
        $metalBar1 = Db::table('top_company_param')->where('category','metalBar1')->limit(2)->column('param_value');
        if(!$metalBar1 || count($metalBar1)<2){
            $metalBar1 = [5,3];
        }
        // 【金属条形图2】获取金属相关参数2，来源表：top_company_param，category=metalBar2
        $metalBar2 = Db::table('top_company_param')->where('category','metalBar2')->limit(3)->column('param_value');
        if(!$metalBar2 || count($metalBar2)<3){
            $metalBar2 = [2,4,1];
        }
        $data = [
            'overview'=>$overview,
            'industry'=>$industry,
            'district'=>$district,
            'districtInfo'=>$districtInfo,
            'top'=>$top,
            'review'=>$review,
            'space'=>$space,
            'dustPie'=>$dustPie,
            'dustBar'=>$dustBar,
            'ammoniaPie'=>$ammoniaPie,
            'metalBar1'=>$metalBar1,
            'metalBar2'=>$metalBar2
        ];
        return json(['code'=>0,'msg'=>'success','data'=>$data]);
    }
}