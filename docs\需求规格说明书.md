# 应急管理评审系统需求规格说明书

## 1. 项目概述
### 1.1 系统背景
本系统面向安全生产标准化评审工作，实现市、区县、企业、评审机构和专家五端协同工作。

### 1.2 技术架构
- 后端框架：ThinkPHP6 多应用架构
- 前端框架：Element UI + Vue.js
- 数据库：达梦数据库（PDO连接）
- 运行环境：PHP7.3 + Nginx

## 2. 功能需求
### 2.1 市应急管理局端（app/city）
- 首页
  - 系统概览：提供系统整体运行状态和关键数据的可视化展示，包括待处理任务数量、评审进度统计等信息，帮助管理人员快速了解系统运行情况。
  - 通知公告：展示重要通知和公告信息，支持按重要程度分类显示，确保管理人员及时获取关键信息。
- 人员信息管理
  - 用户账号管理：提供市局内部用户的创建、权限分配、角色设置等功能，支持组织架构管理和用户状态监控，确保系统使用安全可控。
  - 人员信息维护：支持对人员基本信息、联系方式、职责权限等内容的维护和更新，确保人员信息准确有效。
- 企业信息查询
  - 企业基础信息查询：提供对辖区内企业基本信息的多条件查询功能，包括企业名称、地址、行业类型等维度，支持结果导出和详情查看。
  - 企业安全生产状况查询：查询企业安全生产标准化等级、评审历史、整改情况等信息，为监管决策提供数据支持。
- 定级申请审核
  - 企业定级申请查看：查看企业提交的定级申请材料，包括自评报告、证明材料等内容，支持材料在线预览和下载。
  - 申请审核处理：对企业定级申请进行审核，包括材料完整性、合规性审查，支持审核意见录入和结果通知。
  - 审核结果管理：记录和管理审核结果，支持审核历史查询和统计分析，确保审核过程透明可追溯。
- 企业申诉审核
  - 申诉材料查看：查看企业提交的申诉材料，包括申诉理由、证明文件等内容，支持材料在线预览和下载。
  - 申诉处理：对企业申诉进行处理，包括材料审查、情况核实等环节，支持处理意见录入和结果通知。
  - 申诉结果管理：记录和管理申诉处理结果，支持处理历史查询和统计分析，确保申诉处理公正透明。
- 一、二级备案审批
  - 备案申请查看：查看企业提交的一、二级标准化备案申请材料，包括评审报告、证明材料等内容，支持材料在线预览和下载。
  - 备案审批处理：对备案申请进行审批，包括材料审查、合规性验证等环节，支持审批意见录入和结果通知。
  - 备案结果管理：记录和管理备案审批结果，支持审批历史查询和统计分析，确保备案过程规范有序。
- 评审任务查看
  - 评审任务列表：展示所有评审任务的列表，包括任务状态、负责人、截止时间等信息，支持多条件筛选和排序。
  - 任务详情查看：查看评审任务的详细信息，包括企业信息、评审计划、专家组成等内容，支持相关材料下载和查阅。
  - 任务进度跟踪：跟踪评审任务的执行进度，包括各阶段完成情况、时间节点提示等，确保任务按计划推进。
- 证书管理
  - 证书信息管理：管理企业安全生产标准化证书信息，包括证书编号、发证日期、有效期等，支持证书查询和统计分析。
  - 证书发放管理：处理证书发放申请，生成电子证书，记录证书发放历史，确保证书管理规范有序。
  - 证书变更管理：处理证书信息变更申请，包括企业名称变更、地址变更等情况，确保证书信息准确有效。
- 公示管理
  - 公示信息发布：发布企业安全生产标准化评审结果公示信息，包括公示内容编辑、发布时间设置等功能。
  - 公示期管理：设置和管理公示期限，处理公示期间的异议和反馈，确保公示过程公开透明。
  - 公示结果确认：公示期满后确认最终结果，生成结果报告，通知相关企业和部门，完成公示流程。

### 2.2 区县端（app/area）
- 首页
  - 系统概览：提供区县级系统整体运行状态和关键数据的可视化展示，包括待处理任务数量、辖区企业评审情况等信息，帮助管理人员快速了解工作情况。
  - 通知公告：展示重要通知和公告信息，支持按重要程度分类显示，确保管理人员及时获取关键信息。
- 科室人员管理
  - 用户账号管理：提供区县内部用户的创建、权限分配、角色设置等功能，支持组织架构管理和用户状态监控，确保系统使用安全可控。
  - 人员信息维护：支持对科室人员基本信息、联系方式、职责权限等内容的维护和更新，确保人员信息准确有效。
- 企业信息审核
  - 企业注册申请审核：审核企业提交的注册申请信息，包括基本信息、资质证明等内容，确保企业身份真实有效。
  - 企业资料变更审核：审核企业提交的信息变更申请，包括名称变更、地址变更、法人变更等情况，确保企业信息准确及时。
  - 审核结果管理：记录和管理企业信息审核结果，支持审核历史查询和统计分析，确保审核过程透明可追溯。
- 企业信息查询
  - 企业基础信息查询：提供对辖区内企业基本信息的多条件查询功能，包括企业名称、地址、行业类型等维度，支持结果导出和详情查看。
  - 企业安全生产状况查询：查询企业安全生产标准化等级、评审历史、整改情况等信息，为监管决策提供数据支持。
- 创标申请审核
  - 创标申请查看：查看企业提交的创建安全生产标准化申请材料，包括基础资料、管理制度等内容，支持材料在线预览和下载。
  - 申请审核处理：对创标申请进行审核，包括材料完整性、合规性审查，支持审核意见录入和结果通知。
  - 审核结果管理：记录和管理创标审核结果，支持审核历史查询和统计分析，确保审核过程透明可追溯。
- 定级申请审核
  - 企业定级申请查看：查看企业提交的定级申请材料，包括自评报告、证明材料等内容，支持材料在线预览和下载。
  - 申请审核处理：对企业定级申请进行审核，包括材料完整性、合规性审查，支持审核意见录入和结果通知。
  - 审核结果管理：记录和管理审核结果，支持审核历史查询和统计分析，确保审核过程透明可追溯。
- 证书查询
  - 证书信息查询：提供对辖区内企业安全生产标准化证书的多条件查询功能，包括企业名称、证书级别、有效期等维度，支持结果导出和详情查看。
  - 证书状态管理：查看和管理证书状态，包括有效、过期、撤销等状态，支持证书状态变更记录查询。
  - 证书统计分析：对辖区内证书情况进行统计分析，包括证书数量、级别分布、有效期分布等维度，为监管工作提供数据支持。

### 2.3 企业端（app/company）
- 首页
  - 系统概览：提供企业端系统整体运行状态和关键数据的可视化展示，包括待办事项、评审进度等信息，帮助企业快速了解当前工作情况。
  - 通知公告：展示重要通知和公告信息，支持按重要程度分类显示，确保企业及时获取关键信息。
- 企业信息
  - 企业基础信息维护：支持企业维护和更新基础信息，包括企业名称、地址、联系方式、法人信息等，提供信息变更历史记录，保障企业信息的准确性和时效性。
  - 资料变更申请：提供企业重要资料变更的申请功能，包括变更原因说明、证明材料上传、变更审批流程，确保资料变更合规且可追溯。
- 三级标准化建设
  - 创标申请：提供企业创建安全生产标准化的申请功能，包括基础资料填写、证明材料上传、申请提交等环节，支持申请状态跟踪和结果查询。
  - 运行资料：支持企业上传和管理安全生产标准化运行过程中的各类资料，包括制度文件、记录表单、培训记录等，提供资料分类管理和查询功能。
  - 自评管理：提供在线自评工具，根据行业标准和评分细则引导企业进行全面自评，包括自评项目配置、评分标准说明和自评进度管理，支持自评报告生成和提交。
  - 初次申请：支持企业提交安全生产标准化初次评审申请，包括申请材料准备、自评报告提交、证明材料上传等功能，支持申请状态跟踪和结果查询。
  - 复评申请：支持企业提交安全生产标准化复评申请，包括复评材料准备、自评报告更新、证明材料上传等功能，支持申请状态跟踪和结果查询。
  - 评审群聊：提供企业与评审机构、专家之间的在线交流平台，支持文字、图片等多种形式的信息交换，促进评审过程中的有效沟通。
- 一/二级备案
  - 备案申请：支持企业提交一级或二级安全生产标准化备案申请，包括申请材料准备、证明文件上传等功能，支持申请状态跟踪和结果查询。
  - 备案资料管理：管理企业备案过程中的各类资料，包括评审报告、证明材料等，提供资料分类存储和查询功能。
  - 备案状态查询：查询企业备案申请的处理状态，包括审核中、已通过、已驳回等状态，支持结果通知和反馈功能。
- 我的证书
  - 证书信息查看：支持企业查看已获得的安全生产标准化证书信息，包括证书编号、发证日期、有效期等，提供证书电子版下载和验证功能。
  - 证书状态管理：查看和管理证书状态，包括有效、即将到期、已过期等状态，支持证书续期申请和变更申请功能。

### 2.4 评审机构端（app/org）
- 首页
  - 系统概览：提供评审机构端系统整体运行状态和关键数据的可视化展示，包括待处理任务数量、评审进度等信息，帮助评审机构快速了解工作情况。
  - 通知公告：展示重要通知和公告信息，支持按重要程度分类显示，确保评审机构及时获取关键信息。
- 人员管理
  - 用户账号管理：提供评审机构内部用户的创建、权限分配、角色设置等功能，支持组织架构管理和用户状态监控，确保系统使用安全可控。
  - 人员信息维护：支持对人员基本信息、联系方式、职责权限等内容的维护和更新，确保人员信息准确有效。
- 任务管理
  - 任务接收确认：接收市应急管理局分配的评审任务，包括任务详情查看、评审能力自评、任务接收确认等功能，确保机构具备相应评审资质和能力。
  - 评审计划制定：根据企业情况和评审要求制定详细的评审计划，包括评审时间、评审内容、人员安排等，支持计划模板应用和定制化调整。
  - 任务状态更新：及时更新评审任务的执行状态，包括准备中、评审中、报告编制中等阶段标识，确保任务进度透明可见。
- 现场评审监督
  - 现场评审安排：制定详细的现场评审日程安排，包括评审路线、时间节点、人员配置等，支持现场调整和实时更新功能。
  - 评审过程记录：记录现场评审过程中的关键信息，包括检查情况、问题发现、整改建议等，支持文字记录和多媒体资料采集。
  - 视频监督：通过视频方式对现场评审进行远程监督，确保评审过程规范有序。
- 在线沟通
  - 评审群聊：提供评审机构与企业、专家之间的在线交流平台，支持文字、图片等多种形式的信息交换，促进评审过程中的有效沟通。
  - 问题反馈：接收和处理评审过程中的问题反馈，包括技术咨询、流程疑问等，提供及时响应和解决方案。

### 2.5 专家端（app/expert）
- 首页
  - 系统概览：提供专家端系统整体运行状态和关键数据的可视化展示，包括待处理任务数量、评审进度等信息，帮助专家快速了解工作情况。
  - 通知公告：展示重要通知和公告信息，支持按重要程度分类显示，确保专家及时获取关键信息。
- 评审任务
  - 评审任务接收：接收评审机构分配的评审任务，包括任务详情查看、时间安排确认、接受或婉拒选项等功能，合理安排工作计划。
  - 企业资料查阅：查看企业提交的自评报告和相关资料，包括资料分类浏览、关键信息标记、批注添加等功能，为评审工作做准备。
  - 评审要点检查：根据评审标准和要点进行系统化检查，包括检查项提示、评分指导、合规性分析等功能，确保评审全面准确。
- 在线交流
  - 评审群聊：参与评审机构与企业之间的在线交流，支持文字、图片等多种形式的信息交换，促进评审过程中的有效沟通。
  - 问题反馈：提出评审过程中发现的问题，包括技术咨询、流程疑问等，获取及时响应和解决方案。
- 续聘申请及退出
  - 续聘申请：提供专家续聘申请功能，包括个人业绩总结、专业能力说明等，支持申请状态跟踪和结果查询。
  - 退出申请：提供专家退出申请功能，包括退出原因说明、工作交接等，确保专家退出流程规范有序。
- 资料变更申请
  - 个人资料维护：支持专家维护和更新个人基本信息，包括联系方式、工作单位、专业领域等，确保专家信息准确及时。
  - 资质证明上传：上传专业资质证书、职称证书、培训证明等资质文件，支持证书有效期管理和到期提醒功能。
  - 变更申请提交：提供重要信息变更的申请功能，包括变更原因说明、证明材料提交、审核流程跟踪等，确保信息变更合规有序。
- 休假管理
  - 请假申请：支持专家提交请假申请，包括请假原因、时间段、替代方案等信息，确保评审工作不受影响。
  - 销假操作：提供专家销假功能，包括实际休假时间确认、工作恢复说明等，确保专家状态准确更新。
  - 休假记录查询：查询个人历史休假记录，包括请假时间、请假原因、审批状态等信息，支持休假统计和分析。

## 3. 数据库设计
### 3.1 核心表结构（详见docs/db.sql）
| 表名 | 说明 | 关键字段 |
|------|------|---------|
| tb_company | 企业基本信息 | credit_code, industry_type, safety_level |
| tb_review_task | 评审任务 | task_status, assign_org, deadline |
| tb_expert | 专家库 | qualification_no, specialty_area, is_available |

### 3.2 数据关系
```mermaid
classDiagram
  tb_company "1" -- "*" tb_review_task : 包含
  tb_review_task "1" -- "*" tb_expert : 分配
  tb_org "1" -- "*" tb_review_task : 承接
```

## 4. 接口规范
### 4.1 接口要求
- 遵循RESTful设计规范
- 请求/响应格式：application/json
- 统一状态码：
  ```php
  const CODE_SUCCESS = 200; // 操作成功
  const CODE_AUTH_FAIL = 403; // 权限异常
  const CODE_SYS_ERROR = 500; // 系统错误
  ```

### 4.2 典型接口示例
```php
// 评审任务创建接口
public function createTask()
{
    // 参数验证
    $params = $this->request->only([
        'org_id', 'expert_ids', 'company_id', 'deadline'
    ]);
    // 业务逻辑处理...
    return json([
        'code' => 200,
        'data' => $taskId
    ]);
}
```

## 5. 非功能需求
### 5.1 安全要求
1. 采用RBAC权限控制模型
2. 敏感数据加密存储（AES-256）
3. 文件上传格式白名单控制
4. 操作日志保留180天

### 5.2 性能指标
1. 常规操作响应时间 ≤2秒
2. 复杂报表生成 ≤30秒
3. 支持200+并发用户

## 6. 附录
- 数据库设计文档：`docs/db.sql`
- 接口调试文档：`docs/sign.md`
- 功能对照表：`docs/招标文件功能对照表.xlsx`