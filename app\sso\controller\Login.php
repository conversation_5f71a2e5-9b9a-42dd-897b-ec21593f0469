<?php

namespace app\sso\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use hg\apidoc\annotation as Apidoc;
use think\App;
use think\facade\Db;
use think\facade\View;
use think\facade\Cache;

/**
 * @Apidoc\Title("开放平台")
 * @Apidoc\Group("Sso")
 * @Apidoc\Sort(3)
 */
class Login
{

    public function login($appid=''){
        $loginApp = Db::table('top_login_app')->where(['appid'=>$appid])->find();
        if (request()->isAjax()) {
            if(empty($loginApp)){
                result('',7003,'未知应用');
            }
            $username = request()->param('username');
            $password = request()->param('password');
            $user = Db::table('td_user')->where(['byname'=>$username])->find();
            if(empty($user)){
                result('',1003,'账号或密码错误');
            }
            $checkpass = crypt($password, $user['PASSWORD']);
            if($checkpass!==$user['PASSWORD']){
                result('',1003,'账号或密码错误');
            }
            $data = [
                'appid' => $appid,
                'username' => $user['BYNAME'],
                'name' => $user['USER_NAME'],
                'phone' => $user['MOBIL_NO'],
            ];
            $key = md5($user['BYNAME'].time().rand(1000,9999));
            Cache::tag(['sso','login'])->set($key,$data,10);
            result($key);
        }else{
            if(empty($loginApp)){
                exit('页面错误');
            }
            View::assign('appid',$appid);
            return view();
        }
    }

    public function getBindUser($key=''){
        $data = Cache::get($key);
        if(empty($data)){
            result('',1001,'参数错误');
        }
        result($data);
    }

    function demo($state = '',$code=''){
        $loginApp = Db::table('top_login_app')->where(['id'=>$state])->find();
        if(empty($loginApp)){
            exit('state参数错误');
        }
        $url = 'http://oa.toppingsoft.com:45279/general/natural/index.php/sso/oauth/authorize?appid='.$loginApp['tp_appid'].'&redirect_uri='.urlencode('http://192.168.0.122/general/demo/index.php/sso/login/demo').'&state='.$state;
        if(empty($code)){
            return redirect($url);
        }else{
            $url = 'http://oa.toppingsoft.com:45279/general/natural/index.php/sso/oauth/accessToken?appid='.$loginApp['tp_appid'].'&appkey='.$loginApp['tp_appkey'].'&code=' . $code;
            $results = file_get_contents($url);
            $results = json_decode($results, true);
            $loginurl = '';
            return redirect($loginurl);
        }
    }

}
