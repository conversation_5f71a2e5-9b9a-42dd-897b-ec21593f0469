<?php
declare (strict_types = 1);

namespace app\city\controller;

use app\model\FileModel;
use app\model\MessageModel;
use app\model\SettingModel;
use think\facade\Db;
use think\facade\View;
use think\Request;
use app\model\ExcelModel;

class Ca extends Base
{
    /**
     * @Apidoc\Title("证书列表")
     * @Apidoc\Desc("证书列表")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function index($limit = 20, $excel = 0)
    {
        if (request()->isAjax()) {
            $status = $this->request->param('status','','trim');
            $title = $this->request->param('title','','trim');
            $area = $this->request->param('area','','trim');
            $industry = $this->request->param('industry','','trim');
            $level = $this->request->param('level','','trim');
            $year = $this->request->param('year','','trim');
            $where = [
                ['a.city_id','=',$_SESSION['city']['id']],
            ];
            if($_SESSION['city']['dept_id']!=1){
                $where[] =['a.dept_id','=',$_SESSION['city']['dept_id']];
            }
            if(!empty($area)){
                $where[] = ['a.area_id','=',"$area"];
            }
            if(!empty($industry)){
                $where[] = ['a.industry','=',"$industry"];
            }
            if(!empty($level)){
                $where[] = ['a.level','=',"$level"];
            }
            if(!empty($year)){
                $where[] = ['a.start','like',"$year%"];
            }
            if(!empty($title)){
                $where[] = ['a.company_name|a.code','like',"%{$title}%"];
            }
            if($status==1){
                $where[] = ['a.status','=',1];
                $where[] = ['a.publicity_status','=',0];
            }else if($status==2){
                $where[] = ['a.status','=',1];
                $where[] = ['a.publicity_status','=',1];
                $where[] = ['a.notify_status','=',0];
            }else if(!empty($status)){
                $where[] = ['a.status','=',$status];
            }
            $fields = "a.*,b.title,b.date,type,b.status pstatus,b.notify,b.notify_date";
            $res = Db::table('top_certificate')->alias('a')
                ->leftJoin('top_publicity b','a.publicity_id = b.id')
                ->where($where)->field($fields)->order('a.start desc');
            if($excel==1){
                $res = $res->select()->each(function ($item, $key) {
                    if($item['status']==7){
                        $item['status'] = strtotime($item['ends'])>strtotime(date('Y-m-d'))?7:8;
                    }
                    if($item['pstatus']>=1){
                        $days = floor(abs(strtotime(date('Y-m-d'))-strtotime($item['date']))/86400);
                        $item['publicity_status'] = $item['status']==1&&empty($item['notify'])?'已公示（'.$days.'天）':'已公示';
                    }else{
                        $item['publicity_status'] = '未公示';
                    }
                    if(!empty($item['notify_date'])){
                        $days = floor(abs(strtotime(date('Y-m-d'))-strtotime($item['notify_date']))/86400);
                        $item['notify_status'] = $item['status']==1&&!empty($item['notify_date'])?'已公告'.'（'.$days.'天）':'已公告';
                    }else{
                        $item['notify_status'] = '未公告';
                    }
                    $item['industry'] = $item['industry'].'/'.$item['specialty'];
                    if($item['status']==1){
                        $item['mb_status'] = '未生效';
                    }else if($item['status']==7){
                        $item['mb_status'] = '生效中';
                    }else if($item['status']==8){
                        $item['mb_status'] = '已过期';
                    }else if($item['status']==9){
                        $item['mb_status'] = '已撤销';
                    }
                    return $item;
                })->toArray();
                $title = [
                    ['title' => '企业名称', 'field' => 'company_name', 'width' => '30'],
                    ['title' => '行业/专业', 'field' => 'industry', 'width' => '30'],
                    ['title' => '证书编号', 'field' => 'code', 'width' => '30'],
                    ['title' => '印制编号', 'field' => 'printing_number', 'width' => '30'],
                    ['title' => '有效期', 'field' => 'ends', 'width' => '20'],
                    ['title' => '证书状态', 'field' => 'mb_status', 'width' => '20'],
                    ['title' => '公示状态', 'field' => 'publicity_status', 'width' => '20'],
                    ['title' => '公告状态', 'field' => 'notify_status', 'width' => '20'],
                ];
                ExcelModel::exportExcel($title, $res, '证书信息导出', true);
            }
            $res = $res->paginate($limit)->each(function ($item, $key) {
                if($item['status']==7){
                    $item['status'] = strtotime($item['ends'])>strtotime(date('Y-m-d'))?7:8;
                }
                if($item['pstatus']>=1){
                    $days = floor(abs(strtotime(date('Y-m-d'))-strtotime($item['date']))/86400);
                    $item['publicity_status'] = $item['status']==1&&empty($item['notify'])?'已公示（'.$days.'天）':'已公示';
                }else{
                    $item['publicity_status'] = '未公示';
                }
                if(!empty($item['notify_date'])){
                    $days = floor(abs(strtotime(date('Y-m-d'))-strtotime($item['notify_date']))/86400);
                    $item['notify_status'] = $item['status']==1&&!empty($item['notify_date'])?'已公告'.'（'.$days.'天）':'已公告';
                }else{
                    $item['notify_status'] = '未公告';
                }
                return $item;
            });
            result($res);
        } else {
            $area = Db::table('top_area')->alias('a')->leftJoin('top_pca b','a.pcas = b.code')->field('a.id,b.name')->select()->toArray();
            $industry = Db::table('top_industry')->where(['pid'=>0])->field('id,name')->select()->toArray();
            View::assign('area', $area);
            View::assign('industry', $industry);
            View::assign('title', '首页');
            return view();
        }
    }

    public function publicitySave($type=1){
        $params = $this->request->param();
        $data = [
            'title' => trim($params['title']),
            'date' => trim($params['date']),
            'image' => trim($params['image']),
            'type' => $type,
            'status' => 1,
        ];
        if(empty($data['title'])){
            result('',1002,'请填写公示标题');
        }
        if(empty($data['date'])){
            result('',1002,'请选择公示日期');
        }
        if(empty($params['list'])){
            result('',1002,'请选择公示名单');
        }
        $ids = [];
        foreach ($params['list'] as $v){
            $ids[] = $v['id'];
        }
        $content = '<table class="table-notify"><tbody><tr class="firstRow"><th valign="middle" style="word-break: break-all;" align="center"><strong>序号</strong></th><th valign="middle" style="word-break: break-all;" align="center"><strong>区县</strong></th><th valign="middle" style="word-break: break-all;" align="center"><strong>企业名称<br/></strong></th><th valign="middle" style="word-break: break-all;" align="center"><strong>行业/专业</strong></th></tr>';
        $cas = Db::table('top_certificate')->where('id','in',$ids)->select()->toArray();
        $pcas = SettingModel::getPcasAll(0,3);
//        dd($pcas);
        foreach ($cas as $k=>$v){
            $content .= '<tr><td valign="middle" align="center">'.($k+1).'</td><td valign="middle" align="center">'.$pcas[$v['area']].'</td><td valign="middle" align="center">'.$v['company_name'].'</td><td valign="middle" align="center">'.$v['industry'].'/'.$v['specialty'].'</td></tr>';
        }
        $content .= '</tbody></table>';
        $data_notify = [
            'type' => 4,//类型
            'title' => $data['title'],//标题
            'date' => $data['date'],//发布日期
            'summary' => $data['date'].'企业达标名单公示',//简介
            'is_show' => 1,//是否展示
            'content' => $content,//详细内容
        ];
        Db::table('top_notify')->insert($data_notify);//发系统的通知公告$noid 建议可以用 insertGetId
        //$noid = Db::table('top_notify')->insertGetId($data_notify);//达梦数据库返回的是改变数据的行数
        unset($data_notify['content']);
        $noid = Db::table('top_notify')->where($data_notify)->field('id')->find()['id'];
        $data['user_id'] = $_SESSION['city']['user_id'];
        $data['user_name'] = $_SESSION['city']['user_name'];
        $data['time'] = date('Y-m-d H:i:s');
        $data['no_id'] = $noid;
        Db::table('top_publicity')->insert($data);//公示$id 建议可以用 insertGetId
        //$id = Db::table('top_publicity')->insertGetId($data);//达梦数据库返回的是改变数据的行数
        unset($data['image']);
        $id = Db::table('top_publicity')->where($data)->field('id')->find()['id'];
        Db::table('top_certificate')->where('id','in',$ids)->update(['publicity_id'=>$id,'publicity_status'=>1]);
        result('',0,'公示完成');
    }


    public function notifySave($id=0){
        $params = $this->request->param();
        $data = [
            'notify' => trim($params['notify']),
            'notify_date' => trim($params['notify_date']),
            'status'=>2,
        ];
        if(empty($data['notify'])){
            result('',1002,'请填写公告标题');
        }
        if(empty($data['notify_date'])){
            result('',1002,'请选择公告日期');
        }
        $up = Db::table('top_publicity')->where(['id'=>$id])->update($data);
        Db::table('top_certificate')->where('publicity_id','=',$id)->update(['notify_status'=>1,'notify_summary'=>$data['notify'],'notify_time'=>$data['notify_date']]);
        result('',0,'公告完成');
    }


    public function end($id=0){
        $re = Db::table('top_publicity')->where(['id'=>$id])->find();
        if(empty($re)){
            result('',1002,'数据不存在');
        }
        $data = [
            'status' => 7,
        ];
        $up = Db::table('top_publicity')->where(['id'=>$id])->update($data);
        if($up){
            $number = Db::table('top_certificate')->where([['number','>',0]])->order('number desc')->find()['number'];
            $printing_number = Db::table('top_certificate')->where([['printing_number','>',0]])->order('printing_number desc')->find()['printing_number'];
            $res = Db::table('top_certificate')->where('publicity_id','=',$id)->select()->toArray();
            $industry = SettingModel::getIndustry();
            foreach ($res as $v){
                if(empty($v['code'])){
                    foreach ($industry as $k1=>$v1){
                        if($v['industry']==$v1){
                            $dh = $k1;
                        }
                    }
                    $number ++;
                    $printing_number ++;
                    $data = [
                        'code' => "AQBⅢ".$dh."(川)".date('Y',strtotime($re['notify_date'])).$number,
                        'start' => $re['notify_date'],
                        'ends' => date('Y-m-d',strtotime('+3year',strtotime($re['notify_date']))),
                        'status' => 7,
                        'number' => $number,
                        'printing_number' => $printing_number,
                    ];
                    Db::table('top_certificate')->where(['id'=>$v['id']])->update($data);
                }
            }
        }else{
            result('',2002,'网络错误');
        }
        result('',0,'公告完成');
    }


    public function revoke($id=0,$remark=''){
        $re = Db::table('top_certificate')->where('id','=',$id)->find();
        if(empty($re)||$re['status']!=7){
            result('',1001,'证书不存在或未生效');
        }
        $data = [
            'status' => 9,
        ];
        Db::table('top_certificate')->where(['id'=>$id])->update($data);
        $company = Db::table('top_company_info')->where(['id'=>$re['company_id']])->field('user_id')->find();
        MessageModel::sendSms("company",$company['user_id'],'证书撤销',"您的编号为“{$re['code']}”的证书已被撤销，撤销原因：{$remark}！");
        result('',0,'撤销完成');
    }

    public function publicty($limit = 20, $excel = 0){
        if (request()->isAjax()) {
            $status = $this->request->param('status','','trim');
            $title = $this->request->param('title','','trim');
            $where = [];
            if(!empty($status)){
                $where[] = ['status','=',$status];
            }
            if(!empty($title)){
                $where[] = ['title','like',"%{$title}%"];
            }
            $res = Db::table('top_publicity')
                ->where($where)->order('date desc')
                ->paginate($limit)->each(function ($item, $key) {
                    return $item;
                });
            result($res);
        } else {
            View::assign('title', '首页');
            return view();
        }
    }

    public function policyInfo($id = 0){
        $where[] = ['is_del','=',0];
        $where[] = ['id','=',$id];
        $res = Db::table('top_notify')
            ->where($where)
            ->find();
        if(empty($res)){
            result('',1002,'数据有误');
        }
        result($res);
    }

    public function publictyInfo($limit = 20, $excel = 0){
        if (request()->isAjax()) {
            $status = $this->request->param('status','','trim');
            $title = $this->request->param('title','','trim');
            $where = [];
            if(!empty($status)){
                $where[] = ['status','=',$status];
            }
            if(!empty($title)){
                $where[] = ['title','like',"%{$title}%"];
            }
            $res = Db::table('top_publicity')
                ->where($where)->order('date desc')
                ->paginate($limit)->each(function ($item, $key) {
                    return $item;
                });
            result($res);
        } else {
            View::assign('title', '首页');
            return view();
        }
    }
}
