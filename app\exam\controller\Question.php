<?php

namespace app\exam\controller;

use app\BaseController;
use app\model\ExcelModel;
use app\model\SettingModel;
use PHPExcel_IOFactory;
use think\Exception;
use think\facade\Db;
use think\Request;
use think\facade\View;

class Question extends BaseController
{

    protected $type = 'question';//

    public function datalist(Request $request)
    {
        try {

            return View::fetch('datalist');
        } catch (\Exception $exception) {
            Db::rollback();
            return json(['code' => 201, 'type' => 'error', 'msg' => $exception->getMessage()]);
        }
    }


    /**
     * @param Request $request
     * @return \think\response\Json
     *
     * 获取基础库
     */
    public function getBaseTestInfoList(Request $request)
    {
        try {
            $searchData = $request->param('searchData', []);
            $limit = $request->param('limit', 10);
            $db = Db::table('top_cay_exam_test_info')
                ->alias('i')
                ->field(['i.*', 't.type_name'])
                ->leftJoin('top_cay_exam_test_type t', 'i.type_id=t.id');

            foreach ($searchData as $key => $value) {
                switch ($key) {
                    default:
                        if (!empty($value)) {
                            $db->where('i.'.$key, 'like', '%' . $value . '%');
                        }
                }
            }
            $data = $db->whereNull('industry_ids')->whereNull('deleted_at')->paginate($limit);
            return json(['code' => 1000, 'data' => $data, 'type' => 'success', 'msg' => "获取成功"]);
        } catch (\Exception $exception) {
            Db::rollback();
            return json(['code' => 201, 'type' => 'error', 'msg' => $exception->getMessage()]);
        }
    }

    /**
     * @param Request $request
     * @return \think\response\Json
     * 获取专业库
     */
    public function getSpecialityTestInfoList(Request $request)
    {
        try {
            $searchData = $request->param('searchData', []);
            $limit = $request->param('limit', 10);
            $db = Db::table('top_cay_exam_test_info')
                ->alias('i')
                ->field(['i.*', 't.type_name', 's.name as standard_name'])
                ->leftJoin('top_cay_exam_test_type t', 'i.type_id=t.id')
                ->leftJoin('top_standard_name s', 'i.standard_name_id=s.id');


            foreach ($searchData as $key => $value) {
                switch ($key) {
                    case 'current_standard_id':
                        if (!empty($value)) {
                            $db->where('i.standard_name_id', $value);
                        } else {
                            $db->whereNotNull('i.standard_name_id');
                        }

                        break;
                    default:
                        if (!empty($value)) {
                            $db->where('i.' . $key, 'like', '%' . $value . '%');
                        }
                }
            }
            $data = $db->whereNotNull('i.standard_name_id')
                ->whereNull('i.deleted_at')
                ->paginate($limit);
            return json(['code' => 1000, 'data' => $data, 'type' => 'success', 'msg' => "获取成功"]);
        } catch (\Exception $exception) {
            Db::rollback();
            return json(['code' => 201, 'type' => 'error', 'msg' => $exception->getMessage()]);
        }
    }

    public function importBaseTestInfo(Request $request)
    {
        try {
            $industry_ids = $request->param('industry_ids', "");
            $file = $request->file('file');
            if(!is_null($industry_ids)){
                //去除空数据的情况
                $industryArr = explode(',', $industry_ids);
                $industryArr = array_filter($industryArr);
                $industry_ids = implode(',', $industryArr);
                if(empty($industry_ids)){
                    $industry_ids =null;
                }else{
                    $industry_ids = ',' . $industry_ids . ',';
                }

            }
            set_time_limit(0);
            ini_set('memory_limit', '2048M');
            $file_type = PHPExcel_IOFactory::identify($file);
            $reader = PHPExcel_IOFactory::createReader($file_type);
            $spreadSheet = $reader->load($file);
            $sheet = $spreadSheet->getActiveSheet();
            $data = $sheet->toArray(null, true, true, true);
            if (empty($data) || count($data) == 1) {
                throw new Exception("数据不能为空！");
            }
            Db::startTrans();
            unset($data[1]);
            foreach ($data as $item) {

                //解析类型
                $type_id = Db::table('top_cay_exam_test_type')->where('type_name', $item['D'])->value('id');
                if (empty($type_id)) {
                    throw new Exception("试题类型未选择");
                }

                //解析选项
                $test_title = $item['A'];
                $test_answer = $item['B'];
                $test_analysis = empty($item['C']) ? "" : $item['C'];
                unset($item['A']);
                unset($item['C']);
                unset($item['B']);
                unset($item['D']);
                $test_option = self::removeNullValues($item);
                $test_option_res = [];
                foreach ($test_option as $key => $value) {
                    $test_option_res[] = $value;
                }
                $test_option_str = json_encode($test_option_res, JSON_UNESCAPED_UNICODE);
                //解析答案
                $test_answer = str_replace('、', ",", $test_answer);
                $test_answer = str_replace('选项', "", $test_answer);
                $test_answer = explode(',', $test_answer);
                $test_answer_char = "";
                if (count($test_answer) != 1) {
                    throw new Exception("答案有误");
                }

                $test_answer = implode(',', $test_answer);
                if ($type_id == 1) {

                    $char = self::IntToChr($test_answer - 1);
                    $test_answer_char = $char;
                } else {
                    //解析答案
                    $test_answer = $test_answer + 5;
                    $char = self::IntToChr($test_answer);

                    $test_answer_char = $item[$char];
                }

                $insertData = [
                    'test_title' => $test_title,
                    'test_option' => $test_option_str,
                    'test_answer' => $test_answer_char,
                    'test_analysis' => $test_analysis,
                    'type_id' => $type_id,
                    'industry_ids' => $industry_ids,
                    'create_user_name' => $_SESSION['LOGIN_USER_NAME'],
                    'create_user_id' => $_SESSION['LOGIN_USER_ID'],
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                    'deleted_at' => null,
                ];
                //插入数据
                Db::table('top_cay_exam_test_info')
                    ->insert($insertData);
            }
            Db::commit();
            return json(['code' => 1000, 'type' => 'success', 'msg' => "导入成功"]);
        } catch (\Exception $exception) {
            Db::rollback();
            return json(['code' => 201, 'type' => 'error', 'msg' => $exception->getMessage()]);
        }
    }
    public function importSpecialityTestInfo(Request $request)
    {
        try {
            $standard_name_id = $request->param('standard_name_id', "");
            $file = $request->file('file');
            if(!is_null($standard_name_id) && !empty($standard_name_id)){
                // 验证评审标准是否存在
                $standardExists = Db::table('top_standard_name')
                    ->where('id', $standard_name_id)
                    ->where('is_del', 0)
                    ->count();
                if (!$standardExists) {
                    throw new Exception("评审标准不存在！");
                }
            } else {
                $standard_name_id = null;
            }
            set_time_limit(0);
            ini_set('memory_limit', '2048M');
            $file_type = PHPExcel_IOFactory::identify($file);
            $reader = PHPExcel_IOFactory::createReader($file_type);
            $spreadSheet = $reader->load($file);
            $sheet = $spreadSheet->getActiveSheet();
            $data = $sheet->toArray(null, true, true, true);
            if (empty($data) || count($data) == 1) {
                throw new Exception("数据不能为空！");
            }
            Db::startTrans();
            unset($data[1]);
            foreach ($data as $item) {

                //解析类型
                $type_id = Db::table('top_cay_exam_test_type')->where('type_name', $item['D'])->value('id');
                if (empty($type_id)) {
                    throw new Exception("试题类型未选择");
                }

                //解析选项
                $test_title = $item['A'];
                $test_answer = $item['B'];
                $test_analysis = empty($item['C']) ? "" : $item['C'];
                unset($item['A']);
                unset($item['C']);
                unset($item['B']);
                unset($item['D']);
                $test_option = self::removeNullValues($item);
                $test_option_res = [];
                foreach ($test_option as $key => $value) {
                    $test_option_res[] = $value;
                }
                $test_option_str = json_encode($test_option_res, JSON_UNESCAPED_UNICODE);
                //解析答案
                $test_answer = str_replace('、', ",", $test_answer);
                $test_answer = str_replace('选项', "", $test_answer);
                $test_answer = explode(',', $test_answer);
                $test_answer_char = "";
                if (count($test_answer) != 1) {
                    throw new Exception("答案有误");
                }

                $test_answer = implode(',', $test_answer);
                if ($type_id == 1) {

                    $char = self::IntToChr($test_answer - 1);
                    $test_answer_char = $char;
                } else {
                    //解析答案
                    $test_answer = $test_answer + 5;
                    $char = self::IntToChr($test_answer);

                    $test_answer_char = $item[$char];
                }

                $insertData = [
                    'test_title' => $test_title,
                    'test_option' => $test_option_str,
                    'test_answer' => $test_answer_char,
                    'test_analysis' => $test_analysis,
                    'type_id' => $type_id,
                    'standard_name_id' => $standard_name_id,
                    'create_user_name' => $_SESSION['LOGIN_USER_NAME'],
                    'create_user_id' => $_SESSION['LOGIN_USER_ID'],
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                    'deleted_at' => null,
                ];
                //插入数据
                Db::table('top_cay_exam_test_info')
                    ->insert($insertData);
            }
            Db::commit();
            return json(['code' => 1000, 'type' => 'success', 'msg' => "导入成功"]);
        } catch (\Exception $exception) {
            Db::rollback();
            return json(['code' => 201, 'type' => 'error', 'msg' => $exception->getMessage()]);
        }
    }

    public function importTemplate()
    {
        $fields = [
            'a' => '试题名称',
            'b' => '答案',
            'c' => '解析',
            'd' => '类型',
            'e' => '选项1',
            'f' => '选项2',
            'g' => '选项3',
            'h' => '选项4',
            'i' => '选项5',
            'j' => '选项6',

        ];
        foreach ($fields as $k => $v) {
            $title[] = ['title' => $v, 'field' => $k, 'width' => '20', 'type' => 'string'];
        }
        $data = [
            [
                'a' => '试题名称xxxxx',
                'b' => '选项1',
                'c' => '解析',
                'd' => '单选',
                'e' => 'AAAA',
                'f' => 'BBBB',
                'g' => 'CCCCC',
                'h' => 'DDD',
            ],
            [
                'a' => '试题名称xxxxx',
                'b' => '选项1',
                'c' => '解析',
                'd' => '判断',
                'e' => '对',
                'f' => '错',
            ]];
        ExcelModel::exportExcel($title, $data, '试题导入模板');
    }

    /**
     * @param Request $request
     * @return \think\response\Json|void
     *
     * 删除试题
     */
    public function deleteOne(Request $request)
    {
        try {
            $id = $request->param('test_id', 0);
            Db::table('top_cay_exam_test_info')
                ->where('id', $id)
                ->update([
                    'deleted_at' => date('Y-m-d H:i:s')]);
            Db::commit();
            return json(['code' => 1000, 'type' => 'success', 'msg' => "删除成功"]);
        } catch (\Exception $exception) {
            Db::rollback();
            return json(['code' => 201, 'type' => 'error', 'msg' => $exception->getMessage()]);
        }
    }

    /**
     * @param Request $request
     * @return \think\response\Json|void
     *
     * 修改试题
     */
    public function changeOne(Request $request)
    {
        try {
            $id = $request->param('id', 0);
            $test_analysis = $request->param('test_analysis', "");
            $test_answer = $request->param('test_answer', "");
            $test_option = $request->param('test_option', "");
            $test_title = $request->param('test_title', "");
            $type_id = $request->param('type_id', "");
            $industry_ids = $request->param('industry_ids', null);
            $standard_name_id = $request->param('standard_name_id', null);
            
            // 处理industry_ids（基础库和专业库都可能使用）
            if (!is_null($industry_ids) && !empty($industry_ids)) {
                $industryArr = explode(',', $industry_ids);
                $industryArr = array_filter($industryArr);
                $industry_ids = implode(',', $industryArr);
                if (empty($industry_ids)) {
                    $industry_ids = null;
                } else {
                    $industry_ids = ',' . $industry_ids . ',';
                }
            } else {
                $industry_ids = null;
            }
            
            // 处理standard_name_id（专业库使用）
            if (!is_null($standard_name_id) && !empty($standard_name_id)) {
                // 验证评审标准是否存在
                $standardExists = Db::table('top_standard_name')
                    ->where('id', $standard_name_id)
                    ->where('is_del', 0)
                    ->count();
                if (!$standardExists) {
                    throw new Exception("评审标准不存在！");
                }
            } else {
                $standard_name_id = null;
            }
            if (empty($test_title)) {
                throw new Exception("试题不能为空！");
            }
            if (empty($type_id)) {
                throw new Exception("试题类型不能为空！");
            }
            if (empty($test_answer)) {
                throw new Exception("试题答案为空！");
            }
            if (empty($test_option)) {
                throw new Exception("试题选项为空！");
            }
            Db::startTrans();

            $test_option_res = [];
            foreach ($test_option as $item) {
                if (empty($item['option'])) {
                    throw new Exception("选项不能为空");
                }
                $test_option_res[] = $item['option'];
            }

            $search = array_search($test_answer, $test_option_res);
            if ($search === false) {
                throw new Exception("答案有误");
            }
            $test_option_str = json_encode($test_option_res, JSON_UNESCAPED_UNICODE);


            if ($type_id == 1) {
                $test_answer_char = self::IntToChr($search);
            } else {
                $test_answer_char = $test_answer;
            }
            $updateData = [
                'test_title' => $test_title,
                'test_option' => $test_option_str,
                'test_answer' => $test_answer_char,
                'test_analysis' => $test_analysis,
                'type_id' => $type_id,
                'industry_ids' => $industry_ids,
                'standard_name_id' => $standard_name_id,
                'create_user_name' => $_SESSION['LOGIN_USER_NAME'],
                'create_user_id' => $_SESSION['LOGIN_USER_ID'],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'deleted_at' => null,
            ];
            //更新数据
            Db::table('top_cay_exam_test_info')
                ->where('id', $id)
                ->update($updateData);
            Db::commit();
            return json(['code' => 1000, 'type' => 'success', 'msg' => "修改成功"]);
        } catch (\Exception $exception) {
            Db::rollback();
            return json(['code' => 201, 'type' => 'error', 'msg' => $exception->getMessage()]);
        }
    }

    public function addOne(Request $request)
    {
        try {
            $test_analysis = $request->param('test_analysis', "");
            $test_answer = $request->param('test_answer', "");
            $test_option = $request->param('test_option', "");
            $test_title = $request->param('test_title', "");
            $type_id = $request->param('type_id', "");
            $industry_ids = $request->param('industry_ids', null);
            $standard_name_id = $request->param('standard_name_id', null);
            
            // 处理industry_ids（基础库和专业库都可能使用）
            if (!is_null($industry_ids) && !empty($industry_ids)) {
                $industryArr = explode(',', $industry_ids);
                $industryArr = array_filter($industryArr);
                $industry_ids = implode(',', $industryArr);
                if (empty($industry_ids)) {
                    $industry_ids = null;
                } else {
                    $industry_ids = ',' . $industry_ids . ',';
                }
            } else {
                $industry_ids = null;
            }
            
            // 处理standard_name_id（专业库使用）
            if (!is_null($standard_name_id) && !empty($standard_name_id)) {
                // 验证评审标准是否存在
                $standardExists = Db::table('top_standard_name')
                    ->where('id', $standard_name_id)
                    ->where('is_del', 0)
                    ->count();
                if (!$standardExists) {
                    throw new Exception("评审标准不存在！");
                }
            } else {
                $standard_name_id = null;
            }
            if (empty($test_title)) {
                throw new Exception("试题不能为空！");
            }
            if (empty($type_id)) {
                throw new Exception("试题类型不能为空！");
            }
            if (empty($test_answer)) {
                throw new Exception("试题答案为空！");
            }
            if (empty($test_option)) {
                throw new Exception("试题选项为空！");
            }
            Db::startTrans();

            $test_option_res = [];
            foreach ($test_option as $item) {
                if (empty($item['option'])) {
                    throw new Exception("选项不能为空");
                }
                $test_option_res[] = $item['option'];
            }
            $search = array_search($test_answer, $test_option_res);
            if ($search === false) {
                throw new Exception("答案有误");
            }
            $test_option_str = json_encode($test_option_res, JSON_UNESCAPED_UNICODE);
            if ($type_id == 1) {
                $test_answer_char = self::IntToChr($search);
            } else {
                $test_answer_char = $test_answer;
            }
            $insertData = [
                'test_title' => $test_title,
                'test_option' => $test_option_str,
                'test_answer' => $test_answer_char,
                'test_analysis' => $test_analysis,
                'type_id' => $type_id,
                'industry_ids' => $industry_ids,
                'standard_name_id' => $standard_name_id,
                'create_user_name' => $_SESSION['LOGIN_USER_NAME'],
                'create_user_id' => $_SESSION['LOGIN_USER_ID'],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'deleted_at' => null,
            ];
            //插入数据
            Db::table('top_cay_exam_test_info')->insert($insertData);
            Db::commit();
            return json(['code' => 1000, 'type' => 'success', 'msg' => "添加成功"]);
        } catch (\Exception $exception) {
            Db::rollback();
            return json(['code' => 201, 'type' => 'error', 'msg' => $exception->getMessage()]);
        }
    }

    /**
     * @param Request $request
     * @return \think\response\Json|void
     *
     * 获取一个试题详情
     */
    public function getOneTestInfo(Request $request)
    {
        try {
            $id = $request->param('test_id', 0);
            $model = Db::table('top_cay_exam_test_info')->find($id);
            $model['test_option'] = json_decode($model['test_option']);

            if ($model['type_id'] == 1) {
                $model['test_answer'] = self::convertStrToNumber($model['test_answer']);
                $model['test_answer'] = $model['test_option'] [$model['test_answer'] - 1];
            }


            $test_option = [];
            foreach ($model['test_option'] as $value) {
                $test_option[] = ['option' => $value];
            }
            $model['test_option'] = $test_option;
            return json(['code' => 1000, 'type' => 'success', 'data' => $model]);
        } catch (\Exception $exception) {
            Db::rollback();
            return json(['code' => 201, 'type' => 'error', 'msg' => $exception->getMessage()]);
        }
    }

    public function getStandardNameList(Request $request)
    {
        try {
            $standardList = Db::table('top_standard_name')
                ->where('is_del', 0)
                ->where('status', 1)
                ->field(['id', 'name', 'element'])
                ->order('sort', 'asc')
                ->select()
                ->toArray();
            return json(['code' => 1000, 'type' => 'success', 'data' => $standardList]);
        } catch (\Exception $exception) {
            Db::rollback();
            return json(['code' => 201, 'type' => 'error', 'msg' => $exception->getMessage()]);
        }
    }


    /**
     * @param $array
     * @return array
     * 移除空的值
     */
    public static function removeNullValues($array)
    {
        return array_filter($array, function ($value) {
            return $value !== null;
        });
    }


    public static function convertStrToNumber($str)
    {

        $arr = range('A', 'Z');
        for ($limit = 26, $r = $i = 0, $l = strlen($str); $i < $l; $i++) {
            $find = substr($str, $l - $i - 1, 1);
            $page = array_search($find, $arr) + 1;
            $r += $page * pow($limit, $i);
        }
        return $r;
    }

    /**
     * 数字转字母 （类似于Excel列标）
     * @param Int $index 索引值
     * @param Int $start 字母起始值
     * @return String 返回字母
     */
    public static function IntToChr($index, $start = 65)
    {
        $str = '';
        if (floor($index / 26) > 0) {
            $str .= self::IntToChr(floor($index / 26) - 1);
        }
        return $str . chr($index % 26 + $start);
    }


    /**
     * @param Request $request
     * @return \think\response\Json
     *
     * 下载题库试题
     */
    public function downTestByQuestionId(Request $request)
    {
        try {
            $question_id = $request->param('question_id', 0);
            $testList = Db::table('top_train_exam_test_info')
                ->alias('i')
                ->field([
                    'i.subject_name',
                    'd.difficulty_name',
                    't.type_name',
                    'i.test_title',
                    'i.test_option',
                    'i.test_answer',
                    'i.test_analysis',
                ])
                ->where('question_id', $question_id)
                ->whereNull('i.deleted_at')
                ->leftJoin("top_train_exam_type t", 't.id=i.last_type_id')
                ->leftJoin("top_train_exam_difficulty d", 'd.id = i.difficulty_id')
                ->select()
                ->each(function ($model) {
                    $model['subject_name'] = json_decode($model['subject_name']);
                    $model['test_option'] = json_decode($model['test_option']);
                    $model['test_answer'] = json_decode($model['test_answer']);
                    $model['level_1'] = $model['subject_name'][0];
                    $model['level_2'] = $model['subject_name'][1];
                    $model['level_3'] = $model['subject_name'][2];
                    $model['level_4'] = $model['subject_name'][3];


                    $model['test_option_1'] = $model['test_option'][0];
                    $model['test_option_2'] = $model['test_option'][1];
                    $model['test_option_3'] = $model['test_option'][2];
                    $model['test_option_4'] = $model['test_option'][3];

                    unset($model['subject_name']);
                    unset($model['test_option']);

                    return $model;
                })
                ->toArray();
            if (empty($testList)) {
                throw new Exception('题库试题为空不能执行导出功能');
            }
            $data = [];
            foreach ($testList as $item) {
                $son = [];
                $son[] = $item['level_1'];
                $son[] = $item['level_3'];
                $son[] = $item['level_2'];
                $son[] = $item['level_4'];
                $son[] = $item['difficulty_name'];
                $son[] = $item['type_name'];
                $son[] = $item['test_title'];
                $test_answer_arr = [];
                foreach ($item['test_answer'] as $test_answer) {
                    switch ($test_answer) {
                        case $item['test_option_1']:
                            $test_answer_arr = ['要素9'];
                            break;
                        case $item['test_option_2']:
                            $test_answer_arr = ['要素10'];
                            break;
                        case $item['test_option_3']:
                            $test_answer_arr = ['要素11'];
                            break;
                        case $item['test_option_4']:
                            $test_answer_arr = ['要素12'];
                            break;
                    }
                }
                $son[] = implode('\\', $test_answer_arr);
                $son[] = $item['test_option_1'];
                $son[] = $item['test_option_2'];
                $son[] = $item['test_option_3'];
                $son[] = $item['test_option_4'];
                $son[] = $item['test_analysis'];
                $data[] = $son;
            }


            $header = [
                "要素1-大纲名称",
                "要素2-层级",
                "要素3-岗位/系统",
                "要素4-课目",
                "要素5-标准",
                "要素6-题型",
                "要素7-题干",
                "要素8-答案项",
                "要素9-答案1",
                "要素10-答案2",
                "要素11-答案3",
                "要素12-答案4",
                "要素12-答案4",
                "要素13-解析",
            ];
            $model = Db::table('top_train_exam_question')->find($question_id);
            $name = '/exam/' . $model['question_name'] . time() . ".xlsx";
            export($header, $data, $name);

            return json(['code' => 1000, 'type' => 'success', 'url' => '/general/toppingsoft/public/storage' . $name, 'msg' => "导出成功"]);
        } catch (\Exception $exception) {
            return json(['code' => 201, 'type' => 'error', 'msg' => $exception->getMessage()]);
        }
    }

}