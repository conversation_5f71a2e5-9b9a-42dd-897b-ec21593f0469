//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package kl.hsm.client.sdk;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.io.StringWriter;
import java.math.BigInteger;
import java.nio.ByteBuffer;
import java.nio.file.Paths;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.SecureRandom;
import java.security.Security;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import kl.hsm.common.BCInit;
import kl.hsm.common.Constonts;
import kl.hsm.common.HsmConfig;
import kl.hsm.common.HsmErrorCode;
import kl.hsm.common.SdkUtil;
import kl.hsm.pool.CryptoClient;
import kl.hsm.pool.CryptoClientKeyConfig;
import kl.hsm.pool.CryptoClientKeyFactory;
import kl.hsm.pool.CryptoClientKeyPool;
import kl.hsm.server.svc.base.Algo;
import kl.hsm.server.svc.base.AsymKeypair;
import kl.hsm.server.svc.base.EnvelopedKeyPair;
import kl.hsm.server.svc.base.HashAlgoParam;
import kl.hsm.server.svc.base.IntBinary;
import kl.hsm.server.svc.base.Padding;
import kl.hsm.server.svc.base.SM9Encapsulate;
import kl.hsm.server.svc.base.SM9KeyExchange;
import kl.hsm.server.svc.base.SvcException;
import kl.hsm.server.svc.base.SymParam;
import kl.hsm.server.svc.base.keyAgreementResp;
import kl.nbase.security.crypto.custom.cooperation.DecResult1;
import kl.nbase.security.crypto.custom.cooperation.DecResult2;
import kl.nbase.security.crypto.custom.cooperation.DecResult3;
import kl.nbase.security.crypto.custom.cooperation.GenKeyResult1;
import kl.nbase.security.crypto.custom.cooperation.GenKeyResult2;
import kl.nbase.security.crypto.custom.cooperation.SignResult1;
import kl.nbase.security.crypto.custom.cooperation.SignResult2;
import kl.nbase.security.crypto.custom.cooperation.SignResult3;
import kl.nbase.security.math.ec.ECPoint;
import kl.ssl.gmvpn.crypto.impl.jcajce.JcaTlsCryptoProvider;
import kl.ssl.jsse.provider.KlGMJsseProvider;
import koal.org.apache.commons.lang3.ArrayUtils;
import koal.org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.apache.thrift.transport.MyTSSLTransportFactory;
import org.bouncycastle.crypto.Digest;
import org.bouncycastle.crypto.digests.MD5Digest;
import org.bouncycastle.crypto.digests.SHA1Digest;
import org.bouncycastle.crypto.digests.SHA256Digest;
import org.bouncycastle.crypto.digests.SHA384Digest;
import org.bouncycastle.crypto.digests.SHA512Digest;
import org.bouncycastle.crypto.digests.SM3Digest;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPublicKey;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.openssl.jcajce.JcaPEMWriter;
import org.bouncycastle.util.encoders.Hex;
import org.bouncycastle.util.io.pem.PemObject;

public class HsmSdk extends BCInit {
    public static final String CLIENT_KEY = "hsm";
    private int timeOut = 60000;
    private int maxWaitMillis = 10000;
    public static final int POOL_MAX_TOTAL = 16;
    private String path;
    private static final AtomicBoolean isOpen = new AtomicBoolean(false);
    private int faultCount = 150;
    private Random random;
    public static List<CryptoClientKeyPool> poolList = new ArrayList();
    public static final Map<Long, CryptoClientKeyPool> sessionPool = new HashMap();
    public static int retryCount = 1;
    public static ThreadLocal<CryptoClientKeyPool> poolThreadLocal = new ThreadLocal();
    public static ThreadLocal<Integer> indexThreadLocal = new ThreadLocal();
    private Map<String, AtomicInteger> errIpMap = new HashMap();

    public HsmSdk() {
    }

    private synchronized void poolInit(HsmConfig hsmConfig) throws SvcException {
        if (!isOpen.get()) {
            int poolMax = hsmConfig.getPoolMax();
            retryCount = hsmConfig.getIps().size();
            hsmConfig.getIps().forEach((ip) -> {
                CryptoClientKeyConfig cryptoClientKeyConfig = new CryptoClientKeyConfig(ip, hsmConfig.getPort(), this.timeOut, hsmConfig.getSsl());
                cryptoClientKeyConfig.setLifo(false);
                cryptoClientKeyConfig.setTestWhileIdle(true);
                cryptoClientKeyConfig.setTimeBetweenEvictionRunsMillis(8000L);
                cryptoClientKeyConfig.setNumTestsPerEvictionRun(30);
                cryptoClientKeyConfig.setMaxTotal(poolMax);
                cryptoClientKeyConfig.setMaxTotalPerKey(poolMax);
                int maxIdle = poolMax / 2 > 1 ? poolMax / 2 : 1;
                int minIdle = maxIdle == 1 ? 1 : maxIdle / 2;
                cryptoClientKeyConfig.setMinIdlePerKey(minIdle);
                cryptoClientKeyConfig.setMaxIdlePerKey(maxIdle);
                cryptoClientKeyConfig.setMaxWaitMillis((long)this.maxWaitMillis);
                cryptoClientKeyConfig.setBlockWhenExhausted(false);
                cryptoClientKeyConfig.setTestOnCreate(false);
                cryptoClientKeyConfig.setTestOnBorrow(true);
                cryptoClientKeyConfig.setTestOnReturn(false);
                cryptoClientKeyConfig.setMinEvictableIdleTimeMillis(3000000L);
                cryptoClientKeyConfig.setSoftMinEvictableIdleTimeMillis(-1L);
                cryptoClientKeyConfig.setJmxEnabled(true);
                cryptoClientKeyConfig.setJmxNameBase(cryptoClientKeyConfig.getIp());
                cryptoClientKeyConfig.setJmxNamePrefix("hsm-pool");
                CryptoClientKeyFactory cryptoClientKeyFactory = new CryptoClientKeyFactory(cryptoClientKeyConfig);
                CryptoClientKeyPool cryptoClientKeyPool = new CryptoClientKeyPool(cryptoClientKeyConfig, cryptoClientKeyFactory);
                poolList.add(cryptoClientKeyPool);
                this.errIpMap.put(ip, new AtomicInteger(0));
            });
            isOpen.set(true);
        }

        if ("rand".equals(hsmConfig.getStrategy())) {
            this.random = new Random();
        }

        if (poolList.size() > 1) {
            this.checkKeyHash(poolList);
        }

    }

    private void checkKeyHash(List<CryptoClientKeyPool> poolList) throws SvcException {
        String keyHash = "";
        boolean isOK = true;
        Iterator var4 = poolList.iterator();

        while(var4.hasNext()) {
            CryptoClientKeyPool cryptoClientKeyPool = (CryptoClientKeyPool)var4.next();

            try {
                CryptoClient acquire = cryptoClientKeyPool.acquire("hsm");
                byte[] kh = acquire.getKeyHash();
                if (StringUtils.isBlank(keyHash)) {
                    keyHash = Hex.toHexString(kh);
                }

                if (!keyHash.equals(Hex.toHexString(kh))) {
                    isOK = false;
                    throw HsmErrorCode.createSvcException(HsmErrorCode.SDR_CONFIG_ERR);
                }

                cryptoClientKeyPool.release("hsm", acquire);
            } catch (Exception var11) {
                isOK = false;
                if (var11 instanceof SvcException) {
                    throw (SvcException)var11;
                }

                throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var11.getMessage());
            } finally {
                if (!isOK) {
                    this.closePool();
                }

            }
        }

    }

    public void init(String path) throws Exception {
        this.path = path;
        HsmConfig instance = HsmConfig.getInstance(path);
        if (instance.getSsl()) {
            String keyStore = instance.getKeyStorePath();
            String trustStore = instance.getTrustStorePath();
            if (StringUtils.isBlank(keyStore)) {
                throw new RuntimeException("keyStore is null");
            }

            if (StringUtils.isBlank(trustStore)) {
                throw new RuntimeException("trustStore is null");
            }

            Security.removeProvider("BC");
            Security.insertProviderAt(new BouncyCastleProvider(), 1);
            JcaTlsCryptoProvider cryptoProvider = new JcaTlsCryptoProvider();
            cryptoProvider.setProvider(new BouncyCastleProvider());
            cryptoProvider.create(new SecureRandom());
            Security.addProvider(new KlGMJsseProvider(cryptoProvider));
            CryptoClient.params = new MyTSSLTransportFactory.TSSLTransportParameters();
            CryptoClient.params.setKeyStore(keyStore, "12345678");
            CryptoClient.params.setTrustStore(trustStore, "");
        }

        this.openDevice();
    }

    public void init(String ip, int port) throws Exception {
        this.init(ip, port, false, (String)null, (String)null, 16);
    }

    public void init(String ip, int port, int poolMax) throws Exception {
        this.init(ip, port, false, (String)null, (String)null, poolMax);
    }

    public void init(String ip, int port, boolean ssl, String keyStore, String trustStore) throws Exception {
        this.init(ip, port, ssl, keyStore, trustStore, 16);
    }

    public void init(String ip, int port, boolean ssl, String keyStore, String trustStore, int poolMax) throws Exception {
        HsmConfig instance = HsmConfig.getInstance(ip, port, ssl, keyStore, trustStore, poolMax);
        if (instance.getSsl()) {
            if (StringUtils.isBlank(keyStore)) {
                throw new RuntimeException("keyStore is null");
            }

            if (StringUtils.isBlank(trustStore)) {
                throw new RuntimeException("trustStore is null");
            }

            Security.removeProvider("BC");
            Security.insertProviderAt(new BouncyCastleProvider(), 1);
            JcaTlsCryptoProvider cryptoProvider = new JcaTlsCryptoProvider();
            cryptoProvider.setProvider(new BouncyCastleProvider());
            cryptoProvider.create(new SecureRandom());
            Security.addProvider(new KlGMJsseProvider(cryptoProvider));
            CryptoClient.params = new MyTSSLTransportFactory.TSSLTransportParameters();
            CryptoClient.params.setKeyStore(keyStore, "12345678");
            CryptoClient.params.setTrustStore(trustStore, "");
        }

        this.openDevice(ip, port, ssl, keyStore, trustStore, poolMax);
    }

    public int getFaultCount() {
        return this.faultCount;
    }

    public void setFaultCount(int faultCount) {
        this.faultCount = faultCount;
    }

    public int getTimeOut() {
        return this.timeOut;
    }

    public void setTimeOut(int timeOut) {
        this.timeOut = timeOut;
    }

    public int getMaxWaitMillis() {
        return this.maxWaitMillis;
    }

    public void setMaxWaitMillis(int maxWaitMillis) {
        this.maxWaitMillis = maxWaitMillis;
    }

    public synchronized void openDevice() throws SvcException {
        HsmConfig instance = HsmConfig.getInstance(this.path);
        this.poolInit(instance);
    }

    public synchronized void openDevice(String ip, int port, boolean ssl, String keyStore, String trustStore, int poolMax) throws SvcException {
        HsmConfig instance = HsmConfig.getInstance(ip, port, ssl, keyStore, trustStore, poolMax);
        this.poolInit(instance);
    }

    public void closePool() {
        Iterator var1 = poolList.iterator();

        while(var1.hasNext()) {
            CryptoClientKeyPool cryptoClientKeyPool = (CryptoClientKeyPool)var1.next();
            cryptoClientKeyPool.close();
        }

        isOpen.set(false);
        HsmConfig.resetHsmConfig();
        sessionPool.clear();
        poolList.clear();
    }

    public void closeDevice() {
        Iterator var1 = poolList.iterator();

        while(var1.hasNext()) {
            CryptoClientKeyPool cryptoClientKeyPool = (CryptoClientKeyPool)var1.next();
            cryptoClientKeyPool.close();
        }

        isOpen.set(false);
        HsmConfig.resetHsmConfig();
        sessionPool.clear();
        poolList.clear();
    }

    private CryptoClientKeyPool getCryptoClientKeyPool() {
        int index = this.getPoolIndex();
        return (CryptoClientKeyPool)poolList.get(index);
    }

    private CryptoClientKeyPool getCryptoClientKeyPool(int index) {
        return (CryptoClientKeyPool)poolList.get(index);
    }

    public int getPoolIndex() {
        HsmConfig hsmConfig = HsmConfig.getHsmConfig();
        if (hsmConfig.getIps().size() > 1) {
            if ("poll".equals(hsmConfig.getStrategy())) {
                Integer index = (Integer)indexThreadLocal.get();
                if (index == null) {
                    index = new Integer(0);
                }

                index = index + 1;
                if (index >= hsmConfig.getIps().size()) {
                    index = index % hsmConfig.getIps().size();
                }

                indexThreadLocal.set(index);
                return index;
            }

            if ("rand".equals(hsmConfig.getStrategy())) {
                return this.random.nextInt(hsmConfig.getIps().size());
            }
        }

        return 0;
    }

    private CryptoClientKeyPool getCryptoClientKeyPoolBySession(Long sessionHandle) throws SvcException {
        CryptoClientKeyPool cryptoClientKeyPool = (CryptoClientKeyPool)sessionPool.get(sessionHandle);
        if (cryptoClientKeyPool == null) {
            throw HsmErrorCode.createSvcException(HsmErrorCode.SDR_SESSIONNOTEXIST);
        } else {
            return cryptoClientKeyPool;
        }
    }

    private CryptoClient getCryptoClient() throws SvcException {
        SvcException exception = null;

        for(int i = 0; i < retryCount; ++i) {
            String ip = "";

            try {
                CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPool();
                ip = cryptoClientKeyPool.getIp();
                AtomicInteger integer = (AtomicInteger)this.errIpMap.get(ip);
                if (integer.intValue() <= 0) {
                    CryptoClient client = cryptoClientKeyPool.acquire("hsm");
                    poolThreadLocal.set(cryptoClientKeyPool);
                    ((AtomicInteger)this.errIpMap.get(ip)).set(0);
                    return client;
                }

                integer.decrementAndGet();
            } catch (SvcException var7) {
                exception = var7;
                ((AtomicInteger)this.errIpMap.get(ip)).set(this.faultCount);
            }
        }

        throw exception;
    }

    private void releaseCryptoClient(CryptoClient client) {
        ((CryptoClientKeyPool)poolThreadLocal.get()).release("hsm", client);
    }

    public Long openSession() throws TException {
        CryptoClient client = this.getCryptoClient();

        try {
            while(true) {
                long session = client.openSession();
                if (!sessionPool.containsKey(session)) {
                    sessionPool.put(session, poolThreadLocal.get());
                    return session;
                }

                client.closeSession(session);
            }
        } catch (Exception var8) {
            if (var8 instanceof SvcException) {
                throw (SvcException)var8;
            } else {
                throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var8.getMessage());
            }
        } finally {
            this.releaseCryptoClient(client);
        }
    }

    public void closeSession(Long sessionHandle) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        try {
            client.closeSession(sessionHandle);
        } catch (Exception var8) {
            if (var8 instanceof SvcException) {
                throw (SvcException)var8;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var8.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
            sessionPool.remove(sessionHandle);
        }

    }

    public String getDeviceInfo() throws TException {
        CryptoClient client = this.getCryptoClient();

        String version;
        try {
            version = client.getVersion();
        } catch (Exception var7) {
            if (var7 instanceof SvcException) {
                throw (SvcException)var7;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var7.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return version;
    }

    public ByteBuffer getRandom(int len) throws TException {
        CryptoClient client = this.getCryptoClient();

        ByteBuffer var3;
        try {
            var3 = client.generateRandom(len);
        } catch (Exception var7) {
            if (var7 instanceof SvcException) {
                throw (SvcException)var7;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var7.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return var3;
    }

    public void getPrivateKeyAccessRight(long sessionHandle, int keyIndex, ByteBuffer password) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        try {
            client.getPrivateKeyAccessRight(sessionHandle, keyIndex, password);
        } catch (Exception var11) {
            if (var11 instanceof SvcException) {
                throw (SvcException)var11;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var11.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

    }

    public void releasePrivateKeyAccessRight(long sessionHandle, int keyIndex) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        try {
            client.releasePrivateKeyAccessRight(sessionHandle, keyIndex);
        } catch (Exception var10) {
            if (var10 instanceof SvcException) {
                throw (SvcException)var10;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var10.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

    }

    public ByteBuffer asymKeyExpEncPub(int keyID) throws TException {
        CryptoClient client = this.getCryptoClient();

        ByteBuffer var3;
        try {
            var3 = client.asymKeyExpEncPub(keyID);
        } catch (Exception var7) {
            if (var7 instanceof SvcException) {
                throw (SvcException)var7;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var7.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return var3;
    }

    public ByteBuffer asymKeyExpSignPub(int keyID) throws TException {
        CryptoClient client = this.getCryptoClient();

        ByteBuffer var3;
        try {
            var3 = client.asymKeyExpSignPub(keyID);
        } catch (Exception var7) {
            if (var7 instanceof SvcException) {
                throw (SvcException)var7;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var7.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return var3;
    }

    public AsymKeypair asymGenKeyExp(Algo algo) throws TException {
        CryptoClient client = this.getCryptoClient();

        AsymKeypair var3;
        try {
            var3 = client.asymGenKeyExp(algo);
        } catch (Exception var7) {
            if (var7 instanceof SvcException) {
                throw (SvcException)var7;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var7.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return var3;
    }

    public IntBinary exportGenSKeyInA(long sessionHandle, int index, int keybits) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        IntBinary var7;
        try {
            var7 = client.exportGenSKeyInA(sessionHandle, index, keybits);
        } catch (Exception var11) {
            if (var11 instanceof SvcException) {
                throw (SvcException)var11;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var11.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var7;
    }

    public IntBinary exportGenSKeyOutA(long sessionHandle, int algID, ByteBuffer pubKey, int keybits) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        IntBinary var8;
        try {
            var8 = client.exportGenSKeyOutA(sessionHandle, algID, pubKey, keybits);
        } catch (Exception var12) {
            if (var12 instanceof SvcException) {
                throw (SvcException)var12;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var12.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var8;
    }

    public int importSKeyInA(long sessionHandle, int index, ByteBuffer data) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        int var7;
        try {
            var7 = client.importSKeyInA(sessionHandle, index, data);
        } catch (Exception var11) {
            if (var11 instanceof SvcException) {
                throw (SvcException)var11;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var11.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var7;
    }

    public ByteBuffer exchangeEnvelope(long sessionHandle, int index, int algID, ByteBuffer pubKey, ByteBuffer data) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        ByteBuffer var9;
        try {
            var9 = client.exchangeEnvelope(sessionHandle, index, algID, pubKey, data);
        } catch (Exception var13) {
            if (var13 instanceof SvcException) {
                throw (SvcException)var13;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var13.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var9;
    }

    public keyAgreementResp initiateAgreementKey(long sessionHandle, int privKeyIndex, int keyBits, ByteBuffer spID, Algo algo) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        keyAgreementResp var9;
        try {
            var9 = client.initiateAgreementKey(sessionHandle, privKeyIndex, keyBits, spID, algo);
        } catch (Exception var13) {
            if (var13 instanceof SvcException) {
                throw (SvcException)var13;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var13.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var9;
    }

    public Long generateAgreementKey(long sessionHandle, ByteBuffer respID, ByteBuffer spPubKey, ByteBuffer spTmpPubKey, int handle, Algo algo) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        Long var10;
        try {
            var10 = client.generateAgreementKey(sessionHandle, respID, spPubKey, spTmpPubKey, handle, algo);
        } catch (Exception var14) {
            if (var14 instanceof SvcException) {
                throw (SvcException)var14;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var14.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var10;
    }

    public keyAgreementResp respondAgreementKey(long sessionHandle, int privKeyIndex, int keyBits, ByteBuffer respID, ByteBuffer spID, ByteBuffer spPubKey, ByteBuffer spTmpPubKey, Algo algo) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        keyAgreementResp var12;
        try {
            var12 = client.respondAgreementKey(sessionHandle, privKeyIndex, keyBits, respID, spID, spPubKey, spTmpPubKey, algo);
        } catch (Exception var16) {
            if (var16 instanceof SvcException) {
                throw (SvcException)var16;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var16.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var12;
    }

    public IntBinary exportGenSKeyInS(long sessionHandle, int algID, int kekIndex, int keybits) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        IntBinary var8;
        try {
            var8 = client.exportGenSKeyInS(sessionHandle, algID, kekIndex, keybits);
        } catch (Exception var12) {
            if (var12 instanceof SvcException) {
                throw (SvcException)var12;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var12.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var8;
    }

    public int importSKeyInS(long sessionHandle, int algID, int kekIndex, ByteBuffer data) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        int var8;
        try {
            var8 = client.importSKeyInS(sessionHandle, algID, kekIndex, data);
        } catch (Exception var12) {
            if (var12 instanceof SvcException) {
                throw (SvcException)var12;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var12.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var8;
    }

    public int importSKey(long sessionHandle, ByteBuffer data) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        int var6;
        try {
            var6 = client.importSKey(sessionHandle, data);
        } catch (Exception var10) {
            if (var10 instanceof SvcException) {
                throw (SvcException)var10;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var10.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var6;
    }

    public void destorySKey(long sessionHandle, int sKeyIndex) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        try {
            client.destorySKey(sessionHandle, sKeyIndex);
        } catch (Exception var10) {
            if (var10 instanceof SvcException) {
                throw (SvcException)var10;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var10.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

    }

    public ByteBuffer asymEnc(Algo algo, ByteBuffer pubKey, ByteBuffer data) throws Exception {
        CryptoClient client = this.getCryptoClient();

        ByteBuffer var5;
        try {
            var5 = client.asymEnc(algo, pubKey, data);
        } catch (Exception var9) {
            if (var9 instanceof SvcException) {
                throw (SvcException)var9;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var9.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return var5;
    }

    public ByteBuffer asymDec(Algo algo, ByteBuffer priKey, ByteBuffer data) throws Exception {
        CryptoClient client = this.getCryptoClient();

        ByteBuffer var5;
        try {
            var5 = client.asymDec(algo, priKey, data);
        } catch (Exception var9) {
            if (var9 instanceof SvcException) {
                throw (SvcException)var9;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var9.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return var5;
    }

    public ByteBuffer asymEncIn(int keyID, ByteBuffer data) throws TException {
        CryptoClient client = this.getCryptoClient();

        ByteBuffer var4;
        try {
            var4 = client.asymEncIn(keyID, data);
        } catch (Exception var8) {
            if (var8 instanceof SvcException) {
                throw (SvcException)var8;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var8.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return var4;
    }

    public ByteBuffer asymDecIn(long sessionHandle, int keyID, ByteBuffer data) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        ByteBuffer var7;
        try {
            var7 = client.asymDecIn(sessionHandle, keyID, data);
        } catch (Exception var11) {
            if (var11 instanceof SvcException) {
                throw (SvcException)var11;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var11.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var7;
    }

    /** @deprecated */
    @Deprecated
    public ByteBuffer sign(Algo algo, ByteBuffer priKey, ByteBuffer data) throws Exception {
        if ("SM2".equals(algo.name())) {
            PrivateKey key = SdkUtil.getPrivateKey(priKey.array());
            return SdkUtil.signSm3WithSm2(data, key);
        } else {
            throw new Exception("Incorrect algorithm");
        }
    }

    /** @deprecated */
    @Deprecated
    public Boolean verify(Algo algo, ByteBuffer pubKey, ByteBuffer signData, ByteBuffer data) throws Exception {
        if ("SM2".equals(algo.name())) {
            PublicKey key = SdkUtil.getPublicKey(pubKey.array());
            return SdkUtil.verifySm3WithSm2(data, signData, key);
        } else {
            throw new Exception("Incorrect algorithm");
        }
    }

    public ByteBuffer signEx(Algo algo, ByteBuffer privKey, ByteBuffer digest, HashAlgoParam hashParam) throws TException {
        CryptoClient client = this.getCryptoClient();

        ByteBuffer var6;
        try {
            var6 = client.sign(algo, privKey, digest, hashParam);
        } catch (Exception var10) {
            if (var10 instanceof SvcException) {
                throw (SvcException)var10;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var10.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return var6;
    }

    public Boolean verifyEx(Algo algo, ByteBuffer pubKey, ByteBuffer digest, HashAlgoParam hashParam, ByteBuffer signData) throws TException {
        CryptoClient client = this.getCryptoClient();

        Boolean var7;
        try {
            var7 = client.verify(algo, pubKey, digest, hashParam, signData);
        } catch (Exception var11) {
            if (var11 instanceof SvcException) {
                throw (SvcException)var11;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var11.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return var7;
    }

    public ByteBuffer signIn(long sessionHandle, int keyID, ByteBuffer digest, HashAlgoParam hashParam) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        ByteBuffer var8;
        try {
            var8 = client.signIn(sessionHandle, keyID, digest, hashParam);
        } catch (Exception var12) {
            if (var12 instanceof SvcException) {
                throw (SvcException)var12;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var12.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var8;
    }

    public Boolean verifyIn(int keyID, ByteBuffer digest, HashAlgoParam hashParam, ByteBuffer signData) throws TException {
        CryptoClient client = this.getCryptoClient();

        Boolean var6;
        try {
            var6 = client.verifyIn(keyID, digest, hashParam, signData);
        } catch (Exception var10) {
            if (var10 instanceof SvcException) {
                throw (SvcException)var10;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var10.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return var6;
    }

    public ByteBuffer encSKey(long sessionHandle, Algo algo, int sKeyIndex, SymParam param, ByteBuffer data) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        ByteBuffer var9;
        try {
            if (null == param) {
                throw new TException("SymParam is null!");
            }

            if (param.getPadding() == null) {
                param.setPadding(Padding.NoPadding);
            }

            var9 = client.encSKey(sessionHandle, algo, sKeyIndex, param, data);
        } catch (Exception var13) {
            if (var13 instanceof SvcException) {
                throw (SvcException)var13;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var13.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var9;
    }

    public ByteBuffer decSKey(long sessionHandle, Algo algo, int sKeyIndex, SymParam param, ByteBuffer data) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        ByteBuffer var9;
        try {
            if (null == param) {
                throw new TException("SymParam is null!");
            }

            if (param.getPadding() == null) {
                param.setPadding(Padding.NoPadding);
            }

            var9 = client.decSKey(sessionHandle, algo, sKeyIndex, param, data);
        } catch (Exception var13) {
            if (var13 instanceof SvcException) {
                throw (SvcException)var13;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var13.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var9;
    }

    public ByteBuffer mac(long sessionHandle, int sKeyHandle, Algo algo, ByteBuffer data, ByteBuffer iv) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        ByteBuffer var9;
        try {
            var9 = client.mac(sessionHandle, sKeyHandle, algo, data, iv);
        } catch (Exception var13) {
            if (var13 instanceof SvcException) {
                throw (SvcException)var13;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var13.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var9;
    }

    public ByteBuffer hash(ByteBuffer data, Algo algo) throws Exception {
        Object digest;
        if ("MD5".equalsIgnoreCase(algo.name())) {
            digest = new MD5Digest();
        } else if ("SHA1".equalsIgnoreCase(algo.name())) {
            digest = new SHA1Digest();
        } else if ("SHA2_256".equalsIgnoreCase(algo.name())) {
            digest = new SHA256Digest();
        } else if ("SHA2_384".equalsIgnoreCase(algo.name())) {
            digest = new SHA384Digest();
        } else if ("SHA2_512".equalsIgnoreCase(algo.name())) {
            digest = new SHA512Digest();
        } else {
            if (!"SM3".equalsIgnoreCase(algo.name())) {
                throw new Exception("Incorrect algorithm");
            }

            digest = new SM3Digest();
        }

        ((Digest)digest).update(data.array(), 0, data.array().length);
        byte[] digestData = new byte[((Digest)digest).getDigestSize()];
        ((Digest)digest).doFinal(digestData, 0);
        return ByteBuffer.wrap(digestData);
    }

    public Digest getDigest(Algo algo) throws Exception {
        Object digest;
        if ("MD5".equalsIgnoreCase(algo.name())) {
            digest = new MD5Digest();
        } else if ("SHA1".equalsIgnoreCase(algo.name())) {
            digest = new SHA1Digest();
        } else if ("SHA2_256".equalsIgnoreCase(algo.name())) {
            digest = new SHA256Digest();
        } else if ("SHA2_384".equalsIgnoreCase(algo.name())) {
            digest = new SHA384Digest();
        } else if ("SHA2_512".equalsIgnoreCase(algo.name())) {
            digest = new SHA512Digest();
        } else {
            if (!"SM3".equalsIgnoreCase(algo.name())) {
                throw new Exception("Incorrect algorithm");
            }

            digest = new SM3Digest();
        }

        return (Digest)digest;
    }

    public byte[] hashIn(byte[] data, HashAlgoParam param) throws TException {
        CryptoClient client = this.getCryptoClient();

        byte[] var4;
        try {
            var4 = client.hashIn(data, param);
        } catch (Exception var8) {
            if (var8 instanceof SvcException) {
                throw (SvcException)var8;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var8.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return var4;
    }

    public ByteBuffer getZSM3Hash(ByteBuffer data, ByteBuffer pubKey) {
        byte[] y = ArrayUtils.subarray(pubKey.array(), pubKey.array().length - 32, pubKey.array().length);
        byte[] x = ArrayUtils.subarray(pubKey.array(), pubKey.array().length - 64, pubKey.array().length - 32);
        byte[] z = SdkUtil.getZ(x, y);
        SM3Digest sm3 = new SM3Digest();
        sm3.update(z, 0, z.length);
        sm3.update(data.array(), 0, data.array().length);
        byte[] md = new byte[sm3.getDigestSize()];
        sm3.doFinal(md, 0);
        return ByteBuffer.wrap(md);
    }

    public void createFile(String fileName, int fileSize) throws TException {
        CryptoClient client = this.getCryptoClient();

        try {
            client.createFile(fileName, fileSize);
        } catch (Exception var8) {
            if (var8 instanceof SvcException) {
                throw (SvcException)var8;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var8.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

    }

    public IntBinary readFile(String fileName, int offSet, int readLength) throws TException {
        CryptoClient client = this.getCryptoClient();

        IntBinary var5;
        try {
            var5 = client.readFile(fileName, offSet, readLength);
        } catch (Exception var9) {
            if (var9 instanceof SvcException) {
                throw (SvcException)var9;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var9.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return var5;
    }

    public void writeFile(String fileName, int offSet, ByteBuffer data, int writeLength) throws TException {
        CryptoClient client = this.getCryptoClient();

        try {
            client.writeFile(fileName, offSet, data, writeLength);
        } catch (Exception var10) {
            if (var10 instanceof SvcException) {
                throw (SvcException)var10;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var10.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

    }

    public void deleteFile(String fileName) throws TException {
        CryptoClient client = this.getCryptoClient();

        try {
            client.deleteFile(fileName);
        } catch (Exception var7) {
            if (var7 instanceof SvcException) {
                throw (SvcException)var7;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var7.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

    }

    public byte[] sm2Sign(long sessionHandle, int keyID, byte[] digest) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        byte[] var7;
        try {
            var7 = client.signIn(sessionHandle, keyID, ByteBuffer.wrap(digest), Constonts.HASH_ALGO).array();
        } catch (Exception var11) {
            if (var11 instanceof SvcException) {
                throw (SvcException)var11;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var11.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var7;
    }

    public Boolean sm2Verify(ByteBuffer pubKey, ByteBuffer digest, ByteBuffer signData) throws TException {
        CryptoClient client = this.getCryptoClient();

        Boolean var5;
        try {
            var5 = client.verify(Algo.SM2, pubKey, digest, Constonts.HASH_ALGO, signData);
        } catch (Exception var9) {
            if (var9 instanceof SvcException) {
                throw (SvcException)var9;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var9.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return var5;
    }

    public String exportPubKey(int keyID, boolean withEncKey) throws Exception {
        ByteBuffer pubKey;
        if (withEncKey) {
            pubKey = this.asymKeyExpEncPub(keyID);
        } else {
            pubKey = this.asymKeyExpSignPub(keyID);
        }

        BCECPublicKey publicKey = (BCECPublicKey)SdkUtil.getPublicKey(pubKey.array());
        byte[] x = publicKey.getQ().getAffineXCoord().getEncoded();
        byte[] y = publicKey.getQ().getAffineYCoord().getEncoded();
        byte[] bytes = SdkUtil.addAll(new byte[][]{{4}, x, y});
        return Base64.getEncoder().encodeToString(bytes);
    }

    public void importKeyPair(long sessionHandle, int keyID, String envelopedBlob) throws TException, IOException {
        EnvelopedKeyPair envelopedKeyPair = SdkUtil.getEnvelopedKeyPair(envelopedBlob);
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        try {
            client.importKeyPair(sessionHandle, keyID, true, envelopedKeyPair);
        } catch (Exception var12) {
            if (var12 instanceof SvcException) {
                throw (SvcException)var12;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var12.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

    }

    public void generateCsrToFile(int index, long sessionId, String cn, String o, String ou, String l, String s, String c) throws TException, IOException {
        byte[] data = this.getCsrData(index, sessionId, cn, o, ou, l, s, c);
        if (data.length > 0) {
            String filePath = Paths.get(System.getProperty("user.dir")).toString();
            this.createCsrFile(filePath, "koal.p10", data);
        } else {
            throw new TException("密码机服务端返回CSR数据异常");
        }
    }

    private byte[] getCsrData(int index, long sessionHandle, String cn, String o, String ou, String l, String s, String c) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        byte[] var13;
        try {
            ByteBuffer byteBuffer = client.getCsr(c, s, l, o, ou, cn, index, sessionHandle);
            if (null != byteBuffer) {
                var13 = byteBuffer.array();
                return var13;
            }

            var13 = new byte[0];
        } catch (Exception var17) {
            if (var17 instanceof SvcException) {
                throw (SvcException)var17;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var17.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var13;
    }

    private void createCsrFile(String path, String name, byte[] csr) throws IOException {
        PemObject pemCSR = new PemObject("CERTIFICATE REQUEST", csr);
        StringWriter str = new StringWriter();
        Throwable var6 = null;

        try {
            JcaPEMWriter pemWriter = new JcaPEMWriter(str);
            Throwable var8 = null;

            try {
                RandomAccessFile raf = new RandomAccessFile(path + File.separator + name, "rw");
                Throwable var10 = null;

                try {
                    pemWriter.writeObject(pemCSR);
                    pemWriter.flush();
                    raf.write(str.toString().getBytes());
                } catch (Throwable var54) {
                    var10 = var54;
                    throw var54;
                } finally {
                    if (raf != null) {
                        if (var10 != null) {
                            try {
                                raf.close();
                            } catch (Throwable var53) {
                                var10.addSuppressed(var53);
                            }
                        } else {
                            raf.close();
                        }
                    }

                }
            } catch (Throwable var56) {
                var8 = var56;
                throw var56;
            } finally {
                if (pemWriter != null) {
                    if (var8 != null) {
                        try {
                            pemWriter.close();
                        } catch (Throwable var52) {
                            var8.addSuppressed(var52);
                        }
                    } else {
                        pemWriter.close();
                    }
                }

            }
        } catch (Throwable var58) {
            var6 = var58;
            throw var58;
        } finally {
            if (str != null) {
                if (var6 != null) {
                    try {
                        str.close();
                    } catch (Throwable var51) {
                        var6.addSuppressed(var51);
                    }
                } else {
                    str.close();
                }
            }

        }

    }

    public void getCsr(int index, Long sessionId, String cn, String ouArea) throws IOException, TException {
        byte[] data = this.getCsrData(index, sessionId, "CN", "国家烟草专卖局", "国家烟草专卖局", "北京市", "北京市", "CN");
        if (data.length > 0) {
            String filePath = Paths.get(System.getProperty("user.dir")).toString();
            this.createCsrFile(filePath, "koal.p10", data);
        } else {
            throw new TException("密码机服务端返回CSR数据异常");
        }
    }

    public ByteBuffer sm9Sign(ByteBuffer data, ByteBuffer privatekey, ByteBuffer masterPublicKey) throws TException {
        CryptoClient client = this.getCryptoClient();

        ByteBuffer var5;
        try {
            var5 = client.sm9Sign(data, privatekey, masterPublicKey);
        } catch (Exception var9) {
            if (var9 instanceof SvcException) {
                throw (SvcException)var9;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var9.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return var5;
    }

    public boolean sm9Verify(ByteBuffer data, ByteBuffer id, ByteBuffer signature, ByteBuffer masterPublicKey) throws TException {
        CryptoClient client = this.getCryptoClient();

        boolean var6;
        try {
            var6 = client.sm9Verify(data, id, signature, masterPublicKey);
        } catch (Exception var10) {
            if (var10 instanceof SvcException) {
                throw (SvcException)var10;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var10.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return var6;
    }

    public ByteBuffer sm9Encrypt(ByteBuffer data, ByteBuffer id, ByteBuffer masterPublicKey, int type, Algo algo) throws TException {
        CryptoClient client = this.getCryptoClient();

        ByteBuffer var7;
        try {
            var7 = client.sm9Encrypt(data, id, masterPublicKey, type, Algo.SM4);
        } catch (Exception var11) {
            if (var11 instanceof SvcException) {
                throw (SvcException)var11;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var11.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return var7;
    }

    public ByteBuffer sm9Decrypt(ByteBuffer data, ByteBuffer id, ByteBuffer privatekey, int type, Algo algo) throws TException {
        CryptoClient client = this.getCryptoClient();

        ByteBuffer var7;
        try {
            var7 = client.sm9Decrypt(data, id, privatekey, type, Algo.SM4);
        } catch (Exception var11) {
            if (var11 instanceof SvcException) {
                throw (SvcException)var11;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var11.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return var7;
    }

    public ByteBuffer sm9ExportMasterPublicKey(int uiSM9Index, int type) throws TException {
        CryptoClient client = this.getCryptoClient();

        ByteBuffer var4;
        try {
            var4 = client.sm9ExportMasterPublicKey(uiSM9Index, type);
        } catch (Exception var8) {
            if (var8 instanceof SvcException) {
                throw (SvcException)var8;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var8.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return var4;
    }

    public ByteBuffer sm9GenerateUserPrivKey(int sm9Index, ByteBuffer userID, int type) throws TException {
        CryptoClient client = this.getCryptoClient();

        ByteBuffer var5;
        try {
            var5 = client.sm9GenerateUserPrivKey(sm9Index, userID, type);
        } catch (Exception var9) {
            if (var9 instanceof SvcException) {
                throw (SvcException)var9;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var9.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return var5;
    }

    public SM9Encapsulate sm9Encap(ByteBuffer id, ByteBuffer masterPublicKey, int keyLen) throws TException {
        CryptoClient client = this.getCryptoClient();

        SM9Encapsulate var5;
        try {
            var5 = client.sm9Encap(id, masterPublicKey, keyLen);
        } catch (Exception var9) {
            if (var9 instanceof SvcException) {
                throw (SvcException)var9;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var9.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return var5;
    }

    public ByteBuffer sm9Decap(ByteBuffer id, ByteBuffer privatekey, ByteBuffer cipher, int keyLen) throws TException {
        CryptoClient client = this.getCryptoClient();

        ByteBuffer var6;
        try {
            var6 = client.sm9Decap(id, privatekey, cipher, keyLen);
        } catch (Exception var10) {
            if (var10 instanceof SvcException) {
                throw (SvcException)var10;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var10.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return var6;
    }

    public IntBinary sm9GenerateAgreementData(long sessionHandle, ByteBuffer responseID, ByteBuffer masterPublicKey) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        IntBinary var7;
        try {
            var7 = client.sm9GenerateAgreementData(sessionHandle, responseID, masterPublicKey);
        } catch (Exception var11) {
            if (var11 instanceof SvcException) {
                throw (SvcException)var11;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var11.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var7;
    }

    public SM9KeyExchange sm9GenerateAgreemetDataAndKey(long sessionHandle, int keyLen, ByteBuffer responseID, ByteBuffer sponseID, ByteBuffer privateKey, ByteBuffer masterPublicKey, ByteBuffer sponsorTmpPublicKey) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        SM9KeyExchange var11;
        try {
            var11 = client.sm9GenerateAgreemetDataAndKey(sessionHandle, keyLen, responseID, sponseID, privateKey, masterPublicKey, sponsorTmpPublicKey);
        } catch (Exception var15) {
            if (var15 instanceof SvcException) {
                throw (SvcException)var15;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var15.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var11;
    }

    public SM9KeyExchange sm9GenerateKey(long sessionHandle, int keyLen, ByteBuffer sponseID, ByteBuffer responseID, ByteBuffer privateKey, ByteBuffer masterPublicKey, ByteBuffer responseTmpPublicKey, int agreementHandle, ByteBuffer hashSA, ByteBuffer hashSB) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        SM9KeyExchange var14;
        try {
            var14 = client.sm9GenerateKey(sessionHandle, keyLen, sponseID, responseID, privateKey, masterPublicKey, responseTmpPublicKey, agreementHandle, hashSA, hashSB);
        } catch (Exception var18) {
            if (var18 instanceof SvcException) {
                throw (SvcException)var18;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var18.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var14;
    }

    public boolean sm9GenerateKeyVerify(long sessionHandle, ByteBuffer hashSA, ByteBuffer hashSB, int sKeyHandle) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        boolean var8;
        try {
            var8 = client.sm9GenerateKeyVerify(sessionHandle, hashSA, hashSB, sKeyHandle);
        } catch (Exception var12) {
            if (var12 instanceof SvcException) {
                throw (SvcException)var12;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var12.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var8;
    }

    public byte[] macByMode(long sessionHandle, int sKeyHandle, Algo algo, byte[] data, SymParam param) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        byte[] var9;
        try {
            var9 = client.macByMode(sessionHandle, sKeyHandle, algo, ByteBuffer.wrap(data), param).array();
        } catch (Exception var13) {
            if (var13 instanceof SvcException) {
                throw (SvcException)var13;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var13.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var9;
    }

    public byte[] macByModeEx(byte[] key, Algo algo, byte[] data, SymParam param) throws TException {
        CryptoClient client = this.getCryptoClient();

        byte[] var6;
        try {
            var6 = client.macByModeEx(ByteBuffer.wrap(key), algo, ByteBuffer.wrap(data), param).array();
        } catch (Exception var10) {
            if (var10 instanceof SvcException) {
                throw (SvcException)var10;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var10.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return var6;
    }

    public byte[] macByModeIn(int keyIndex, Algo algo, byte[] data, SymParam param) throws TException {
        CryptoClient client = this.getCryptoClient();

        byte[] var6;
        try {
            if (param == null) {
                param = new SymParam();
            }

            var6 = client.macByModeIn(keyIndex, algo, ByteBuffer.wrap(data), param).array();
        } catch (Exception var10) {
            if (var10 instanceof SvcException) {
                throw (SvcException)var10;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var10.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return var6;
    }

    public byte[] zuc_eia(byte[] inbuf, byte[] key, int count, int bearer, int direction) throws TException {
        CryptoClient client = this.getCryptoClient();

        byte[] var7;
        try {
            var7 = client.zuc_eia(ByteBuffer.wrap(inbuf), ByteBuffer.wrap(key), count, bearer, direction).array();
        } catch (Exception var11) {
            if (var11 instanceof SvcException) {
                throw (SvcException)var11;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var11.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return var7;
    }

    public byte[] zuc_eea(byte[] inbuf, byte[] key, int count, int bearer, int direction) throws TException {
        CryptoClient client = this.getCryptoClient();

        byte[] var7;
        try {
            var7 = client.zuc_eea(ByteBuffer.wrap(inbuf), ByteBuffer.wrap(key), count, bearer, direction).array();
        } catch (Exception var11) {
            if (var11 instanceof SvcException) {
                throw (SvcException)var11;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var11.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return var7;
    }

    public byte[] dec(Algo algo, byte[] key, SymParam param, byte[] data) throws TException {
        CryptoClient client = this.getCryptoClient();

        byte[] var6;
        try {
            if (null == param) {
                throw new TException("SymParam is null!");
            }

            var6 = client.dec(algo, ByteBuffer.wrap(key), param, ByteBuffer.wrap(data)).array();
        } catch (Exception var10) {
            if (var10 instanceof SvcException) {
                throw (SvcException)var10;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var10.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return var6;
    }

    public byte[] enc(Algo algo, byte[] key, SymParam param, byte[] data) throws TException {
        CryptoClient client = this.getCryptoClient();

        byte[] var6;
        try {
            if (null == param) {
                throw new TException("SymParam is null!");
            }

            var6 = client.enc(algo, ByteBuffer.wrap(key), param, ByteBuffer.wrap(data)).array();
        } catch (Exception var10) {
            if (var10 instanceof SvcException) {
                throw (SvcException)var10;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var10.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return var6;
    }

    public byte[] decIn(int index, SymParam param, byte[] data) throws TException {
        CryptoClient client = this.getCryptoClient();

        byte[] var5;
        try {
            if (null == param) {
                throw new TException("SymParam is null!");
            }

            var5 = client.decIn(index, param, ByteBuffer.wrap(data)).array();
        } catch (Exception var9) {
            if (var9 instanceof SvcException) {
                throw (SvcException)var9;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var9.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return var5;
    }

    public byte[] encIn(int index, SymParam param, byte[] data) throws TException {
        CryptoClient client = this.getCryptoClient();

        byte[] var5;
        try {
            if (null == param) {
                throw new TException("SymParam is null!");
            }

            var5 = client.encIn(index, param, ByteBuffer.wrap(data)).array();
        } catch (Exception var9) {
            if (var9 instanceof SvcException) {
                throw (SvcException)var9;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var9.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return var5;
    }

    public byte[] rsaPubOpIn(int keyID, byte[] data) throws TException {
        CryptoClient client = this.getCryptoClient();

        byte[] var5;
        try {
            boolean withEncKey = keyID >= 0;
            var5 = client.asymPubOpIn(keyID, data, withEncKey);
        } catch (Exception var9) {
            if (var9 instanceof SvcException) {
                throw (SvcException)var9;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var9.getMessage());
        } finally {
            this.releaseCryptoClient(client);
        }

        return var5;
    }

    public byte[] rsaPrivOpIn(long sessionHandle, int keyID, byte[] data) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        byte[] var8;
        try {
            boolean withEncKey = keyID >= 0;
            var8 = client.asymPrivOpIn(sessionHandle, keyID, data, withEncKey);
        } catch (Exception var12) {
            if (var12 instanceof SvcException) {
                throw (SvcException)var12;
            }

            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var12.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var8;
    }

    public GenKeyResult1 genCooperateKey1(long sessionHandle) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        GenKeyResult1 var5;
        try {
            var5 = client.genCooperateKey1();
        } catch (Exception var9) {
            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var9.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var5;
    }

    public GenKeyResult2 genCooperateKey2(long sessionHandle, ECPoint point) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        GenKeyResult2 var6;
        try {
            var6 = client.genCooperateKey2(point);
        } catch (Exception var10) {
            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var10.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var6;
    }

    public SignResult1 cooperateSign1(long sessionHandle, byte[] mid, byte[] M, ECPoint P) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        SignResult1 var8;
        try {
            var8 = client.cooperateSign1(mid, M, P);
        } catch (Exception var12) {
            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var12.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var8;
    }

    public SignResult2 cooperateSign2(long sessionHandle, byte[] e, ECPoint Q1, BigInteger D2) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        SignResult2 var8;
        try {
            var8 = client.cooperateSign2(e, Q1, D2);
        } catch (Exception var12) {
            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var12.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var8;
    }

    public SignResult3 cooperateSign3(long sessionHandle, BigInteger r, BigInteger s2, BigInteger s3, BigInteger D1, BigInteger k1) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        SignResult3 var10;
        try {
            var10 = client.cooperateSign3(r, s2, s3, D1, k1);
        } catch (Exception var14) {
            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var14.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var10;
    }

    public DecResult1 cooperatePriDec1(long sessionHandle, byte[] C, BigInteger D1) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        DecResult1 var7;
        try {
            var7 = client.cooperatePriDec1(C, D1);
        } catch (Exception var11) {
            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var11.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var7;
    }

    public DecResult2 cooperatePriDec2(long sessionHandle, ECPoint T1, BigInteger D2) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        DecResult2 var7;
        try {
            var7 = client.cooperatePriDec2(T1, D2);
        } catch (Exception var11) {
            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var11.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var7;
    }

    public DecResult3 cooperatePriDec3(long sessionHandle, ECPoint T2, ECPoint C1, byte[] C2, byte[] C3) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        DecResult3 var9;
        try {
            var9 = client.cooperatePriDec3(T2, C1, C2, C3);
        } catch (Exception var13) {
            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var13.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var9;
    }

    public String envelopedData(long sessionHandle, byte[] data, String cert, byte[] rawKey) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        String var8;
        try {
            var8 = client.envelopedData(data, cert, rawKey);
        } catch (Exception var12) {
            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var12.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var8;
    }

    public String envelopedDataParse(long sessionHandle, String envelopedDataStr, String cert, String pri) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        String var8;
        try {
            var8 = client.envelopedDataParse(envelopedDataStr, cert, pri);
        } catch (Exception var12) {
            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var12.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var8;
    }

    public ByteBuffer envelopeEncryption(long sessionHandle, String cert, ByteBuffer data) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        ByteBuffer var7;
        try {
            var7 = client.envelopeEncryption(cert, data);
        } catch (Exception var11) {
            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var11.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var7;
    }

    public ByteBuffer envelopeEncryption(long sessionHandle, String cert, String pri, ByteBuffer data) throws TException {
        CryptoClientKeyPool cryptoClientKeyPool = this.getCryptoClientKeyPoolBySession(sessionHandle);
        CryptoClient client = cryptoClientKeyPool.acquire("hsm");

        ByteBuffer var8;
        try {
            var8 = client.envelopeEncryption(cert, pri, data);
        } catch (Exception var12) {
            throw HsmErrorCode.createSvcException(HsmErrorCode.JNI_RETURN_NULL, var12.getMessage());
        } finally {
            cryptoClientKeyPool.release("hsm", client);
        }

        return var8;
    }
}
