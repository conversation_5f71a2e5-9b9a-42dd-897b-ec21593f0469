<?php
declare (strict_types=1);

namespace app\city\controller;

use app\model\ExcelModel;
use app\model\FileModel;
use think\facade\Db;
use think\facade\View;
use think\Request;
use app\city\model\CompanyModel;

class Company extends Base
{
    /**
     * @Apidoc\Title("企业管理列表")
     * @Apidoc\Desc("企业管理列表")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function index($limit = 20, $excel = 0)
    {
        if (request()->isAjax()) {
            $title = $this->request->param('title', '', 'trim');
            $area = $this->request->param('area', '', 'trim');
            $enterprise_size = $this->request->param('enterprise_size', '', 'trim');
            $industry = $this->request->param('industry', '', 'trim');
            $mb_operate_address = $this->request->param('mb_operate_address', '', 'trim');
            $mb_status = $this->request->param('mb_status', '', 'trim');
            $ca_status = $this->request->param('ca_status', '', 'trim');
            $where = [
                ['city_id', '=', $_SESSION['city']['id']]
            ];
            if ($_SESSION['city']['dept_id'] != 1) {
                $where[] = ['dept_id', '=', $_SESSION['city']['dept_id']];
            }
            if (!empty($title)) {
                $where[] = ['name|legal', 'like', "%{$title}%"];
            }
            if (!empty($area)) {
                $where[] = ['area_id', '=', $area];
            }
            if (!empty($enterprise_size)) {
                $where[] = ['enterprise_size', 'like', "%{$enterprise_size}%"];
            }
            if (!empty($industry)) {
                $where[] = ['industry', 'like', "%{$industry}%"];
            }
            if (!empty($mb_operate_address)) {
                $where[] = ['operate_address_info', 'like', "%{$mb_operate_address}%"];
            }

            //企业状态
            if (!empty($mb_status)) {
                switch ($mb_status) {
                    case 'info':
                        $where[] = ['stand_status', '=', 0];
                        break;
                    case 'primary':
                        $where[] = ['stand_status', '=', 1];
                        $where[] = ['ca_status', '=', 0];
                        break;
                    case 'success':
                        $where[] = ['stand_status', '=', 1];
                        $where[] = ['ca_status', '=', 1];
                        break;
                }
            }

            //证书状态
            if (!empty($ca_status)) {
                switch ($ca_status) {
                    case 'warning':
                        if (!$mb_status == 'primary') {
                            $where[] = ['ca_status', '=', 0];
                        }
                        break;
                    case 'success':
                        $where[] = ['ca_status', '=', 1];
                        break;
                    case 'danger':
                        $where[] = ['ca_status', '=', 2];
                }
            }
            $res = Db::table('top_company_info')->where($where)->order('id desc');
            if ($excel == 1) {
                $data = [];
                $res = $res->select()->toArray();
                foreach ($res as $k => $item) {
                    $item = CompanyModel::codeToText($item);
                    $item['mb_status_text'] = CompanyModel::getStatusText($item['stand_status'], $item['ca_status']);
                    $item['ca_status_text'] = CompanyModel::getCaStatusText($item['ca_status']);
                    $data[$k] = $item;
                }
                $title = [
                    ['title' => '所属区县', 'field' => 'mb_region', 'width' => '20',],
                    ['title' => '企业名称', 'field' => 'name', 'width' => '30', 'type' => 'string'],
                    ['title' => '生产经营地址', 'field' => 'mb_operate_address', 'width' => '30', 'type' => 'string'],
                    ['title' => '企业状态', 'field' => 'mb_status_text', 'type' => 'string'],
                    ['title' => '是否取得证书', 'field' => 'ca_status_text', 'type' => 'string'],
                    ['title' => '行业/专业', 'field' => 'industry', 'type' => 'string'],
                    ['title' => '企业规模', 'field' => 'enterprise_size', 'type' => 'string'],
                ];
                ExcelModel::exportExcel($title, $data, '企业信息导出', true);
            }
            $res = $res->paginate($limit)->each(function ($item, $key) {
                $item = CompanyModel::codeToText($item);
                return $item;
            });
            result($res);
        } else {
            $area = Db::table('top_area')->alias('a')->leftJoin('top_pca b', 'a.pcas = b.code')->field('a.id,b.name')->select()->toArray();
            $industry = Db::table('top_industry')->where('pid','=',0)->field(['name'])->order('sort')->select()->toArray();
            View::assign('area', $area);
            View::assign('industry', $industry);
            View::assign('title', '首页');
            return view();
        }
    }

    public function info($id = 0)
    {
        $company = Db::table('top_company_info')->where(['id' => $id])->find();
        $element_id = Db::table('top_company_review_element')->where(['main_id' => $company['review_id']])->field('id')->order('sort')->find()['id'];
        View::assign('title', '首页');
        View::assign('id', $id);
        View::assign('title', $company['name']);
        View::assign('element_id', $element_id);
        return view();
    }

    public function industry_list()
    {
        $industryList = Db::table('top_industry')->where('level','<=',2)
            ->field(['id','pid','name'])
            ->order('sort')->select()->toArray();


        function buildTree(array $elements, $parentId = 0)
        {
            $branch = [];

            foreach ($elements as &$element) {
                if ($element['pid'] == $parentId) {
                    $children = buildTree($elements, $element['id']);
                    if ($children) {
                        $element['children'] = $children;
                    }

                    // 重命名字段，适配前端 el-cascader
                    $element['value'] = $element['name'];
                    $element['label'] = $element['name'];

                    // 可选：删除原始字段
                    unset($element['id'], $element['pid'], $element['name'],$element['numrow']);

                    $branch[] = $element;
                }
            }

            return $branch;
        }
        $res = buildTree($industryList);
        result($res);
    }

    public function companyInfo($id = 0)
    {
        \app\model\CompanyModel::getCompanyInfo($id);
        exit();
    }

    public function getReview($element_id = 0)
    {
        if (request()->isAjax()) {
            $request = $this->request->post();
            \app\model\CompanyModel::getAjaxReview($element_id, $request);
            exit();

        } else {
            View::assign('element_id', $element_id);
            View::assign('title', '首页');
            return view();
        }
    }
}
