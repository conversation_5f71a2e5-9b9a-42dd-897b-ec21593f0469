<style>
.my-autocomplete li{line-height: normal;padding: 7px;}
.my-autocomplete li .name{text-overflow: ellipsis;overflow: hidden;}
.my-autocomplete li .addr{font-size: 12px;color: #b4b4b4;}
.my-autocomplete li .highlighted{color: #ddd;}
.mytable tbody tr td {font-family: 宋体;text-align: left;}
.mytable tbody tr td p{line-height: 30px;}
.el-form-item{margin-bottom: 0px;}
.el-message-box-top {vertical-align: top;margin-top: 60px;left: 0;}
</style>
<template>
  <el-dialog :title="title" :close-on-click-modal="false" :visible.sync="visible" width="90%" top="10px" @close="refresh()" :append-to-body="true" label-position="top">
    <el-form :model="form" :rules="rules" ref="form" label-width="120px" class="demo-ruleForm">
      <el-form-item label="评审日期:" prop="date">
        <el-date-picker
            size="mini"
            v-model="form.date"
            type="date"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            placeholder="选择日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="评审企业:">
        {{data.company_name}}
      </el-form-item>
      <el-form-item label="企业行业:">
        {{data.industry_name}}
      </el-form-item>
      <el-form-item label="所在行政区:">
        {{data.company_residence}}
      </el-form-item>
      <el-form-item label="咨询机构:">
        {{data.create_nature}}
      </el-form-item>
      <el-form-item label="评审专家小组">
        <el-button type="primary" @click="random" size="mini">随机生成</el-button>
        <el-button type="primary" @click="add" size="mini">添加</el-button>
      </el-form-item>
      <el-table
          :data="form.experts"
          border
          size="small"
          v-loading="loading"
          style="width: 100%">
        <el-table-column
            label="姓名"
            prop="user_name"
            width="100">
        </el-table-column>
        <el-table-column
            prop="position"
            show-overflow-tooltip
            label="职务"
            width="100">
          <template slot-scope="scope">
            <el-select size="mini" v-model="scope.row.position_id" placeholder="请选择">
              <el-option :key="1" label="组长" :value="1"></el-option>
              <el-option :key="2" label="组员" :value="2"></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column
            prop="unit_name"
            show-overflow-tooltip
            label="所属单位"
            width="200">
        </el-table-column>
        <el-table-column
            property="sepc"
            label="从事专业"
            show-overflow-tooltip
            width="100">
        </el-table-column>
        <el-table-column
            prop="status"
            label="响应状态"
            show-overflow-tooltip
            width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status==0" size="mini" type="info">未接收</el-tag>
            <el-tag v-if="scope.row.status==1" size="mini" type="primary">已接收</el-tag>
            <el-tag v-if="scope.row.status==2" size="mini" type="success">已完成</el-tag>
          </template>
        </el-table-column>
        <el-table-column
            prop="reason"
            label="未接收原因"
            show-overflow-tooltip
            min-width="100">
        </el-table-column>
        <el-table-column
            prop="review_info"
            label="历史评审记录"
            show-overflow-tooltip
            width="100">
        </el-table-column>
        <el-table-column
            prop="pbwc_name"
            label="分发执法仪"
            show-overflow-tooltip
            width="100">
        </el-table-column>
        <el-table-column
            prop="offer_info"
            label="专家受聘情况"
            show-overflow-tooltip
            min-width="100">
        </el-table-column>
        <el-table-column fixed="right" label="操作" align="center" width="100">
          <template slot-scope="scope">
            <el-button size="mini" type="danger" @click="del(scope.$index,scope.row)" >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-form-item>
        <el-button type="primary" @click="release(0)" size="mini">保存</el-button>
        <el-button v-if="!form.status" type="primary" @click="release(1)" size="mini">下发</el-button>
      </el-form-item>
    </el-form>
    <expertschoose ref="expertsChoose" @ok="expertsFun"></expertschoose>
  </el-dialog>
</template>
<script>
module.exports = {
  name: "add",
  data:function() {
    return {
      id:0,
      activeName:'a',
      type:1,
      visible: false,
      dialogVisible: false,
      dialogImageUrl: '',
      title: '制定评审计划',
      loading: false,
      user_id: 0,
      data:{

      },
      form:{
        id:'',
        rankapply_id:'',
        experts:[],
      },
      is_see:0,
      userData:[],
      deleteList:[],
      cards:{},
      pcas: [],
      thisuser:'',
      rules: {
        date: [
          { required: true, message: '请选择评审日期', trigger: 'blur' },
        ],
      },
    }
  },
  components:{
    'expertschoose':'url:/general/secure/app/org/view/manager/vue/expertsChoose.vue?v=1',
  },
  methods: {
    /**
     * 打开弹窗调用方法
     * */
    open: function (data) {
      var _this =this;
      _this.visible = true;
      _this.id = data.id;
      _this.getInfo(data);
    },
    //随机生成
    random:function(){
      var _this = this;
      var param = _this.form;
      _this.$refs.form.validate((valid) => {
        if (valid) {
          _this.$prompt('随机生成人数', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            inputPattern: /^\d+$/,
            inputErrorMessage: '请输入数字',
            closeOnClickModal: false,
            closeOnHashChange: false,
            inputType: 'number',
            customClass: 'el-message-box-top',
          }).then(({ value }) => {
            _this.loading = true;
            param.num = value;
            axios.post("random", param).then(function (res) {
              _this.loading = false;
              if(res.data.code==0){
                for(var i in res.data.data){
                  _this.form.experts.push(res.data.data[i]);
                }
              }else{
                _this.$message({
                  message: res.data.msg,
                  type: res.data.type
                });
              }
            }).catch(function (error) {
              console.log("出现错误:",error);
            });
          }).catch(() => {
            console.log('关闭弹框')
          });
        } else {
          return false;
        }
      });
    },
    add() {
      this.$refs.expertsChoose.initialize(this.form);
    },
    /**
     * 专家选择回调数据
     * */
    expertsFun:function(data)
    {
      for(var i in data){
        this.form.experts.push(data[i]);
      }
    },
    del(index, row) {
      this.form.experts.splice(index,1);
      this.deleteList.push(row);
    },
    handleRemove(file, fileList) {
      this.form.files = [];
    },
    uploadBefore(file) {
      const isJPG = file.type === 'image/jpeg';
      if(!isJPG){
        this.$message.error('请上传jpg图片');
      }
      return isJPG;
    },
    uploadSuccess(res, file) {
      let data = {
        id:res.id,
        url:res.url,
        name:res.name,
      };
      if(res.state==='SUCCESS'){
        this.form.files.push(data);
      }
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    handleClick(tab, event) {
      console.log(tab, event);
    },
    closeDialog: function () {
      this.visible = false;
    },
    //下发
    release: function (status) {
      var _this = this;
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$confirm('确定保存?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'info'
          }).then(() => {
            _this.loading = true;
            _this.form.status = status;
            _this.form.deleteList = _this.deleteList;
            axios.post("release", _this.form).then(function (res) {
              _this.loading = false;
              _this.$message({
                message: res.data.msg,
                type: res.data.type
              });
              if (res.data.code == 0) {
                _this.visible = false;
                _this.$emit("ok");
              }
            }).catch(function (error) {
              console.log("出现错误:",error);
            });
          }).catch(() => {
            return false;
          });
        } else {
          return false;
        }
      });
    },
    getInfo:function(data){
      var _this = this;
      axios.post("checkInfo", {
        id:data.id,
      }).then(function (res) {
        if (res.data.code == 0) {
          _this.data=res.data.data;
          _this.form = res.data.data.form;
          _this.deleteList = [];
        }else {
          _this.$message({
            message: res.data.msg,
            type: res.data.type
          });
        }
      }).catch(function (error) {
        console.log("出现错误:",error);
      });
    },
    refresh: function () {
      this.$emit("refresh");
    },
    printDiv:function(){
      var printContents = document.getElementById("print-content").innerHTML;
      var originalContents = document.body.innerHTML;
      document.body.innerHTML = printContents;
      window.print();
      document.body.innerHTML = originalContents;
    },

}
}
</script>


