<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>定级申请审核</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .el-table .danger-row {
            background: #fbc4c4;
        }
        .el-table .warning-row {
            background: oldlace;
        }
        .el-table .success-row {
            background: #f0f9eb;
        }
        .el-badge__content.is-fixed { right: 20px;z-index: 9;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">
        <el-form :inline="true" :model="searchFrom" class="form-inline" @submit.native.prevent>
            <el-form-item>
                <el-radio-group @change="statusChange" size="mini" v-model="searchFrom.status">
                    <el-radio-button label="">全部</el-radio-button>
                    <el-radio-button label="1">待审核</el-radio-button>
                    <el-radio-button label="2">审核中</el-radio-button>
                    <el-radio-button label="7">已完成</el-radio-button>
                    <el-radio-button label="5">未通过</el-radio-button>
                </el-radio-group>
            </el-form-item>
            <el-form-item>
                <el-input v-model="searchFrom.title" size="mini" placeholder="企业名称"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="getData()" size="mini">查询</el-button>
                <el-button @click="reset()" size="mini">重置</el-button>
            </el-form-item>
            <el-form-item style="float: right">
                <!--<el-button :loading="loading" type="success" size="mini" @click="add">添加</el-button>
                <el-button :loading="loading" type="primary" size="mini" @click="export1">导出</el-button>
                <el-button :loading="loading" type="primary"  size="mini" @click="import1">导入</el-button>-->
            </el-form-item>
        </el-form>
        <el-table border
                  v-loading="loading"
                  :data="data"
                  style="width: 100%;margin-bottom: 20px;"
                  ref="qtable"
                  :height="height"
                  size="small">
            <el-table-column
                    type="index"
                    label="序号"
                    align="center"
                    width="50">
            </el-table-column>
            <el-table-column
                    prop="company_name"
                    label="企业名称"
                    align="center"
                    show-overflow-tooltip
                    min-width="200">
                <template slot-scope="scope">
                    <!--<el-link target="_blank" :href="'/general/toppingsoft/index.php/area/company/info?id='+scope.row.id" type="primary" v-html="scope.row.name"></el-link>-->
                    <el-link @click="info(scope.row)" type="primary" v-html="scope.row.company_name"></el-link>
                </template>
            </el-table-column>
            <el-table-column
                    prop="level"
                    label="申请等级"
                    align="center"
                    width="100">
            </el-table-column>
            <el-table-column
                    prop="type"
                    label="申请类型"
                    align="center"
                    width="100">
            </el-table-column>
            <el-table-column
                    prop="nature"
                    label="创建性质"
                    align="center"
                    min-width="100">
                <template slot-scope="scope">
                    <div v-if="scope.row.nature==0" type="info">自主创建</div>
                    <div v-if="scope.row.nature==1" type="primary">“{{scope.row.advisory}}”指导</div>
                </template>
            </el-table-column>
            <!--<el-table-column
                    prop="reform_status"
                    label="整改内容"
                    align="center"
                    width="100">
                <template slot-scope="scope">
                    <el-link v-if="scope.row.reform_status>0" @click="reforminfo(scope.row)" type="primary">详情</el-link>
                </template>
            </el-table-column>-->
            <template v-if="searchFrom.status!=7&&searchFrom.status!=5">
            <el-table-column
                    width="150px"
                    :show-overflow-tooltip="true"
                    prop="processing_stage_name"
                    label="当前办理阶段">
                <template slot-scope="scope">
                    {{scope.row.prcs_name}}
                </template>
            </el-table-column>
            </template>
            <!--<el-table-column
                    prop="FLOW_PRCS"
                    label="已停留"
                    show-overflow-tooltip
                    width="150">
                <template slot-scope="scope">
                    <p>{{scope.row.reach_time}}</p>
                    <p>{{scope.row.receive_time}}</p>
                </template>
            </el-table-column>-->
            <el-table-column
                    prop="mb_status"
                    label="申请状态"
                    align="center"
                    show-overflow-tooltip
                    width="120">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.status==0" type="">未提交</el-tag>
                    <el-tag v-if="scope.row.status==1" type="info">审批中</el-tag>
                    <el-tag v-if="scope.row.status==2" type="info">整改中</el-tag>
                    <el-tag v-if="scope.row.status==3" type="info">整改审批中</el-tag>
                    <el-tag v-if="scope.row.status==7" type="primary">已通过</el-tag>
                    <el-tag v-if="scope.row.status==5" type="danger">未通过</el-tag>
                    <el-tag v-if="scope.row.status==9" type="danger">放弃评审</el-tag>
                </template>
            </el-table-column>
            <el-table-column
                    prop="apply_time"
                    label="申请时间"
                    align="center"
                    show-overflow-tooltip
                    width="120">
            </el-table-column>
            <el-table-column
                    label="操作"
                    align="center"
                    width="120">
                <template slot-scope="scope">
                    <el-button v-if="scope.row.prcs_id==2||scope.row.prcs_id==6" type="primary" @click="check(scope.row)" size="mini">审核</el-button>
                    <el-button v-else  @click="check(scope.row)" size="mini">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!--分页条total, sizes, prev, pager, next, jumper-->
        <div class="block">
            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="page"
                    :page-sizes="[10, 20, 50, 100, 500, 1000]"
                    :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total">
            </el-pagination>
        </div>
    </div>
    <import1 ref="import1" @refresh="getData()"></import1>
    <check ref="check" @ok="getData()"></check>
    <info ref="info" @ok="getData()"></info>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.all.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                title: '新增',
                searchFrom: {
                    status: 1,
                    title: '',
                },
                thisrow: {},
                thisuser: '',
                data: [],
                filelist: [],
                page: 1,
                pageSize: 20,
                total: 0,
                deptData: [],
                userData: [],
                dialogFormVisible: false,
                visible: false,
                loading: true,
                form: {},
                formRules: {
                    form_name: [{required: true, message: "请输入表单名称", trigger: "blur"}],
                },
                ueObj: null,
                height: document.documentElement.clientHeight - 155,
            };
        },
        components: {
            'check': 'url:/general/toppingsoft/app/area/view/grading/vue/check.vue?v=1',
            'import1': 'url:/general/toppingsoft/public/vue/import.vue',
            'info': 'url:/general/toppingsoft/app/area/view/company/vue/info.vue?v=1',
        },
        watch: {
            $route() {
                //切换标签时
                this.handleRefreshTable();
            }
        },
        created() {
            let _this = this;
            //窗口改变时
            window.onresize = function () {
                _this.handleRefreshTable();
            };
        },
        updated() {
            //数据改变时
            this.handleRefreshTable();
        },
        methods: {
            tableRowClassName({row, rowIndex}) {
                if (row.back) {
                    return row.back;
                }
                return '';
            },
            handleClick(tab, event) {
                this.searchFrom = {
                    our: '',
                    status: '',
                    level: '',
                    type: '',
                    company: '',
                    charge_user_name: '',
                };
                this.getData();
            },
            statusChange() {
                this.page = 1;
                this.getData();
            },
            handleSizeChange: function (val) {
                this.pageSize = val;
                this.getData();
                console.log('每页 ${val} 条');
            },
            handleCurrentChange: function (val) {
                this.page = val;
                this.getData();
                console.log('当前页: ${val}');
            },
            info(row) {
                var tmp = row;
                tmp.id = row.company_id;
                this.$refs.info.open(tmp);
            },
            check(row) {
                this.thisrow = row;
                this.$refs.check.open(row);
            },
            //数据初始化
            reset() {
                this.searchFrom.title='';
                this.page = 1;
                this.pageSize = 20;
                this.getData();
            },
            //数据加载
            getData() {
                var _this = this;
                var param = _this.searchFrom;
                param._ajax = 1;
                param.page = _this.page;
                param.limit = _this.pageSize;
                param.our = 1;
                _this.loading = true;
                axios.post('index', param).then(function (res) {
                    _this.loading = false;
                    if (res.data.code == 0) {
                        _this.data = res.data.data.data;
                        _this.page = res.data.data.current_page;
                        _this.pageSize = res.data.data.per_page;
                        _this.total = res.data.data.total;
                        _this.$nextTick(() => {
                            // 这里的代码会在DOM更新完成后执行
                            _this.loading = false;
                        });
                        _this.handleRefreshTable();
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            import1: function () {
                this.$refs.import1.templateUrl = 'importTemplate';
                this.$refs.import1.submitUrl = 'import';
                this.$refs.import1.title = '企业信息导入';
                this.$refs.import1.open();
            },
            export1: function () {
                let _this = this;
                let where = "";
                let type = '';
                //获得where
                where = '';
                for (let index in this.searchFrom) {
                    if (this.searchFrom[index] !== '' && this.searchFrom[index] !== [] && this.searchFrom[index] !== {}) {
                        let str = "";
                        if (index == 'type') {
                            for (let i in this.searchFrom.type) {
                                type += this.searchFrom.type[i] + ',';
                            }
                            str += 'type=' + type;
                        } else {
                            str += index + '=' + this.searchFrom[index];
                        }
                        where += "&" + str;
                    }
                }
                let url = "index?excel=1&" + where;
                //window.open(url);
                location.href = url;
            },
            handleRefreshTable: function () {
                this.$nextTick(function () {
                    var refList = this.$refs;
                    if (refList) {
                        for (var i of Object.keys(refList)) {
                            if (refList[i] && refList[i].doLayout) {
                                refList[i].doLayout();
                            }
                        }
                    }
                });
            },
        },
        mounted() {
            //获取列表
            this.getData();

        }
    })
</script>


</body>
</html>