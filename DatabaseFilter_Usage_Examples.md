# 数据库表和字段快速过滤功能使用说明

## 概述
为 `DatabaseCompatibilityService` 添加了三个新的过滤方法，支持对表和字段进行快速搜索和过滤。

## 新增方法

### 1. filterTables($filter, $options) - 过滤表列表

#### 基本用法
```php
use app\admin\service\DatabaseCompatibilityService;

// 基本过滤 - 搜索包含 "user" 的表
$tables = DatabaseCompatibilityService::filterTables('user');

// 精确匹配表名
$tables = DatabaseCompatibilityService::filterTables('user_info', [
    'exact_match' => true
]);

// 只搜索表名，不搜索注释
$tables = DatabaseCompatibilityService::filterTables('user', [
    'search_name' => true,
    'search_comment' => false
]);

// 大小写敏感搜索
$tables = DatabaseCompatibilityService::filterTables('User', [
    'case_sensitive' => true
]);
```

#### 过滤选项
- `search_name` (bool): 是否搜索表名，默认 true
- `search_comment` (bool): 是否搜索表注释，默认 true
- `case_sensitive` (bool): 是否大小写敏感，默认 false
- `exact_match` (bool): 是否精确匹配，默认 false

### 2. filterTableFields($tableName, $filter, $options) - 过滤表字段

#### 基本用法
```php
// 基本过滤 - 搜索表中包含 "name" 的字段
$fields = DatabaseCompatibilityService::filterTableFields('user_info', 'name');

// 只搜索字段类型
$fields = DatabaseCompatibilityService::filterTableFields('user_info', 'varchar', [
    'search_name' => false,
    'search_comment' => false,
    'search_type' => true
]);

// 精确匹配字段名
$fields = DatabaseCompatibilityService::filterTableFields('user_info', 'user_name', [
    'exact_match' => true
]);

// 搜索字段注释
$fields = DatabaseCompatibilityService::filterTableFields('user_info', '用户', [
    'search_name' => false,
    'search_comment' => true,
    'search_type' => false
]);
```

#### 过滤选项
- `search_name` (bool): 是否搜索字段名，默认 true
- `search_comment` (bool): 是否搜索字段注释，默认 true
- `search_type` (bool): 是否搜索字段类型，默认 true
- `case_sensitive` (bool): 是否大小写敏感，默认 false
- `exact_match` (bool): 是否精确匹配，默认 false

### 3. quickSearch($keyword, $options) - 快速搜索

#### 基本用法
```php
// 快速搜索 - 同时搜索表和字段
$result = DatabaseCompatibilityService::quickSearch('user');
// 返回格式：
// [
//     'tables' => [...],    // 匹配的表列表
//     'fields' => [...]     // 匹配的字段列表（包含所属表信息）
// ]

// 只搜索表
$result = DatabaseCompatibilityService::quickSearch('user', [
    'search_tables' => true,
    'search_fields' => false
]);

// 限制返回数量
$result = DatabaseCompatibilityService::quickSearch('user', [
    'max_tables' => 10,
    'max_fields_per_table' => 5
]);

// 大小写敏感搜索
$result = DatabaseCompatibilityService::quickSearch('User', [
    'case_sensitive' => true
]);
```

#### 搜索选项
- `search_tables` (bool): 是否搜索表，默认 true
- `search_fields` (bool): 是否搜索字段，默认 true
- `max_tables` (int): 最大返回表数量，默认 50
- `max_fields_per_table` (int): 每个表最大返回字段数量，默认 10
- `case_sensitive` (bool): 是否大小写敏感，默认 false

## 实际应用场景

### 1. 表管理界面的搜索功能
```php
// 用户在表列表中输入搜索关键词
$searchKeyword = $_GET['search'] ?? '';
if (!empty($searchKeyword)) {
    $tables = DatabaseCompatibilityService::filterTables($searchKeyword);
} else {
    $tables = DatabaseCompatibilityService::getTables();
}
```

### 2. 字段管理界面的过滤
```php
// 在表结构页面过滤字段
$tableName = $_GET['table'] ?? '';
$fieldFilter = $_GET['field_filter'] ?? '';

if (!empty($fieldFilter)) {
    $fields = DatabaseCompatibilityService::filterTableFields($tableName, $fieldFilter);
} else {
    $fields = DatabaseCompatibilityService::getTableFields($tableName);
}
```

### 3. 全局搜索功能
```php
// 实现类似IDE的全局搜索功能
$keyword = $_POST['keyword'] ?? '';
if (!empty($keyword)) {
    $searchResult = DatabaseCompatibilityService::quickSearch($keyword, [
        'max_tables' => 20,
        'max_fields_per_table' => 8
    ]);
    
    echo "找到 " . count($searchResult['tables']) . " 个表\n";
    echo "找到 " . count($searchResult['fields']) . " 个字段\n";
}
```

### 4. API接口示例
```php
// 表搜索API
public function searchTables()
{
    $keyword = input('keyword', '');
    $options = [
        'search_name' => input('search_name/b', true),
        'search_comment' => input('search_comment/b', true),
        'case_sensitive' => input('case_sensitive/b', false),
        'exact_match' => input('exact_match/b', false)
    ];
    
    $tables = DatabaseCompatibilityService::filterTables($keyword, $options);
    
    return json([
        'code' => 200,
        'data' => $tables,
        'count' => count($tables)
    ]);
}

// 字段搜索API
public function searchFields()
{
    $tableName = input('table_name', '');
    $keyword = input('keyword', '');
    $options = [
        'search_name' => input('search_name/b', true),
        'search_comment' => input('search_comment/b', true),
        'search_type' => input('search_type/b', true),
        'case_sensitive' => input('case_sensitive/b', false),
        'exact_match' => input('exact_match/b', false)
    ];
    
    $fields = DatabaseCompatibilityService::filterTableFields($tableName, $keyword, $options);
    
    return json([
        'code' => 200,
        'data' => $fields,
        'count' => count($fields)
    ]);
}

// 快速搜索API
public function quickSearch()
{
    $keyword = input('keyword', '');
    $options = [
        'search_tables' => input('search_tables/b', true),
        'search_fields' => input('search_fields/b', true),
        'max_tables' => input('max_tables/d', 50),
        'max_fields_per_table' => input('max_fields_per_table/d', 10),
        'case_sensitive' => input('case_sensitive/b', false)
    ];
    
    $result = DatabaseCompatibilityService::quickSearch($keyword, $options);
    
    return json([
        'code' => 200,
        'data' => $result,
        'tables_count' => count($result['tables']),
        'fields_count' => count($result['fields'])
    ]);
}
```

## 性能优化建议

1. **缓存结果**: 对于大型数据库，可以考虑缓存表和字段信息
2. **分页处理**: 对于大量结果，建议实现分页功能
3. **索引优化**: 确保数据库的系统表有适当的索引
4. **异步搜索**: 对于复杂搜索，可以考虑异步处理

## 注意事项

1. 所有过滤方法都是基于内存中的数据进行过滤，适合中小型数据库
2. 对于大型数据库，建议在SQL层面进行过滤以提高性能
3. 搜索是基于字符串包含匹配，不支持正则表达式
4. 字段搜索会遍历所有表，对于大量表的情况需要注意性能
