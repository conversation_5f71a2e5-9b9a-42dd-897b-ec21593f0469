<?php

namespace app\company\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use app\company\model\CompanyModel;
use app\company\model\GradingModel;
use app\org\controller\Tasks;
use app\validate\GradingVerify as Verify;
use app\model\SettingModel;
use hg\apidoc\annotation as Apidoc;
use think\facade\View;
use think\Request;
use think\facade\Db;
use app\model\FileModel;
use DateTime;

/**
 * @Apidoc\Title("创标申请")
 * @Apidoc\Group("Index")
 * @Apidoc\Sort(1)
 */
class Grading extends Base {

    /**
     * @Apidoc\Title("企业首页")
     * @Apidoc\Desc("企业首页")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function index($limit=20) {
        if (request()->isAjax()) {
            $where = [];
            $where[] = ['company_id','=',$_SESSION['company']['id']];
            $where[] = ['type','=','初次申请'];
            $res = Db::table('top_grading')->alias('a')
                ->leftJoin('top_grading_approval b','a.prcs_id = b.prcs_id and a.id = b.grading_id and b.status in (1,2)')
                ->where($where)->order('id desc')
                ->field('a.*,b.create_time reach_time,b.receive_time,b.status prcs_status')
                ->paginate($limit)->each(function ($item, $key) {
                    $item = GradingModel::codeToText($item);
                    $item['prcs_name'] = empty($item['prcs_name'])?'已结束':$item['prcs_name'];
                    $item['apply_time'] = date('Y-m-d', strtotime($item['apply_time']));
                    if($item['prcs_status']){
                        $interval = date_diff(new DateTime($item['reach_time']), new DateTime(date('Y-m-d H:i:s')));
                        $item['reach_time'] = $interval->format('到达：%a天%h小时');
                        $interval = date_diff(new DateTime($item['receive_time']), new DateTime(date('Y-m-d H:i:s')));
                        $item['receive_time'] = empty($item['receive_time']) ? '办理：未接收' : $interval->format('办理：%a天%h小时');
                        $item['self_time'] = date('Y-m-d', $item['self_time']);
                    }else{
                        $item['reach_time'] = '已结束';
                    }
                    return $item;
                });
            result($res);
        } else {
            $company = Db::table('top_company_info')->where(['id'=>$_SESSION['company']['id']])->find();
            View::assign('company',CompanyModel::codeToText($company));
            return view();
        }
    }

    /**
     * @Apidoc\Title("企业首页")
     * @Apidoc\Desc("企业首页")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function repeat($limit=20) {
        if (request()->isAjax()) {
            $where = [];
            $where[] = ['company_id','=',$_SESSION['company']['id']];
            $where[] = ['type','=','复评申请'];
            $res = Db::table('top_grading')->alias('a')
                ->leftJoin('top_grading_approval b','a.prcs_id = b.prcs_id and a.id = b.grading_id and b.status in (1,2)')
                ->where($where)->order('id desc')
                ->field('a.*,b.create_time reach_time,b.receive_time,b.status prcs_status')
                ->paginate($limit)->each(function ($item, $key) {
                    $item = GradingModel::codeToText($item);
                    $item['prcs_name'] = empty($item['prcs_name'])?'已结束':$item['prcs_name'];
                    $item['apply_time'] = date('Y-m-d', strtotime($item['apply_time']));
                    if($item['prcs_status']){
                        $interval = date_diff(new DateTime($item['reach_time']), new DateTime(date('Y-m-d H:i:s')));
                        $item['reach_time'] = $interval->format('到达：%a天%h小时');
                        $interval = date_diff(new DateTime($item['receive_time']), new DateTime(date('Y-m-d H:i:s')));
                        $item['receive_time'] = empty($item['receive_time']) ? '办理：未接收' : $interval->format('办理：%a天%h小时');
                        $item['self_time'] = date('Y-m-d', $item['self_time']);
                    }else{
                        $item['reach_time'] = '已结束';
                    }
                    return $item;
                });
            result($res);
        } else {
            $company = Db::table('top_company_info')->where(['id'=>$_SESSION['company']['id']])->find();
            $company['ca_end'] = date('Y-m-d',strtotime($company['ca_date'].' +3year'));
            View::assign('company',CompanyModel::codeToText($company));
            return view();
        }
    }


    public function record($limit=20) {
        if (request()->isAjax()) {
            $where = [];
            $where[] = ['company_id','=',$_SESSION['company']['id']];
            $res = Db::table('top_grading_record')
                ->where($where)->order('id desc')->paginate($limit)->each(function ($item, $key) {
                    // 处理证书附件文件
                    $codes = explode(',',$item['file']);
                    $item['file'] = [];
                    foreach ($codes as $v1){
                        if(!empty($v1)){
                            $f = FileModel::getFile(0,$v1,'');
                            $item['file'][] = [
                                'id' => $f['id'],
                                'code' => $f['code'],
                                'name' => $f['name'],
                                'url' => $f['url'],
                                'ext' => $f['ext'],
                            ];
                        }
                    }

                    // 获取区县名称
                    if($item['area_id']){
                        $area = Db::table('top_area')->where(['id'=>$item['area_id']])->find();
                        $item['area_name'] = $area['name'] ?? '';
                    }
                    
                    $item['apply_time'] = date('Y-m-d', strtotime($item['apply_time']));
                    $item['check_time'] = empty($item['check_time'])?'':date('Y-m-d', strtotime($item['check_time']));
                    $item['achieve_date'] = empty($item['achieve_date'])?'':date('Y-m-d', strtotime($item['achieve_date']));
                    $item['certificate_end_date'] = empty($item['certificate_end_date'])?'':date('Y-m-d', strtotime($item['certificate_end_date']));
                    return $item;
                });
            result($res);
        } else {
            $company = Db::table('top_company_info')->where(['id'=>$_SESSION['company']['id']])->find();
            $company = CompanyModel::codeToText($company);
            if($company['area_id']){
                $area = Db::table('top_area')->where(['id'=>$company['area_id']])->find();
                $company['area_name'] = $area['name'] ?? '';
            }
            View::assign('company',$company);
            return view();
        }
    }

    public function recordSave() {
        $company = Db::table('top_company_info')->where(['id'=>$_SESSION['company']['id']])->find();
        $company = CompanyModel::codeToText($company);
        $id = $this->request->post('id', 0);
        $level = $this->request->post('level');
        $content = $this->request->post('content');
        $file = $this->request->post('file');
        $certificate_code = $this->request->post('certificate_code');
        $certificate_start_date = $this->request->post('certificate_start_date');
        $certificate_end_date = $this->request->post('certificate_end_date');
        $certificate_file = $this->request->post('file');
        
        $data = [
            'company_id' => $company['id'],
            'company_name' => $company['name'],
            'address' => $company['mb_operate_address'],
            'company_code' => $company['license_number'],
            'legal' => $company['legal'],
            'legal_mobile' => $company['legal_mobile'],
            'legal_email' => $company['legal_email'],
            'manager' => $company['manager'],
            'manager_mobile' => $company['manager_mobile'],
            'manager_email' => $company['manager_email'],
            'content' => $content,
            'level' => $level,
            'industry' => $company['industry'],
            'specialty' => $company['specialty'],
            'area_id' => $company['area_id'],
            'city_id' => $company['city_id'],
            'dept_id' => $company['dept_id'],
            'enterprise_size' => $company['enterprise_size'],
            'certificate_code' => $certificate_code,
            'certificate_start_date' => $certificate_start_date,
            'certificate_end_date' => $certificate_end_date,
        ];

        // 处理证书附件
        $certificate_codes = [];
        if($certificate_file){
            foreach ($certificate_file as $v1){
                $certificate_codes[] = $v1['code'];
                FileModel::saveFile($v1['code'],'company/grading/certificate/'.date('Ym'));
            }
        }
        $data['file'] = implode(',',$certificate_codes);
        if($id > 0) {
            // 编辑模式
            $data['update_time'] = date('Y-m-d H:i:s');
            $result = Db::table('top_grading_record')->where(['id'=>$id,'company_id'=>$_SESSION['company']['id']])->update($data);
            if($result !== false){
                result('',0,'更新成功');
            }else{
                result('',1001,'更新失败');
            }
        } else {
            // 新增模式
            $data['date'] = date('Y-m-d');
            $data['status'] = 1;
            $data['apply_user_id'] = $_SESSION['company']['user_id'];
            $data['apply_user_name'] = $_SESSION['company']['user_name'];
            $data['apply_time'] = date('Y-m-d H:i:s');
            
            $insert_id = Db::table('top_grading_record')->insert($data);
            if($insert_id){
                result('',0,'提交成功');
            }else{
                result('',1001,'网络错误');
            }
        }
    }

    public function recordInfo($id=0) {
        $re = Db::table('top_grading_record')->where(['id'=>$id,'company_id'=>$_SESSION['company']['id']])->find();
        result($re);
    }

    public function applyfirst($id=0) {
        if (request()->isAjax()) {
            $company = Db::table('top_company_info')->where(['id'=>$_SESSION['company']['id']])->find();
            $company = CompanyModel::codeToText($company);
            $company['company_name'] = $company['name'];
            $company['address'] = $company['mb_reg_address'];
            $company['company_code'] = $company['license_number'];
            if($id>0){
                $re = Db::table('top_grading')->where(['id'=>$id])->find();
                foreach ($re as $k=>$v){
                    $re[$k] = empty($company[$k])?$v:$company[$k];
                }
                foreach (config('global.grading_files') as $v){
                    $files = empty(trim($re[$v['field']],','))?[]:explode(',',trim($re[$v['field']],','));
                    $re[$v['field']] = [];
                    foreach ($files as $k1=>$v1){
                        $re[$v['field']][$k1] = FileModel::getFile(0,$v1,'');
                    }
//                    $re[$v['field']] = FileModel::getFile(0,$re[$v['field']]);
                }
            }else{
                //standard_level,$company['standard_level'],是否包含小型或者微型
                $company['level'] = '三级';
                if (!empty($company['standard_level'])) {
                    // 检查是否包含 "小型"
                    if (strpos($company['standard_level'], '小型') !== false) {
                        $company['level'] = '小型';
                    }
                    // 检查是否包含 "微型"
                    elseif (strpos($company['standard_level'], '微型') !== false) {
                        $company['level'] = '微型';
                    }
                }
                $company['type'] = '初次申请';
                $company['nature'] = '';
                foreach (config('global.grading_files') as $v){
                    $company[$v['field']] = '';
                    $company[$v['field'].'Url'] = '';
                }
                $re = $company;
            }
            $re['id'] = $id;
            result($re);
        } else {
            View::assign('files',config('global.grading_files'));
            View::assign('id',$id);
            return view();
        }
    }

    public function applyrepeat($id=0) {
        if (request()->isAjax()) {
            $company = Db::table('top_company_info')->where(['id'=>$_SESSION['company']['id']])->find();
            $company = CompanyModel::codeToText($company);
            $company['company_name'] = $company['name'];
            $company['address'] = $company['mb_reg_address'];
            $company['company_code'] = $company['license_number'];
            if($id>0){
                $re = Db::table('top_grading')->where(['id'=>$id])->find();
                foreach ($re as $k=>$v){
                    $re[$k] = empty($company[$k])?$v:$company[$k];
                }
                foreach (config('global.grading_files') as $v){
                    $files = empty(trim($re[$v['field']],','))?[]:explode(',',trim($re[$v['field']],','));
                    $re[$v['field']] = [];
                    foreach ($files as $k1=>$v1){
                        $re[$v['field']][$k1] = FileModel::getFile(0,$v1,'');
                    }
//                    $re[$v['field']] = FileModel::getFile(0,$re[$v['field']]);
                }
            }else{
                $company['level'] = '三级';
                if (!empty($company['standard_level'])) {
                    // 检查是否包含 "小型"
                    if (strpos($company['standard_level'], '小型') !== false) {
                        $company['level'] = '小型';
                    }
                    // 检查是否包含 "微型"
                    elseif (strpos($company['standard_level'], '微型') !== false) {
                        $company['level'] = '微型';
                    }
                }
                $company['type'] = '复评申请';
                $company['nature'] = '';
                foreach (config('global.grading_files') as $v){
                    $company[$v['field']] = '';
                    $company[$v['field'].'Url'] = '';
                }
                $re = $company;
            }
            $re['id'] = $id;
            result($re);
        } else {
            View::assign('files',config('global.grading_files'));
            View::assign('id',$id);
            return view();
        }
    }

    public function upload($model='company') {
        $file = request()->file('file');
        $result = FileModel::upload($file,$model);
        result($result);
    }

    //删除文件
    public function deleteFile()
    {
        try {
            $request = $this->request->post();
            FileModel::deleteFile([$request['code']]);
            result('',0,'删除成功');
        }catch (\Exception $e){
            result('',$e->getCode(),'删除失败，原因：'.$e->getMessage());
        }
    }

    public function gradingSave($id=0) {
        $request = $this->request->post();
        if($request['status']==1){
            Verify::userCheck('save',$request);
        }
        $id = GradingModel::gradingSave($request,$id);
        if($id>0){
            result(['id'=>$id],0,'提交成功');
        }else{
            result('',7001,$id);
        }
    }

    //放弃评审
    public function retract($id=0) {
        $re = Db::table('top_grading')->where(['id'=>$id,'company_id'=>$_SESSION['company']['id']])->find();
        if(empty($re)){
            result('',1002,'单据信息有误，请刷新重试');
        }
        if($re['status']==1){
            $date = date('Y-m-d',strtotime('+6month'));
            $data = [
                'status' => 9,
                'prcs_id' => 0,
                'prcs_name' => '',
            ];
            Db::table('top_grading')->where('id',$id)->update($data);
            $apply_data = [
                'grading_id' => $id,
                'prcs_id' => 10,
                'prcs_name' => config('global.grading_prcs')[10]['title'],
                'status' => 7,
                'status_name' => '已完成',
                'create_user_id' => $_SESSION['company']['user_id'],
                'create_user_name' => $_SESSION['company']['user_name'],
                'create_time' => date('Y-m-d H:i:s'),
                'receive_user_id' => $_SESSION['company']['user_id'],
                'receive_user_name' => $_SESSION['company']['user_name'],
                'receive_time' => date('Y-m-d H:i:s'),
                'end_user_id' => $_SESSION['company']['user_id'],
                'end_user_name' => $_SESSION['company']['user_name'],
                'end_time' => date('Y-m-d H:i:s'),
            ];
            Db::table('top_grading_approval')->insertGetId($apply_data);
            Db::table('top_company_info')->where('id',$_SESSION['company']['id'])->update(['st_date'=>$date]);
            $task = Db::table('top_org_tasks')->where(['grading_id'=>$id])->find();
            if($task){
                Tasks::cancelTask($task['id']);
            }
            result('',0,'操作成功');
        }else{
            result('',1002,'当前状态无法放弃评审');
        }
    }

    public function info($id=0) {
        if (request()->isAjax()) {
            $re = Db::table('top_grading')->where(['id'=>$id,'company_id'=>$_SESSION['company']['id']])->find();
            $re = GradingModel::codeToText($re);
            $re['prcs'] = Db::table('top_grading_approval')->where(['grading_id'=>$re['id']])->order('id')
                ->select()->each(function ($item, $key) {
                    $files = empty($item['check_files'])?[]:explode(',',$item['check_files']);
                    foreach ($files as $v){
                        $f = FileModel::getFile(0,$v,'');
                        $item['files'][] = [
                            'id' => $f['id'],
                            'name' => $f['name'],
                            'url' => $f['url'],
                        ];
                    }
                    $item['end_time'] = empty($item['end_time'])?'':date('Y-m-d',strtotime($item['end_time']));
                    $item['remark'] = empty($item['remark'])?$item['check_content']:$item['remark'];
                    return $item;
                })->toArray();
            $re['steps'] = [];
            $re['stepsActive'] = 1;
            if(in_array($re['status'],[5,7,9])){
                foreach ($re['prcs'] as $v){
                    $re['steps'][] = [
                        'label' => $v['prcs_name'],
                        'status' => 'success',
                    ];
                }
                $re['steps'][count($re['steps'])-1]['status'] = in_array($re['status'],[5,9])?'error':'success';
                $re['stepsActive'] = count($re['steps']);
            }else{
                $i = 0;
                foreach (config('global.grading_prcs') as $k=>$v){
                    if((!in_array($k,[5,6,7])||in_array($re['reform_status'],[1,2]))&&!in_array($k,[10])){
                        $re['steps'][] = [
                            'label' => $v['title'],
                            'status' => '',
                        ];
                    }
                    if($re['prcs_id']==$k){
                        $re['stepsActive'] = $i;
                    }
                    $i++;
                }
            }
            $re['files'] = config('global.grading_files');
            result($re);
        } else {
            View::assign('files',config('global.grading_files'));
            View::assign('id',$id);
            return view();
        }
    }

    public function reformSave($id=0) {
        $files = $this->request->post('files');
        $remark = $this->request->post('remark');
        $re = Db::table('top_grading')->where(['id'=>$id])->find();
        if($re['company_id']!=$_SESSION['company']['id']||$re['reform_status']!=1){
            result('',7002,'申请数据有误');
        }
        $codes = [];
        foreach ($files as $v){
            $codes[] = $v['code'];
            FileModel::saveFile($v['code'],'company/reform/'.date('Ym'));
        }
        if(empty($codes)){
            result('',1002,'请上传整改报告');
        }
        $data = [
            'check_files' => implode(',',$codes),
            'remark' => $remark,
            'status' => 7,
            'status_name' => '已提交',
            'end_user_id' => $_SESSION['company']['user_id'],
            'end_user_name' => $_SESSION['company']['user_name'],
            'end_time' => date('Y-m-d H:i:s'),
        ];
        $area = Db::table('top_grading_approval')->field("params")->where(['grading_id'=>$re['id'],'prcs_id'=>2])->find();
        Db::table('top_grading_approval')->where(['grading_id'=>$re['id'],'prcs_id'=>5,'status'=>1])->update($data);
        $apply_data = [
            'grading_id' => $id,
            'prcs_id' => 6,
            'prcs_name' => config('global.grading_prcs')[6]['title'],
            'status' => 1,
            'params' => $area['params'],
            'status_name' => '待接收',
            'create_user_id' => $_SESSION['company']['user_id'],
            'create_user_name' => $_SESSION['company']['user_name'],
            'create_time' => date('Y-m-d H:i:s'),
        ];
        Db::table('top_grading_approval')->insertGetId($apply_data);
        Db::table('top_grading')->where(['id'=>$id])->update(['prcs_id'=>6,'prcs_name'=>'整改审批中','status'=>3,'reform_status'=>2]);
        result('',0,'提交成功');
    }

    //获取企业整改详情数据
    public function getReformInfo()
    {
        $request = $this->request->post();
        if( isset($request['grading_id']) && !empty($request['grading_id']) ){
            $data = Db::table('top_company_reform')->where(["grading_id" => $request['grading_id']])->select()->toArray();
            if( !empty($data) && count($data) > 0 )
            {
                foreach ($data as $k => $datum)
                {
                    $reform_before_files = !empty($datum['reform_before_files'])?$datum['reform_before_files'] = explode(',',$datum['reform_before_files']):[];
                    $data[$k]['reform_before_files'] = [];
                    foreach ($reform_before_files as $v){
                        $f = FileModel::getFile('',$v,'');
                        $data[$k]['reform_before_files'][] = $f;
                        $data[$k]['reform_before_files_preview'][] = $f['url'];
                    }

                    $reform_affter_files = !empty($datum['reform_affter_files'])?$datum['reform_affter_files'] = explode(',',$datum['reform_affter_files']):[];
                    $data[$k]['reform_affter_files'] = [];
                    foreach ($reform_affter_files as $v){
                        $f = FileModel::getFile('',$v,'');
                        $data[$k]['reform_affter_files'][] = $f;
                        $data[$k]['reform_affter_files_preview'][] = $f['url'];
                    }
                }
            }

            result($data,0,'提交成功');
        }else{
            result('',7001,'缺少关键参数');
        }
    }

    //保存企业整改详情数据
    public function reformItemSave()
    {
        $grading_id = $this->request->post("grading_id");
        $data = $this->request->post("data");

        $re = Db::table('top_grading')->field("company_id,reform_status")->where(['id'=>$grading_id])->find();
//        dd($re['company_id'],$re['reform_status'],$_SESSION['company']['id']);

        if( $re['company_id'] != $_SESSION['company']['id'] || $re['reform_status']!=1){
            result('',7002,'申请数据有误');
        }

        if( is_array($data) && count($data) > 0 )
        {
            foreach ($data as $item)
            {
                $reform_before_files = [];
                if( is_array($item['reform_before_files']) && count($item['reform_before_files']) > 0 )
                {
                    foreach ($item['reform_before_files'] as $fileItem)
                    {
                        $reform_before_files[] = $fileItem['code'];
                    }
                    $item['reform_before_files'] = implode(',',$reform_before_files);
                }else{
                    $item['reform_before_files'] = "";
                }

                $reform_affter_files = [];
                if( is_array($item['reform_affter_files']) && count($item['reform_affter_files']) > 0 )
                {
                    foreach ($item['reform_affter_files'] as $fileItem)
                    {
                        $reform_affter_files[] = $fileItem['code'];
                    }
                    $item['reform_affter_files'] = implode(',',$reform_affter_files);
                }else{
                    $item['reform_affter_files'] = "";
                }

                $item['update_user_id'] =  $_SESSION['company']['user_id'];
                $item['update_user_name'] =  $_SESSION['company']['user_name'];
                $item['update_time'] =  date('Y-m-d H:i:s');
                $id = $item['id'];
                unset($item['id']);
                Db::table('top_company_reform')->where(['id'=>$id])->strict(false)->update($item);
            }
        }

        result('',0,'保存成功');
    }

}
