<?php
namespace app\model;

use think\facade\Db;
use think\Model;

class MessageModel extends Model
{

    /**
     * 发送通知消息
     * @param $user_type 用户类型 {company:企业用户，area：区县应急局用户，city：市应急局用户，org：评审单位用户，expert：专家用户}
     * @param $user_id 用户id
     * @param $sms_type 消息类型
     * @param $sms_content 消息内容
     * @param $sms_url 跳转地址
     */
    public static function sendSms($user_type = '', $user_id, $sms_type = '',$sms_content='',$sms_url='')
    {
        $data = [
            'user_type' => $user_type,
            'user_id' => $user_id,
            'sms_type' => $sms_type,
            'sms_content' => $sms_content,
            'sms_url' => $sms_url,
        ];
        $re = Db::table('top_message')->where($data)->find();
        if($re){
            return ['code'=>1002,'msg'=>'重复操作'];
        }
        $data['sms_time'] = date('Y-m-d H:i:s');
        Db::table('top_message')->insert($data);
        $wechat = new Wechat();
//        $wechat->send();
        return ['code'=>0,'msg'=>'发送成功'];
    }


}
