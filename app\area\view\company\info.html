﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{$title}</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/company.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .my-label { width: 200px;}
        .my-content { width: 450px;}
        .item { width: 30px;height:30px;line-height:30px;margin: 10px 0;border-radius:40px;background-color: #f0f0f0;text-align:center;cursor: pointer;}
        .item.checked { background-color: #1989FA;color:#fff;}
        .el-divider.el-divider--horizontal { margin:10px 0;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <el-container>
        <el-tabs type="border-card" style="width:100%;">
            <el-tab-pane label="基本信息">
                <el-descriptions class="margin-top" title="" :column="3" border label-class-name="my-label" content-class-name="my-content">
                    <el-descriptions-item label="企业名称">
                        {{data.name}}
                    </el-descriptions-item>
                    <el-descriptions-item label="注册地址">
                        {{data.mb_reg_address}}
                    </el-descriptions-item>
                    <el-descriptions-item label="生产经营地点">
                        {{data.mb_operate_address}}
                    </el-descriptions-item>
                    <el-descriptions-item label="所属行政区">
                        {{data.mb_region}}
                    </el-descriptions-item>
                    <el-descriptions-item label="营业执照">
                        <el-image
                                style="width: 100px; height: 100px"
                                :src="data.licenseUrl"
                                :preview-src-list="[data.licenseUrl]">
                        </el-image>
                    </el-descriptions-item>
                    <el-descriptions-item label="安全行政许可资料">
                        <el-image
                                style="width: 100px; height: 100px"
                                :src="data.aocUrl"
                                :preview-src-list="[data.aocUrl]">
                        </el-image>
                    </el-descriptions-item>
                    <el-descriptions-item label="法定代表人">
                        {{data.legal}}
                    </el-descriptions-item>
                    <el-descriptions-item label="法人联系电话">
                        {{data.legal_mobile}}
                    </el-descriptions-item>
                    <el-descriptions-item label="法人邮箱">
                        {{data.legal_email}}
                    </el-descriptions-item>
                    <el-descriptions-item label="座机电话号码">
                        {{data.phone}}
                    </el-descriptions-item>
                    <el-descriptions-item label="企业传真">
                        {{data.fax}}
                    </el-descriptions-item>
                    <el-descriptions-item label="产业园区">
                        {{data.industrial_park}}
                    </el-descriptions-item>
                    <el-descriptions-item label="邮政编码">
                        {{data.postal_code}}
                    </el-descriptions-item>
                    <el-descriptions-item label="国民经济行业">
                        {{data.mb_economy_sector}}
                    </el-descriptions-item>
                    <el-descriptions-item label="行业/专业">
                        {{data.industry}}/{{data.specialty}}
                    </el-descriptions-item>
                    <el-descriptions-item label="统一社会信用代码">
                        {{data.license_number}}
                    </el-descriptions-item>
                    <el-descriptions-item label="信用代码有效期">
                        {{data.license_date}}
                    </el-descriptions-item>
                    <el-descriptions-item label="经济类型">
                        {{data.mb_economy_type}}
                    </el-descriptions-item>
                    <el-descriptions-item label="企业规模">
                        {{data.enterprise_size}}
                    </el-descriptions-item>
                    <el-descriptions-item label="注册资本">
                        {{data.reg_money}}万元
                    </el-descriptions-item>
                    <el-descriptions-item label="安全管理联系人">
                        {{data.manager}}
                    </el-descriptions-item>
                    <el-descriptions-item label="联系电话">
                        {{data.manager_mobile}}
                    </el-descriptions-item>
                    <el-descriptions-item label="邮箱">
                        {{data.manager_email}}
                    </el-descriptions-item>
                    <el-descriptions-item label="成立日期">
                        {{data.date}}
                    </el-descriptions-item>
                    <el-descriptions-item label="固定资产">
                        {{data.fixed_asset}}
                    </el-descriptions-item>
                    <el-descriptions-item label="年营业收入">
                        {{data.revenue}}万元
                    </el-descriptions-item>
                    <el-descriptions-item label="员工总数">
                        {{data.personnel}}
                    </el-descriptions-item>
                    <el-descriptions-item label="营业场所面积">
                        {{data.area}}m²
                    </el-descriptions-item>
                    <el-descriptions-item label="专职安全管理人数">
                        {{data.personnel_full}}
                    </el-descriptions-item>
                    <el-descriptions-item label="兼职安全管理人数">
                        {{data.personnel_part}}
                    </el-descriptions-item>
                    <el-descriptions-item label="特种作业人数">
                        {{data.personnel_special}}
                    </el-descriptions-item>
                    <el-descriptions-item label="所属集团名称">
                        {{data.group_name}}
                    </el-descriptions-item>
                    <el-descriptions-item label="经营范围" :span="3">
                        {{data.business}}
                    </el-descriptions-item>

                    <el-descriptions-item :span="3">
                        <template slot="label">
                            <el-tag type="info" effect="dark" style="font-size: 15px;display: block;">粉尘涉爆</el-tag>
                        </template>
                    </el-descriptions-item>
                    <el-descriptions-item label="是否粉尘涉爆" :span="3">
                        {{data.is_dust_explosion}}
                    </el-descriptions-item>
                    <el-descriptions-item label="所属行业" v-if="data.is_dust_explosion=='是'">
                        {{data.sector}}
                    </el-descriptions-item>
                    <el-descriptions-item label="粉尘种类" v-if="data.is_dust_explosion=='是'">
                        {{data.dust_type}}
                    </el-descriptions-item>
                    <el-descriptions-item label="涉粉作业人数" v-if="data.is_dust_explosion=='是'">
                        {{data.dust_work_person}}
                    </el-descriptions-item>


                    <el-descriptions-item :span="3">
                        <template slot="label">
                            <el-tag type="info" effect="dark" style="font-size: 15px;display: block;">涉氨制冷</el-tag>
                        </template>
                    </el-descriptions-item>
                    <el-descriptions-item label="是否涉氨制冷" :span="3">
                        {{data.is_ammonia_cold}}
                    </el-descriptions-item>
                    <el-descriptions-item label="液氨的用途" v-if="data.is_ammonia_cold=='是'">
                        {{data.ammonia_use}}
                    </el-descriptions-item>
                    <el-descriptions-item label="液氨使用量" v-if="data.is_ammonia_cold=='是'">
                        {{data.ammonia_usage}}t/a
                    </el-descriptions-item>
                    <el-descriptions-item label="液氨储存方式" v-if="data.is_ammonia_cold=='是'">
                        {{data.ammonia_storage_method}}
                    </el-descriptions-item>
                    <el-descriptions-item label="液氨储存量" v-if="data.is_ammonia_cold=='是'">
                        {{data.ammonia_storage_capacity}}t
                    </el-descriptions-item>
                    <el-descriptions-item label="气体泄露报警装置数" :span="2" v-if="data.is_ammonia_cold=='是'">
                        {{data.gas_alarm_number}}
                    </el-descriptions-item>


                    <el-descriptions-item :span="3">
                        <template slot="label">
                            <el-tag type="info" effect="dark" style="font-size: 15px;display: block;">涉高温熔融金属</el-tag>
                        </template>
                    </el-descriptions-item>
                    <el-descriptions-item label="是否涉高温熔融金属" :span="3">
                        {{data.is_hot_melting}}
                    </el-descriptions-item>

                    <el-descriptions-item label="高炉数量" :span="1" v-if="data.is_hot_melting=='是'">
                        {{data.blast_furnace_number}}
                    </el-descriptions-item>
                    <el-descriptions-item label="熔炼炉" :span="2" v-if="data.is_hot_melting=='是'">
                        <span style="width: 120px;display: inline-block">{{data.melting_furnace1}}</span> 数量: {{data.melting_furnace_number1}}
                    </el-descriptions-item>
                    <el-descriptions-item label="有色金属冶炼炉数量" :span="1" v-if="data.is_hot_melting=='是'">
                        {{data.nonferrous_furnace_number}}
                    </el-descriptions-item>
                    <el-descriptions-item label="熔炼炉" :span="2" v-if="data.is_hot_melting=='是'">
                        <span style="width: 120px;display: inline-block">{{data.melting_furnace2}}</span> 数量: {{data.melting_furnace_number2}}
                    </el-descriptions-item>
                    <el-descriptions-item label="铁合金矿热炉数量" :span="1" v-if="data.is_hot_melting=='是'">
                        {{data.ferroalloy_furnace_number}}
                    </el-descriptions-item>
                    <el-descriptions-item label="熔炼炉" :span="2" v-if="data.is_hot_melting=='是'">
                        <span style="width: 120px;display: inline-block">{{data.melting_furnace3}}</span> 数量: {{data.melting_furnace_number3}}
                    </el-descriptions-item>
                    <el-descriptions-item label="冲天炉数量" :span="1" v-if="data.is_hot_melting=='是'">
                        {{data.soaring_furnace_number}}
                    </el-descriptions-item>
                    <el-descriptions-item label="熔炼炉" :span="2" v-if="data.is_hot_melting=='是'">
                        <span style="width: 120px;display: inline-block">{{data.melting_furnace4}}</span> 数量: {{data.melting_furnace_number4}}
                    </el-descriptions-item>



                    <el-descriptions-item :span="3">
                        <template slot="label">
                            <el-tag type="info" effect="dark" style="font-size: 15px;display: block;">涉轻工行业有限空间</el-tag>
                        </template>
                    </el-descriptions-item>
                    <el-descriptions-item label="涉轻工行业有限空间" :span="3">
                        {{data.is_light_industry}}
                    </el-descriptions-item>
                    <template v-for="(limited,key) in data.limited_list">
                        <el-descriptions-item label="有限空间类型" v-if="data.is_light_industry=='是'" :span="2">
                            {{limited.name}}
                        </el-descriptions-item>
                        <el-descriptions-item label="数量" v-if="data.is_light_industry=='是'" :span="1">
                            {{limited.param_value}}
                        </el-descriptions-item>
                    </template>

                </el-descriptions>
            </el-tab-pane>
            <el-tab-pane label="证书信息">
                <el-table border
                          v-loading="loading"
                          :data="data.calist"
                          style="width: 100%;margin-bottom: 20px;"
                          ref="qtable"
                          :height="height"
                          size="small">
                    <el-table-column
                            prop="company_name"
                            label="企业名称"
                            align="center"
                            show-overflow-tooltip
                            min-width="200">
                    </el-table-column>
                    <el-table-column
                            prop="industry"
                            label="行业/专业"
                            align="center"
                            width="200">
                        <template slot-scope="scope">
                            {{scope.row.industry}}/{{scope.row.specialty}}
                        </template>
                    </el-table-column>
                    <el-table-column
                            prop="code"
                            label="证书编号"
                            align="center"
                            width="200">
                    </el-table-column>
                    <el-table-column
                            prop="level"
                            label="证书等级"
                            align="center"
                            width="80">
                    </el-table-column>
                    <el-table-column
                            prop="date"
                            label="证书有效期"
                            align="center"
                            min-width="100">
                    </el-table-column>
                </el-table>
            </el-tab-pane>
            <el-tab-pane label="运行资料">
                <el-row :gutter="20">
                    <el-col style="width:150px;color:#606266;text-align:right;line-height:30px;">要素选择</el-col>
                    <el-col style="width:500px;">
                        <el-cascader style="width:100%;" size="small" v-model="element_id" :props="{value:'id',label:'name',checkStrictly: true}" :options="elements" @change="handleChange"></el-cascader>
                    </el-col>
                </el-row>
                <el-divider style="margin:10px 0;"></el-divider>
                <el-row :gutter="20">
                    <el-col :span="16" :style="{overflow: 'auto',height:height+'px'}">
                        <el-form v-if="reviewdata" v-model="reviewdata" label-width="150px">
                            <el-form-item label="基本规范要求">
                                <div v-html="reviewdata.ask"></div>
                            </el-form-item>
                            <el-form-item label="企业达标标准">
                                <div v-html="reviewdata.standards"></div>
                            </el-form-item>
                            <el-form-item label="填报周期">
                                <div>{{reviewdata.cycle}}</div>
                            </el-form-item>
                            <el-form-item label="上报记录">
                                <el-table border
                                          v-loading="loading"
                                          :data="reviewdata.list"
                                          style="width: 100%;margin-bottom: 20px;"
                                          size="small">
                                    <el-table-column
                                            type="index"
                                            label="序号"
                                            align="center"
                                            width="50">
                                    </el-table-column>
                                    <el-table-column
                                            prop="time"
                                            label="上报时间"
                                            align="center"
                                            width="100">
                                    </el-table-column>
                                    <el-table-column
                                            prop="sub_files"
                                            label="上报材料"
                                            align="center"
                                            show-overflow-tooltip
                                            min-width="100">
                                        <template slot-scope="scope">
                                            <el-upload
                                                    v-if="scope.row.edit"
                                                    action="upload"
                                                    :file-list="scope.row.mb_sub_files"
                                                    limit="10"
                                                    :on-success="(response,file,fileList)=>uploadSuccess(response,file,fileList,scope.row)"
                                                    :before-upload="uploadBefore"
                                                    :on-remove="(file,fileList)=>handleRemove(file,fileList,scope.row)">
                                                <el-button size="small" type="primary">点击上传</el-button>
                                                <!--                                <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>-->
                                            </el-upload>
                                            <div v-if="!scope.row.edit" v-for="v in scope.row.mb_sub_files">
                                                <el-image v-if="v.ext=='jpg'||v.ext=='png'||v.ext=='JPG'||v.ext=='PNG'" style="width:60px;height: 60px;border: 1px solid #999;"
                                                          :title="v.name"
                                                          :src="v.url"
                                                          :preview-src-list="[v.url]"></el-image>
                                                <el-link style="color: #2c89ff;" v-else :href="v.url" target="_blank">{{v.name}}</el-link>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                            prop="hazard"
                                            label="运行检查"
                                            align="center"
                                            show-overflow-tooltip
                                            min-width="100">
                                        <template slot-scope="scope">
                                            <el-input v-if="scope.row.edit" type="textarea" v-model="scope.row.hazard"></el-input>
                                            <div v-if="!scope.row.edit">{{scope.row.hazard}}</div>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </el-form-item>
                        </el-form>
                    </el-col>
                    <el-col :span="8" :style="{overflow: 'auto',height:height+'px'}">
                        <el-col v-for="(item,key) in review" style="width:50px;">
                            <el-tooltip effect="dark" placement="top">
                                <div slot="content" v-html="item.ask"></div>
                                <div :class="(key+1)==index?'item checked':'item'" @click="change(key)">{{key+1}}</div>
                            </el-tooltip>
                        </el-col>
                    </el-col>
                </el-row>
            </el-tab-pane>
            <el-tab-pane label="教育培训">

                <el-table border
                          :data="trainingData"
                          style="width: 100%;margin-bottom: 20px;"
                          :height="height"
                          size="small">
                    <el-table-column
                            type="index"
                            label="序号"
                            align="center"
                            width="50">
                    </el-table-column>
                    <el-table-column
                            prop="name"
                            label="姓名"
                            align="center"
                            width="100">
                    </el-table-column>
                    <el-table-column
                            prop="job"
                            label="职务"
                            align="center"
                            min-width="100">
                    </el-table-column>
                    <el-table-column
                            prop="grant_date"
                            label="证书发放时间"
                            align="center"
                            min-width="150">
                    </el-table-column>
                    <el-table-column
                            prop="validity"
                            label="有效期"
                            align="center"
                            show-overflow-tooltip
                            min-width="200">
                        <template slot-scope="scope">
                            {{scope.row.validity_start}} 至 {{scope.row.validity_end}}
                        </template>
                    </el-table-column>
                    <el-table-column
                            prop="year_review_time"
                            label="年审时间"
                            align="center"
                            min-width="150">
                    </el-table-column>
                    <el-table-column
                            prop="year_review_info"
                            label="年审记录"
                            align="center"
                            min-width="200">
                    </el-table-column>
                    <el-table-column
                            prop="file"
                            label="证书扫描件"
                            align="left"
                            min-width="200">
                        <template slot-scope="scope">
                            <div v-for="item in scope.row.files">
                                <el-link type="primary" :href="item.url" target="_blank">{{item.name}}</el-link>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>

            </el-tab-pane>
            <el-tab-pane label="特殊作业许可管理">

                <el-table border
                          :data="specialWorkData"
                          style="width: 100%;margin-bottom: 20px;"
                          ref="qtable"
                          :height="height"
                          size="small">
                    <el-table-column
                            type="index"
                            label="序号"
                            align="center"
                            width="50">
                    </el-table-column>
                    <el-table-column
                            prop="work_item"
                            label="作业项目"
                            align="center"
                            min-width="240">
                    </el-table-column>
                    <el-table-column
                            prop="work_time"
                            label="作业时间段"
                            align="center"
                            show-overflow-tooltip
                            min-width="200">
                        <template slot-scope="scope">
                            {{scope.row.work_time_start}} 至 {{scope.row.work_time_end}}
                        </template>
                    </el-table-column>
                    <el-table-column
                            prop="work_place"
                            label="作业地点"
                            align="center"
                            min-width="150">
                    </el-table-column>
                    <el-table-column
                            prop="work_risk"
                            label="作业风险"
                            align="center"
                            min-width="150">
                    </el-table-column>
                    <el-table-column
                            prop="examin_user"
                            label="审批人"
                            align="center"
                            show-overflow-tooltip
                            min-width="150">
                    </el-table-column>
                    <el-table-column
                            prop="work_permit"
                            label="证书扫描件"
                            align="left"
                            min-width="200">
                        <template slot-scope="scope">
                            <div v-for="item in scope.row.files">
                                <el-link type="primary" :href="item.url" target="_blank">{{item.name}}</el-link>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>

            </el-tab-pane>
            <el-tab-pane label="自评报告">

                <el-table border
                          :data="selfEvaluationData"
                          style="width: 100%;margin-bottom: 20px;"
                          :height="height"
                          size="small">
                    <el-table-column
                            type="index"
                            label="序号"
                            align="center"
                            width="60">
                    </el-table-column>
                    <el-table-column
                            prop="year"
                            label="年度"
                            align="center"
                            width="80">
                    </el-table-column>
                    <el-table-column
                            prop="company_name"
                            label="企业名称"
                            align="center"
                            show-overflow-tooltip
                            min-width="200">
                    </el-table-column>
                    <el-table-column
                            prop="industry"
                            label="行业/专业"
                            align="center"
                            width="200">
                        <template slot-scope="scope">
                            {{scope.row.industry}}/{{scope.row.specialty}}
                        </template>
                    </el-table-column>
                    <el-table-column
                            prop="date"
                            label="自评日期"
                            align="center"
                            width="120">
                    </el-table-column>
                    <el-table-column
                            prop="score.total.score"
                            label="自评得分"
                            align="center"
                            width="100">
                    </el-table-column>
                    <el-table-column
                            label="操作"
                            align="center"
                            width="150">
                        <template slot-scope="scope">
                            <el-button v-if="scope.row.status>=2" type="primary" @click="preview(scope.row.files)" size="small">自评报告</el-button>
                        </template>
                    </el-table-column>
                </el-table>

            </el-tab-pane>
        </el-tabs>
    </el-container>
    <preview ref="preview"></preview>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                title: '',
                index: 1,
                element_id: [{$element_id}],
                elements: [],
                loading: false,
                data: {},
                calist: [],
                review: [],
                specialWorkData: [],
                trainingData: [],
                selfEvaluationData: [],
                reviewdata: {
                    ask:'',
                    standards:'',
                    cycle:'',
                    list:[],
                },
                height: document.documentElement.clientHeight-200,
            };
        },
        components: {
            'preview': 'url:/general/toppingsoft/public/vue/preview.vue',
        },
        methods: {
            handleChange(){
                this.getReview();
            },
            //数据加载
            getData() {
                var loading = this.$loading();
                var _this = this;
                var param = {};
                param.id = '{$id}';
                param._ajax = 1;
                axios.post('companyInfo', param).then(function (res) {
                    if (res.data.code == 0) {
                        _this.data = res.data.data;
                        _this.trainingData = _this.data.trainingData;
                        _this.specialWorkData = _this.data.specialWorkData;
                        _this.selfEvaluationData = _this.data.selfEvaluationData;
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                    loading.close();
                }).catch(function (error) {
                    console.log(error);
                });
            },
            //数据加载
            getReview() {
                var _this = this;
                var param = {};
                param.element_id = _this.element_id;
                param._ajax = 1;
                var loading = this.$loading();
                axios.post('getReview', param).then(function (res) {
                    if (res.data.code == 0) {
                        _this.index = 1;
                        _this.review = res.data.data.content;
                        _this.reviewdata = res.data.data.content[0];
                        _this.elements = res.data.data.element;
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                    loading.close();
                }).catch(function (error) {
                    console.log(error);
                });
            },
            preview: function (file) {
              file = file.response?file.response.data:file;
              this.$refs.preview.open(file.url,file.name);
            },
            change(key) {
                this.index = key+1;
                this.reviewdata = this.review[key];
            },
        },
        mounted() {
            this.getData();
            this.getReview();
            // console.log(this.textContent('2024-05-02'));
        }
    })
</script>


</body>
</html>