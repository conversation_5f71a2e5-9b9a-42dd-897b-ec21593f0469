<?php

namespace app\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use hg\apidoc\annotation as Apidoc;
use app\BaseController;
use app\model\FileModel;
use app\model\ExcelModel;
use think\facade\Db;

/**
 * @Apidoc\Title("文件管理示例参考")
 * @Apidoc\Group("Demo")
 * @Apidoc\Sort(1)
 */
class File {

    /**
     * @Apidoc\Title("查看文件")
     * @Apidoc\Param("file", type="string",default="", desc="文件")
     * @Apidoc\Param("model", type="string",default="", desc="上传模块区分{为空则默认为：控制器名_方法名}")
     * @Apidoc\Returned("id", type="object", desc="文件id")
     * @Apidoc\Returned("filepath", type="int", desc="文件路径")
     */
    public function info($code='') {
        $re = Db::table('top_files')->where(['code'=>$code])->find();
        if($re['formal']==1){
            $filePath = get_oa_attach().'/'.$re['filepath'];
        }else{
            $filePath = config('filesystem.disks.oa.root').'/'.$re['filepath'];
        }
        if (file_exists($filePath)) {
            // 发送HTTP头部
            header('Content-Type: ' . $re['filetype']);
            header('Content-Length: ' . filesize($filePath));
            // 修复：简化文件名处理逻辑，移除双重编码
            $filename = rawurlencode($re['filename']);
            header("Content-Disposition: inline; filename=\"$filename\"; filename*=UTF-8''$filename");
            // 输出文件内容
            readfile($filePath);
            exit;
        } else {
            // 文件不存在的处理逻辑
            echo '文件不存在。';
        }
    }

    /**
     * @Apidoc\Title("下载文件")
     * @Apidoc\Param("file", type="string",default="", desc="文件")
     * @Apidoc\Param("model", type="string",default="", desc="上传模块区分{为空则默认为：控制器名_方法名}")
     * @Apidoc\Returned("id", type="object", desc="文件id")
     * @Apidoc\Returned("filepath", type="int", desc="文件路径")
     */
    public function down($code='') {
        $re = Db::table('top_files')->where(['code'=>$code])->find();
        if($re['formal']==1){
            $filePath = get_oa_attach().'/'.$re['filepath'];
        }else{
            $filePath = config('filesystem.disks.oa.root').'/'.$re['filepath'];
        }
        if (file_exists($filePath)) {
            // 发送HTTP头部
            header('Content-Type: ' . $re['filetype']);
            header('Content-Length: ' . filesize($filePath));
            // 修复：保持与info方法一致的编码逻辑
            $filename = rawurlencode($re['filename']);
            header("Content-Disposition: attachment; filename=\"$filename\"; filename*=UTF-8''$filename");
            // 输出文件内容
            readfile($filePath);
            exit;
        } else {
            // 文件不存在的处理逻辑
            echo '文件不存在。';
        }
    }

    /**
     * @Apidoc\Title("上传文件到临时文件夹")
     * @Apidoc\Param("file", type="string",default="", desc="文件")
     * @Apidoc\Param("model", type="string",default="", desc="上传模块区分{为空则默认为：控制器名_方法名}")
     * @Apidoc\Returned("id", type="object", desc="文件id")
     * @Apidoc\Returned("filepath", type="int", desc="文件路径")
     */
    public function upload($model='') {
        echo '接口禁用';
        $file = request()->file('file');
        $result = FileModel::upload($file,$model);
        result($result);
    }

    /**
     * @Apidoc\Title("保存文件到正式文件夹")
     * @Apidoc\Param("file", type="string",default="", desc="文件id或文件路径")
     * @Apidoc\Param("folder", type="string",default="", desc="正式文件夹目录")
     * @Apidoc\Param("filename", type="string",default="", desc="指定文件名，为空则随机生成")
     * @Apidoc\Returned("id", type="object", desc="文件id")
     * @Apidoc\Returned("filepath", type="int", desc="文件路径")
     */
    public function saveFile() {
        echo '接口禁用';
        $file = '/storage/tmp/20220523/123.png';
        $folder = 'Userinfo/avatar';
        $filename = '510000';
        $result = FileModel::saveFile($file,$folder,$filename);
        result($result);
    }


}
