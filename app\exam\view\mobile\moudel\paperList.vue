<template>
  <el-row>
    <div class="examinationHeader">
      成都市企业安全生产标准化知识测评
      <div class="countdown">剩余时间：{{ minutes }}分{{ seconds }}秒</div>
    </div>
    <div class="examinationSection">
      <div class="examinationItem" v-for="(item,index) in testList" key="index">
        <div class="examinationItemLabel">{{ index + 1 }}、{{ item.test_title }}</div>
        <div class="examinationRadioBox">
          <el-radio-group v-model="item.my_answer">
            <template v-for="(option,index) in item.test_option">
              <el-radio :label="index">{{ index }}、
                {{ option }}
              </el-radio>
            </template>

          </el-radio-group>
        </div>
      </div>
    </div>
    <div class="submitBtn">
      <el-button type="primary" @click="submitForm()">提交试卷</el-button>
    </div>
  </el-row>
</template>

<script>
module.exports = {
  name: "companyInfo",
  // 模板导入区
  components: {
    'check-view': 'url:../../../../../app/work/view/check/moudel/check.vue?v=1',
    'work': 'url:../../../../../app/work/view/work/moudel/wrokContent.vue?v=1',
  },
  props: {
    signName: {
      type: String,
      default: '',
    },
    taskId: {
      type: String,
      default: '',
    },
    companyName: {
      type: String,
      default: '',
    },
    industryId: {
      type: String,
      default: '',
    },
    paperId: {
      type: String,
      default: '',
    },
  },
  data: function () {
    return {
      sign: this.signName,
      task_id: this.taskId,
      company_name: this.companyName,
      industry_id: this.industryId,
      paper_id: this.paperId,
      position_name: "",
      user_name: "",
      countdownTime: 30 * 60, // 倒计时30分钟，转换为秒
      intervalId: null,
      form: {},
      list: [//题目list
        {
          id: '12132',
          checked: '',
          name: '题干16389902738是什么什么什么',
          listItem: [
            {
              name: '选项A',
              value: '1'
            },
            {
              name: '选项B',
              value: '2'
            },
            {
              name: '选项C',
              value: '3'
            },
            {
              name: '选项D',
              value: '4'
            },
          ]
        },
        {
          id: '121323',
          checked: '',
          name: '题干16389902738是什么什么什么',
          listItem: [
            {
              name: '选项A',
              value: '1'
            },
            {
              name: '选项B',
              value: '2'
            },
            {
              name: '选项C',
              value: '3'
            },
            {
              name: '选项D',
              value: '4'
            },
          ]
        },
        {
          id: '121324',
          checked: '',
          name: '题干16389902738是什么什么什么',
          listItem: [
            {
              name: '选项A',
              value: '1'
            },
            {
              name: '选项B',
              value: '2'
            },
            {
              name: '选项C',
              value: '3'
            },
            {
              name: '选项D',
              value: '4'
            },
          ]
        },
        {
          id: '121325',
          checked: '',
          name: '题干16389902738是什么什么什么',
          listItem: [
            {
              name: '选项A',
              value: '1'
            },
            {
              name: '选项B',
              value: '2'
            },
            {
              name: '选项C',
              value: '3'
            },
            {
              name: '选项D',
              value: '4'
            },
          ]
        },
        {
          id: '1213251',
          checked: '',
          name: '题干16389902738是什么什么什么',
          listItem: [
            {
              name: '对',
              value: '1'
            },
            {
              name: '错',
              value: '2'
            },
          ]
        },
        {
          id: '12132512',
          checked: '',
          name: '题干16389902738是什么什么什么',
          listItem: [
            {
              name: '对',
              value: '1'
            },
            {
              name: '错',
              value: '2'
            },
          ]
        },
      ],
      testList: [],
    };
  },
  watch: {
    paper_id() {
      this.getOnePaper();
    },
  },
  filters: {
    capitalize(value) {
      let letter = '';
      switch (value) {
        case 0:
          letter = "A";
          break;
        case 1:
          letter = "B";
          break;
        case 2:
          letter = "C";
          break;
        case 3:
          letter = "D";
          break;
      }
      return letter;
    }
  },
  mounted() {
    this.getOnePaper();
  },
  computed: {

    minutes() {
      return Math.floor(this.countdownTime / 60);
    },
    seconds() {
      return this.countdownTime % 60 < 10 ? '0' + Math.floor(this.countdownTime % 60) : Math.floor(this.countdownTime % 60);
    },

  },
  created() {
    this.intervalId = setInterval(this.updateCountdown, 1000);
  },
  beforeDestroy() {
    clearInterval(this.intervalId);
  },
  methods: {
    /*倒计时*/
    updateCountdown() {
      let that = this;
      if (this.countdownTime > 0) {
        this.countdownTime--;
      } else {
        clearInterval(that.intervalId);
        that.submitAnswer();//倒计时结束自动提交试卷
      }
    },
    /*提交试卷*/
    submitForm() {
      this.$confirm('提交试卷，即刻查看考试成绩是否继续？', '考试提醒', {
        confirmButtonText: '提交试卷',
        cancelButtonText: '继续答题',
        showClose: false,
        distinguishCancelAndClose: false,
        type: 'warning'
      }).then(() => {
        console.log(123123)
        this.submitAnswer()
      }).catch(() => {

      });
    },
    getOnePaper() {
      var vm = this;
      vm.loading = true;
      var url = "../paper/getOnePaperInfo";
      axios.post(url, {
        paper_id: vm.paper_id,
        sign: vm.sign,
        industry_id: vm.industry_id,
        position_name: vm.position_name,
        user_name: vm.user_name,
      }).then(function (res) {
        vm.loading = false;
        if (res.data.code == 1000) {
          vm.testList = res.data.data.test_list;
        } else {
          vm.$message({
            message: res.data.msg,
            type: res.data.type
          });
        }
      }).catch(function (error) {
        vm.loading = false;

      });


    },
    submitAnswer() {
      var vm = this;
      vm.loading = true;
      var url = "../paper/submitOnePaperInfo";
      axios.post(url, {
        paper_id: vm.paper_id,
        paper_list: vm.testList,
      }).then(function (res) {
        vm.loading = false;
        if (res.data.code == 1000) {
          vm.$emit('ok', res.data.data)
        } else {
          vm.$message({
            message: res.data.msg,
            type: res.data.type
          });
        }
      }).catch(function (error) {
        vm.loading = false;

      });
    },
  },
}
</script>
<style scoped>
/*修改表单下内边距*/
.el-form-item--mini.el-form-item {
  margin-bottom: 0px;
}


</style>
