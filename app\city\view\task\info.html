<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>评审任务</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/company.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .my-title { font-weight: 700;line-height: 30px; margin-top: 20px;}
        .my-content { text-indent: 30px;line-height: 40px;}
        .my-files { margin-left: 30px;line-height: 40px; overflow: hidden;}
        .my-files label { float: left;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">
        <el-row :gutter="20">
            <el-col>
                <el-descriptions class="margin-top" title="现场评审" column="3" border>
                    <el-descriptions-item :labelStyle="{'width':'100px'}">
                        <template slot="label">
                            <i class="el-icon-office-building"></i>
                            单位名称
                        </template>
                        {$grading.company_name}
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template slot="label">
                            <i class="el-icon-office-building"></i>
                            行业/专业
                        </template>
                        {$grading.industry}/{$grading.specialty}
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template slot="label">
                            <i class="el-icon-office-building"></i>
                            申请等级
                        </template>
                        {$grading.level}
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template slot="label">
                            <i class="el-icon-user"></i>
                            法定代表人
                        </template>
                        {$grading.legal}
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template slot="label">
                            <i class="el-icon-mobile-phone"></i>
                            手机号
                        </template>
                        {$grading.legal_mobile}
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template slot="label">
                            <i class="el-icon-mobile-phone"></i>
                            邮箱
                        </template>
                        {$grading.legal_email}
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template slot="label">
                            <i class="el-icon-user"></i>
                            安全管理联系人
                        </template>
                        {$grading.manager}
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template slot="label">
                            <i class="el-icon-mobile-phone"></i>
                            手机号
                        </template>
                        {$grading.manager_mobile}
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template slot="label">
                            <i class="el-icon-mobile-phone"></i>
                            邮箱
                        </template>
                        {$grading.manager_email}
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template slot="label">
                            <i class="el-icon-mobile-phone"></i>
                            状态
                        </template>
                        <el-tag v-if="'{$task.status}'==5" type="danger">终止评审</el-tag>
                        <el-tag v-if="'{$task.status}'==7" type="success">等待现场评审</el-tag>
                        <el-tag v-if="'{$task.status}'==8" type="warning">现场评审中</el-tag>
                        <el-tag v-if="'{$task.status}'==9" type="info">结束评审</el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template slot="label">
                            <i class="el-icon-location-outline"></i>
                            单位地址
                        </template>
                        {$grading.address}
                    </el-descriptions-item>
                </el-descriptions>
            </el-col>
            <el-col>
                <el-steps :active="active" finish-status="" simple style="margin-top: 20px">
                    <el-step style="cursor: pointer;" title="首次会议" @click.native="on_click(0)"></el-step>
                    <el-step style="cursor: pointer;" title="现场评审" @click.native="on_click(1)"></el-step>
                    <el-step style="cursor: pointer;" title="末次会议" @click.native="on_click(2)"></el-step>
                </el-steps>
            </el-col>
            <el-col v-for="(item,key) in review_flow" v-show="key == active">
                <template v-if="item.work" v-for="(v,k) in item.work">
                    <div class="my-title">{{k+1}}、{{v.title}}
                        <el-button v-if="v.type=='pinfen'" type="primary" @click="pingshen" size="mini">评审报告</el-button>
                        <el-button v-if="v.type=='exam'" type="primary" @click="exam" size="mini">考试记录</el-button>
                        <el-button v-if="v.status==2" type="success" disabled size="mini">已完成</el-button></div>
                    <div class="my-content">{{v.content}}</div>
                    <div v-if="v.type=='yiyi'">
                        <el-form style="margin-top:15px;">
                            <el-form-item label="">
                                <div class="my-content">{{v.remark}}</div>
                            </el-form-item>
                        </el-form>
                    </div>
                    <div v-if="v.type=='wenjuan'">
                        <el-row>
                            <el-col style="width:150px;">
                                <el-image
                                        style="width: 100px; height: 100px;margin-left:30px;background-color:#f5f7fa;text-align:center;line-height:100px;"
                                        :src="mydcodeimg"
                                        :preview-src-list="[mydcodeimg]">
                                    <div slot="error" class="image-slot">
                                        <i class="el-icon-picture-outline"></i>
                                    </div>
                                </el-image>
                                <br/>
                                <p style="text-align:center;">满意度调查问卷</p>
                            </el-col>
                        </el-row>
                    </div>
                    <div v-if="v.type=='pinfen'||v.type=='fengong'">
                        <el-table
                                :data="review_list"
                                border
                                style="width: 550px;">
                            <el-table-column
                                    prop="element_name"
                                    label="要素"
                                    align="center"
                                    min-width="180">
                            </el-table-column>
                            <el-table-column
                                    prop="expert_name"
                                    label="评审专家"
                                    align="center"
                                    width="120">
                            </el-table-column>
                            <el-table-column
                                    v-if="v.type=='pinfen'"
                                    prop="status"
                                    align="center"
                                    label="状态"
                                    width="120">
                                <template slot-scope="scope">
                                    <el-tag v-if="scope.row.status==1" type="danger">未完成打分</el-tag>
                                    <el-tag v-if="scope.row.status==2" type="success">已完成打分</el-tag>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                    <div v-if="v.files" v-for="v1 in v.files" class="my-files">
                        <label>{{v1.name}}：</label>
                        <el-link v-for="file in v1.filelists" @click="preview(file)">{{file.name}}</el-link>
                    </div>
                </template>
                <div style="text-align: center;margin-top: 10px">
                    <el-button v-show="active>0" @click="prev()">上一步</el-button>
                    <el-button v-show="active<2" @click="next()">下一步</el-button>
                    <el-button @click="location.href = 'index'">返回</el-button>
                </div>
            </el-col>
        </el-row>
        <el-dialog :visible.sync="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt="">
        </el-dialog>
        <pingshen ref="pingshen" @ok="index"></pingshen>
        <exam ref="exam" @ok="index"></exam>
        <preview ref="preview"></preview>
    </div>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script>
    Vue.use(httpVueLoader);
    new Vue({
        el: '#app',
        data: function () {
            return{
                active:0,
                dialogImageUrl:'',
                dialogVisible:false,
                review_flow:[],
                review_list:[],
                form: {
                    'company_name':''
                },
                loading: false,
                examcodeimg:'',
                mydcodeimg:'{$mydcodeimg}',
                files: [],
                single:true,
                multiple:true,
                data: [],
                tableHeight:document.documentElement.clientHeight-250,
            }
        },
        components:{
            'pingshen':'url:/general/toppingsoft/app/city/view/task/vue/pingshen.vue?v=1',
            'exam':'url:/general/toppingsoft/app/city/view/task/vue/exam.vue?v=1',
            'preview': 'url:/general/toppingsoft/public/vue/preview.vue',
        },
        methods: {
            on_click(e){
                if(e != "" || e != null){ this.active = e }
            },
            prev(){
                this.active--;
            },
            next(){
                this.active++;
            },
            index(){
                var _this = this;
                _this.loading = true
                axios.post('info',{id:'{$task.id}',_ajax:1}).then(res => {
                    _this.loading = false
                    if(res.data.code === 0){
                        _this.review_flow = res.data.data.review_work;
                        _this.review_list = res.data.data.review_list;
                    }else{
                        _this.$message.error(res.data.msg);
                    }
                })
            },
            preview: function (file) {
                file = file.response?file.response.data:file;
                this.$refs.preview.open(file.url,file.name);
            },
            pingshen() {
                this.$refs.pingshen.open('{$task.id}');
            },
            exam() {
                this.$refs.exam.open('{$task.id}');
            },
            success(id,type) {
                if(type=='yiyi'){
                    this.$confirm('确定该项工作已完成?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.loading = true;
                        axios.post('worksuccess',{id:id.id,remark:id.remark}).then(res => {
                            this.loading = false
                            if(res.data.code === 0){
                                this.index();
                            }else{
                                this.$message.error(res.data.msg);
                            }
                        })
                    }).catch(() => {
                    });
                }else{
                    this.$confirm('确定该项工作已完成?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.loading = true;
                        axios.post('worksuccess',{id:id}).then(res => {
                            this.loading = false
                            if(res.data.code === 0){
                                this.index();
                            }else{
                                this.$message.error(res.data.msg);
                            }
                        })
                    }).catch(() => {
                    });
                }
            },
            examupdate() {
                var _this = this;
                _this.loading = true;
                axios.post('examupdate',{id:'{$task.id}',industry:"{$grading.specialty}"}).then(res => {
                    _this.loading = false
                    if(res.data.code === 0){
                        _this.examcodeimg = res.data.data.img;
                    }else{
                        _this.$message.error(res.data.msg);
                    }
                })
            },
            examend() {
                var _this = this;
                _this.loading = true;
                axios.post('examend',{id:'{$task.id}'}).then(res => {
                    _this.loading = false
                    if(res.data.code === 0){
                        _this.examcodeimg = '';
                    }else{
                        _this.$message.error(res.data.msg);
                    }
                })
            },
            info : function(row) {
                this.$refs.info.open(row);
            },
            selection(selection) {
                this.ids = selection.map(item => item.id)
                this.single = selection.length !== 1
                this.multiple = !selection.length
            },
            //计算table高度(动态设置table高度)
            getTableHeight() {
                let tableH = 250; //距离页面下方的高度
                let tempHeight = window.innerHeight - tableH;
                if (tempHeight <= 250) {
                    this.tableHeight =250;
                }
                this.tableHeight = tempHeight;
            },
        },
        mounted(){
            //挂载window.onresize事件(动态设置table高度)
            let _this = this;
            window.onresize = () => {
                if (_this.resizeFlag) {
                    clearTimeout(_this.resizeFlag);
                }
                _this.resizeFlag = setTimeout(() => {
                    _this.getTableHeight();
                    _this.resizeFlag = null;
                }, 1000);
            };
            _this.index()
        },
    })
</script>


</body>
</html>