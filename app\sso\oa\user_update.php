<?php
$BYNAME = '';
$USER_NAME = '';
$USER_PRIV = '';
$PRIV_NAME0 = '';
$PRIV_ID = '';
$PRIV_NAME = '';
$LEAVE_DEPT = 0;
$DEPT_ID = 0;
$DEPT_ID_OTHER = '';
$DEPT_NAME_OTHER = '';
$USER_NO = 10;
$POST_PRIV = 0;
$TO_ID = '';
$TO_NAME = '';
$NOT_LOGIN = 0;
$NOT_MOBILE_LOGIN = 0;
$IM_RANGE = 1;
$PRIV_ID1 = '';
$PRIV_NAME1 = '';
$USER_ID1 = '';
$USER_NAME1 = '';
$DEPT_ID_MSG = '';
$DEPT_NAME_MSG = '';
$BIND_IP = '';
$REMARK = '';
$SEX = 0;
$BIRTHDAY = '';
$THEME = 19;
$MOBIL_NO = '';
$EMAIL = '';
$TEL_NO_DEPT = '';
$USER_ID = '';
$UID = '';
$DEPT_ID1 = 0;
$USER_PRIV_TYPE = 0;
$USE_POP3_OLD = 0;
$MOBIL_NO_OLD = 0;

include_once "auth.php";
include_once "inc/session.php";
$PHPSESSID = (isset($_GET["PHPSESSID"]) ? $_GET["PHPSESSID"] : (isset($_POST["PHPSESSID"]) ? $_POST["PHPSESSID"] : ""));

if (preg_match("/^[a-z0-9]{20,32}$/i", $PHPSESSID)) {
    session_id($PHPSESSID);
}

if (stristr($PHP_SELF, "export") || stristr($PHP_SELF, "excel") || stristr($PHP_SELF, "word") || stristr($PHP_SELF, "attach.php") || stristr($PHP_SELF, "download.php") || stristr($PHP_SELF, "down.php")) {
    session_cache_limiter("private, must-revalidate");
}
session_start();
ob_start();
include_once "inc/utility.php";
$SCRIPT_NAME = $_SERVER["SCRIPT_NAME"];
if (0 < MYOA_OFFLINE_TIME_MIN) {
    $LAST_OPERATION_TIME = $_COOKIE["LAST_OPERATION_TIME"];
    if (!stristr($SCRIPT_NAME, "/general/ipanel/") && !stristr($SCRIPT_NAME, "/general/task_center/") && !stristr($SCRIPT_NAME, "/general/mytable/") && !stristr($SCRIPT_NAME, "/general/status_bar/") && !stristr($SCRIPT_NAME, "/general/topbar.php") && !stristr($SCRIPT_NAME, "/inc/")) {
        setcookie("LAST_OPERATION_TIME", time(), 0, "/");
    }
    if (($LAST_OPERATION_TIME != "") && ((MYOA_OFFLINE_TIME_MIN * 60) < (time() - $LAST_OPERATION_TIME))) {
        $sessionid = session_id();
        del_my_online_status($sessionid);
        clear_online_status();
        setcookie("LAST_OPERATION_TIME", "", 1, "/");
        session_unset();
        session_destroy();
    }
}

if (!isset($_SESSION["LOGIN_USER_PRIV"])) {
    $query = "select USER_PRIV from TD_USER where UID='" . $_SESSION["LOGIN_UID"] . "'";
    $cursor = exequery(TD::conn(), $query);

    if ($ROW = mysqli_fetch_array($cursor)) {
        $_SESSION["LOGIN_USER_PRIV"] = $ROW["USER_PRIV"];
    }
}

if (!isset($SESSION_WRITE_CLOSE) || ($SESSION_WRITE_CLOSE != 0)) {
    session_write_close();
}

$USER_FUNC_ID_STR = $_SESSION["LOGIN_FUNC_STR"];

if ($_SESSION["LOGIN_USER_ID"] == "admin") {
    $USER_FUNC_ID_STR .= "32,33,56,";
}

$GZIP_POSTFIX = (MYOA_SUPPORT_GZIP ? ".gz" : "");

if (isset($HTML_PAGE_TITLE)) {
    unset($HTML_PAGE_TITLE);
}
include_once "inc/utility_all.php";
include_once "inc/utility_org.php";
include_once "inc/check_type.php";
include_once "inc/utility_sms1.php";
include_once "inc/td_core.php";
include_once "inc/utility_cache.php";
include_once "inc/utility_user.php";
include_once "inc/utility_secu.php";
include_once "inc/utility_email_audit.php";
include_once "inc/TRedis/TRedis.php";
//include_once "inc/TSecurity/TSecurity.inc.php";
$redis = TRedis::redis();
$secu_arr = check_secure();
$secu = $secu_arr["SWITCH"];
if (($secu == 1) && (get_secure_priv("sys_user_edit") != 1)) {
	//exit(json_encode(array('code' => 1001, 'msg' => iconv('GB2312','UTF-8','无管理权限'))));
}

$HTML_PAGE_TITLE = _("修改用户");
if (!isset($USER_ID) || ($USER_ID == "")) {
	$sql = "select USER_ID from td_user where UID='$UID'";
	$cursor = exequery(TD::conn(), $sql);

	if ($ROW = mysqli_fetch_array($cursor)) {
		$USER_ID = $ROW["USER_ID"];
	}
}

$BIND_IP = str_replace("\r\n", ",", $BIND_IP);
$BIND_IP = str_replace("\n", ",", $BIND_IP);
$BIND_IP = trim($BIND_IP);
$USER_NAME = trim($USER_NAME);
$BYNAME = trim($BYNAME);

if ($USER_ID == "admin") {
	$USER_PRIV = 1;
	$POST_PRIV = 1;
	$NOT_LOGIN = 0;
	$NOT_MOBILE_LOGIN = 0;
}

if ($BYNAME == "") {
	//exit(json_encode(array('code' => 1001, 'msg' => iconv('GB2312','UTF-8','用户名不能为空'))));
}

if ($USER_NAME == "") {
	exit(json_encode(array('code' => 1001, 'msg' => '用户姓名不能为空')));
}

$DEPT_ID = intval($DEPT_ID);
if (($_SESSION["MYOA_IS_GROUP"] == "1") && ($_SESSION["LOGIN_USER_PRIV_TYPE"] != "1")) {
	if ($_SESSION["LOGIN_USER_PRIV_TYPE"] == "0") {
		$query = "SELECT POST_PRIV,POST_DEPT,DEPT_ID,USER_PRIV_OTHER from TD_USER where UID='" . $_SESSION["LOGIN_UID"] . "'";
		$cursor = exequery(TD::conn(), $query, $QUERY_MASTER);

		if ($ROW = mysqli_fetch_array($cursor)) {
			$MY_POST_PRIV = $ROW["POST_PRIV"];
			$MY_POST_DEPT = $ROW["POST_DEPT"];
			$MY_DEPT_ID = $ROW["DEPT_ID"];
			$USER_PRIV_OTHER = $ROW["USER_PRIV_OTHER"];
		}

		if ($MY_POST_PRIV == 0) {
			$dept_str = td_trim(GetUnionSetOfChildDeptId($MY_DEPT_ID));
		} else {
			$DEPT_ID_ALL = get_manage_dept_ids($_SESSION["LOGIN_UID"]);
			$DEPT_ID_ALL = GetUidByOther("", "", $DEPT_ID_ALL);
		}
	}
}

$USER_PRIV = intval($USER_PRIV);

if ($USER_PRIV <= 0) {
	exit(json_encode(array('code' => 1001, 'msg' => '角色无效')));
}

if (substr($BYNAME, -1) == "\\") {
	$BYNAME = substr($BYNAME, 0, -1);
}

if (substr($USER_NAME, -1) == "\\") {
	$USER_NAME = substr($USER_NAME, 0, -1);
}

$USER_NAME = str_replace(array(",", "\\\"", "\'", "\"", "'", "\t", "\\", "\\\\"), array("", "", "", "", "", "", "", ""), $USER_NAME);
$BYNAME = str_replace(array(",", "\\\"", "\'", "\"", "'", "\t", "\\", "\\\\"), array("", "", "", "", "", "", "", ""), $BYNAME);

if (substr($BYNAME, -1) == "\\") {
	$BYNAME = substr($BYNAME, 0, -1);
}

if (substr($USER_NAME, -1) == "\\") {
	$USER_NAME = substr($USER_NAME, 0, -1);
}

$query = "select NOT_LOGIN from TD_USER where UID='$UID'";
$cursor = exequery(TD::conn(), $query);

if ($ROW = mysqli_fetch_array($cursor)) {
	$NOT_LOGIN_OLD = $ROW["NOT_LOGIN"];
}

if (($NOT_LOGIN == 0) && ($NOT_LOGIN_OLD == "1")) {
	login_check("[TDCORE_ADDUSER]", "[TDCORE_ADDUSER]");
}

if (strstr($BYNAME, "\'") != false) {
	exit(json_encode(array('code' => 1001, 'msg' => '用户名中含有非法字符')));
}

if (($BIRTHDAY != "") && !is_date($BIRTHDAY)) {
	exit(json_encode(array('code' => 1001, 'msg' => "生日格式不合法，应形如：") . date("Y-m-d", time())));
}

if ($BYNAME != "") {
	$query = "select * from TD_USER where UID!='$UID' and BYNAME='$BYNAME'";
	$cursor = exequery(TD::conn(), $query);

	if ($ROW = mysqli_fetch_array($cursor)) {
	exit(json_encode(array('code' => 1001, 'msg' => sprintf(_("用户名 %s 已存在"), $BYNAME))));
	}
}

if ($USER_NO == "") {
	$USER_NO = 10;
}

if (!is_number($USER_NO)) {
	exit(json_encode(array('code' => 1001, 'msg' =>'用户排序号应为数字')));
}

if (($secu == 1) && (get_secure_priv("sys_user_ban") == 1) && ($NOT_LOGIN_SECU != "")) {
	$NOT_LOGIN = $NOT_LOGIN_SECU;
}

if ($NOT_VIEW_USER == "on") {
	$NOT_VIEW_USER = 1;
}
else {
	$NOT_VIEW_USER = 0;
}

if ($NOT_VIEW_TABLE == "on") {
	$NOT_VIEW_TABLE = 1;
}
else {
	$NOT_VIEW_TABLE = 0;
}

if ($DEPT_ID == 0) {
	$NOT_MOBILE_LOGIN = 1;
}

if ($MOBIL_NO_HIDDEN == "on") {
	$MOBIL_NO_HIDDEN = "1";
}
else {
	$MOBIL_NO_HIDDEN = "0";
}

if ($USEING_KEY == "on") {
	$USEING_KEY = 1;
}
else {
	$USEING_KEY = 0;
}

if ($USING_FINGER == "on") {
	$USING_FINGER = 1;
}
else {
	$USING_FINGER = 0;
}

if ($IS_LUNAR == "on") {
	$IS_LUNAR = 1;
}
else {
	$IS_LUNAR = 0;
}


if (($MOBIL_NO != "") && ($MOBIL_NO != $MOBIL_NO_OLD)) {
	$pattern = "/^((\d{11})|^((\d{7,8})|(\d{4}|\d{3})-(\d{7,8})|(\d{4}|\d{3})-(\d{7,8})-(\d{4}|\d{3}|\d{2}|\d{1})|(\d{7,8})-(\d{4}|\d{3}|\d{2}|\d{1}))$)/";

	if (!preg_match($pattern, $MOBIL_NO)) {
	exit(json_encode(array('code' => 1001, 'msg' => '手机号码有误！')));
	}
}

$u_priv_str = $USER_PRIV . "," . $PRIV_ID;
$u_dept_str = $DEPT_ID . "," . $DEPT_ID_OTHER;
$u_priv_str = td_trim($u_priv_str);
$u_dept_str = td_trim($u_dept_str);
$u_dept_arr = explode(",", $u_dept_str);
$count = count($u_dept_arr);
$org_str = "";

for ($i = 0; $i < $count; $i++) {
	$org = GetOrgIDByDeptID($u_dept_arr[$i]);

	if ($org != 0) {
		$org_str .= $org . ",";
	}
	else {
		$org = GetWholeIDByDeptID($u_dept_arr[$i]);

		if ($org != 0) {
			$org_str .= $org . ",";
		}
	}
}
$org_str = td_trim($org_str);

if ($DEPT_ID != 0) {
	$sql = "SELECT * FROM user_priv WHERE FIND_IN_SET(USER_PRIV,'$u_priv_str') and PRIV_DEPT_ID!=0";
	$cursor = exequery(TD::conn(), $sql);

	while ($arr = mysqli_fetch_array($cursor)) {
		$PRIV_DEPT_ID = $arr["PRIV_DEPT_ID"];
		$PRIV_TYPE = $arr["PRIV_TYPE"];
		$IS_GLOBAL = $arr["IS_GLOBAL"];
		if (($PRIV_TYPE == 2) || ($IS_GLOBAL == 2)) {
			if (!find_id($org_str, $PRIV_DEPT_ID)) {
	exit(json_encode(array('code' => 1001, 'msg' => '所选的角色与所选部门不匹配！')));
			}
		}

		if (($PRIV_TYPE == 0) && ($IS_GLOBAL == 1)) {
			if (!find_id($u_dept_str, $PRIV_DEPT_ID)) {
	exit(json_encode(array('code' => 1001, 'msg' => '所选的角色与所选部门不匹配！')));
			}
		}
	}
}

if (($PRIV_ID1 != "") || ($USER_ID1 != "") || ($DEPT_ID_MSG != "")) {
	$querys = "select PRIV_ID,USER_ID,DEPT_ID from MODULE_PRIV where UID='$UID' and MODULE_ID='0'";
	$cursors = exequery(TD::conn(), $query);

	if ($ROWS = mysqli_fetch_array($cursors)) {
		$PRIV_ID_OLD = $ROWS["PRIV_ID"];
		$USER_ID_OLD = $ROWS["USER_ID"];
		$DEPT_ID_OLD = $ROWS["DEPT_ID"];
	}
}

$query = "select * from TD_USER where UID='$UID'";
$cursor = exequery(TD::conn(), $query);

if ($ROW = mysqli_fetch_array($cursor)) {
	$BYNAME_OLD = $ROW["BYNAME"];
	$USER_NO_OLD = $ROW["USER_NO"];
	$USER_NAME_OLD = $ROW["USER_NAME"];
	$SEX_OLD = $ROW["SEX"];
	$DEPT_ID_OLD = $ROW["DEPT_ID"];
	$USER_PRIV_OLD = $ROW["USER_PRIV"];
	$IM_RANGE_OLD = $ROW["IM_RANGE"];
	$NOT_LOGIN_OLD = $ROW["NOT_LOGIN"];
	$NOT_VIEW_USER_OLD = $ROW["NOT_VIEW_USER"];
	$NOT_VIEW_TABLE_OLD = $ROW["NOT_VIEW_TABLE"];
	$NOT_MOBILE_LOGIN_OLD = $ROW["NOT_MOBILE_LOGIN"];
	$MOBIL_NO_HIDDEN_OLD = $ROW["MOBIL_NO_HIDDEN"];
	$USEING_KEY_OLD = $ROW["USEING_KEY"];
	$USING_FINGER_OLD = $ROW["USING_FINGER"];
	if (($BYNAME_OLD != $BYNAME) || ($USER_NO_OLD != $USER_NO) || ($USER_NAME_OLD != $USER_NAME) || ($SEX_OLD != $SEX) || ($DEPT_ID_OLD != $DEPT_ID) || ($USER_PRIV_OLD != $USER_PRIV) || ($IM_RANGE_OLD != $IM_RANGE) || ($PRIV_ID_OLD != $PRIV_ID1) || ($USER_ID_OLD != $USER_ID1) || ($DEPT_ID_OLD != $DEPT_ID_MSG)) {
		set_sys_para(array("ORG_UPDATE" => date("Y-m-d H:i:s")));
	}
}

if ($USER_PRIV_OLD != $USER_PRIV) {
	$USER_PRIV_NEW = $USER_PRIV;
}
else {
	$USER_PRIV_NEW = $USER_PRIV_OLD;
}

$query_priv = "SELECT * from USER_PRIV where USER_PRIV='$USER_PRIV_NEW'";
$cursor_priv = exequery(TD::conn(), $query_priv);

if ($ROW_PRIV = mysqli_fetch_array($cursor_priv)) {
	$USER_PRIV_NO = $ROW_PRIV["PRIV_NO"];
	$USER_PRIV_NAME = $ROW_PRIV["PRIV_NAME"];
	$USER_PRIV_TYPE = $ROW_PRIV["PRIV_TYPE"];
}

$USER_NAME_INDEX = getChnprefix($USER_NAME);
$DEPT_ID_OTHER = check_id($DEPT_ID, $DEPT_ID_OTHER, false);
$USER_MANAGE_ORGS = ($_SESSION["MYOA_IS_GROUP"] == "1" ? $USER_MANAGE_ORGS : "");

if ($NOT_LOGIN == "") {
	$NOT_LOGIN = $NOT_LOGIN_OLD;
}
else {
	$NOT_LOGIN = $NOT_LOGIN;
}

if (($DEPT_ID == "0") && ($LEAVE_DEPT != "0")) {
	$USER_ARRAY = array("USER_NAME" => $USER_NAME, "USER_NAME_INDEX" => $USER_NAME_INDEX, "SEX" => $SEX, "DEPT_ID" => $DEPT_ID, "DEPT_ID_OTHER" => $DEPT_ID_OTHER, "LEAVE_DEPT" => $LEAVE_DEPT, "USER_PRIV" => $USER_PRIV, "POST_PRIV" => $POST_PRIV, "POST_DEPT" => $TO_ID, "IM_RANGE" => $IM_RANGE, "USER_PRIV_OTHER" => $PRIV_ID, "USER_NO" => $USER_NO, "NOT_LOGIN" => $NOT_LOGIN, "NOT_VIEW_USER" => $NOT_VIEW_USER, "NOT_VIEW_TABLE" => $NOT_VIEW_TABLE, "NOT_MOBILE_LOGIN" => $NOT_MOBILE_LOGIN, "BYNAME" => $BYNAME, "BIRTHDAY" => $BIRTHDAY, "IS_LUNAR" => $IS_LUNAR, "THEME" => $THEME, "MOBIL_NO" => $MOBIL_NO, "MOBIL_NO_HIDDEN" => $MOBIL_NO_HIDDEN, "BIND_IP" => $BIND_IP, "USEING_KEY" => $USEING_KEY, "REMARK" => $REMARK, "TEL_NO_DEPT" => $TEL_NO_DEPT, "EMAIL" => $EMAIL, "USING_FINGER" => $USING_FINGER, "USER_PRIV_NO" => $USER_PRIV_NO, "USER_PRIV_NAME" => $USER_PRIV_NAME, "USER_MANAGE_ORGS" => $USER_MANAGE_ORGS, "USER_PRIV_TYPE" => $USER_PRIV_TYPE);
	$sql1 = "insert into HR_STAFF_LEAVE(CREATE_USER_ID,CREATE_DEPT_ID,LEAVE_PERSON,LEAVE_DEPT) values ('" . $_SESSION["LOGIN_USER_ID"] . "','" . $_SESSION["LOGIN_DEPT_ID"] . "','$USER_ID','$LEAVE_DEPT')";
	exequery(TD::conn(), $sql1);
}
else {
	$USER_ARRAY = array("USER_NAME" => $USER_NAME, "USER_NAME_INDEX" => $USER_NAME_INDEX, "SEX" => $SEX, "DEPT_ID" => $DEPT_ID, "DEPT_ID_OTHER" => $DEPT_ID_OTHER, "USER_PRIV" => $USER_PRIV, "POST_PRIV" => $POST_PRIV, "POST_DEPT" => $TO_ID, "IM_RANGE" => $IM_RANGE, "USER_PRIV_OTHER" => $PRIV_ID, "USER_NO" => $USER_NO, "NOT_LOGIN" => $NOT_LOGIN, "NOT_VIEW_USER" => $NOT_VIEW_USER, "NOT_VIEW_TABLE" => $NOT_VIEW_TABLE, "NOT_MOBILE_LOGIN" => $NOT_MOBILE_LOGIN, "BYNAME" => $BYNAME, "BIRTHDAY" => $BIRTHDAY, "IS_LUNAR" => $IS_LUNAR, "THEME" => $THEME, "MOBIL_NO" => $MOBIL_NO, "MOBIL_NO_HIDDEN" => $MOBIL_NO_HIDDEN, "BIND_IP" => $BIND_IP, "USEING_KEY" => $USEING_KEY, "REMARK" => $REMARK, "TEL_NO_DEPT" => $TEL_NO_DEPT, "EMAIL" => $EMAIL, "USING_FINGER" => $USING_FINGER, "USER_PRIV_NO" => $USER_PRIV_NO, "USER_PRIV_NAME" => $USER_PRIV_NAME, "USER_MANAGE_ORGS" => $USER_MANAGE_ORGS, "USER_PRIV_TYPE" => $USER_PRIV_TYPE);
}

if (($_SESSION["MYOA_IS_GROUP"] == "1") && ($_SESSION["LOGIN_USER_PRIV_TYPE"] == "1")) {
	$USER_ARRAY["VIEW_DEPT_ID"] = $VIEW_DEPT_ID;
}

$email_audit_flag = check_email_audit(3);
$audit_flag = $email_audit_flag && (intval($SECRET_LEVEL) != intval($SECRET_LEVEL_OLD));

if ($audit_flag) {
	$USER_ARRAY["SECRET_LEVEL"] = $SECRET_LEVEL;
}

set_user($USER_ARRAY, $USER_ID, $UID);

if ($DEPT_ID == "0") {
	$query = "select DISC_GROUP_ID from im_discuss_group";
	$cursor = exequery(TD::conn(), $query);

	while ($ROW = mysqli_fetch_array($cursor)) {
		$DISC_GROUP_ID[] = $ROW["DISC_GROUP_ID"];
	}

	if (!empty($DISC_GROUP_ID)) {
		foreach ($DISC_GROUP_ID as $K ) {
			$query2 = "select DISC_GROUP_UID from im_discuss_group where DISC_GROUP_ID = '$K'";
			$cursor2 = exequery(TD::conn(), $query2);

			if ($ROW2 = mysqli_fetch_array($cursor2)) {
				$DISC_GROUP_UID = td_trim($ROW2["DISC_GROUP_UID"]);
				$DISC_GROUP_UID = explode(",", $DISC_GROUP_UID);

				foreach ($DISC_GROUP_UID as $k => $v ) {
					if ($v == $UID) {
						unset($DISC_GROUP_UID[$k]);
					}
				}

				$DISC_GROUP_UID = implode(",", $DISC_GROUP_UID);
				$DISC_GROUP_UID = td_trim($DISC_GROUP_UID);
				$DISC_GROUP_UID = $DISC_GROUP_UID . ",";
			}

			$query3 = "UPDATE im_discuss_group SET DISC_GROUP_UID = '$DISC_GROUP_UID' where DISC_GROUP_ID='$K'";
			exequery(TD::conn(), $query3);
			$lutime = array("group" => time());
			$redis->hmset("message:info:lutime", $lutime);
		}
	}

	$query = "select GROUP_ID from im_group";
	$cursor = exequery(TD::conn(), $query);

	while ($ROW = mysqli_fetch_array($cursor)) {
		$GROUP_ID[] = $ROW["GROUP_ID"];
	}

	if (!empty($GROUP_ID)) {
		foreach ($GROUP_ID as $K ) {
			$query4 = "select GROUP_UID from im_group where GROUP_ID = '$K'";
			$cursor4 = exequery(TD::conn(), $query4);

			if ($ROW4 = mysqli_fetch_array($cursor4)) {
				$GROUP_UID = td_trim($ROW4["GROUP_UID"]);
				$GROUP_UID = explode(",", $GROUP_UID);
				$GROUP_UID = array_unique($GROUP_UID);
				$GROUP_UID = array_filter($GROUP_UID);

				foreach ($GROUP_UID as $k => $v ) {
					if ($v == $UID) {
						unset($GROUP_UID[$k]);
					}
				}

				$GROUP_UID = implode(",", $GROUP_UID);
				$GROUP_UID = td_trim($GROUP_UID);
				$GROUP_UID = $GROUP_UID . ",";
			}

			$query5 = "UPDATE im_group SET GROUP_UID = '$GROUP_UID' where GROUP_ID='$K'";
			exequery(TD::conn(), $query5);
			$lutime = array("group" => time());
			$redis->hmset("message:info:lutime", $lutime);
		}
	}

	$data_info[] = array("user_id" => $USER_ID, "priv_id" => "", "to_id" => "", "type" => 2);
	include_once "inc/attendance/attend.setting.funcs.php";
	$setting = new AttendSetting();
	$setting->set_userduty($data_info);
	$sql = "select * from meeting_room where find_in_set('$USER_ID',OPERATOR)";
	$cur = exequery(TD::conn(), $sql);

	while ($line = mysqli_fetch_array($cur)) {
		$mr_id = $line["MR_ID"];
		$operator = $line["OPERATOR"];
		$operator = "," . $operator;
		$user_id_leave = "," . $USER_ID . ",";
		$operator_new = str_replace($user_id_leave, ",", $operator);
		$operator_new = ltrim($operator_new, ",");
		$sql_update = "update meeting_room set OPERATOR = '$operator_new' where MR_ID = '$mr_id'";
		exequery(TD::conn(), $sql_update);
	}
}

if ($audit_flag) {
	$log_data = array("src" => intval($SECRET_LEVEL_OLD), "des" => intval($SECRET_LEVEL), "change_user" => $USER_ID);
	addEmailAuditLog(72, $_SESSION["LOGIN_UID"], $log_data);
}

$UID = rtrim(GetUidByUserID($USER_ID), ",");
$query = "update TD_USER set AVATAR='$SEX' where USER_ID='$USER_ID' and (AVATAR='0' || AVATAR='1')";
exequery(TD::conn(), $query);

for ($MI = 0; $MI < 11; $MI++) {
	TD::cache()->delete("MY_PRIV_" . $UID . "_" . $MI);
}
if ($IM_RANGE_OLD != $IM_RANGE) {
	set_sys_para(array("ORG_UPDATE" => date("Y-m-d H:i:s")));
}
if (($DEPT_ID_OLD != $DEPT_ID) && ($DEPT_ID != "0")) {
	$DEPT_NAME = get_long_dept_name($DEPT_ID);

	if ($DEPT_ID_OLD == 0) {
		$DEPT_NAME_OLD = _("离职/外部人员");
		$CONTENT = _("离职用户：$USER_NAME_OLD");
		add_secure_log(23, $CONTENT);
	}
	else {
		$DEPT_NAME_OLD = get_long_dept_name($DEPT_ID_OLD);
	}

	$SMS_CONTENT = sprintf(_("您的部门已由[%s]调整至[%s]，请重新登录。"), $DEPT_NAME_OLD, $DEPT_NAME);
	$SEND_TIME = date("Y-m-d H:i:s", time());
	send_sms($SEND_TIME, 'admin', $USER_ID, 0, $SMS_CONTENT, "");
}

if ($USER_PRIV_OLD != $USER_PRIV) {
	$PRIV_NAME = $USER_PRIV_NAME;
	$PRIV_NAME_OLD = GetPrivNameById($USER_PRIV_OLD);
	$PRIV_NAME_OLD = substr($PRIV_NAME_OLD, 0, -1);
	$SMS_CONTENT2 = sprintf(_("您的角色已由[%s]调整至[%s]，请重新登录。"), $PRIV_NAME_OLD, $PRIV_NAME);
	$SEND_TIME2 = date("Y-m-d H:i:s", time());
	send_sms($SEND_TIME2, 'admin', $USER_ID, 0, $SMS_CONTENT2, "");
}



if (($PRIV_ID1 != "") || ($USER_ID1 != "") || ($DEPT_ID_MSG != "")) {
	$query = "select * from MODULE_PRIV where UID='$UID' and MODULE_ID='0'";
	$cursor = exequery(TD::conn(), $query);

	if ($ROW = mysqli_fetch_array($cursor)) {
		$query = "update MODULE_PRIV set PRIV_ID='$PRIV_ID1',USER_ID='$USER_ID1',DEPT_ID='$DEPT_ID_MSG' where UID='$UID' and MODULE_ID='0'";
		exequery(TD::conn(), $query);
	}
	else {
		$query = "insert into MODULE_PRIV (UID,MODULE_ID,DEPT_PRIV,ROLE_PRIV,PRIV_ID,USER_ID,DEPT_ID) values('$UID','0','1','2','$PRIV_ID1','$USER_ID1','$DEPT_ID_MSG')";
		exequery(TD::conn(), $query);
	}

	$user_strs = "";
	$user_arr = array();
	$sql = "SELECT * FROM td_user WHERE NOT_LOGIN = 0 OR NOT_MOBILE_LOGIN = 0 ";
	$curs = exequery(TD::conn(), $sql);

	while ($row = mysqli_fetch_array($curs)) {
		$user_arr[] = array("UID" => $row["UID"], "USER_ID" => $row["USER_ID"], "DEPT_ID" => $row["DEPT_ID"], "DEPT_ID_OTHER" => $row["DEPT_ID_OTHER"], "USER_PRIV" => $row["USER_PRIV"], "USER_PRIV_OTHER" => $row["USER_PRIV_OTHER"]);
	}

	if (($DEPT_ID_MSG != "") && ($DEPT_ID_MSG != "ALL_DEPT")) {
		foreach ($user_arr as $value ) {
			if (find_id($DEPT_ID_MSG, $value["DEPT_ID"]) || (check_id($DEPT_ID_MSG, $value["DEPT_ID_OTHER"], 1) != "")) {
				$user_strs .= $value["USER_ID"] . ",";
			}
		}
	}

	if (($USER_ID1 != "") && ($DEPT_ID_MSG != "ALL_DEPT")) {
		$user_strs .= $USER_ID1;
	}

	if (($PRIV_ID1 != "") && ($DEPT_ID_MSG != "ALL_DEPT")) {
		foreach ($user_arr as $value ) {
			if (find_id($PRIV_ID1, $value["USER_PRIV"]) || (check_id($PRIV_ID1, $value["USER_PRIV_OTHER"], 1) != "")) {
				$user_strs .= $value["USER_ID"] . ",";
			}
		}
	}

	$UID_STR = array_unique(explode(",", $user_strs));
	$user_strs = implode(",", $UID_STR);

	if ($user_strs != "") {
		$sql = "SELECT TD_USER_ID FROM user_ext WHERE FIND_IN_SET('$USER_ID',EMAIL_RECENT_LINKMAN)";
		$curs = exequery(TD::conn(), $sql);

		if (0 < mysqli_affected_rows()) {
			$up_user = $USER_ID . ",";

			while ($row = mysqli_fetch_array($curs)) {
				if (!find_id($user_strs, $row["USER_ID"])) {
					$sql_up = "UPDATE td_user_ext SET EMAIL_RECENT_LINKMAN = REPLACE(EMAIL_RECENT_LINKMAN,'$up_user','') WHERE USER_ID = '{$row["USER_ID"]}'";
					exequery(TD::conn(), $sql_up);
				}
			}
		}
	}
}
else {
	$query = "delete from MODULE_PRIV where UID='$UID' and MODULE_ID='0'";
	exequery(TD::conn(), $query);
}
cache_users();
if (file_exists("fis_acset_update.php")) {
	include_once "fis_acset_update.php";
}
if (($secu == 1) && (get_secure_priv("sys_user_edit") == 1)) {
	$CONTENT = _("修改用户：$USER_NAME");
	add_secure_log(1, $CONTENT);
}
if ((($DEPT_ID_OLD != $DEPT_ID) && ($DEPT_ID != "0")) || ($USER_PRIV_OLD != $USER_PRIV)) {
	$data_info[] = array("user_id" => $USER_ID, "priv_id" => $USER_PRIV, "to_id" => $DEPT_ID, "type" => 1);
	include_once "inc/attendance/attend.setting.funcs.php";
	$setting = new AttendSetting();
	$setting->set_userduty($data_info);
}
exit(json_encode(array('code' => 0, 'msg' => '成功','data'=>array('user_id'=>$USER_ID))));

?>
