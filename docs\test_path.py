import os

# 测试文件路径是否正确
print("当前工作目录:", os.getcwd())

# 测试功能内容文件是否存在
if os.path.exists('功能内容.txt'):
    print("功能内容.txt 文件存在")
else:
    print("功能内容.txt 文件不存在")

# 测试周报目录是否存在
if os.path.exists('周报'):
    print("周报目录存在")
    print("周报目录内容:", os.listdir('周报'))
else:
    print("周报目录不存在")
    
# 尝试打开功能内容文件
try:
    with open('功能内容.txt', 'r', encoding='utf-8') as f:
        print("成功打开功能内容.txt文件")
        print("文件前10行:")
        for i, line in enumerate(f):
            if i < 10:
                print(line.strip())
            else:
                break
except Exception as e:
    print("打开文件时出错:", e)