<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>视频</title>
    <!-- 引入样式 -->
    <!--<link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/company.css">-->
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>

    <script src="__PUBLIC__/video/DispatchLib/DispatchCtrlLib.js"></script>
    <script src="__PUBLIC__/video/DispatchCallback.js"></script>
    <script src="__PUBLIC__/video/lib/vue/vue.min.js"></script>
    <script src="__PUBLIC__/video/lib/vue/polyfill.min.js"></script>
    <script src="__PUBLIC__/video/lib/vue/axios/axios.min.js"></script>
    <link rel="stylesheet" href="__PUBLIC__/video/lib/vue/element_ui/theme-chalk/index.css">
    <script src="__PUBLIC__/video/lib/vue/element_ui/index.js"></script>
    <script src="__PUBLIC__/video/lib/md5.js"></script>
    <style>
        .split-line {
            border-top: 1px solid #666;
            height: 20px;
        }

        .pannel {
            width: 100%;
            display: inline-block;
            vertical-align: top;
        }

        .pannel_input {
            display: inline-block;
            width: 200px
        }

        .video_window,
        .vv {
            width: 100%;
            /*height: 400px;*/
        }

        .videowindow_blur,
        .video_local {
            margin: 0;
            width: 100%;
            height: 100%;
            display: inline-block;
        }

        .localobj {
            width: 100%;
            height: 100%;
        }

        #demo {
            width: 100%;
            height: auto;
            display: inline-block;
        }

        .video_block {
            width: 600px;
            height: auto;
            display: inline-block;
            position: fixed;
        }

        .log {
            position: absolute;
            width: calc(100% - 400px);
            height: 100%;
            left: 0;
            top: 0;
            background: rgba(96, 96, 96, 0.5);
        }

        .log_close {
            position: absolute;
            right: 50px;
            top: 10px;
            font-size: 30px;
            color: red;
            transform: rotate(45deg);
            z-index: 2147483647;
            transition: .3s;
            cursor: pointer;
        }

        .log_close:hover {
            transition: .3s;
            transform: rotate(135deg);
        }
        .video_item { display: block;border: 1px solid #000; float: left;margin: 5px;position: relative;}
        .video_item .title { width:100%; line-height:50px; background-color: rgba(235, 238, 245,0.3); position: absolute;bottom:0;z-index:99;color:#fff;}
    </style>

</head>
<body style="height:100%;">
<div id="app" v-cloak>
    <div id="demo">
        <div class="header" style="display:none;">
            <h2>账号</h2>
            <el-form :inline="true" :model="user" label-position="right">
                <el-form-item label="服务器ip:">
                    <el-input v-model="user.ip" placeholder=""></el-input>
                </el-form-item>
                <el-form-item label="调度账号:">
                    <el-input v-model="user.account" placeholder="请输入账号"></el-input>
                </el-form-item>
                <el-form-item label="密码:">
                    <el-input v-model="user.pwd" placeholder="请输入密码" type="password"></el-input>
                </el-form-item>
            </el-form>
            <el-form :inline="true" :model="user" label-position="right">
                <el-form-item>
                    <el-button @click="buttonclick">{{user.initAction}}</el-button>
                </el-form-item>
                <el-form-item label="初始化状态:">
                    <span>{{user.initStatus}}</span>
                </el-form-item>
                <el-form-item>
                    <el-button @click="loginfn">{{user.loginAction}}</el-button>
                </el-form-item>
                <el-form-item label="登录状态:">
                    <span>{{user.loginStatus}}</span>
                </el-form-item>
            </el-form>
        </div>
        <!--        <div class="split-line"></div>-->
        <div class="actions">
            <!-----------------------------------视频操作窗口------------------------------------------->
            <div class="pannel">
                <el-card class="box-card" v-model="sipVideo" style="display:none;">
                    <div slot="header" class="clearfix">
                        <h3>视频拉取</h3>
                        <span>终端号码:</span>
                        <el-input class="pannel_input" v-model="sipVideo.number"></el-input>
                        <el-button @click="sipVideoFn(0, 1)">{{sipVideo.action}}</el-button>
                        <el-button @click="sipVideoFn(0, 5)">视频源-{{sipVideo.action}}</el-button>
                        <br />
                        <br />
                        <span>转发号码:</span>
                        <el-input class="pannel_input" v-model="sipVideo.pushNumber"></el-input>
                        <el-button @click="sipVideoFnPush">转发</el-button>
                        <br />
                        <br />
                    </div>
                </el-card>
                <div v-for="item in numbers" class="video_item" :style="{width: width+'px',height: height+'px'}">
                    <div class="title"><span style="padding: 0 30px;">{{item.title}}</span></div>
                </div>
            </div>
        </div>
    </div>
</div>

<style scoped>.expert-score .el-form-item__label{line-height: 20px;}</style>
<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script>
    Vue.use(httpVueLoader);
    function getUrlParams(key) {
        var reg = new RegExp("(^|&)" + key + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) {
            return unescape(r[2]);
        }
        console.info("%c 0000000=" + (new Date()), "color:red");
        return null;
    };
    var app = new Vue({
        el: '#app',
        data: {
            numbers:[
                {number:160401,title:'是少时诵诗书试试'},
                {number:160402,title:'是少时诵诗书试试'},
                {number:160403,title:'是少时诵诗书试试'},
                {number:160404,title:'是少时诵诗书试试'},
            ],
            videos:[],
            height: (document.documentElement.clientHeight-50)/2,
            width: (document.documentElement.clientWidth-70)/2,
            user: {
                ip: '*************',           //登录服务器的ip
                account: 'test01',                //调度台账号
                pwd: 'Dx@2025',                    //调度台密码
                initAction: '初始化',       //初始化按钮
                initStatus: '未初始化',      //初始化状态
                loginAction: '登录',       //登录按钮
                loginStatus: '未登录',      //登录状态
                axios_header: 'http://',
                fleetId: -1,
                pagename: null,
            },
            sipVideo: {
                number: '{$number}',                 //需要进行sip视频的终端号码
                action: '拉取视频',         //按钮显示
                status: '空闲',               //视频状态
                callId: -1,                 //sip视频的呼叫id
                pushNumber: '********'                 //转发的号码
            },
            pocCall: {
                type: '1',                     //类型，前端检验呼叫终端还是群组
                number: '160302',             //终端/群组号码
                id: '11014',                    //终端/群组id
                name: '测试人员长度',           //终端/群组名称
                status: '',                     //呼叫状态
                status_channel: '建立通道',      //按钮显示
                channelId: -1,                   //呼叫的通道号，后续所有控件操作都围绕channelId
                status_moniter: '未监听',
                status_call: '未呼叫',
                status_speak: '未呼叫',
                speaker: '',
            },
            ipCall: {
                channelId: -1,                  //ip呼叫通道
                number: '{$number}',               //终端号码
                ExtNum: '10003',                //分机号，也叫SIP号码，Extnumber/Extnum
                channelId_voice: -1,            //IP语音channel，前端自己拓展数组
                channelId_video: -1,            //IP视频channel,
                status_voice: '空闲',
                status_video: '空闲',
                action_voice: '语音呼叫',
                action_video: '视频呼叫',
            },
            pocMsg: {
                type: '0',                      //发送给群组还是终端的识别号，此处前端设置为 0终端 1群组
                number: '10000',             //发消息给号码为number的终端
                txt: '这是一条测试文本',                        //发的文本消息
                img: '',                        //发的图片
                position: {                     //发的位置
                    lng: '',                            //经度
                    lat: '',                            //维度
                    desc: ''                            //描述
                },
                status: ''
            },
            confLog: [],                           //会议详情
            confLogVideo: [],                           //会议详情
            aLog: [],                             //日志
            aLog_show: false,  //是否显示日志
            isInit: false
        },
        methods: {
            //数据加载
            getData() {
                var _this = this;
                var param = {};
                param._ajax = 1;
                axios.post('', param).then(function (res) {
                    if (res.data.code == 0) {
                        _this.numbers = res.data.data;
                        setTimeout(function() {
                            _this.buttonclick();
                            setTimeout(function() {
                                _this.loginfn();
                                setTimeout(async function() {
                                    for (let i in _this.numbers){
                                        await _this.sipVideoFnAsync(_this.numbers[i].number,i,1);
                                    }
                                }, 1000);
                            }, 1000);
                        }, 1000);
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            toggleLog: function () {
                this.aLog_show = !this.aLog_show
            },
            //初始化
            buttonclick: function () {
                //http://127.0.0.1/web/demo/demo.html?pagename=main
                //http://127.0.0.1/web/demo/demo.html?pagename=test
                this.user.pagename = getUrlParams("pagename");
                if (this.user.initStatus === '未初始化') {
                    var retCode = DispatchCtrlLib.Initialize(DispatchCallBack, 0, this.user.pagename);
                    if (retCode === 0) {
                        this.user.initAction = '释放';
                        this.user.initStatus = '已初始化';
                        this.isInit = true;
                        var demourl = window.location.href;
                        if (demourl.indexOf("https") == 0) {
                            this.user.axios_header = "https://" + this.user.ip + ":1614";
                        } else {
                            this.user.axios_header = "http://" + this.user.ip + ":1604";
                        }
                        for (i in this.numbers){
                            videoInit(i);
                        }
                        // videoInit(1);
                        // videoInit(2);
                        // this.$message("初始化成功");
                    } else {
                        this.$message("初始化失败");
                    }
                } else {
                    var retCode = DispatchCtrlLib.Uninitialize();
                    if (retCode === 0) {
                        this.user.initAction = '初始化';
                        this.user.initStatus = '未初始化';
                        this.isInit = false;
                        this.$message("释放成功");
                    } else {
                        this.$message("释放失败");
                    }
                }
            },
            //登录/登出
            loginfn: function () {
                if (this.user.loginStatus === '未登录') {
                    if (this.isInit == true) {
                        this.getFleetId();
                        var Server = this.user.ip + "";
                        var Name = this.user.account + "";
                        var Password = this.user.pwd + "";
                        var retCode = DispatchCtrlLib.Login(Name, Password, Server);
                        if (retCode === 0) {
                            this.user.loginAction = '注销';
                            this.user.loginStatus = '已登录';
                            // this.$message("登录成功");
                        } else {
                            this.$message("登录失败");
                        }
                    } else {
                        this.$message("未初始化，请先初始化");
                    }
                } else {
                    var retCode = DispatchCtrlLib.Logout();
                    if (retCode === 0) {
                        this.user.loginAction = '登录';
                        this.user.loginStatus = '未登录';
                        this.$message("注销成功");
                    } else {
                        this.$message("注销失败");
                    }
                }
            },
            pocStatusLogin: function () {
                this.user.pocAction = '注销POC';
                this.user.pocStatus = '已登录POC';
                this.$message('登录POC成功');

                this.aLog.push({ time: new Date().toLocaleTimeString(), entry: 'pocStatusLogin', obj: '登录POC成功' });
            },
            pocStatusLogout: function () {
                this.user.pocAction = '登录POC';
                this.user.pocStatus = '未登录POC';

                this.aLog.push({ time: new Date().toLocaleTimeString(), entry: 'pocStatusLogin', obj: '登出POC成功' });
            },

            //sip视频/挂断
            sipVideoFn: function (number,index, type) {
                var info;
                if (this.user.loginStatus === '未登录') {
                    return this.$message('未登录无法进行视频拉取')
                }
                if (this.sipVideo.callId === -1) {
                    var Number = number + "";
                    var Type = type;
                    CallArray[index] = DispatchCtrlLib.MonitorVideoPull(Number, Type, "");
                    if (CallArray[index] === 0) {
                        this.$message('拉取视频失败');
                        return;
                    } else {
                        this.sipVideo.callId = CallArray[index].toString();  //保存CallID
                        this.sipVideo.action = '挂断视频';
                        this.sipVideo.status = '视频中';
                        VideoLibArray[index].StartDisplay(CallArray[index]);
                    }
                } else {
                    CallArray[index] = this.sipVideo.callId;
                    var retCode = DispatchCtrlLib.MonitorVideoHangup(parseInt(CallArray[index]));
                    if (retCode == 0) {
                        VideoLibArray[index].StopDisplay();
                        this.sipVideo.callId = -1;
                        this.sipVideo.action = '拉取视频';
                        this.sipVideo.status = '空闲';

                    } else {
                        this.$message('挂断失败');
                    }
                }
            },

            // 异步sip视频/挂断
            sipVideoFnAsync: async function (number, index, type) {
                // 确保登录状态
                if (this.user.loginStatus === '未登录') {
                    this.$message('未登录无法进行视频拉取');
                    return;
                }
                // 独立维护每个视频的callId
                if (!this.sipVideo.callIds) {
                    this.sipVideo.callIds = {};
                }
                // 如果当前index还没有callId，说明是拉取，否则是挂断
                if (!this.sipVideo.callIds[index] || this.sipVideo.callIds[index] === -1) {
                    var Number = number + "";
                    var Type = type;
                    CallArray[index] = DispatchCtrlLib.MonitorVideoPull(Number, Type, "");
                    if (CallArray[index] === 0) {
                        this.$message('拉取视频失败');
                        return;
                    } else {
                        this.sipVideo.callIds[index] = CallArray[index].toString();  //为每个视频保存独立callId
                        this.sipVideo.action = '挂断视频';
                        this.sipVideo.status = '视频中';
                        VideoLibArray[index].StartDisplay(CallArray[index]);
                    }
                } else {
                    CallArray[index] = this.sipVideo.callIds[index];
                    var retCode = DispatchCtrlLib.MonitorVideoHangup(parseInt(CallArray[index]));
                    if (retCode == 0) {
                        VideoLibArray[index].StopDisplay();
                        this.sipVideo.callIds[index] = -1;
                        this.sipVideo.action = '拉取视频';
                        this.sipVideo.status = '空闲';
                    } else {
                        this.$message('挂断失败');
                    }
                }
            },

            //视频转发
            sipVideoFnPush: function () {

                var sipObj = this.sipVideo;
                if (sipObj.callId === -1) {
                    return this.$message('请先拉取终端后再进行转发');
                }
                var CallID = sipObj.callId;
                var Number = sipObj.pushNumber + "";
                var retCode = DispatchCtrlLib.MonitorVideoPush(parseInt(CallID), Number);
                if (retCode == 0) {
                    this.$message('转发成功');
                } else {
                    this.$message('转发失败');
                }

            },
            resetPocCall: function () {
                this.pocCall.channelId = -1
            },
            setPocStatus: function (txt) {
                this.pocCall.status = txt
            },

            //通过号码获取群组信息（群组id，号码，名称等）
            getGinfoByGnum: function (number, callback) {
                axios.post(this.user.axios_header + '/scs/Subscriber/getGInfoByGnumber', { gnumber: number })
                    .then(function (res) {
                        this.aLog.push({
                            time: new Date().toLocaleTimeString(),
                            entry: 'getGinfoByGnum',
                            obj: JSON.stringify(res.data)
                        });
                        if (res.status === 200) {
                            callback(res)
                        }
                    }.bind(this))
            },

            //通过号码获取终端信息(分机号，号码，名称等)
            getSubscriberInfoByNum: function (number, callback) {
                axios.post(this.user.axios_header + '/scs/Subscriber/queryNumAndNameByUid', {
                    uid: number
                }).then(function (res) {
                    this.aLog.push({
                        time: new Date().toLocaleTimeString(),
                        entry: 'getSubscriberInfoByNum',
                        obj: JSON.stringify(res.data)
                    });
                    if (res.status === 200) {
                        callback(res)
                    } else {
                        this.$message('接口错误');
                    }
                }.bind(this)).catch(function (err) {
                    this.$message('请求错误')
                }.bind(this));
            },
            //获取集团号
            getFleetId: function () {
                var fleetId;
                var condition = { userId: this.user.account };
                axios.post(this.user.axios_header + '/scs/electricRailController/fleetId', {
                    userId: this.user.account
                }).then(function (res) {
                    if (res.status === 200) {
                        if (res.data.success) {
                            this.user.fleetId = res.data.obj[0].fid;
                        }
                    } else {
                        this.$message('接口错误');
                    }
                }.bind(this)).catch(function (err) {
                    this.$message('请求错误')
                }.bind(this));
            },
            //ip通话方法
            ipCallFn: function (type) {
                var callObj = this.ipCall;
                this.aLog.push({
                    time: new Date().toLocaleTimeString(),
                    entry: 'ipCallFn',
                    obj: JSON.stringify(callObj)
                });
                var sipObj;
                switch (type) {
                    case 'voice':
                        if (callObj.channelId_voice > 0) {
                            var CallID = parseInt(callObj.channelId_voice);
                            var retCode = DispatchCtrlLib.VoipHangupCall(CallID);
                            if (retCode == 0) {
                                demo.ipCall.channelId_voice = -1;
                                demo.ipCall.action_voice = '语音呼叫';
                                callObj.status_voice = '空闲';
                            } else {
                                this.$message('参数错误')
                            }
                        } else {
                            this.getSubscriberInfoByNum(callObj.number, function (res) {
                                var data = res.data.obj;
                                if (data.length > 0) {
                                    var user = data[0];

                                    callObj.ExtNum = user.sipNum;
                                    var ExtNum = callObj.ExtNum.toString();
                                    callObj.channelId_voice = DispatchCtrlLib.VoipMakeAudioCall(ExtNum);
                                    if (callObj.channelId_voice < 0) {
                                        this.$message("视频通话失败");
                                    } else {
                                        callObj.status_voice = '连接中';
                                        callObj.action_voice = '挂断语音';
                                    }
                                } else {
                                    callObj.ExtNum = callObj.number;
                                    var ExtNum = callObj.ExtNum.toString();
                                    callObj.channelId_voice = DispatchCtrlLib.VoipMakeAudioCall(ExtNum);
                                    if (callObj.channelId_voice < 0) {
                                        this.$message("视频通话失败");
                                    } else {
                                        callObj.status_voice = '连接中';
                                        callObj.action_voice = '挂断语音';
                                    }
                                }
                            }.bind(this));
                        }
                        break;
                    case 'video':
                        /* IP视频通话*/
                        if (callObj.channelId_video > 0) {
                            var CallID = parseInt(callObj.channelId_video);
                            var retCode = DispatchCtrlLib.VoipHangupCall(CallID);
                            if (retCode == 0) {
                                VideoLibArray[1].StopDisplay();
                                VideoLibArray[2].StopDisplay();
                                demo.ipCall.channelId_video = -1;
                                demo.ipCall.action_video = '视频呼叫';
                                demo.ipCall.status_video = '空闲';
                            } else {
                                this.$message('参数错误')
                            }
                        } else {
                            this.getSubscriberInfoByNum(callObj.number, function (res) {
                                var data = res.data.obj;
                                if (data.length > 0) {
                                    var user = data[0];

                                    callObj.ExtNum = user.sipNum;

                                    var ExtNum = callObj.ExtNum.toString();
                                    CallArray[2] = DispatchCtrlLib.VoipMakeVideoCall(ExtNum);
                                    callObj.channelId_video = CallArray[2];
                                    if (callObj.channelId_video < 0) {
                                        this.$message("视频通话失败");
                                    } else {
                                        VideoLibArray[2].StartDisplay(CallArray[2]);
                                        VideoLibArray[1].StartDisplay(-1);
                                        callObj.status_video = '连接中。。。';
                                        callObj.action_video = '挂断视频'
                                    }
                                } else {
                                    var ExtNum = callObj.number.toString();
                                    CallArray[2] = DispatchCtrlLib.VoipMakeVideoCall(ExtNum);
                                    callObj.channelId_video = CallArray[2];
                                    if (callObj.channelId_video < 0) {
                                        this.$message("视频通话失败");
                                    } else {
                                        VideoLibArray[2].StartDisplay(CallArray[2]);
                                        VideoLibArray[1].StartDisplay(-1);
                                        callObj.status_video = '连接中。。。';
                                        callObj.action_video = '挂断视频'
                                    }
                                }
                            }.bind(this));
                        }
                        break;
                }
            },
            ipSetVoiceStatus: function (txt) {
                var callObj = this.ipCall;
                callObj.status_voice = txt
            },
            ipSetVideoStatus: function (txt) {
                var callObj = this.ipCall;
                callObj.status_video = txt

            },
            selectImg: function () {
                var dom = document.getElementById('pocMsg_img');
                dom.click();
                return;
            },
            pocMsgFn: function (type) {
                var rspOBJ, obj = this.pocMsg;
                var TargetInfo = {
                    UserID: 0, UserNumber: '', UserName: '', GroupID: 0, GroupNumber: '',
                    GroupName: '', TempGroupID: '', GroupNumberList: '', UserNumberList: ''
                };
                var MsgInfo = {
                    MsgString: '', MsgAttachAddr: '', LatitudeType: 0, Latitude: 0,
                    LongitudeType: 0, Longitude: 0, LocDescription: ''
                };
                var MsgType = 0;
                var TargetType = 0;
                switch (type) {
                    case 'txt':
                        MsgInfo.MsgString = obj.txt.toString();
                        MsgType = 0;
                        break;
                    case 'img':
                        var dom = document.getElementById('pocMsg_img');

                        if (dom.value == null || dom.value === '') {
                            this.$message('请先选择图片');
                            return;
                        }
                        var attAddr;
                        var file = dom.files[0];
                        var path = "http://" + this.user.ip + ":1603/scs/pocMss/upload";

                        var request = new XMLHttpRequest();
                        var formData = new FormData();
                        formData.append("pocFile", file);
                        request.open("post", path, false);
                        request.onload = function (e) {
                            var text = e.target.response;
                            var textObj = JSON.parse(text);
                            if (textObj && textObj.success) {
                                attAddr = textObj.obj.relativeUrl;
                            } else {
                                alert(textObj.msg);
                            }
                        };
                        request.send(formData);
                        if (attAddr != null) {
                            MsgInfo.MsgAttachAddr = attAddr;
                            MsgType = 1;
                        }
                        break;
                    case 'position':
                        var position = obj.position;
                        MsgType = 4;
                        MsgInfo.Latitude = 100000 * position.lat;
                        MsgInfo.Longitude = 100000 * position.lng;
                        MsgInfo.LocDescription = position.desc.toString();
                        break;
                }

                if (parseInt(obj.type) === 0) {
                    TargetInfo.GroupNumber = obj.number;
                    TargetType = 1;
                    MsgIndex = DispatchCtrlLib.MsgSend(TargetType, TargetInfo, MsgType, MsgInfo);
                } else if (parseInt(obj.type) === 1) {
                    TargetInfo.UserNumber = obj.number;
                    TargetType = 0;
                    MsgIndex = DispatchCtrlLib.MsgSend(TargetType, TargetInfo, MsgType, MsgInfo);

                }

            },

        },
        mounted: function () {
            var _this = this;
            this.getData();
            // this.loginfn();
        }
    });
    var VideoLibArray = new Array();
    var CallArray = new Array();
    function videoInit(index) {
        VideoLibArray[index] = new DispatchVideoLib();
        VideoLibArray[index].OnStatus = OnVideoStatus;
        VideoLibArray[index].Init(document.getElementsByClassName('video_item')[index]);
    }
    function OnVideoStatus(event) {
        console.log("OnVideoStatus" + event);
    }
    function DispatchCallBack(Type, Parm) {
        console.log("Dispatch CallBack" + Type + Parm);
        switch (Type) {
            case "InitializeRsp":
                DispatchCallback.InitializeRsp(Parm.Result);
                break;
            case "UninitializeRsp":
                DispatchCallback.UninitializeRsp(Parm.Result);
                break;
            case "LoginRsp":
                DispatchCallback.LoginRsp(Parm.Result);
                break;
            case "LogoutRsp":
                DispatchCallback.LogoutRsp(Parm.Result);
                break;
            case "ServerLinkStatusNotify":
                DispatchCallback.ServerLinkStatusNotify(Parm.Type);
                break;
            case "MonitorVideoStatusNotify":
                DispatchCallback.MonitorVideoStatusNotify(Parm.Status);
                if (Parm.Status == 2) {
                    //VideoLibArray[0].StartDisplay(Parm.CallID);
                }
                break;
            case "MonitorVideoIncomingCallNotify":
                DispatchCallback.MonitorVideoIncomingCallNotify(Parm.CallID, Parm.Number);
                break;
            case "VoipCallStatusNotify":
                DispatchCallback.VoipCallStatusNotify(Parm.Status);
                break;
            case "VoipIncomingCallNotify":
                DispatchCallback.VoipIncomingCallNotify(Parm.CallID, Parm.CallType, Parm.ConferenceInvite);
                break;
            case "ConferenceStatusNotify":
                DispatchCallback.ConferenceStatusNotify(Parm.Status, Parm.MemType);
                var ExtNum = Parm.ExtNum;
                var ConfID = parseInt(Parm.ConfID);
                var that;
                if (ConfID >= 1200) {
                    that = demo.confLog;
                } else {
                    that = demo.confLogVideo;
                }
                if (Parm.Status == 0) {
                    that.push({
                        time: new Date().toLocaleTimeString(),
                        obj: "分机号为[" + ExtNum + "]的成员已被移除会议室"
                    });
                } else if (Parm.Status == 1) {
                    that.push({
                        time: new Date().toLocaleTimeString(),
                        obj: "分机号为[" + ExtNum + "]的成员已接入会议室"
                    });
                } else if (Parm.Status == 4) {
                    that.push({
                        time: new Date().toLocaleTimeString(),
                        obj: "会议已结束"
                    });
                } else if (Parm.Status == 5) {
                    that.push({
                        time: new Date().toLocaleTimeString(),
                        obj: "分机号为[" + ExtNum + "]的成员已被禁言"
                    });
                } else if (Parm.Status == 6) {
                    that.push({
                        time: new Date().toLocaleTimeString(),
                        obj: "分机号为[" + ExtNum + "]的成员已解除禁言"
                    });
                } else if (Parm.Status == 9) {
                    if (ExtNum > 0) {
                        that.push({
                            time: new Date().toLocaleTimeString(),
                            obj: "正在聚焦分机号为[" + ExtNum + "]的成员"
                        });
                    } else {
                        that.push({
                            time: new Date().toLocaleTimeString(),
                            obj: "正在聚焦所有成员"
                        });
                    }
                }
                break;
            //                case "PttMonitorRsp":
            //                  DispatchCallback.PttMonitorRsp(Parm.Type,Parm.Result);
            //              	break;
            //                case "LoginRsp":
            //                  DispatchCallback.PttMonitorRsp(Parm.Type,Parm.Result);
            //              	break;
            case "LocStartSubscribeRsp":
                DispatchCallback.LocStartSubscribeRsp(Parm.Result);
                break;
            case "StatusNotify":
                DispatchCallback.StatusNotify(Parm.StatusList);
                break;
            case "MsgSendRsp":
                DispatchCallback.MsgSendRsp(Parm.Result);
                break;
            case "MsgRecvNotify":
                DispatchCallback.MsgRecvNotify(Parm.TargetType, Parm.MsgType);
                break;
            case "ServerBroadcastNotify":
                DispatchCallback.ServerBroadcastNotify(Parm.OperType);
                break;
            case "PttMonitorRsp":
            case "PttCallRsp":
            case "PttTalkRequestRsp":
            case "PttTalkReleaseRsp":
            case "PttCallStatusNotify":
            case "PttTalkStatusNotify":
                DispatchCallback.PttCallback(Type, Parm);
                break;
        }
    }
    function DispatchVideoCallBack(Type, Parm) {
        console.log("Dispatch CallBack" + Type + Parm);
    }
    //回调，需应用层声明 function(Number, Type, CallID)
    DispatchCtrlLib.cb_forward = function (Number, Type, CallID) {
        console.log("cb_forward Number:" + Number + " Type:" + Type + " CallID:" + CallID);
    }

    DispatchCtrlLib.cb_ptz = function (Number, Type, CallID) {
        console.log("cb_ptz Number:" + Number + " Type:" + Type + " CallID:" + CallID);
    }


    function validateValue(insValue) {
        return (typeof (insValue) !== "undefined" && insValue != null)
    }


    /* -----------------------------POC相关事件--------------------------------------    */
    function pocLoadChannel() {
        console.log("pocLoadChannel")

        var target = {};
        if (demo.pocMsg.type == 0) {
            target.GroupNumber = demo.pocMsg.number;
        } else if (demo.pocMsg.type == 1) {
            target.UserNumber = demo.pocMsg.number;
        } else if (demo.pocMsg.type == 2) {
            users = demo.pocMsg.number.split(',');
            target.GroupList = [];
            target.UserList = [];
            for (i = 0; i < users.length; i++) {
                target.UserList.push({
                    Number:users[i]
                });
            }
        }

        var channelid = DispatchCtrlLib.PttLoadChannel(parseInt(demo.pocMsg.type), target);
        if (channelid < 0) {
            this.$message("加载通道失败");
            return;
        }

        demo.pocCall.channelId = channelid;
    }

    function PttReleaseChannel() {
        console.log("PttReleaseChannel");
        DispatchCtrlLib.PttReleaseChannel(demo.pocCall.channelId);
        demo.pocCall.channelId = null;
    }

    function pocStartMoniter() {
        console.log("pocStartMoniter");
        DispatchCtrlLib.PttMonitor(1, demo.pocCall.channelId);
    }

    function pocStopMoniter() {
        console.log("pocStopMoniter")
        DispatchCtrlLib.PttMonitor(0, demo.pocCall.channelId);
        demo.pocCall.channelId = -1;
    }

    function pocStartCall() {
        console.log("pocStartCall")
        DispatchCtrlLib.PttCall(demo.pocCall.channelId, 1);
    }

    function pocStopCall() {
        console.log("pocStopCall")
        DispatchCtrlLib.PttCall(demo.pocCall.channelId, 0);
    }

    function pocStartSpeak() {
        console.log("pocStartSpeak")
        DispatchCtrlLib.PttTalkRequest(demo.pocCall.channelId);
    }

    function pocStartCallSpeak() {
        console.log("pocStartCallSpeak")
        DispatchCtrlLib.PttCallTalkRequest(demo.pocCall.channelId);
    }

    function pocStopSpeak() {
        console.log("pocStopSpeak")
        DispatchCtrlLib.PttTalkRelease(demo.pocCall.channelId);
    }
    /*-------------------------SIP视频-----------------------------------*/

    var SIP = {};
    /*IP语音/视频响应*/
    SIP.callStatusRespone = function (jsonData) {
        var callObj = demo.ipCall;
        console.log(jsonData);
        var txt;
        var Status = jsonData.Status;

        if (jsonData.CallID !== callObj.channelId_video) {
            var aStr = ['',
                '连接中...', '通话中...', '对方挂断...',
                '连接超时...', '对方拒接...', '超时未接...',
                '连接失败...', '对方正忙...', '对方不在线...'];
            demo.ipSetVoiceStatus(aStr[Status]);
            if ([1, 2].indexOf(parseInt(jsonData.Status)) > 0) {
                callObj.action_voice = '挂断语音';
            } else {
                callObj.channelId_voice = -1;
                callObj.action_voice = '语音呼叫';
            }
        } else {
            switch (parseInt(Status)) {
                case 1:
                    demo.$message('视频链接中。。。');
                    callObj.action_video = '挂断视频';
                    break;
                case 2://接通成功
                    callObj.action_video = '挂断视频';
                    var sipObj = {
                        CallID: jsonData.CallID,
                        Channel: -1 // -1-左声道 0-静音 1-右声道
                    };
                    //设置声道
                    ScsPlayerCtrl_0.funcSetSpeaker(sipObj);
                    txt = '视频中。。。';
                    break;
                case 3://无法接通//对方挂断
                case 4://对方未接
                case 5://对方拒接
                case 6://无法接通
                case 7:
                    callObj.action_video = '视频呼叫';
                    if (callObj.channelId_video === jsonData.CallID) {
                        txt = '当前用户拒接或挂断IP视频';
                        callObj.channelId_video = -1;
                    }
                    break;
                case 8:
                    if (callObj.channelId_video === jsonData.CallID) {
                        txt = '对方正忙...';
                        callObj.channelId_video = -1;
                    }
                    break;
                case 9:
                    if (callObj.channelId_video === jsonData.CallID) {
                        txt = '对方不在线';
                        callObj.channelId_video = -1;
                    }
                    break;
            }
            demo.ipSetVideoStatus(txt)
        }

    };
    /**
     * 处理呼叫接入，由处理控件事件commentEvent调用
     */
    SIP.processSipEvent = function (sipEventObj) {
        var jsonData = (new Function("return " + sipEventObj.JsonInfo))();

        var type = jsonData.type;
        var sipCallId = jsonData.sipCallId;
        var sipCallType = jsonData.sipCallType;
        var cause = jsonData.cause;
        var index = -1;
        var mediaType = '-1';   //媒体类型
        SIP.sipEventHandler(type, index, sipCallId, cause);
    };

    SIP.sipEventHandler = function (type, index, sipCallId, cause) {
        console.log("拉取视频返回码" + type + "对应提示看ocx文档/或者web文档");
        switch (type) {
            case MediaConstants.EVENT_RECEIVETIMEOUT:
            case MediaConstants.EVENT_SETUPTIMEOUT:
                break;
            case MediaConstants.EVENT_TRYING:
                demo.$message(MediaConstants.VIDEO_CONNECTING);
                break;
            case MediaConstants.EVENT_OK:                                            // 接入成功，任务视频框状态进入play状态.写到监控窗口对象中，当详情对话框打开时数据写入详情
                demo.$message("接入成功");
                demo.sipVideo.action = '挂断视频';
                demo.sipVideo.callId = sipCallId;
                break;
            case MediaConstants.EVENT_READY:
                break;
            case MediaConstants.EVENT_ANSWERED:
                //ScsPlayerCtrl_0.writeDacLog("info", "process sip event answered");
                break;
            case MediaConstants.EVENT_BYE:
                demo.$message(MediaConstants.VIDEO_SOURCE_OFF);
                demo.sipVideo.action = '拉取视频';
                demo.sipVideo.callId = -1;
                break;
            case MediaConstants.EVENT_DELRING:
            case MediaConstants.EVENT_CANCEL:
                demo.$message(MediaConstants.TERMINAL_REJECT);
                break;
            case MediaConstants.EVENT_EXCEPTION:
            case MediaConstants.EVENT_CONFLICT:
                if (cause === "request") {
                    // ScsVideoMediaView.exceptionEndDispatch(mediaType, index, sipCallId);  // 终端呼叫未接，上拉该终端时触发
                }
                break;
            case MediaConstants.EVENT_REJECT:
                demo.$message(MediaConstants.TERMINAL_REJECT);
                break;
            case MediaConstants.EVENT_OFFLINE:
                demo.$message(MediaConstants.VIDEO_SOURCE_OFFLINE);
                demo.sipVideo.action = '拉取视频';
                demo.sipVideo.callId = -1;
                break;
            default:
                break;
        }
    };

    /*---------宏变量------*/
    var MediaConstants = {};
    MediaConstants.EVENT_RECEIVETIMEOUT = "receiveTimeout";     //控件sipEvent上报接收sip超时事件
    MediaConstants.EVENT_SETUPTIMEOUT = "setupTimeout";		//控件sipEvent上报建立sip超时事件
    MediaConstants.EVENT_TRYING = "trying";				//控件sipEvent上报180trying事件
    MediaConstants.EVENT_OK = "ok";					//控件sipEvent上报ok事件
    MediaConstants.EVENT_READY = "ready";				//控件sipEvent上报ready事件
    MediaConstants.EVENT_ANSWERED = "answered";			//控件sipEvent上报answered事件
    MediaConstants.EVENT_BYE = "bye";				//控件sipEvent上报bye事件
    MediaConstants.EVENT_DELRING = "delRing";			//控件sipEvent上报delRing事件
    MediaConstants.EVENT_CANCEL = "cancel";   			//控件sipEvent上报cancel事件
    MediaConstants.EVENT_EXCEPTION = "exception";			//控件sipEvent上报exception事件
    MediaConstants.EVENT_CONFLICT = "conflict";			//控件sipEvent上报conflict事件
    MediaConstants.EVENT_REJECT = "reject";			    //控件sipEvent上报reject事件
    MediaConstants.EVENT_OFFLINE = "offline";			//控件sipEvent上报离线事件

    MediaConstants.TERMINAL_HANGUP = "终端挂断";
    MediaConstants.TERMINAL_REJECT = "终端拒接";
    MediaConstants.TERMINAL_SUCCEED = "视频接入成功";
    MediaConstants.TERMINAL_TRYING = "视频接入中";
    MediaConstants.TERMINAL_TIMEOUT = "视频接入超时";
    MediaConstants.VIDEOBOX_READY = "视频窗口准备中...";
    MediaConstants.VIDEO_SOURCE_OFF = "视频源挂断...";
    MediaConstants.VIDEO_SOURCE_OFFLINE = "终端未连接视频服务器...";
    MediaConstants.VIDEO_SOURCE_REJECT = "视频源拒绝";
    MediaConstants.VIDEO_CONNECTING = "正在视频连接...";
    MediaConstants.VIDEO_CS_CUT = "视频挂断中...";
</script>


</body>
</html>
</body>
</html>