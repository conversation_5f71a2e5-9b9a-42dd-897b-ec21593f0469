<?php
namespace app\demo\model;

use think\Model;
use Medoo\Mdb;

class DemoModel extends Model
{
        protected $table = 'top_fixed_form';

        /**
         * h获取列表数据
         * @param $request
         * @return \think\response\Json
         */
		public function  getDataList ($request){
			try {
				$database = Mdb::DB();
				$params = $request->post();
				$pageSize = (int)empty($params['pageSize']) ? config('app.pageSize') : $params['pageSize'];
				$page = (int)empty($params['page']) ? 1 : $params['page'];
				$offset = ($page-1) * $pageSize;
				$keyword = empty($params['keyword']) ? "" : $params['keyword'];
                $where = null;
                if($keyword){
					$where["OR"] = [
						"f.form_name[~]" => $keyword,
					];
				}
                $joinTable = ['[>]top_user(u)'=>['f.create_user_id'=>'id']];
				//获取记录数
				$count =  $database->count($this->table."(f)",$joinTable,["form_name"],$where);
				$where['LIMIT'] = [$offset,$pageSize];
				//获取本页数据
				$data = $database->select($this->table."(f)", $joinTable
                    , ["f.id","form_name","form_desc","update_time","u.real_name(update_user)"]
                    , $where);
				return  json(['data'=>$data,'total' => $count,'code'=>200,'msg'=>'获取成功']);
			} catch (\Exception $e) {
				$rdata = [
					'code' => 1001,
					'data' => [],
					'msg' => $e->getMessage(),
				];
				return json($rdata);
			}

		}


}