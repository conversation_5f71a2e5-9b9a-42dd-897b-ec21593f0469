<?php
session_start();

// 动态密码验证
function validateDynamicPassword($inputPassword) {
    $basePassword = "qtsawqhy";
    $currentDateTime = date('YmdHi'); // 格式：202312151430 (年月日时分)
    $validPassword = $basePassword . $currentDateTime;
    return $inputPassword === $validPassword;
}

// 检查登录状态
function checkLogin() {
    return isset($_SESSION['dm_query_logged_in']) && $_SESSION['dm_query_logged_in'] === true;
}

// 处理登录请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login_action'])) {
    header('Content-Type: application/json');
    
    if ($_POST['login_action'] === 'login') {
        $password = $_POST['password'] ?? '';
        
        if (validateDynamicPassword($password)) {
            $_SESSION['dm_query_logged_in'] = true;
            echo json_encode(['success' => true, 'message' => '登录成功']);
        } else {
            echo json_encode(['success' => false, 'message' => '密码错误']);
        }
        exit;
    }
    
    if ($_POST['login_action'] === 'logout') {
        session_destroy();
        echo json_encode(['success' => true, 'message' => '已退出登录']);
        exit;
    }
}

// 如果未登录，显示登录页面
if (!checkLogin()) {
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title> 登录</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); height: 100vh; display: flex; align-items: center; justify-content: center; }
        
        .login-container { background: white; padding: 40px; border-radius: 10px; box-shadow: 0 15px 35px rgba(0,0,0,0.1); width: 400px; }
        .login-header { text-align: center; margin-bottom: 30px; }
        .login-header h1 { color: #2c3e50; margin-bottom: 10px; }
        .login-header p { color: #7f8c8d; }
        .system-time { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; border-left: 4px solid #3498db; }
        
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 5px; color: #2c3e50; font-weight: bold; }
        .form-group input { width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 5px; font-size: 14px; }
        .form-group input:focus { outline: none; border-color: #3498db; }
        
        .login-btn { width: 100%; padding: 12px; background: #3498db; color: white; border: none; border-radius: 5px; font-size: 16px; cursor: pointer; }
        .login-btn:hover { background: #2980b9; }
        .login-btn:disabled { background: #bdc3c7; cursor: not-allowed; }
        
        .error-message { background: #e74c3c; color: white; padding: 10px; border-radius: 5px; margin-bottom: 20px; display: none; }
        .password-hint { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 20px; font-size: 12px; color: #6c757d; }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>D-s-q-l-M</h1>
            <div class="system-time">
                <p><strong>现在是:</strong></p>
                <p id="currentTime" style="font-size: 16px; color: #3498db; font-weight: bold;"></p>
            </div>
        </div>
        
        <div id="errorMessage" class="error-message"></div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" placeholder="请输入密码" required>
            </div>
            
            <button type="submit" class="login-btn" id="loginBtn">登录</button>
        </form>
    
    </div>

    <script>

        const serverStartTime = new Date('<?php echo date('Y-m-d H:i:s'); ?>');
        const clientStartTime = new Date();
        const serverTimeOffset = serverStartTime.getTime() - clientStartTime.getTime();

        function updateTime() {
            // 计算当前的服务器时间
            const now = new Date(Date.now() + serverTimeOffset);
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');

            const timeString = `${year}年${month}月${day}日 ${hours}:${minutes}:${seconds}`;

            document.getElementById('currentTime').innerHTML =
                `${timeString}`;
        }

        // 页面加载时开始更新时间
        updateTime();
        setInterval(updateTime, 1000);

        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const errorMessage = document.getElementById('errorMessage');
            
            if (!password) {
                showError('请输入密码');
                return;
            }
            
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';
            
            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: `login_action=login&password=${encodeURIComponent(password)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.reload();
                } else {
                    showError(data.message);
                }
            })
            .catch(error => {
                showError('网络错误，请重试');
            })
            .finally(() => {
                loginBtn.disabled = false;
                loginBtn.textContent = '登录';
            });
        });
        
        function showError(message) {
            const errorMessage = document.getElementById('errorMessage');
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            setTimeout(() => {
                errorMessage.style.display = 'none';
            }, 3000);
        }
    </script>
</body>
</html>
<?php
    exit;
}

// 达梦数据库配置
$config = [
    'host' => '127.0.0.1',
    'port' => '5236',
    'dbname' => 'SYSDBA',
    'username' => 'SYSDBA',
    'password' => 'SYSDBA'
];

// 处理AJAX请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        $dsn = "dm:host={$config['host']};port={$config['port']};dbname={$config['dbname']}";
        $pdo = new PDO($dsn, $config['username'], $config['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        
        switch ($_POST['action']) {
            case 'execute_sql':
                $page = isset($_POST['page']) ? (int)$_POST['page'] : 1;
                $pageSize = isset($_POST['pageSize']) ? (int)$_POST['pageSize'] : 1000;
                echo json_encode(executeSql($pdo, $_POST['sql'], $page, $pageSize));
                break;
            case 'get_tables':
                echo json_encode(getTables($pdo));
                break;
            case 'get_table_structure':
                echo json_encode(getTableStructure($pdo, $_POST['table']));
                break;
            case 'get_column_comments':
                echo json_encode(getColumnComments($pdo, $_POST['table']));
                break;

            case 'export_data':
                $format = $_POST['format'] ?? 'csv';
                echo json_encode(exportData($pdo, $_POST['sql'], $format));
                break;
            case 'add_column':
                echo json_encode(addColumn($pdo, $_POST['table'], $_POST['column_data']));
                break;
            case 'modify_column':
                echo json_encode(modifyColumn($pdo, $_POST['table'], $_POST['old_column'], $_POST['column_data']));
                break;
            case 'drop_column':
                echo json_encode(dropColumn($pdo, $_POST['table'], $_POST['column']));
                break;
            case 'insert_data':
                echo json_encode(insertData($pdo, $_POST['table'], $_POST['data']));
                break;
            case 'update_data':
                echo json_encode(updateData($pdo, $_POST['table'], $_POST['data'], $_POST['where']));
                break;
            case 'delete_data':
                echo json_encode(deleteData($pdo, $_POST['table'], $_POST['where']));
                break;
            case 'batch_insert':
                echo json_encode(batchInsert($pdo, $_POST['table'], $_POST['data']));
                break;
            case 'batch_update':
                echo json_encode(batchUpdate($pdo, $_POST['table'], $_POST['data']));
                break;
            case 'batch_delete':
                echo json_encode(batchDelete($pdo, $_POST['table'], $_POST['conditions']));
                break;
            case 'get_db_status':
                echo json_encode(getDatabaseStatus($pdo));
                break;
            case 'test_column_comments':
                // 测试功能：获取指定表的列注释
                $tableName = $_POST['table'] ?? '';
                if ($tableName) {
                    $result = getColumnComments($pdo, $tableName);
                    echo json_encode($result);
                } else {
                    echo json_encode(['success' => false, 'error' => '缺少表名参数']);
                }
                break;
            case 'get_server_time':
                // 获取服务器时间
                try {
                    // 尝试从数据库获取时间
                    $stmt = $pdo->query("SELECT TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS') as server_time FROM DUAL");
                    $result = $stmt->fetch();
                    if ($result && isset($result['SERVER_TIME'])) {
                        echo json_encode(['success' => true, 'server_time' => $result['SERVER_TIME']]);
                    } else {
                        // 如果数据库查询失败，使用PHP服务器时间
                        echo json_encode(['success' => true, 'server_time' => date('Y-m-d H:i:s')]);
                    }
                } catch (Exception $e) {
                    // 出错时使用PHP服务器时间
                    echo json_encode(['success' => true, 'server_time' => date('Y-m-d H:i:s')]);
                }
                break;
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    exit;
}

// 执行SQL函数
function executeSql($pdo, $sql, $page = 1, $pageSize = 50) {
    $startTime = microtime(true);

    try {
        $sql = trim($sql);
        if (empty($sql)) {
            return ['success' => false, 'error' => 'SQL语句不能为空'];
        }

        // 判断SQL类型
        $sqlType = strtoupper(substr(ltrim($sql), 0, 6));

        if (in_array($sqlType, ['SELECT', 'SHOW', 'DESC', 'EXPLAI'])) {
            // 查询类SQL
            if ($pageSize > 0 && $page > 0) {
                // 分页查询 - 先获取总数
                try {
                    $countSql = "SELECT COUNT(*) as TOTAL FROM (" . $sql . ") t";
                    $countStmt = $pdo->prepare($countSql);
                    $countStmt->execute();
                    $totalRows = $countStmt->fetch()['TOTAL'];

                    // 计算分页信息
                    $totalPages = max(1, ceil($totalRows / $pageSize)); // 至少1页
                    $startRow = ($page - 1) * $pageSize + 1;
                    $endRow = $page * $pageSize;

                    // 构建分页SQL (达梦数据库使用ROWNUM)
                    if ($totalRows > 0) {
                        $pagedSql = "SELECT * FROM (
                            SELECT t.*, ROWNUM as rn FROM (
                                " . $sql . "
                            ) t WHERE ROWNUM <= " . $endRow . "
                        ) WHERE rn >= " . $startRow;
                    } else {
                        // 即使没有数据，也执行原SQL以获取列信息
                        $pagedSql = $sql;
                    }
                } catch (Exception $e) {
                    // 如果分页查询失败，回退到简单查询
                    $pagedSql = $sql;
                    $totalRows = 0;
                    $totalPages = 1;
                }
            } else {
                // 不分页，直接执行原SQL
                $pagedSql = $sql;
                $totalRows = 0;
                $totalPages = 1;
            }

            $stmt = $pdo->prepare($pagedSql);
            $stmt->execute();
            $result = $stmt->fetchAll();
            $rowCount = count($result);
            $columns = [];

            if ($rowCount > 0) {
                $allColumns = array_keys($result[0]);

                // 检查是否有ROWNUM列（分页查询会有）
                $hasRownum = in_array('RN', array_map('strtoupper', $allColumns));

                if ($hasRownum) {
                    // 过滤掉ROWNUM列
                    $columns = array_filter($allColumns, function($col) {
                        return strtoupper($col) !== 'RN';
                    });
                    $columns = array_values($columns); // 重新索引

                    // 从结果中移除ROWNUM列
                    foreach ($result as &$row) {
                        unset($row['RN']);
                        unset($row['rn']);
                    }
                } else {
                    // 没有ROWNUM列，直接使用所有列
                    $columns = $allColumns;
                }
            }

            // 只有在分页时才返回分页信息
            $pagination = null;
            if ($pageSize > 0 && $page > 0 && isset($totalRows)) {
                // 即使总行数为0，也要返回分页信息以显示分页控件
                $pagination = [
                    'currentPage' => $page,
                    'pageSize' => $pageSize,
                    'totalRows' => $totalRows,
                    'totalPages' => max(1, $totalPages), // 至少1页
                    'hasNext' => $page < $totalPages && $totalRows > 0,
                    'hasPrev' => $page > 1
                ];
            }

        } else {
            // 增删改类SQL
            $stmt = $pdo->prepare($sql);
            $stmt->execute();
            $rowCount = $stmt->rowCount();
            $result = [];
            $columns = [];
            $pagination = null;
        }

        $endTime = microtime(true);
        $executionTime = round(($endTime - $startTime) * 1000, 2);

        return [
            'success' => true,
            'data' => $result,
            'columns' => $columns,
            'rowCount' => $rowCount,
            'executionTime' => $executionTime,
            'sqlType' => $sqlType,
            'pagination' => $pagination
        ];

    } catch (Exception $e) {
        // 记录详细错误信息
        $errorInfo = [
            'message' => $e->getMessage(),
            'code' => $e->getCode(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'sql' => $sql ?? 'N/A',
            'sqlType' => $sqlType ?? 'N/A'
        ];

        // 如果是分页查询失败，尝试简单查询
        if (isset($pagedSql) && $pagedSql !== $sql) {
            try {
                $stmt = $pdo->prepare($sql);
                $stmt->execute();
                $result = $stmt->fetchAll();
                $rowCount = count($result);
                $columns = [];

                if ($rowCount > 0) {
                    $columns = array_keys($result[0]);
                }

                $endTime = microtime(true);
                $executionTime = round(($endTime - $startTime) * 1000, 2);

                return [
                    'success' => true,
                    'data' => $result,
                    'columns' => $columns,
                    'rowCount' => $rowCount,
                    'executionTime' => $executionTime,
                    'sqlType' => $sqlType,
                    'pagination' => null,
                    'warning' => '分页查询失败，已回退到简单查询'
                ];
            } catch (Exception $e2) {
                $errorInfo['fallback_error'] = $e2->getMessage();
            }
        }

        return ['success' => false, 'error' => $e->getMessage(), 'debug' => $errorInfo];
    }
}

// 获取所有表
function getTables($pdo) {
    try {
        $sql = "SELECT TABLE_NAME, COMMENTS FROM USER_TAB_COMMENTS ORDER BY TABLE_NAME";
        $stmt = $pdo->query($sql);
        return ['success' => true, 'data' => $stmt->fetchAll()];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// 获取表结构
function getTableStructure($pdo, $tableName) {
    try {
        $sql = "SELECT
                    c.COLUMN_NAME,
                    c.DATA_TYPE,
                    c.DATA_LENGTH,
                    c.NULLABLE,
                    c.DATA_DEFAULT,
                    c.COLUMN_ID,
                    cc.COMMENTS as COLUMN_COMMENT
                FROM USER_TAB_COLUMNS c
                LEFT JOIN USER_COL_COMMENTS cc ON c.TABLE_NAME = cc.TABLE_NAME AND c.COLUMN_NAME = cc.COLUMN_NAME
                WHERE c.TABLE_NAME = ?
                ORDER BY c.COLUMN_ID";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([strtoupper($tableName)]);
        $data = $stmt->fetchAll();

        return [
            'success' => true,
            'data' => $data,
            'debug' => [
                'table' => $tableName,
                'upperTable' => strtoupper($tableName),
                'rowCount' => count($data),
                'sql' => $sql
            ]
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage(),
            'debug' => [
                'table' => $tableName,
                'sql' => $sql ?? 'N/A'
            ]
        ];
    }
}

// 获取列注释信息
function getColumnComments($pdo, $tableName) {
    try {
        // 尝试多种方式获取列注释
        $sql = "SELECT COLUMN_NAME, COMMENTS
                FROM USER_COL_COMMENTS
                WHERE TABLE_NAME = ? AND COMMENTS IS NOT NULL";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([strtoupper($tableName)]);
        $comments = [];
        foreach ($stmt->fetchAll() as $row) {
            if (!empty($row['COMMENTS'])) {
                $comments[$row['COLUMN_NAME']] = $row['COMMENTS'];
            }
        }

        // 如果没有找到注释，尝试从ALL_COL_COMMENTS查询
        if (empty($comments)) {
            try {
                $sql2 = "SELECT COLUMN_NAME, COMMENTS
                        FROM ALL_COL_COMMENTS
                        WHERE TABLE_NAME = ? AND OWNER = USER AND COMMENTS IS NOT NULL";
                $stmt2 = $pdo->prepare($sql2);
                $stmt2->execute([strtoupper($tableName)]);
                foreach ($stmt2->fetchAll() as $row) {
                    if (!empty($row['COMMENTS'])) {
                        $comments[$row['COLUMN_NAME']] = $row['COMMENTS'];
                    }
                }
            } catch (Exception $e2) {
                // 忽略这个错误，继续使用空的comments数组
            }
        }

        return ['success' => true, 'data' => $comments, 'debug' => ['table' => $tableName, 'count' => count($comments)]];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}



// 导出数据
function exportData($pdo, $sql, $format = 'csv') {
    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $data = $stmt->fetchAll();

        if (empty($data)) {
            return ['success' => false, 'error' => '没有数据可导出'];
        }

        $columns = array_keys($data[0]);
        $timestamp = date('Y-m-d_H-i-s');

        switch (strtolower($format)) {
            case 'csv':
                return exportToCsv($data, $columns, $timestamp);
            case 'json':
                return exportToJson($data, $timestamp);
            case 'excel':
                return exportToExcel($data, $columns, $timestamp);
            case 'xml':
                return exportToXml($data, $columns, $timestamp);
            default:
                return ['success' => false, 'error' => '不支持的导出格式'];
        }
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// 导出为CSV格式
function exportToCsv($data, $columns, $timestamp) {
    // 创建Excel格式的CSV内容，添加BOM以支持UTF-8
    $csv = "\xEF\xBB\xBF"; // UTF-8 BOM

    // 添加表头
    $csv .= implode(',', array_map(function($column) {
        return '"' . str_replace('"', '""', $column) . '"';
    }, $columns)) . "\r\n";

    // 添加数据行
    foreach ($data as $row) {
        $csv .= implode(',', array_map(function($value) {
            if ($value === null) return '""';
            $value = (string)$value;
            $value = str_replace('"', '""', $value);
            return '"' . $value . '"';
        }, $row)) . "\r\n";
    }

    return [
        'success' => true,
        'data' => base64_encode($csv),
        'filename' => "export_{$timestamp}.csv",
        'type' => 'csv',
        'mimeType' => 'text/csv'
    ];
}

// 导出为JSON格式
function exportToJson($data, $timestamp) {
    $json = json_encode([
        'export_time' => date('Y-m-d H:i:s'),
        'total_records' => count($data),
        'data' => $data
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

    return [
        'success' => true,
        'data' => base64_encode($json),
        'filename' => "export_{$timestamp}.json",
        'type' => 'json',
        'mimeType' => 'application/json'
    ];
}

// 导出为Excel格式（HTML表格）
function exportToExcel($data, $columns, $timestamp) {
    $html = '<!DOCTYPE html><html><head><meta charset="UTF-8"><title>数据导出</title></head><body>';
    $html .= '<table border="1" style="border-collapse: collapse;">';

    // 表头
    $html .= '<thead><tr>';
    foreach ($columns as $column) {
        $html .= '<th style="background-color: #f0f0f0; padding: 8px;">' . htmlspecialchars($column) . '</th>';
    }
    $html .= '</tr></thead>';

    // 数据行
    $html .= '<tbody>';
    foreach ($data as $row) {
        $html .= '<tr>';
        foreach ($columns as $column) {
            $value = $row[$column] ?? '';
            $html .= '<td style="padding: 8px;">' . htmlspecialchars($value) . '</td>';
        }
        $html .= '</tr>';
    }
    $html .= '</tbody></table></body></html>';

    return [
        'success' => true,
        'data' => base64_encode($html),
        'filename' => "export_{$timestamp}.xls",
        'type' => 'excel',
        'mimeType' => 'application/vnd.ms-excel'
    ];
}

// 导出为XML格式
function exportToXml($data, $columns, $timestamp) {
    $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    $xml .= '<export>' . "\n";
    $xml .= '<metadata>' . "\n";
    $xml .= '<export_time>' . date('Y-m-d H:i:s') . '</export_time>' . "\n";
    $xml .= '<total_records>' . count($data) . '</total_records>' . "\n";
    $xml .= '</metadata>' . "\n";
    $xml .= '<data>' . "\n";

    foreach ($data as $row) {
        $xml .= '<record>' . "\n";
        foreach ($columns as $column) {
            $value = $row[$column] ?? '';
            $xml .= '<' . $column . '>' . htmlspecialchars($value) . '</' . $column . '>' . "\n";
        }
        $xml .= '</record>' . "\n";
    }

    $xml .= '</data>' . "\n";
    $xml .= '</export>';

    return [
        'success' => true,
        'data' => base64_encode($xml),
        'filename' => "export_{$timestamp}.xml",
        'type' => 'xml',
        'mimeType' => 'application/xml'
    ];
}

// 添加字段
function addColumn($pdo, $tableName, $columnData) {
    try {
        $columnData = json_decode($columnData, true);
        $columnName = $columnData['name'];
        $dataType = $columnData['type'];
        $length = $columnData['length'] ?? '';
        $nullable = $columnData['nullable'] ?? 'Y';
        $defaultValue = $columnData['default'] ?? '';

        // 构建ALTER TABLE语句
        $sql = "ALTER TABLE " . strtoupper($tableName) . " ADD " . strtoupper($columnName) . " " . strtoupper($dataType);

        if ($length && in_array(strtoupper($dataType), ['VARCHAR', 'VARCHAR2', 'CHAR', 'NUMBER'])) {
            $sql .= "(" . $length . ")";
        }

        if ($nullable === 'N') {
            $sql .= " NOT NULL";
        }

        if ($defaultValue) {
            $sql .= " DEFAULT " . (is_numeric($defaultValue) ? $defaultValue : "'" . $defaultValue . "'");
        }

        $pdo->exec($sql);
        return ['success' => true, 'message' => '字段添加成功'];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// 修改字段
function modifyColumn($pdo, $tableName, $oldColumn, $columnData) {
    try {
        $columnData = json_decode($columnData, true);
        $columnName = $columnData['name'];
        $dataType = $columnData['type'];
        $length = $columnData['length'] ?? '';
        $nullable = $columnData['nullable'] ?? 'Y';
        $defaultValue = $columnData['default'] ?? '';

        // 注意：$oldColumn参数保留用于将来可能的字段重命名功能
        // 当前版本中暂未使用，但保留接口兼容性
        // 构建ALTER TABLE MODIFY语句
        $sql = "ALTER TABLE " . strtoupper($tableName) . " MODIFY " . strtoupper($columnName) . " " . strtoupper($dataType);

        if ($length && in_array(strtoupper($dataType), ['VARCHAR', 'VARCHAR2', 'CHAR', 'NUMBER'])) {
            $sql .= "(" . $length . ")";
        }

        if ($nullable === 'N') {
            $sql .= " NOT NULL";
        } else {
            $sql .= " NULL";
        }

        if ($defaultValue) {
            $sql .= " DEFAULT " . (is_numeric($defaultValue) ? $defaultValue : "'" . $defaultValue . "'");
        }

        $pdo->exec($sql);
        return ['success' => true, 'message' => '字段修改成功'];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// 删除字段
function dropColumn($pdo, $tableName, $columnName) {
    try {
        $sql = "ALTER TABLE " . strtoupper($tableName) . " DROP COLUMN " . strtoupper($columnName);
        $pdo->exec($sql);
        return ['success' => true, 'message' => '字段删除成功'];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// 插入数据
function insertData($pdo, $tableName, $data) {
    try {
        $data = json_decode($data, true);
        $columns = array_keys($data);
        $placeholders = array_fill(0, count($columns), '?');

        $sql = "INSERT INTO " . strtoupper($tableName) . " (" . implode(', ', array_map('strtoupper', $columns)) . ") VALUES (" . implode(', ', $placeholders) . ")";
        $stmt = $pdo->prepare($sql);
        $stmt->execute(array_values($data));

        return ['success' => true, 'message' => '数据插入成功'];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// 更新数据
function updateData($pdo, $tableName, $data, $where) {
    try {
        $data = json_decode($data, true);
        $where = json_decode($where, true);

        $setParts = [];
        $values = [];
        foreach ($data as $column => $value) {
            $setParts[] = strtoupper($column) . " = ?";
            $values[] = $value;
        }

        $whereParts = [];
        foreach ($where as $column => $value) {
            $whereParts[] = strtoupper($column) . " = ?";
            $values[] = $value;
        }

        $sql = "UPDATE " . strtoupper($tableName) . " SET " . implode(', ', $setParts) . " WHERE " . implode(' AND ', $whereParts);
        $stmt = $pdo->prepare($sql);
        $stmt->execute($values);

        return ['success' => true, 'message' => '数据更新成功', 'affected_rows' => $stmt->rowCount()];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// 删除数据
function deleteData($pdo, $tableName, $where) {
    try {
        $where = json_decode($where, true);

        $whereParts = [];
        $values = [];
        foreach ($where as $column => $value) {
            $whereParts[] = strtoupper($column) . " = ?";
            $values[] = $value;
        }

        $sql = "DELETE FROM " . strtoupper($tableName) . " WHERE " . implode(' AND ', $whereParts);
        $stmt = $pdo->prepare($sql);
        $stmt->execute($values);

        return ['success' => true, 'message' => '数据删除成功', 'affected_rows' => $stmt->rowCount()];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// 批量插入数据
function batchInsert($pdo, $tableName, $data) {
    try {
        $data = json_decode($data, true);
        if (empty($data)) {
            return ['success' => false, 'error' => '没有数据可插入'];
        }

        $columns = array_keys($data[0]);
        $placeholders = array_fill(0, count($columns), '?');

        $sql = "INSERT INTO " . strtoupper($tableName) . " (" . implode(', ', array_map('strtoupper', $columns)) . ") VALUES (" . implode(', ', $placeholders) . ")";
        $stmt = $pdo->prepare($sql);

        $pdo->beginTransaction();
        $successCount = 0;
        foreach ($data as $row) {
            $stmt->execute(array_values($row));
            $successCount++;
        }
        $pdo->commit();

        return ['success' => true, 'message' => "批量插入成功，共插入 {$successCount} 条记录"];
    } catch (Exception $e) {
        $pdo->rollBack();
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// 批量更新数据
function batchUpdate($pdo, $tableName, $data) {
    try {
        $data = json_decode($data, true);
        if (empty($data)) {
            return ['success' => false, 'error' => '没有数据可更新'];
        }

        $pdo->beginTransaction();
        $successCount = 0;

        foreach ($data as $item) {
            $updateData = $item['data'];
            $whereData = $item['where'];

            $setParts = [];
            $values = [];
            foreach ($updateData as $column => $value) {
                $setParts[] = strtoupper($column) . " = ?";
                $values[] = $value;
            }

            $whereParts = [];
            foreach ($whereData as $column => $value) {
                $whereParts[] = strtoupper($column) . " = ?";
                $values[] = $value;
            }

            $sql = "UPDATE " . strtoupper($tableName) . " SET " . implode(', ', $setParts) . " WHERE " . implode(' AND ', $whereParts);
            $stmt = $pdo->prepare($sql);
            $stmt->execute($values);
            $successCount++;
        }

        $pdo->commit();
        return ['success' => true, 'message' => "批量更新成功，共更新 {$successCount} 条记录"];
    } catch (Exception $e) {
        $pdo->rollBack();
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// 批量删除数据
function batchDelete($pdo, $tableName, $conditions) {
    try {
        $conditions = json_decode($conditions, true);
        if (empty($conditions)) {
            return ['success' => false, 'error' => '没有删除条件'];
        }

        $pdo->beginTransaction();
        $totalAffected = 0;

        foreach ($conditions as $where) {
            $whereParts = [];
            $values = [];
            foreach ($where as $column => $value) {
                $whereParts[] = strtoupper($column) . " = ?";
                $values[] = $value;
            }

            $sql = "DELETE FROM " . strtoupper($tableName) . " WHERE " . implode(' AND ', $whereParts);
            $stmt = $pdo->prepare($sql);
            $stmt->execute($values);
            $totalAffected += $stmt->rowCount();
        }

        $pdo->commit();
        return ['success' => true, 'message' => "批量删除成功，共删除 {$totalAffected} 条记录"];
    } catch (Exception $e) {
        $pdo->rollBack();
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// 获取数据库状态信息
function getDatabaseStatus($pdo) {
    try {
        $status = [];

        // 数据库基本信息
        $stmt = $pdo->query("SELECT BANNER FROM V\$VERSION WHERE ROWNUM = 1");
        $version = $stmt->fetch();
        $status['version'] = $version ? $version['BANNER'] : '未知版本';

        // 数据库实例信息
        $stmt = $pdo->query("SELECT INSTANCE_NAME, STATUS, STARTUP_TIME FROM V\$INSTANCE");
        $instance = $stmt->fetch();
        if ($instance) {
            $status['instance_name'] = $instance['INSTANCE_NAME'];
            $status['instance_status'] = $instance['STATUS'];
            $status['startup_time'] = $instance['STARTUP_TIME'];
        }

        // 会话信息
        $stmt = $pdo->query("SELECT COUNT(*) as session_count FROM V\$SESSION WHERE STATUS = 'ACTIVE'");
        $sessions = $stmt->fetch();
        $status['active_sessions'] = $sessions['SESSION_COUNT'];

        // 表空间信息
        $stmt = $pdo->query("SELECT COUNT(*) as tablespace_count FROM DBA_TABLESPACES");
        $tablespaces = $stmt->fetch();
        $status['tablespace_count'] = $tablespaces['TABLESPACE_COUNT'];

        // 用户表数量
        $stmt = $pdo->query("SELECT COUNT(*) as table_count FROM USER_TABLES");
        $tables = $stmt->fetch();
        $status['user_table_count'] = $tables['TABLE_COUNT'];

        // 当前时间
        $stmt = $pdo->query("SELECT SYSDATE as current_time FROM DUAL");
        $time = $stmt->fetch();
        $status['current_time'] = $time['CURRENT_TIME'];

        // 连接状态
        $status['connection_status'] = 'connected';
        $status['last_check'] = date('Y-m-d H:i:s');

        return ['success' => true, 'data' => $status];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>达梦数据库SQL查询分析器</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; background: #f5f5f5; }
        
        .container { width: 100%; margin: 0; padding: 10px; }
        .header { background: #2c3e50; color: white; padding: 15px; border-radius: 8px; margin-bottom: 20px; position: relative; }
        .header h1 { font-size: 24px; }
        .logout-btn { position: absolute; top: 15px; right: 15px; background: #e74c3c; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer; }
        .logout-btn:hover { background: #c0392b; }
        
        .main-content { display: grid; grid-template-columns: 250px 1fr; gap: 20px; }
        
        .sidebar { background: white; border-radius: 8px; padding: 15px; height: fit-content; }
        .sidebar h3 { color: #2c3e50; margin-bottom: 15px; border-bottom: 2px solid #3498db; padding-bottom: 5px; }
        .table-list { max-height: 400px; overflow-y: auto; }
        .table-item { padding: 8px; cursor: pointer; border-radius: 4px; margin-bottom: 5px; }
        .table-item:hover { background: #ecf0f1; }
        .table-item.active { background: #3498db; color: white; }
        
        .content { background: white; border-radius: 8px; padding: 20px; }
        
        .sql-editor { margin-bottom: 20px; position: relative; }
        .sql-textarea { width: 100%; height: 200px; padding: 15px; border: 1px solid #ddd; border-radius: 4px;
                       font-family: 'Courier New', monospace; font-size: 14px; resize: vertical;
                       background: #f8f9fa; line-height: 1.4; }
        .sql-textarea:focus { outline: none; border-color: #3498db; background: white; }

        .sql-editor-toolbar { margin-bottom: 10px; display: flex; gap: 10px; align-items: center; }
        .sql-editor-toolbar .btn { padding: 6px 12px; font-size: 12px; }

        .sql-suggestions { position: absolute; top: 100%; left: 0; right: 0; background: white; border: 1px solid #ddd;
                          border-top: none; max-height: 200px; overflow-y: auto; z-index: 1000; display: none; }
        .sql-suggestion-item { padding: 8px 12px; cursor: pointer; border-bottom: 1px solid #eee; }
        .sql-suggestion-item:hover { background: #f5f5f5; }
        .sql-suggestion-item.active { background: #3498db; color: white; }

        .sql-keyword { color: #0066cc; font-weight: bold; }
        .sql-string { color: #009900; }
        .sql-number { color: #cc6600; }
        .sql-comment { color: #999999; font-style: italic; }
        
        .toolbar { margin: 15px 0; display: flex; gap: 10px; flex-wrap: wrap; }
        .btn { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; }
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn:hover { opacity: 0.9; }
        .btn:disabled { opacity: 0.6; cursor: not-allowed; }
        
        .result-area { margin-top: 20px; }
        .tabs { display: flex; border-bottom: 1px solid #ddd; margin-bottom: 15px; }
        .tab { padding: 10px 20px; cursor: pointer; border-bottom: 2px solid transparent; }
        .tab.active { border-bottom-color: #3498db; color: #3498db; }
        
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        
        .result-table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        .result-table th, .result-table td { padding: 8px 12px; border: 1px solid #ddd; text-align: left; position: relative; }
        .result-table th { background: #f8f9fa; font-weight: bold; }
        .result-table tr:nth-child(even) { background: #f8f9fa; }
        .result-table tr:hover { background: #e3f2fd; }
        .result-table tr.selected { background: #bbdefb; }

        .data-toolbar { margin: 10px 0; display: flex; gap: 10px; flex-wrap: wrap; align-items: center; }
        .data-toolbar .btn { padding: 6px 12px; font-size: 12px; }

        .column-filter {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            padding: 8px;
            z-index: 100;
            display: none;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border-radius: 4px;
            min-width: 200px;
        }
        .column-filter input, .column-filter select {
            width: 100%;
            padding: 4px 6px;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-size: 12px;
        }
        .column-filter input:focus, .column-filter select:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }
        .filter-active {
            background-color: #e3f2fd !important;
            position: relative;
        }
        .filter-active::after {
            content: '●';
            position: absolute;
            top: 2px;
            right: 2px;
            color: #2196f3;
            font-size: 8px;
        }

        .row-checkbox { margin-right: 5px; }
        .row-actions { opacity: 0; transition: opacity 0.2s; }
        .result-table tr:hover .row-actions { opacity: 1; }
        .row-actions button { padding: 2px 6px; margin-left: 2px; font-size: 10px; border: none; border-radius: 2px; cursor: pointer; }

        .batch-actions { background: #e3f2fd; padding: 10px; border-radius: 4px; margin: 10px 0; display: none; }
        .batch-info { color: #1976d2; font-weight: bold; margin-bottom: 10px; }

        .pagination-container { margin: 15px 0; display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; }
        .pagination-info { color: #666; font-size: 14px; }
        .pagination-controls { display: flex; align-items: center; gap: 10px; }
        .pagination-nav { display: flex; gap: 5px; }
        .pagination-nav button { padding: 6px 12px; border: 1px solid #ddd; background: white; cursor: pointer; border-radius: 3px; }
        .pagination-nav button:hover { background: #f5f5f5; }
        .pagination-nav button:disabled { background: #f9f9f9; color: #ccc; cursor: not-allowed; }
        .pagination-nav button.active { background: #3498db; color: white; border-color: #3498db; }
        .page-size-selector { display: flex; align-items: center; gap: 5px; }
        .page-size-selector select { padding: 4px 8px; border: 1px solid #ddd; border-radius: 3px; }

        .db-status { display: flex; align-items: center; gap: 8px; padding: 8px 12px; background: rgba(255,255,255,0.1); border-radius: 4px; }
        .status-indicator { font-size: 12px; }
        .status-indicator.connected { color: #27ae60; }
        .status-indicator.disconnected { color: #e74c3c; }
        .status-indicator.checking { color: #f39c12; }

        .db-info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0; }
        .db-info-card { background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #3498db; }
        .db-info-card h4 { margin: 0 0 10px 0; color: #2c3e50; }
        .db-info-item { display: flex; justify-content: space-between; margin: 5px 0; }
        .db-info-label { font-weight: bold; color: #34495e; }
        .db-info-value { color: #2c3e50; }
        
        .info-panel { background: #ecf0f1; padding: 15px; border-radius: 4px; margin-bottom: 15px; }
        .info-item { margin-bottom: 5px; }
        .info-label { font-weight: bold; color: #2c3e50; }
        
        .error { background: #e74c3c; color: white; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .success { background: #27ae60; color: white; padding: 15px; border-radius: 4px; margin: 10px 0; }
        
        .loading { text-align: center; padding: 20px; color: #7f8c8d; }
        .loading-spinner { display: inline-block; width: 20px; height: 20px; border: 3px solid #f3f3f3; border-top: 3px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite; margin-right: 10px; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }

        .toast { position: fixed; top: 20px; right: 20px; padding: 15px 20px; border-radius: 6px; color: white; z-index: 10000; opacity: 0; transform: translateX(100%); transition: all 0.3s ease; }
        .toast.show { opacity: 1; transform: translateX(0); }
        .toast.success { background: #27ae60; }
        .toast.error { background: #e74c3c; }
        .toast.warning { background: #f39c12; }
        .toast.info { background: #3498db; }

        .confirm-dialog { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10000; display: flex; align-items: center; justify-content: center; }
        .confirm-content { background: white; padding: 30px; border-radius: 8px; max-width: 400px; text-align: center; }
        .confirm-buttons { margin-top: 20px; display: flex; gap: 10px; justify-content: center; }

        .progress-bar { width: 100%; height: 4px; background: #ecf0f1; border-radius: 2px; overflow: hidden; margin: 10px 0; }
        .progress-fill { height: 100%; background: #3498db; width: 0%; transition: width 0.3s ease; }
        
        .table-structure { max-height: 400px; overflow-y: auto; }

        .structure-toolbar { margin-bottom: 15px; display: flex; gap: 10px; flex-wrap: wrap; }
        .structure-toolbar .btn { padding: 6px 12px; font-size: 12px; }

        .column-row { position: relative; }
        .column-row:hover .column-actions { opacity: 1; }
        .column-actions { opacity: 0; transition: opacity 0.2s; position: absolute; right: 5px; top: 50%; transform: translateY(-50%); }
        .column-actions button { padding: 2px 6px; margin-left: 2px; font-size: 11px; border: none; border-radius: 2px; cursor: pointer; }
        .btn-edit { background: #f39c12; color: white; }
        .btn-delete { background: #e74c3c; color: white; }

        .modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); }
        .modal-content {
            background-color: white;
            margin: 2% auto;
            padding: 20px;
            border-radius: 8px;
            width: 600px;
            max-width: 90%;
            max-height: 90vh;
            position: relative;
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        .modal-content.draggable { cursor: move; }

        /* 拖拽相关样式 */
        .modal-header[style*="cursor: move"] {
            user-select: none;
        }

        .modal-header[style*="cursor: grabbing"] {
            user-select: none;
        }
        .modal-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; cursor: move; }
        .modal-header h3 { margin: 0; color: #2c3e50; }
        .close { color: #aaa; font-size: 28px; font-weight: bold; cursor: pointer; }
        .close:hover { color: #000; }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 15px;
            max-height: 60vh;
            overflow-y: auto;
            padding-right: 10px;
        }
        .form-grid .form-row { margin-bottom: 0; }

        .form-row { margin-bottom: 15px; }
        .form-row label { display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50; }
        .form-row input, .form-row select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .form-row input:focus, .form-row select:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }
        .form-row input[readonly] {
            background-color: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
        }

        /* 必填字段标识 */
        .form-row label span[style*="color: red"] {
            font-weight: bold;
        }

        /* 字段类型提示样式 */
        .form-row small {
            display: block;
            margin-bottom: 5px;
            color: #6c757d;
            font-size: 11px;
            font-style: italic;
        }

        /* 字段注释样式 */
        .form-row label {
            position: relative;
        }

        .form-row label small {
            color: #999;
            font-size: 10px;
            font-weight: normal;
            font-style: normal;
            margin-top: 2px;
            margin-bottom: 0;
        }

        /* 字段标签悬停效果 */
        .form-row label[title]:hover {
            color: #007bff;
            cursor: help;
        }

        /* 输入框悬停显示完整信息 */
        .form-row input[title]:hover {
            border-color: #007bff;
        }

        /* 改善复选框样式 */
        .form-row input[type="checkbox"] {
            width: auto;
            margin-right: 8px;
            transform: scale(1.1);
        }

        /* 模态框头部样式改进 */
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 15px;
            border-bottom: 1px solid #e9ecef;
            margin-bottom: 20px;
        }

        .modal-header h3 {
            margin: 0;
            color: #2c3e50;
            font-size: 18px;
        }

        .modal-header .close {
            font-size: 24px;
            font-weight: bold;
            color: #aaa;
            cursor: pointer;
            border: none;
            background: none;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s;
        }

        .modal-header .close:hover {
            color: #000;
            background-color: #f8f9fa;
        }

        /* 模态框底部工具栏固定样式 */
        .modal-footer {
            position: sticky;
            bottom: 0;
            background: white;
            padding: 15px 0 0 0;
            margin-top: 20px;
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        /* 滚动条样式优化 */
        .form-grid::-webkit-scrollbar {
            width: 8px;
        }
        .form-grid::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }
        .form-grid::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }
        .form-grid::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content { grid-template-columns: 1fr; }
            .toolbar { flex-direction: column; }
            .modal-content {
                width: 95%;
                margin: 2% auto;
                max-height: 95vh;
                padding: 15px;
            }
            .form-grid {
                grid-template-columns: 1fr;
                max-height: 60vh;
            }
            .modal-footer {
                flex-direction: column-reverse;
                gap: 8px;
            }
            .modal-footer button {
                width: 100%;
            }
        }

        @media (max-width: 480px) {
            .modal-content {
                width: 98%;
                margin: 1% auto;
                padding: 10px;
            }
            .form-grid {
                max-height: 65vh;
            }
            .form-row input, .form-row select {
                font-size: 16px; /* 防止iOS缩放 */
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h1>达梦数据库SQL查询分析器</h1>
                    <p>支持增删查改操作，数据管理分析</p>
                </div>
                <div style="display: flex; gap: 15px; align-items: center;">
                    <div id="dbStatus" class="db-status">
                        <span class="status-indicator" id="statusIndicator">●</span>
                        <span id="statusText">检查中...</span>
                    </div>
                    <button class="btn btn-info" onclick="showDbStatusModal()" style="padding: 6px 12px;">系统信息</button>
                    <button class="logout-btn" onclick="logout()">退出登录</button>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="sidebar">
                <h3>数据库表</h3>
                <div class="search-box" style="margin-bottom: 15px;">
                    <input type="text" id="tableSearch" placeholder="搜索表名或注释..."
                           style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                </div>
                <div class="table-list" id="tableList">
                    <div class="loading">加载中...</div>
                </div>
                
                <h3 style="margin-top: 20px;">快速操作</h3>
                <div class="toolbar" style="flex-direction: column;">
                    <button class="btn btn-primary" onclick="insertTemplate()">INSERT模板</button>
                    <button class="btn btn-success" onclick="updateTemplate()">UPDATE模板</button>
                    <button class="btn btn-warning" onclick="deleteTemplate()">DELETE模板</button>
                    <button class="btn btn-primary" onclick="selectTemplate()">SELECT模板</button>
                    <button class="btn btn-info" onclick="testTableStructure()" style="font-size: 11px; margin-top: 10px;">测试表结构</button>
                </div>
            </div>
            
            <div class="content">
                <div class="sql-editor">
                    <div class="sql-editor-toolbar">
                        <button class="btn btn-info" onclick="formatSql()">格式化SQL</button>
                        <button class="btn btn-success" onclick="showSqlHistory()">查询历史</button>
                        <button class="btn btn-warning" onclick="saveSqlTemplate()">保存模板</button>
                        <button class="btn btn-secondary" onclick="clearSqlEditor()">清空</button>
                        <span style="margin-left: auto; color: #666; font-size: 12px;">
                            快捷键: F5执行 | Ctrl+Enter执行 | Ctrl+/ 注释
                        </span>
                    </div>
                    <div style="position: relative;">
                        <textarea id="sqlTextarea" class="sql-textarea" placeholder="请输入SQL语句...">SELECT SYSDATE FROM DUAL</textarea>
                        <div id="sqlSuggestions" class="sql-suggestions"></div>
                    </div>
                </div>
                
                <div class="toolbar">
                    <button class="btn btn-primary" onclick="executeSql()">分页查询 (F5)</button>
                    <button class="btn btn-success" onclick="executeSimpleSql()">简单查询</button>
                    <div class="btn-group" style="position: relative; display: inline-block;">
                        <button class="btn btn-warning" onclick="showExportOptions()">导出数据 ▼</button>
                        <div id="exportDropdown" class="dropdown-menu" style="display: none; position: absolute; top: 100%; left: 0; background: white; border: 1px solid #ddd; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); z-index: 1000; min-width: 150px;">
                            <a href="#" onclick="exportData('csv')" style="display: block; padding: 8px 12px; text-decoration: none; color: #333; border-bottom: 1px solid #eee;">CSV格式</a>
                            <a href="#" onclick="exportData('json')" style="display: block; padding: 8px 12px; text-decoration: none; color: #333; border-bottom: 1px solid #eee;">JSON格式</a>
                            <a href="#" onclick="exportData('excel')" style="display: block; padding: 8px 12px; text-decoration: none; color: #333; border-bottom: 1px solid #eee;">Excel格式</a>
                            <a href="#" onclick="exportData('xml')" style="display: block; padding: 8px 12px; text-decoration: none; color: #333;">XML格式</a>
                        </div>
                    </div>
                    <button class="btn btn-danger" onclick="clearResult()">清空结果</button>
                </div>
                
                <div class="result-area">
                    <div class="tabs">
                        <div class="tab active" onclick="switchTab('result')">查询结果</div>
                        <div class="tab" onclick="switchTab('structure')">表结构</div>
                    </div>
                    
                    <div id="resultTab" class="tab-content active">
                        <div id="resultInfo" class="info-panel" style="display: none;">
                            <div class="info-item"><span class="info-label">执行时间:</span> <span id="executionTime">-</span></div>
                            <div class="info-item"><span class="info-label">影响行数:</span> <span id="rowCount">-</span></div>
                            <div class="info-item"><span class="info-label">SQL类型:</span> <span id="sqlType">-</span></div>
                        </div>
                        <div id="resultContent"></div>
                    </div>
                    

                    
                    <div id="structureTab" class="tab-content">
                        <div id="structureContent"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 字段编辑模态框 -->
    <div id="columnModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">添加字段</h3>
                <span class="close" onclick="closeColumnModal()">&times;</span>
            </div>
            <form id="columnForm">
                <div class="form-row">
                    <label for="columnName">字段名:</label>
                    <input type="text" id="columnName" name="columnName" required>
                </div>
                <div class="form-row">
                    <label for="columnType">数据类型:</label>
                    <select id="columnType" name="columnType" required>
                        <option value="VARCHAR2">VARCHAR2</option>
                        <option value="NUMBER">NUMBER</option>
                        <option value="DATE">DATE</option>
                        <option value="TIMESTAMP">TIMESTAMP</option>
                        <option value="CHAR">CHAR</option>
                        <option value="CLOB">CLOB</option>
                        <option value="BLOB">BLOB</option>
                        <option value="INTEGER">INTEGER</option>
                    </select>
                </div>
                <div class="form-row">
                    <label for="columnLength">长度:</label>
                    <input type="text" id="columnLength" name="columnLength" placeholder="如: 50 或 10,2">
                </div>
                <div class="form-row">
                    <label for="columnNullable">允许空值:</label>
                    <select id="columnNullable" name="columnNullable">
                        <option value="Y">是</option>
                        <option value="N">否</option>
                    </select>
                </div>
                <div class="form-row">
                    <label for="columnDefault">默认值:</label>
                    <input type="text" id="columnDefault" name="columnDefault" placeholder="可选">
                </div>
                <div class="toolbar">
                    <button type="submit" class="btn btn-primary">保存</button>
                    <button type="button" class="btn btn-danger" onclick="closeColumnModal()">取消</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let currentTable = '';
        
        // 安全的Base64编码函数，支持中文字符
        function safeBase64Encode(str) {
            try {
                // 先将字符串转换为UTF-8字节，再进行base64编码
                return btoa(unescape(encodeURIComponent(str)));
            } catch (error) {
                console.warn('Base64编码失败，使用备用方案:', error);
                // 备用方案：使用索引存储
                const index = 'temp_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                if (!window.tempDataStorage) {
                    window.tempDataStorage = {};
                }
                window.tempDataStorage[index] = str;
                return index;
            }
        }

        // 安全的Base64解码函数
        function safeBase64Decode(encodedStr) {
            try {
                // 如果是临时存储的索引
                if (encodedStr.startsWith('temp_') && window.tempDataStorage && window.tempDataStorage[encodedStr]) {
                    return window.tempDataStorage[encodedStr];
                }
                // 正常的base64解码
                return decodeURIComponent(escape(atob(encodedStr)));
            } catch (error) {
                console.warn('Base64解码失败:', error);
                return encodedStr;
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadTables();
            checkDatabaseStatus(); // 检查数据库状态

            // 定期检查数据库状态
            setInterval(checkDatabaseStatus, 30000); // 每30秒检查一次

            // 绑定搜索框事件
            document.getElementById('tableSearch').addEventListener('input', function(e) {
                filterTables(e.target.value);
            });

            // 绑定快捷键
            document.addEventListener('keydown', function(e) {
                if (e.key === 'F5') {
                    e.preventDefault();
                    executeSql();
                }
                if (e.ctrlKey && e.key === 'Enter') {
                    executeSql();
                }
                if (e.ctrlKey && e.key === '/') {
                    e.preventDefault();
                    toggleSqlComment();
                }
            });

            // 绑定SQL编辑器事件
            const sqlTextarea = document.getElementById('sqlTextarea');
            sqlTextarea.addEventListener('input', handleSqlInput);
            sqlTextarea.addEventListener('keydown', handleSqlKeydown);
            sqlTextarea.addEventListener('blur', hideSqlSuggestions);

            // 处理字段表单提交
            document.getElementById('columnForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                const columnData = {
                    name: formData.get('columnName'),
                    type: formData.get('columnType'),
                    length: formData.get('columnLength'),
                    nullable: formData.get('columnNullable'),
                    default: formData.get('columnDefault')
                };

                const action = currentEditMode === 'add' ? 'add_column' : 'modify_column';
                let body = `action=${action}&table=${currentEditTable}&column_data=${encodeURIComponent(JSON.stringify(columnData))}`;

                if (currentEditMode === 'edit') {
                    body += `&old_column=${currentEditColumn}`;
                }

                fetch('', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                    body: body
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        closeColumnModal();
                        loadTableStructure(currentEditTable);
                    } else {
                        alert('操作失败: ' + data.error);
                    }
                });
            });
        });

        // 编辑行数据
        function editRow(rowIndex) {
            const rowData = window.currentResultData.data[rowIndex];
            const columns = window.currentResultData.columns;

            // 如果有当前表，先获取字段注释，然后创建编辑表单
            if (currentTable) {
                fetchColumnCommentsForEdit(currentTable, rowData, columns, rowIndex);
            } else {
                createEditModal(rowData, columns, {}, rowIndex);
            }
        }

        // 获取字段注释用于编辑
        function fetchColumnCommentsForEdit(tableName, rowData, columns, rowIndex) {
            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: `action=get_column_comments&table=${tableName}`
            })
            .then(response => response.json())
            .then(columnComments => {
                createEditModal(rowData, columns, columnComments || {}, rowIndex);
            })
            .catch(error => {
                console.error('获取字段注释失败:', error);
                createEditModal(rowData, columns, {}, rowIndex);
            });
        }

        // 创建编辑模态框
        function createEditModal(rowData, columns, columnComments, rowIndex) {
            // 创建编辑表单
            let formHtml = '<div class="modal" id="editModal" style="display: block;">';
            formHtml += '<div class="modal-content draggable" id="editModalContent">';
            formHtml += '<div class="modal-header" id="editModalHeader"><h3>编辑数据</h3><span class="close" onclick="closeEditModal()">&times;</span></div>';
            formHtml += '<form id="editForm">';

            // 使用网格布局
            formHtml += '<div class="form-grid">';
            columns.forEach(column => {
                const value = rowData[column] || '';
                const isReadonly = column.includes('ID') || column.includes('_ID');
                const comment = columnComments[column];

                // 构建标签文本
                let labelText = column;
                let titleText = column;
                if (comment && comment.trim()) {
                    labelText = `${comment}`;
                    titleText = `${column}: ${comment}`;
                }

                formHtml += `<div class="form-row">
                    <label title="${titleText}">
                        ${labelText}
                        ${comment && comment.trim() ? `<br><small style="color: #666; font-size: 11px; font-weight: normal;">${column}</small>` : ''}
                        ${isReadonly ? ' <span style="color: #999; font-size: 11px;">(只读)</span>' : ''}
                    </label>
                    <input type="text" name="${column}" value="${value}" ${isReadonly ? 'readonly' : ''} title="${titleText}">
                </div>`;
            });
            formHtml += '</div>';

            // 固定底部工具栏
            formHtml += '<div class="modal-footer">';
            formHtml += '<button type="button" class="btn btn-danger" onclick="closeEditModal()">取消</button>';
            formHtml += '<button type="submit" class="btn btn-primary">保存</button>';
            formHtml += '</div></form></div></div>';

            document.body.insertAdjacentHTML('beforeend', formHtml);

            // 启用拖动功能
            makeDraggable('editModalContent', 'editModalHeader');

            // 调整模态框高度
            adjustModalHeight('editModal');

            // 绑定表单提交事件
            document.getElementById('editForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const formData = new FormData(this);
                const updateData = {};
                const whereData = {};

                // 构建更新数据和条件数据
                columns.forEach(column => {
                    const value = formData.get(column);
                    if (column.includes('ID')) {
                        whereData[column] = value; // ID字段作为WHERE条件
                    } else {
                        updateData[column] = value;
                    }
                });

                // 如果没有ID字段，使用所有原始数据作为WHERE条件
                if (Object.keys(whereData).length === 0) {
                    columns.forEach(column => {
                        whereData[column] = rowData[column];
                    });
                }

                updateRowData(updateData, whereData);
            });
        }

        // 更新行数据
        function updateRowData(updateData, whereData) {
            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: `action=update_data&table=${currentTable}&data=${encodeURIComponent(JSON.stringify(updateData))}&where=${encodeURIComponent(JSON.stringify(whereData))}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    closeEditModal();
                    // 重新执行查询以刷新数据
                    executeSql();
                } else {
                    alert('更新失败: ' + data.error);
                }
            });
        }

        // 删除行数据
        function deleteRow(rowIndex) {
            if (!confirm('确定要删除这条数据吗？')) return;

            const rowData = window.currentResultData.data[rowIndex];
            const columns = window.currentResultData.columns;
            const whereData = {};

            // 构建WHERE条件
            columns.forEach(column => {
                if (rowData[column] !== null) {
                    whereData[column] = rowData[column];
                }
            });

            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: `action=delete_data&table=${currentTable}&where=${encodeURIComponent(JSON.stringify(whereData))}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    executeSql(); // 重新执行查询以刷新数据
                } else {
                    alert('删除失败: ' + data.error);
                }
            });
        }

        // 关闭编辑模态框
        function closeEditModal() {
            const modal = document.getElementById('editModal');
            if (modal) {
                modal.remove();
            }
        }

        // 批量编辑
        function batchEdit() {
            const selectedRows = getSelectedRows();
            if (selectedRows.length === 0) {
                alert('请先选择要编辑的数据行');
                return;
            }

            const columns = window.currentResultData.columns;

            // 如果有当前表，先获取字段注释，然后创建批量编辑表单
            if (currentTable) {
                fetchColumnCommentsForBatchEdit(currentTable, columns, selectedRows);
            } else {
                createBatchEditModal(columns, {}, selectedRows);
            }
        }

        // 获取字段注释用于批量编辑
        function fetchColumnCommentsForBatchEdit(tableName, columns, selectedRows) {
            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: `action=get_column_comments&table=${tableName}`
            })
            .then(response => response.json())
            .then(columnComments => {
                createBatchEditModal(columns, columnComments || {}, selectedRows);
            })
            .catch(error => {
                console.error('获取字段注释失败:', error);
                createBatchEditModal(columns, {}, selectedRows);
            });
        }

        // 创建批量编辑模态框
        function createBatchEditModal(columns, columnComments, selectedRows) {
            // 创建批量编辑表单
            let formHtml = '<div class="modal" id="batchEditModal" style="display: block;">';
            formHtml += '<div class="modal-content draggable" id="batchEditModalContent">';
            formHtml += '<div class="modal-header" id="batchEditModalHeader"><h3>批量编辑数据</h3><span class="close" onclick="closeBatchEditModal()">&times;</span></div>';
            formHtml += `<p style="margin-bottom: 15px; padding: 10px; background: #e3f2fd; border-radius: 4px; color: #1976d2;">将对选中的 ${selectedRows.length} 行数据进行批量修改</p>`;
            formHtml += '<form id="batchEditForm">';

            // 使用网格布局
            formHtml += '<div class="form-grid">';
            columns.forEach(column => {
                if (!column.includes('ID') && !column.includes('_ID')) { // 排除ID字段
                    const comment = columnComments[column];

                    // 构建标签文本
                    let labelText = column;
                    let titleText = column;
                    if (comment && comment.trim()) {
                        labelText = comment;
                        titleText = `${column}: ${comment}`;
                    }

                    formHtml += `<div class="form-row">
                        <label title="${titleText}">
                            <input type="checkbox" name="update_${column}" style="margin-right: 8px;">
                            ${labelText}
                            ${comment && comment.trim() ? `<br><small style="color: #666; font-size: 11px; font-weight: normal; margin-left: 20px;">${column}</small>` : ''}
                        </label>
                        <input type="text" name="${column}" placeholder="新值" disabled title="${titleText}">
                    </div>`;
                }
            });
            formHtml += '</div>';

            // 固定底部工具栏
            formHtml += '<div class="modal-footer">';
            formHtml += '<button type="button" class="btn btn-danger" onclick="closeBatchEditModal()">取消</button>';
            formHtml += '<button type="submit" class="btn btn-primary">批量更新</button>';
            formHtml += '</div></form></div></div>';

            document.body.insertAdjacentHTML('beforeend', formHtml);

            // 启用拖动功能
            makeDraggable('batchEditModalContent', 'batchEditModalHeader');

            // 调整模态框高度
            adjustModalHeight('batchEditModal');

            // 绑定复选框事件，控制输入框的启用/禁用
            document.querySelectorAll('#batchEditModal input[type="checkbox"]').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const fieldName = this.name.replace('update_', '');
                    const input = document.querySelector(`#batchEditModal input[name="${fieldName}"]`);
                    if (input) {
                        input.disabled = !this.checked;
                        if (!this.checked) {
                            input.value = '';
                        }
                    }
                });
            });

            // 绑定表单提交事件
            document.getElementById('batchEditForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const formData = new FormData(this);
                const updateData = {};

                // 只更新选中的字段
                columns.forEach(column => {
                    if (formData.get(`update_${column}`) && formData.get(column)) {
                        updateData[column] = formData.get(column);
                    }
                });

                if (Object.keys(updateData).length === 0) {
                    alert('请至少选择一个字段进行更新');
                    return;
                }

                executeBatchUpdate(selectedRows, updateData);
            });
        }

        // 执行批量更新
        function executeBatchUpdate(selectedRows, updateData) {
            const batchData = selectedRows.map(rowIndex => {
                const rowData = window.currentResultData.data[rowIndex];
                const columns = window.currentResultData.columns;
                const whereData = {};

                // 构建WHERE条件
                columns.forEach(column => {
                    if (rowData[column] !== null) {
                        whereData[column] = rowData[column];
                    }
                });

                return { data: updateData, where: whereData };
            });

            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: `action=batch_update&table=${currentTable}&data=${encodeURIComponent(JSON.stringify(batchData))}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    closeBatchEditModal();
                    executeSql(); // 重新执行查询以刷新数据
                } else {
                    alert('批量更新失败: ' + data.error);
                }
            });
        }

        // 批量删除
        function batchDelete() {
            const selectedRows = getSelectedRows();
            if (selectedRows.length === 0) {
                alert('请先选择要删除的数据行');
                return;
            }

            if (!confirm(`确定要删除选中的 ${selectedRows.length} 行数据吗？此操作不可撤销！`)) {
                return;
            }

            const conditions = selectedRows.map(rowIndex => {
                const rowData = window.currentResultData.data[rowIndex];
                const columns = window.currentResultData.columns;
                const whereData = {};

                // 构建WHERE条件
                columns.forEach(column => {
                    if (rowData[column] !== null) {
                        whereData[column] = rowData[column];
                    }
                });

                return whereData;
            });

            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: `action=batch_delete&table=${currentTable}&conditions=${encodeURIComponent(JSON.stringify(conditions))}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    executeSql(); // 重新执行查询以刷新数据
                    cancelBatchMode(); // 退出批量模式
                } else {
                    alert('批量删除失败: ' + data.error);
                }
            });
        }

        // 获取选中的行索引
        function getSelectedRows() {
            const selectedRows = [];
            const checkboxes = document.querySelectorAll('.row-checkbox:checked');
            checkboxes.forEach(checkbox => {
                const row = checkbox.closest('tr');
                const rowIndex = parseInt(row.getAttribute('data-row-index'));
                selectedRows.push(rowIndex);
            });
            return selectedRows;
        }

        // 关闭批量编辑模态框
        function closeBatchEditModal() {
            const modal = document.getElementById('batchEditModal');
            if (modal) {
                modal.remove();
            }
        }

        // 显示新增数据模态框
        function showAddDataModal() {
            if (!currentTable) {
                alert('请先选择一个表');
                return;
            }

            // 获取表结构
            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: `action=get_table_structure&table=${currentTable}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    createAddDataModal(data.data);
                } else {
                    alert('获取表结构失败: ' + data.error);
                }
            });
        }

        // 创建新增数据模态框
        function createAddDataModal(tableStructure) {
            let formHtml = '<div class="modal" id="addDataModal" style="display: block;">';
            formHtml += '<div class="modal-content draggable" id="addDataModalContent">';
            formHtml += '<div class="modal-header" id="addDataModalHeader"><h3>新增数据</h3><span class="close" onclick="closeAddDataModal()">&times;</span></div>';
            formHtml += '<form id="addDataForm">';

            // 使用网格布局显示字段
            formHtml += '<div class="form-grid">';
            tableStructure.forEach(column => {
                const required = column.NULLABLE === 'N' ? 'required' : '';
                const placeholder = column.DATA_DEFAULT ? `默认值: ${column.DATA_DEFAULT}` : '';
                const dataType = column.DATA_TYPE;
                const maxLength = column.DATA_LENGTH ? ` (最大长度: ${column.DATA_LENGTH})` : '';
                const isRequired = column.NULLABLE === 'N';
                const comment = column.COMMENTS || column.COLUMN_COMMENT || '';

                // 构建标签文本
                let labelText = column.COLUMN_NAME;
                let titleText = `${column.COLUMN_NAME} - ${dataType}${maxLength}`;
                if (comment && comment.trim()) {
                    labelText = comment;
                    titleText = `${column.COLUMN_NAME}: ${comment} - ${dataType}${maxLength}`;
                }

                formHtml += `<div class="form-row">
                    <label title="${titleText}">
                        ${labelText}${isRequired ? ' <span style="color: red;">*</span>' : ''}
                        ${comment && comment.trim() ? `<br><small style="color: #666; font-size: 11px; font-weight: normal;">${column.COLUMN_NAME}</small>` : ''}
                    </label>
                    <small style="color: #666; font-size: 11px; margin-bottom: 5px; display: block;">${dataType}${maxLength}</small>
                    <input type="text" name="${column.COLUMN_NAME}" placeholder="${placeholder}" ${required} title="${titleText}">
                </div>`;
            });
            formHtml += '</div>';

            // 固定底部工具栏
            formHtml += '<div class="modal-footer">';
            formHtml += '<button type="button" class="btn btn-danger" onclick="closeAddDataModal()">取消</button>';
            formHtml += '<button type="submit" class="btn btn-primary">保存</button>';
            formHtml += '</div></form></div></div>';

            document.body.insertAdjacentHTML('beforeend', formHtml);

            // 启用拖动功能
            makeDraggable('addDataModalContent', 'addDataModalHeader');

            // 调整模态框高度
            adjustModalHeight('addDataModal');

            // 绑定表单提交事件
            document.getElementById('addDataForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const formData = new FormData(this);
                const insertData = {};

                for (let [key, value] of formData.entries()) {
                    if (value.trim()) {
                        insertData[key] = value;
                    }
                }

                fetch('', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                    body: `action=insert_data&table=${currentTable}&data=${encodeURIComponent(JSON.stringify(insertData))}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        closeAddDataModal();
                        executeSql(); // 重新执行查询以刷新数据
                    } else {
                        alert('插入失败: ' + data.error);
                    }
                });
            });
        }

        // 关闭新增数据模态框
        function closeAddDataModal() {
            const modal = document.getElementById('addDataModal');
            if (modal) {
                modal.remove();
            }
        }

        // 显示批量导入模态框
        function showBatchImportModal() {
            if (!currentTable) {
                alert('请先选择一个表');
                return;
            }

            let modalHtml = '<div class="modal" id="batchImportModal" style="display: block;">';
            modalHtml += '<div class="modal-content draggable" id="batchImportModalContent" style="width: 80%; max-width: 800px;">';
            modalHtml += '<div class="modal-header" id="batchImportModalHeader"><h3>批量导入数据</h3><span class="close" onclick="closeBatchImportModal()">&times;</span></div>';

            modalHtml += '<div style="margin-bottom: 15px; max-height: 50vh; overflow-y: auto;">';
            modalHtml += '<div style="background: #f8f9fa; padding: 15px; border-radius: 4px; margin-bottom: 15px;">';
            modalHtml += '<p><strong>使用说明：</strong></p>';
            modalHtml += '<ul style="margin-left: 20px; color: #666; margin-bottom: 0;">';
            modalHtml += '<li>从Excel复制数据（包含表头），粘贴到下方文本框</li>';
            modalHtml += '<li>数据格式：第一行为列名，后续行为数据</li>';
            modalHtml += '<li>列之间用制表符(Tab)分隔</li>';
            modalHtml += '<li>空值请留空或填写NULL</li>';
            modalHtml += '</ul></div>';

            modalHtml += '<div class="form-row">';
            modalHtml += '<label>粘贴Excel数据:</label>';
            modalHtml += '<textarea id="excelData" rows="15" style="width: 100%; font-family: monospace; border: 1px solid #ddd; border-radius: 4px; padding: 10px;" placeholder="请粘贴从Excel复制的数据..."></textarea>';
            modalHtml += '</div>';

            modalHtml += '<div id="importPreview" style="margin-top: 15px; max-height: 300px; overflow-y: auto;"></div>';
            modalHtml += '</div>';

            // 固定底部工具栏
            modalHtml += '<div class="modal-footer">';
            modalHtml += '<button type="button" class="btn btn-danger" onclick="closeBatchImportModal()">取消</button>';
            modalHtml += '<button type="button" class="btn btn-info" onclick="previewImportData()">预览数据</button>';
            modalHtml += '<button type="button" class="btn btn-primary" onclick="executeImport()">导入数据</button>';
            modalHtml += '</div></div></div>';

            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // 启用拖动功能
            makeDraggable('batchImportModalContent', 'batchImportModalHeader');

            // 调整模态框高度
            adjustModalHeight('batchImportModal');
        }

        // 预览导入数据
        function previewImportData() {
            const excelData = document.getElementById('excelData').value.trim();
            if (!excelData) {
                alert('请先粘贴Excel数据');
                return;
            }

            const parsedData = parseExcelData(excelData);
            if (parsedData.error) {
                alert('数据格式错误: ' + parsedData.error);
                return;
            }

            // 显示预览
            let previewHtml = '<h4>数据预览 (共 ' + parsedData.data.length + ' 行):</h4>';
            previewHtml += '<table class="result-table"><thead><tr>';
            parsedData.columns.forEach(col => {
                previewHtml += `<th>${col}</th>`;
            });
            previewHtml += '</tr></thead><tbody>';

            parsedData.data.slice(0, 5).forEach(row => { // 只显示前5行
                previewHtml += '<tr>';
                parsedData.columns.forEach(col => {
                    previewHtml += `<td>${row[col] || '<em>NULL</em>'}</td>`;
                });
                previewHtml += '</tr>';
            });

            if (parsedData.data.length > 5) {
                previewHtml += '<tr><td colspan="' + parsedData.columns.length + '" style="text-align: center; color: #666;">... 还有 ' + (parsedData.data.length - 5) + ' 行数据</td></tr>';
            }

            previewHtml += '</tbody></table>';
            document.getElementById('importPreview').innerHTML = previewHtml;
        }

        // 解析Excel数据
        function parseExcelData(excelData) {
            try {
                const lines = excelData.split('\n').filter(line => line.trim());
                if (lines.length < 2) {
                    return { error: '数据至少需要包含表头和一行数据' };
                }

                // 解析表头
                const columns = lines[0].split('\t').map(col => col.trim());
                if (columns.length === 0) {
                    return { error: '无法解析表头' };
                }

                // 解析数据行
                const data = [];
                for (let i = 1; i < lines.length; i++) {
                    const values = lines[i].split('\t');
                    const row = {};
                    columns.forEach((col, index) => {
                        const value = values[index] ? values[index].trim() : '';
                        row[col] = value === 'NULL' || value === '' ? null : value;
                    });
                    data.push(row);
                }

                return { columns, data };
            } catch (error) {
                return { error: '数据解析失败: ' + error.message };
            }
        }

        // 执行导入
        function executeImport() {
            const excelData = document.getElementById('excelData').value.trim();
            if (!excelData) {
                alert('请先粘贴Excel数据');
                return;
            }

            const parsedData = parseExcelData(excelData);
            if (parsedData.error) {
                alert('数据格式错误: ' + parsedData.error);
                return;
            }

            if (!confirm(`确定要导入 ${parsedData.data.length} 行数据吗？`)) {
                return;
            }

            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: `action=batch_insert&table=${currentTable}&data=${encodeURIComponent(JSON.stringify(parsedData.data))}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    closeBatchImportModal();
                    executeSql(); // 重新执行查询以刷新数据
                } else {
                    alert('导入失败: ' + data.error);
                }
            });
        }

        // 关闭批量导入模态框
        function closeBatchImportModal() {
            const modal = document.getElementById('batchImportModal');
            if (modal) {
                modal.remove();
            }
        }

        // 退出登录
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                fetch('', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                    body: 'login_action=logout'
                })
                .then(() => {
                    window.location.reload();
                });
            }
        }
        
        // 存储所有表数据
        let allTables = [];

        // 加载数据库表列表
        function loadTables() {
            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: 'action=get_tables'
            })
            .then(response => response.json())
            .then(data => {
                const tableList = document.getElementById('tableList');
                if (data.success) {
                    allTables = data.data; // 存储所有表数据
                    displayTables(allTables);
                } else {
                    tableList.innerHTML = `<div class="error">加载失败: ${data.error}</div>`;
                }
            })
            .catch(error => {
                document.getElementById('tableList').innerHTML = `<div class="error">网络错误: ${error.message}</div>`;
            });
        }

        // 显示表列表
        function displayTables(tables) {
            const tableList = document.getElementById('tableList');
            tableList.innerHTML = '';
            tables.forEach(table => {
                const div = document.createElement('div');
                div.className = 'table-item';
                div.setAttribute('data-table-name', table.TABLE_NAME);
                div.innerHTML = `<strong>${table.TABLE_NAME}</strong><br><small>${table.COMMENTS || '无注释'}</small>`;
                div.onclick = () => selectTable(table.TABLE_NAME, div);
                tableList.appendChild(div);
            });
        }

        // 过滤表列表
        function filterTables(searchText) {
            if (!searchText.trim()) {
                displayTables(allTables);
                return;
            }

            const filtered = allTables.filter(table => {
                const tableName = table.TABLE_NAME.toLowerCase();
                const comments = (table.COMMENTS || '').toLowerCase();
                const search = searchText.toLowerCase();
                return tableName.includes(search) || comments.includes(search);
            });

            displayTables(filtered);
        }
        
        // 选择表
        function selectTable(tableName, element) {
            // 更新选中状态
            document.querySelectorAll('.table-item').forEach(item => item.classList.remove('active'));
            element.classList.add('active');
            
            currentTable = tableName;
            loadTableStructure(tableName);
            
            // 自动生成SELECT语句
            document.getElementById('sqlTextarea').value = `SELECT * FROM ${tableName} WHERE ROWNUM <= 100;`;
        }
        
        // 加载表结构
        function loadTableStructure(tableName) {
            const structureContent = document.getElementById('structureContent');
            structureContent.innerHTML = '<div class="loading">加载表结构中...</div>';

            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: `action=get_table_structure&table=${tableName}`
            })
            .then(response => response.json())
            .then(data => {
                console.log('表结构数据:', data);
                if (data.success) {
                    let html = `<h4>表: ${tableName}</h4>`;
                    html += `<div class="structure-toolbar">
                        <button class="btn btn-primary" onclick="addColumn('${tableName}')">添加字段</button>
                        <button class="btn btn-success" onclick="refreshTableStructure('${tableName}')">刷新</button>
                    </div>`;
                    html += '<div class="table-structure">';
                    html += '<table class="result-table"><thead><tr><th>字段名</th><th>注释</th><th>数据类型</th><th>长度</th><th>允许空值</th><th>默认值</th><th>操作</th></tr></thead><tbody>';

                    if (data.data && data.data.length > 0) {
                        data.data.forEach((column, index) => {
                            // 使用索引而不是btoa来避免中文字符问题
                            const columnIndex = `structure_${index}`;
                            // 将列数据存储到全局变量中
                            if (!window.tableStructureData) {
                                window.tableStructureData = {};
                            }
                            window.tableStructureData[columnIndex] = column;

                            html += `<tr class="column-row">
                                <td><strong>${column.COLUMN_NAME}</strong></td>
                                <td>${column.COLUMN_COMMENT || '-'}</td>
                                <td>${column.DATA_TYPE}</td>
                                <td>${column.DATA_LENGTH || '-'}</td>
                                <td>${column.NULLABLE === 'Y' ? '是' : '否'}</td>
                                <td>${column.DATA_DEFAULT || '-'}</td>
                                <td>
                                    <div class="column-actions">
                                        <button class="btn-edit" onclick="editColumnByIndex('${tableName}', '${column.COLUMN_NAME}', '${columnIndex}')">编辑</button>
                                        <button class="btn-delete" onclick="deleteColumn('${tableName}', '${column.COLUMN_NAME}')">删除</button>
                                    </div>
                                </td>
                            </tr>`;
                        });
                    } else {
                        html += '<tr><td colspan="7" style="text-align: center; color: #666;">没有找到字段信息</td></tr>';
                    }

                    html += '</tbody></table></div>';
                    structureContent.innerHTML = html;
                } else {
                    structureContent.innerHTML = `<div class="error">加载表结构失败: ${data.error}</div>`;
                    console.error('表结构加载失败:', data);
                }
            })
            .catch(error => {
                structureContent.innerHTML = `<div class="error">网络错误: ${error.message}</div>`;
                console.error('表结构加载网络错误:', error);
            });
        }
        
        // 分页相关变量
        let currentPage = 1;
        let currentPageSize = 50;
        let currentSql = '';

        // SQL关键词列表
        const sqlKeywords = [
            'SELECT', 'FROM', 'WHERE', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER',
            'TABLE', 'INDEX', 'VIEW', 'DATABASE', 'SCHEMA', 'PROCEDURE', 'FUNCTION', 'TRIGGER',
            'AND', 'OR', 'NOT', 'IN', 'EXISTS', 'BETWEEN', 'LIKE', 'IS', 'NULL', 'DISTINCT',
            'ORDER', 'BY', 'GROUP', 'HAVING', 'LIMIT', 'OFFSET', 'JOIN', 'INNER', 'LEFT', 'RIGHT',
            'FULL', 'OUTER', 'ON', 'AS', 'UNION', 'ALL', 'CASE', 'WHEN', 'THEN', 'ELSE', 'END',
            'COUNT', 'SUM', 'AVG', 'MAX', 'MIN', 'ROWNUM', 'SYSDATE', 'DECODE', 'NVL', 'SUBSTR'
        ];

        // SQL查询历史
        let sqlHistory = JSON.parse(localStorage.getItem('sqlHistory') || '[]');
        let sqlTemplates = JSON.parse(localStorage.getItem('sqlTemplates') || '[]');

        // 执行SQL
        function executeSql(page = 1, pageSize = null) {
            const sql = document.getElementById('sqlTextarea').value.trim();
            if (!sql) {
                alert('请输入SQL语句');
                return;
            }

            // 更新当前状态
            currentSql = sql;
            currentPage = page;
            if (pageSize !== null) {
                currentPageSize = pageSize;
            }

            const resultContent = document.getElementById('resultContent');
            resultContent.innerHTML = '<div class="loading">执行中...</div>';

            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: `action=execute_sql&sql=${encodeURIComponent(sql)}&page=${currentPage}&pageSize=${currentPageSize}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 保存到查询历史
                    const historyItem = {
                        sql: sql,
                        time: new Date().toISOString(),
                        success: true,
                        rowCount: data.rowCount
                    };
                    sqlHistory.unshift(historyItem);
                    if (sqlHistory.length > 30) sqlHistory.pop(); // 最多保存30条历史
                    localStorage.setItem('sqlHistory', JSON.stringify(sqlHistory));

                    displayResult(data);
                } else {
                    resultContent.innerHTML = `<div class="error">执行失败: ${data.error}</div>`;
                }
            })
            .catch(error => {
                resultContent.innerHTML = `<div class="error">网络错误: ${error.message}</div>`;
            });
        }
        
        // 显示查询结果
        function displayResult(data) {
            // 更新信息面板
            document.getElementById('executionTime').textContent = data.executionTime + 'ms';

            // 显示行数信息（包含分页信息）
            if (data.pagination) {
                const { currentPage, pageSize, totalRows, totalPages } = data.pagination;
                const startRow = (currentPage - 1) * pageSize + 1;
                const endRow = Math.min(currentPage * pageSize, totalRows);
                document.getElementById('rowCount').textContent = `${startRow}-${endRow} / ${totalRows} (第${currentPage}页/共${totalPages}页)`;
            } else {
                document.getElementById('rowCount').textContent = data.rowCount;
            }

            document.getElementById('sqlType').textContent = data.sqlType;
            document.getElementById('resultInfo').style.display = 'block';

            // 如果有当前表，获取列注释信息
            if (currentTable && data.columns && data.columns.length > 0) {
                fetchColumnComments(currentTable, data);
            } else {
                renderResultTable(data, {});
            }
        }

        // 获取列注释
        function fetchColumnComments(tableName, resultData) {
            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: `action=get_column_comments&table=${tableName}`
            })
            .then(response => response.json())
            .then(commentData => {
                console.log('列注释数据:', commentData);
                const comments = commentData.success ? commentData.data : {};
                console.log('处理后的注释:', comments);
                renderResultTable(resultData, comments);
            })
            .catch(error => {
                console.warn('获取列注释失败:', error);
                renderResultTable(resultData, {});
            });
        }

        // 渲染结果表格
        function renderResultTable(data, columnComments) {
            const resultContent = document.getElementById('resultContent');

            if (data.data.length === 0) {
                // 即使没有数据，也显示操作工具栏和分页控件
                let html = '';
                if (currentTable) {
                    html += '<div class="data-toolbar">';
                    html += `<button class="btn btn-primary" onclick="showAddDataModal()">新增数据</button>`;
                    html += `<button class="btn btn-warning" onclick="showBatchImportModal()">批量导入</button>`;
                    html += '</div>';
                }
                html += '<div class="info-panel">查询成功，但没有返回数据</div>';

                // 如果有分页信息，显示分页控件
                if (data.pagination) {
                    html += createPaginationControls(data.pagination);
                }

                resultContent.innerHTML = html;
                return;
            }

            // 存储当前查询结果数据
            window.currentResultData = data;

            // 生成数据操作工具栏
            let html = '<div class="data-toolbar">';
            if (currentTable) {
                html += `<button class="btn btn-primary" onclick="showAddDataModal()">新增数据</button>`;
                html += `<button class="btn btn-warning" onclick="showBatchImportModal()">批量导入</button>`;
                html += `<button class="btn btn-success" onclick="toggleBatchMode()">批量操作</button>`;
            }
            html += `<button class="btn btn-info" onclick="toggleColumnFilters()">列过滤</button>`;
            html += `<button class="btn btn-secondary" onclick="clearAllColumnFilters()">清除过滤</button>`;
            if (currentTable) {
                html += `<button class="btn btn-warning" onclick="testColumnComments('${currentTable}')" style="font-size: 11px;">测试注释</button>`;
            }
            html += `<button class="btn btn-danger" onclick="debugTableStructure()" style="font-size: 11px;">调试表格</button>`;
            html += '</div>';

            // 批量操作面板
            html += '<div id="batchActions" class="batch-actions">';
            html += '<div class="batch-info">已选择 <span id="selectedCount">0</span> 行</div>';
            html += '<div class="toolbar">';
            html += '<button class="btn btn-warning" onclick="batchEdit()">批量修改</button>';
            html += '<button class="btn btn-danger" onclick="batchDelete()">批量删除</button>';
            html += '<button class="btn btn-secondary" onclick="cancelBatchMode()">取消</button>';
            html += '</div></div>';

            // 生成表格
            html += '<table class="result-table" id="resultTable"><thead><tr>';
            html += '<th id="selectAllHeader" style="display: none;"><input type="checkbox" id="selectAll" onchange="toggleSelectAll()"> 选择</th>';
            data.columns.forEach((column, index) => {
                // 显示列名和注释
                const comment = columnComments[column];
                console.log(`列 ${column} 的注释:`, comment);

                let displayName, titleText;
                if (comment && comment.trim()) {
                    displayName = `${comment}<br><small style="color: #666; font-size: 10px;">${column}</small>`;
                    titleText = `${column}: ${comment}`;
                } else {
                    displayName = column;
                    titleText = column;
                }

                html += `<th onclick="toggleColumnFilter(${index}, event)" style="position: relative; cursor: pointer;" title="${titleText}" data-column-index="${index}">
                    ${displayName}
                    <span style="font-size: 12px; color: #999; margin-left: 5px;">▼</span>
                    <div class="column-filter" id="filter_${index}" onclick="event.stopPropagation()" style="position: absolute; top: 100%; left: 0; min-width: 200px; background: white; border: 1px solid #ddd; padding: 8px; z-index: 100; display: none; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-radius: 4px;">
                        <div style="margin-bottom: 8px;">
                            <label style="font-size: 12px; color: #666;">过滤列: ${column}</label>
                        </div>
                        <div style="margin-bottom: 8px;">
                            <label style="font-size: 12px; color: #666;">过滤模式:</label>
                            <select id="filterMode_${index}" style="width: 100%; padding: 2px; font-size: 12px;" onchange="changeFilterMode(${index})">
                                <option value="frontend">前端过滤</option>
                                <option value="backend">后端查询</option>
                            </select>
                        </div>
                        <div style="margin-bottom: 8px;">
                            <input type="text" id="filterInput_${index}" placeholder="过滤值..." style="width: 100%; padding: 4px; border: 1px solid #ddd; border-radius: 2px; font-size: 12px;" onkeyup="handleFilterInput(${index}, this.value, event)">
                        </div>
                        <div style="font-size: 11px; color: #999; line-height: 1.3;">
                            <div>支持: =精确, >大于, <小于</div>
                            <div>*通配符, 默认包含匹配</div>
                        </div>
                        <div style="margin-top: 8px; text-align: right;">
                            <button onclick="clearColumnFilter(${index})" style="padding: 2px 6px; font-size: 11px; border: 1px solid #ddd; background: white; border-radius: 2px; cursor: pointer;">清除</button>
                            <button onclick="applyColumnFilter(${index})" style="padding: 2px 6px; font-size: 11px; border: 1px solid #3498db; background: #3498db; color: white; border-radius: 2px; cursor: pointer; margin-left: 4px;">应用</button>
                        </div>
                    </div>
                </th>`;
            });
            if (currentTable) {
                html += '<th>操作</th>';
            }
            html += '</tr></thead><tbody>';

            data.data.forEach((row, rowIndex) => {
                html += `<tr data-row-index="${rowIndex}">`;
                html += '<td class="select-cell" style="display: none;"><input type="checkbox" class="row-checkbox" onchange="updateSelectedCount()"></td>';
                data.columns.forEach(column => {
                    const value = row[column];
                    html += `<td>${value !== null ? value : '<em>NULL</em>'}</td>`;
                });
                if (currentTable) {
                    html += `<td class="row-actions">
                        <button class="btn-edit" onclick="editRow(${rowIndex})">编辑</button>
                        <button class="btn-delete" onclick="deleteRow(${rowIndex})">删除</button>
                    </td>`;
                }
                html += '</tr>';
            });

            html += '</tbody></table>';

            // 添加分页控件
            if (data.pagination) {
                html += createPaginationControls(data.pagination);
            }

            resultContent.innerHTML = html;

            // 切换到结果标签
            switchTab('result');
        }

        // 创建分页控件
        function createPaginationControls(pagination) {
            const { currentPage, pageSize, totalRows, totalPages, hasNext, hasPrev } = pagination;

            let html = '<div class="pagination-container">';

            // 分页信息
            html += '<div class="pagination-info">';
            html += `显示第 ${(currentPage - 1) * pageSize + 1} - ${Math.min(currentPage * pageSize, totalRows)} 条，共 ${totalRows} 条记录`;
            html += '</div>';

            // 分页控件
            html += '<div class="pagination-controls">';

            // 页面大小选择器
            html += '<div class="page-size-selector">';
            html += '<label>每页显示:</label>';
            html += '<select onchange="changePageSize(this.value)">';
            const pageSizes = [10, 20, 50, 100, 200, 500, 1000];
            pageSizes.forEach(size => {
                const selected = size === pageSize ? 'selected' : '';
                html += `<option value="${size}" ${selected}>${size}</option>`;
            });
            html += '</select>';
            html += '<span>条</span>';
            html += '</div>';

            // 分页导航
            html += '<div class="pagination-nav">';

            // 首页和上一页
            html += `<button onclick="goToPage(1)" ${!hasPrev ? 'disabled' : ''}>首页</button>`;
            html += `<button onclick="goToPage(${currentPage - 1})" ${!hasPrev ? 'disabled' : ''}>上一页</button>`;

            // 页码按钮
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            if (startPage > 1) {
                html += '<button onclick="goToPage(1)">1</button>';
                if (startPage > 2) {
                    html += '<span>...</span>';
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                const activeClass = i === currentPage ? 'active' : '';
                html += `<button class="${activeClass}" onclick="goToPage(${i})">${i}</button>`;
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    html += '<span>...</span>';
                }
                html += `<button onclick="goToPage(${totalPages})">${totalPages}</button>`;
            }

            // 下一页和末页
            html += `<button onclick="goToPage(${currentPage + 1})" ${!hasNext ? 'disabled' : ''}>下一页</button>`;
            html += `<button onclick="goToPage(${totalPages})" ${!hasNext ? 'disabled' : ''}>末页</button>`;

            html += '</div>'; // pagination-nav
            html += '</div>'; // pagination-controls
            html += '</div>'; // pagination-container

            return html;
        }

        // 跳转到指定页
        function goToPage(page) {
            if (page < 1 || !currentSql) return;
            executeSql(page, currentPageSize);
        }

        // 改变页面大小
        function changePageSize(newPageSize) {
            currentPageSize = parseInt(newPageSize);
            executeSql(1, currentPageSize); // 重新从第一页开始
        }
        

        
        // 显示导出选项
        function showExportOptions() {
            const dropdown = document.getElementById('exportDropdown');
            dropdown.style.display = dropdown.style.display === 'none' ? 'block' : 'none';

            // 点击其他地方关闭下拉菜单
            document.addEventListener('click', function closeDropdown(e) {
                if (!e.target.closest('.btn-group')) {
                    dropdown.style.display = 'none';
                    document.removeEventListener('click', closeDropdown);
                }
            });
        }

        // 导出数据
        function exportData(format = 'csv') {
            const sql = document.getElementById('sqlTextarea').value.trim();
            if (!sql || !sql.toUpperCase().startsWith('SELECT')) {
                showToast('只能导出SELECT查询的结果', 'warning');
                return;
            }

            // 隐藏下拉菜单
            document.getElementById('exportDropdown').style.display = 'none';

            // 显示导出进度
            showToast(`正在导出${format.toUpperCase()}格式数据...`, 'info');

            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: `action=export_data&sql=${encodeURIComponent(sql)}&format=${format}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 将base64数据转换为Uint8Array以保持编码
                    const binaryString = atob(data.data);
                    const bytes = new Uint8Array(binaryString.length);
                    for (let i = 0; i < binaryString.length; i++) {
                        bytes[i] = binaryString.charCodeAt(i);
                    }

                    // 创建带有正确MIME类型的Blob
                    const mimeType = data.mimeType || getMimeType(format);
                    const blob = new Blob([bytes], { type: mimeType });

                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = data.filename;
                    a.style.display = 'none';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    showToast(`${format.toUpperCase()}格式数据导出成功！`, 'success');
                } else {
                    showToast('导出失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showToast('导出失败: ' + error.message, 'error');
            });
        }

        function getMimeType(format) {
            const mimeTypes = {
                'csv': 'text/csv;charset=utf-8',
                'json': 'application/json;charset=utf-8',
                'excel': 'application/vnd.ms-excel',
                'xml': 'application/xml;charset=utf-8'
            };
            return mimeTypes[format] || 'application/octet-stream';
        }
        
        // 切换标签
        function switchTab(tabName) {
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

            document.querySelector(`.tab:nth-child(${tabName === 'result' ? 1 : 2})`).classList.add('active');
            document.getElementById(tabName + 'Tab').classList.add('active');
        }
        
        // 清空结果
        function clearResult() {
            document.getElementById('resultContent').innerHTML = '';
            document.getElementById('resultInfo').style.display = 'none';
        }
        
        // SQL模板函数
        function insertTemplate() {
            if (!currentTable) {
                alert('请先选择一个表');
                return;
            }
            document.getElementById('sqlTextarea').value = `INSERT INTO ${currentTable} (column1, column2) VALUES (value1, value2);`;
        }
        
        function updateTemplate() {
            if (!currentTable) {
                alert('请先选择一个表');
                return;
            }
            document.getElementById('sqlTextarea').value = `UPDATE ${currentTable} SET column1 = value1 WHERE condition;`;
        }
        
        function deleteTemplate() {
            if (!currentTable) {
                alert('请先选择一个表');
                return;
            }
            document.getElementById('sqlTextarea').value = `DELETE FROM ${currentTable} WHERE condition;`;
        }
        
        function selectTemplate() {
            if (!currentTable) {
                alert('请先选择一个表');
                return;
            }
            document.getElementById('sqlTextarea').value = `SELECT * FROM ${currentTable} WHERE ROWNUM <= 100;`;
        }

        // 数据操作相关变量
        let batchModeActive = false;
        let columnFiltersVisible = false;

        // 切换批量操作模式
        function toggleBatchMode() {
            batchModeActive = !batchModeActive;
            const selectCells = document.querySelectorAll('.select-cell');
            const selectAllHeader = document.getElementById('selectAllHeader');
            const batchActions = document.getElementById('batchActions');

            if (batchModeActive) {
                selectCells.forEach(cell => cell.style.display = 'table-cell');
                selectAllHeader.style.display = 'table-cell';
                batchActions.style.display = 'block';
            } else {
                selectCells.forEach(cell => cell.style.display = 'none');
                selectAllHeader.style.display = 'none';
                batchActions.style.display = 'none';
                // 清除所有选择
                document.querySelectorAll('.row-checkbox').forEach(cb => cb.checked = false);
                document.getElementById('selectAll').checked = false;
                updateSelectedCount();
            }
        }

        // 取消批量模式
        function cancelBatchMode() {
            batchModeActive = false;
            toggleBatchMode();
        }

        // 切换全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.row-checkbox');
            checkboxes.forEach(cb => cb.checked = selectAll.checked);
            updateSelectedCount();
        }

        // 更新选中数量
        function updateSelectedCount() {
            const selected = document.querySelectorAll('.row-checkbox:checked');
            document.getElementById('selectedCount').textContent = selected.length;
        }

        // 切换列过滤显示
        function toggleColumnFilters() {
            columnFiltersVisible = !columnFiltersVisible;
            const button = event.target;

            if (columnFiltersVisible) {
                button.textContent = '隐藏过滤';
                button.className = 'btn btn-warning';
                showToast('点击列标题设置过滤条件', 'info');
            } else {
                button.textContent = '列过滤';
                button.className = 'btn btn-info';
                // 隐藏所有过滤器
                const filters = document.querySelectorAll('.column-filter');
                filters.forEach(filter => {
                    filter.style.display = 'none';
                });
                // 清除前端过滤效果
                clearFrontendFilter();
            }
        }

        // 清除所有列过滤
        function clearAllColumnFilters() {
            // 清除所有过滤输入框
            const filterInputs = document.querySelectorAll('[id^="filterInput_"]');
            filterInputs.forEach((input, index) => {
                input.value = '';
                // 移除过滤标记
                markColumnAsFiltered(index, false);
            });

            // 清除前端过滤效果
            clearFrontendFilter();

            // 移除所有列的过滤标记
            const table = document.getElementById('resultTable');
            if (table) {
                const headerCells = table.querySelectorAll('thead th');
                headerCells.forEach(cell => {
                    cell.classList.remove('filter-active');
                });
            }

            // 重新执行原始查询以清除后端过滤
            if (currentSql && !currentSql.includes('WHERE')) {
                // 如果当前SQL没有WHERE子句，说明可能是原始查询
                executeSql(currentPage, currentPageSize);
            } else {
                // 如果有WHERE子句，可能包含过滤条件，需要用户手动清除
                showToast('请手动清除SQL中的WHERE条件后重新执行查询', 'warning');
            }

            showToast('已清除所有列过滤', 'success');
        }

        // 切换单个列过滤器
        function toggleColumnFilter(columnIndex, event) {
            event.stopPropagation(); // 阻止事件冒泡

            // 先隐藏所有其他过滤器
            document.querySelectorAll('.column-filter').forEach((filter, index) => {
                if (filter.id !== `filter_${columnIndex}`) {
                    filter.style.display = 'none';
                }
            });

            const filter = document.getElementById(`filter_${columnIndex}`);
            const isVisible = filter.style.display === 'block';
            filter.style.display = isVisible ? 'none' : 'block';

            if (!isVisible) {
                // 聚焦到输入框
                setTimeout(() => {
                    const input = document.getElementById(`filterInput_${columnIndex}`);
                    if (input) input.focus();
                }, 100);

                // 添加点击外部关闭的事件监听
                setTimeout(() => {
                    document.addEventListener('click', function closeFilter(e) {
                        if (!filter.contains(e.target)) {
                            filter.style.display = 'none';
                            document.removeEventListener('click', closeFilter);
                        }
                    });
                }, 100);
            }
        }

        // 过滤列数据
        function filterColumn(columnIndex, filterValue) {
            const table = document.getElementById('resultTable');
            const rows = table.querySelectorAll('tbody tr');

            // 如果过滤值为空，显示所有行
            if (!filterValue.trim()) {
                rows.forEach(row => {
                    row.style.display = '';
                });
                return;
            }

            rows.forEach(row => {
                // 重要修复：HTML表格结构分析
                // 表格列结构：[选择列, 数据列1, 数据列2, ..., 操作列]
                // - 选择列：批量模式时显示(index=0)，非批量模式时隐藏但仍存在(index=0)
                // - 数据列：从index=1开始，对应data.columns[0], data.columns[1], ...
                // - 操作列：最后一列

                // 所以实际的列索引应该是：columnIndex + 1（因为选择列总是存在）
                const actualColumnIndex = columnIndex + 1;
                const cell = row.cells[actualColumnIndex];

                console.log(`过滤调试详细:
                    - 点击的列名: ${window.currentResultData ? window.currentResultData.columns[columnIndex] : 'unknown'}
                    - columnIndex: ${columnIndex}
                    - batchModeActive: ${batchModeActive}
                    - actualColumnIndex: ${actualColumnIndex}
                    - 总列数: ${row.cells.length}
                    - 实际过滤的单元格内容: ${cell ? cell.textContent.substring(0, 20) : 'null'}`);


                if (cell) {
                    const cellText = cell.textContent.toLowerCase().trim();
                    const filterText = filterValue.toLowerCase().trim();

                    // 支持多种过滤模式
                    let shouldShow = false;
                    if (filterText.startsWith('=')) {
                        // 精确匹配 =value
                        shouldShow = cellText === filterText.substring(1);
                    } else if (filterText.startsWith('>')) {
                        // 大于 >value
                        const value = parseFloat(filterText.substring(1));
                        const cellValue = parseFloat(cellText);
                        shouldShow = !isNaN(cellValue) && !isNaN(value) && cellValue > value;
                    } else if (filterText.startsWith('<')) {
                        // 小于 <value
                        const value = parseFloat(filterText.substring(1));
                        const cellValue = parseFloat(cellText);
                        shouldShow = !isNaN(cellValue) && !isNaN(value) && cellValue < value;
                    } else if (filterText.includes('*')) {
                        // 通配符匹配
                        const regex = new RegExp(filterText.replace(/\*/g, '.*'), 'i');
                        shouldShow = regex.test(cellText);
                    } else {
                        // 默认包含匹配
                        shouldShow = cellText.includes(filterText);
                    }

                    row.style.display = shouldShow ? '' : 'none';
                }
            });

            // 更新显示的行数统计
            updateFilteredRowCount();
        }

        // 更新过滤后的行数统计
        function updateFilteredRowCount() {
            const table = document.getElementById('resultTable');
            const allRows = table.querySelectorAll('tbody tr');
            const visibleRows = table.querySelectorAll('tbody tr:not([style*="display: none"])');

            // 在表格下方显示过滤统计
            let filterInfo = document.getElementById('filterInfo');
            if (!filterInfo) {
                filterInfo = document.createElement('div');
                filterInfo.id = 'filterInfo';
                filterInfo.style.cssText = 'margin-top: 10px; padding: 8px; background: #e3f2fd; border-radius: 4px; font-size: 14px; color: #1976d2;';
                table.parentNode.insertBefore(filterInfo, table.nextSibling);
            }

            if (visibleRows.length < allRows.length) {
                filterInfo.textContent = `显示 ${visibleRows.length} 行，共 ${allRows.length} 行 (已过滤 ${allRows.length - visibleRows.length} 行)`;
                filterInfo.style.display = 'block';
            } else {
                filterInfo.style.display = 'none';
            }
        }

        // 处理过滤输入
        function handleFilterInput(columnIndex, filterValue, event) {
            const filterMode = document.getElementById(`filterMode_${columnIndex}`).value;

            if (event.key === 'Enter') {
                applyColumnFilter(columnIndex);
            } else if (filterMode === 'frontend') {
                // 前端过滤实时响应
                filterColumn(columnIndex, filterValue);
            }

            // 调试信息（可以在控制台查看）
            if (event.key === 'Enter' && filterValue.trim()) {
                const columnName = window.currentResultData ? window.currentResultData.columns[columnIndex] : 'Unknown';
                console.log(`过滤列: ${columnName} (索引: ${columnIndex}), 值: ${filterValue}, 模式: ${filterMode}`);
            }
        }

        // 改变过滤模式
        function changeFilterMode(columnIndex) {
            const filterMode = document.getElementById(`filterMode_${columnIndex}`).value;
            const filterValue = document.getElementById(`filterInput_${columnIndex}`).value;

            if (filterMode === 'frontend' && filterValue) {
                // 切换到前端过滤时立即应用
                filterColumn(columnIndex, filterValue);
            } else if (filterMode === 'backend') {
                // 切换到后端过滤时清除前端过滤效果
                clearFrontendFilter();
            }
        }

        // 应用列过滤
        function applyColumnFilter(columnIndex) {
            const filterMode = document.getElementById(`filterMode_${columnIndex}`).value;
            const filterValue = document.getElementById(`filterInput_${columnIndex}`).value;
            const columnName = window.currentResultData.columns[columnIndex];

            if (filterValue.trim()) {
                if (filterMode === 'frontend') {
                    filterColumn(columnIndex, filterValue);
                } else if (filterMode === 'backend') {
                    applyBackendFilter(columnName, filterValue);
                }

                // 标记列为已过滤状态
                markColumnAsFiltered(columnIndex, true);
            } else {
                // 清除过滤
                clearColumnFilter(columnIndex);
            }

            // 隐藏过滤器
            document.getElementById(`filter_${columnIndex}`).style.display = 'none';
        }

        // 标记列的过滤状态
        function markColumnAsFiltered(columnIndex, isFiltered) {
            const table = document.getElementById('resultTable');
            if (!table) return;

            // 重要修复：计算实际的表头列索引
            // HTML表格结构：[选择列(th), 数据列1(th), 数据列2(th), ..., 操作列(th)]
            // - 选择列：总是第1个th (CSS nth-child(1))
            // - 数据列：从第2个th开始 (CSS nth-child(2), nth-child(3), ...)
            // - columnIndex对应data.columns数组索引，从0开始
            // 所以数据列的CSS选择器索引 = columnIndex + 2
            const headerCellIndex = columnIndex + 2;

            console.log(`标记过滤状态: columnIndex=${columnIndex}, headerCellIndex=${headerCellIndex}, isFiltered=${isFiltered}`);

            const headerCell = table.querySelector(`thead th:nth-child(${headerCellIndex})`);
            if (headerCell) {
                if (isFiltered) {
                    headerCell.classList.add('filter-active');
                } else {
                    headerCell.classList.remove('filter-active');
                }
                console.log(`成功标记列 ${columnIndex}，表头单元格:`, headerCell.textContent.substring(0, 20));
            } else {
                console.error(`未找到表头单元格，索引: ${headerCellIndex}`);
            }
        }

        // 清除列过滤
        function clearColumnFilter(columnIndex) {
            const filterInput = document.getElementById(`filterInput_${columnIndex}`);
            filterInput.value = '';

            const filterMode = document.getElementById(`filterMode_${columnIndex}`).value;
            if (filterMode === 'frontend') {
                filterColumn(columnIndex, '');
            } else {
                // 清除后端过滤，重新执行原始查询
                executeSql(currentPage, currentPageSize);
            }

            // 移除过滤标记
            markColumnAsFiltered(columnIndex, false);

            // 隐藏过滤器
            document.getElementById(`filter_${columnIndex}`).style.display = 'none';
        }

        // 清除前端过滤效果
        function clearFrontendFilter() {
            const table = document.getElementById('resultTable');
            const rows = table.querySelectorAll('tbody tr');
            rows.forEach(row => {
                row.style.display = '';
            });

            // 隐藏过滤统计
            const filterInfo = document.getElementById('filterInfo');
            if (filterInfo) {
                filterInfo.style.display = 'none';
            }
        }

        // 应用后端过滤
        function applyBackendFilter(columnName, filterValue) {
            if (!filterValue.trim()) {
                showToast('请输入过滤条件', 'warning');
                return;
            }

            let originalSql = currentSql.trim();
            // 移除末尾的分号
            if (originalSql.endsWith(';')) {
                originalSql = originalSql.slice(0, -1);
            }

            let filteredSql = '';

            // 构建过滤条件
            let condition = '';
            const value = filterValue.trim();

            if (value.startsWith('=')) {
                // 精确匹配
                const exactValue = value.substring(1);
                condition = `${columnName} = '${exactValue.replace(/'/g, "''")}'`;
            } else if (value.startsWith('>')) {
                // 大于
                const numValue = value.substring(1);
                if (isNaN(numValue)) {
                    condition = `${columnName} > '${numValue.replace(/'/g, "''")}'`;
                } else {
                    condition = `${columnName} > ${numValue}`;
                }
            } else if (value.startsWith('<')) {
                // 小于
                const numValue = value.substring(1);
                if (isNaN(numValue)) {
                    condition = `${columnName} < '${numValue.replace(/'/g, "''")}'`;
                } else {
                    condition = `${columnName} < ${numValue}`;
                }
            } else if (value.includes('*')) {
                // 通配符匹配
                const likeValue = value.replace(/\*/g, '%');
                condition = `${columnName} LIKE '${likeValue.replace(/'/g, "''")}'`;
            } else {
                // 包含匹配
                condition = `${columnName} LIKE '%${value.replace(/'/g, "''")}%'`;
            }

            // 修改SQL添加过滤条件
            const upperSql = originalSql.toUpperCase();
            if (upperSql.includes('WHERE')) {
                filteredSql = originalSql + ` AND ${condition}`;
            } else if (upperSql.includes('ORDER BY') || upperSql.includes('GROUP BY')) {
                // 在ORDER BY或GROUP BY之前插入WHERE
                const orderIndex = originalSql.search(/\s+(ORDER\s+BY|GROUP\s+BY)/i);
                filteredSql = originalSql.substring(0, orderIndex) + ` WHERE ${condition}` + originalSql.substring(orderIndex);
            } else {
                filteredSql = originalSql + ` WHERE ${condition}`;
            }

            // 执行过滤后的SQL
            const resultContent = document.getElementById('resultContent');
            resultContent.innerHTML = '<div class="loading">正在应用后端过滤...</div>';

            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: `action=execute_sql&sql=${encodeURIComponent(filteredSql)}&page=1&pageSize=${currentPageSize}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新当前SQL为过滤后的SQL
                    currentSql = filteredSql;
                    currentPage = 1;

                    // 更新SQL输入框内容
                    document.getElementById('sqlTextarea').value = filteredSql;

                    displayResult(data);
                    showToast(`后端过滤应用成功，找到 ${data.rowCount} 条记录`, 'success');
                } else {
                    showToast('后端过滤失败: ' + data.error, 'error');
                    // 恢复原始查询
                    executeSql(currentPage, currentPageSize);
                }
            })
            .catch(error => {
                showToast('后端过滤网络错误: ' + error.message, 'error');
                // 恢复原始查询
                executeSql(currentPage, currentPageSize);
            });
        }

        // 字段管理相关函数
        let currentEditMode = 'add';
        let currentEditTable = '';
        let currentEditColumn = '';

        function addColumn(tableName) {
            currentEditMode = 'add';
            currentEditTable = tableName;
            document.getElementById('modalTitle').textContent = '添加字段';
            document.getElementById('columnForm').reset();
            document.getElementById('columnModal').style.display = 'block';
        }

        function editColumn(tableName, columnName, encodedColumnData) {
            currentEditMode = 'edit';
            currentEditTable = tableName;
            currentEditColumn = columnName;
            document.getElementById('modalTitle').textContent = '编辑字段';

            try {
                // 解码base64数据
                const columnData = JSON.parse(atob(encodedColumnData));

                // 填充表单数据
                document.getElementById('columnName').value = columnData.COLUMN_NAME;
                document.getElementById('columnType').value = columnData.DATA_TYPE;
                document.getElementById('columnLength').value = columnData.DATA_LENGTH || '';
                document.getElementById('columnNullable').value = columnData.NULLABLE;
                document.getElementById('columnDefault').value = columnData.DATA_DEFAULT || '';

                document.getElementById('columnModal').style.display = 'block';
            } catch (error) {
                console.error('编辑字段失败:', error);
                showToast('编辑字段失败: ' + error.message, 'error');
            }
        }

        function editColumnByIndex(tableName, columnName, columnIndex) {
            currentEditMode = 'edit';
            currentEditTable = tableName;
            currentEditColumn = columnName;
            document.getElementById('modalTitle').textContent = '编辑字段';

            // 从全局变量中获取列数据
            const columnData = window.tableStructureData && window.tableStructureData[columnIndex];
            if (!columnData) {
                showToast('无法获取字段数据', 'error');
                return;
            }

            // 填充表单数据
            document.getElementById('columnName').value = columnData.COLUMN_NAME;
            document.getElementById('columnType').value = columnData.DATA_TYPE;
            document.getElementById('columnLength').value = columnData.DATA_LENGTH || '';
            document.getElementById('columnNullable').value = columnData.NULLABLE;
            document.getElementById('columnDefault').value = columnData.DATA_DEFAULT || '';

            document.getElementById('columnModal').style.display = 'block';
        }

        function deleteColumn(tableName, columnName) {
            if (!confirm(`确定要删除字段 "${columnName}" 吗？此操作不可撤销！`)) {
                return;
            }

            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: `action=drop_column&table=${tableName}&column=${columnName}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    loadTableStructure(tableName);
                } else {
                    alert('删除失败: ' + data.error);
                }
            });
        }

        function closeColumnModal() {
            document.getElementById('columnModal').style.display = 'none';
        }

        function refreshTableStructure(tableName) {
            loadTableStructure(tableName);
        }

        // SQL编辑器增强功能
        function handleSqlInput(e) {
            const textarea = e.target;
            const value = textarea.value;
            const cursorPos = textarea.selectionStart;

            // 获取当前单词
            const beforeCursor = value.substring(0, cursorPos);
            const words = beforeCursor.split(/\s+/);
            const currentWord = words[words.length - 1].toUpperCase();

            if (currentWord.length >= 2) {
                showSqlSuggestions(currentWord, textarea);
            } else {
                hideSqlSuggestions();
            }
        }

        function handleSqlKeydown(e) {
            const suggestions = document.getElementById('sqlSuggestions');
            if (suggestions.style.display === 'block') {
                const items = suggestions.querySelectorAll('.sql-suggestion-item');
                const activeItem = suggestions.querySelector('.sql-suggestion-item.active');

                if (e.key === 'ArrowDown') {
                    e.preventDefault();
                    const nextItem = activeItem ? activeItem.nextElementSibling : items[0];
                    if (nextItem) {
                        if (activeItem) activeItem.classList.remove('active');
                        nextItem.classList.add('active');
                    }
                } else if (e.key === 'ArrowUp') {
                    e.preventDefault();
                    const prevItem = activeItem ? activeItem.previousElementSibling : items[items.length - 1];
                    if (prevItem) {
                        if (activeItem) activeItem.classList.remove('active');
                        prevItem.classList.add('active');
                    }
                } else if (e.key === 'Enter' && activeItem) {
                    e.preventDefault();
                    insertSuggestion(activeItem.textContent, e.target);
                } else if (e.key === 'Escape') {
                    hideSqlSuggestions();
                }
            }

            // Tab键自动缩进
            if (e.key === 'Tab') {
                e.preventDefault();
                const start = e.target.selectionStart;
                const end = e.target.selectionEnd;
                e.target.value = e.target.value.substring(0, start) + '    ' + e.target.value.substring(end);
                e.target.selectionStart = e.target.selectionEnd = start + 4;
            }
        }

        function showSqlSuggestions(currentWord, textarea) {
            const suggestions = sqlKeywords.filter(keyword =>
                keyword.startsWith(currentWord) && keyword !== currentWord
            );

            if (suggestions.length === 0) {
                hideSqlSuggestions();
                return;
            }

            const suggestionsDiv = document.getElementById('sqlSuggestions');
            suggestionsDiv.innerHTML = '';

            suggestions.slice(0, 10).forEach((suggestion, index) => {
                const item = document.createElement('div');
                item.className = 'sql-suggestion-item' + (index === 0 ? ' active' : '');
                item.textContent = suggestion;
                item.onclick = () => insertSuggestion(suggestion, textarea);
                suggestionsDiv.appendChild(item);
            });

            suggestionsDiv.style.display = 'block';
        }

        function hideSqlSuggestions() {
            setTimeout(() => {
                document.getElementById('sqlSuggestions').style.display = 'none';
            }, 150);
        }

        function insertSuggestion(suggestion, textarea) {
            const value = textarea.value;
            const cursorPos = textarea.selectionStart;
            const beforeCursor = value.substring(0, cursorPos);
            const afterCursor = value.substring(cursorPos);

            // 找到当前单词的开始位置
            const words = beforeCursor.split(/\s+/);
            const currentWord = words[words.length - 1];
            const wordStart = beforeCursor.lastIndexOf(currentWord);

            // 替换当前单词
            const newValue = value.substring(0, wordStart) + suggestion + afterCursor;
            textarea.value = newValue;
            textarea.selectionStart = textarea.selectionEnd = wordStart + suggestion.length;

            hideSqlSuggestions();
            textarea.focus();
        }

        function formatSql() {
            const textarea = document.getElementById('sqlTextarea');
            let sql = textarea.value.trim();

            if (!sql) return;

            // 简单的SQL格式化
            sql = sql.replace(/\s+/g, ' '); // 合并多个空格
            sql = sql.replace(/\s*,\s*/g, ',\n    '); // 逗号后换行
            sql = sql.replace(/\s+(FROM|WHERE|GROUP BY|ORDER BY|HAVING|UNION|JOIN|INNER JOIN|LEFT JOIN|RIGHT JOIN)\s+/gi, '\n$1 ');
            sql = sql.replace(/\s+(AND|OR)\s+/gi, '\n    $1 ');
            sql = sql.replace(/\s*\(\s*/g, ' (');
            sql = sql.replace(/\s*\)\s*/g, ') ');

            textarea.value = sql;
        }

        function toggleSqlComment() {
            const textarea = document.getElementById('sqlTextarea');
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            const value = textarea.value;

            // 获取选中的行
            const beforeSelection = value.substring(0, start);
            const selection = value.substring(start, end);
            const afterSelection = value.substring(end);

            const lines = selection.split('\n');
            const isCommented = lines.every(line => line.trim().startsWith('--') || line.trim() === '');

            let newLines;
            if (isCommented) {
                // 取消注释
                newLines = lines.map(line => line.replace(/^\s*--\s?/, ''));
            } else {
                // 添加注释
                newLines = lines.map(line => line.trim() ? '-- ' + line : line);
            }

            const newSelection = newLines.join('\n');
            textarea.value = beforeSelection + newSelection + afterSelection;
            textarea.selectionStart = start;
            textarea.selectionEnd = start + newSelection.length;
        }

        function clearSqlEditor() {
            if (confirm('确定要清空SQL编辑器吗？')) {
                document.getElementById('sqlTextarea').value = '';
            }
        }

        function saveSqlTemplate() {
            const sql = document.getElementById('sqlTextarea').value.trim();
            if (!sql) {
                alert('请先输入SQL语句');
                return;
            }

            const name = prompt('请输入模板名称:');
            if (name) {
                const template = { name, sql, createTime: new Date().toISOString() };
                sqlTemplates.unshift(template);
                if (sqlTemplates.length > 50) sqlTemplates.pop(); // 最多保存50个模板
                localStorage.setItem('sqlTemplates', JSON.stringify(sqlTemplates));
                alert('模板保存成功');
            }
        }

        function showSqlHistory() {
            let html = '<div class="modal" id="sqlHistoryModal" style="display: block;">';
            html += '<div class="modal-content" style="width: 80%; max-width: 900px;">';
            html += '<div class="modal-header"><h3>SQL查询历史</h3><span class="close" onclick="closeSqlHistoryModal()">&times;</span></div>';

            html += '<div style="margin-bottom: 15px;">';
            html += '<button class="btn btn-info" onclick="showHistoryTab(\'history\')">查询历史</button>';
            html += '<button class="btn btn-success" onclick="showHistoryTab(\'templates\')">保存的模板</button>';
            html += '</div>';

            html += '<div id="historyTabContent"></div>';
            html += '</div></div>';

            document.body.insertAdjacentHTML('beforeend', html);
            showHistoryTab('history');
        }

        function showHistoryTab(tab) {
            const content = document.getElementById('historyTabContent');
            let html = '';

            if (tab === 'history') {
                html += '<h4>最近执行的SQL (最多显示30条)</h4>';
                if (sqlHistory.length === 0) {
                    html += '<p>暂无查询历史</p>';
                } else {
                    html += '<div style="max-height: 400px; overflow-y: auto;">';
                    sqlHistory.forEach((item, index) => {
                        html += `<div style="border: 1px solid #ddd; margin: 10px 0; padding: 10px; border-radius: 4px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                                <small style="color: #666;">${new Date(item.time).toLocaleString()}</small>
                                <div>
                                    <button class="btn btn-primary" style="padding: 2px 8px; font-size: 11px;" onclick="useSqlFromHistory(${index})">使用</button>
                                    <button class="btn btn-danger" style="padding: 2px 8px; font-size: 11px;" onclick="deleteSqlHistory(${index})">删除</button>
                                </div>
                            </div>
                            <pre style="background: #f5f5f5; padding: 8px; border-radius: 3px; font-size: 12px; margin: 0; white-space: pre-wrap;">${item.sql}</pre>
                        </div>`;
                    });
                    html += '</div>';
                }
            } else {
                html += '<h4>保存的SQL模板</h4>';
                if (sqlTemplates.length === 0) {
                    html += '<p>暂无保存的模板</p>';
                } else {
                    html += '<div style="max-height: 400px; overflow-y: auto;">';
                    sqlTemplates.forEach((item, index) => {
                        html += `<div style="border: 1px solid #ddd; margin: 10px 0; padding: 10px; border-radius: 4px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                                <strong>${item.name}</strong>
                                <div>
                                    <button class="btn btn-primary" style="padding: 2px 8px; font-size: 11px;" onclick="useSqlFromTemplate(${index})">使用</button>
                                    <button class="btn btn-danger" style="padding: 2px 8px; font-size: 11px;" onclick="deleteSqlTemplate(${index})">删除</button>
                                </div>
                            </div>
                            <small style="color: #666;">${new Date(item.createTime).toLocaleString()}</small>
                            <pre style="background: #f5f5f5; padding: 8px; border-radius: 3px; font-size: 12px; margin: 5px 0 0 0; white-space: pre-wrap;">${item.sql}</pre>
                        </div>`;
                    });
                    html += '</div>';
                }
            }

            content.innerHTML = html;
        }

        function useSqlFromHistory(index) {
            document.getElementById('sqlTextarea').value = sqlHistory[index].sql;
            closeSqlHistoryModal();
        }

        function useSqlFromTemplate(index) {
            document.getElementById('sqlTextarea').value = sqlTemplates[index].sql;
            closeSqlHistoryModal();
        }

        function deleteSqlHistory(index) {
            if (confirm('确定要删除这条历史记录吗？')) {
                sqlHistory.splice(index, 1);
                localStorage.setItem('sqlHistory', JSON.stringify(sqlHistory));
                showHistoryTab('history');
            }
        }

        function deleteSqlTemplate(index) {
            if (confirm('确定要删除这个模板吗？')) {
                sqlTemplates.splice(index, 1);
                localStorage.setItem('sqlTemplates', JSON.stringify(sqlTemplates));
                showHistoryTab('templates');
            }
        }

        function closeSqlHistoryModal() {
            const modal = document.getElementById('sqlHistoryModal');
            if (modal) {
                modal.remove();
            }
        }

        // 数据库状态监控功能
        function checkDatabaseStatus() {
            const indicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('statusText');

            indicator.className = 'status-indicator checking';
            statusText.textContent = '检查中...';

            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: 'action=get_db_status'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    indicator.className = 'status-indicator connected';
                    statusText.textContent = '已连接';
                    window.dbStatusData = data.data; // 保存状态数据
                } else {
                    indicator.className = 'status-indicator disconnected';
                    statusText.textContent = '连接失败';
                }
            })
            .catch(error => {
                indicator.className = 'status-indicator disconnected';
                statusText.textContent = '网络错误';
            });
        }

        function showDbStatusModal() {
            if (!window.dbStatusData) {
                alert('正在获取数据库状态信息，请稍后再试');
                return;
            }

            const data = window.dbStatusData;
            let html = '<div class="modal" id="dbStatusModal" style="display: block;">';
            html += '<div class="modal-content" style="width: 80%; max-width: 800px;">';
            html += '<div class="modal-header"><h3>数据库系统信息</h3><span class="close" onclick="closeDbStatusModal()">&times;</span></div>';

            html += '<div class="db-info-grid">';

            // 基本信息卡片
            html += '<div class="db-info-card">';
            html += '<h4>基本信息</h4>';
            html += `<div class="db-info-item"><span class="db-info-label">数据库版本:</span><span class="db-info-value">${data.version || '未知'}</span></div>`;
            html += `<div class="db-info-item"><span class="db-info-label">实例名称:</span><span class="db-info-value">${data.instance_name || '未知'}</span></div>`;
            html += `<div class="db-info-item"><span class="db-info-label">实例状态:</span><span class="db-info-value">${data.instance_status || '未知'}</span></div>`;
            html += `<div class="db-info-item"><span class="db-info-label">启动时间:</span><span class="db-info-value">${data.startup_time || '未知'}</span></div>`;
            html += '</div>';

            // 连接信息卡片
            html += '<div class="db-info-card">';
            html += '<h4>连接信息</h4>';
            html += `<div class="db-info-item"><span class="db-info-label">连接状态:</span><span class="db-info-value">${data.connection_status || '未知'}</span></div>`;
            html += `<div class="db-info-item"><span class="db-info-label">活动会话:</span><span class="db-info-value">${data.active_sessions || '0'}</span></div>`;
            html += `<div class="db-info-item"><span class="db-info-label">检查时间:</span><span class="db-info-value">${data.last_check || '未知'}</span></div>`;
            html += `<div class="db-info-item"><span class="db-info-label">数据库时间:</span><span class="db-info-value">${data.current_time || '未知'}</span></div>`;
            html += '</div>';

            // 存储信息卡片
            html += '<div class="db-info-card">';
            html += '<h4>存储信息</h4>';
            html += `<div class="db-info-item"><span class="db-info-label">表空间数量:</span><span class="db-info-value">${data.tablespace_count || '0'}</span></div>`;
            html += `<div class="db-info-item"><span class="db-info-label">用户表数量:</span><span class="db-info-value">${data.user_table_count || '0'}</span></div>`;
            html += '</div>';

            // 操作按钮卡片
            html += '<div class="db-info-card">';
            html += '<h4>操作</h4>';
            html += '<div style="display: flex; gap: 10px; flex-wrap: wrap;">';
            html += '<button class="btn btn-primary" onclick="checkDatabaseStatus(); closeDbStatusModal();">刷新状态</button>';
            html += '<button class="btn btn-info" onclick="exportDbStatus()">导出信息</button>';
            html += '</div>';
            html += '</div>';

            html += '</div>'; // db-info-grid
            html += '</div></div>';

            document.body.insertAdjacentHTML('beforeend', html);
        }

        function closeDbStatusModal() {
            const modal = document.getElementById('dbStatusModal');
            if (modal) {
                modal.remove();
            }
        }

        function exportDbStatus() {
            if (!window.dbStatusData) {
                alert('没有可导出的状态信息');
                return;
            }

            const data = window.dbStatusData;
            const exportData = {
                export_time: new Date().toISOString(),
                database_info: data
            };

            const jsonStr = JSON.stringify(exportData, null, 2);
            const blob = new Blob([jsonStr], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `db_status_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
            a.style.display = 'none';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            showToast('数据库状态信息已导出', 'success');
        }

        // 拖动功能实现
        function makeDraggable(modalId, headerId) {
            const modal = document.getElementById(modalId);
            const header = document.getElementById(headerId);

            if (!modal || !header) return;

            let isDragging = false;
            let startX = 0;
            let startY = 0;
            let modalStartX = 0;
            let modalStartY = 0;

            // 设置初始样式
            header.style.cursor = 'move';
            header.style.userSelect = 'none';

            header.addEventListener('mousedown', dragStart);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', dragEnd);

            function dragStart(e) {
                // 不拖动关闭按钮
                if (e.target.classList.contains('close')) return;

                // 只在头部区域开始拖拽
                if (e.target === header || header.contains(e.target)) {
                    isDragging = true;

                    // 记录鼠标起始位置
                    startX = e.clientX;
                    startY = e.clientY;

                    // 获取模态框当前位置
                    const modalRect = modal.getBoundingClientRect();
                    modalStartX = modalRect.left;
                    modalStartY = modalRect.top;

                    // 设置拖拽状态样式
                    header.style.cursor = 'grabbing';
                    modal.style.position = 'fixed';
                    modal.style.margin = '0';
                    modal.style.left = modalStartX + 'px';
                    modal.style.top = modalStartY + 'px';

                    // 防止文本选择
                    e.preventDefault();
                }
            }

            function drag(e) {
                if (!isDragging) return;

                e.preventDefault();

                // 计算鼠标移动距离
                const deltaX = e.clientX - startX;
                const deltaY = e.clientY - startY;

                // 计算新位置
                let newX = modalStartX + deltaX;
                let newY = modalStartY + deltaY;

                // 限制拖动范围，确保模态框不会完全移出视窗
                const modalWidth = modal.offsetWidth;
                const modalHeight = modal.offsetHeight;
                const windowWidth = window.innerWidth;
                const windowHeight = window.innerHeight;

                // 至少保留50px在视窗内
                const minVisible = 50;
                newX = Math.max(-modalWidth + minVisible, Math.min(newX, windowWidth - minVisible));
                newY = Math.max(0, Math.min(newY, windowHeight - minVisible));

                // 应用新位置
                modal.style.left = newX + 'px';
                modal.style.top = newY + 'px';
            }

            function dragEnd(e) {
                if (isDragging) {
                    isDragging = false;
                    header.style.cursor = 'move';
                }
            }

            // 双击头部重置位置到中心
            header.addEventListener('dblclick', function(e) {
                if (e.target.classList.contains('close')) return;

                const windowWidth = window.innerWidth;
                const windowHeight = window.innerHeight;
                const modalWidth = modal.offsetWidth;
                const modalHeight = modal.offsetHeight;

                const centerX = (windowWidth - modalWidth) / 2;
                const centerY = (windowHeight - modalHeight) / 2;

                modal.style.position = 'fixed';
                modal.style.margin = '0';
                modal.style.left = centerX + 'px';
                modal.style.top = centerY + 'px';
                modal.style.transition = 'all 0.3s ease';

                // 移除过渡效果
                setTimeout(() => {
                    modal.style.transition = '';
                }, 300);
            });
        }

        // 调整模态框高度以适应屏幕
        function adjustModalHeight(modalId) {
            const modal = document.getElementById(modalId);
            if (!modal) return;

            const modalContent = modal.querySelector('.modal-content');
            const formGrid = modal.querySelector('.form-grid');

            if (!modalContent || !formGrid) return;

            // 计算可用高度
            const windowHeight = window.innerHeight;
            const modalPadding = 40; // 模态框上下边距
            const headerHeight = modal.querySelector('.modal-header')?.offsetHeight || 60;
            const footerHeight = modal.querySelector('.modal-footer')?.offsetHeight || 60;
            const otherElementsHeight = 100; // 其他元素的预估高度

            const availableHeight = windowHeight - modalPadding - headerHeight - footerHeight - otherElementsHeight;
            const maxFormGridHeight = Math.max(300, availableHeight * 0.6);

            formGrid.style.maxHeight = maxFormGridHeight + 'px';
        }

        // 窗口大小改变时重新调整模态框
        window.addEventListener('resize', function() {
            const modals = ['editModal', 'batchEditModal', 'addDataModal', 'batchImportModal'];
            modals.forEach(modalId => {
                if (document.getElementById(modalId)) {
                    adjustModalHeight(modalId);
                }
            });
        });

        // 用户体验增强功能
        function showToast(message, type = 'info', duration = 3000) {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.textContent = message;
            document.body.appendChild(toast);

            // 显示动画
            setTimeout(() => toast.classList.add('show'), 100);

            // 自动隐藏
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => document.body.removeChild(toast), 300);
            }, duration);
        }

        function showConfirm(message, onConfirm, onCancel = null) {
            const dialog = document.createElement('div');
            dialog.className = 'confirm-dialog';
            dialog.innerHTML = `
                <div class="confirm-content">
                    <h3 style="margin-bottom: 15px; color: #2c3e50;">确认操作</h3>
                    <p style="margin-bottom: 20px; color: #34495e;">${message}</p>
                    <div class="confirm-buttons">
                        <button class="btn btn-primary" onclick="confirmYes()">确定</button>
                        <button class="btn btn-secondary" onclick="confirmNo()">取消</button>
                    </div>
                </div>
            `;

            document.body.appendChild(dialog);

            window.confirmYes = function() {
                document.body.removeChild(dialog);
                if (onConfirm) onConfirm();
                delete window.confirmYes;
                delete window.confirmNo;
            };

            window.confirmNo = function() {
                document.body.removeChild(dialog);
                if (onCancel) onCancel();
                delete window.confirmYes;
                delete window.confirmNo;
            };
        }

        function showLoadingSpinner(element, text = '加载中...') {
            element.innerHTML = `<div class="loading"><div class="loading-spinner"></div>${text}</div>`;
        }

        function showProgress(element, progress, text = '') {
            element.innerHTML = `
                <div style="padding: 20px;">
                    <div>${text}</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${progress}%"></div>
                    </div>
                    <div style="text-align: center; margin-top: 5px;">${progress}%</div>
                </div>
            `;
        }

        // 增强现有函数的用户体验
        function enhancedExecuteSql(page = 1, pageSize = null) {
            const sql = document.getElementById('sqlTextarea').value.trim();
            if (!sql) {
                showToast('请输入SQL语句', 'warning');
                return;
            }

            // 更新当前状态
            currentSql = sql;
            currentPage = page;
            if (pageSize !== null) {
                currentPageSize = pageSize;
            }

            const resultContent = document.getElementById('resultContent');
            showLoadingSpinner(resultContent, '正在执行SQL查询...');

            const startTime = Date.now();

            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: `action=execute_sql&sql=${encodeURIComponent(sql)}&page=${currentPage}&pageSize=${currentPageSize}`
            })
            .then(response => response.json())
            .then(data => {
                const endTime = Date.now();
                const clientTime = endTime - startTime;

                if (data.success) {
                    // 保存到查询历史
                    const historyItem = {
                        sql: sql,
                        time: new Date().toISOString(),
                        success: true,
                        rowCount: data.rowCount,
                        executionTime: data.executionTime
                    };
                    sqlHistory.unshift(historyItem);
                    if (sqlHistory.length > 30) sqlHistory.pop();
                    localStorage.setItem('sqlHistory', JSON.stringify(sqlHistory));

                    displayResult(data);
                    if (data.warning) {
                        showToast(data.warning, 'warning');
                    } else {
                        showToast(`查询成功，耗时 ${data.executionTime}ms`, 'success');
                    }
                } else {
                    let errorHtml = `<div class="error">执行失败: ${data.error}</div>`;
                    if (data.debug) {
                        errorHtml += `<details style="margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                            <summary style="cursor: pointer; font-weight: bold;">调试信息</summary>
                            <pre style="margin: 10px 0; font-size: 12px; white-space: pre-wrap;">${JSON.stringify(data.debug, null, 2)}</pre>
                        </details>`;
                    }
                    resultContent.innerHTML = errorHtml;
                    showToast('SQL执行失败', 'error');
                }
            })
            .catch(error => {
                resultContent.innerHTML = `<div class="error">网络错误: ${error.message}</div>`;
                showToast('网络连接错误', 'error');
            });
        }

        // 替换原有的executeSql函数
        function executeSql(page = 1, pageSize = null) {
            enhancedExecuteSql(page, pageSize);
        }

        // 测试列注释功能
        function testColumnComments(tableName) {
            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: `action=test_column_comments&table=${tableName}`
            })
            .then(response => response.json())
            .then(data => {
                console.log('列注释测试结果:', data);
                if (data.success) {
                    const commentCount = Object.keys(data.data).length;
                    showToast(`表 ${tableName} 共有 ${commentCount} 个字段有注释`, 'info');
                    console.log('字段注释详情:', data.data);
                } else {
                    showToast('获取列注释失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('测试列注释失败:', error);
                showToast('测试失败: ' + error.message, 'error');
            });
        }

        // 调试表格结构
        function debugTableStructure() {
            const table = document.getElementById('resultTable');
            if (!table) {
                showToast('没有找到结果表格', 'warning');
                return;
            }

            console.log('=== 表格结构调试信息 ===');

            // 分析表头结构
            const headerCells = table.querySelectorAll('thead th');
            console.log('表头列数:', headerCells.length);
            headerCells.forEach((th, index) => {
                const text = th.textContent.trim().substring(0, 30);
                console.log(`表头 ${index + 1} (nth-child(${index + 1})): ${text}`);
            });

            // 分析数据列结构
            if (window.currentResultData && window.currentResultData.columns) {
                console.log('数据列信息:');
                window.currentResultData.columns.forEach((col, index) => {
                    console.log(`数据列 ${index}: ${col}`);
                });
            }

            // 分析第一行数据
            const firstRow = table.querySelector('tbody tr');
            if (firstRow) {
                const cells = firstRow.querySelectorAll('td');
                console.log('第一行数据列数:', cells.length);
                cells.forEach((td, index) => {
                    const text = td.textContent.trim().substring(0, 20);
                    console.log(`数据列 ${index + 1}: ${text}`);
                });
            }

            console.log('批量模式状态:', batchModeActive);
            console.log('=== 调试信息结束 ===');

            showToast('表格结构调试信息已输出到控制台', 'info');
        }

        // 测试表结构功能
        function testTableStructure() {
            if (!currentTable) {
                showToast('请先选择一个表', 'warning');
                return;
            }

            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: `action=get_table_structure&table=${currentTable}`
            })
            .then(response => response.json())
            .then(data => {
                console.log('表结构测试结果:', data);
                if (data.success) {
                    const fieldCount = data.data ? data.data.length : 0;
                    showToast(`表 ${currentTable} 共有 ${fieldCount} 个字段`, 'info');
                    console.log('表结构详情:', data);

                    // 强制切换到表结构标签并加载
                    switchTab('structure');
                    loadTableStructure(currentTable);
                } else {
                    showToast('获取表结构失败: ' + data.error, 'error');
                    console.error('表结构错误:', data);
                }
            })
            .catch(error => {
                console.error('测试表结构失败:', error);
                showToast('测试失败: ' + error.message, 'error');
            });
        }

        // 简单查询函数（不分页）
        function executeSimpleSql() {
            const sql = document.getElementById('sqlTextarea').value.trim();
            if (!sql) {
                showToast('请输入SQL语句', 'warning');
                return;
            }

            const resultContent = document.getElementById('resultContent');
            showLoadingSpinner(resultContent, '正在执行SQL查询...');

            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: `action=execute_sql&sql=${encodeURIComponent(sql)}&page=0&pageSize=0`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayResult(data);
                    if (data.warning) {
                        showToast(data.warning, 'warning');
                    } else {
                        showToast('查询执行成功', 'success');
                    }
                } else {
                    let errorHtml = `<div class="error">执行失败: ${data.error}</div>`;
                    if (data.debug) {
                        errorHtml += `<details style="margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                            <summary style="cursor: pointer; font-weight: bold;">调试信息</summary>
                            <pre style="margin: 10px 0; font-size: 12px; white-space: pre-wrap;">${JSON.stringify(data.debug, null, 2)}</pre>
                        </details>`;
                    }
                    resultContent.innerHTML = errorHtml;
                    showToast('SQL执行失败', 'error');
                    console.error('SQL Error:', data);
                }
            })
            .catch(error => {
                resultContent.innerHTML = `<div class="error">网络错误: ${error.message}</div>`;
                showToast('网络连接错误', 'error');
                console.error('Network Error:', error);
            });
        }

    </script>
</body>
</html>
