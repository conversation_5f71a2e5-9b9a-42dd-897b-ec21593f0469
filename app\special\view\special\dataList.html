﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>专项配置</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>

</head>

<body>
<div id="app" v-cloak>
    <div class="centainer">

        <el-tabs type="border-card">
            <el-tab-pane label="专项数据">
                <el-form :inline="false" size="mini" label-width="100px"
                         class="tableSearch">
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="专项类型">
                                <el-select @change="templateChange" v-model="searchData.table_order_id"
                                           placeholder="请选择">
                                    <el-option
                                            v-for="item in specialTypeOption"
                                            :key="item.id"
                                            :label="item.dc_special_title"
                                            :value="item.id">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>


                        <!--循环构造查询条件-->
                        <template v-for="(item ,index) in tableData.searchData ">
                            <template v-if="item.field_type =='date'">
                                <el-col :span="6" v-if="index>1?toggleSearchStatus:true">
                                    <el-form-item :label="item.field_name">
                                        <el-date-picker
                                                v-model="searchData[item.field]"
                                                type="date"
                                                placeholder="选择日期"
                                                format="yyyy 年 MM 月 dd 日"
                                                value-format="yyyy-MM-dd">
                                        </el-date-picker>
                                    </el-form-item>
                                </el-col>
                            </template>
                            <template v-else-if="item.field_type =='user'">
                                <el-col :span="6" v-if="index>1?toggleSearchStatus:true">
                                    <el-form-item :label="item.field_name">
                                        <el-input @focus="choiceUser(item.field,searchData[item.field])"
                                                  v-model="searchData[item.field + '_name']"></el-input>
                                    </el-form-item>
                                </el-col>

                            </template>
                            <template v-else-if="item.field_type =='dept'">
                                <el-col :span="6" v-if="index>1?toggleSearchStatus:true">
                                    <el-form-item :label="item.field_name">
                                        <el-input @focus="choiceDept(item.field,searchData[item.field])"
                                                  v-model="searchData[item.field + '_name']"></el-input>
                                    </el-form-item>
                                </el-col>
                            </template>

                            <template v-else>
                                <el-col :span="6" v-if="index>1?toggleSearchStatus:true">
                                    <el-form-item :label="item.field_name">
                                        <el-input v-model="searchData[item.field]"></el-input>
                                    </el-form-item>
                                </el-col>
                            </template>
                        </template>

                        <el-col :span="6">
                            <el-form-item>
                                <el-button type="primary" @click="getSpecialDataList" :loading="loading" size="mini"
                                           icon="el-icon-search">
                                    查询
                                </el-button>
                                <el-button @click="resetForm()" size="mini" icon="el-icon-delete">重置
                                </el-button>
                                <el-button type="text" @click="handleToggleSearch">
                                    {{toggleSearchStatus ? '收起' : '展开'}}
                                    <i :class="toggleSearchStatus?'el-icon-arrow-up':'el-icon-arrow-down'"></i>
                                </el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <!--功能区-->
                <el-row style="margin-bottom: 20px">
                    <el-button v-if="searchData.table_order_id"
                               type="primary"
                               @click="upLoad()" size="mini">导入专项数据
                    </el-button>
                </el-row>
                <!--列表区-->
                <template>
                    <template v-if="searchData.table_order_id">
                        <el-table
                                :data="tableData.data"
                                border
                                size="mini"
                                :height="height"
                                v-loading="loading"
                                style="width: 100%">
                            <el-table-column
                                    label="操作"
                                    width="230">
                                <template slot-scope="scope">
                                    <el-button @click="changeOneData(searchData.table_order_id,scope.row.id)"
                                               type="warning" size="small">编辑
                                    </el-button>

                                    <el-button @click="deleteOneSpecialData(searchData.table_order_id,scope.row.id)"
                                               type="danger" size="small">删除
                                    </el-button>
                                    <el-button @click="lookOneData(searchData.table_order_id,scope.row.id)"
                                               type="primary" size="small">查看
                                    </el-button>
                                </template>
                            </el-table-column>
                            <el-table-column
                                    type="index"
                                    label="序号"
                                    align="center"
                                    width="50">
                            </el-table-column>
                            <el-table-column v-for="(item, index) in tableData.header"
                                             :key="index"
                                             :prop="item.prop"
                                             show-overflow-tooltip
                                             :label="item.label"
                                             :width="item.width">

                            </el-table-column>


                        </el-table>
                    </template>
                    <template v-else>
                        <el-empty description="描述文字"></el-empty>
                    </template>


                </template>
                <!-- 分页区-->
                <div class="pagination">
                    <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="tableData.page"
                            :page-size="tableData.limit"
                            layout="total, sizes, prev, pager, next,jumper"
                            :page-sizes="[10, 20, 30, 40]"
                            :total="tableData.total">
                    </el-pagination>
                </div>
            </el-tab-pane>
            <el-tab-pane label="专项配置">
                <!--查询区-->
                <el-form :inline="false" size="mini" label-width="100px"
                         class="tableSearch">
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="专项名称">
                                <el-input clearable v-model="searchConfigData.dc_special_title"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="备注">
                                <el-input clearable v-model="searchConfigData.dc_remark"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item>
                                <el-button type="primary" @click="getSpecialConfigList" :loading="loading" size="mini"
                                           icon="el-icon-search">
                                    查询
                                </el-button>
                                <el-button @click="resetConfigForm()" size="mini" icon="el-icon-delete">重置
                                </el-button>

                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <el-row style="margin-bottom: 10px">
                    <el-button @click="addOneTemplate" size="mini" type="primary" icon="el-icon-plus">新增模板</el-button>
                </el-row>
                <template>
                    <el-table
                            :data="configData.data"
                            border
                            size="mini"
                            :height="height"
                            v-loading="loading"
                            style="width: 100%">

                        <el-table-column
                                label="操作"
                                width="320">
                            <template slot-scope="scope">
                                <el-button @click="changeOneTemplate(scope.row.id)"
                                           type="warning" size="small">编辑
                                </el-button>

                                <el-button @click="deleteOnemplate(scope.row.id)"
                                           type="danger" size="small">删除
                                </el-button>
                                <el-button @click="lookOneTemplate(scope.row.id)"
                                           type="primary" size="small">查看
                                </el-button>
                                <el-button @click="downTemplate(scope.row.file_path)"
                                           type="info" size="small">导入模板
                                </el-button>
                            </template>
                        </el-table-column>
                        <el-table-column
                                type="index"
                                label="序号"
                                align="center"
                                width="50">
                        </el-table-column>

                        <el-table-column prop="dc_special_title" label="专项名称" width="250" show-overflow-tooltip>

                        </el-table-column>
                        <el-table-column prop="dc_remark" label="备注" show-overflow-tooltip>

                        </el-table-column>
                        <el-table-column show-overflow-tooltip
                                         prop="dc_create_user_name"
                                         label="创建人"
                                         width="150">

                        </el-table-column>
                        <el-table-column show-overflow-tooltip
                                         prop="dc_created_at"
                                         label="创建时间"
                                         width="150">
                        </el-table-column>
                        <el-table-column width="150"
                                         show-overflow-tooltip
                                         prop="dc_updated_at"
                                         label="修改时间">
                        </el-table-column>
                    </el-table>
                </template>

                <!-- 分页区-->
                <div class="pagination">
                    <el-pagination
                            @size-change="handleConfigSizeChange"
                            @current-change="handleConfigCurrentChange"
                            :current-page="configData.page"
                            :page-size="configData.limit"
                            layout="total, sizes, prev, pager, next,jumper"
                            :page-sizes="[10, 20, 30, 40]"
                            :total="configData.total">
                    </el-pagination>
                </div>
            </el-tab-pane>

        </el-tabs>


    </div>
    <!--基本信息-->
    <up-load ref="upLoad" @ok="uploadSuccess"></up-load>
    <temp-info ref="templateInfo" @ok="templateBack"></temp-info>
    <info ref="info" @ok="infoDoBack"></info>
    <choice-user ref="personnelChoose" @ok="userChoiceBack"></choice-user>
    <choice-dept ref="deptChoose" @ok="deptChoiceBack"></choice-dept>
</div>
</body>
<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>
<script src="__PUBLIC__/static/js/es6-promise.auto.min.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        // 模板导入区
        components: {

            'temp-info': 'url:../../../app/special/view/special/modules/template.vue?v=2',
            'up-load': 'url:../../../app/special/view/special/modules/upData.vue?v=2',
            'info': 'url:../../../app/special/view/special/modules/info.vue?v=2',

            'choice-user': 'url:../../../public/vue/personnelChoose.vue',
            'choice-dept': 'url:../../../public/vue/deptChoose.vue',
        },
        data: function () {
            return {
                tableData: {
                    total: 0,
                    limit: 10,
                    data: [],
                    searchData: [],
                    page: 1
                },

                searchData: {
                    table_order_id: "",
                },


                searchConfigData: {

                },
                configData: {
                    total: 0,
                    limit: 10,
                    searchData: [],
                    data: [],
                    page: 1
                },

                dialogVisible: false,
                toggleSearchStatus: false,
                user_input_id: '',
                dept_input_id: '',
                fileData: [],
                flowOppion: [],
                specialTypeOption: [],
                codeInfo: [],
                /* 查询折叠 */
                is_super_man: false,
                loading: false,
                height: document.documentElement.clientHeight - 350,

            }
        },

        methods: {

            choiceUser(inpt_id, value) {
                console.log(inpt_id)
                console.log(value)
                this.user_input_id = inpt_id
                this.$refs.personnelChoose.choice(value);
            },
            userChoiceBack(res) {
                console.log(res);
                var user_id = [];
                var user_name = [];
                res.filter(function (param) {
                    user_id.push(param.uuid)
                    user_name.push(param.name)
                })
                this.searchData[this.user_input_id] = user_id.join(',');
                this.searchData[this.user_input_id + "_name"] = "";
                this.searchData[this.user_input_id + "_name"] = user_name.join(',');
                console.log(this.searchData)
                this.user_input_id = "";
                this.$forceUpdate();

            },


            choiceDept(inpt_id, value) {
                this.dept_input_id = inpt_id

                this.$refs.deptChoose.choice(value);
            },
            deptChoiceBack(res) {
                console.log(res);
                var user_id = [];
                var user_name = [];
                res.filter(function (param) {
                    user_id.push(param.uuid)
                    user_name.push(param.name)
                })
                this.searchData[this.dept_input_id] = user_id.join(',');
                this.searchData[this.dept_input_id + "_name"] = "";
                this.searchData[this.dept_input_id + "_name"] = user_name.join(',');
                console.log(this.searchData)
                this.dept_input_id = "";
                this.$forceUpdate();

            },


            /**
             * 展开收起
             * */
            handleToggleSearch() {
                this.toggleSearchStatus = !this.toggleSearchStatus;
            },
            getFlowList() {
                var vm = this;
                vm.loading = true;
                var url = "getFlowList";
                axios.post(url, {
                    searchData: this.searchData,
                    page: this.tableData.page,
                    limit: this.tableData.limit,
                }).then(function (res) {
                    vm.loading = false;
                    if (res.data.code == 200) {
                        vm.flowOppion = res.data.data
                    } else {
                        vm.$message({
                            message: res.data.msg,
                            type: res.data.type
                        });
                    }
                }).catch(function (error) {
                    vm.loading = false;
                    console.log(error);
                });
            },
            getCodeInfo() {
                var vm = this;
                vm.loading = true;
                var url = "getCodeInfo";
                axios.post(url, {
                    searchData: this.searchData,
                    page: this.tableData.page,
                    limit: this.tableData.limit,
                }).then(function (res) {
                    vm.loading = false;
                    if (res.data.code == 1000) {
                        vm.codeInfo = res.data.data
                    } else {
                        vm.$message({
                            message: res.data.msg,
                            type: res.data.type
                        });
                    }
                }).catch(function (error) {
                    vm.loading = false;
                    console.log(error);
                });
            },


            /**
             * 初始数据
             * */
            getSpecialDataList() {
                var vm = this;
                vm.loading = true;
                var url = "getSpecialDataList";
                axios.post(url, {
                    searchData: this.searchData,
                    page: this.tableData.page,
                    limit: this.tableData.limit,
                }).then(function (res) {
                    vm.loading = false;
                    if (res.data.code == 1000) {
                        vm.tableData.data = res.data.data.data
                        vm.tableData.header = res.data.header
                        vm.tableData.total = res.data.data.total
                        vm.is_super_man = res.data.is_super_man
                    } else if (res.data.code == 1001) {
                        vm.$alert(res.data.msg, '系统提示', {
                            confirmButtonText: '确定',

                        });
                    } else {
                        vm.$message({
                            message: res.data.msg,
                            type: res.data.type
                        });
                    }
                }).catch(function (error) {
                    vm.loading = false;
                    console.log(error);
                });
            },

            getSpecialTypeList() {
                var vm = this;
                vm.loading = true;
                var url = "getSpecialTypeList";
                axios.post(url, {
                    searchData: this.searchData,
                    page: this.tableData.page,
                    limit: this.tableData.limit,
                }).then(function (res) {
                    vm.loading = false;
                    if (res.data.code == 1000) {
                        vm.specialTypeOption = res.data.data

                        if (vm.searchData.table_order_id.length == 0 && vm.specialTypeOption.length > 0) {

                            console.log(vm.searchData.table_order_id)
                            console.log(vm.searchData.table_order_id.length)
                            console.log(vm.specialTypeOption.length)
                            console.log(vm.specialTypeOption[0])
                            console.log(vm.specialTypeOption[0].id)
                            vm.searchData.table_order_id = vm.specialTypeOption[0].id
                            console.log(vm.searchData)
                            vm.getSpecialDataList()
                        }
                        console.log(vm.specialTypeOption)
                    } else {
                        vm.$message({
                            message: res.data.msg,
                            type: res.data.type
                        });
                    }
                }).catch(function (error) {
                    vm.loading = false;
                    console.log(error);
                });
            },
            deleteOneSpecialData(type_id, data_id) {
                this.$confirm('此操作将永久删除, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.deleteOneSpecialDataNet(type_id, data_id)
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });
                });
            },
            deleteOneSpecialDataNet(type_id, data_id) {
                var vm = this;
                vm.loading = true;
                var url = "deleteOneSpecialData";
                axios.post(url, {
                    type_id: type_id,
                    data_id: data_id,
                }).then(function (res) {
                    vm.loading = false;
                    if (res.data.code == 1000) {
                        vm.getSpecialDataList()
                    }
                    vm.$message({
                        message: res.data.msg,
                        type: res.data.type
                    });
                }).catch(function (error) {
                    vm.loading = false;
                    console.log(error);
                });
            },


            /**
             * 重置
             * */
            resetForm() {
                var table_order_id = this.searchData.table_order_id;
                this.searchData = {
                    table_order_id: "",
                };
                this.searchData.table_order_id = table_order_id;
                this.getSpecialDataList()
            },


            /**
             * 分页
             * */
            handleCurrentChange(val) {
                this.tableData.page = val;
                this.getSpecialDataList()
            },

            handleSizeChange(val) {
                this.tableData.limit = val;
                this.getSpecialDataList()
            },


            /**
             *
             * 获取搜索条件
             */
            getSpecialDataSearch(template_id) {
                var vm = this;
                vm.loading = true;
                var url = "getSpecialDataSearch";
                axios.post(url, {
                    template_id: template_id
                }).then(function (res) {
                    vm.loading = false;
                    if (res.data.code == 1000) {
                        vm.tableData.searchData = res.data.search
                    } else {
                        vm.$message({
                            message: res.data.msg,
                            type: res.data.type
                        });
                    }
                }).catch(function (error) {
                    vm.loading = false;
                    console.log(error);
                });
            },


            resetConfigForm() {
                this.searchConfigData = {
                    dc_special_title: "",
                    dc_remark: "",
                };
                this.getSpecialConfigList()
            },
            getSpecialConfigList() {
                var vm = this;
                vm.loading = true;
                var url = "getSpecialConfigList";
                axios.post(url, {
                    searchConfigData: this.searchConfigData,
                    page: this.configData.page,
                    limit: this.configData.limit,
                }).then(function (res) {
                    vm.loading = false;
                    if (res.data.code == 1000) {
                        vm.configData.data = res.data.data.data
                        vm.configData.header = res.data.header
                        vm.configData.total = res.data.data.total
                        vm.is_super_man = res.data.is_super_man
                    } else {
                        vm.$message({
                            message: res.data.msg,
                            type: res.data.type
                        });
                    }
                }).catch(function (error) {
                    vm.loading = false;
                    console.log(error);
                });
            },
            deleteOnemplate(template_id) {
                this.$confirm('此操作将永久删除, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.deleteOnemplateNet(template_id)
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });
                });
            },
            deleteOnemplateNet(template_id) {
                var vm = this;
                vm.loading = true;
                var url = "deleteOnemplate";
                axios.post(url, {
                    template_id: template_id
                }).then(function (res) {
                    vm.loading = false;
                    if (res.data.code == 1000) {
                        vm.getSpecialConfigList()
                    }
                    vm.$message({
                        message: res.data.msg,
                        type: res.data.type
                    });
                }).catch(function (error) {
                    vm.loading = false;
                    console.log(error);
                });
            },
            handleConfigCurrentChange(val) {
                this.configData.page = val;
                this.getSpecialConfigList()
            },
            handleConfigSizeChange(val) {
                this.configData.limit = val;
                this.getSpecialConfigList()
            },

            flowInfo(URL) {
                openTab2(this, "info", "文件详情", URL);
            },


            //添加模板
            addOneTemplate() {
                this.$refs.templateInfo.addOne();
            },
            changeOneTemplate(template_id) {
                this.$refs.templateInfo.changeOne(template_id);
            },
            lookOneTemplate(template_id) {
                this.$refs.templateInfo.lookOne(template_id);
            },

            downTemplate(file_path) {
                window.open(file_path)
            },
            templateBack() {
                this.getSpecialConfigList();
                this.getSpecialTypeList();
                this.getSpecialDataList();
                this.getSpecialDataSearch(this.searchData.table_order_id);
            },


            changeOneData(type_id, data_id) {
                this.$refs.info.changeOne(type_id, data_id);
            },
            lookOneData(type_id, data_id) {
                this.$refs.info.lookOneData(type_id, data_id);
            },
            infoDoBack() {
                this.getSpecialDataList()
            },


            upLoad() {
                this.$refs.upLoad.addOne(this.searchData.table_order_id);
            },
            uploadSuccess() {
                this.getSpecialDataList();
            },


            templateChange(template_id) {
                console.log(template_id)
                this.toggleSearchStatus = false;
                this.tableData = {
                    total: 0,
                    limit: 10,
                    data: [],
                    searchData: [],
                    page: 1
                };
                this.getSpecialDataSearch(template_id);


                this.getSpecialDataList();
            },


        },
        created: function () {


            this.getSpecialConfigList();
            this.getSpecialTypeList();
            // this.getSpecialDataList();


           var is_admin = "{$isadmin}" ;
            console.log(is_admin)
        }
    })


</script>
</html>