<?php

namespace app\company\model;

use app\model\SettingModel;
use PhpOffice\PhpWord\TemplateProcessor;
use think\facade\Db;
use think\Model;
use Medoo\Mdb;
use app\model\FileModel;

class SeModel extends Model
{
    public static function seSave($param,$id=0)
    {
        $company = Db::table('top_company_info')->where([['id','=',$_SESSION['company']['id']],['status','=','1']])->find();
        if(empty($company)||empty($_SESSION['company']['id'])){
            result('',1003,'请先认证企业信息');
        }
        if(empty($company['review_id'])){
            result('',2003,'请先完成创标');
        }
        if(empty($param['year'])){
            result('',1001,'请选择自评年度');
        }
        if(date('Y',strtotime('+3month'))<=$param['year']){
            //result('',1001,'年度自评未开始');
        }
        if(date('Y',strtotime('-3month'))>$param['year']){
            result('',1001,'年度自评已结束');
        }
        foreach ($param['personnels'] as $k=>$v){
            if(empty($v['name'])){
                result('',1001,'请完善成员信息');
            }
            if($k>=1){
                break;
            }
        }
        $company = CompanyModel::codeToText($company);
        $data = [
            'company_id' => $company['id'],
            'company_name' => $company['name'],
            'industry' => $company['industry'],
            'specialty' => $company['specialty'],
            'residence' => $company['mb_operate_address'],
            'business_type' => $company['business_type'],
            'area_id' => $company['area_id'],
            'city_id' => $company['city_id'],
            'dept_id' => $company['dept_id'],
            'year' => $param['year'],
            'government' => $param['government'],
            'legal' => $company['legal'],
            'legal_mobile' => $company['legal_mobile'],
            'legal_fax' => $company['fax'],
            'contacts' => $param['contacts'],
            'contacts_tel' => $param['contacts_tel'],
            'contacts_fax' => $param['contacts_fax'],
            'contacts_mobile' => $param['contacts_mobile'],
            'contacts_email' => $param['contacts_email'],
            'old_level' => $param['old_level'],
            'group_name' => $param['group_name'],
            'date' => date('Y-m-d'),
            'personnels' => empty($param['personnels'])?'':json_encode($param['personnels']),
        ];
        if($id){
            $re = Db::table('top_company_evaluate')->where(['id'=>$id,'company_id'=>$_SESSION['company']['id']])->find();
            if(empty($re)){
                result('',1003,'数据不存在或已删除');
            }
//            dd($data);
            $re = Db::table('top_company_evaluate')->where(['id'=>$id])->update($data);
        }else{
            //判断本年是否已提交
            $isEvaluate = Db::table('top_company_evaluate')->where([
                'company_id'=>$_SESSION['company']['id'],
                'year'=>$data['year'],
            ])->find();
            if(!empty($isEvaluate)){
                result('',1003,'您的自评已提交，请勿重复提交');
            }
            $data['create_user_id'] = $_SESSION['company']['user_id'];
            $data['create_user_name'] = $_SESSION['company']['user_name'];
            $data['create_time'] = date('Y-m-d H:i:s');
            $data['status'] = 1;
            Db::table('top_company_evaluate')->insertGetId($data);
            $id = Db::table('top_company_evaluate')->where($data)->order('id desc')->field('id')->find()['id'];
        }
        return $id;
    }

    public static function scoreSave($param,$id=0)
    {
        $re = Db::table('top_company_evaluate')->where([['id','=',$id],['company_id','=',$_SESSION['company']['id']]])->find();
        if(empty($re)){
            result('',1003,'自评信息有误，请刷新重试');
        }
        $err = false;
        foreach ($param['data'] as $v){
            $score = Db::table('top_company_review_content_score')->where(['evaluate_id'=>$id,'content_id'=>$v['id']])->find();
            if(empty($score)){
                $data = [
                    'evaluate_id' => $id,
                    'element_id' => explode(',',$v['element_ids'])[1],
                    'element_ids' => $v['element_ids'],
                    'content_id' => $v['id'],
                    'sumscore' => $v['scores']??0,
                    'score' => $v['score']??0,
                    'reform' => $v['reform']??'',
                    'miss' => $v['miss']??0,
                    'resion' => $v['resion']??'',
                    'summary' => $v['summary']??'',
                ];
                Db::table('top_company_review_content_score')->insert($data);
            }else{
                $data = [
                    'element_id' => explode(',',$v['element_ids'])[1],
                    'element_ids' => $v['element_ids'],
                    'sumscore' => $v['scores']??0,
                    'score' => $v['score']??0,
                    'reform' => $v['reform']??'',
                    'miss' => $v['miss']??0,
                    'resion' => $v['resion']??'',
                    'summary' => $v['summary']??'',
                ];
                Db::table('top_company_review_content_score')->where(['id'=>$score['id']])->update($data);
            }
            if($v['scores']-$v['score']-$v['miss']>0&&empty($v['summary'])){
                $err = true;
            }
        }
        if($param['status']==1){
            if($err){
                result('',1003,'请完成所有内容打分，扣分项目必须写明扣分原因');//正式上线打开
            }
            $where = [
                ['evaluate_id', '=', $id],
            ];
            $res = Db::table('top_company_review_content_score')->alias('a')
                ->leftJoin('top_company_review_element b','a.element_id = b.id')
                ->where($where)
                ->field('a.*,b.name,b.sort')
                ->order('b.sort,a.id')
                ->select()->each(function ($item){
                    return $item;
                })->toArray();
            $data_score = [
                'score' => [],
                'deduct' => [],
                'total' => [],
            ];
            foreach ($res as $v){
                if(empty($scorelist[$v['element_id']])){
                    $scorelist[$v['element_id']] = [
                        'id' => $v['element_id'],
                        'name' => $v['name'],
                        'sumscore' => $v['sumscore'],
                        'deduct' => $v['sumscore']-$v['score']-$v['miss'],
                        'score' => $v['score'],
                        'miss' => $v['miss'],
                        'summary' => $v['summary'],
                    ];
                }else{
                    $scorelist[$v['element_id']]['summary'] .= $v['summary'];
                    $scorelist[$v['element_id']]['sumscore'] += round($v['sumscore'],2);
                    $scorelist[$v['element_id']]['deduct'] += round($v['sumscore']-$v['score']-$v['miss'],2);
                    $scorelist[$v['element_id']]['score'] += round($v['score'],2);
                    $scorelist[$v['element_id']]['miss'] += round($v['miss'],2);
                }
                /*if($v['sumscore']>($v['score']+$v['miss'])){
                    if(empty($data_score['deduct'][$v['element_id']])){
                        $data_score['deduct'][$v['element_id']] = [
                            'id' => $v['element_id'],
                            'name' => $v['name'],
                            'summary' => $v['summary'],
                            'deduct' => $v['sumscore']-$v['score']-$v['miss'],
                            'miss' => $v['miss'],
                        ];
                    }else{
                        $data_score['deduct'][$v['element_id']]['summary'] .= $v['summary'];
                        $data_score['deduct'][$v['element_id']]['deduct'] += $v['sumscore']-$v['score']-$v['miss'];
                        $data_score['deduct'][$v['element_id']]['miss'] += $v['miss'];
                    }
                }*/
                $data_score['total']['sumscore'] += round($v['sumscore'],2);
                $data_score['total']['score'] += round($v['score'],2);
                $data_score['total']['miss'] += round($v['miss'],2);
            }
            foreach ($scorelist as $v){
                $v['sumscore'] = round($v['sumscore'],2);
                $v['deduct'] = round($v['deduct'],2);
                $v['score'] = round($v['score'],2);
                $v['miss'] = round($v['miss'],2);
                $data_score['score'][] = $v;
            }
            foreach ($data_score['total'] as $k=>$v){
                $data_score['total'][$k] = round($v,2);
            }
            $data_score['total']['score'] = round($data_score['total']['score']/($data_score['total']['sumscore']-$data_score['total']['miss'])*100,2);
//            dd($data_score);
            Db::table('top_company_evaluate')->where([['id','=',$id]])->update(['score'=>json_encode($data_score)]);
        }
        return $id;
    }

    public static function endSave($param,$id=0)
    {
        $re = Db::table('top_company_evaluate')->where([['id','=',$id],['company_id','=',$_SESSION['company']['id']]])->find();
        if(empty($re)){
            result('',1003,'自评信息有误，请刷新重试');
        }
        if(empty($re['score'])){
            result('',1003,'请先完成打分');
        }
        $data = [
            'overview' => $param['overview'],
            'accident' => $param['accident'],
            'question' => $param['question'],
            'reform' => $param['reform'],
            'conclusion' => $param['conclusion'],
            'letter' => $param['letter'],
        ];
        if($param['status']==1){
            $data['status'] = 2;
            $data['submit_status'] = 1;  // 设置提交状态为已提交
            $data['submit_time'] = date('Y-m-d H:i:s');  // 设置提交时间
            $re = Db::table('top_company_evaluate')->where(['id'=>$id])->update($data);
            $re = self::setSebaogao($id);
            Db::table('top_company_evaluate')->where(['id'=>$id])->update(['files'=>$re['code']]);
        }else{
            $re = Db::table('top_company_evaluate')->where(['id'=>$id])->update($data);
        }
        return $id;
    }

    public static function setSebaogao($id=0){
        $result = Db::table('top_company_evaluate')->where([['id','=',$id]])->find();
        if(empty($result)){
            result('',1003,'自评数据不存在');
        }
        $score = json_decode($result['score'],true);
//        dd($score);
        $template = new TemplateProcessor(root_path() . 'public/word/se.docx');
        $template->setValue("company_name", $result['company_name']);
        $template->setValue("industry", $result['industry']);
        $template->setValue("specialty", $result['specialty']);
        $template->setValue("scorea", $score['total']['score']);
        $template->setValue("level", $result['level']);
        $template->setValue("year", date('Y',strtotime($result['date'])));
        $template->setValue("month", date('m',strtotime($result['date'])));
        $template->setValue("day", date('d',strtotime($result['date'])));
        $template->setValue("residence", $result['residence']);
        $template->setValue("type", $result['business_type']);
        $template->setValue("government", $result['government']);
        $template->setValue("legal", $result['legal']);
        $template->setValue("legal_mobile", $result['legal_mobile']);
        $template->setValue("legal_fax", $result['legal_fax']);
        $template->setValue("contacts", $result['contacts']);
        $template->setValue("contacts_tel", $result['contacts_tel']);
        $template->setValue("contacts_fax", $result['contacts_fax']);
        $template->setValue("contacts_mobile", $result['contacts_mobile']);
        $template->setValue("contacts_email", $result['contacts_email']);
        $template->setValue("group_name", $result['group_name']);
        $template->setValue("overview", $result['overview']);
        $template->setValue("accident", $result['accident']);
        $template->setValue("question", $result['question']);
        $template->setValue("conclusion", $result['conclusion']);
        $i = 1;
        $personnels = json_decode($result['personnels'],true);
        $template->cloneRow("name3", count($personnels)-2);
        foreach ($personnels as $k=>$v){
            $template->setValue("name".$i, $v['name']);
            $template->setValue("deptname".$i, $v['deptname']);
            $template->setValue("mobile".$i, $v['mobile']);
            $template->setValue("remark".$i, $v['remark']);
            if($i>=3){
                $template->setValue("name3#".($i-2), $v['name']);
                $template->setValue("deptname3#".($i-2), $v['deptname']);
                $template->setValue("mobile3#".($i-2), $v['mobile']);
                $template->setValue("remark3#".($i-2), $v['remark']);
            }
            $i++;
        }
        $template->cloneRow("sort", count($score['score']));
        foreach ($score['score'] as $k => $v) {
            $template->setValue("sort#" . ($k + 1), $k+1);
            $template->setValue("name#" . ($k + 1), $v['name']);
            $template->setValue("sumscore#" . ($k + 1), $v['sumscore']);
            $template->setValue("score#" . ($k + 1), $v['score']);
            $template->setValue("deduct#" . ($k + 1), $v['deduct']);
            $template->setValue("miss#" . ($k + 1), $v['miss']);
            $template->setValue("summary#" . ($k + 1), $v['summary']);
        }
        //导出文件备份
        $copyPath = config('filesystem.disks.oa.root') . "/tmp/".date('Ymd').'/' . time() . rand(1000, 9999) . '.docx';
        /*$template->saveAs($copyPath);
        header("Content-type: application/vnd.ms-word");
        header("Content-Disposition:attachment;filename=" . $result['company_name'].date('Y',strtotime($result['date'])) . '年度自评报告.docx');
        header('Cache-Control: max-age=0');
        readfile($copyPath);
        unlink($copyPath);
        exit;*/
        $pdfpath =  "tmp/".date('Ymd').'/' . time() . rand(1000, 9999) . '.pdf';
        $template->saveAs($copyPath);
        FileModel::wordToPdf($copyPath,config('filesystem.disks.oa.root').'/'.$pdfpath);
        $re = FileModel::saveFile($pdfpath,'se');
        //unlink($copyPath);
        return $re;
    }


    //参数格式化
    public static function codeToText($info){
        if(empty($info)){
            return $info;
        }
        foreach ($info as $k=>$v){
            $info[$k] = $v==='0000-00-00'?'':$v;
            if(is_int($v)){
                $info[$k] = (string)$v;
            }
            if($v===null){
                $info[$k] = '';
            }
        }
        return $info;
    }


}