<?php

// 测试验证完整性功能
require_once 'app/common.php';

echo "=== 数据完整性验证功能测试 ===\n\n";

// 测试数据
$testData = [
    // 明文数据
    '张三',
    '<EMAIL>',
    '13800138000',
    '123456789',
    
    // 模拟加密数据（Base64格式）
    'SGVsbG8gV29ybGQhVGhpcyBpcyBhIGxvbmcgZW5vdWdoIGJhc2U2NCBzdHJpbmcgdG8gdGVzdCBlbmNyeXB0aW9uIGRldGVjdGlvbg==',
    'QWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXoxMjM0NTY3ODkwQUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVo=',
    
    // HSM特定格式
    'HSM_abc123def456ghi789jkl012mno345pqr678stu901vwx234yz',
    'ENC_encrypted_data_example_with_prefix_marker',
    'some_data_HSM',
    'another_data_ENC',
    
    // 异常数据
    '',
    null,
    str_repeat('a', 70000), // 超长数据
];

echo "1. 测试 heuristicEncryptionCheck 函数\n";
echo str_repeat('-', 50) . "\n";

foreach ($testData as $index => $data) {
    if ($data === null) {
        $displayData = 'NULL';
    } elseif (empty($data)) {
        $displayData = '(空字符串)';
    } else {
        $displayData = strlen($data) > 50 ? substr($data, 0, 50) . '...' : $data;
    }
    
    $isEncrypted = heuristicEncryptionCheck($data);
    $status = $isEncrypted ? '✅ 已加密' : '❌ 未加密';
    
    echo sprintf("数据 %2d: %s - %s\n", $index + 1, $status, $displayData);
}

echo "\n2. 测试字段完整性验证逻辑\n";
echo str_repeat('-', 50) . "\n";

// 模拟验证字段完整性的函数
function testVerifyFieldIntegrity($value, $fieldName) {
    $result = [
        'is_valid' => true,
        'error_type' => '',
        'error_message' => '',
        'field_name' => $fieldName,
        'is_encrypted' => false,
        'can_decrypt' => false
    ];

    // 检查是否为空值
    if (empty($value)) {
        $result['is_valid'] = true; // 空值认为是有效的
        return $result;
    }

    // 检查是否为加密数据
    $isEncrypted = heuristicEncryptionCheck($value);
    $result['is_encrypted'] = $isEncrypted;

    if ($isEncrypted) {
        // 如果是加密数据，模拟解密验证（实际环境中会调用hsmDecrypt）
        try {
            // 这里只是模拟，实际应该调用 hsmDecrypt($value)
            $mockDecryptResult = strlen($value) > 10 ? 'mock_decrypted_data' : false;
            
            if ($mockDecryptResult === false || empty($mockDecryptResult)) {
                $result['is_valid'] = false;
                $result['error_type'] = 'decrypt_failed';
                $result['error_message'] = '加密数据解密失败，可能已损坏';
                $result['can_decrypt'] = false;
            } else {
                // 解密成功，验证解密后的数据是否合理
                $result['can_decrypt'] = true;
                
                if (strlen($mockDecryptResult) > 65535) {
                    $result['is_valid'] = false;
                    $result['error_type'] = 'data_too_long';
                    $result['error_message'] = '解密后数据长度超出合理范围';
                }
            }
        } catch (Exception $e) {
            $result['is_valid'] = false;
            $result['error_type'] = 'decrypt_exception';
            $result['error_message'] = '解密过程发生异常: ' . $e->getMessage();
            $result['can_decrypt'] = false;
        }
    } else {
        // 如果不是加密数据，检查数据格式是否合理
        if (strlen($value) > 65535) {
            $result['is_valid'] = false;
            $result['error_type'] = 'data_too_long';
            $result['error_message'] = '数据长度超出合理范围';
        }
    }

    return $result;
}

// 测试字段完整性验证
$testFields = ['name', 'email', 'phone', 'encrypted_field'];

foreach ($testData as $index => $data) {
    if ($data === null) continue;
    
    $fieldName = $testFields[$index % count($testFields)];
    $result = testVerifyFieldIntegrity($data, $fieldName);
    
    $displayData = strlen($data) > 30 ? substr($data, 0, 30) . '...' : $data;
    $status = $result['is_valid'] ? '✅ 正常' : '❌ 异常';
    $encStatus = $result['is_encrypted'] ? '加密' : '明文';
    
    echo sprintf("字段 %s: %s (%s) - %s", 
        $fieldName, 
        $status, 
        $encStatus, 
        $displayData
    );
    
    if (!$result['is_valid']) {
        echo " [错误: " . $result['error_message'] . "]";
    }
    
    echo "\n";
}

echo "\n3. 模拟批量验证完整性操作\n";
echo str_repeat('-', 50) . "\n";

// 模拟数据库记录
$mockRecords = [
    ['id' => 1, 'name' => '张三', 'email' => '<EMAIL>', 'phone' => '13800138000'],
    ['id' => 2, 'name' => 'SGVsbG8gV29ybGQ=', 'email' => 'bGVlQGV4YW1wbGUuY29t', 'phone' => 'MTM5MDAxMzkwMDA='],
    ['id' => 3, 'name' => 'HSM_encrypted_name', 'email' => 'ENC_encrypted_email', 'phone' => '13900139000'],
    ['id' => 4, 'name' => '', 'email' => 'invalid@', 'phone' => str_repeat('1', 70000)],
];

$fields = ['name', 'email', 'phone'];
$successCount = 0;
$validCount = 0;
$invalidCount = 0;

echo "开始批量验证...\n";

foreach ($mockRecords as $record) {
    $recordId = $record['id'];
    $integrityStatus = 1; // 默认完整性正常
    
    echo "验证记录 ID: {$recordId}\n";
    
    foreach ($fields as $field) {
        if (!isset($record[$field])) {
            continue;
        }
        
        $fieldValue = $record[$field];
        $fieldIntegrity = testVerifyFieldIntegrity($fieldValue, $field);
        
        $status = $fieldIntegrity['is_valid'] ? '✅' : '❌';
        $encType = $fieldIntegrity['is_encrypted'] ? '加密' : '明文';
        
        echo "  - {$field}: {$status} ({$encType})";
        
        if (!$fieldIntegrity['is_valid']) {
            echo " - " . $fieldIntegrity['error_message'];
            $integrityStatus = 0;
        }
        
        echo "\n";
    }
    
    // 模拟更新check_status字段
    echo "  更新 check_status = {$integrityStatus}\n";
    
    $successCount++;
    if ($integrityStatus === 1) {
        $validCount++;
    } else {
        $invalidCount++;
    }
    
    echo "\n";
}

echo "批量验证完成！\n";
echo "成功处理: {$successCount} 条记录\n";
echo "完整性正常: {$validCount} 条记录\n";
echo "完整性异常: {$invalidCount} 条记录\n";

echo "\n=== 测试完成 ===\n";
