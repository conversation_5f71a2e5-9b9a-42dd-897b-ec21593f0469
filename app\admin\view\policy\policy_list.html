<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>通知公告</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .el-form-item__label { width: 70px;}
		.el-menu-item, .el-submenu__title{height:40px;line-height:40px;}
        .el-form-item { margin-bottom: 0;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">
        <el-container>
            <el-main>
                <el-form :inline="true" :model="searchFrom" class="form-inline" @submit.native.prevent>
                    <el-form-item>
                        <el-radio-group @change="statusChange" size="mini" v-model="searchFrom.type">
                            <el-radio-button label="">全部</el-radio-button>
                            <el-radio-button v-for="(item,key) in types" :label="key">{{item}}</el-radio-button>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item>
                        <el-input v-model="searchFrom.title" size="mini" placeholder="标题" @keyup.enter.native="getData"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="getData()" size="mini">查询</el-button>
                        <el-button @click="reset()" size="mini">重置</el-button>
                    </el-form-item>
                    <el-form-item style="float: right">
                        <el-button v-if="isAdmin" type="primary" size="mini" @click="add">新增</el-button>
                    </el-form-item>
                </el-form>
                <el-table border
                          v-loading="loading"
                          :data="data"
                          style="width: 100%;margin-bottom: 20px;"
                          ref="qtable"
                          :height="height"
                          size="small">
                    <el-table-column
                            type="index"
                            label="序号"
                            align="center"
                            show-overflow-tooltip
                            width="60">
                    </el-table-column>
                    <el-table-column
                            prop="mb_type"
                            label="类型"
                            align="center"
                            show-overflow-tooltip
                            min-width="150">
                    </el-table-column>
                    <el-table-column
                            prop="title"
                            label="标题"
                            align="center"
                            show-overflow-tooltip
                            min-width="150">
                    </el-table-column>
                    <el-table-column
                            prop="summary"
                            label="简介"
                            align="center"
                            show-overflow-tooltip
                            min-width="200">
                    </el-table-column>
                    <el-table-column
                            prop="date"
                            label="发布时间"
                            align="center"
                            show-overflow-tooltip
                            min-width="100">
                    </el-table-column>
                    <el-table-column
                            v-show="isAdmin"
                            prop="is_show"
                            label="是否展示"
                            align="center"
                            min-width="70">
                        <template slot-scope="scope">
                            <el-switch size="mini" @change="upShow(scope.row)" v-model="scope.row.is_show"></el-switch>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="操作"
                            align="center"
                            width="220">
                        <template slot-scope="scope">
                            <el-button @click="info(scope.row)" size="mini" type="primary" >详情</el-button>
                            <el-button v-if="isAdmin" @click="edit(scope.row)" size="mini" type="warning" >编辑</el-button>
                            <el-button v-if="isAdmin" @click="deleteList(scope.row)" size="mini" type="danger" >删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <!--分页条total, sizes, prev, pager, next, jumper-->
                <div class="block">
                    <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="page"
                            :page-sizes="[10, 20, 50, 100, 500, 1000]"
                            :page-size="pageSize"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="total">
                    </el-pagination>
                </div>
            </el-main>
        </el-container>
        <!--新增弹窗-->
        <el-dialog :close-on-click-modal="false" :title="title" width="1000px" :visible.sync="dialogFormVisible">
            <el-form  :model="form" :rules="formRules" ref="form" class="oa-form">
                <el-descriptions class="margin-top" title="" label-style="width:100px;" content-style="width:200px;" :column="3" size="small" border>
                    <el-descriptions-item label="类型" :span="3">
                        <el-form-item label="">
                            <el-radio-group v-model="form.type" size="mini">
                                <el-radio v-for="(item,key) in types" :label="key">{{item}}</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="标题">
                        <el-form-item label="">
                            <el-input v-model="form.title" size="mini"></el-input>
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="发布日期">
                        <el-form-item label="">
                            <el-date-picker size="mini"
                                            v-model="form.date"
                                            type="date"
                                            placeholder="选择日期"
                                            value-format="yyyy-MM-dd">
                            </el-date-picker>
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="是否展示">
                        <el-form-item label="">
                            <el-switch size="mini" v-model="form.is_show"></el-switch>
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="简介" :span="3">
                        <el-form-item label="">
                            <el-input type="textarea" :rows="2" v-model="form.summary" size="mini"></el-input>
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="详细内容" :span="3">
                        <el-form-item label="">
                            <div style="line-height: 20px;">
                                <script id="container" name="content" type="text/plain" style="width: 100%"></script>
                            </div>
                        </el-form-item>
                    </el-descriptions-item>
                </el-descriptions>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogFormVisible = false" size="small">取 消</el-button>
                <el-button type="primary" @click="addSubmit" size="small">确 定</el-button>
            </div>
        </el-dialog>
        <info ref="infoChoose"></info>
    </div>
</div>

<!--<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>-->
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.all.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                title:'新增',
                dept:'',
                isAdmin:true,
                searchFrom: {
                    type: '',
                    keywords: '',
                },
                data: [],
                filelist: [],
                types: [],
                page: 1,
                pageSize: 20,
                total: 0,
                deptData: [],
                userData: [],
                dialogFormVisible: false,
                visible: false,
                loading: true,
                form:{},
                formRules: {
                    form_name: [{required: true, message: "请输入表单名称", trigger: "blur"}],
                },
                ueObj:null,
                height: document.documentElement.clientHeight - 160,
            };
        },
        components: {
            'info': 'url:/general/toppingsoft/app/admin/view/policy/module/info.vue',
        },
        methods: {
            statusChange(){
                this.searchFrom.page = 1;
                this.getData();
            },
            deptSet(dept){
                this.dept = dept;
                this.searchFrom.type = '';
                this.searchFrom.keywords = '';
                this.getData();
            },
            // 日期格式化
            dateFormatter (row, column) {
                let datetime = row[column.property];
                if(datetime){
                    var date=new Date(parseInt(datetime)* 1000);
                    var year = date.getFullYear().toString().padStart(4, "0");
                    var mon = (date.getMonth() + 1).toString().padStart(2, "0");
                    var day = date.getDate().toString().padStart(2, "0");
                    var hours = date.getHours().toString().padStart(2, "0");
                    var minu = date.getMinutes().toString().padStart(2, "0");
                    var sec = date.getSeconds().toString().padStart(2, "0");
                    return year+'-'+mon+'-'+day+' '+hours+':'+minu;
                }
                return ''
            },
            handleSizeChange: function(val) {
                this.pageSize = val;
                this.getData();
                console.log('每页 ${val} 条');
            },
            handleCurrentChange: function(val) {
                this.page = val;
                this.getData();
                console.log('当前页: ${val}');
            },
            add(){
				var date=new Date();
                    var year = date.getFullYear().toString().padStart(4, "0");
                    var mon = (date.getMonth() + 1).toString().padStart(2, "0");
                    var day = date.getDate().toString().padStart(2, "0");
                this.title = '新增';
                this.form = {
                    type:"",
                    user_type:["1","2","3","4"],
                    date:year+'-'+mon+'-'+day,
                    is_show:true,
                    is_send:false,
                    content:'',
                };
                this.filelist = [];
                this.dialogFormVisible = true;
                this.useUedotor();
            },
            edit(row){
                this.title = '编辑';
                this.form = JSON.parse(JSON.stringify(row));
                this.filelist = row.files;
                this.dialogFormVisible = true;
                this.useUedotor();
            },
            info(row){
                this.$refs.infoChoose.open(row.id);
            },
            uploadSuccess(res, file) {
                let thisfile = {};
                if(res.code==0){
                    thisfile.name = res.data.filename;
                    thisfile.url = res.data.filepath;
                    thisfile.id = res.data.id;
                    this.filelist.push(thisfile);
                }
                //this.data.avatar = '/general/toppingsoft/public/storage/tmp/20220517/1652774313182jpg';
            },
            uploadBefore(file) {
                console.log(file.type);
                // const isJPG = file.type === 'application/pdf';
                // const isLt2M = file.size / 1024 / 1024 < 2;
                // return isJPG && isLt2M;
            },
            handleRemove(file, fileList){
                this.filelist = fileList;
            },
            deleteList(row){
                var _this = this;
                _this.$confirm("确认删除？", "提示", {}).then(() => {
                    _this.addLoading = true;
                    var param = {};
                    param.id = row.id;
                    param.dept = _this.dept;
                    axios.post('policyDel',param).then(function (res) {
                        _this.addLoading = false;
                        if( res.data.code == 0 ) {
                            _this.$message({
                                message: res.data.msg,
                                type: "success"
                            });
                            _this.dialogFormVisible = false;
                            _this.getData();
                        }else{
                            _this.$message({
                                message: res.data.msg,
                                type: "error"
                            });
                        }
                    }).catch(function (error) {
                        console.log(error);
                    });
                });
            },
            //数据初始化
            reset(){
                this.searchFrom = {};
                this.page = 1;
                this.pageSize = 20;
                this.getData();
            },
            //数据加载
            getData() {
                var _this = this;
                var param = _this.searchFrom;
                param._ajax = 1;
                param.dept = _this.dept;
                param.page = _this.page;
                param.pageSize = _this.pageSize;
                var url = "policyList";
                axios.post(url,param).then(function (res) {
                    if( res.data.code == 0 ){
                        _this.data = res.data.data.data;
                        _this.searchFrom.page = res.data.data.current_page;
                        _this.searchFrom.pageSize = res.data.data.per_page;
                        _this.total = res.data.data.total;
                        _this.types = res.data.data.types;
                        _this.loading = false;
                        _this.$nextTick(()=>{
                            _this.$refs.qtable.doLayout();
                        })
                    }else{
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            addSubmit: function () {
                this.$refs.form.validate(valid => {
                    var _this = this;
                    if (valid) {
                        _this.$confirm("确认保存？", "提示", {}).then(() => {
                            _this.addLoading = true;
                            var param = _this.form;
                            param.dept = _this.dept;
                            var url = "savePolicy";
                            _this.ueObj.ready(()=> {
                                param.content = _this.ueObj.getContent();
                            });
                            param.files = _this.filelist;
                            axios.post(url,param).then(function (res) {
                                console.log(res);
                                _this.addLoading = false;
                                if( res.data.code == 0 ) {
                                    _this.$message({
                                        message: res.data.msg,
                                        type: "success"
                                    });
                                    _this.dialogFormVisible = false;
                                    _this.getData();
                                }else{
                                    _this.$message({
                                        message: res.data.msg,
                                        type: "error"
                                    });
                                }
                            }).catch(function (error) {
                                console.log(error);
                            });
                        });
                    }
                });
            },
            /**
             * 部门选择
             * */
            choiceDept:function(field_id,field_name,isRadio) {
                this.$refs.deptChoose.visible = true;
                this.deptChooseFieldId = field_id;
                this.deptChooseFieldName = field_name;
                this.$refs.deptChoose.isRadio = isRadio; // 是否单选  true 单选  false 多选
                this.$refs.deptChoose.checkedKeys=this.data[this.deptChooseFieldId].toString().split(",");
                this.$refs.deptChoose.initialize();
            },
            /**
             * 部门选择 - 单选
             * */
            choiceDept2() {
                this.$refs.deptChoose.visible = true;
                this.$refs.deptChoose.isRadio = true; // 是否单选  true 单选  false 多选
                this.$refs.deptChoose.initialize(this.deptData);
            },
            /**
             * 人员选择
             * */
            choiceUser() {
                this.$refs.personnelChoose.visible = true;
                this.$refs.personnelChoose.isRadio = false; // 是否单选  true 单选  false 多选
                this.$refs.personnelChoose.initialize(this.userData);
            },
            /**
             * 人员选择 - 单选
             * */
            choiceUser2() {
                this.$refs.personnelChoose.visible = true;
                this.$refs.personnelChoose.isRadio = true; // 是否单选  true 单选  false 多选
                this.$refs.personnelChoose.initialize(this.userData);
            },
            /**
             * 部门选择回调数据
             * */
            deptFun:function(data)
            {
                var _k = []
                var _v = [];
                for(var i in data){
                    _k.push(data[i]['dept_id']);
                    _v.push(data[i]['dept_name']);
                }
                this.deptData = _k;
                this.form[this.deptChooseFieldId] = _k.join(",");
                this.form[this.deptChooseFieldName] = _v.join(",");
            },
            /**
             * 人员选择回调数据
             * */
            userFun(data) {
                this.userData = data;
                console.log(data);
            },
            upShow(row){
                var _this = this;
                var url = "upPolicy";
                axios.post(url,{id:row.id}).then(function (res) {
                    if( res.data.code == 0) {
                        row.is_show = res.data.data.status;
                    }else{
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        row.is_show = !row.is_show;
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            useUedotor(){
                this.ueObj = UE.getEditor('container',{
                    /*toolbars: [
                        ['fullscreen', 'source', 'undo', 'redo'],
                        ['bold', 'italic', 'underline', 'fontborder',
                            'strikethrough', 'superscript', 'subscript',
                            'removeformat', 'formatmatch', 'autotypeset',
                            'blockquote', 'pasteplain', '|', 'forecolor',
                            'backcolor', 'insertorderedlist','paragraph','fontfamily','fontsize','lineheight',
                            'insertunorderedlist', 'selectall', 'cleardoc']
                    ],*/
                    initialFrameHeight:200,
                    zIndex : 2003 ,
                    autoHeightEnabled:false,
                });
                var content=this.form.content
                this.ueObj.ready(function() {
                    //设置编辑器的内容
                    this.setContent(content);
                });
            },
        },
        mounted() {
            //获取列表
			var height = document.documentElement.clientHeight-50;
			$('.el-aside').css('height',height);
            this.getData();
        }
    })
</script>


</body>
</html>