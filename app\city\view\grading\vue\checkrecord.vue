<style>
.my-autocomplete li{line-height: normal;padding: 7px;}
.my-autocomplete li .name{text-overflow: ellipsis;overflow: hidden;}
.my-autocomplete li .addr{font-size: 12px;color: #b4b4b4;}
.my-autocomplete li .highlighted{color: #ddd;}
.mytable {border-collapse:collapse;width: 100%;}
.mytable thead tr td {border: 1px solid #333;font-weight: 700; padding: 10px;background: #f5f7fa;text-align: center;}
.mytable tbody tr th {border: 1px solid #333;font-weight: 700; padding: 10px;text-align: center;font-size: 16px;}
.mytable tbody tr td {border: 1px solid #333; padding: 10px;text-align: center;font-size: 16px;font-family: 宋体;text-align: left;}
.mytable .active td{ background: #f2f2f2;}
.mytable tbody tr td p{line-height: 30px;}
.el-image {
  position: relative;
  display: inline-block;
  overflow: hidden;
  width: 150px;
  height: 100px;
  border: 1px dashed #d9d9d9;
}
</style>
<template>
  <el-dialog :title="title" :close-on-click-modal="false"  modal="false" :visible.sync="visible" width="800px" top="10px" @close="refresh()" append-to-body="true" label-position="top">
    <el-descriptions class="margin-top" :column="2" border label-style="width:100px" content-style="width:300px">
      <el-descriptions-item label="企业名称">
          {{data.company_name}}
      </el-descriptions-item>
      <el-descriptions-item label="地址">
          {{data.address}}
      </el-descriptions-item>
      <el-descriptions-item label="区县">
          {{data.area_name}}
      </el-descriptions-item>
      <el-descriptions-item label="联系人">
          {{data.manager}}
      </el-descriptions-item>
      <el-descriptions-item label="电话">
          {{data.manager_mobile}}
      </el-descriptions-item>
      <el-descriptions-item label="行业类别">
          {{data.industry}}
      </el-descriptions-item>
      <el-descriptions-item label="证书编号">
          {{data.certificate_code }}
      </el-descriptions-item>
      <el-descriptions-item label="证书生效时间">
          {{data.certificate_start_date }}
      </el-descriptions-item>
      <el-descriptions-item label="证书有效期">
          {{data.certificate_end_date}}
      </el-descriptions-item>
      <el-descriptions-item label="申请等级">
          {{data.level}}
      </el-descriptions-item>
      <el-descriptions-item label="申请时间">
          {{data.apply_time}}
      </el-descriptions-item>
      <el-descriptions-item label="状态">
          <el-tag v-if="data.status==0" type="">未提交</el-tag>
          <el-tag v-if="data.status==1" type="info">审批中</el-tag>
          <el-tag v-if="data.status==7" type="primary">已通过</el-tag>
          <el-tag v-if="data.status==5" type="danger">未通过</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="证书附件" :span="2">
          <div v-if="data.file && data.file.length > 0">
            <p v-for="file in data.file" :key="file.id">
              <el-link @click="preview(file)" type="primary">{{file.name}}</el-link>
            </p>
          </div>
          <span v-else>暂无证书附件</span>
      </el-descriptions-item>
      <el-descriptions-item label="备注说明" :span="2">
          {{data.content}}
      </el-descriptions-item>
    </el-descriptions>
    <div slot="footer" class="dialog-footer">
        <el-button v-if="data.status==1" type="primary" @click="submit(7)" size="small">审核通过</el-button>
        <el-button v-if="data.status==1" type="danger" @click="submit(5)" size="small">驳回</el-button>
        <el-button @click="visible = false" size="small">关闭</el-button>
    </div>
    <el-dialog width="800" title="审核" :visible.sync="dialogFormVisible" :close-on-click-modal="false" append-to-body>
      <el-form :model="form" :rules="rules" ref="form" label-width="150px" class="demo-ruleForm">
        <el-form-item label="审批状态" prop="status">
          <el-radio v-model="form.status" :label="7">审批通过</el-radio>
          <el-radio v-model="form.status" :label="5">驳回</el-radio>
        </el-form-item>
        <el-form-item label="审批意见" prop="content">
          <el-input type="textarea" :rows="3" placeholder="请输入内容" v-model="form.content"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit" v-loading.fullscreen.lock="loading">确定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <preview ref="preview"></preview>

    <el-dialog :visible.sync="dialogVisible" :modal=true>
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>

  </el-dialog>
</template>
<script>
module.exports = {
  name: "add",
  // 模板导入区
  components: {
    'preview': 'url:/general/toppingsoft/public/vue/preview.vue',
  },
  data:function() {
    return {
      id:0,
      isAdmin:false,
      visible: false,
      dialogFormVisible: false,
      title: '详情',
      loading: false,
      noMore: false,
      user_id: 0,
      filename: '企业定级申请表（区县级主管部门盖章扫描件）',
      data: {
      },
      form: {
        status:'',
        content:'',
        files:[],
      },
      is_see:0,
      files:[],
      details:[],
      cards:{},
      type:{},
      pcas: [],
      rules:[],
      restaurants2:[],
      height: document.documentElement.clientHeight - 370,
      dialogImageUrl: '',
      dialogVisible: false,
      tableData: [],
      imageList: [],
    }
  },
  computed: {
    disabled () {
      return this.loading || this.noMore
    }
  },
  mounted: function(){
    //this.getConfig();
  },
  created:function(){
  },
  methods: {
    /**
     * 打开弹窗调用方法
     * */
    open: function (row) {
      var _this =this;
      _this.id = row.id;
      _this.visible = true;
      _this.noMore = false;
      _this.getInfo(row.id);
    },
    closeDialog: function () {
      this.visible = false;
    },
    refresh: function () {

    },
    uploadBefore(file) {
      const isJPG = file.type === 'image/jpeg';
      const isPNG = file.type === 'image/png';
      const isPDF = file.type === 'application/pdf';
      if(!isJPG&&!isPNG&&!isPDF){
        this.$message.error('请上传jpg/png/pdf文件');
      }
      return isJPG||isPNG||isPDF;
    },
    uploadSuccess(res, file,fileList) {
      var files = [];
      for(var i in fileList){
        files.push(fileList[i].response.data??fileList[i]);
      }
      this.form.files = files;
    },
    handleRemove(file, fileList) {
      var files = [];
      for(var i in fileList){
        files.push(fileList[i].response.data??fileList[i]);
      }
      this.form.files = files;
    },
    preview: function (file) {
      file = file.response?file.response.data:file;
      this.$refs.preview.open(file.url,file.name);
    },
    submit:function(status){
      var _this = this;
      _this.form.status = status;
      _this.dialogFormVisible = true;
    },
    onSubmit:function(status){
      var _this = this;
      var param = _this.form;
      param.id = _this.id;
      _this.loading = true;
      axios.post("recordCheck", param).then(function (res) {
        _this.$message({
          message: res.data.msg,
          type: res.data.type
        });
        if (res.data.code == 0) {
          _this.visible = false;
          _this.dialogFormVisible = false;
          window.parent.getMessage();
          _this.$emit("ok");
        }
        _this.loading = false;
      }).catch(function (error) {
        console.log("出现错误:",error);
      });
    },
    getInfo:function(id){
      var _this = this;
      if(id){
        _this.loading = true;
        axios.post("recordInfo", {id:id}).then(function (res) {
          if (res.data.code == 0) {
            _this.data=res.data.data;
          }else {
            _this.$message({
              message: res.data.msg,
              type: res.data.type
            });
          }
          _this.loading = false;
        }).catch(function (error) {
          console.log("出现错误:",error);
        });
      }
    },
    addContacts: function(){
      this.form = {};
      this.dialogFormVisible = true;
    },
    editContacts: function(row){
      this.form = row;
      this.dialogFormVisible = true;
    },
  }
}
</script>


