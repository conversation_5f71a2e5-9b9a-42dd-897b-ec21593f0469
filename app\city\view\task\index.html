<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>评审任务</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/company.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .my-content p{ line-height:20px; margin: 10px 0;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">
        <el-form :inline="true" :model="searchFrom" class="form-inline" @submit.native.prevent>
            <el-form-item>
                <el-radio-group @change="statusChange" size="mini" v-model="searchFrom.status">
                    <el-radio-button label="">全部</el-radio-button>
                    <el-radio-button label="1">今日评审</el-radio-button>
                </el-radio-group>
            </el-form-item>
            <el-form-item>
                <el-input v-model="searchFrom.title" size="mini" placeholder="企业名称"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="getData()" size="mini">查询</el-button>
                <el-button @click="reset()" size="mini">重置</el-button>
            </el-form-item>
        </el-form>
        <el-table border
                  v-loading="loading"
                  :data="data"
                  style="width: 100%;margin-bottom: 20px;"
                  ref="qtable"
                  :height="height"
                  size="small">
            <el-table-column
                    type="index"
                    label="序号"
                    align="center"
                    width="50">
            </el-table-column>
            <el-table-column
                    prop="company_name"
                    label="企业名称"
                    align="center"
                    show-overflow-tooltip
                    min-width="200">
                <template slot-scope="scope">
                    <!--<el-link :href="'/general/toppingsoft/index.php/city/company/info?id='+scope.row.company_id" target="_blank" type="primary" v-html="scope.row.company_name"></el-link>-->
                    <el-link @click="company_info(scope.row)" type="primary" v-html="scope.row.company_name"></el-link>
                </template>
            </el-table-column>
            <el-table-column
                    prop="level"
                    label="申请等级"
                    align="center"
                    width="100">
            </el-table-column>
            <el-table-column
                    prop="date"
                    label="评审日期"
                    align="center"
                    width="100">
            </el-table-column>
            <el-table-column
                    prop="mb_status"
                    label="任务状态"
                    align="center"
                    show-overflow-tooltip
                    width="120">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.status==1" type="info">待派发</el-tag>
                    <el-tag v-if="scope.row.status==7" type="primary">审核通过</el-tag>
                    <el-tag v-if="scope.row.status==5" type="danger">已驳回</el-tag>
                    <el-tag v-if="scope.row.status==8" type="">评审中</el-tag>
                    <el-tag v-if="scope.row.status==9" type="">评审结束</el-tag>
                </template>
            </el-table-column>
            <el-table-column
                    prop="element_name"
                    label="评审人员"
                    align="center"
                    show-overflow-tooltip
                    min-width="120">
            </el-table-column>
            <el-table-column
                    label="满意度调查"
                    align="center"
                    width="120">
                <template slot-scope="scope">
                    <el-button
                        v-if="scope.row.satisfaction_survey_status == 1"
                        size="small"
                        type="success"
                        @click="viewSatisfactionSurvey(scope.row)">
                        查看
                    </el-button>
                </template>
            </el-table-column>
            <el-table-column
                    label="廉政问卷"
                    align="center"
                    width="120">
                <template slot-scope="scope">
                    <el-button
                        v-if="scope.row.integrity_survey_status == 1"
                        size="small"
                        type="success"
                        @click="viewIntegritySurvey(scope.row)">
                        查看
                    </el-button>
                </template>
            </el-table-column>
            <el-table-column
                    label="操作"
                    align="center"
                    width="150">
                <template slot-scope="scope">
                    <el-button size="small" @click="info(scope.row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
    <!-- 查看满意度调查弹出框 -->
    <el-dialog
        title="查看满意度调查"
        :visible.sync="viewSatisfactionDialogVisible"
        width="800px"
        :close-on-click-modal="false">
        <div v-loading="viewSatisfactionLoading">
            <div style="padding: 20px;">
                <el-form label-width="200px">
                    <el-form-item label="被评审企业名称：">
                        <el-input v-model="viewSatisfactionData.company_name" readonly></el-input>
                    </el-form-item>
                    <el-form-item label="填表时间：">
                        <el-input v-model="viewSatisfactionData.field2" readonly></el-input>
                    </el-form-item>
                    <el-form-item label="填表人及联系电话：">
                        <el-input v-model="viewSatisfactionData.field3" readonly></el-input>
                    </el-form-item>
                    <el-form-item label="现场评审人员是否存在违法廉洁纪律行为：">
                        <el-input v-model="viewSatisfactionData.field4" readonly type="textarea" :rows="3"></el-input>
                    </el-form-item>
                    <el-form-item label="您对本次评审成绩是否满意：">
                        <el-input v-model="viewSatisfactionData.field5" readonly></el-input>
                    </el-form-item>
                    <el-form-item label="您对本次评审人员的评审工作是否满意：">
                        <el-input v-model="viewSatisfactionData.field6" readonly></el-input>
                    </el-form-item>
                    <el-form-item label="您的意见与建议：">
                        <el-input v-model="viewSatisfactionData.field7" readonly type="textarea" :rows="3"></el-input>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="viewSatisfactionDialogVisible = false">确定</el-button>
        </span>
         </el-dialog>

    <!-- 查看廉政问卷文件弹出框 -->
    <el-dialog
        title="查看廉政问卷"
        :visible.sync="viewIntegrityDialogVisible"
        width="500px"
        :close-on-click-modal="false">
        <div v-loading="viewIntegrityLoading">
            <div style="padding: 20px; text-align: center;">
                <div style="margin-bottom: 20px;">
                    <i class="el-icon-document" style="font-size: 64px; color: #409EFF;"></i>
                </div>
                <div style="margin-bottom: 15px;">
                    <strong>文件名：</strong>{{ integrityFileInfo.file_name }}
                </div>
                                 <div style="margin-bottom: 15px;">
                     <strong>文件类型：</strong>{{ integrityFileInfo.file_ext ? integrityFileInfo.file_ext.toUpperCase() : '' }}
                 </div>
                <div style="margin-bottom: 20px;">
                    <strong>文件大小：</strong>{{ integrityFileInfo.file_size }}
                </div>
                <el-alert
                    title="该文件不支持在线预览，请下载后查看"
                    type="info"
                    :closable="false"
                    show-icon>
                </el-alert>
            </div>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="viewIntegrityDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="downloadFile">
                <i class="el-icon-download"></i> 下载文件
            </el-button>
        </span>
    </el-dialog>

    <!-- PDF预览弹出框 -->
    <el-dialog
        :title="pdfPreviewTitle"
        :visible.sync="pdfPreviewVisible"
        width="90%"
        top="10px"
        append-to-body
        :before-close="closePdfPreview"
        custom-class="pdf-preview-dialog">
        <iframe
            :src="pdfPreviewUrl"
            width="100%"
            :height="pdfPreviewHeight"
            border="0"
            frameborder="0"
            framespacing="0"
            marginheight="0"
            marginwidth="0"
            style="border: none;">
        </iframe>
    </el-dialog>

    <company_info ref="company_info" @ok="getData()"></company_info>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                title: '新增',
                searchFrom: {
                    status: 1,
                    title: '',
                },
                data: [],
                visible: false,
                loading: false,
                // 查看满意度调查相关数据
                viewSatisfactionDialogVisible: false,
                viewSatisfactionLoading: false,
                viewSatisfactionData: {
                    company_name: '',
                    field2: '',
                    field3: '',
                    field4: '',
                    field5: '',
                    field6: '',
                    field7: '',
                    time: ''
                },
                // 查看廉政问卷相关数据
                viewIntegrityDialogVisible: false,
                viewIntegrityLoading: false,
                integrityFileInfo: {
                    file_name: '',
                    file_ext: '',
                    file_url: '',
                    is_pdf: false,
                    file_size: ''
                },
                // PDF预览相关数据
                pdfPreviewVisible: false,
                pdfPreviewUrl: '',
                pdfPreviewTitle: '',
                pdfPreviewHeight: document.documentElement.clientHeight - 180,
                height: document.documentElement.clientHeight - 155,
            };
        },
        components: {
            'company_info': 'url:/general/toppingsoft/app/city/view/company/vue/info.vue?v=1',
        },
        watch: {
            $route() {
                //切换标签时
                this.handleRefreshTable();
            }
        },
        created() {
            let _this = this;
            //窗口改变时
            window.onresize = function () {
                _this.handleRefreshTable();
            };
        },
        updated() {
            //数据改变时
            this.handleRefreshTable();
        },
        methods: {
            selectHandle(row,index) {
                console.log(row)
                return row.status==1||row.status==0;
            },
            statusChange() {
                this.page = 1;
                this.getData();
            },
            //数据初始化
            reset() {
                this.searchFrom.title='';
                this.page = 1;
                this.pageSize = 20;
                this.getData();
            },
            //数据加载
            getData() {
                var _this = this;
                var param = _this.searchFrom;
                param._ajax = 1;
                _this.loading = true;
                axios.post('', param).then(function (res) {
                    if (res.data.code == 0) {
                        _this.data = res.data.data.data;
                        _this.$nextTick(() => {
                            // 这里的代码会在DOM更新完成后执行
                            _this.loading = false;
                        });
                        _this.handleRefreshTable();
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            check(row,status){
                var _this = this;
                var param = {};
                param.id = row.id;
                param.status = status;
                if(status==7){
                    _this.$confirm('确定接收评审任务，接收后请按时到达现场参与评审！', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        _this.loading = true;
                        axios.post('check', param).then(function (res) {
                            _this.loading = false;
                            if (res.data.code == 0) {
                                _this.$message({
                                    message: res.data.msg,
                                    type: "success"
                                });
                                _this.getData();
                            } else {
                                _this.$message({
                                    message: res.data.msg,
                                    type: "error"
                                });
                            }
                        }).catch(function (error) {
                            console.log(error);
                        });
                    });
                }else{
                    this.$prompt('请填写请假原因', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        inputPattern: /^\S+$/,
                        inputErrorMessage: '请填写请假原因'
                    }).then(({value}) => {
                        _this.loading = true;
                        param.reason = value;
                        axios.post('check', param).then(function (res) {
                            _this.loading = false;
                            if (res.data.code == 0) {
                                _this.$message({
                                    message: res.data.msg,
                                    type: "success"
                                });
                                _this.getData();
                            } else {
                                _this.$message({
                                    message: res.data.msg,
                                    type: "error"
                                });
                            }
                        }).catch(function (error) {
                            console.log(error);
                        });
                    }).catch(function (error){
                        console.log(error);
                    });
                }
            },
            review(row){
                location.href = 'review?id='+row.id;
            },
            info(row){
                location.href = 'info?id='+row.id;
            },
            company_info (row) {
                var tmp = row;
                tmp.id = row.company_id;
                this.$refs.company_info.open(tmp);
            },
            retract(row){
                var _this = this;
                this.$prompt('放弃评审半年内不可申请评审，确认请在下方输入框输入“放弃评审”', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    inputPattern: /^放弃评审$/,
                    inputErrorMessage: '输入有误'
                }).then(({value}) => {
                    _this.loading = true;
                    var param = {};
                    param.id = row.id;
                    axios.post('retract', param).then(function (res) {
                        _this.loading = false;
                        if (res.data.code == 0) {
                            _this.$message({
                                message: res.data.msg,
                                type: "success"
                            });
                            _this.getData();
                        } else {
                            _this.$message({
                                message: res.data.msg,
                                type: "error"
                            });
                        }
                    }).catch(function (error) {
                        console.log(error);
                    });
                }).catch(function (error){
                    console.log(error);
                });
            },
            handleRefreshTable: function () {
                this.$nextTick(function () {
                    var refList = this.$refs;
                    if (refList) {
                        for (var i of Object.keys(refList)) {
                            if (refList[i] && refList[i].doLayout) {
                                refList[i].doLayout();
                            }
                        }
                    }
                });
            },
            // 查看满意度调查
            viewSatisfactionSurvey(row) {
                var _this = this;
                _this.viewSatisfactionLoading = true;
                _this.viewSatisfactionDialogVisible = true;

                // 调用后端接口获取满意度调查数据
                axios.post('/general/toppingsoft/index.php/city/task/viewSatisfactionSurvey', {
                    task_id: row.id
                }).then(function (res) {
                    _this.viewSatisfactionLoading = false;
                    if (res.data.code == 0) {
                        _this.viewSatisfactionData = res.data.data;
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        _this.viewSatisfactionDialogVisible = false;
                    }
                }).catch(function (error) {
                    _this.viewSatisfactionLoading = false;
                    _this.$message({
                        message: "加载失败，请重试",
                        type: "error"
                    });
                    _this.viewSatisfactionDialogVisible = false;
                    console.log("出现错误:", error);
                });
            },
            // 查看廉政问卷
            viewIntegritySurvey(row) {
                var _this = this;
                _this.viewIntegrityLoading = true;
                _this.viewIntegrityDialogVisible = true;

                // 调用后端接口获取廉政问卷文件信息
                axios.post('/general/toppingsoft/index.php/city/task/viewIntegritySurvey', {
                    task_id: row.id
                }).then(function (res) {
                    _this.viewIntegrityLoading = false;
                    if (res.data.code == 0) {
                        _this.integrityFileInfo = res.data.data;

                        // 如果是PDF文件，直接预览
                        if (_this.integrityFileInfo.is_pdf) {
                            _this.viewIntegrityDialogVisible = false;
                            _this.previewPdf(_this.integrityFileInfo.file_url, _this.integrityFileInfo.file_name);
                        }
                        // 如果不是PDF文件，显示下载选项
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        _this.viewIntegrityDialogVisible = false;
                    }
                }).catch(function (error) {
                    _this.viewIntegrityLoading = false;
                    _this.$message({
                        message: "加载失败，请重试",
                        type: "error"
                    });
                    _this.viewIntegrityDialogVisible = false;
                    console.log("出现错误:", error);
                });
            },
            // PDF预览功能
            previewPdf(url, title) {
                this.pdfPreviewUrl = url;
                this.pdfPreviewTitle = title || '廉政问卷预览';
                this.pdfPreviewVisible = true;
            },
            // 下载文件
            downloadFile() {
                if (this.integrityFileInfo.file_url) {
                    // 创建一个隐藏的下载链接
                    const link = document.createElement('a');
                    link.href = this.integrityFileInfo.file_url;
                    link.download = this.integrityFileInfo.file_name;
                    link.target = '_blank';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    this.viewIntegrityDialogVisible = false;
                    this.$message.success('开始下载文件');
                }
            },
            // 关闭PDF预览
            closePdfPreview() {
                this.pdfPreviewVisible = false;
                this.pdfPreviewUrl = '';
                this.pdfPreviewTitle = '';
            },
        },
        mounted() {
            //获取列表
            this.getData();

        }
    })
</script>


</body>
</html>