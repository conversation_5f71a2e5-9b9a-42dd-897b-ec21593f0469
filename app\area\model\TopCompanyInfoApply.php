<?php
declare (strict_types=1);

namespace app\area\model;

use think\Model;
use think\facade\Db;
use app\admin\model\TopPcaModel;
use app\model\FileModel;
use app\admin\model\CompanyModel;


/**
 * @mixin \think\Model
 */
class TopCompanyInfoApply extends Model
{
	//


	public function getList($where, $page = 1, $limit = 10)
	{
//		$whereArr[] = ['status', '=', 1];
		if (isset($where['name']) && $where['name'] != '') $whereArr[] = ['name', 'like', '%' . $where['name'] . '%'];
		$data = $this->where($whereArr)->paginate($limit)->each(function ($item, $index) {
			$item = $this->getInfo($item);
			return $item;
		});
		return $data;
	}

	public function getInfo($item, &$model = null)
	{
		$pcaModel = new TopPcaModel;
		$item->regionName = $pcaModel->getNames($item->region, ' ', false, true);
		$item->reg_address_name = $pcaModel->getNames($item->reg_address, ' ', false, true);
		$item->operate_address_name = $pcaModel->getNames($item->operate_address, ' ', false, true);
		$item->apply_time = date('Y-m-d H:i:s', strtotime($item->apply_time));
		$item->licenseUrl = FileModel::getFile($item->id, $item->license);
		$item->aocUrl = FileModel::getFile($item->id, $item->aoc);
		$item->statusStr = $this->getStatus($item->status);
		return $item;
	}

	public function getStatus($status){
		$str = '';
		switch ($status){
			case 0:
				$str = '未提交';
				break;
			case 1:
				$str = '审核中';
				break;
			case 5:
				$str = '驳回';
				break;
			case 7:
				$str = '通过';
				break;
			default:
				break;
		}
		return $str;
	}

	public function doCheck($model, $status, $reason)
	{
		$model->status = $status;
		$model->reason = $reason;
		$model->check_user_id = $_SESSION['area']['id'];
		$model->check_user_name = $_SESSION['area']['name'];
		$model->check_time = date('Y-m-d H:i:s');
		Db::startTrans();
		try  {
			$model->save();
			if ($status == 7) {
				$data = $model->toArray();
				$data['industry'][0] = $data['industry'];
				$data['specialty'][1] = $data['specialty'];
				$companyModel = new CompanyModel;
				$id = $companyModel::companySave($data, 0, 1);
			}
			Db::commit();
			return '数据处理成功';
		}catch (Exception $e){
			Db::rollback();
		}
		return '数据处理失败';
	}

}
