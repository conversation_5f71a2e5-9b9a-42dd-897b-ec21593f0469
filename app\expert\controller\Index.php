<?php

namespace app\expert\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)

use app\api\model\SecsModel;
use app\company\model\CompanyModel;
use app\expert\model\ExpertModel;
use app\validate\ExpertVerify as Verify;
use hg\apidoc\annotation as Apidoc;
use think\facade\View;
use think\Request;
use think\facade\Db;
use app\model\FileModel;

/**
 * @Apidoc\Title("评审专家首页")
 * @Apidoc\Group("Index")
 * @Apidoc\Sort(1)
 */
class Index extends Base {

    /**
     * @Apidoc\Title("评审专家首页")
     * @Apidoc\Desc("评审专家首页")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function index() {
        View::assign('title','首页');
        //$expert = Db::table('top_expert_info')->where(['expert_id'=>$_SESSION['expert']['id']])->field('id,status')->find();
        //$url = $expert['status']!=7?'index/info':'index/main';
        $url =  'index/main';
        //$url = 'index/gotoSecsUrl?url=user/profile';
        View::assign('url',$url);
        View::assign('user',$_SESSION['expert']);
        //View::assign('status',empty($expert['status'])?0:$expert['status']);
        View::assign('status',7);
        return view();
    }

    public function loginout() {
        $_SESSION['expert'] = [];
        return redirect('/general/toppingsoft/index.php/expert/login/login');
    }

    /**
     * @Apidoc\Title("区县应急局首页")
     * @Apidoc\Desc("区县应急局首页")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function main() {
        //通过接口获取专家系统的专家信息
        $res = SecsModel::getSecsInterfaceData('secsExpert/get-with-extension',['expertId'=>$_SESSION['expert']['expert_id']],'get');
        $expertInfo = $res['data'];
        $expertInfo['createTime'] = date('Y-m-d',$expertInfo['createTime']/1000);
        $total['company']['sum'] = number_format(Db::table('top_org_tasks_experts')->where("expert_id={$_SESSION['expert']['id']} and (status in (7,1) or status > 8)")->count());
        $total['company']['sum1'] = number_format(Db::table('top_org_tasks_experts')->where(['expert_id'=>$_SESSION['expert']['id']])->where('status','>',8)->count());
        $total['company']['sum2'] = number_format(Db::table('top_org_tasks_experts')->where(['expert_id'=>$_SESSION['expert']['id']])->where(['status'=>7])->count());
        $total['company']['sum3'] = number_format(Db::table('top_org_tasks_experts')->where(['expert_id'=>$_SESSION['expert']['id']])->where(['status'=>1])->count());
        $result['notify'] = Db::table('top_notify')->alias('a')
            ->leftJoin('top_notify_type t','a.type = t.id')
            ->where(['a.is_del'=>0,'a.is_show'=>1])
            ->order('date desc,create_time desc')
            ->field('a.*,t.name mb_type')
            ->page(1,10)->select()->toArray();
        $result['ca'] = Db::table('top_org_tasks')
            ->where([['org_id','=',$_SESSION['org']['id']],['status','=',1]])
            ->order('id desc')
            ->page(1,10)->select()->toArray();
        View::assign('total',$total);
        View::assign('result',$result);
        View::assign('title','首页');
        View::assign('userInfo',$expertInfo);
        return view();

    }

    public function editPassword() {
        $old_password = request()->param('old_password');
        $new_password = request()->param('new_password');
        $confirm_password = request()->param('confirm_password');
        $user = Db::table('top_expert')->where(['id'=>$_SESSION['expert']['user_id']])->find();
        if($user['password']!==crypt($old_password,$user['salt'])){
            result('',1003,'原密码错误');
        }
        $pattern = '/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$|^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$|^(?=.*[a-z])(?=.*[A-Z])(?=.*[\W_]).{8,}$|^(?=.*[a-z])(?=.*\d)(?=.*[\W_]).{8,}$|^(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$/';
        // 使用preg_match函数进行匹配
        if (!preg_match($pattern, $new_password)) {
            result('',1001,'请填写6-18位密码，并且须由大写字母、小写字母、数字及特殊符号中的三种或三种以上进行组合');
        }
        if($new_password!==$confirm_password){
            result('',1005,'两次密码不一致');
        }
        $data['salt'] = create_nonce_str(8);
        $data['password'] = crypt($new_password,$data['salt']);
        $re = Db::table('top_expert')->where(['id'=>$_SESSION['expert']['user_id']])->update($data);
        if($re){
            result('',0,'密码修改成功，请重新登陆');
        }else{
            result('',1007,'修改失败');
        }
    }

    public function expertSave($id=0) {
        $request = $this->request->post();
        Verify::userCheck('save',$request);
        $id = ExpertModel::expertSave($request,$id);
        if($id>0){
            result(['id'=>$id],0,'提交成功');
        }else{
            result('',7001,$id);
        }
    }

    public function upload($model='expert') {
        $file = request()->file('file');
        $result = FileModel::upload($file,$model);
        result($result);
    }

    /**
     * @Apidoc\Title("专家信息管理")
     * @Apidoc\Desc("专家信息管理")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function info() {
        $fields = "a.id,a.name,a.email,a.mobile,b.status,a.org_id,b.nation,b.sex,b.birthday,b.qq,b.address,b.head,b.school,b.speciality,b.education,b.employer,b.position,b.work_date,b.position_date,b.professional,b.professional_number,b.secure,b.secure_number,b.reg_secure_number,b.other_number,b.major,b.resume,b.employ_date,b.offer_info,o.name org_name";
        $expert = Db::table('top_expert')->alias('a')
            ->leftJoin('top_expert_info b','a.id = b.expert_id')
            ->leftJoin('top_org o','o.id = a.org_id')
            ->where(['a.id'=>$_SESSION['expert']['id']])->field($fields)->find();
        if(empty($expert['status'])){
            return redirect('auth');
        }
        if($expert['status']==7){
            $class = 'success';
            $title = '已认证';
        }else if($expert['status']==5){
            $class = 'danger';
            $title = '已驳回';
        }else if($expert['status']==1){
            $class = 'primary';
            $title = '认证审核中';
        }
        View::assign('class',$class);
        View::assign('title',$title);
        View::assign('expert',ExpertModel::codeToText($expert));
        return view();
    }

    /**
     * @Apidoc\Title("专家资料认证")
     * @Apidoc\Desc("专家资料认证")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function auth() {
        return view();
    }

    public function getConfig() {
        $result['orgs'] = Db::table('top_org')->where(['is_del'=>0])->field('id,name')->select()->toArray();
        foreach ($result['orgs'] as $k=>$v){
            $result['orgs'][$k]['id'] = (string)$v['id'];
        }
        result($result);
    }

    public function getExpertInfo() {
        $fields = "a.id,a.name,a.email,a.mobile,a.status,a.org_id,b.nation,b.sex,b.birthday,b.qq,b.address,b.head,b.school,b.speciality,b.education,b.employer,b.position,b.work_date,b.position_date,b.professional,b.professional_number,b.secure,b.secure_number,b.reg_secure_number,b.other_number,b.major,b.resume,b.employ_date,b.offer_info";
        $re = Db::table('top_expert')->alias('a')
            ->leftJoin('top_expert_info b','a.id = b.expert_id')
            ->where(['a.id'=>$_SESSION['expert']['id']])->field($fields)->find();
        if(!empty($re)){
            $re = ExpertModel::codeToText($re);
            $re['industry'] = [$re['industry'],$re['specialty']];
        }else{
            $re = [
                'name' => '',
                'head' =>'',
                'headUrl' => '',
                'mobile' => '',
                'sex' => '',
                'org_id' => '',
                'email' => '',
                'birthday' => '',
                'nation' => '',
                'qq' => '',
                'address' => '',
                'school' => '',
                'speciality' => '',
                'education' => '',
                'employer' => '',
                'position' => '',
                'work_date' => '',
                'position_date' => '',
                'professional' => '',
                'professional_number' => '',
                'secure' => '',
                'secure_number' => '',
                'reg_secure_number' => '',
                'other_number' => '',
                'major' => '',
                'employ_date' => '',
                'offer_info' => '',
                'resume' => '',
                'status' => '1',
            ];
        }
        result($re);
    }

    public function policyInfo($id = 0){
        $where[] = ['is_del','=',0];
        $where[] = ['id','=',$id];
        $res = Db::table('top_notify')
            ->where($where)
            ->find();
        if(empty($res)){
            result('',1002,'数据有误');
        }
        result($res);
    }

    /**
     * @Apidoc\Title("跳转至专家系统")
     * @Apidoc\Desc("跳转至专家系统")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("首页")
     */
    function gotoSecsUrl($url){
        //如果登录已经超时，则退出
        if(empty($_SESSION['expert']['id'])){
            header("Location: /general/toppingsoft/index.php/expert/login/login");
        }
        $SecsConfig = SecsModel::getSecsConfig();
        $urlExt = SecsModel::getSsoUrlToken();
        $url = $SecsConfig['domain'].$url.$urlExt;
        header("Location: $url");
    }


}
