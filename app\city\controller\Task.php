<?php
declare (strict_types = 1);

namespace app\city\controller;

use app\city\model\CompanyModel;
use app\model\FileModel;
use Endroid\QrCode\QrCode;
use think\facade\Db;
use think\facade\View;
use Html2image\Assets\Html2img;

class Task extends Base
{


    public function htmlToImg($url,$filepath){
        $html=file_get_contents($url);
        $path= public_path().'storage/tmp/';
        $file_name=time();
        $data['filepath']=$filepath;
        $data['file_name']=$file_name;
        $back_url='/general/toppingsoft/index.php/file/base64_image_content';
        $html2img=new Html2img($back_url);
        $html2img->getImage($html,$data);
        exit('sssssss');
        result();
    }

    /**
     * @Apidoc\Title("评审任务")
     * @Apidoc\Desc("评审任务")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("日程")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function index($limit=20) {
        if (request()->isAjax()) {
            $title = $this->request->param('title','','trim');
            $where = [
                ['a.city_id','=',$_SESSION['city']['id']],
            ];
            if($_SESSION['city']['dept_id']!=1){
                $where[] =['a.dept_id','=',$_SESSION['city']['dept_id']];
            }
            if(!empty($title)){
                $where[] = ['a.company_name','like',"%{$title}%"];
            }
            $field = "b.id,a.company_id,a.company_name,a.level,b.date,b.status,b.integrity_code";
            $res = Db::table('top_org_tasks')->alias('b')
                ->leftJoin('top_grading a','a.id = b.grading_id')
                ->where($where)->order('b.date desc')
                ->field($field)
                ->paginate($limit)->each(function ($item, $key) {
                    $e = Db::table('top_org_tasks_experts')->where(['tasks_id'=>$item['id']])->column('expert_name');
                    $item['element_name'] = empty($e)?'未分配':implode('，',$e);
                    // 查询满意度调查状态
                    $mydStatus = Db::table('top_org_tasks_myd')->where(['tasks_id'=>$item['id']])->find();
                    $item['satisfaction_survey_status'] = $mydStatus ? 1 : 0;
                    // 查询廉政问卷状态 - 检查任务表中的integrity_code字段
                    $taskIntegrity = isset($item['integrity_code'])?$item['integrity_code']:'';
                    $item['integrity_survey_status'] = !empty($taskIntegrity) ? 1 : 0;
                    return $item;
                });
            result($res);
        } else {
            return view();
        }
    }


//    public function info($id=0){
//        if (request()->isAjax()) {
//            $task = Db::table('top_org_tasks')->where(['id'=>$id])->find();
//            $grading = Db::table('top_grading')->where(['id'=>$task['grading_id']])->find();
//            $details = Db::table('top_org_tasks_works')->where(['tasks_id'=>$task['id']])->order('sort')->select()->toArray();
//            $detail = [];
//            foreach ($details as $v){
//                $v['files'] = json_decode($v['files'],true);
//                $detail[$v['stage']][] = $v;
//            }
//            $review_flow = config('global.review_flow');
//            foreach ($review_flow as $k=>$v){
//                $review_work[] = [
//                    'stage'=>$v['stage'],
//                    'work' => $detail[$k],
//                ];
//            }
//            $data['review_work'] = $review_work;
//            $res = Db::table('top_org_tasks_experts')->where(['tasks_id'=>$id])->select()->toArray();
//            $review_list = Db::table('top_org_tasks_element')->where('tasks_id','=',$id)->select()->toArray();
//            $data['review_list'] = $review_list;
//            result($data);
//        } else {
//            $task = Db::table('top_org_tasks')->where(['id'=>$id])->find();
//            $re = Db::table('top_org_tasks_experts')->where(['tasks_id'=>$id,'expert_id'=>$_SESSION['expert']['id']])->find();
//            $grading = Db::table('top_grading')->where(['id'=>$task['grading_id']])->find();
//            $details = Db::table('top_org_tasks_works')->where(['tasks_id'=>$task['id']])->order('sort')->select()->toArray();
//            $detail = [];
//            foreach ($details as $v){
//                $v['files'] = json_decode($v['files'],true);
//                $detail[$v['stage']][] = $v;
//            }
//            $review_flow = config('flow.review_flow');
//            foreach ($review_flow as $k=>$v){
//                $review_work[] = [
//                    'stage'=>$v['stage'],
//                    'work' => $detail[$k],
//                ];
//            }
//            View::assign('review_flow', $review_work);
//            View::assign('task', $task);
//            View::assign('re', $re);
//            View::assign('grading', $grading);
//            return view();
//        }
//    }

    public function info($id=0){
        if (request()->isAjax()) {
            $task = Db::table('top_org_tasks')->where(['id'=>$id])->find();
            $details = Db::table('top_org_tasks_works')->where(['tasks_id'=>$task['id']])->order('sort')->select()->toArray();
            $detail = [];
            foreach ($details as $v){
                $v['files'] = json_decode($v['files'],true);
                $detail[$v['stage']][] = $v;
            }
            $review_flow = config('global.review_flow');
            foreach ($review_flow as $k=>$v){
                $review_work[] = [
                    'stage'=>$v['stage'],
                    'work' => $detail[$k],
                ];
            }
            $data['review_work'] = $review_work;
            $res = Db::table('top_org_tasks_experts')->where(['tasks_id'=>$id])->select()->toArray();
            $ids = [];
            foreach ($res as $v){
                $eids = explode(',',$v['element_id']);
                foreach ($eids as $v1){
                    if($v1>0){
                        $idarr[$v1] = [
                            'user_name' => $v['expert_name'],
                            'status' => $v['status'],
                        ];
                        $ids[] = $v1;
                    }
                }
            }
            $review_list = Db::table('top_org_tasks_element')->where('tasks_id','=',$id)->select()->toArray();
            $data['review_list'] = $review_list;
            result($data);
        } else {
            $task = Db::table('top_org_tasks')->where(['id'=>$id])->find();
            $re = Db::table('top_org_tasks_experts')->where(['tasks_id'=>$id,'expert_id'=>$_SESSION['expert']['id']])->find();
            $grading = Db::table('top_grading')->where(['id'=>$task['grading_id']])->find();
            $details = Db::table('top_org_tasks_works')->where(['tasks_id'=>$task['id']])->order('sort')->select()->toArray();
            $detail = [];
            foreach ($details as $v){
                $v['files'] = json_decode($v['files'],true);
                $detail[$v['stage']][] = $v;
            }
            $review_flow = config('flow.review_flow');
            foreach ($review_flow as $k=>$v){
                $review_work[] = [
                    'stage'=>$v['stage'],
                    'work' => $detail[$k],
                ];
            }
            $url = $_SERVER['REQUEST_SCHEME'].'://'.$_SERVER['HTTP_HOST'].'/general/toppingsoft/index.php/exam/mobile/myd?id='.$id;
            // 实例化QrCode对象
            $qrCode = new QrCode($url);
            // 设置二维码的尺寸
            $qrCode->setSize(500);
            // 设置二维码的边距
            $qrCode->setMargin(10);
            // 设置二维码的颜色和背景颜色
            $qrCode->setForegroundColor(['r' => 0, 'g' => 0, 'b' => 0, 'a' => 0]);
            View::assign('mydcodeimg', $qrCode->writeDataUri());
            View::assign('review_flow', $review_work);
            View::assign('task', $task);
            View::assign('re', $re);
            View::assign('grading', $grading);
            return view();
        }
    }

    //评审任务查询详情
    public function baogaoInfo($id=0,$isJson=true)
    {
        $task = Db::table('top_org_tasks')->where(['id'=>$id])->find();
        $re = Db::table('top_grading')->where(['id'=>$task['grading_id']])->find();
        //获取企业信息表
        $companyInfo = Db::table('top_company_info')
            ->where('id', $task['company_id'])
            ->find();
        $companyInfo = \app\model\CompanyModel::codeToText($companyInfo);

        $experts = Db::table('top_org_tasks_experts')->field("expert_name,mobile")->where(['tasks_id'=>$task['id']])->order('position_id')->select()->toArray();
        foreach ($experts as $k=>$v)
        {
            $experts[$k]['unit_name'] = '市城安院';
            $experts[$k]['mobile'] = '028-87706080';
        }
        $re['task'] = $task;
        $re['experts'] = $experts;
        $re['citys'] = [
            ['name'=>'','unit'=>'','mobile'=>''],
            ['name'=>'','unit'=>'','mobile'=>''],
            ['name'=>'','unit'=>'','mobile'=>''],
        ];
        $re['status'] = '是';
        $re['year'] = date('Y');
        $re['month'] = date('m');
        $re['day'] = date('d');
        $review_list = Db::table('top_org_tasks_element')->where('tasks_id','=',$task['id'])->select()->toArray();
        $re['total'] = $re['miss'] = $re['score'] = $re['bishi'] = $mark = $bishiscore = 0;
        $bishi = Db::table('top_cay_exam_paper')->field("total_score")->where(['task_id'=>$id])->field('id,total_score')->select()->toArray();
        foreach ($bishi as $v){
            $bishiscore += $v['total_score'];
        }
        $re['bishi'] = (int)($bishiscore/count($bishi));

        $reform = [''];
        $taskReformList = [];
        $reformList = [];


        //查询专家整改信息
        $taskReformListTemp = Db::table('top_org_tasks_score')->field("reform")->where('tasks_id','=',$task['id'])->select()->toArray();

        if( !empty($taskReformListTemp) && count($taskReformListTemp) )
        {
            foreach ($taskReformListTemp as $v)
            {
                if(!empty($v['reform']))
                {
                    $reformTemp = explode(',',$v['reform']);
                    $taskReformList = array_merge($taskReformList,$reformTemp);
                }
            }
        }

        //查询企业整改信息
        $companyReformList = Db::table('top_company_reform')->field("id,reform")->where('task_id','=',$task['id'])->select()->toArray();

        //判断企业无整改信息，则直接查询专家填报的整改信息，第一次则无企业整改信息
        if( is_array($companyReformList) && count($companyReformList) && count($companyReformList) >= count($taskReformList) )
        {
            $reformList = $companyReformList;

            foreach ($companyReformList as $item)
            {
                $reform[] = $item['reform'];
            }
        }
        else
        {
            if( !empty($taskReformList) && count($taskReformList) )
            {
                foreach ($taskReformList as $v)
                {
                    if(!empty($v))
                    {
                        $reformTemp = [
                            'id'=>'',
                            'reform'=>$v
                        ];

                        $reformList[] = $reformTemp;
                    }
                }
            }
        }

        if(count($reformList) == 0)
        {
            $reformList = ['id'=>'','reform'=>''];
        }

        //不合格的事项
//        $re['bufu'] = [];
        foreach ($review_list as $k=>$v)
        {
            $re['total'] += $v['total'];
            $re['miss'] += $v['miss'];
            $re['score'] += $v['score'];
//            //如果不合格
//            if($v['rate']<$v['mark']){
//                $re['bufu'][] = $v['element_name'];
//            }
            $v['deduct'] = $v['total']-$v['score']-$v['miss'];
            $review_list[$k]['deduct'] = $v['deduct'];
            if($v['score']/($v['total']-$v['miss'])*100<$v['mark']){
                $re['status'] = '否';
            }

            //实得分（汇总）：单项得分总和—教育培训得分*（100-笔试平均分）% @todo
            if(mb_strpos($v['element_name'],'教育培训')!==false){
                $re['score'] -= round($v['total']*(100-$re['bishi'])/100,1);
            }
//            $mark += $v['score']/($v['total']-$v['miss'])*100*$v['weight'];
        }

        //如果不合格
        $jielunExt0 = "";
        $jielunExt1 = "且每个一级要素得分率均不低于60%";
        if(count($re['bufu'])>0){
            $jielunExt0 = "不";
            $jielunExt1 =implode(",",$re['bufu'])."要素得分率低于60%";
        }

        //企业性质 ☑️☐
        // 定义选项数组
        $natureOptions = ['国有', '集体', '民营', '私营', '合资', '独资', '其它'];

        // 假设这是动态获取的值
        $re['nature_source'] = '国有';

        // 使用 generateOptions 函数生成企业性质选项字符串
        $re['nature'] = $this->generateOptions($natureOptions, [$re['nature_source']]);

        //企业涉及主要风险
        // 定义选项数组
        $riskOptions = [
            '动火作业' => '动火作业',
            '外委外包' => '外委外包',
            '检维修' => '检维修',
            '涉危险化学品' => '涉危险化学品',
            '涉爆粉尘' => '涉爆粉尘',
            '有限空间作业' => '有限空间作业',
            '用电安全' => '用电安全',
            '吊装作业' => '吊装作业',
            '高处作业' => '高处作业',
            '消防安全' => '消防安全',
            '金属冶炼' => '金属冶炼',
        ];

        // 假设这是动态获取的值，表示哪些风险项被选中
        $selectedRisks = ['动火作业', '涉危险化学品', '用电安全'];

        // 使用 generateOptions 函数生成企业涉及主要风险选项字符串
        $re['risk'] = $this->generateOptions($riskOptions, $selectedRisks);
        $re['status_cn'] = $re['status']=='1'?'☑是 □否':'□是 ☑否';
        //现场评审得分（百分制）：实得分（汇总）/（标准分-缺项分）
        $res = ($re['total']-$re['miss']) !== 0 ? $re['score'] / ($re['total']-$re['miss']) : 0;
        $re['scores'] = round($res*100,1);
        $re['review_list'] = $review_list;
        //现场评审情况
        $curDate = date('Y年m月日');
        $re['qingkuang']=<<<EOT
    {$curDate}，成都市城市安全与应急管理研究院受成都市应急管理局委托，成立由专家、技术人员组成的现场评审组，按照《成都市企业三级安全生产标准化基本规范》对{$companyInfo['name']}进行安全生产标准化三级达标评审工作。评审主要采用资料查阅、现场查看、笔试及抽查考核等方法，对该企业安全生产标准化建设工作进行评审。首次会议上听取了企业安全生产标准化建设情况汇报，现场评审阶段评审人员查看了企业生产作业现场并按照相关评分标准逐项评审，末次会议中现场评审组向企业通报了安全生产标准化评审情况，指出了存在的主要问题和整改要求，企业负责人做出了整改承诺。
    {$companyInfo['name']}主要从事{$re['industry_name']}，企业现有人员{$companyInfo['personnel']}人，参加笔试的人员  人，询问人员  人次；企业现有生产设备  台，抽查  台。
EOT;
        //现场评审结论
        $re['jielun']=<<<EOT
    该企业已开展安全生产标准化工作并持续运行半年以上，且按《成都市企业三级安全生产标准化基本规范》进行了自评，自评得分为  分；企业在申请评审之日的前1年内无生产安全死亡事故，并向XXXX应急管理局申请了企业安全生产标准化三级达标评审。
    评审组按照《成都市企业三级安全生产标准化基本规范》的要求，对{$companyInfo['name']}进行了三级安全生产标准化评审，评审得分为{$re['scores']}分，{$jielunExt1}。
    评审结论：依据《成都市企业安全生产标准化建设定级管理办法》和《成都市企业三级安全生产标准化基本规范》的规定，{$companyInfo['name']}安全生产标准化评审得分{$jielunExt0}安全生产标准化三级达标要求，成都市城市安全与应急管理研究院向成都市应急管理局{$jielunExt0}推荐{$companyInfo['name']}为安全生产标准化三级达标企业。
    岗位达标建设情况：该企业设置岗位XX个，其中特种（设备）作业岗位XX个、生产工序岗位XX个、承包（承租）单位作业岗位XX个。达标岗位XX个。
    本次评审仅对当时现场和评审资料负责。
EOT;
        //企业安全生产基本情况概述
        $re['gaishu1'] =<<<EOT
    {$companyInfo['name']}公司成立于{$companyInfo['mb_date']}，注册地位于{$companyInfo['reg_address_info']}，生产地位于{$companyInfo['mb_operate_address']}，自有占地面积{$companyInfo['area']}m²（或租赁厂房面积{$companyInfo['area']}m²），法定代表人为{$companyInfo['legal']}，企业现有员工{$companyInfo['personnel']}人。企业主要经营（生产、销售）包括{$companyInfo['business']}、XXXX年XX月开始投入生产，XXXX年年产值为XXXX万元（或XXXX年年产能为XXXX吨），属于（规上、规下或微小）企业。
    公司主要生产工艺为XXXXXXXXXX。生产过程中涉及（动火（包括电气焊、明火、产生火花的切割等）、有限空间作业、临时用电、吊装、涉危险化学品、粉尘涉爆、检维修、外委外包等）危险作业。
EOT;
        //企业标准化管理体系建设及其运行情况
        $re['gaishu2']=<<<EOT
    {$companyInfo['name']}安全生产标准化建设由XXXXXXX咨询指导，从XXXX年XX月开始创建，XXXX年XX月开始安全生产标准化管理体系运行。在安全生产标准化建设中，企业按照《成都市企业三级安全生产标准化基本规范》的要求，成立了安全生产委员会（或安全生产领导小组），设置了安全生产管理机构，配备了X名专职、X名兼职安全管理人员，落实了各级各部门的安全职责，与X个二级部门签订了安全生产目标责任书，建立了X个安全生产责任制、X个安全生产管理制度、X个安全操作规程。
    XXX（企业部门名称）为企业安全教育培训主管部门。制定了各类人员的教育培训计划，对安全教育培训效果进行了评估和改进。建立了培训教育档案，培训记录比较齐全。XX名主要负责人和XX名专（兼）职安全管理人员已参加培训并取得培训合格证书，XX名人员取得了职业卫生管理人员培训合格证书，XX名作业人员取得特种作业操作证或特种设备作业人员证。
    构建了安全风险分级管控和隐患排查治理双重预防机制，企业开展了风险辨识评估，辨识风险xx条，形成了风险管控清单，在关键岗位进行了风险告知。制定了隐患排查工作方案，按照方案进行了隐患排查工作，确定了隐患等级并登记建档。在安全生产标准化建设期间，共查找出XX项事故隐患，且在成都市隐患排查系统进行了申报，目前已整改事故隐患XX项。
    建立了应急救援管理制度，编制了《生产安全事故应急救援预案》，成立了应急救援领导小组，组建了应急救援队伍，配备了应急救援设备设施和应急救援物资。《生产安全事故应急救援预案》于XXXX年XX月在相关部门备案。
EOT;
        $re['reform'] = $reform;
        $re['reformList'] = $reformList;
        $re['reformTotal'] = count($taskReformList);
        $re['jianyi'] = "建议{$companyInfo['name']}按照《成都市企业三级安全生产标准化基本规范》等法律法规、标准规范的要求，结合本次安全生产标准化的评审意见，制定整改方案，及时整改，并将现场存在问题报成都市隐患排查治理动态监管系统。";
//        $re['jiainyi'] = "建议{$re['company_name']}按照《成都市企业三级安全生产标准化基本规范》等法律法规、标准规范的要求，结合本次安全生产标准化的评审意见，制定整改方案，及时整改，并将现场存在问题报成都市隐患排查治理动态监管系统。";
        $re = empty($plan['baogao'])?$re:json_decode($plan['baogao'],true);
        if($isJson){
            result($re);
        }else{
            return $re;
        }
    }

    public function exam($limit=20,$id=0) {
        $where = [];
        $where[] = ['task_id','=',$id];
        $res = Db::table('top_cay_exam_paper')
            ->where($where)->order('start_date desc')
            ->paginate($limit)->each(function ($item, $key) {
                return $item;
            });
        result($res);
    }


    public function video($id=0){
        if (request()->isAjax()) {
            $where = [
                ['date','=',date('Y-m-d')],
                ['video_id','>',0],
            ];
            $res = [
                ['title'=>'测试企业001','number'=>'160401'],
                ['title'=>'测试企业002','number'=>'160402'],
                ['title'=>'测试企业003','number'=>'160403'],
                ['title'=>'测试企业004','number'=>'160404'],
            ];
            $online = Db::table('top_org_tasks')->where($where)->field('id,company_name title,video_id as "number" ')->select()->order("video_id")->toArray();
            if(count($online)<4){
                for($i=count($online);$i<4;$i++)
                    $online[]=[
                    'title'=>'测试企业00'.($i+1),
                    'number'=>'',
                ];
                $res = $online;
            }
            result($res);
        } else {
            View::assign('date', date('Y-m-d'));
            return view();
        }
    }

    /**
     * 查看满意度调查
     */
    public function viewSatisfactionSurvey()
    {
        try {
            $taskId = request()->param('task_id', 0);

            if (!$taskId) {
                result('', 1, '缺少任务ID');
            }

            // 查询满意度调查数据
            $surveyData = Db::table('top_org_tasks_myd')
                ->where('tasks_id', $taskId)
                ->find();

            if (!$surveyData) {
                result('', 1, '未找到满意度调查数据');
            }

            // 格式化数据
            $result = [
                'company_name' => $surveyData['company_name'],
                'field2' => $surveyData['field2'], // 填表时间
                'field3' => $surveyData['field3'], // 填表人及联系电话
                'field4' => $surveyData['field4'], // 现场评审人员是否存在违法廉洁纪律行为
                'field5' => $surveyData['field5'], // 您对本次评审成绩是否满意
                'field6' => $surveyData['field6'], // 您对本次评审人员的评审工作是否满意
                'field7' => $surveyData['field7'], // 您的意见与建议
                'time' => $surveyData['time'] // 提交时间
            ];

            result($result, 0, '查询成功');

        } catch (\Exception $e) {
            result('', 1, '查询过程中发生错误：' . $e->getMessage());
        }
    }

    /**
     * 查看廉政问卷
     */
    public function viewIntegritySurvey()
    {
        try {
            $taskId = request()->param('task_id', 0);

            if (!$taskId) {
                result('', 1, '缺少任务ID');
            }

            // 查询任务中的廉政问卷文件编码
            $task = Db::table('top_org_tasks')
                ->where('id', $taskId)
                ->find();

            if (!$task) {
                result('', 1, '任务不存在');
            }

            if (empty($task['integrity_code'])) {
                result('', 1, '未找到廉政问卷文件');
            }

            // 根据文件编码获取文件信息
            $fileRecord = Db::table('top_files')
                ->where('code', $task['integrity_code'])
                ->find();

            if (!$fileRecord) {
                result('', 1, '文件记录不存在');
            }

            // 判断文件类型
            $fileExt = strtolower($fileRecord['fileext']);
            $isPdf = $fileExt === 'pdf';

            // 构建文件访问URL
            $fileUrl = '/general/toppingsoft/index.php/file/info?code=' . $fileRecord['code'];

            $result = [
                'file_name' => $fileRecord['filename'],
                'file_ext' => $fileExt,
                'file_url' => $fileUrl,
                'is_pdf' => $isPdf,
                'file_size' => $fileRecord['filesize']
            ];

            result($result, 0, '查询成功');

        } catch (\Exception $e) {
            result('', 1, '查询过程中发生错误：' . $e->getMessage());
        }
    }

    public function companyInfo($id = 0)
    {
        \app\model\CompanyModel::getCompanyInfo($id);
        exit();
    }

    public function getReview($element_id = 0)
    {
        if (request()->isAjax()) {
            $request = $this->request->post();
            \app\model\CompanyModel::getAjaxReview($element_id, $request);
            exit();

        } else {
            View::assign('element_id', $element_id);
            View::assign('title', '首页');
            return view();
        }
    }

}
