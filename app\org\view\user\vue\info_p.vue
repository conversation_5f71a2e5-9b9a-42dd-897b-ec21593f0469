<style>
    .my-label {
        width: 200px;
    }

    .my-content {
        width: 450px;
    }

    .margin-bottom {
        margin-bottom: 15px;
    }

    .form-header {
        background-color: #E9F2F3;
        line-height: 25px;
        margin-bottom: 15px;
        padding: 5px 10px;
    }

    .el-dialog__body {
        padding: 15px 20px;
    }

    .el-tabs__content {
        overflow: auto;
    }
</style>
<template>
    <el-dialog :title="title" :close-on-click-modal="false" modal="false" :visible.sync="visible" width="90%"
               top="100px"
               @close="refresh()" append-to-body="true" label-position="top">
        <el-descriptions class="margin-top" title="基本信息" :column="2" border label-class-name="my-label"
                         content-class-name="my-content">
            <el-descriptions-item label="姓名">
                {{data.name}}
            </el-descriptions-item>
            <el-descriptions-item label="用户名">
                {{data.username}}
            </el-descriptions-item>
            <el-descriptions-item label="电话">
                {{data.mobile}}
            </el-descriptions-item>
            <el-descriptions-item label="邮箱">
                {{data.email}}
            </el-descriptions-item>
            <el-descriptions-item label="注册时间">
                {{data.reg_time}}
            </el-descriptions-item>
            <el-descriptions-item label="注册IP">
                {{data.reg_ip}}
            </el-descriptions-item>
            <el-descriptions-item label="所属应急局">
                {{data.areaName}}
            </el-descriptions-item>
            <el-descriptions-item label="部门">
                {{data.deptName}}
            </el-descriptions-item>
            <el-descriptions-item label="角色">
                {{data.roleName}}
            </el-descriptions-item>
            <el-descriptions-item label="密码盐值">
                {{data.salt}}
            </el-descriptions-item>
            <el-descriptions-item label="状态">
                <el-tag v-if="data.status==0" type="danger">禁用</el-tag>
                <el-tag v-if="data.status==1" type="success">正常</el-tag>
            </el-descriptions-item>

        </el-descriptions>
        <el-form v-if="this.check" class="tableSearch" label-position="right" style="margin-top: 15px" label-width="170px">
            <el-row :gutter="20">
                <el-col :span="24" style="text-align: center;">
                    <el-button type="primary" @click="submit(1)">通过</el-button>
                    <el-button type="primary" class="error" @click="submit(4)">不通过</el-button>
                    <el-button @click="visible = false">关闭</el-button>
                </el-col>
            </el-row>
        </el-form>
        <!--编辑联系人-->
    </el-dialog>

</template>
<script>
    module.exports = {
        name: "add",
        // 模板导入区
        components: {},
        data: function () {
            return {
                id: 0,
                isAdmin: false,
                visible: false,
                dialogFormVisible: false,
                title: '详情',
                loading: false,
                noMore: false,
                user_id: 0,
                data: {},
                is_see: 0,
                ca: [],
                details: [],
                cards: {},
                type: {},
                pcas: [],
                check: false,
                restaurants: [],
                restaurants2: [],
                height: document.documentElement.clientHeight - 250,
                url:{
                    'sub': 'check_user'
                },
            }
        },
        computed: {
            disabled() {
                return this.loading || this.noMore
            }
        },
        mounted: function () {
            //this.getConfig();
        },
        created: function () {
        },


        methods: {
            /**
             * 打开弹窗调用方法
             * */
            open: function (row, check = false) {
                var _this = this;
                _this.id = row.id;
                _this.visible = true;
                _this.noMore = false;
                _this.data = row;
                _this.check = check;
            },
            closeDialog: function () {
                this.visible = false;
            },

            submit(status){
                var _this = this;
                this.$confirm("确认提交审核状态吗？", "提示", {}).then(() => {
                    _this.addLoading = true;
                    var param = {id: _this.id, status: status};
                    var url = _this.url.sub;
                    axios.post(url, param).then(function (res) {
                        _this.addLoading = false;
                        if (res.data.code == -200) {
                            _this.$message({
                                message: res.data.msg,
                                type: "error"
                            });
                        }else{
                            _this.$message({
                                message: res.data.msg,
                                type: "success"
                            });
                            _this.visible = false;
                        }

                    }).catch(function (error) {
                        console.log(error);
                    });

                });
            }
        }
    }
</script>


