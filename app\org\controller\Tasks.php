<?php
declare (strict_types = 1);

namespace app\org\controller;

use app\model\FileModel;
use app\model\VideoModel;
use Endroid\QrCode\QrCode;
use hg\apidoc\annotation as Apidoc;
use mysql_xdevapi\Result;
use think\facade\Db;
use think\facade\View;
use think\Request;
use app\admin\model\CompanyModel;
use app\model\TopOrgTasks;
use app\model\TopGradingApproval;
use app\model\TopGrading;
use app\api\controller\Expert;
use app\api\model\SecsModel;

/**
 * @Apidoc\Title("评审任务")
 * @Apidoc\Group("Tasks")
 * @Apidoc\Sort(1)
 */
class Tasks extends Base
{
    /**
     * @Apidoc\Title("评审任务")
     * @Apidoc\Desc("评审任务")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("日程")
     * @Apidoc\Returned("data", type="object", desc="基础数据")
     */
    public function index($limit=20) {
        if (request()->isAjax()) {
            $status = $this->request->param('status');
            $title = $this->request->param('title');
            $where = [];
            $where[] = ['b.org_id','=',$_SESSION['org']['id']];
            if(!empty($title)){
                $where[] = ['a.company_name','like','%'.$title.'%'];
            }
            if($status==1){
                $where[] = ['b.status','in',[1,2,3,4,5,6]];
            }else if($status==7){
                $where[] = ['b.status','in',[7]];
            }else if($status==8){
                $where[] = ['b.status','in',[8]];
            }else if($status==9){
                $where[] = ['b.status','in',[9,10]];
            }
            $field = "b.id,a.company_id,a.company_name,a.level,b.date,b.status,b.code";
            $res = Db::table('top_grading')->alias('a')
                ->leftJoin('top_org_tasks b','a.id = b.grading_id')
                ->where($where)->order('b.date desc')
                ->field($field)
                ->paginate($limit)->each(function ($item, $key) {
                    $e = Db::table('top_org_tasks_element')->where(['tasks_id'=>$item['id'],'expert_id'=>$_SESSION['expert']['id']])->column('element_name');
                    $item['element_name'] = empty($e)?'未分配':implode('，',$e);
                    $experts = Db::table('top_org_tasks_experts')->where(['tasks_id'=>$item['id']])->select()->toArray();
                    $nameArr = [];
                    foreach ($experts as $expert){
                        $nameArr[]= $expert['expert_name'];
                    }
                    $item['experts'] = $experts;
                    $item['expert_names'] = implode(",",$nameArr);
                    return $item;
                });
            result($res);
        } else {
            return view();
        }
    }


    public function update($id=0,$status=0) {
        $re = Db::table('top_org_tasks')->where(['id'=>$id])->find();
        if(empty($re)){
            result('',1001,'信息有误');
        }
        if($status==1){
            // 开启事务
            Db::startTrans();
            try {
                $data = [
                    'status' => $status,
                    'receive_user_id' => $_SESSION['org']['id'],
                    'receive_user_name' => $_SESSION['org']['user_name'],
                    'receive_time' => date('Y-m-d H:i:s'),
                ];
                Db::table('top_org_tasks')->where(['id'=>$id])->update($data);
                $prcs = Db::table('top_grading_approval')->where([['grading_id','=',$re['grading_id']],['prcs_id','=',4],['status','in',[1,2]]])->find();
                $data = [
                    'receive_user_id' => $_SESSION['org']['id'],
                    'receive_user_name' => $_SESSION['org']['user_name'],
                    'receive_time' => date('Y-m-d H:i:s'),
                    'check_content' => '',
                    'check_files' => '',
                    'status' => 2,
                    'status_name' => '已接收',
                ];
                Db::table('top_grading_approval')->where(['id'=>$prcs['id']])->update($data);
                // 提交事务
                Db::commit();
                result('',0,'操作成功');
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                result('',1002,'操作失败：'.$e->getMessage());
            }
        }
    }

    /**
     * NotHeaders
     * @Apidoc\Title("专家需求接收接口")
     * @Apidoc\Desc("专家需求接收接口")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("日程")
     * @Apidoc\Param("taskcode", type="string", require=true,desc="评审任务单号"),
     * @Apidoc\Param("company_name", type="string", require=true,desc="公司名称"),
     * @Apidoc\Param("company_industry", type="string", require=true,desc="公司行业类型"),
     * @Apidoc\Param("company_address", type="string", require=true,desc="地址"),
     * @Apidoc\Param("date", type="string", require=true,desc="评审日期"),
     * @Apidoc\Param("number", type="string", require=true,desc="所需专家数量"),
     * @Apidoc\Param("number_city", type="string", require=true,desc="所需市级以上专家数量"),
     * @Apidoc\Param("time", type="string", require=true,desc="请求时间：2025-01-02 09:12:23"),
     * @Apidoc\Param("sign", type="string", require=true,desc="签名，签名规则：MD5(taskcode+time+秘钥key)"),
     */
    public function expertSubmit($id=0) {
        $re = Db::table('top_org_tasks')->where(['id'=>$id])->find();
		$companyInfo = Db::table('top_company_info')->where(['id'=>$re['company_id']])->find();
		$companyInfo = CompanyModel::codeToText($companyInfo);
        if(empty($re)){
            result('',1001,'信息有误');
        }
        $params = $this->request->param('','','trim');
        $data = [
            'tasks_id' => $id,
            'date' => $params['date'],
            'number' => $params['number'],
            'number_city' => $params['number_city'],
            'time' => date('Y-m-d H:i:s'),
        ];
        // 开启事务
        Db::startTrans();
        try {
            $newTaskCode = "T".date('YmdHis').rand(100,999);
            Db::table('top_org_tasks')->where(['id'=>$id])->update(['date'=>$params['date'],'code'=>$newTaskCode]);
            Db::table('top_org_tasks_plan')->insert($data);
            /**
             * 推送到专家系统
             */
            $expertSystemData = [
                'taskcode'=>$newTaskCode,
                'company_name'=>$re['company_name'],
                "company_industry"=>$companyInfo['industry'],
                "categoryId"=>(int)$params["expert_type"],
                "directOrgName"=>$params["directOrgName"],		
                "region"=>$companyInfo['mb_region'],
                'date' => $params['date'],
                'number' => (int)$params['number'],
                'number_city' => (int)$params['number1'],
                'company_address' => $re['address'],
            ];
            
            $res = SecsModel::getSecsInterfaceData('secsTask/receive',$expertSystemData);	
            if($res['code']!=0){
                result('',1002,'操作失败：'.$res['msg']);
            }else{
				//更新状态到已推送
                Db::table('top_org_tasks')->where(['id'=>$id])->update(['status'=>2]);
            }
            Db::commit();
            result('',0,'操作成功');
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            result('',1002,'操作失败：'.$e->getMessage());
        }
	}
	
	//获取推送类型
	public function getExpertTypes()
	{
		$data = SecsModel::getSecsInterfaceData('secsExpert/getAllCategory');
		return $data;
	}

    //取消任务
    public function cancelTask($id=0) {
        //更新数据库的状态为已取消
        $taskInfo = Db::table('top_org_tasks')->where(['id'=>$id])->find();
        //通知专家系统取消任务
    	$res = SecsModel::getSecsInterfaceData('secsTask/cancelTaskNotice',['taskcode'=>$taskInfo['code']],'post');
        if($res['code']!=0){
            result('',1002,'操作失败：'.$res['msg']);
        }
        Db::table('top_org_tasks')->where(['id'=>$id])->update(['status'=>-1]);
        result('',0,'操作成功');
    }

    //完成任务
    public  function finishTask($id=0) {
        $taskInfo = Db::table('top_org_tasks')->where(['id'=>$id])->find();
        $res = SecsModel::getSecsInterfaceData('secsTask/finishedTaskNotice',['taskcode'=>$taskInfo['code']],'post');
        if($res['code']!=0){
            result('',1002,'操作失败：'.$res['msg']);
        }
        Db::table('top_org_tasks')->where(['id'=>$id])->update(['status'=>9]);
        result('',0,'操作成功');	
    }

    /**
     * NotHeaders
     * @Apidoc\Title("专家临时请假接口")
     * @Apidoc\Desc("专家临时请假接口")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("日程")
     * @Apidoc\Param("taskcode", type="string", require=true,desc="评审任务单号"),
     * @Apidoc\Param("experts", type="list", require=true,desc="请假专家列表",children={
     * @Apidoc\Param("expert_id", type="string", require=true,desc="专家唯一id"),
     * @Apidoc\Param("expert_name", type="string", require=true,desc="专家姓名"),
     * }),
     * @Apidoc\Param("time", type="string", require=true,desc="请求时间：2025-01-02 09:12:23"),
     * @Apidoc\Param("sign", type="string", require=true,desc="签名，签名规则：MD5(taskcode+time+秘钥key)"),
     */
    public function rsa($id=0) {

// 生成私钥和公钥
        $config = [
            "digest_alg" => "sha512",
            "private_key_bits" => 2048,
            "private_key_type" => OPENSSL_KEYTYPE_RSA,
        ];
        $res = openssl_pkey_new($config);
        dd($res);
        openssl_pkey_export($res, $privateKey);
        $publicKeyDetails = openssl_pkey_get_details($res);
        $publicKey = $publicKeyDetails["key"];
        exit($publicKey);
        exit($privateKey);

// 加密数据
        $data = "Hello, RSA!";
        openssl_public_encrypt($data, $encrypted, $publicKey);
        echo "Encrypted: " . base64_encode($encrypted) . "\n";

// 解密数据
        openssl_private_decrypt($encrypted, $decrypted, $privateKey);
        echo "Decrypted: " . $decrypted . "\n";

    }

    /**
     * NotHeaders
     * @Apidoc\Title("专家分配结果接收")
     * @Apidoc\Desc("专家分配结果通知/专家变更结果通知")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("日程")
     * @Apidoc\Param("taskcode", type="string", require=true,desc="评审任务单号"),
     * @Apidoc\Param("experts", type="list", require=true,desc="分配专家列表",children={
         * @Apidoc\Param("expert_id", type="string", require=true,desc="专家唯一id"),
         * @Apidoc\Param("expert_name", type="string", require=true,desc="专家姓名"),
         * @Apidoc\Param("mobile", type="string", require=true,desc="专家手机号"),
         * @Apidoc\Param("email", type="string", desc="专家邮箱"),
     * }),
     * @Apidoc\Param("time", type="string", require=true,desc="请求时间：2025-01-02 09:12:23"),
     * @Apidoc\Param("sign", type="string", require=true,desc="签名，签名规则：MD5(taskcode+time+秘钥key)"),
     */
    public function tasksExpert($id=0) {
        $params = $this->request->param();
		//记录日志
		SecsModel::logs("tasksExpert",$params,[]);
		$secsConfig = getSecsConfig();
		$appkey = $secsConfig['appkey'];
        $sign = md5($params['taskcode'].$params['time'].$appkey);
        if($sign!==$params['sign']){
            result('',8001,'签名错误');
        }
        $re = Db::table('top_org_tasks')->where(['id'=>$id])->find();
        if(empty($re)){
            result('',1001,'信息有误');
        }
        $params = $this->request->param('','','trim');
        $data = [
            'tasks_id' => $id,
            'date' => $params['date'],
            'number' => $params['number'],
            'number1' => $params['number1'],
            'time' => date('Y-m-d H:i:s'),
        ];
        Db::table('top_org_tasks')->where(['id'=>$id])->update(['date'=>$params['date']]);
        Db::table('top_org_tasks_plan')->insert($data);
        /**
         * 推送到专家系统
         */
        result('',0,'操作成功');
    }

    /**
     * NotHeaders
     * @Apidoc\Title("专家分配结果接收")
     * @Apidoc\Desc("专家分配结果通知/专家变更结果通知")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("日程")
     * @Apidoc\Param("taskcode", type="string", require=true,desc="评审任务单号"),
     * @Apidoc\Param("experts", type="list", require=true,desc="分配专家列表",children={
         * @Apidoc\Param("expert_id", type="string", require=true,desc="专家唯一id"),
         * @Apidoc\Param("expert_name", type="string", require=true,desc="专家姓名"),
     * }),
     * @Apidoc\Param("time", type="string", require=true,desc="请求时间：2025-01-02 09:12:23"),
     * @Apidoc\Param("sign", type="string", require=true,desc="签名，签名规则：MD5(taskcode+time+秘钥key)"),
     */
    public function expertCancel($id=0) {
        $file = '/opt/tdoa/webroot/general/toppingsoft/expertCancel.txt';
        $params = $this->request->param();
        file_put_contents($file,date('【Y-m-d H:i:s】').json_encode($params)."\n",FILE_APPEND);
        $sign = md5($params['taskcode'].$params['time'].'QZ6O8PLI4M57GKAJVUHZF2T0E4SNAET2');
        if($sign!==$params['sign']){
            result('',8001,'签名错误');
        }
        result();
        $re = Db::table('top_org_tasks')->where(['id'=>$id])->find();
        if(empty($re)){
            result('',1001,'信息有误');
        }
        $params = $this->request->param('','','trim');
        $data = [
            'tasks_id' => $id,
            'date' => $params['date'],
            'number' => $params['number'],
            'number1' => $params['number1'],
            'time' => date('Y-m-d H:i:s'),
        ];
        Db::table('top_org_tasks')->where(['id'=>$id])->update(['date'=>$params['date']]);
        Db::table('top_org_tasks_plan')->insert($data);
        /**
         * 推送到专家系统
         */
        result('',0,'操作成功');
    }


    /**
     * @Apidoc\Title("获取任务专家列表")
     * @Apidoc\Desc("获取指定评审任务的专家列表")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("日程")
     * @Apidoc\Param("id", type="integer", require=true, desc="任务ID")
     * @Apidoc\Returned("data", type="object", desc="专家列表数据")
     */
    public function getTaskExperts($id=0) {
        if(empty($id)){
            result('',1001,'任务ID不能为空');
        }
        $task = Db::table('top_org_tasks')->where(['id'=>$id])->find();
        if(empty($task)){
            result('',1001,'任务不存在');
        }
        
        $experts = Db::table('top_org_tasks_experts')
            ->where(['tasks_id'=>$id])
            ->field('id,expert_id,expert_name,position')
            ->order('id desc')
            ->select();
        result(['experts'=>$experts]);
    }
	
	
	/**
     * @Apidoc\Title("组长确认")
     * @Apidoc\Desc("组长确认评审任务")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("组长确认")
     * @Apidoc\Param("tasks_id", type="integer", require=true, desc="任务ID")
     * @Apidoc\Param("expert_id", type="integer", require=true, desc="组长")
     * @Apidoc\Returned("data", type="object", desc="操作结果")
     */
    public function confirmLeader($tasks_id=0,$expert_id='',$video_id='',$video_id1='',$video_id2='',$video_name='',$video_name1='',$video_name2='') {
        $re = Db::table('top_org_tasks')->where(['id'=>$tasks_id])->find();
        if(empty($re)){
            result('',1001,'任务不存在');
        }
        Db::startTrans();
        try {
            // 更新任务状态为审核通过(7)
            Db::table('top_org_tasks')->where(['id'=>$tasks_id])->update(['status'=>7,'video_id'=>$video_id,'video_id1'=>$video_id1,'video_id2'=>$video_id2]);
            Db::table('top_org_tasks_experts')
                ->where('tasks_id',$tasks_id)
                ->where('expert_id',$expert_id)
                ->update(['position'=>'组长','position_id'=>1]);
            Db::table('top_org_tasks_experts')
                ->where('tasks_id',$tasks_id)
                ->where('expert_id','<>',$expert_id)
                ->update(['position'=>'组员']);
            if(!empty($video_id)){
                $experts = Db::table('top_org_tasks_experts')
                    ->where('tasks_id',$tasks_id)->select()->toArray();
                $video = new VideoModel();
                $data = [
                    'terminalName' => $video_name,
                    'firmNameId' => $video_id,
                    'videoAccountId'=>$video_id1,
                    'videoAccountName'=>$video_name1,
                    'supervisionAccountId'=>$video_id2,
                    'supervisionAccountName'=>$video_name2,
                    'firmName' => $re['company_name'],
                    'taskId' => $re['code'],
                    'humans' => [],
                ];
                foreach ($experts as $v){
                    $data['humans'][] = ['name'=>$v['expert_name'],'id'=>$v['expert_id']];
                }
                $video->pushTask($data);
            }
            Db::commit();
            result('',0,'操作成功');
        } catch (\Exception $e) {
            Db::rollback();
            result('',1003,'操作失败：'.$e->getMessage());
        }
    }

    //提交专家评分并完成任务
    public function submitExpertScores() {
        $params = $this->request->param();
        $task_id = $params['task_id'];
        //获取任务信息
        $taskInfo = Db::table('top_org_tasks')->where(['id'=>$task_id])->find();
        if(empty($taskInfo)){
            result('',1001,'任务不存在');
        }
        //获取专家ID对照表
        $expertIds = Db::table('top_expert')->column('expert_id','id');
        $scores = $params['scores'];
        $expertsScores = [];
        foreach($scores as $key=>$val){
            if(!isset($expertIds[$val['id']])){
                result('',1001,$val['name']."[".$val['id'].']专家不存在');
            }
            $expertsScores[] = [
                'expert_id'=>$expertIds[$val['id']],
                'expert_name'=>$val['name'],
                'isLeader'=>$val['position']=='组长',
                'evaluate'=>[[
                    "score_item"=>"专业知识",
                    "score"=>$val['knowledge']*5
                    ],
                    [
                    "score_item"=>"业务能力",
                    "score"=>$val['ability']*5
                    ],
                    [
                    "score_item"=>"工作态度",
                    "score"=>$val['attitude']*5
                    ],
                    [
                    "score_item"=>"廉政意识",
                    "score"=>$val['integrity']*5
                    ]],
               'evaluate_total_score'=>($val['knowledge']+$val['ability']+$val['attitude']+$val['integrity'])*5,     
            ];
        }
        $outData = [
            'taskcode'=>$taskInfo['code'],
            'experts'=>$expertsScores,
        ];

        $res = SecsModel::getSecsInterfaceData('secsTask/finishedTaskNotice',$outData,'post');
        if($res['code']!=0){
            result('',1002,'操作失败：'.$res['msg']);
        }
        Db::table('top_org_tasks')->where(['id'=>$task_id])->update(['status'=>10]);
        result($res);	
    }

    public function info($id=0){
        if (request()->isAjax()) {
            $task = Db::table('top_org_tasks')->where(['id'=>$id])->find();
            $details = Db::table('top_org_tasks_works')->where(['tasks_id'=>$task['id']])->order('sort')->select()->toArray();
            $detail = [];
            foreach ($details as $v){
                $v['files'] = json_decode($v['files'],true);
                $detail[$v['stage']][] = $v;
            }
            $review_flow = config('global.review_flow');
            foreach ($review_flow as $k=>$v){
                $review_work[] = [
                    'stage'=>$v['stage'],
                    'work' => $detail[$k],
                ];
            }
            $data['review_work'] = $review_work;
            $res = Db::table('top_org_tasks_experts')->where(['tasks_id'=>$id])->select()->toArray();
            $ids = [];
            foreach ($res as $v){
                $eids = explode(',',$v['element_id']);
                foreach ($eids as $v1){
                    if($v1>0){
                        $idarr[$v1] = [
                            'user_name' => $v['expert_name'],
                            'status' => $v['status'],
                        ];
                        $ids[] = $v1;
                    }
                }
            }
            $review_list = Db::table('top_org_tasks_element')->where('tasks_id','=',$id)->select()->toArray();
            $data['review_list'] = $review_list;
            result($data);
        } else {
            $task = Db::table('top_org_tasks')->where(['id'=>$id])->find();
            $re = Db::table('top_org_tasks_experts')->where(['tasks_id'=>$id,'expert_id'=>$_SESSION['expert']['id']])->find();
            $grading = Db::table('top_grading')->where(['id'=>$task['grading_id']])->find();
            $details = Db::table('top_org_tasks_works')->where(['tasks_id'=>$task['id']])->order('sort')->select()->toArray();
            $detail = [];
            foreach ($details as $v){
                $v['files'] = json_decode($v['files'],true);
                $detail[$v['stage']][] = $v;
            }
            $review_flow = config('flow.review_flow');
            foreach ($review_flow as $k=>$v){
                $review_work[] = [
                    'stage'=>$v['stage'],
                    'work' => $detail[$k],
                ];
            }
            $url = $_SERVER['REQUEST_SCHEME'].'://'.$_SERVER['HTTP_HOST'].'/general/toppingsoft/index.php/exam/mobile/myd?id='.$id;
            // 实例化QrCode对象
            $qrCode = new QrCode($url);
            // 设置二维码的尺寸
            $qrCode->setSize(500);
            // 设置二维码的边距
            $qrCode->setMargin(10);
            // 设置二维码的颜色和背景颜色
            $qrCode->setForegroundColor(['r' => 0, 'g' => 0, 'b' => 0, 'a' => 0]);
            View::assign('mydcodeimg', $qrCode->writeDataUri());
            View::assign('review_flow', $review_work);
            View::assign('task', $task);
            View::assign('re', $re);
            View::assign('grading', $grading);
            return view();
        }
    }
    public function video($id=0){
        if (request()->isAjax()) {
            $where = [
                ['org_id','=',$_SESSION['org']['id']],
                ['date','=',date('Y-m-d')],
                ['video_id','>',0],
            ];
            $res = Db::table('top_org_tasks')->where($where)->field('id,company_name title,video_id number')->select()->toArray();
            if(empty($res)){
                $res = [
                    ['title'=>'测试企业001','number'=>'160401'],
                    ['title'=>'测试企业002','number'=>'160402'],
                    ['title'=>'测试企业003','number'=>'160403'],
                    ['title'=>'测试企业004','number'=>'160404'],
                ];
            }
            result($res);
        } else {
            return view();
        }
    }

    public function companyInfo($id = 0)
    {
        \app\model\CompanyModel::getCompanyInfo($id);
        exit();
    }

    public function getReview($element_id = 0)
    {
        if (request()->isAjax()) {
            $request = $this->request->post();
            \app\model\CompanyModel::getAjaxReview($element_id, $request);
            exit();

        } else {
            View::assign('element_id', $element_id);
            View::assign('title', '首页');
            return view();
        }
    }

}
