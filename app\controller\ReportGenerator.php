<?php

namespace app\controller;

use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\PhpWord;
use think\facade\Filesystem;

class ReportGenerator
{
    /**
     * 解析功能内容文件，生成工作内容数组
     * @return array 功能内容数组
     */
    private static function parseFeatureContent()
    {
        $filePath = root_path() . 'docs/功能内容.txt';
        $content = file_get_contents($filePath);
        // 由于文件编码可能是GBK，需要转换为UTF-8
        if (!mb_check_encoding($content, 'utf-8')) {
           // $content = mb_convert_encoding($content, 'UTF-8', 'GBK,GB2312,CP936');
        }
        // 解析功能内容
        $features = [];
        $lines = explode("\n", $content);
        $currentStage = '';
        $currentWeek = '';
        foreach ($lines as $line) {
            $trimmed = trim($line);
            if (empty($trimmed)) continue;
            // 阶段标题
            if (preg_match('/^###\s*(第[一二三四]阶段)：(.+?)（第(\d+)-(\d+)周）/', $trimmed, $m)) {
                $currentStage = $m[2];
                continue;
            }
            // 周标题
            if (preg_match('/^- \*\*(第\d+周)\*\*/', $trimmed, $m)) {
                $currentWeek = $m[1];
                continue;
            }
            // 功能点
            if (preg_match('/^- \*\*(.+?)\*\*：(.+)/', $trimmed, $m)) {
                $features[] = [
                    'stage' => $currentStage,
                    'week' => $currentWeek,
                    'module' => isset($m[1]) ? $m[1] : '未知模块',
                    'name' => $m[1],
                    'description' => $m[2]
                ];
                if (!isset($m[1])) {
                    error_log("功能点行未能正确提取 module 字段: " . $line);
                }
                continue;
            }
            // 子功能点（缩进）
            if (preg_match('/^\s{4,}- \*\*(.+?)\*\*：(.+)/', $line, $m)) {
                $features[] = [
                    'stage' => $currentStage,
                    'week' => $currentWeek,
                    'module' => isset($m[1]) ? $m[1] : '未知模块',
                    'name' => $m[1],
                    'description' => $m[2]
                ];
                if (!isset($m[1])) {
                    error_log("子功能点行未能正确提取 module 字段: " . $line);
                }
                continue;
            }
        }
        return $features;
    }
    
    /**
     * 根据日期范围智能分配功能到工作内容
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @param array $features 功能内容数组
     * @return array 分配后的工作内容
     */
    private static function assignFeaturesToDateRange($startDate, $endDate, $features)
    {
        // 计算日期范围内的天数
        $start = strtotime($startDate);
        $end = strtotime($endDate);
        $days = floor(($end - $start) / 86400) + 1;
        
        // 根据日期范围长度决定分配的功能数量
        $featureCount = min(count($features), max(3, floor($days / 2)));
        
        // 随机选择功能
        $selectedIndices = array_rand($features, $featureCount);
        if (!is_array($selectedIndices)) {
            $selectedIndices = [$selectedIndices];
        }
        
        $tasks = [];
        foreach ($selectedIndices as $index) {
            $feature = $features[$index];
            $tasks[] = [
                'name' => $feature['module'] . ': ' . $feature['name'],
                'description' => !empty($feature['description']) ? $feature['description'] : '完成' . $feature['name'] . '功能开发'
            ];
        }
        
        return $tasks;
    }
    
    /**
     * 生成周报
     * @param string $yearMonth 年月格式 YYYY-MM
     * @param int $week 第几周
     */
    public static function generateWeeklyReport($yearMonth, $week)
    {
        $phpWord = new PhpWord();
        
        // 设置默认字体和段落样式
        $phpWord->setDefaultFontName('宋体');
        $phpWord->setDefaultFontSize(10.5);
        
        $section = $phpWord->addSection();
        
        // 计算日期范围
        $year = substr($yearMonth, 0, 4);
        $month = substr($yearMonth, 5, 2);
        
        // 计算该月第几周的起止日期
        $firstDayOfMonth = strtotime("{$year}-{$month}-01");
        $startDate = strtotime("+" . (($week - 1) * 7) . " days", $firstDayOfMonth);
        $endDate = strtotime("+6 days", $startDate);
        
        $startDateStr = date('n月j日', $startDate);
        $endDateStr = date('n月j日', $endDate);
        
        // 添加标题 - 居中
        $titleStyle = ['bold' => true, 'size' => 19];
        $titleParagraphStyle = ['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER];
        $section->addText('"标准化管理系统"工作报告', $titleStyle, $titleParagraphStyle);
        
        // 添加日期范围 - 居中
        $dateStyle = ['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER];
        $section->addText("（{$year}年{$startDateStr}—{$endDateStr}）",['bold' => true, 'size' => 13], $dateStyle);
        // 创建表格
        $table = $section->addTable(['borderSize' => 1, 'borderColor' => '000000', 'width' => 100 * 50, 'unit' => 'pct']);
        
        // 表头行
        $table->addRow();
        $table->addCell(1500)->addText('主要板块', ['bold' => true]);
        $table->addCell(1500)->addText('子任务', ['bold' => true]);
        $table->addCell(6000)->addText('本周工作总结/下周工作计划', ['bold' => true]);
        
        // 计算本周日期范围
        $weekStartDate = date('Y-m-d', $startDate);
        $weekEndDate = date('Y-m-d', $endDate);
        
        // 计算下周的起止日期 - 从本周结束日期推算
        $nextWeekStart = strtotime("+1 day", $endDate);
        $nextWeekEnd = strtotime("+7 days", $nextWeekStart);
        $nextWeekStartDate = date('Y-m-d', $nextWeekStart);
        $nextWeekEndDate = date('Y-m-d', $nextWeekEnd);
        
        // 获取功能内容
        $features = self::parseFeatureContent();
        
        // 智能分配功能到本周工作内容
        $weeklyTasks = self::assignFeaturesToDateRange($weekStartDate, $weekEndDate, $features);
        
        // 智能分配功能到下周计划
        $nextWeekTasks = self::assignFeaturesToDateRange($nextWeekStartDate, $nextWeekEndDate, $features);
        
        // 内容行
        $table->addRow();
        $mainCell = $table->addCell(1500);
        $mainCell->addText('功能模块');
        
        $subCell = $table->addCell(1500);
        // 子任务单元格留空
        
        $contentCell = $table->addCell(6000);
        $contentCell->addText('一、本周工作总结');
        
        // 添加本周工作内容
        foreach ($weeklyTasks as $index => $task) {
            $contentCell->addText(sprintf('%d. %s', $index + 1, $task['name']));
            if (!empty($task['description'])) {
                $contentCell->addText('   ' . $task['description'], ['size' => 11, 'italic' => false]);
            }
        }
        
        $contentCell->addText('二、下周工作计划');
        
        // 添加下周计划
        foreach ($nextWeekTasks as $index => $task) {
            $contentCell->addText(sprintf('%d. %s', $index + 1, $task['name']));
            if (!empty($task['description'])) {
                $contentCell->addText('   ' . $task['description'], ['size' => 11, 'italic' => false]);
            }
        }
        
        // 保存文件
        $fileName = "{$yearMonth}-第{$week}周周报.docx";
        $filePath = Filesystem::disk('public')->path("docs/{$fileName}");
        
        $objWriter = IOFactory::createWriter($phpWord, 'Word2007');
        $objWriter->save($filePath);
        
        return $filePath;
    }
    
    /**
     * 生成月报
     * @param string $yearMonth 年月格式 YYYY-MM
     */
    public static function generateMonthlyReport($yearMonth)
    {
        $phpWord = new PhpWord();
        
        // 设置默认字体和段落样式
        $phpWord->setDefaultFontName('宋体');
        $phpWord->setDefaultFontSize(10.5);
        
        $section = $phpWord->addSection();
        
        // 解析年月
        $year = substr($yearMonth, 0, 4);
        $month = substr($yearMonth, 5, 2);
        $nextMonthDate = strtotime('+1 month', strtotime($yearMonth));
        $nextMonthStr = date('n月', $nextMonthDate);
        
        // 添加标题 - 居中
        $titleStyle = ['bold' => true, 'size' => 16, 'alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER];
        $section->addText("\"标准化管理系统\"工作报告", $titleStyle);
        
        // 添加日期范围 - 居中
        $dateStyle = ['size' => 12, 'alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER];
        $section->addText("（{$year}年{$month}月—{$nextMonthStr}）", $dateStyle);
        
        // 创建表格
        $table = $section->addTable(['borderSize' => 1, 'borderColor' => '000000', 'width' => 100 * 50, 'unit' => 'pct']);
        
        // 表头行
        $table->addRow();
        $table->addCell(1500)->addText('主要板块', ['bold' => true]);
        $table->addCell(1500)->addText('子任务', ['bold' => true]);
        $table->addCell(6000)->addText('本月工作总结/下月工作计划', ['bold' => true]);
        
        // 获取本月的起止日期
        $monthStart = date('Y-m-01', strtotime($yearMonth));
        $monthEnd = date('Y-m-t', strtotime($yearMonth));
        
        // 获取下月的起止日期
        $nextMonth = date('Y-m', strtotime('+1 month', strtotime($yearMonth)));
        $nextMonthStart = date('Y-m-01', strtotime($nextMonth));
        $nextMonthEnd = date('Y-m-t', strtotime($nextMonth));
        
        // 获取功能内容
        $features = self::parseFeatureContent();
        
        // 智能分配功能到本月工作内容
        $monthlyTasks = self::assignFeaturesToDateRange($monthStart, $monthEnd, $features);
        
        // 智能分配功能到下月计划
        $nextMonthTasks = self::assignFeaturesToDateRange($nextMonthStart, $nextMonthEnd, $features);
        
        // 内容行
        $table->addRow();
        $mainCell = $table->addCell(1500);
        $mainCell->addText('功能模块');
        
        $subCell = $table->addCell(1500);
        // 子任务单元格留空
        
        $contentCell = $table->addCell(6000);
        $contentCell->addText('一、本月工作总结');
        
        // 添加本月工作内容
        foreach ($monthlyTasks as $index => $task) {
            $contentCell->addText(sprintf('%d. %s', $index + 1, $task['name']));
            if (!empty($task['description'])) {
                $contentCell->addText('   ' . $task['description'], ['size' => 11, 'italic' => false]);
            }
        }
        
        $contentCell->addText('二、下月工作计划');
        
        // 添加下月计划
        foreach ($nextMonthTasks as $index => $task) {
            $contentCell->addText(sprintf('%d. %s', $index + 1, $task['name']));
            if (!empty($task['description'])) {
                $contentCell->addText('   ' . $task['description'], ['size' => 11, 'italic' => false]);
            }
        }
        
        // 保存文件
        $fileName = "{$yearMonth}-月报.docx";
        $filePath = Filesystem::disk('public')->path("docs/{$fileName}");
        
        $objWriter = IOFactory::createWriter($phpWord, 'Word2007');
        $objWriter->save($filePath);
        
        return $filePath;
    }
}