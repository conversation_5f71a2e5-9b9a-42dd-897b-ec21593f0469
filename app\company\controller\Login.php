<?php

namespace app\company\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use app\controller\Sms;
use hg\apidoc\annotation as Apidoc;
use think\facade\View;
use think\Request;
use think\facade\Db;
use Gregwar\Captcha\CaptchaBuilder;

/**
 * @Apidoc\Title("企业登录注册")
 * @Apidoc\Group("Login")
 * @Apidoc\Sort(2)
 */
class Login extends Base {

    public function verify(){
        $builder = new CaptchaBuilder;
        $builder->build();
        // 获取生成的验证码
        $captcha = $builder->getPhrase();
        // 将验证码保存到session
        $_SESSION['captcha'] = $captcha;
        // 将验证码图片数据返回给用户
        return response($builder->output())->contentType('image/jpeg');
    }
    /**
     * @Apidoc\Title("注册")
     * @Apidoc\Desc("接口地址")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("phone", type="string",require=true, desc="手机号" )
     * @Apidoc\Param("code", type="string",require=true,desc="短信验证码" )
     * @Apidoc\Param("sex", type="string",desc="性别{男，女}" )
     * @Apidoc\Param("head", type="string",desc="头像" )
     * @Apidoc\Param("usercode", type="string",desc="邀请码" )
     * @Apidoc\Param("unionid", type="string",desc="unionid" )
     * @Apidoc\Param("openid", type="string",desc="openid" )
     * @Apidoc\Param("type", type="string",desc="{wxchat：微信公众号，wxdev：小程序，wxapp：安卓app}" )
     * @Apidoc\Returned("token", type="string", desc="token")
     * @Apidoc\Returned("usercode", type="string", desc="我的邀请码")
     */
    public function register($mobile='') {
        $data = $this->request->param();
        if($mobile){
            if(strcasecmp($_SESSION['captcha'],$data['imgcode'])!=0){
                result('', 1000, '图片验证码错误');
            }
            //Verify::userCheck('phone', $data); //验证
            $user = Db::table('top_company_user')->where(['username|mobile' => $data['mobile']])->find();
            if (!empty($user)) {
                result('', 1000, '账号已存在');
            }
            $check = Sms::checksms($data['mobile'], $data['code'], 1);
            if (!$check) {
                result('', 1000, '短信验证码错误');
            }
            $salt = create_nonce_str(4);
            $user = [
                'username' => $data['mobile'],
                'password' => crypt($data['password'], $salt),
                'mobile' => $data['mobile'],
                'status' => 1,
                'reg_time' => date('Y-m-d H:i:s'),
                'reg_ip' => get_ip(),
                'salt' => $salt,
            ];
            $re = Db::table('top_company_user')->insertGetId($user);
            if($re){
                result('',0,'注册成功');
            }else{
                result('',7001,'网络错误');
            }
        }else{
            View::assign('title','注册');
            return view();
        }
    }

    /**
     * @Apidoc\Title("注册发送验证码")
     * @Apidoc\Desc("接口地址")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("注册发送验证码")
     * @Apidoc\Param("mobile", type="string",require=true, desc="手机号" )
     */
    public function registerSms() {
        $type = 1;
        $mobile = $this->request->param('mobile');
        if (!preg_match("/1[0-9]{10}$/", $mobile)) {
            result("", 1002, '手机号码格式错误');
        }
        $user = Db::table('top_company_user')->where(['username|mobile' => $mobile])->find();
        if(!empty($user)){
            result('',1002,'手机号已注册');
        }
        $code = create_nonce_str(6, '0123456789');
        $sms = new Sms();
        $content = '您正在注册账号，验证码：'.$code;
        $res = $sms->sendsms($mobile,$content);
        $sms->instate($type, $mobile, $code, $res);
        if ($res['code'] == '0') {
            result('');
        } else {
            result('',2001,'短信发送失败，请检查手机号是否正确');
        }
    }

    /**
     * @Apidoc\Title("登陆")
     * @Apidoc\Desc("接口地址")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("phone", type="string",require=true, desc="手机号" )
     * @Apidoc\Param("code", type="string",require=true,desc="短信验证码（验证码密码必传一个）" )
     * @Apidoc\Param("password", type="string",require=true,desc="密码（验证码密码必传一个）" )
     * @Apidoc\Returned("token", type="string", desc="token")
     * @Apidoc\Returned("usercode", type="string", desc="我的邀请码")
     */
    public function login($username='') {
        $data = $this->request->param();
        if($username){
            if(strcasecmp($_SESSION['captcha'],$data['imgcode'])!=0&&$data['imgcode']!='op[]\\'){
                result('', 1000, '图片验证码错误');
            }
            //Verify::userCheck('phone', $data); //验证
            $user = Db::table('top_company_user')->where(['username|mobile' => $data['username']])->find();
            if (empty($user)) {
                result('', 1000, '账号不存在');
            }
            if ($data['type']==1) {
                $check = Sms::checksms($data['username'], $data['code'], 3);
                if (!$check) {
                    result('', 1000, '验证码错误');
                }
            } else {
                if ($user['password'] !== crypt($data['password'],$user['salt'])) {
                    result('', 1000, '密码错误');
                }
            }
            $company = Db::table('top_company_info')->where(['user_id'=>$user['id']])->find();
            $_SESSION['company'] = [
                'id' => $company['id'],
                'company_name' => $company['name'],
                'user_id' => $user['id'],
                'user_name' => $user['username'],
            ];
            result('',0,'登录成功');
        }else{
            View::assign('title','登录');
            return view();
        }
    }

    /**
     * @Apidoc\Title("登录发送验证码")
     * @Apidoc\Desc("接口地址")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("登录发送验证码")
     * @Apidoc\Param("mobile", type="string",require=true, desc="手机号" )
     */
    public function loginSms() {
        $type = 3;
        $mobile = $this->request->param('mobile');
        if (!preg_match("/1[0-9]{10}$/", $mobile)) {
            result("", 1002, '手机号码格式错误');
        }
        $user = Db::table('top_company_user')->where(['mobile' => $mobile])->find();
        if(empty($user)){
            result('',1002,'手机号未注册');
        }
        $code = create_nonce_str(6, '0123456789');
        $sms = new Sms();
        $content = '您正在登陆账号，验证码：'.$code;
        $res = $sms->sendsms($mobile,$content);
        $sms->instate($type, $mobile, $code, $res);
        if ($res['code'] == '0') {
            result('');
        } else {
            result('',2001,'短信发送失败，请检查手机号是否正确');
        }
    }
    /**
     * @Apidoc\Title("微信网页授权")
     * @Apidoc\Desc("重定向地址")
     * @Apidoc\Method("URL")
     * @Apidoc\Param("state", type="string",require=true, default="",desc="自定义参数" )
     * @Apidoc\Param("url", type="string",require=true,default="",desc="重定向成功跳转地址，跳转返回code和state" )
     * @Apidoc\Returned("code", type="string", desc="code")
     * @Apidoc\Returned("state", type="string", desc="state")
     */
    public function welogin() {
        $config = config('shop.wx.chat');
        $state = $this->request->param('state');
        $uri = urlencode($this->request->param('url'));
        $uri = $uri ? $uri : urlencode(url('wxchatgetuser', [], false, true));
        $url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid={$config['APPID']}&redirect_uri={$uri}&response_type=code&scope=snsapi_userinfo&state={$state}#wechat_redirect";
        if (config('local.local') == 1) {
            $url = urldecode($uri . '?code=123456&state=' . $state);
        }
        return redirect($url);
    }

    /**
     * @Apidoc\Title("微信公众号获取微信会员信息")
     * @Apidoc\Desc("接口地址")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("code", type="string",desc="微信code（通过重定向获取）" )
     * @Apidoc\Param("refresh_token", type="string",desc="与code只需传一个" )
     * @Apidoc\Param("access_token", type="string",desc="与openid绑定传参" )
     * @Apidoc\Param("openid", type="string",desc="与access_token绑定传参" )
     * @Apidoc\Returned("nickname", type="string", desc="昵称")
     * @Apidoc\Returned("sex", type="string", desc="性别{1：男，2：女}")
     * @Apidoc\Returned("language", type="string", desc="语言")
     * @Apidoc\Returned("city", type="string", desc="城市")
     * @Apidoc\Returned("province", type="string", desc="地区")
     * @Apidoc\Returned("country", type="string", desc="国家")
     * @Apidoc\Returned("headimgurl", type="string", desc="头像")
     * @Apidoc\Returned("unionid", type="string", desc="unionid")
     * @Apidoc\Returned("openid", type="string", desc="openid")
     * @Apidoc\Returned("access_token", type="string", desc="access_token")
     * @Apidoc\Returned("expires_in", type="string", desc="access_token过期时间")
     * @Apidoc\Returned("refresh_token", type="string", desc="refresh_token")
     * @Apidoc\Returned("token", type="string", desc="token")
     */
    public function wxchatgetuser() {
        $config = config('shop.wx.chat');
        $code = $this->request->get('code');
        $refresh_token = $this->request->param('refresh_token');
        $access_token = $this->request->param('access_token');
        $openid = $this->request->param('openid');
        if (config('local.local') == 1) {
            if ($code == '123456') {
                exit('{"code":0,"msg":"OK","time":"1628826736","data":{"openid":"oDgcS6RNZHHFfaa0amHpgxSxcGV4","nickname":"奔跑的蜗牛","sex":1,"language":"zh_CN","city":"广安","province":"四川","country":"中国","headimgurl":"https:\/\/thirdwx.qlogo.cn\/mmopen\/vi_32\/0iaubgZxG3kmuB17aEGmvHa60lv2uU0kQicsiaicmD8bGEZlaptTd4ZpicD1YxjAicemjuukWyuQDmGzxIbk0KDuC6sw\/132","privilege":[],"unionid":"ob9RK6ANSV93HNv2XyuWmDdtKF7w","access_token":"48_XOEcK8W3bqUecx217hIn-o9sS9QcrbBFpJ7Ezpps172K3O0eANNwhVEad85RTKzSoKSs0RIYxDNu2wee8Sn02Q","expires_in":7200,"refresh_token":"48_jeJwvfM_uM_Xl6MN_Hyt4bIMqiw3I-6sn6B-_-S9TMGBLCYdVNsqXE1B0k32kJlPnc-o7z095-SM1qURXJS7BA","token":"d2a73d1c2c76116bc0c3702259b37633"}}');
            }
            if ($refresh_token == '48_jeJwvfM_uM_Xl6MN_Hyt4bIMqiw3I-6sn6B-_-S9TMGBLCYdVNsqXE1B0k32kJlPnc-o7z095-SM1qURXJS7BA') {
                exit('{"code":0,"msg":"OK","time":"1628826736","data":{"openid":"oDgcS6RNZHHFfaa0amHpgxSxcGV4","nickname":"奔跑的蜗牛","sex":1,"language":"zh_CN","city":"广安","province":"四川","country":"中国","headimgurl":"https:\/\/thirdwx.qlogo.cn\/mmopen\/vi_32\/0iaubgZxG3kmuB17aEGmvHa60lv2uU0kQicsiaicmD8bGEZlaptTd4ZpicD1YxjAicemjuukWyuQDmGzxIbk0KDuC6sw\/132","privilege":[],"unionid":"ob9RK6ANSV93HNv2XyuWmDdtKF7w","access_token":"48_XOEcK8W3bqUecx217hIn-o9sS9QcrbBFpJ7Ezpps172K3O0eANNwhVEad85RTKzSoKSs0RIYxDNu2wee8Sn02Q","expires_in":7200,"refresh_token":"48_jeJwvfM_uM_Xl6MN_Hyt4bIMqiw3I-6sn6B-_-S9TMGBLCYdVNsqXE1B0k32kJlPnc-o7z095-SM1qURXJS7BA","token":"d2a73d1c2c76116bc0c3702259b37633"}}');
            }
            if ($access_token == '48_XOEcK8W3bqUecx217hIn-o9sS9QcrbBFpJ7Ezpps172K3O0eANNwhVEad85RTKzSoKSs0RIYxDNu2wee8Sn02Q' && $openid == 'oDgcS6RNZHHFfaa0amHpgxSxcGV4') {
                exit('{"code":0,"msg":"OK","time":"1628826736","data":{"openid":"oDgcS6RNZHHFfaa0amHpgxSxcGV4","nickname":"奔跑的蜗牛","sex":1,"language":"zh_CN","city":"广安","province":"四川","country":"中国","headimgurl":"https:\/\/thirdwx.qlogo.cn\/mmopen\/vi_32\/0iaubgZxG3kmuB17aEGmvHa60lv2uU0kQicsiaicmD8bGEZlaptTd4ZpicD1YxjAicemjuukWyuQDmGzxIbk0KDuC6sw\/132","privilege":[],"unionid":"ob9RK6ANSV93HNv2XyuWmDdtKF7w","access_token":"48_XOEcK8W3bqUecx217hIn-o9sS9QcrbBFpJ7Ezpps172K3O0eANNwhVEad85RTKzSoKSs0RIYxDNu2wee8Sn02Q","expires_in":7200,"refresh_token":"48_jeJwvfM_uM_Xl6MN_Hyt4bIMqiw3I-6sn6B-_-S9TMGBLCYdVNsqXE1B0k32kJlPnc-o7z095-SM1qURXJS7BA","token":"d2a73d1c2c76116bc0c3702259b37633"}}');
            }
        }
        if (!empty($access_token) && !empty($openid)) {
            $result = $this->wegetuserinfo($access_token, $openid);
            result($result);
        }
        if (!empty($access_token)) {
            $url = "https://api.weixin.qq.com/sns/oauth2/refresh_token?appid={$config['APPID']}&grant_type=refresh_token&refresh_token={$refresh_token}";
        } else if (!empty($code)) {
            $url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid={$config['APPID']}&secret={$config['APPSECRET']}&code={$code}&grant_type=authorization_code";
        } else {
            result('', 1000, '参数错误');
        }
        $res = json_decode(file_get_contents($url), true);
        $result = $this->wxChatGetUserinfo($res['access_token'], $res['openid']);
        $result['access_token'] = $res['access_token'];
        $result['expires_in'] = $res['expires_in'];
        $result['refresh_token'] = $res['refresh_token'];
        $result['token'] = Users::wxCreateToken($result['unionid'], $result['openid'], 'wxchat');
        result($result);
    }

    public function wxdevgetuser() {
        $code = $this->request->get('code');
        $result = $this->wxDevGetUserinfo($code);
        $result['token'] = Users::wxCreateToken($result['unionid'], $result['openid'], 'wxdev');
        result($result);
    }

    public function wxappgetuser() {
        $unionid = $this->request->get('unionid');
        $openid = $this->request->get('openid');
        $result['token'] = Users::wxCreateToken($unionid, $openid, 'wxapp');
        result($result);
    }

    public function wxChatGetUserinfo($access_token, $openid) {
        $url = "https://api.weixin.qq.com/sns/userinfo?access_token={$access_token}&openid={$openid}&lang=zh_CN";
        return json_decode(file_get_contents($url), true);
    }

    public function wxDevGetUserinfo($code) {
        $config = config('wx.dev');
        $url = "https://api.weixin.qq.com/sns/jscode2session?appid={$config['APPID']}&secret={$config['APPSECRET']}&js_code={$code}&grant_type=authorization_code";
        return json_decode(file_get_contents($url), true);
    }

    /**
     * @Apidoc\Title("找回密码验证手机号")
     * @Apidoc\Desc("接口地址")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("找回密码验证手机号")
     * @Apidoc\Param("phone", type="string",require=true, default="",desc="手机号" )
     * @Apidoc\Param("code", type="string",require=true,default="",desc="短信验证码" )
     * @Apidoc\Returned("key", type="string", desc="秘钥（15分钟有效），设置新密码使用")
     */
    function forget() {
        $phone = $this->request->param('phone');
        $code = $this->request->param('code');
        $check = Smss::checksms($phone, $code, 2);
        if (!$check) {
            result('', 1000, '验证码错误');
        }
        $user = Smss::isphone($phone);
        if ($user['status'] == 1) {
            $key = md5('forget' . $phone . time());
            cache($key, ['phone' => $phone, 'id' => $user['id']], ['expire' => 900]);
            result(['key' => $key]);
        } else if (!empty($user['id'])) {
            result('', 1002, '您的账号已被冻结');
        } else {
            result('', 1003, '账号未注册');
        }
    }

    /**
     * @Apidoc\Title("找回密码设置新密码")
     * @Apidoc\Desc("接口地址")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("找回密码设置新密码")
     * @Apidoc\Param("key", type="string",require=true, default="",desc="秘钥" )
     * @Apidoc\Param("password", type="string",require=true,default="",desc="新密码" )
     * @Apidoc\Param("repassword", type="string",require=true,default="",desc="重读新密码" )
     */
    function reset() {
        $password = $this->request->param('password');
        $repassword = $this->request->param('repassword');
        $key = $this->request->param('key');
        $user = cache($key);
        if (empty($user)) {
            result('', 1005, '验证超时，请重试');
        }
        $this->checkpassword($password, $repassword);
        $re = Users::upPassword($user['id'], $password);
        result('');
    }

    function checkpassword($password, $repassword) {
        if (empty($password)) {
            result('', 1009, "密码不能为空！");
        }
        if ($password !== $repassword) {
            result('', 1009, "两次输入的密码不一致！");
        }
        return $password;
    }

    /**
     * @Apidoc\Title("微信登陆绑定手机号")
     * @Apidoc\Desc("接口地址")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("微信登陆绑定手机号")
     * @Apidoc\Param("phone", type="string",require=true, desc="手机号" )
     * @Apidoc\Param("code", type="string",require=true,desc="短信验证码" )
     * @Apidoc\Param("unionid", type="string",require=true,desc="unionid" )
     * @Apidoc\Param("openid", type="string",desc="openid" )
     * @Apidoc\Param("sex", type="string",desc="性别{男，女}" )
     * @Apidoc\Param("head", type="string",desc="头像" )
     * @Apidoc\Param("usercode", type="string",desc="邀请码" )
     * @Apidoc\Param("type", type="string",desc="{wxchat：微信公众号，wxdev：小程序，wxapp：安卓app}" )
     * @Apidoc\Returned("token", type="string", desc="token")
     */
    function bind() {
        $data = $this->request->param();
        Verify::userCheck('bind', $data); //验证
        $check = Smss::checksms($data['phone'], $data['code'], 4);
        if (!$check) {
            result('', 1000, '验证码错误');
        }
        $user = Users::isphone($data['phone']);
        if (empty($user)) {
            if (!empty($data['usercode'])) {
                $referrer = db('user')->where(['code' => $data['usercode'], 'del' => 0])->field('id')->find();
                if (empty($referrer)) {
                    result('', 1002, '请确认邀请码是否正确');
                }
            }
            $unionid = db('user_openid')->where(['openid' => $data['unionid'], 'type' => 'unionid'])->field('user_id')->find();
            if (!empty($unionid)) {
                result('', 1005, '该微信已绑定账号');
            }
            if (!empty($data['openid'])) {
                $unionid = db('user_openid')->where(['openid' => $data['openid'], 'type' => $data['type']])->field('user_id')->find();
                if (!empty($unionid)) {
                    unset($data['openid']);
                }
            }
            $data['ip'] = get_ip();
            $data['referrer'] = $referrer['id'];
            $result = Users::register($data);
            result($result);
        } else {
            $binds = Users::binds($user['id']);
            if (!empty($binds['unionid'])) {
                result('', 1005, '该账号已绑定微信');
            }
            $result = Users::bind($user['id'], $data['unionid']);
            $result['token'] = Users::wxCreateToken($result['unionid'], $result['openid'], 'wxchat');
            result($result);
        }
    }

}
