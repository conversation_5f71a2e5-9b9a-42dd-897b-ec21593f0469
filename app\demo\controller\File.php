<?php

namespace app\demo\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use hg\apidoc\annotation as Apidoc;
use app\BaseController;
use app\model\FileModel;
use app\model\ExcelModel;
use think\facade\Db;

/**
 * @Apidoc\Title("文件管理示例参考")
 * @Apidoc\Group("Demo")
 * @Apidoc\Sort(1)
 */
class File extends BaseController {

    /**
     * @Apidoc\Title("上传文件到临时文件夹")
     * @Apidoc\Param("file", type="string",default="", desc="文件")
     * @Apidoc\Param("model", type="string",default="", desc="上传模块区分{为空则默认为：控制器名_方法名}")
     * @Apidoc\Returned("id", type="object", desc="文件id")
     * @Apidoc\Returned("filepath", type="int", desc="文件路径")
     */
    public function upload($model='company') {
        $file = request()->file('file');
        $result = FileModel::upload($file,$model);
        result($result);
    }

    /**
     * @Apidoc\Title("保存文件到正式文件夹")
     * @Apidoc\Param("file", type="string",default="", desc="文件code或文件路径")
     * @Apidoc\Param("folder", type="string",default="", desc="正式文件夹目录")
     * @Apidoc\Param("filename", type="string",default="", desc="指定文件名，为空则随机生成")
     * @Apidoc\Returned("id", type="object", desc="文件id")
     * @Apidoc\Returned("filepath", type="int", desc="文件路径")
     */
    public function saveFile() {
        $file = '0b77af5d346f9fdab5a5fabeb8acb6c8';
        $folder = 'company/info';
        $filename = '';
        $result = FileModel::saveFile($file,$folder,$filename);
        result($result);
    }

    /**
     * @Apidoc\Title("word转pdf")
     * @Apidoc\Param("path", type="string",default="", desc="word文件路径")
     * @Apidoc\Param("path2", type="string",default="", desc="保存pdf文件路径")
     * @Apidoc\Returned("id", type="object", desc="文件id")
     * @Apidoc\Returned("filepath", type="int", desc="文件路径")
     */
    public function wordToPdf() {
        $path = '/opt/tdoa/webroot/general/toppingsoft/se.doc';
        $path2 = '/opt/tdoa/webroot/general/toppingsoft/public/storage/se.pdf';
        $result = FileModel::wordToPdf($path,$path2);
        result($result);
    }

    /**
     * @Apidoc\Title("excel导出")
     * @Apidoc\Param("title", type="array",default="", desc="表头")
     * @Apidoc\Param("results", type="array",default="", desc="数据")
     * @Apidoc\Param("fileName", type="string",default="", desc="文件名称")
     * @Apidoc\Param("sort", type="boolean",default="false", desc="是否添加序号")
     */
    public function export() {
        /*简单表格start*/
        $title = [
            ['title' => '序号','field' => 'x'],
            ['title' => '序号1','field' => 'y'],
            ['title' => '序号2','field' => 'x'],
            ['title' => '序号3','field' => 'y'],
            ['title' => '序号4','field' => 'x'],
            ['title' => '序号5','field' => 'y'],
            ['title' => '序号6','field' => 'x'],
            ['title' => '序号7','field' => 'y'],
            ['title' => '序号8','field' => 'x'],
        ];
        $results = [
            ['x'=>1,'y'=>'测试1'],
            ['x'=>2,'y'=>'测试2'],
            ['x'=>3,'y'=>'测试3'],
            ['x'=>4,'y'=>'测试4'],
            ['x'=>5,'y'=>'测试5'],
            ['x'=>6,'y'=>'测试6'],
            ['x'=>7,'y'=>'测试7'],
        ];
        /*简单表格end*/
        /*复杂表格start*/
        $title = [
            ['title' => '姓名','type' => 'string','field' => 'name','width' => '15',],
            [
                'title' => '照片', //表头标题
                'type' => 'image', //类型{string：单元格为文本类型,image：插入图片}
                'field' => 'photo',//对应字段
                'width' => '20',//列宽
                'height' => '120',//内容行高
                'imgwidth' => '100',//图片宽度（仅type为image时有效）
                'imgheight' => '140'//图片高度（仅type为image时有效）
            ],
            ['title' => '家庭成员', 'type' => 'string', 'children' => [
                    ['title' => '姓名','type' => 'string','field' => 'name','width' => '15',],
                    ['title' => '照片','type' => 'image','field' => 'photo','width' => '20','height' => '120','imgwidth' => '100',],
                    ['title' => '来访记录', 'type' => 'string', 'children' => [
                            ['title' => '来访时间','type' => 'string','field' => 'id1','width' => '15',],
                            ['title' => '人数','type' => 'string','field' => 'id2','width' => '15',],]
                    ]
                ],
            ],
            ['title' => '照片','field' => 'name','width' => '20','height' => '120','imgwidth' => '100','imgheight' => '140',],
            ['title' => '家庭成员', 'type' => 'string', 'children' => [
                    ['title' => '姓名','type' => 'string','field' => 'name','width' => '15',],
                    ['title' => '姓名','type' => 'string','field' => 'name','width' => '15',],],
            ],
        ];
        $results = [
            ['name' => '张三', 'photo' => '/storage/Userinfo/avatar/20220527172454.png', 'children' => [
                    ['name' => '李四', 'photo' => '/storage/Userinfo/avatar/20220527172454.png', 'children' => [
                            ['id1' => '42','id2' => '452',],
                            ['id1' => '459','id2' => '412',]],
                    ],
                    ['name' => '李四', 'photo' => '/storage/Userinfo/avatar/20220527172454.png', 'children' => [
                            ['id1' => '45943823923412', 'id2' => '4594123412',]],
                    ],
                    ['name' => '李四', 'photo' => '/storage/Userinfo/avatar/20220527172454.png', 'children' => [
                            ['id1' => '42','id2' => '452',],
                            ['id1' => '459','id2' => '412',]],
                    ],
                ]
            ],
            ['name' => '张三', 'photo' => '/storage/Userinfo/avatar/20220527172454.png', 'children' => [
                    ['name' => '李四', 'photo' => '/storage/Userinfo/avatar/20220527172454.png', 'children' => [],],
                    ['name' => '李四', 'photo' => '/storage/Userinfo/avatar/20220527172454.png',]]
            ]
        ];
        /*复杂表格end*/
        //$results = [];
        //print_r(count($a[3]));die;
        ExcelModel::exportExcel($title, $results, '人员档案',true);
    }

    public function test(){
        exit('err');
        $pcas = get_pcas_all(2);
        print_r($pcas);
        foreach($pcas as $k=>$v){
            Db::table('top_pcas')->where(['code'=>$k])->update(['names'=>$v]);
        }
    }


}
