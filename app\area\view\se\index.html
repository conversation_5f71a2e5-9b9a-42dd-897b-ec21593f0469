<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>企业自评确认</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/company.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .my-content p{ line-height:20px; margin: 10px 0;}
        .iframe-container {
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            overflow: hidden;
        }
        .iframe-container iframe {
            display: block;
            border: none;
        }
        .dialog-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }
        .dialog-title span {
            font-size: 16px;
            font-weight: 500;
        }
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">
        <el-form :inline="true" :model="searchFrom" class="form-inline" @submit.native.prevent>
            <el-form-item>
                <el-radio-group @change="statusChange" size="mini" v-model="searchFrom.confirm_status">
                    <el-radio-button label="">全部</el-radio-button>
                    <el-radio-button label="0">待确认</el-radio-button>
                    <el-radio-button label="1">已确认</el-radio-button>
                </el-radio-group>
            </el-form-item>
            <el-form-item>
                <el-input v-model="searchFrom.title" size="mini" placeholder="企业名称"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="getData()" size="mini">查询</el-button>
                <el-button @click="reset()" size="mini">重置</el-button>
            </el-form-item>
        </el-form>
        <el-table border
                  v-loading="loading"
                  :data="data"
                  style="width: 100%;margin-bottom: 20px;"
                  ref="qtable"
                  :height="height"
                  size="small">
            <el-table-column
                    type="index"
                    label="序号"
                    align="center"
                    width="50">
            </el-table-column>
            <el-table-column
                    prop="year"
                    label="年度"
                    align="center"
                    width="100">
            </el-table-column>
            <el-table-column
                    prop="company_name"
                    label="企业名称"
                    align="center"
                    show-overflow-tooltip
                    min-width="200">
            </el-table-column>
            <el-table-column
                    prop="industry"
                    label="行业/专业"
                    align="center"
                    show-overflow-tooltip
                    min-width="100">
                <template slot-scope="scope">
                    {{scope.row.industry}}/{{scope.row.specialty}}
                </template>
            </el-table-column>
            <el-table-column
                    prop="date"
                    label="自评日期"
                    align="center"
                    show-overflow-tooltip
                    min-width="100">
            </el-table-column>
            <el-table-column
                    prop="score.total.score"
                    label="自评得分"
                    align="center"
                    min-width="100">
            </el-table-column>
            <el-table-column
                    prop="mb_status"
                    label="状态"
                    align="center"
                    show-overflow-tooltip
                    width="120">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.status==1" type="info">自评进行中</el-tag>
                    <el-tag v-if="scope.row.status==2" type="primary">待审核</el-tag>
                    <el-tag v-if="scope.row.status==7" type="success">审核完成</el-tag>
                    <el-tag v-if="scope.row.status==5" type="danger">已驳回</el-tag>
                </template>
            </el-table-column>
            <el-table-column
                    prop="files"
                    label="自评报告"
                    align="center"
                    show-overflow-tooltip
                    width="120">
                <template slot-scope="scope">
                    <el-tag @click="preview(scope.row.files)" type="">查看</el-tag>
                </template>
            </el-table-column>
            <el-table-column
                    prop="area_confirm_time"
                    label="确认时间"
                    align="center"
                    width="150">
            </el-table-column>
            <el-table-column
                    prop="area_confirm_user"
                    label="确认人"
                    align="center"
                    width="120">
            </el-table-column>
            <el-table-column
                    label="操作"
                    align="center"
                    width="150">
                <template slot-scope="scope">
                    <el-button v-if="scope.row.area_confirm_status==0" size="small" type="primary" @click="confirm(scope.row)">确认</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
    <!-- 确认对话框 -->
    <el-dialog title="确认企业自评" :visible.sync="confirmDialog" width="500px">
        <el-form :model="confirmForm" label-width="100px">
            <el-form-item label="企业名称">
                <span>{{confirmForm.company_name}}</span>
            </el-form-item>
            <el-form-item label="自评年度">
                <span>{{confirmForm.year}}</span>
            </el-form-item>
            <el-form-item label="自评得分">
                <span>{{confirmForm.score.total.score}}</span>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="confirmDialog = false">取 消</el-button>
            <el-button type="primary" @click="submitConfirm">确定接收</el-button>
        </div>
    </el-dialog>
    <!-- 企业详情弹框 -->
    <el-dialog 
        title="企业详情" 
        :visible.sync="detailDialog" 
        :width="isMobile ? '95%' : '90%'" 
        :before-close="closeDetailDialog" 
        top="5vh"
        :close-on-click-modal="false">
        <div slot="title" class="dialog-title">
            <span>企业详情</span>
            <el-button @click="openFullscreen" icon="el-icon-full-screen" size="mini" type="text" title="在新窗口中全屏查看">全屏查看</el-button>
        </div>
        <div v-loading="iframeLoading" element-loading-text="正在加载企业详情..." class="iframe-container" :style="'height: ' + (isMobile ? '60vh' : '70vh')">
            <iframe 
                ref="detailFrame"
                :src="detailUrl" 
                width="100%" 
                height="100%" 
                frameborder="0"
                v-show="!iframeLoading"
                @load="iframeLoaded">
            </iframe>
        </div>
        <div slot="footer" class="dialog-footer">
            <el-button @click="detailDialog = false">关 闭</el-button>
        </div>
    </el-dialog>
    <preview ref="preview"></preview>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        components:{
            'preview': 'url:/general/toppingsoft/public/vue/preview.vue',
        },
        data() {
            return {
                title: '企业自评确认',
                searchFrom: {
                    confirm_status: '',
                    title: '',
                },
                data: [],
                loading: false,
                height: document.documentElement.clientHeight - 155,
                confirmDialog: false,
                confirmForm: {
                    id: '',
                    company_name: '',
                    score: {
                        total: {
                            score:''
                        },
                    },
                    remark: ''
                },
                detailDialog: false,
                detailUrl: '',
                iframeLoading: false,
                fullscreen: false,
                isMobile: false
            };
        },
        watch: {
            $route() {
                //切换标签时
                this.handleRefreshTable();
            }
        },
        created() {
            let _this = this;
            //窗口改变时
            window.onresize = function () {
                _this.handleRefreshTable();
            };
        },
        updated() {
            //数据改变时
            this.handleRefreshTable();
        },
        methods: {
            statusChange() {
                this.getData();
            },
            //数据初始化
            reset() {
                this.searchFrom.title='';
                this.searchFrom.confirm_status='';
                this.getData();
            },
            preview: function (file) {
                file = file.response?file.response.data:file;
                this.$refs.preview.open(file.url,file.name);
            },
            //数据加载
            getData() {
                var _this = this;
                var param = _this.searchFrom;
                param._ajax = 1;
                _this.loading = true;
                axios.post('', param).then(function (res) {
                    if (res.data.code == 0) {
                        _this.data = res.data.data.data;
                        _this.$nextTick(() => {
                            _this.loading = false;
                        });
                        _this.handleRefreshTable();
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        _this.loading = false;
                    }
                }).catch(function (error) {
                    console.log(error);
                    _this.loading = false;
                });
            },
            confirm(row) {
                this.confirmForm = row;
                this.confirmDialog = true;
            },
            submitConfirm() {
                var _this = this;
                if (!this.confirmForm.id) {
                    this.$message.error('参数错误');
                    return;
                }
                
                this.loading = true;
                axios.post('confirm', {
                    id: this.confirmForm.id,
                    remark: this.confirmForm.remark,
                    _ajax:1
                }).then(function (res) {
                    _this.loading = false;
                    if (res.data.code == 0) {
                        _this.$message({
                            message: res.data.msg,
                            type: "success"
                        });
                        _this.confirmDialog = false;
                        _this.getData();
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                    }
                }).catch(function (error) {
                    console.log(error);
                    _this.loading = false;
                });
            },
            viewDetail(row) {
                // 在弹框中加载企业详情页面
                this.detailUrl = '/general/toppingsoft/index.php/area/company/info?id=' + row.company_id;
                this.detailDialog = true;
                this.iframeLoading = true;
                
                // 设置加载超时
                const loadTimeout = setTimeout(() => {
                    this.iframeLoading = false;
                }, 10000); // 10秒后自动隐藏加载状态
                
                // 监听iframe加载完成
                this.$nextTick(() => {
                    const iframe = this.$refs.detailFrame;
                    if (iframe) {
                        iframe.onload = () => {
                            clearTimeout(loadTimeout);
                            this.iframeLoading = false;
                        };
                        iframe.onerror = () => {
                            clearTimeout(loadTimeout);
                            this.iframeLoading = false;
                            this.$message.error('页面加载失败，请稍后重试');
                        };
                    }
                });
            },
            iframeLoaded() {
                this.iframeLoading = false;
            },
            closeDetailDialog() {
                this.detailDialog = false;
                this.fullscreen = false;
                // 延时清空URL，避免弹框关闭时闪烁
                setTimeout(() => {
                    this.detailUrl = '';
                    this.iframeLoading = false;
                }, 300);
            },
            openFullscreen() {
                // 在新窗口中打开全屏查看
                window.open(this.detailUrl, '_blank', 'width=' + screen.width + ',height=' + screen.height + ',scrollbars=yes');
            },
            handleRefreshTable: function () {
                this.$nextTick(function () {
                    var refList = this.$refs;
                    if (refList) {
                        for (var i of Object.keys(refList)) {
                            if (refList[i] && refList[i].doLayout) {
                                refList[i].doLayout();
                            }
                        }
                    }
                });
            },
        },
        mounted() {
            //获取列表
            this.getData();
            
            // 检测是否为移动设备
            this.isMobile = window.innerWidth <= 768;
            
            // 监听窗口大小变化
            window.addEventListener('resize', () => {
                this.isMobile = window.innerWidth <= 768;
            });
        }
    })
</script>

</body>
</html>