{"name": "topthink/think", "description": "the new thinkphp framework", "type": "project", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "http://thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=7.1.0", "topthink/framework": "^6.0.0", "topthink/think-filesystem": "2.0.2", "topthink/think-orm": "^2.0", "hg/apidoc": "^5.0", "topthink/think-view": "^1.0", "liliuwei/thinkphp-jump": "^1.5", "phpoffice/phpspreadsheet": "^1.5", "phpoffice/phpexcel": "^1.8", "phpoffice/phpword": "^1.0", "topthink/think-multi-app": "^1.0", "catfan/medoo": "^1.7", "endroid/qr-code": "^3.9", "firebase/php-jwt": "^6.4", "mpdf/mpdf": "^8.1", "tecnickcom/tcpdf": "^6.6", "overtrue/pinyin": "^4.1", "topthink/think-worker": "^1.0", "workerman/gatewayclient": "^3.0", "topthink/think-queue": "^3.0", "workerman/gateway-worker": "^3.0", "workerman/crontab": "^1.0", "textalk/websocket": "^1.5", "erusev/parsedown": "^1.7", "dompdf/dompdf": "^2.0", "gregwar/captcha": "^1.2", "knplabs/knp-snappy": "^1.4", "yangshuanlin/php-html2img": "^1.0"}, "require-dev": {"symfony/var-dumper": "^4.2", "topthink/think-trace": "^1.0"}, "autoload": {"psr-4": {"app\\": "app"}, "psr-0": {"": "extend/"}}, "config": {"preferred-install": "dist", "allow-plugins": {"topthink/think-installer": true}}, "scripts": {"post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"]}}