<?php
declare (strict_types = 1);

namespace app\admin\model;

use think\Model;

/**
 * @mixin \think\Model
 */
class TopCityUserModel extends Model
{
	protected $field = array(
		'username',
		'password',
		'email',
		'mobile',
		'status',
		'reg_time',
		'reg_ip',
		'city_id',
		'department',
		'name',
		'salt',
		'role'
	);

	public $validate = array(
		'email' => array('pattern' => '/^([a-zA-Z0-9]+[-_\.]?)+@[a-zA-Z0-9]+\.[a-z]+$/', 'msg' => '邮箱地址无效'),
		'password' => array('pattern' => '/^(?![\d]+$)(?![a-zA-Z]+$)(?![^\da-zA-Z]+$)([^\u4e00-\u9fa5\s]){8,20}$/', 'msg' => '密码为8-20位大/小写字母+数字+特殊字符'),
		'mobile' => array('pattern' => '/^((0\d{2,3}-\d{7,8})|(1[34578]\d{9}))$/', 'msg' => '手机号无效'),
		'username' => array('pattern' => '/^[_a-zA-Z0-9]{4,20}$/', 'msg' => '用户名为4-20位中英文/数字/下划线'),
	);


	public $department_arr = array(
		array(
			'label' => '基础科',
			'value' => 1
		),
		array(
			'label' => '危化科',
			'value' => 2
		),
		array(
			'label' => '商务楼宇',
			'value' => 3
		),
	);

	public $role_arr = array(
		array(
			'label' => '普通角色',
			'value' => 0
		),
		array(
			'label' => '管理员',
			'value' => 1
		),
		array(
			'label' => '超级管理员',
			'value' => 2
		),
	);

	public $table = 'top_city_user';


	public $sessionName = 'city';

	public function city()
	{
		return $this->belongsTo(TopCityModel::class, 'city_id', 'id');
	}

	/**
	 * 保存数据
	 * @param $model
	 * @param $data
	 * @param int $id
	 * @return string
	 */
	public function newSave($model, $data, $id = 0)
	{
		$check = $this->checkName($data, intval($id));
		if ($check) {

			if (!$id) { // 新增
				$salt = uniqid();
				$data['password'] = crypt($data['password'], $salt);
				$data['salt'] = $salt;
				$data['reg_ip'] = get_ip();
				$data['reg_time'] = date('Y-m-d H:i:s');
			}
			if ($model->allowField($this->field)->save($data)) {
				return '数据处理成功';
			} else {
				return '数据处理失败';
			}
		}
		return '用户名/手机号/邮箱已注册';
	}

	/**
	 * 检测名称是否重复
	 * @param $data
	 * @param int $id
	 * @return bool
	 */
	public function checkName($data, $id = 0)
	{
		if ($id) $where[] = ['id', '<>', $id];
		if ($data['username']) $whereOr[] = " username='".$data['username']."' ";
		if ($data['email']) $whereOr[] = " email='".$data['email']."'";
		if ($data['mobile']) $whereOr[] = " mobile='".$data['mobile']."'";
		if (!empty($whereOr)){
			$whereOr = implode(' OR ', $whereOr);
		}
		$where[] = ['status', '<>', 2];
		$data = $this->whereOr($whereOr)->where($where)->find();
		return !$data;
	}

	/**SELECT * FROM top_city_user WHERE (( username = 'lisi' ) OR ( email = '<EMAIL>' ) OR ( mobile = '13500000000' )) AND  id <> 7 AND status <> 2
	 * 获取列表数据
	 * @param $where
	 * @param int $page
	 * @param int $limit
	 * @param bool $export
	 * @return mixed
	 */
	public function getList($where, $page = 1, $limit = 10, $export = false)
	{
		$whereArr[] = ['status', '<>', 2];
		if (isset($where['username']) && $where['username'] != '') {
			$whereArr[] = ['username', 'like', "%" . $where['username'] . "%"];
		}
		if (isset($where['name']) && $where['name'] != '') {
			$whereArr[] = ['name', 'like', "%" . $where['name'] . "%"];
		}
		if (isset($where['mobile']) && $where['mobile'] != '') {
			$whereArr[] = ['mobile', 'like', "%" . $where['mobile'] . "%"];
		}
		if (isset($where['email']) && $where['email'] != '') {
			$whereArr[] = ['email', 'like', "%" . $where['email'] . "%"];
		}
		if (isset($where['city_id']) && $where['city_id'] > 0) {
			$whereArr[] = ['city_id', '=', $where['city_id']];
		}
		if ($export) { // 导出标记
			$data = $this->where($whereArr)->select()->each(function ($item, $index) {
				$item = $this->getInfo($item);
				return $item;
			})->toArray();
			return $data;
		}
		$data = $this->where($whereArr)->paginate($limit)->each(function ($item, $index) {
			$item = $this->getInfo($item);
			return $item;
		});
		return $data;
	}

	public function getInfo($itemModel, &$model = null)
	{
		$itemModel->statusStr = $this->getStatusStr($itemModel->status);
		$itemModel->deptName = $this->getDeptName($itemModel->department);
		$itemModel->roleName = $this->getRoleName($itemModel->role);
		$itemModel->cityName = $itemModel->city ? $itemModel->city->toArray()['name'] : "";
		$itemModel->reg_time = date('Y-m-d H:i:s', strtotime($itemModel->reg_time));
		return $itemModel;
	}

	public function getDeptName($id)
	{
		$d_arr = $this->department_arr;
		$str = '';
		foreach ($d_arr as $v) {
			if ($v['value'] == $id) $str = $v['label'];
		}
		return $str;
	}

	public function getRoleName($id)
	{
		$r_arr = $this->role_arr;
		$str = '';
		foreach ($r_arr as $v) {
			if ($v['value'] == $id) $str = $v['label'];
		}
		return $str;
	}

	/**
	 * 获取状态
	 * @param string $status
	 * @return string
	 */
	public function getStatusStr($status = '')
	{
		$str = '';
		switch ($status) {
			case 0:
				$str = '未使用';
				break;
			case 1:
				$str = '正常';
				break;
			case 3:
				$str = '未审核';
				break;
			case 4:
				$str = '未通过';
				break;
			default:
				break;
		}
		return $str;
	}

	/**
	 * 删除
	 * @param $model
	 * @return string
	 */
	public function delData($model)
	{
		$data = array('status' => 2);
		if ($model->save($data)) {
			return '数据处理成功';
		}
		return '数据处理失败';
	}

	/**
	 * 获取表头字段
	 * @return array
	 */
	public function getTitle()
	{
		$title = [
			['title' => '姓名', 'field' => 'name', 'width' => '30', 'type' => 'string'],
			['title' => '用户名', 'field' => 'username', 'width' => '30', 'type' => 'string'],
			['title' => '电话', 'field' => 'mobile', 'width' => '30', 'type' => 'string'],
			['title' => '邮箱', 'field' => 'email', 'width' => '30', 'type' => 'string'],
			['title' => '注册时间', 'field' => 'reg_time', 'width' => '60', 'type' => 'string'],
			['title' => '注册IP', 'field' => 'reg_ip', 'width' => '60', 'type' => 'string'],
			['title' => '所属应急局', 'field' => 'cityName', 'width' => '60', 'type' => 'string'],
			['title' => '部门', 'field' => 'deptName', 'width' => '60', 'type' => 'string'],
			['title' => '角色', 'field' => 'roleName', 'width' => '60', 'type' => 'string'],
//			['title' => '密码盐值', 'field' => 'salt', 'width' => '60', 'type' => 'string'],

		];
		return $title;
	}

	/**
	 * 导入数据
	 * @param $data
	 * @param $dataHeader
	 * @return mixed
	 */
	public function importData($data, $dataHeader)
	{
		$fields = $this->getTitle();
		foreach ($dataHeader as $k => $v) {
			foreach ($fields as $k1 => $v1) {
				if ($v1['title'] == $v) {
					$tit[$k] = $v1['field'];
				}
			}
		}
		foreach ($data as $k => $v) {
			$tmp = [];
			foreach ($v as $k1 => $v1) {
				$tmp[$tit[$k1]] = str_replace(' ', '', $v1);
			}
			$dataList[] = $tmp;
		}
		foreach ($dataList as $v) {
			if (!empty($v['name'])) {
				$datauser[] = $v;
			}
		}

		foreach ($datauser as $k => $v) {
			$v['status'] = 1;
			$v['password'] = '';
			$validateEmail = $this->validateStr('email', $v['email']);
			if (true !== $validateEmail) {
				$result['fail'][] = ['row' => $k + 2, 'name' => $v['name'], 'msg' => '第' . ($k + 2) . '行,' . $validateEmail];
				continue;
			}
			$validateUsername = $this->validateStr('username', $v['username']);
			if (true !== $validateUsername) {
				$result['fail'][] = ['row' => $k + 2, 'name' => $v['name'], 'msg' => '第' . ($k + 2) . '行,' . $validateUsername];
				continue;
			}
			$validateMobile = $this->validateStr('mobile', $v['mobile']);
			if (true !== $validateMobile) {
				$result['fail'][] = ['row' => $k + 2, 'name' => $v['name'], 'msg' => '第' . ($k + 2) . '行,' . $validateMobile];
				continue;
			}
			$cityModel = new TopCityModel();
			$city = $cityModel->where('name', $v['cityName'])->find();
			if (!$city) {
				$result['fail'][] = ['row' => $k + 2, 'name' => $v['name'], 'msg' => '第' . ($k + 2) . '行,所属应急局无法匹配'];
				continue;
			}
			$v['city_id'] = $city->id;
			$v['department'] = $this->getDeptId($v['deptName']);
			$v['role'] = $this->getRoleId($v['roleName']);
			$re = $this->newSave(new self(), $v);
			if ($re == '数据处理成功') {
				$result['success'][] = ['row' => $k + 2, 'name' => $v['name'], 'msg' => '数据处理成功'];
			} else if ($re == '用户名/手机号/邮箱已注册') {
				$result['repeat'][] = ['row' => $k + 2, 'name' => $v['name'], 'msg' => '重复'];
			} else {
				$result['fail'][] = ['row' => $k + 2, 'name' => $v['name'], 'msg' => $re];
			}
		}
		return $result;
	}

	/**
	 * 数据验证
	 * @param $field
	 * @param $string
	 * @return bool
	 */
	public function validateStr($field, $string)
	{
		if (!preg_match($this->validate[$field]['pattern'], $string))
			return $this->validate[$field]['msg'];
		return true;
	}

	/**
	 * 获取部门id
	 * @param $name
	 * @return int|mixed
	 */
	public function getDeptId($name)
	{
		$dept_arr = $this->department_arr;
		$id = 0;
		foreach ($dept_arr as $v) {
			if ($v['label'] == $name) $id = $v['value'];
		}
		return $id;
	}

	/**
	 * 获取角色id
	 * @param $name
	 * @return int|mixed
	 */
	public function getRoleId($name)
	{
		$role_arr = $this->role_arr;
		$id = 0;
		foreach ($role_arr as $v) {
			if ($v['label'] == $name) $id = $v['value'];
		}
		return $id;
	}

	/**
	 * 修改密码
	 * @param $model
	 * @param $password
	 * @return string
	 */
	public function updatePassword($model, $password)
	{

//		$old_password = $model->password;
//		if($old_password === crypt($password, $old_password)){
//			return '请输入和旧密码不同的密码';
//		}
		$salt = uniqid();
		$model->password = crypt($password, $salt);
		$model->salt = $salt;
		if ($model->save()) {
			return '数据处理成功';
		}
		return '数据处理失败';
	}


	public function checkUser($model, $status){
		$model->status = $status;
		if ($model->save()) {
			return '数据处理成功';
		}
		return '数据处理失败';
	}
}
