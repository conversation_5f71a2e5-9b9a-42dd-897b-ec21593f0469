
<template>
  <el-dialog :title="title" :close-on-click-modal="false" :visible.sync="visible" width="1000px" top="10px" @close="refresh()" :append-to-body="true" label-position="top">

    <div class="centainer">
      <el-form :inline="true" :model="searchFrom" class="form-inline" @submit.native.prevent>
        <el-form-item>
          <el-input v-model="searchFrom.user_name" size="mini" placeholder="姓名"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getData()" size="mini">查询</el-button>
          <el-button @click="refresh()" size="mini">刷新</el-button>
        </el-form-item>
        <el-form-item style="float: right">
          <!--<el-button :loading="loading" type="success" size="mini" @click="add">添加</el-button>
          <el-button :loading="loading" type="primary" size="mini" @click="export1">导出</el-button>
          <el-button :loading="loading" type="primary"  size="mini" @click="import1">导入</el-button>-->
        </el-form-item>
      </el-form>
      <el-table border
                v-loading="loading"
                :data="data"
                style="width: 100%;margin-bottom: 20px;"
                ref="qtable"
                :height="height"
                size="small">
        <el-table-column
            type="index"
            label="序号"
            align="center"
            width="50">
        </el-table-column>
        <el-table-column
            prop="user_name"
            label="姓名"
            align="center"
            show-overflow-tooltip
            width="150">
        </el-table-column>
        <el-table-column
            prop="position_name"
            label="职务"
            align="center"
            min-width="180">
        </el-table-column>
        <el-table-column
            prop="start_date"
            label="考试开始时间"
            align="center"
            width="150">
        </el-table-column>
        <el-table-column
            prop="end_date"
            label="考试结束时间"
            align="center"
            show-overflow-tooltip
            width="150">
        </el-table-column>
        <el-table-column
            prop="total_score"
            label="得分"
            align="center"
            width="100">
        </el-table-column>
        <el-table-column
            label="操作"
            align="center"
            width="120">
          <template slot-scope="scope">
            <el-button @click="del(scope.row)" type="danger" size="mini">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--分页条total, sizes, prev, pager, next, jumper-->
      <div class="block">
        <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="page"
            :page-sizes="[10, 20, 50, 100, 500, 1000]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total">
        </el-pagination>
      </div>
    </div>
  </el-dialog>
</template>
<script>
module.exports = {
  name: "add",
  // 模板导入区
  components: {
  },
  data:function() {
    return {
      id:0,
      activeName:'a',
      type:1,
      visible: false,
      dialogVisible: false,
      dialogImageUrl: '',
      title: '考试记录',
      loading: false,
      searchFrom: {
        id: 0,
        user_name: '',
      },
      data: [],
      page: 1,
      pageSize: 20,
      total: 20,
      height: document.documentElement.clientHeight - 355,
    }
  },
  methods: {
    /**
     * 打开弹窗调用方法
     * */
    open: function (id) {
      var _this =this;
      _this.visible = true;
      _this.searchFrom.id = id;
      _this.getData(id);
    },
    handleSizeChange: function (val) {
      this.pageSize = val;
      this.getData();
    },
    handleCurrentChange: function (val) {
      this.page = val;
      this.getData();
    },
    refresh: function () {
      this.page = 1;
      this.searchFrom.user_name = '';
      this.getData();
    },
    del(row){
      var _this = this;
      _this.$confirm('确定删除该考试?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _this.loading = true;
        axios.post('examDel',{id:row.id}).then(res => {
          _this.loading = false
          if(res.data.code === 0){
            _this.getData();
          }else{
            _this.$message.error(res.data.msg);
          }
        })
      }).catch(() => {
        /*this.$message({
            type: 'info',
            message: '已取消删除'
        });*/
      });
    },
    //数据加载
    getData() {
      var _this = this;
      var param = _this.searchFrom;
      param._ajax = 1;
      param.page = _this.page;
      param.limit = _this.pageSize;
      axios.post('exam', param).then(function (res) {
        if (res.data.code == 0) {
          _this.data = res.data.data.data;
          _this.page = res.data.data.current_page;
          _this.pageSize = res.data.data.per_page;
          _this.total = res.data.data.total;
          _this.$nextTick(() => {
            // 这里的代码会在DOM更新完成后执行
            _this.loading = false;
          });
        } else {
          _this.$message({
            message: res.data.msg,
            type: "error"
          });
          return false;
        }
      }).catch(function (error) {
        console.log(error);
      });
    },
    handleClick(tab, event) {
      console.log(tab, event);
    },
    closeDialog: function () {
      this.visible = false;
    },

  }
}
</script>


