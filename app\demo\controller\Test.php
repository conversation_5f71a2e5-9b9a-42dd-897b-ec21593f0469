<?php

namespace app\demo\controller;

// 添加这句，注释写法为 @Apidoc\参数名(...)
use hg\apidoc\annotation as Apidoc;
use app\BaseController;
use Mpdf\Mpdf;
use PhpOffice\PhpWord\IOFactory;
use think\facade\Db;
use GatewayClient\Gateway;
use PhpOffice\PhpWord\TemplateProcessor;

/**
 * @Apidoc\Title("调试接口")
 * @Apidoc\Group("Test")
 * @Apidoc\Sort(1)
 */
class Test extends BaseController {

    public function filehang($file = ''){
        $files = self::getDir('D:\MYOA\webroot\general\toppingsoft\app/'.$file);
        $i = 0;
        foreach ($files as $v){
            $i += self::hangshu($v);
        }
        echo '文件数：'.count($files).'<br/>';
        echo '行数：'.$i;
        exit('');
    }

    public function searchDir($path,&$files){
        if(is_dir($path)){
            $opendir = opendir($path);
            while ($file = readdir($opendir)){
                if($file != '.'&&$file != '..'){
                    self::searchDir($path.'/'.$file,$files);
                }
            }
        }
        if(!is_dir($path)){
            $files[] = $path;
        }
        return $files;
    }

    public function getDir($dir){
        $files = array();
        $files = self::searchDir($dir,$files);
        return $files;
    }

    public function hangshu($path){
        $line = 0;
        set_time_limit(0);
        $fp = fopen($path,'r') or die("0");
        if($fp){
            while (stream_get_line($fp,8192,"\n")){
                $line ++;
            }
            fclose($fp);
        }
        return $line;
    }

	public function aa(){
        exit('aaa');
        $re = Db::table('top_user_info_vacation_plan')->where(['year'=>'2023'])->select()->toArray();
        foreach ($re as $k=>$v){
            if(empty($v['join_army_time'])){
                $u = Db::table('top_user_info')->where(['user_id'=>$v['user_id']])->field('id,join_army_time')->find();
                Db::table('top_user_info_vacation_plan')->where(['id'=>$v['id']])->update(['join_army_time'=>$u['join_army_time']]);
            }
        }
		echo date('md H:i:s','1668336663');die;
		$user = ['dept_id'=>'284','user_id'=>''];
		$dept = Db::table('department')->where(['DEPT_ID'=>$user['dept_id']])->find();
		$re = Db::table('top_user_info_config')->where(['key'=>'dep_admin_'.$user['dept_id']])->find();
		if(empty(trim($re['value'],','))||trim($re['value'],',')==$user['user_id']){
			$re = Db::table('top_user_info_config')->where(['key'=>'dep_admin_'.$dept['DEPT_PARENT']])->find();
		}
		dd($re);
		echo '王  局';
		echo '王　局';
		dd('aa');
		//Db::table('top_user_info')->where([['id','>','1']])->update(['is_reduce'=>0]);
		echo 'success';
	}


    /**
     * @Apidoc\Title("OA触发器调试")
     */
    public function trundemo(){
        $re = Db::table('top_push')->fetchSql(true)->where(['run_id'=>'122'])->update(['status'=>'2']);
        exit($re);
		Db::table("bpm_run")->where('run_id','594')->update(['DEL_FLAG'=>'1','DEL_TIME'=>date('Y-m-d H:i:s')]);
		die;
        $a = '{"ALLUSERSPROFILE":"C:\\ProgramData","APPDATA":"C:\\Windows\\system32\\config\\systemprofile\\AppData\\Roaming","CommonProgramFiles":"C:\\Program Files (x86)\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-T0E2N261","ComSpec":"C:\\Windows\\system32\\cmd.exe","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","LOCALAPPDATA":"C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local","NUMBER_OF_PROCESSORS":"4","OS":"Windows_NT","Path":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;D:\\git\\bin;C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local\\Microsoft\\WindowsApps","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","PROCESSOR_ARCHITECTURE":"x86","PROCESSOR_ARCHITEW6432":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 140 Stepping 1, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"8c01","ProgramData":"C:\\ProgramData","ProgramFiles":"C:\\Program Files (x86)","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PSModulePath":"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\Windows","TEMP":"C:\\Windows\\TEMP","TMP":"C:\\Windows\\TEMP","USERDOMAIN":"WORKGROUP","USERNAME":"LAPTOP-T0E2N261$","USERPROFILE":"C:\\Windows\\system32\\config\\systemprofile","windir":"C:\\Windows","HTTP_COOKIE":"SID_616=1bb24971; flow_view=0; creat_work=new; SID_625=48ec7fcc; PHPSESSID=kq7kql8n5atijrn96e67cg3br2; USER_NAME_COOKIE=admin; OA_USER_ID=admin; SID_1=8ab788f9","HTTP_ACCEPT_LANGUAGE":"zh-CN,zh;q=0.9","HTTP_ACCEPT_ENCODING":"gzip, deflate, br","HTTP_REFERER":"http:\/\/localhost\/general\/approve_center\/list\/input_form\/work_handle.php?CONNSTATUS=true&MENU_FLAG=85&EDIT_MODE=&RUN_ID=152&FLOW_ID=85&PRCS_ID=1&FLOW_PRCS=1&PRCS_KEY_ID=256&SAVE_FLAG=t&PUBLIC_FLAG=&SIGN_FLAG=&connstatus=1&next=true","HTTP_SEC_FETCH_SITE":"same-origin","HTTP_ACCEPT":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3","HTTP_SEC_FETCH_USER":"?1","HTTP_SEC_FETCH_MODE":"nested-navigate","HTTP_USER_AGENT":"Mozilla\/5.0 (Windows NT 10.0; WOW64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/77.0.3865.120 Safari\/537.36 Core\/1.77.119.400 QQBrowser\/10.9.4817.400","HTTP_CONTENT_TYPE":"multipart\/form-data; boundary=----WebKitFormBoundaryORBrVz22QttPqWDf","HTTP_UPGRADE_INSECURE_REQUESTS":"1","HTTP_ORIGIN":"http:\/\/localhost","HTTP_CACHE_CONTROL":"no-cache","HTTP_PRAGMA":"no-cache","HTTP_CONTENT_LENGTH":"12154","HTTP_CONNECTION":"keep-alive","HTTP_HOST":"localhost","REDIRECT_STATUS":"200","SERVER_NAME":"localhost","SERVER_PORT":"80","SERVER_ADDR":"127.0.0.1","REMOTE_PORT":"51168","REMOTE_ADDR":"127.0.0.1","SERVER_SOFTWARE":"nginx","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","DOCUMENT_ROOT":"D:\/MYOA\/webroot","DOCUMENT_URI":"\/general\/approve_center\/list\/input_form\/input_submit.php","REQUEST_URI":"\/general\/approve_center\/list\/input_form\/input_submit.php","SCRIPT_NAME":"\/general\/approve_center\/list\/input_form\/input_submit.php","CONTENT_LENGTH":"12154","CONTENT_TYPE":"multipart\/form-data; boundary=----WebKitFormBoundaryORBrVz22QttPqWDf","REQUEST_METHOD":"POST","QUERY_STRING":"","SCRIPT_FILENAME":"D:\/MYOA\/webroot\/general\/approve_center\/list\/input_form\/input_submit.php","FCGI_ROLE":"RESPONDER","PHP_SELF":"\/general\/approve_center\/list\/input_form\/input_submit.php","REQUEST_TIME_FLOAT":1658459192.6141,"REQUEST_TIME":1658459192}';
        print_r($a);
        print_r(json_decode($a,true));die;
        $request = $this->request->param();
        $data = [
            'data' => json_encode($request),
            'status' => 1,
        ];
        Db::table('top_trun')->insert($data);
    }

    /**
     * @Apidoc\Title("接口超时测试")
     */
    public function timeout(){
        //set_time_limit(3);
        ini_set('max_execution_time','306');
        sleep(301);
        exit('success');
    }
    /**
     * @Apidoc\Title("民族数据")
     */
    public function nation(){
        $a = config('global.UserInfo.nation');
        array_values($a);
//        print_r($a);
        exit(json_encode($a));
    }
    /**
     * @Apidoc\Title("生成pdf文件")
     */
    public function getpdf(){
        $template = new TemplateProcessor(root_path() . 'public/word/a.docx');
        $template->setValue("a",'惆怅长岑长错错错错错错错错错');
        //导出文件备份
        $copyPath = root_path() . "public/storage/tmp/" .date("Y-m-d"). '.docx';
        $template->saveAs($copyPath);
        $phpWord = IOFactory::load($copyPath,'Word2007');
        $htmlPath = root_path('public/storage/tmp') . time() . '.html';
        $pdfPath = root_path('public/storage/tmp') . time() . '.pdf';
        $xmlWriter = IOFactory::createWriter($phpWord, 'HTML');
        $xmlWriter->save($htmlPath);
        $mpdf = new Mpdf();
        // 设置PDF文档属性
        $mpdf->SetTitle('My PDF');
        $mpdf->SetAuthor('Your Name');
        $mpdf->SetSubject('Generating PDF using mPDF');
        $mpdf->SetKeywords('mPDF, PDF, PHP');
        // 添加一页
        $mpdf->AddPage();
        // 编写PDF内容
        $mpdf->WriteHTML(mb_convert_encoding(file_get_contents($htmlPath),'GBK','UTF-8'));
        // 输出PDF文件
        $mpdf->Output($pdfPath, 'F');die;
        word_to_pdf($copyPath, $pdfPath);
        header("Content-type: application/vnd.ms-word");
        header("Content-Disposition:attachment;filename={$filename}.docx");
        header('Cache-Control: max-age=0');
        readfile($copyPath);
        unlink($copyPath);
        exit;
    }

}
