<?php
declare (strict_types = 1);

namespace app\sso\controller;

use think\App;
use app\BaseController;
use think\exception\ValidateException;
use think\facade\Db;
use think\Validate;
use \liliuwei\think\Jump;

/**
 * 控制器基础类
 */
abstract class Base
{
    use Jump;

    /**
     * Request实例
     * @var \think\Request
     */
    protected $request;

    /**
     * 应用实例
     * @var \think\App
     */
    protected $app;

    /**
     * 是否批量验证
     * @var bool
     */
    protected $batchValidate = false;

    /**
     * 控制器中间件
     * @var array
     */
    protected $middleware = [];

    //空方法
    public function __call($method, $args)
    {
        $data = request()->param();
        return view($method, ['data' => $data]);
    }

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        $this->app     = $app;
        $this->request = $this->app->request;

        // 关闭未定义对象报错
        error_reporting(E_ERROR | E_PARSE);
        //配置session
        $this->auth();
    }

    public function auth(){
        $this->appid = $_SERVER['HTTP_APPID'];
        $this->appkey = $_SERVER['HTTP_APPKEY'];
        $re = Db::table('top_login_app')->where(['appid'=>$this->appid,'del'=>0])->find();
        if(empty($re)){
            result('',8001,'无效的appid');
        }
        if($re['appkey']!==$this->appkey){
            result('',8002,'身份验证失败');
        }
    }

}
