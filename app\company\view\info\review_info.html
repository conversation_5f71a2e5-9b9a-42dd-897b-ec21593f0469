<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>运行资料</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/company.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .el-table .danger-row {
            background: #fbc4c4;
        }
        .el-table .warning-row {
            background: oldlace;
        }
        .el-table .success-row {
            background: #f0f9eb;
        }
        .el-badge__content.is-fixed { right: 70px;top:60px;height:30px;line-height:30px;border-radius:30px;width:18px;z-index: 9;}
        .success .el-descriptions__header {
            border-left:5px solid #67c23a;
            color:#67c23a;
            line-height: 40px;
            padding-left:20px;
            background: -ms-linear-gradient(left, #f0f9eb, #fff); /* IE 10+ */
            background: -moz-linear-gradient(left, #f0f9eb, #fff); /* Firefox */
            background: -webkit-linear-gradient(left, #f0f9eb, #fff); /* Chrome, Safari, Opera */
            background: linear-gradient(to right, #f0f9eb, #fff); /* 标准语法 */
        }
        .primary .el-descriptions__header {
            margin-top: 20px;
            border-left:5px solid #436CE6;
            color:#436CE6;
            line-height: 40px;
            padding-left:20px;
            background: -ms-linear-gradient(left, #F4F7FE, #fff); /* IE 10+ */
            background: -moz-linear-gradient(left, #F4F7FE, #fff); /* Firefox */
            background: -webkit-linear-gradient(left, #F4F7FE, #fff); /* Chrome, Safari, Opera */
            background: linear-gradient(to right, #F4F7FE, #fff); /* 标准语法 */
        }
        .success .el-descriptions-item__label.is-bordered-label { background-color: #f0f9eb; width:140px;}
        .primary .el-descriptions-item__label.is-bordered-label { background-color: #F4F7FE; width:140px;}
        .el-descriptions .is-bordered td { width:250px;}
        .item { width: 30px;height:30px;line-height:30px;margin: 10px 0;border-radius:40px;background-color: #f0f0f0;text-align:center;cursor: pointer;}
        .item.checked { background-color: #1989FA;color:#fff;}
        .el-divider.el-divider--horizontal { margin:10px 0;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">
        <el-page-header @back="goBack" content="运行资料上报记录"></el-page-header>
        <el-divider style="margin:10px 0;"></el-divider>
        <el-row :gutter="20">
            <el-col style="width:150px;color:#606266;text-align:right;line-height:30px;">要素选择</el-col>
            <el-col style="width:500px;">
                <el-cascader style="width:100%;" size="small" v-model="element_id" :props="{value:'id',label:'name',checkStrictly: true}" :options="elements" @change="handleChange"></el-cascader>
            </el-col>
        </el-row>
        <el-divider style="margin:10px 0;"></el-divider>
        <el-row :gutter="20">
            <el-col :span="16" :style="{overflow: 'auto',height:height+'px'}">
                <el-form v-for="(item,key) in data" v-show="(key+1)==index" label-width="150px">
                    <el-form-item label="考评内容" v-show="item.content!=''&&item.content!='/'">
                        <div v-html="item.content"></div>
                    </el-form-item>
                    <el-form-item label="基本规范要求" v-show="item.ask!=''&&item.ask!='/'">
                        <div v-html="item.ask"></div>
                    </el-form-item>
                    <el-form-item label="企业达标标准" v-show="item.standards!=''&&item.standards!='/'">
                        <div v-html="item.standards"></div>
                    </el-form-item>
                    <el-form-item label="评分方式" v-show="item.method!=''&&item.method!='/'">
                        <div style="white-space:pre-line" v-html="item.method"></div>
                        <!--                        <div v-html="item.method"></div>-->
                    </el-form-item>
                    <el-form-item label="填报周期" v-show="item.cycle!=''&&item.cycle!='/'">
                        <div>{{item.cycle}}</div>
                    </el-form-item>
                    <el-form-item label="自评/评审描述" v-show="item.file_remark!=''&&item.file_remark!='/'">
                        <div>{{item.file_remark}}</div>
                    </el-form-item>
                    <el-form-item label="上报记录">
                        <el-table border
                                  v-loading="loading"
                                  :data="item.list"
                                  style="width: 100%;margin-bottom: 20px;"
                                  size="small">
                            <el-table-column
                                    type="index"
                                    label="序号"
                                    align="center"
                                    width="50">
                            </el-table-column>
                            <el-table-column
                                    prop="time"
                                    label="上报时间"
                                    align="center"
                                    width="100">
                            </el-table-column>
                            <el-table-column
                                    prop="sub_files"
                                    label="上报材料"
                                    align="center"
                                    show-overflow-tooltip
                                    min-width="100">
                                <template slot-scope="scope">
                                    <el-upload
                                            v-if="scope.row.edit"
                                            action="upload"
                                            multiple
                                            :file-list="scope.row.mb_sub_files"
                                            limit="20"
                                            :on-success="(response,file,fileList)=>uploadSuccess(response,file,fileList,scope.row)"
                                            :before-upload="uploadBefore"
                                            :on-remove="(file,fileList)=>handleRemove(file,fileList,scope.row)">
                                        <el-button size="small" type="primary">点击上传</el-button>
                                        <!--                                <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>-->
                                    </el-upload>
                                    <div v-if="!scope.row.edit" v-for="v in scope.row.mb_sub_files">
                                        <el-image v-if="v.ext=='jpg'||v.ext=='png'||v.ext=='JPG'||v.ext=='PNG'" style="width:60px;height: 60px;border: 1px solid #999;"
                                                  :title="v.name"
                                                  :src="v.url"
                                                  :preview-src-list="[v.url]"></el-image>
                                        <el-link style="color: #2c89ff;" v-else :href="v.url" target="_blank">{{v.name}}</el-link>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column
                                    prop="hazard"
                                    label="其他说明"
                                    align="center"
                                    show-overflow-tooltip
                                    min-width="100">
                                <template slot-scope="scope">
                                    <el-input v-if="scope.row.edit" type="textarea" v-model="scope.row.hazard"></el-input>
                                    <div v-if="!scope.row.edit">{{scope.row.hazard}}</div>
                                </template>
                            </el-table-column>
                            <el-table-column
                                    label="操作"
                                    align="center"
                                    width="120">
                                <template slot-scope="scope">
                                    <el-button v-if="scope.row.edit" type="primary" size="small" @click="onSubmit(scope.row)">提交</el-button>
                                    <el-button v-if="!scope.row.edit&&scope.row.is_sub==1" type="primary" size="small" @click="scope.row.edit=true">编辑</el-button>
                                    <el-button v-if="!scope.row.edit&&scope.row.is_sub==0" type="primary" size="small" @click="scope.row.edit=true">上报</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-form-item>
                </el-form>
            </el-col>
            <el-col :span="8" :style="{overflow: 'auto',height:height+'px'}">
                <el-col v-for="(item,key) in data" style="width:50px;">
                    <!--<el-tooltip effect="dark" placement="top">
                        <div slot="content" v-html="item.ask"></div>-->
                        <div :class="(key+1)==index?'item checked':'item'" @click="index=(key+1)">{{key+1}}</div>
<!--                    </el-tooltip>-->
                </el-col>
            </el-col>
        </el-row>
    </div>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.all.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                title: '新增',
                index: 1,
                element_id: [{$element_id}],
                elements: [],
                data: [],
                visible: false,
                loading: false,
                height: document.documentElement.clientHeight - 122,
            };
        },
        methods: {
            handleChange(data){
                this.getData();
            },
            //数据加载
            getData() {
                var _this = this;
                var param = {};
                param.element_id = _this.element_id;
                param._ajax = 1;
                axios.post('', param).then(function (res) {
                    if (res.data.code == 0) {
                        _this.index = 1;
                        _this.data = res.data.data.content;
                        _this.elements = res.data.data.element;
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            onSubmit(item){
                var _this = this;
                _this.loading = true;
                axios.post("reviewSave", item).then(function (res) {
                    _this.loading = false;
                    _this.$message({
                        message: res.data.msg,
                        type: res.data.type
                    });
                    if (res.data.code == 0) {
                        item.is_sub = 1;
                        item.edit = false;
                    }
                }).catch(function (error) {
                    _this.loading = false;
                    console.log("出现错误:",error);
                });
            },
            uploadBefore(file) {
                /*const isJPG = file.type === 'image/jpeg';
                const isPNG = file.type === 'image/png';
                if(!isJPG&&!isPNG){
                    this.$message.error('请上传jpg图片');
                }
                return isJPG||isPNG;*/
            },
            uploadSuccess(res, file,fileList,item) {
                var files = [];
                var mb_files = [];
                for(var i in fileList){
                    files.push(fileList[i].response?fileList[i].response.data.code:fileList[i].code);
                    mb_files.push(fileList[i].response?fileList[i].response.data:fileList[i]);
                }
                item.sub_files = files;
                item.mb_sub_files = mb_files;
            },
            handleRemove(file, fileList,item) {
                var files = [];
                var mb_files = [];
                for(var i in fileList){
                    files.push(fileList[i].response?fileList[i].response.data.code:fileList[i].code);
                    mb_files.push(fileList[i].response?fileList[i].response.data:fileList[i]);
                }
                item.sub_files = files;
                item.mb_sub_files = mb_files;
            },
            goBack() {
                location.href = 'review';
            },
        },
        mounted() {
            //获取列表
            this.getData();

        }
    })
</script>


</body>
</html>