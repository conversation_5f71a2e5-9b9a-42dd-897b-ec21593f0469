<style>
.my-autocomplete li{line-height: normal;padding: 7px;}
.my-autocomplete li .name{text-overflow: ellipsis;overflow: hidden;}
.my-autocomplete li .addr{font-size: 12px;color: #b4b4b4;}
.my-autocomplete li .highlighted{color: #ddd;}
.el-cascader { width:100%;}
.el-form-item__content .el-input-group { vertical-align: middle;}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 150px;
  height: 150px;
  line-height: 150px;
  text-align: center;
}
.avatar {
  width: 150px;
  height: 150px;
  display: block;
}
.el-date-editor.el-input, .el-date-editor.el-input__inner{width:auto;}
</style>
<template>
  <el-dialog :title="title" :close-on-click-modal="false"  modal="false" :visible.sync="visible" width="90%" @close="refresh()" append-to-body="true" label-position="top">
    <el-form ref="form" :model="data" class="tableSearch" label-position="right" label-width="170px" :rules="rules">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="data.name" size="mini"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号" prop="mobile">
            <el-input v-model="data.mobile" size="mini"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="用户名" prop="username">
            <el-input v-model="data.username" size="mini"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="data.email" size="mini"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属科室" prop="department">
            <el-radio-group v-model="data.department">
              <el-radio :label="1">基础科</el-radio>
              <el-radio :label="2">危化科</el-radio>
              <el-radio :label="3">综合协调科</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="管辖范围" prop="manager">
            <el-checkbox-group v-model="data.manager">
              <el-checkbox label="1">工矿商贸</el-checkbox>
              <el-checkbox label="2">危化</el-checkbox>
              <el-checkbox label="3">商务楼宇</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="密码" prop="password">
            <el-input v-model="data.password" size="mini" placeholder="不填则不修改"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否管理员" prop="username">
            <el-radio-group v-model="data.role">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="data.status">
              <el-radio :label="1">正常</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="24" style="text-align: center;">
          <el-button type="primary" @click="submit()">保存</el-button>
          <el-button @click="visible = false">关闭</el-button>
        </el-col>
      </el-row>
    </el-form>
  </el-dialog>
</template>
<script>
module.exports = {
  name: "add",
  // 模板导入区
  components: {
  },
  data:function() {
    return {
      id:0,
      isAdmin: false,
      visible: false,
      title: '新增',
      model: '',
      loading: false,
      user_id: 0,
      date_object:new Date(),
      user_name:'',
      data: {
      },
      rules: {
      },
      config:[],
      contractData:[],
      salesData:[],
      userData:[],
      dels:[],
      dels2:[],
      imageUrl:'',
    }
  },
  mounted: function(){
    // this.getConfig();
  },
  created:function(){
  },
  methods: {
    handleChange(value) {
      var data = [];
      for (var i=0;i<3;i++){
        data.push(value[i]);
      }
      this.data.region = data;
    },
    /**
     * 打开弹窗调用方法
     * */
    open: function (row) {
      console.log(row)
      row = row?row:{id:0};
      var _this =this;
      _this.visible = true;
      _this.getInfo(row.id);
    },
    submit: function () {
      var _this = this;
      var param = _this.data;
      this.$refs.form.validate(function (valid) {
        if(valid){
          axios.post("userSave", param).then(function (res) {
            _this.$message({
              message: res.data.msg,
              type: res.data.type
            });
            if (res.data.code == 0) {
              _this.visible = false;
              _this.$emit("ok");
            }
          }).catch(function (error) {
            console.log("出现错误:",error);
          });
        }
      });
    },
    uploadBefore(file) {
      const isJPG = file.type === 'image/jpeg';
      const isPNG = file.type === 'image/png';
      if(!isJPG&&!isPNG){
          this.$message.error('请上传jpg图片');
      }
      return isJPG||isPNG;
    },
    uploadSuccess(res, file,fileList,field) {
      var files = [];
      console.log(res)
      for(var i in fileList){
        files.push(fileList[i].response??fileList[i]);
      }
      this.data[field] = res.data.code;
      this.data[field+'Url'] = res.data.url;
    },
    getInfo:function(id){
      var _this = this;
      _this.loading = true;
      axios.post('getInfo', {id:id}).then(function (res) {
        _this.loading = false;
        if (res.data.code == 0) {
          _this.data = res.data.data;
        }
      }).catch(function (error) {
        _this.loading = false;
        console.log("出现错误:",error);
      });
    },
    refresh: function () {
      this.$emit("refresh");
    },
    changeweekBegin(data){
      console.log(data);
    },
  }
}
</script>