<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>专家信息管理</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__PUBLIC__/plugs/element/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="__PUBLIC__/plugs/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/common.css">
    <!-- 引入组件库 -->
    <script src="__PUBLIC__/plugs/element/vue.min.js"></script>
    <script src="__PUBLIC__/plugs/element/lib/index.js"></script>
    <script src="__PUBLIC__/static/js/common.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .el-table .danger-row {
            background: #fbc4c4;
        }
        .el-table .warning-row {
            background: oldlace;
        }
        .el-table .success-row {
            background: #f0f9eb;
        }
        .el-badge__content.is-fixed { right: 20px;z-index: 9;}
    </style>
</head>
<body>
<div id="app" v-cloak>
    <div class="centainer">
        <el-form :inline="true" :model="searchFrom" class="form-inline" @submit.native.prevent>
            <el-form-item>
                <el-input v-model="searchFrom.name" size="mini" placeholder="姓名"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="getData()" size="mini">查询</el-button>
                <el-button @click="reset()" size="mini">重置</el-button>
            </el-form-item>
            <el-form-item style="float: right">
                <el-button :loading="loading" type="success" size="mini" @click="add">添加</el-button>
                <el-button :loading="loading" type="primary" size="mini" @click="export1">导出</el-button>
                <el-button :loading="loading" type="primary"  size="mini" @click="import1">导入</el-button>
            </el-form-item>
        </el-form>
        <el-table border
                  v-loading="loading"
                  :data="data"
                  style="width: 100%;margin-bottom: 20px;"
                  ref="qtable"
                  :height="height"
                  size="small"
                  :row-class-name="tableRowClassName">
            <el-table-column
                    type="index"
                    label="序号"
                    align="center"
                    width="50">
            </el-table-column>
            <el-table-column
                    prop="name"
                    label="姓名"
                    align="center"
                    show-overflow-tooltip
                    min-width="100">
            </el-table-column>
            <el-table-column
                    prop="sex"
                    label="性别"
                    align="center"
                    show-overflow-tooltip
                    min-width="100">
            </el-table-column>
            <el-table-column
                    prop="mobile"
                    label="手机号"
                    align="center"
                    show-overflow-tooltip
                    min-width="100">
            </el-table-column>
            <el-table-column
                    prop="org_name"
                    label="所属评审单位"
                    align="center"
                    show-overflow-tooltip
                    min-width="150">
            </el-table-column>
            <el-table-column
                    prop="email"
                    label="邮箱"
                    align="center"
                    show-overflow-tooltip
                    min-width="100">
            </el-table-column>
            <el-table-column
                    prop="education"
                    label="学历"
                    align="center"
                    show-overflow-tooltip
                    min-width="100">
            </el-table-column>
            <el-table-column
                    prop="speciality"
                    label="专业"
                    align="center"
                    show-overflow-tooltip
                    min-width="100">
            </el-table-column>
            <el-table-column
                    prop="work_date"
                    label="参加工作时间"
                    align="center"
                    show-overflow-tooltip
                    min-width="100">
            </el-table-column>
            <el-table-column
                    prop="major"
                    label="擅长专业"
                    align="center"
                    show-overflow-tooltip
                    min-width="100">
            </el-table-column>
            <el-table-column
                    prop="employ_date"
                    label="聘用日期"
                    align="center"
                    show-overflow-tooltip
                    min-width="100">
            </el-table-column>
            <el-table-column
                    prop="mb_status"
                    label="状态"
                    align="center"
                    show-overflow-tooltip
                    width="120">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.status==0" type="danger">禁用</el-tag>
                    <el-tag v-if="scope.row.status==1" type="success">正常</el-tag>
                </template>
            </el-table-column>
            <el-table-column
                    label="操作"
                    align="center"
                    fixed="right"
                    width="120">
                <template slot-scope="scope">
                    <el-dropdown split-button size="small" type="success" @click="info(scope.row)">
                        详情
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item @click.native="edit(scope.row)" type="warning">编辑</el-dropdown-item>
                            <el-dropdown-item @click.native="del(scope.row)" type="danger">删除</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </template>
            </el-table-column>
        </el-table>
        <!--分页条total, sizes, prev, pager, next, jumper-->
        <div class="block">
            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="page"
                    :page-sizes="[10, 20, 50, 100, 500, 1000]"
                    :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total">
            </el-pagination>
        </div>
    </div>
    <import1 ref="import1" @refresh="getData()"></import1>
    <add ref="add" @ok="getData()"></add>
    <info ref="info"></info>
</div>

<script src="__PUBLIC__/plugs/layui/layui.all.js"></script>
<script src="__PUBLIC__/plugs/element/axios.min.js"></script>
<script src="__PUBLIC__/static/js/jquery1.12.4.min.js"></script>
<script src="__PUBLIC__/plugs/layer/layer.js"></script>
<script src="__PUBLIC__/static/js/httpVueLoader.js"></script>
<script src="__PUBLIC__/static/js/request.js"></script>

<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="__PUBLIC__/plugs/ueditor/ueditor.all.js"></script>
<script>
    Vue.use(httpVueLoader);
    var app = new Vue({
        el: '#app',
        data() {
            return {
                title: '新增',
                searchFrom: {
                    work_type: [],
                    title: '',
                    is_delivery: '',
                    is_end: '',
                },
                thisrow: {},
                thisuser: '',
                data: [],
                filelist: [],
                page: 1,
                pageSize: 20,
                total: 0,
                deptData: [],
                userData: [],
                dialogFormVisible: false,
                visible: false,
                loading: true,
                form: {},
                formRules: {
                    form_name: [{required: true, message: "请输入表单名称", trigger: "blur"}],
                },
                ueObj: null,
                height: document.documentElement.clientHeight - 155,
            };
        },
        components: {
            'add': 'url:/general/toppingsoft/app/admin/view/expert/vue/add.vue?v=1',
            'info': 'url:/general/toppingsoft/app/admin/view/expert/vue/info.vue?v=1',
            'import1': 'url:/general/toppingsoft/public/vue/import.vue',
        },
        watch: {
            $route() {
                //切换标签时
                this.handleRefreshTable();
            }
        },
        created() {
            let _this = this;
            //窗口改变时
            window.onresize = function () {
                _this.handleRefreshTable();
            };
        },
        updated() {
            //数据改变时
            this.handleRefreshTable();
        },
        methods: {
            tableRowClassName({row, rowIndex}) {
                if (row.back) {
                    return row.back;
                }
                return '';
            },
            handleClick(tab, event) {
                this.searchFrom = {
                    our: '',
                    status: '',
                    level: '',
                    type: '',
                    company: '',
                    charge_user_name: '',
                };
                this.getData();
            },
            statusChange() {
                this.page = 1;
                this.getData();
            },
            handleSizeChange: function (val) {
                this.pageSize = val;
                this.getData();
                console.log('每页 ${val} 条');
            },
            handleCurrentChange: function (val) {
                this.page = val;
                this.getData();
                console.log('当前页: ${val}');
            },
            add() {
                this.$refs.add.title="新增专家信息";
                this.$refs.add.open();
            },
            edit(row) {
                this.$refs.add.title="修改专家信息";
                this.$refs.add.open(row);
            },
            info(row) {
                this.thisrow = row;
                this.$refs.info.open(row);
            },
            del(row) {
                var _this = this;
                _this.$confirm("确认删除？", "提示", {}).then(() => {
                    _this.addLoading = true;
                    var param = {};
                    var url = "del";
                    param.id = row.id;
                    axios.post(url, param).then(function (res) {
                        _this.addLoading = false;
                        if (res.data.code == 0) {
                            _this.$message({
                                message: res.data.msg,
                                type: "success"
                            });
                            _this.$refs.info.visible = false;
                            _this.getData();
                        } else {
                            _this.$message({
                                message: res.data.msg,
                                type: "error"
                            });
                        }
                    }).catch(function (error) {
                        console.log(error);
                    });
                });
            },
            //数据初始化
            reset() {
                this.searchFrom = {};
                this.page = 1;
                this.pageSize = 20;
                this.getData();
            },
            //数据加载
            getData() {
                var _this = this;
                var param = _this.searchFrom;
                param._ajax = 1;
                param.page = _this.page;
                param.limit = _this.pageSize;
                param.our = 1;
                _this.loading  = true;
                axios.post('', param).then(function (res) {
                    _this.loading  = false;
                    if (res.data.code == 0) {
                        _this.data = res.data.data.data;
                        _this.page = res.data.data.current_page;
                        _this.pageSize = res.data.data.per_page;
                        _this.total = res.data.data.total;
                        _this.$nextTick(() => {
                            // 这里的代码会在DOM更新完成后执行
                            _this.loading = false;
                        });
                        _this.handleRefreshTable();
                    } else {
                        _this.$message({
                            message: res.data.msg,
                            type: "error"
                        });
                        return false;
                    }
                }).catch(function (error) {
                    console.log(error);
                });
            },
            import1: function () {
                this.$refs.import1.templateUrl = 'importTemplate';
                this.$refs.import1.submitUrl = 'import';
                this.$refs.import1.title = '专家信息导入';
                this.$refs.import1.open();
            },
            export1: function () {
                let _this = this;
                let where = "";
                let type = '';
                //获得where
                where = '';
                for (let index in this.searchFrom) {
                    if (this.searchFrom[index] !== '' && this.searchFrom[index] !== [] && this.searchFrom[index] !== {}) {
                        let str = "";
                        if (index == 'type') {
                            for (let i in this.searchFrom.type) {
                                type += this.searchFrom.type[i] + ',';
                            }
                            str += 'type=' + type;
                        } else {
                            str += index + '=' + this.searchFrom[index];
                        }
                        where += "&" + str;
                    }
                }
                let url = "index?excel=1&" + where;
                //window.open(url);
                location.href = url;
            },
            handleRefreshTable: function () {
                this.$nextTick(function () {
                    var refList = this.$refs;
                    if (refList) {
                        for (var i of Object.keys(refList)) {
                            if (refList[i] && refList[i].doLayout) {
                                refList[i].doLayout();
                            }
                        }
                    }
                });
            },
        },
        mounted() {
            //获取列表
            this.getData();

        }
    })
</script>


</body>
</html>