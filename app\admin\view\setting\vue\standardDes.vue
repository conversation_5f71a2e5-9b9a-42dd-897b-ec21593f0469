<style>
.el-dialog__header {
  padding: 0;
}
.el-dialog__headerbtn {
  position: absolute;
  top: 8px;
  right: 20px;
  padding: 0;
  background: 0 0;
  border: none;
  outline: 0;
  cursor: pointer;
  font-size: 16px;
}
.pad0 .el-dialog__body{ padding: 30px 20px 0}
iframe{
  border: none;
}
</style>

<template>
  <el-dialog title="" width="98%"
             class="icon-dialog pad0"
             top="1vh"
             :visible.sync="visible"
             :fullscreen="true"
             append-to-body>
    <iframe :style="{height: height+'px', width: '100%'}" :src="src"></iframe>
  </el-dialog>
</template>

<script>
module.exports = {
  name: "add",
  // 模板导入区
  components: {
  },
  data:function() {
    return {
      src: "",
      visible: false,
      title: '新增',
      loading:false,
      height: document.documentElement.clientHeight-40,
    }
  },
  mounted: function(){
  },
  created:function(){
  },
  methods: {
    /**
     * 打开弹窗调用方法
     * */
    open: function (row) {
      var _this =this;
      _this.visible = true;
      _this.src = "./standardDes?id="+row.id;
    },
  }
}
</script>